[uwsgi]
# Error on unknown options (prevents typos)
strict = true

socket       = :8000
chdir        = /home/<USER>/main/zhulinks_supply_chains_backend/
module       = zhulinks_supply_chains_backend.wsgi

# Most of uWSGI features depend on the master mode
master       = true

# Formula: cores * 2 + 2
processes    = 6

# Respect SIGTERM and do shutdown instead of reload
die-on-term  = true

chmod-socket = 664
vacuum       = true
pidfile      = /home/<USER>/main/uwsgi/zhulinks_supply_chains_backend.pid

# Respawn processes after serving ... requests
max-requests = 1000
