import asyncio
import getpass
import os
import re
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


async def test_db_routers():
    from tortoise import Tortoise

    from app.core.tortoise_config import TORTOISE_ORM
    from app.models.orders_models import OrdersOrder

    # Initialize Tortoise ORM
    await Tortoise.init(TORTOISE_ORM, modules={"models": ["models.orders_models"]})
    # Create database tables
    await Tortoise.generate_schemas()

    try:
        order = await OrdersOrder.all().count()
        print(order)
    except Exception as e:
        print(e)
    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(test_db_routers())
