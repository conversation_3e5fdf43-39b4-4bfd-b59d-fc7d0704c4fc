import pytest
from fastapi import FastAPI
from fastapi.testclient import Test<PERSON><PERSON>
from tortoise.contrib.test import finalizer, initializer
from pytest_asyncio import fixture

from app.api.endpoints.company import company_router
from app.models.company_models import Company
from app.models.user_models import User


@pytest.fixture(scope="module")
def test_app():
    """Create a FastAPI test app with the company router"""
    app = FastAPI()
    app.include_router(company_router)
    return app


@pytest.fixture(scope="module")
def test_client(test_app):
    """Create a test client for the FastAPI app"""
    return TestClient(test_app)


@pytest.fixture(scope="module")
def init_db():
    """Initialize test database"""
    initializer(["app.models"], db_url="sqlite://:memory:")
    yield
    finalizer()


@fixture
async def admin_user():
    """Create a test admin user"""
    user = await User.create(
        username="admin_user",
        password="hashed_password",
        mobile="13800138001",
        is_active=True,
        is_superuser=True
    )
    yield user
    await user.delete()


@fixture
async def test_company():
    """Create a test company"""
    company = await Company.create(
        company_id="test_company",
        name="Test Company",
        create_user_id=1,
        update_user_id=1
    )
    yield company
    await company.delete()


@pytest.mark.asyncio
async def test_get_companies(test_client, admin_user):
    """测试获取公司列表"""
    with test_client:
        # 测试非管理员访问
        response = test_client.get(
            "/companies",
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 403  # 应该没有权限

        # 测试管理员访问
        admin_user.is_superuser = True
        await admin_user.save()
        response = test_client.get(
            "/companies",
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert isinstance(data["data"], dict)
        assert "items" in data["data"]


@pytest.mark.asyncio
async def test_get_company(test_client, test_company, admin_user):
    """测试获取公司详情"""
    with test_client:
        # 测试获取不存在的公司
        response = test_client.get(
            "/companies/invalid_company",
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 400  # 应该返回404

        # 测试获取存在的公司
        response = test_client.get(
            f"/companies/{test_company.company_id}",
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert data["data"]["name"] == "Test Company"


@pytest.mark.asyncio
async def test_create_company(test_client, admin_user):
    """测试创建公司"""
    with test_client:
        # 测试非管理员创建公司
        response = test_client.post(
            "/companies",
            json={
                "company_id": "new_company",
                "name": "New Company"
            },
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 403  # 应该没有权限

        # 测试管理员创建公司
        admin_user.is_superuser = True
        await admin_user.save()
        response = test_client.post(
            "/companies",
            json={
                "company_id": "new_company",
                "name": "New Company"
            },
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert data["data"]["name"] == "New Company"


@pytest.mark.asyncio
async def test_update_company(test_client, test_company, admin_user):
    """测试更新公司信息"""
    with test_client:
        # 测试非管理员更新公司
        response = test_client.put(
            f"/companies/{test_company.company_id}",
            json={
                "name": "Updated Company"
            },
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 403  # 应该没有权限

        # 测试管理员更新公司
        admin_user.is_superuser = True
        await admin_user.save()
        response = test_client.put(
            f"/companies/{test_company.company_id}",
            json={
                "name": "Updated Company"
            },
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert data["data"]["name"] == "Updated Company"


@pytest.mark.asyncio
async def test_delete_company(test_client, test_company, admin_user):
    """测试删除公司"""
    with test_client:
        # 测试非管理员删除公司
        response = test_client.delete(
            f"/companies/{test_company.company_id}",
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 403  # 应该没有权限

        # 测试管理员删除公司
        admin_user.is_superuser = True
        await admin_user.save()
        response = test_client.delete(
            f"/companies/{test_company.company_id}",
            headers={"Authorization": f"Bearer {admin_user.id}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
