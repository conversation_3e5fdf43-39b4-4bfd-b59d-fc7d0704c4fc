import pytest
import asyncio
from pytest_asyncio import fixture
from tortoise import Tortoise
from app.models.sequence_models import Sequence


@fixture(scope="module")
async def db():
    """数据库连接配置"""
    await Tortoise.init(
        db_url="sqlite://:memory:",
        modules={"models": ["app.models.sequence_models"]}
    )
    await Tortoise.generate_schemas()
    yield
    await Tortoise.close_connections()


@pytest.mark.asyncio
async def test_sequence_create_and_get_last_value(db):
    """测试创建序列和获取最后一个值"""
    # 创建一个新的序列
    name = "test_sequence"
    await Sequence.create(name=name, last=100)
    
    # 获取序列的最后一个值
    last_value = await Sequence.get_last_value(name)
    assert last_value == 100


@pytest.mark.asyncio
async def test_sequence_get_nonexistent(db):
    """测试获取不存在的序列"""
    name = "nonexistent_sequence"
    last_value = await Sequence.get_last_value(name)
    assert last_value is None


@pytest.mark.asyncio
async def test_sequence_get_next_value_initial(db):
    """测试获取序列的下一个值（初始状态）"""
    name = "test_next_sequence"
    
    # 创建序列
    seq = await Sequence.create(name=name, last=0)
    
    # 获取下一个值
    next_value = await Sequence.get_next_value(name)
    assert next_value == 1
    
    # 确保序列已正确更新
    seq = await Sequence.get(name=name)
    assert seq.last == 1


@pytest.mark.asyncio
async def test_sequence_get_next_value_increment(db):
    """测试获取序列的下一个值（已存在序列）"""
    name = "test_increment_sequence"
    
    # 创建初始序列
    seq = await Sequence.create(name=name, last=100)
    
    # 获取下一个值
    next_value = await Sequence.get_next_value(name)
    assert next_value == 101
    
    # 确保值已正确更新
    seq = await Sequence.get(name=name)
    assert seq.last == 101


@pytest.mark.asyncio
async def test_sequence_get_next_value_custom_initial(db):
    """测试获取序列的下一个值（自定义初始值）"""
    name = "test_custom_sequence"
    initial_value = 1000
    
    # 获取下一个值，应该返回初始值
    next_value = await Sequence.get_next_value(name, initial_value=initial_value)
    assert next_value == 1000
    
    # 确保序列已正确创建
    seq = await Sequence.get(name=name)
    assert seq.last == 1000


@pytest.mark.asyncio
async def test_sequence_concurrent_access(db):
    """测试并发访问序列"""
    name = "test_concurrent_sequence"
    
    # 创建初始序列
    seq = await Sequence.create(name=name, last=0)
    
    # 使用锁来序列化对序列的访问
    lock = asyncio.Lock()
    
    async def get_next():
        async with lock:
            return await Sequence.get_next_value(name)
    
    # 顺序执行获取下一个值
    results = []
    for _ in range(3):
        next_val = await get_next()
        results.append(next_val)
    
    # 验证结果
    assert results == [1, 2, 3]
    
    # 验证最终值
    seq = await Sequence.get(name=name)
    assert seq.last == 3


@fixture(autouse=True)
async def cleanup():
    """清理测试数据"""
    await Sequence.filter().delete()  # 清理所有测试数据
    # 不需要预创建序列，因为每个测试都会创建需要的序列