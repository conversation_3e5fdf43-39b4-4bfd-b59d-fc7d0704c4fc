#!/usr/bin/env python3
"""
测试新的数据库模型设计
验证模型关联关系和枚举功能是否正常工作
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.inbound_order_models import (
    InboundOrderStatusEnum,
    ShippingTypeEnum,
    CertTypeEnum,
    WaybillStatusEnum,
    CertificateTypeEnum,
    WayBillCodeBindCertStatusEnum,
)


def test_enums():
    """测试所有枚举类型"""
    print("=== 测试枚举类型 ===")
    
    # 测试入库单状态枚举
    print("\n1. 入库单状态枚举:")
    for choice in InboundOrderStatusEnum.choices():
        print(f"  {choice}")
    
    # 测试发货类型枚举
    print("\n2. 发货类型枚举:")
    for choice in ShippingTypeEnum.choices():
        print(f"  {choice}")
    
    # 测试发证方式枚举
    print("\n3. 发证方式枚举:")
    for choice in CertTypeEnum.choices():
        print(f"  {choice}")
    
    # 测试面单状态枚举
    print("\n4. 面单状态枚举:")
    for choice in WaybillStatusEnum.choices():
        print(f"  {choice}")
    
    # 测试证书类型枚举
    print("\n5. 证书类型枚举:")
    for choice in CertificateTypeEnum.choices():
        print(f"  {choice}")
    
    # 测试绑定状态枚举
    print("\n6. 绑定状态枚举:")
    for choice in WayBillCodeBindCertStatusEnum.choices():
        print(f"  {choice}")


def test_enum_labels():
    """测试枚举标签功能"""
    print("\n=== 测试枚举标签功能 ===")
    
    # 测试面单状态标签
    print(f"\n面单状态标签:")
    print(f"  待发货: {WaybillStatusEnum.PENDING.label}")
    print(f"  已发货: {WaybillStatusEnum.SHIPPED.label}")
    print(f"  已完成: {WaybillStatusEnum.COMPLETED.label}")
    
    # 测试证书类型标签
    print(f"\n证书类型标签:")
    print(f"  一物一证: {CertificateTypeEnum.ONE_TO_ONE.label}")
    print(f"  随机发证: {CertificateTypeEnum.RANDOM.label}")


def test_model_imports():
    """测试模型导入"""
    print("\n=== 测试模型导入 ===")
    
    try:
        from app.models import (
            ShippingWaybill,
            ShippingItem,
            ShippingCertificate,
            InboundOrder,
            InboundOrderDetail,
        )
        print("✅ 所有新模型导入成功")
        
        # 打印模型信息
        print(f"\nShippingWaybill 表名: {ShippingWaybill._meta.table}")
        print(f"ShippingItem 表名: {ShippingItem._meta.table}")
        print(f"ShippingCertificate 表名: {ShippingCertificate._meta.table}")
        
    except ImportError as e:
        print(f"❌ 模型导入失败: {e}")


def print_database_design_summary():
    """打印数据库设计总结"""
    print("\n" + "="*60)
    print("新数据库设计总结")
    print("="*60)
    
    print("\n📋 核心表结构:")
    print("  1. cert_inbound_order - 入库单主表")
    print("  2. cert_inbound_order_detail - 入库单明细表(任务分配)")
    print("  3. cert_shipping_waybill - 发货面单表")
    print("  4. cert_shipping_item - 发货商品项目表")
    print("  5. cert_shipping_certificate - 发货证书表")
    
    print("\n🔄 业务流程:")
    print("  入库单 → 任务分配 → 面单创建 → 商品添加 → 证书绑定")
    
    print("\n🔍 快速查询支持:")
    print("  • 快递单号 (waybill_code)")
    print("  • 订单号 (order_id)")
    print("  • 证书号 (cert_sn_code)")
    print("  • 商品SN码 (product_sn_code)")
    
    print("\n💡 设计特点:")
    print("  • 一个面单可包含多个商品和证书")
    print("  • 支持一物一证和随机发证两种模式")
    print("  • 通过冗余字段优化查询性能")
    print("  • 完整的索引设计支持高效查询")


if __name__ == "__main__":
    print("开始测试新的数据库模型设计...")
    
    test_enums()
    test_enum_labels()
    test_model_imports()
    print_database_design_summary()
    
    print(f"\n✅ 测试完成!")
