#!/usr/bin/env python3
"""
测试枚举改进功能
验证枚举的字符串描述和选项列表功能是否正常工作
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models.inbound_order_models import (
    InboundOrderStatusEnum,
    ShippingTypeEnum,
    CertTypeEnum,
    WayBillCodeBindCertStatusEnum,
)


def test_enum_labels():
    """测试枚举标签功能"""
    print("=== 测试枚举标签功能 ===")
    
    # 测试入库单状态枚举
    print("\n1. 入库单状态枚举:")
    for status in InboundOrderStatusEnum:
        print(f"  {status.name} ({status.value}): {status.label}")
    
    # 测试发货类型枚举
    print("\n2. 发货类型枚举:")
    for shipping_type in ShippingTypeEnum:
        print(f"  {shipping_type.name} ({shipping_type.value}): {shipping_type.label}")
    
    # 测试发证方式枚举
    print("\n3. 发证方式枚举:")
    for cert_type in CertTypeEnum:
        print(f"  {cert_type.name} ({cert_type.value}): {cert_type.label}")
    
    # 测试绑定状态枚举
    print("\n4. 绑定状态枚举:")
    for bind_status in WayBillCodeBindCertStatusEnum:
        print(f"  {bind_status.name} ({bind_status.value}): {bind_status.label}")


def test_enum_choices():
    """测试枚举选项列表功能"""
    print("\n=== 测试枚举选项列表功能 ===")
    
    # 测试入库单状态选项
    print("\n1. 入库单状态选项:")
    choices = InboundOrderStatusEnum.choices()
    for choice in choices:
        print(f"  {choice}")
    
    # 测试发货类型选项
    print("\n2. 发货类型选项:")
    choices = ShippingTypeEnum.choices()
    for choice in choices:
        print(f"  {choice}")
    
    # 测试发证方式选项
    print("\n3. 发证方式选项:")
    choices = CertTypeEnum.choices()
    for choice in choices:
        print(f"  {choice}")
    
    # 测试绑定状态选项
    print("\n4. 绑定状态选项:")
    choices = WayBillCodeBindCertStatusEnum.choices()
    for choice in choices:
        print(f"  {choice}")


def test_enum_api_response():
    """模拟API响应格式"""
    print("\n=== 模拟API响应格式 ===")
    
    api_response = {
        "status": InboundOrderStatusEnum.choices(),
        "shipping_type": ShippingTypeEnum.choices(),
        "cert_type": CertTypeEnum.choices(),
        "bind_cert_status": WayBillCodeBindCertStatusEnum.choices(),
    }
    
    print("\nAPI响应格式预览:")
    import json
    print(json.dumps(api_response, ensure_ascii=False, indent=2))


def test_enum_usage_examples():
    """测试枚举使用示例"""
    print("\n=== 枚举使用示例 ===")
    
    # 示例1：根据值获取标签
    status_value = 1
    status_enum = InboundOrderStatusEnum(status_value)
    print(f"\n状态值 {status_value} 对应的标签: {status_enum.label}")
    
    # 示例2：在业务逻辑中使用
    print(f"\n业务逻辑示例:")
    print(f"  待扫描状态: {InboundOrderStatusEnum.PENDING_SCAN.value} - {InboundOrderStatusEnum.PENDING_SCAN.label}")
    print(f"  发货中状态: {InboundOrderStatusEnum.SHIPPING.value} - {InboundOrderStatusEnum.SHIPPING.label}")
    
    # 示例3：前端下拉框数据
    print(f"\n前端下拉框数据示例:")
    for choice in ShippingTypeEnum.choices()[:2]:  # 只显示前两个
        print(f"  <option value='{choice['value']}'>{choice['label']}</option>")


if __name__ == "__main__":
    print("开始测试枚举改进功能...")
    
    try:
        test_enum_labels()
        test_enum_choices()
        test_enum_api_response()
        test_enum_usage_examples()
        
        print("\n✅ 所有测试通过！枚举改进功能正常工作。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
