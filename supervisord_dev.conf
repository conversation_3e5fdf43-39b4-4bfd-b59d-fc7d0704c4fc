[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisord]
pidfile=/var/run/supervisord.pid
user=root

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[program:backend]
command=/home/<USER>/zhulinks_venv/bin/uwsgi --ini /home/<USER>/main/uwsgi_dev.ini
directory=/home/<USER>/main/
redirect_stderr = true
stdout_logfile=/var/log/zhulinks_supply_chains_backend/uwsgi.log
stopasgroup=true
user=zhulinks

[program:celery]
command=/home/<USER>/zhulinks_venv/bin/celery -A zhulinks_supply_chains_backend worker -l INFO -c 3
directory=/home/<USER>/main/zhulinks_supply_chains_backend/
redirect_stderr = true
stdout_logfile=/var/log/zhulinks_supply_chains_backend/celery.log
stopasgroup=true
user=zhulinks

[program:celery_beat]
command=/home/<USER>/zhulinks_venv/bin/celery -A zhulinks_supply_chains_backend beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
directory=/home/<USER>/main/zhulinks_supply_chains_backend/
redirect_stderr = true
stdout_logfile=/var/log/zhulinks_supply_chains_backend/celery_beat.log
stopasgroup=true
user=zhulinks

[program:flower]
command=/home/<USER>/zhulinks_venv/bin/celery -A zhulinks_supply_chains_backend flower --port=5555 --basic_auth=makerbi:flower@zhulinks! --url_prefix=flower
directory=/home/<USER>/main/zhulinks_supply_chains_backend/
redirect_stderr = true
stdout_logfile=/var/log/zhulinks_supply_chains_backend/flower.log
stopasgroup=true
user=zhulinks
