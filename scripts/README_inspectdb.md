# 数据库表结构检查工具使用说明

## 功能介绍

这个工具可以帮助您：
- 检查现有数据库的表结构
- 生成 Tortoise ORM 模型代码
- 支持多数据库连接
- 交互式操作，无需命令行参数

## 特点

✅ **无需 aerich 迁移** - 直接连接数据库查询表结构  
✅ **交互式界面** - 友好的用户界面，无需记忆命令参数  
✅ **多数据库支持** - 支持项目中配置的所有数据库连接  
✅ **灵活输出** - 可输出到控制台或保存到文件  
✅ **完整的表结构** - 包括字段类型、主键、外键、约束等信息  

## 使用方法

### 1. 运行工具

```bash
python scripts/inspectdb.py
```

### 2. 按提示选择选项

工具会依次询问：

1. **选择数据库连接**
   - 显示所有可用的数据库连接
   - 输入数字选择对应的连接

2. **选择表名**
   - 选项 1：检查所有表
   - 选项 2：检查指定表（需要输入表名）

3. **选择输出方式**
   - 选项 1：输出到控制台
   - 选项 2：保存到文件（可指定文件路径）

### 3. 查看结果

工具会生成包含以下内容的 Tortoise ORM 模型代码：
- 字段定义（类型、长度、约束等）
- 主键设置
- 外键关系
- 表元数据

## 示例输出

```python
# -*- coding: utf-8 -*-
# 自动生成的 Tortoise ORM 模型
# 数据库连接: default
# 生成时间: 2024-01-01 12:00:00

from tortoise import fields, models

class User(models.Model):
    id = fields.BigIntField(primary_key=True, description="id")
    username = fields.CharField(max_length=50, description="username")
    email = fields.CharField(max_length=100, null=True, description="email")
    created_at = fields.DatetimeField(description="created_at")
    
    class Meta:
        table = "users"
        table_description = "User 表"
```

## 依赖要求

- Python 3.10+
- asyncpg (PostgreSQL 连接库)
- 项目配置文件中的数据库连接信息

## 故障排除

### 1. 连接失败
- 检查数据库服务是否运行
- 验证连接配置是否正确
- 确认网络连接正常

### 2. 表不存在
- 确认表名拼写正确
- 检查表是否在 public schema 中
- 验证用户是否有查询权限

### 3. 缺少依赖
```bash
pip install asyncpg
```

## 注意事项

- 工具只读取表结构，不会修改数据库
- 生成的模型代码可能需要手动调整
- 外键关系的模型名称可能需要修正
- 建议在使用前备份重要数据

## 支持的数据库

目前支持 PostgreSQL 数据库。如需支持其他数据库，请联系开发团队。
