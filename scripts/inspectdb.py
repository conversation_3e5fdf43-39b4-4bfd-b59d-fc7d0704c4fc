# -*- coding: utf-8 -*-
#!/usr/bin/env python3
"""
数据库表结构检查工具 - 交互式版本

这个工具可以帮助您检查数据库表结构并生成 Tortoise ORM 模型代码
支持多数据库连接，可以检查指定表或所有表
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings

try:
    import asyncpg
except ImportError:
    print("❌ 错误: 需要安装 asyncpg")
    print("请运行: pip install asyncpg")
    sys.exit(1)


async def get_database_connection(connection_name):
    """获取数据库连接"""
    if connection_name not in settings.DATABASES:
        print(f"错误: 连接 '{connection_name}' 不存在于配置中")
        sys.exit(1)

    # 获取连接凭据
    credentials = settings.DATABASES[connection_name]["credentials"]

    try:
        conn = await asyncpg.connect(
            host=credentials["host"], port=credentials["port"], user=credentials["user"], password=credentials["password"], database=credentials["database"]
        )
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)


async def get_table_list(conn, table_name=None):
    """获取表列表"""
    if table_name:
        # 检查指定表是否存在
        query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
        AND table_name = $1
        """
        result = await conn.fetch(query, table_name)
        if not result:
            print(f"❌ 表 '{table_name}' 不存在")
            return []
        return [table_name]
    else:
        # 获取所有表
        query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
        """
        result = await conn.fetch(query)
        return [row["table_name"] for row in result]


async def get_table_structure(conn, table_name):
    """获取表结构"""
    # 获取列信息
    columns_query = """
    SELECT
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length,
        numeric_precision,
        numeric_scale
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = $1
    ORDER BY ordinal_position
    """

    # 获取主键信息
    pk_query = """
    SELECT column_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
    WHERE tc.table_schema = 'public'
    AND tc.table_name = $1
    AND tc.constraint_type = 'PRIMARY KEY'
    """

    # 获取外键信息
    fk_query = """
    SELECT
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage ccu
        ON ccu.constraint_name = tc.constraint_name
    WHERE tc.table_schema = 'public'
    AND tc.table_name = $1
    AND tc.constraint_type = 'FOREIGN KEY'
    """

    columns = await conn.fetch(columns_query, table_name)
    primary_keys = await conn.fetch(pk_query, table_name)
    foreign_keys = await conn.fetch(fk_query, table_name)

    return {
        "columns": columns,
        "primary_keys": [row["column_name"] for row in primary_keys],
        "foreign_keys": {row["column_name"]: {"table": row["foreign_table_name"], "column": row["foreign_column_name"]} for row in foreign_keys},
    }


def postgres_to_tortoise_type(data_type, max_length=None, precision=None, scale=None):
    """将 PostgreSQL 数据类型转换为 Tortoise ORM 字段类型"""
    type_mapping = {
        "integer": "IntField",
        "bigint": "BigIntField",
        "smallint": "SmallIntField",
        "boolean": "BooleanField",
        "character varying": "CharField",
        "varchar": "CharField",
        "text": "TextField",
        "timestamp without time zone": "DatetimeField",
        "timestamp with time zone": "DatetimeField",
        "date": "DateField",
        "time": "TimeField",
        "numeric": "DecimalField",
        "decimal": "DecimalField",
        "real": "FloatField",
        "double precision": "FloatField",
        "json": "JSONField",
        "jsonb": "JSONField",
        "uuid": "UUIDField",
    }

    field_type = type_mapping.get(data_type, "CharField")

    # 添加字段参数
    params = []
    if field_type == "CharField" and max_length:
        params.append(f"max_length={max_length}")
    elif field_type == "DecimalField":
        if precision and scale:
            params.append(f"max_digits={precision}, decimal_places={scale}")

    return field_type, params


def generate_tortoise_model(table_name, structure):
    """生成 Tortoise ORM 模型代码"""
    class_name = "".join(word.capitalize() for word in table_name.split("_"))

    lines = [
        "from tortoise import fields, models\n",
        f"class {class_name}(models.Model):",
    ]

    # 生成字段
    for column in structure["columns"]:
        col_name = column["column_name"]
        data_type = column["data_type"]
        is_nullable = column["is_nullable"] == "YES"
        default = column["column_default"]
        max_length = column["character_maximum_length"]
        precision = column["numeric_precision"]
        scale = column["numeric_scale"]

        field_type, params = postgres_to_tortoise_type(data_type, max_length, precision, scale)

        # 构建字段定义
        field_params = []

        # 主键
        if col_name in structure["primary_keys"]:
            if field_type == "IntField":
                field_type = "IntField"
                field_params.append("primary_key=True")
            elif field_type == "BigIntField":
                field_type = "BigIntField"
                field_params.append("primary_key=True")

        # 外键
        if col_name in structure["foreign_keys"]:
            fk_info = structure["foreign_keys"][col_name]
            field_type = "ForeignKeyField"
            field_params.append(f'"models.{fk_info["table"].title()}"')
            field_params.append("on_delete=fields.CASCADE")

        # 其他参数
        field_params.extend(params)

        if is_nullable and col_name not in structure["primary_keys"]:
            field_params.append("null=True")

        if default and "nextval" not in str(default):
            if "true" in str(default).lower():
                field_params.append("default=True")
            elif "false" in str(default).lower():
                field_params.append("default=False")
            elif str(default).replace("'", "").replace('"', "").isdigit():
                field_params.append(f"default={default}")

        # 添加描述
        field_params.append(f'description="{col_name}"')

        params_str = ", ".join(field_params) if field_params else ""
        lines.append(f"    {col_name} = fields.{field_type}({params_str})")

    # 添加 Meta 类
    lines.extend(["", "    class Meta:", f'        table = "{table_name}"', f'        table_description = "{class_name} 表"'])

    return "\n".join(lines) + "\n\n"


async def inspect_database(connection_name, table_name=None, output_path=None):
    """检查数据库表结构"""
    try:
        print(f"🔗 连接到数据库: {connection_name}")
        conn = await get_database_connection(connection_name)

        print("📋 获取表列表...")
        tables = await get_table_list(conn, table_name)

        if not tables:
            print("❌ 没有找到任何表")
            return False

        print(f"✅ 找到 {len(tables)} 个表: {', '.join(tables)}")

        # 生成模型代码
        model_code = "# -*- coding: utf-8 -*-\n"
        model_code += "# 自动生成的 Tortoise ORM 模型\n"
        model_code += f"# 数据库连接: {connection_name}\n"
        model_code += f"# 生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        for table in tables:
            print(f"🔍 分析表: {table}")
            structure = await get_table_structure(conn, table)
            model_code += generate_tortoise_model(table, structure)

        # 输出结果
        if output_path:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with open(output_file, "a", encoding="utf-8") as f:
                f.write(model_code)
            print(f"✅ 模型已保存到: {output_file}")
            print(f"📁 文件大小: {output_file.stat().st_size} bytes")
        else:
            print("📋 生成的模型代码:")
            print("=" * 80)
            print(model_code)
            print("=" * 80)

        await conn.close()
        return True

    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")
        return False


def get_user_input():
    """获取用户输入的参数"""
    print("=" * 60)
    print("🔍 数据库表结构检查工具")
    print("=" * 60)
    print("这个工具可以帮助您检查数据库表结构并生成 Tortoise ORM 模型代码")
    print("-" * 60)

    # 1. 选择数据库连接
    print("\n📊 可用的数据库连接:")
    connections = list(settings.DATABASES.keys())
    for i, conn in enumerate(connections, 1):
        db_info = settings.DATABASES[conn]["credentials"]
        print(f"  {i}. {conn} - {db_info['host']}:{db_info['port']}/{db_info['database']}")

    while True:
        try:
            choice = input(f"\n请选择数据库连接 (1-{len(connections)}): ").strip()
            if choice.isdigit() and 1 <= int(choice) <= len(connections):
                connection = connections[int(choice) - 1]
                break
            else:
                print(f"❌ 请输入 1 到 {len(connections)} 之间的数字")
        except (ValueError, KeyboardInterrupt):
            print("\n👋 用户取消操作")
            sys.exit(0)

    print(f"✅ 已选择数据库连接: {connection}")

    # 2. 选择表名
    print("\n📋 表名设置:")
    print("  1. 检查所有表")
    print("  2. 检查指定表")

    while True:
        table_choice = input("\n请选择 (1-2): ").strip()
        if table_choice == "1":
            table = None
            print("✅ 将检查所有表")
            break
        elif table_choice == "2":
            table = input("请输入表名: ").strip()
            if table:
                print(f"✅ 将检查表: {table}")
                break
            else:
                print("❌ 表名不能为空")
        else:
            print("❌ 请输入 1 或 2")

    # 3. 选择输出方式
    print("\n💾 输出方式:")
    print("  1. 输出到控制台")
    print("  2. 保存到文件")

    while True:
        output_choice = input("\n请选择输出方式 (1-2): ").strip()
        if output_choice == "1":
            output_file = None
            print("✅ 将输出到控制台")
            break
        elif output_choice == "2":
            default_filename = f"{table or 'all_tables'}_{connection}_model.py"
            output_file = input(f"请输入文件路径 (默认: {default_filename}): ").strip()
            if not output_file:
                output_file = default_filename
            print(f"✅ 将保存到文件: {output_file}")
            break
        else:
            print("❌ 请输入 1 或 2")

    return {"connection": connection, "table": table, "output": output_file}


def main():
    """主函数"""
    try:
        # 获取用户输入
        params = get_user_input()

        print("\n🔄 开始检查数据库...")
        print(f"连接: {params['connection']}")
        print(f"表名: {params['table'] or '所有表'}")
        print(f"输出: {params['output'] or '控制台'}")

        # 运行数据库检查
        success = asyncio.run(inspect_database(params["connection"], params["table"], params["output"]))

        if not success:
            print("\n❌ 操作失败")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)

    print("\n🎉 检查完成!")
    print("感谢使用数据库表结构检查工具!")


if __name__ == "__main__":
    main()
