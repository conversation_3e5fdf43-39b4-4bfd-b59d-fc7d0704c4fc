# -*- coding: utf-8 -*-
import asyncio
import getpass
import os
import re
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


async def create_superuser():
    from tortoise import Tortoise

    from app.core.tortoise_config import TORTOISE_ORM
    from app.models.user_models import User

    # Initialize Tortoise ORM
    await Tortoise.init(TORTOISE_ORM, modules={"models": ["models.user"]})
    # Create database tables
    await Tortoise.generate_schemas()

    try:
        # 获取用户名
        while True:
            username = input("请输入用户名: ").strip()
            if username:
                break
            print("用户名不能为空，请重新输入")

        # 检查用户是否已存在
        user = await User.get_or_none(username=username)
        if user:
            print(f"用户名 {username} 已存在")
            return

        # 获取密码（优化版：两次密码错误时重新输入密码）
        while True:
            password = getpass.getpass("请输入密码: ").strip()
            if len(password) < 6:
                print("密码长度必须大于等于6位，请重新输入")
                continue

            # 确认密码
            confirm_password = getpass.getpass("请确认密码: ").strip()
            if password == confirm_password:
                print("密码设置成功!")
                break
            else:
                print("两次输入的密码不一致，请重新输入密码")
                # 密码不一致时，重新开始输入密码流程

        # 获取手机号
        while True:
            mobile = input("请输入手机号: ").strip()
            if re.match(r"^1[3-9]\d{9}$", mobile):
                break
            print("手机号格式不正确，请输入11位手机号")

        # 创建超级用户
        user = await User.create(
            username=username,
            password=User.get_password_hash(password),
            mobile=mobile,
            status=1,
            is_superuser=True,
        )
        print(f"\n超级用户 {username} 创建成功!")
    except Exception as e:
        print(f"创建过程中发生错误: {str(e)}")
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    print("=== 创建超级用户 ===")
    asyncio.run(create_superuser())
