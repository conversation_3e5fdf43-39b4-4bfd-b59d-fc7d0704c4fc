# 跨数据库查询解决方案

## 问题背景

在项目中，`WaybillProduct` 模型存储在 `default` 数据库中，而 `OrdersOrderitems` 模型存储在 `orders` 数据库中。需要根据 `outer_oi_id` 字段进行关联查询。

由于 Tortoise ORM 没有 `.join()` 方法，我们提供了多种解决方案来处理跨数据库查询。

## 解决方案

### 1. 基础分步查询方法（推荐用于开发环境）

```python
async def get_orders_basic(current_user: User):
    # 第一步：查询面单商品信息
    waybill_products = await WaybillProduct.filter(
        bind_status=2,
        is_deleted=False,
        company_id=current_user.company_id,
        inbound_order_detail__shipping_user_id=current_user.id,
    ).values("id", "waybill_code", "order_id", "sku_id", "outer_oi_id")

    # 第二步：收集关联字段值
    outer_oi_ids = [item["outer_oi_id"] for item in waybill_products if item["outer_oi_id"]]
    
    # 第三步：查询订单项信息
    order_items = await OrdersOrderitems.filter(
        ex_sub_order_id__in=outer_oi_ids
    ).values("ex_sub_order_id", "raw_product_name", "sku_id", "item_num")
    
    # 第四步：合并数据
    order_items_dict = {item["ex_sub_order_id"]: item for item in order_items}
    result = []
    for waybill_item in waybill_products:
        order_item = order_items_dict.get(waybill_item["outer_oi_id"], {})
        merged_item = {**waybill_item, **order_item}
        result.append(merged_item)
    
    return result
```

**优点：**
- 兼容性好，适用于所有数据库配置
- 逻辑清晰，易于调试
- 支持复杂的过滤条件

**缺点：**
- 需要多次数据库查询
- 数据量大时性能较差

### 2. 原生SQL跨数据库查询（推荐用于生产环境）

```python
async def get_orders_with_raw_sql(current_user: User):
    # 构建跨数据库SQL查询
    sql = """
    SELECT 
        wp.id as waybill_product_id,
        wp.waybill_code,
        wp.order_id,
        wp.sku_id as waybill_sku_id,
        wp.outer_oi_id,
        oi.ex_sub_order_id,
        oi.raw_product_name,
        oi.sku_id as order_sku_id,
        oi.item_num,
        oi.goods_price
    FROM cert_waybill_product wp
    INNER JOIN cert_inbound_order_detail iod ON wp.inbound_order_detail_id = iod.id
    LEFT JOIN zhulinks_orders.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
    WHERE wp.bind_status = $1
      AND wp.is_deleted = $2
      AND wp.company_id = $3
      AND iod.shipping_user_id = $4
    """
    
    # 执行查询
    default_db = connections.get("default")
    results = await default_db.execute_query(sql, [2, False, current_user.company_id, current_user.id])
    
    return results
```

**优点：**
- 性能最佳，单次查询完成
- 支持复杂的JOIN操作
- 可以利用数据库索引优化

**缺点：**
- 需要两个数据库在同一PostgreSQL实例中
- SQL语句较复杂，维护成本高
- 数据库名称硬编码

### 3. 使用跨数据库查询助手类

```python
from app.utils.cross_db_query import CrossDatabaseQueryHelper

async def get_orders_with_helper(current_user: User):
    results = await CrossDatabaseQueryHelper.join_models_by_field(
        primary_model=WaybillProduct,
        secondary_model=OrdersOrderitems,
        primary_filters={
            "bind_status": 2,
            "is_deleted": False,
            "company_id": current_user.company_id,
            "inbound_order_detail__shipping_user_id": current_user.id,
        },
        join_field_primary="outer_oi_id",
        join_field_secondary="ex_sub_order_id",
        primary_fields=["id", "waybill_code", "order_id", "sku_id", "outer_oi_id"],
        secondary_fields=["ex_sub_order_id", "raw_product_name", "sku_id", "item_num"],
        batch_size=1000
    )
    return results
```

**优点：**
- 封装了复杂逻辑，使用简单
- 支持批量查询优化
- 自动处理数据合并
- 提供错误处理和回退机制

**缺点：**
- 仍然是多次查询
- 需要额外的工具类维护

## 性能对比

| 方法 | 查询次数 | 适用场景 | 性能 | 维护性 |
|------|----------|----------|------|--------|
| 基础分步查询 | 2+ | 开发/小数据量 | 中等 | 高 |
| 原生SQL | 1 | 生产/大数据量 | 最佳 | 中等 |
| 助手类 | 2+ | 通用场景 | 中等 | 高 |

## 使用建议

### 开发环境
推荐使用**基础分步查询方法**，因为：
- 逻辑清晰，便于调试
- 兼容性好，不依赖特定数据库配置
- 易于修改和扩展

### 生产环境
如果两个数据库在同一PostgreSQL实例中，推荐使用**原生SQL跨数据库查询**：
- 性能最佳
- 减少网络开销
- 充分利用数据库优化

如果数据库分布在不同实例中，使用**助手类方法**：
- 提供了批量查询优化
- 有完善的错误处理
- 代码复用性好

## 测试接口

项目提供了测试接口来比较不同方法的性能：

```bash
# 测试跨数据库查询能力
GET /api/v1/test/test-cross-db

# 比较不同查询方法
GET /api/v1/test/orders/compare

# 获取数据库连接信息
GET /api/v1/test/database-info
```

## 配置要求

### 数据库配置
确保在 `local_settings.py` 中正确配置了两个数据库连接：

```python
DATABASES = {
    "default": {
        "engine": "tortoise.backends.asyncpg",
        "credentials": {
            "host": "your-host",
            "port": 5432,
            "user": "your-user",
            "password": "your-password",
            "database": "zhulinks_cert",  # 默认数据库
        },
    },
    "orders": {
        "engine": "tortoise.backends.asyncpg",
        "credentials": {
            "host": "your-host",  # 可以是同一主机
            "port": 5432,
            "user": "your-user",
            "password": "your-password",
            "database": "zhulinks_orders",  # 订单数据库
        },
    },
}
```

### 跨数据库查询要求
要使用原生SQL跨数据库查询，需要：
1. 两个数据库在同一PostgreSQL实例中
2. 用户有访问两个数据库的权限
3. 正确的数据库名称配置

## 故障排除

### 常见错误

1. **AttributeError: 'QuerySet' object has no attribute 'join'**
   - 原因：Tortoise ORM 没有 join 方法
   - 解决：使用本文档提供的替代方案

2. **跨数据库查询失败**
   - 检查数据库是否在同一实例中
   - 验证数据库名称是否正确
   - 确认用户权限

3. **性能问题**
   - 对于大数据量，使用原生SQL方法
   - 添加适当的数据库索引
   - 考虑分页查询

### 调试建议

1. 使用测试接口验证不同方法的可用性
2. 检查日志输出，了解查询执行情况
3. 监控数据库查询性能
4. 根据实际数据量选择合适的方法
