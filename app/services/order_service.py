# -*- coding: utf-8 -*-
from app.models import OrdersOrderitems, User, WaybillProduct


class OrderService:
    @staticmethod
    async def get_orders(current_user: User):
        # 获取当前用户绑定的面单
        waybill_product_relates = await WaybillProduct.filter(
            bind_status=2,
            is_deleted=False,
            company_id=current_user.company_id,
            inbound_order_detail__shipping_user_id=current_user.id,
        ).join(OrdersOrderitems, on=(WaybillProduct.outer_oi_id == OrdersOrderitems.ex_sub_order_id))

        # 根据order_id跟sku_id join orders_orderitems表
        print(waybill_product_relates)
