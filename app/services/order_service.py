# -*- coding: utf-8 -*-
from typing import Any, Dict, List

from loguru import logger

from app.models import OrdersOrderitems, User, WaybillProduct
from app.utils.cross_db_query import CrossDatabaseQueryHelper


class OrderService:
    @staticmethod
    async def get_orders(current_user: User):
        """
        获取当前用户绑定的面单及关联的订单项信息
        由于两个表在不同数据库，使用分步查询的方式
        """
        # 第一步：从 default 数据库查询面单商品信息
        waybill_products = await WaybillProduct.filter(
            bind_status=2,
            is_deleted=False,
            company_id=current_user.company_id,
            inbound_order_detail__shipping_user_id=current_user.id,
        ).values("id", "waybill_code", "order_id", "sku_id", "outer_oi_id", "bind_status", "product_name", "product_sn_code")

        if not waybill_products:
            return []

        # 第二步：收集所有需要查询的 outer_oi_id
        outer_oi_ids = [item["outer_oi_id"] for item in waybill_products if item["outer_oi_id"]]

        if not outer_oi_ids:
            return waybill_products

        # 第三步：从 orders 数据库查询订单项信息
        order_items = await OrdersOrderitems.filter(ex_sub_order_id__in=outer_oi_ids).values(
            "ex_sub_order_id",
            "raw_product_name",
            "sku_id",
            "item_num",
            "goods_price",
            "order_status",
            "order_status_desc",
            "order_amount",
            "pay_amount",
        )

        # 第四步：创建订单项字典，便于查找
        order_items_dict = {item["ex_sub_order_id"]: item for item in order_items}

        # 第五步：合并数据
        result = []
        for waybill_item in waybill_products:
            order_item = order_items_dict.get(waybill_item["outer_oi_id"], {})
            merged_item = {**waybill_item, **order_item}
            result.append(merged_item)

        return result

    @staticmethod
    async def get_orders_with_raw_sql(current_user: User):
        """
        使用原生SQL进行跨数据库查询
        注意：这种方法需要确保两个数据库在同一个PostgreSQL实例中
        """
        try:
            # 获取数据库名称映射
            db_names = await CrossDatabaseQueryHelper.get_database_names()
            orders_db_name = db_names.get("orders", "zhulinks_orders")

            # 使用工具类构建跨数据库SQL
            sql = CrossDatabaseQueryHelper.build_cross_db_sql(
                primary_table="cert_waybill_product",
                secondary_table="orders_orderitems",
                secondary_db_name=orders_db_name,
                join_condition="p.outer_oi_id = s.ex_sub_order_id",
                primary_fields=[
                    "id",
                    "waybill_code",
                    "order_id",
                    "sku_id",
                    "outer_oi_id",
                    "bind_status",
                    "product_name",
                    "product_sn_code",
                ],
                secondary_fields=[
                    "ex_sub_order_id",
                    "raw_product_name",
                    "sku_id",
                    "item_num",
                    "goods_price",
                    "order_status",
                    "order_status_desc",
                    "order_amount",
                    "pay_amount",
                ],
                where_conditions=[
                    "p.bind_status = $1",
                    "p.is_deleted = $2",
                    "p.company_id = $3",
                    "EXISTS (SELECT 1 FROM cert_inbound_order_detail iod WHERE iod.id = p.inbound_order_detail_id AND iod.shipping_user_id = $4)",
                ],
                join_type="LEFT JOIN",
            )

            # 执行跨数据库查询
            results = await CrossDatabaseQueryHelper.execute_cross_db_sql(
                primary_db="default",
                secondary_db="orders",
                sql=sql,
                params=[2, False, current_user.company_id, current_user.id],
                fallback_to_separate_queries=True,
            )

            if results:
                return results
            else:
                # 如果跨数据库查询失败，回退到分步查询
                logger.info("跨数据库查询无结果，回退到分步查询")
                return await OrderService.get_orders(current_user)

        except Exception as e:
            logger.error(f"跨数据库查询失败: {e}")
            # 如果跨数据库查询失败，回退到分步查询
            return await OrderService.get_orders(current_user)

    @staticmethod
    async def get_orders_optimized(current_user: User) -> List[Dict[str, Any]]:
        """
        优化版本：使用批量查询减少数据库访问次数
        """
        # 第一步：查询面单商品信息（包含关联的入库单明细）
        waybill_products = (
            await WaybillProduct.filter(
                bind_status=2,
                is_deleted=False,
                company_id=current_user.company_id,
                inbound_order_detail__shipping_user_id=current_user.id,
            )
            .prefetch_related("inbound_order_detail")
            .values("id", "waybill_code", "order_id", "sku_id", "outer_oi_id", "bind_status", "product_name", "product_sn_code", "inbound_order_detail__inbound_order_code")
        )

        if not waybill_products:
            return []

        # 第二步：批量查询订单项信息
        outer_oi_ids = [item["outer_oi_id"] for item in waybill_products if item["outer_oi_id"]]

        order_items_dict = {}
        if outer_oi_ids:
            # 分批查询，避免IN子句过长
            batch_size = 1000
            for i in range(0, len(outer_oi_ids), batch_size):
                batch_ids = outer_oi_ids[i : i + batch_size]
                batch_order_items = await OrdersOrderitems.filter(ex_sub_order_id__in=batch_ids).values(
                    "ex_sub_order_id",
                    "raw_product_name",
                    "sku_id",
                    "item_num",
                    "goods_price",
                    "order_status",
                    "order_status_desc",
                    "order_amount",
                    "pay_amount",
                    "cost_price_amount",
                )

                # 更新字典
                for item in batch_order_items:
                    order_items_dict[item["ex_sub_order_id"]] = item

        # 第三步：合并数据并添加计算字段
        result = []
        for waybill_item in waybill_products:
            order_item = order_items_dict.get(waybill_item["outer_oi_id"], {})

            # 合并数据
            merged_item = {
                # 面单信息
                "waybill_product_id": waybill_item["id"],
                "waybill_code": waybill_item["waybill_code"],
                "order_id": waybill_item["order_id"],
                "waybill_sku_id": waybill_item["sku_id"],
                "outer_oi_id": waybill_item["outer_oi_id"],
                "bind_status": waybill_item["bind_status"],
                "waybill_product_name": waybill_item["product_name"],
                "product_sn_code": waybill_item["product_sn_code"],
                "inbound_order_code": waybill_item.get("inbound_order_detail__inbound_order_code"),
                # 订单项信息
                "order_product_name": order_item.get("raw_product_name"),
                "order_sku_id": order_item.get("sku_id"),
                "item_num": order_item.get("item_num"),
                "goods_price": order_item.get("goods_price"),
                "order_status": order_item.get("order_status"),
                "order_status_desc": order_item.get("order_status_desc"),
                "order_amount": order_item.get("order_amount"),
                "pay_amount": order_item.get("pay_amount"),
                "cost_price_amount": order_item.get("cost_price_amount"),
                # 计算字段
                "has_order_info": bool(order_item),
                "sku_match": waybill_item["sku_id"] == order_item.get("sku_id") if order_item else False,
            }

            result.append(merged_item)

        return result

    @staticmethod
    async def get_orders_with_helper(current_user: User) -> List[Dict[str, Any]]:
        """
        使用跨数据库查询助手类的简化方法
        """
        try:
            # 使用工具类进行关联查询
            results = await CrossDatabaseQueryHelper.join_models_by_field(
                primary_model=WaybillProduct,
                secondary_model=OrdersOrderitems,
                primary_filters={
                    "bind_status": 2,
                    "is_deleted": False,
                    "company_id": current_user.company_id,
                    "inbound_order_detail__shipping_user_id": current_user.id,
                },
                join_field_primary="outer_oi_id",
                join_field_secondary="ex_sub_order_id",
                primary_fields=["id", "waybill_code", "order_id", "sku_id", "outer_oi_id", "bind_status", "product_name", "product_sn_code"],
                secondary_fields=["ex_sub_order_id", "raw_product_name", "sku_id", "item_num", "goods_price", "order_status", "order_status_desc", "order_amount", "pay_amount"],
                batch_size=1000,
            )

            return results

        except Exception as e:
            logger.error(f"使用助手类查询失败: {e}")
            # 回退到基础方法
            return await OrderService.get_orders(current_user)

    @staticmethod
    async def test_cross_db_capability() -> Dict[str, Any]:
        """
        测试跨数据库查询能力
        """
        result = {"cross_db_available": False, "database_names": {}, "error": None}

        try:
            # 测试跨数据库连接
            result["cross_db_available"] = await CrossDatabaseQueryHelper.test_cross_db_connection()

            # 获取数据库名称
            result["database_names"] = await CrossDatabaseQueryHelper.get_database_names()

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"测试跨数据库能力失败: {e}")

        return result
