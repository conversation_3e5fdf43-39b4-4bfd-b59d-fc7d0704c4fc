from app.core.exceptions import BusinessException
from app.models.common_models import LiveAuth
from app.models.user_models import User
from app.schemas.live_author_schemas import LiveAuthPydantic


class LiveAuthService:
    @staticmethod
    async def create_live_auth(create_user: LiveAuthPydantic, current_user: User):
        if await LiveAuth.get_or_none(author_id=create_user.author_id):
            raise BusinessException(msg="达人id已存在")

        live_auth = LiveAuth(
            author_id=create_user.author_id,
            author_name=create_user.author_name,
            company_id=current_user.company_id,
            company=current_user.company,
        )
        await live_auth.save()
        return await LiveAuthPydantic.from_tortoise_orm(live_auth)

    @staticmethod
    async def get_live_auth_list(current_user: User):
        return await LiveAuth.filter(company_id=current_user.company_id).all()

    @staticmethod
    async def get_live_auth_enum(current_user: User):
        return await LiveAuth.filter(company_id=current_user.company_id, is_deleted=False).all()
