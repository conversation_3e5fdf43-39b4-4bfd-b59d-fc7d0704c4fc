from typing import Any, Dict

from loguru import logger

from app.models import ShippingSubscribe


class SubscribeService:
    @staticmethod
    async def create_jst_shipping_subscribe(data: dict) -> Dict[str, Any]:
        # 创建聚水潭发货订阅信息
        # https://openweb.jushuitan.com/message-doc?docType=shipped&docId=sync_shipped
        try:
            save_data = {
                "msg_from": "JST",
                "logistics_company_id": data.get("lc_id"),
                "logistics_company": data.get("logistics_company"),
                "logistics_order_id": data.get("l_id"),
                "order_id": data.get("so_id"),
                "send_date": data.get("send_date"),
                "total_qty": sum([item["qty"] for item in data.get("items")]),
                "items": data.get("items"),
            }
            await ShippingSubscribe.create(**save_data)
            return {"code": 0, "msg": "执行成功"}
        except Exception as e:
            logger.error(f"创建聚水潭发货订阅信息失败: {e}")
            return {"code": -1, "msg": "执行失败"}
