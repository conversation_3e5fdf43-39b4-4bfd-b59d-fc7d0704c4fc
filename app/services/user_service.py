import csv
import io
from typing import List

from fastapi import UploadFile
from fastapi_pagination.ext.tortoise import apaginate
from tortoise.expressions import Q

from app.core.exceptions import BusinessException, DataNotFoundException
from app.core.role_codes import ROLES_MAP
from app.filters.user_filters import UserFilterParams
from app.models import Company, User
from app.schemas.user_schemas import UserCreateRequest, UserInfoPydantic, UserUpdateRequest


class UserService:
    @staticmethod
    async def get_current_user_info(current_user: User) -> UserInfoPydantic:
        """获取当前用户信息"""
        return await UserInfoPydantic.from_tortoise_orm(current_user)

    @staticmethod
    async def get_users(current_user: User, user_filter_query: UserFilterParams) -> List[UserInfoPydantic]:
        """
        获取用户列表
        :param current_user:
        :param user_filter_query:
        :return:
        """
        query = User.filter(is_deleted=False)

        # # 添加公司过滤条件
        # if not current_user.is_superuser:
        #     query = query.filter(company=current_user.company)
        #
        # # 添加搜索条件
        # q = user_filter_query.q
        # if q:
        #     query = query.filter(Q(username__icontains=q) | Q(mobile__icontains=q))

        return await apaginate(query, user_filter_query)

    @staticmethod
    async def get_user(current_user: User, user_id: int) -> UserInfoPydantic:
        """获取用户详情"""
        if current_user.is_superuser:
            user = await User.get_or_none(user_id=user_id, is_deleted=False)
        else:
            user = await User.get_or_none(user_id=user_id, company=current_user.company, is_deleted=False)
        if not user:
            raise DataNotFoundException(msg="用户不存在")
        return user

    @staticmethod
    async def create_user(user_data: UserCreateRequest, current_user: User) -> UserInfoPydantic:
        """创建新用户"""

        # 校验手机号是否已存在
        if await User.get_or_none(mobile=user_data.mobile):
            raise BusinessException(msg="手机号已存在")

        # 校验用户名是否已存在
        if await User.get_or_none(username=user_data.username):
            raise BusinessException(msg="用户名已存在")

        if current_user.is_superuser:
            if not user_data.company_id:
                raise BusinessException(msg="公司ID不能为空")

            # 校验company_id
            company = await Company.get_or_none(company_id=user_data.company_id)
            if not company:
                raise DataNotFoundException(msg="公司不存在")

            user_data.company_id = company.id
        else:
            user_data.company_id = current_user.company_id

        user_data.password = User.get_password_hash(user_data.password)
        # 创建用户
        user = await User.create(**user_data.model_dump(), create_user_id=current_user.id)
        return await UserInfoPydantic.from_tortoise_orm(user)

    @staticmethod
    async def update_user(user_id: int, update_data: UserUpdateRequest, current_user: User) -> UserInfoPydantic:
        """更新用户信息"""
        if current_user.is_superuser:
            user = await User.get_or_none(user_id=user_id, is_deleted=False)
        else:
            user = await User.get_or_none(user_id=user_id, company=current_user.company, is_deleted=False)
        if not user:
            raise DataNotFoundException(msg="用户不存在")

        # 更新用户信息
        if update_data.password:
            update_data.password = User.get_password_hash(update_data.password)

        update_data.update_user_id = current_user.id
        await user.update_from_dict(update_data.model_dump(exclude_unset=True))
        await user.save()

        return await UserInfoPydantic.from_tortoise_orm(user)

    @staticmethod
    async def delete_user(user_id: int, current_user: User) -> None:
        """删除用户"""
        if not current_user.is_superuser:
            user = await User.get_or_none(user_id=user_id, company=current_user.company, is_deleted=False)
        else:
            user = await User.get_or_none(user_id=user_id, is_deleted=False)
        if not user:
            raise DataNotFoundException(msg="用户不存在")

        user.is_deleted = True
        user.update_user_id = current_user.id
        await user.save()

    @staticmethod
    async def batch_disable_users(user_ids: list[int], current_user: User) -> None:
        """批量禁用用户"""
        if not user_ids:
            raise BusinessException(msg="用户ID列表不能为空")

        user_str_ids = list(map(str, user_ids))
        if str(current_user.id) in user_str_ids:
            raise BusinessException(msg="不能禁用当前用户")

        if current_user.is_superuser:
            users = await User.filter(
                user_id__in=user_ids,
                is_deleted=False,
            ).all()
        else:
            users = (
                await User.filter(
                    user_id__in=user_ids,
                    company=current_user.company,
                    is_deleted=False,
                )
                .exclude(is_superuser=False)
                .all()
            )

        if not users:
            raise DataNotFoundException(msg="未找到需要禁用的用户")

        for user in users:
            user.status = 2
            user.update_user_id = current_user.id
            await user.save()

    @staticmethod
    async def batch_enable_users(user_ids: list[int], current_user: User) -> None:
        """批量启用用户"""
        if not user_ids:
            raise BusinessException(msg="用户ID列表不能为空")

        user_str_ids = list(map(str, user_ids))
        if str(current_user.id) in user_str_ids:
            raise BusinessException(msg="不能启用当前用户")

        if current_user.is_superuser:
            users = await User.filter(user_id__in=user_ids, is_deleted=False).all()
        else:
            users = await User.filter(user_id__in=user_ids, company=current_user.company, is_deleted=False).exclude(is_superuser=False).all()

        if not users:
            raise DataNotFoundException(msg="未找到需要启用的用户")

        for user in users:
            user.status = 1
            user.update_user_id = current_user.id
            await user.save()

    @staticmethod
    async def batch_delete_users(user_ids: list[int], current_user: User) -> None:
        """批量删除用户"""
        if not user_ids:
            raise BusinessException(msg="用户ID列表不能为空")

        user_str_ids = list(map(str, user_ids))
        if str(current_user.id) in user_str_ids:
            raise BusinessException(msg="不能删除当前用户")

        if current_user.is_superuser:
            users = await User.filter(user_id__in=user_ids, is_deleted=False).all()
        else:
            users = await User.filter(user_id__in=user_ids, company=current_user.company, is_deleted=False).exclude(is_superuser=False).all()

        users = await User.filter(user_id__in=user_ids, is_deleted=False).all()
        if not users:
            raise DataNotFoundException(msg="未找到需要删除的用户")

        for user in users:
            user.is_deleted = True
            user.update_user_id = current_user.id
            await user.save()

    @staticmethod
    async def import_users(file: UploadFile, current_user: User) -> dict:
        """Import users from CSV file with optimized batch processing"""
        if not file.filename.endswith(".csv"):
            raise BusinessException(msg="只支持CSV格式文件")

        # 预定义角色映射
        roles_codes_map = {v["name"]: v["code"] for v in ROLES_MAP.values()}
        BATCH_SIZE = 100  # 设置每批处理的最大数量

        try:
            # Read and parse CSV
            content = await file.read()
            content = content.decode("utf-8-sig")  # Handle BOM
            csv_reader = csv.DictReader(io.StringIO(content))

            # Validate headers
            required_headers = ["用户名", "手机号码", "角色"]
            if not all(header in csv_reader.fieldnames for header in required_headers):
                raise BusinessException(msg="CSV文件格式不正确，请下载模板后使用")

            # 预处理数据
            rows = list(csv_reader)
            user_batch = []
            error_rows = []
            identifiers_to_check = set()  # 用于存储需要检查唯一性的用户名和手机号

            # First pass: 数据预处理和基础验证
            for i, row in enumerate(rows, start=2):  # CSV 头为第1行，数据从第2行开始
                try:
                    username = row["用户名"].strip()
                    mobile = row["手机号码"].strip()

                    # 基础验证
                    if not username or not mobile:
                        raise ValueError("用户名和手机号不能为空")
                    if len(mobile) > 20:
                        raise ValueError("手机号格式错误")
                    if len(username) > 50:
                        raise ValueError("用户名长度不能超过50个字符")

                    password = row.get("初始密码", "123456").strip() or "123456"
                    roles_str = row.get("角色", "").strip()
                    roles = [r.strip() for r in roles_str.split(",") if r.strip()]

                    # Validate roles
                    if not roles:
                        raise ValueError("角色不能为空")

                    invalid_roles = [r for r in roles if r not in roles_codes_map]
                    if invalid_roles:
                        raise ValueError(f"无效的角色: {', '.join(invalid_roles)}")

                    validate_roles = [roles_codes_map[r] for r in roles if r in roles_codes_map]

                    # 收集要检查的标识符
                    identifiers_to_check.add(username)
                    identifiers_to_check.add(mobile)

                    user_data = {
                        "username": username,
                        "mobile": mobile,
                        "password": User.get_password_hash(password),
                        "roles": validate_roles,
                        "company_id": current_user.company_id,
                        "company": current_user.company_id,  # 设置外键关系
                        "status": 1,
                        "is_superuser": False,  # 确保导入的用户不是超级管理员
                        "create_user_id": current_user.id,
                        "_row": i,  # 临时存储行号
                    }
                    user_batch.append(user_data)

                except Exception as e:
                    error_rows.append({"row": i, "username": row.get("用户名", ""), "error": str(e)})

            if not user_batch:
                return {"total": len(rows), "success": 0, "failed": len(rows), "errors": error_rows}

            # 批量检查用户名和手机号是否存在
            existing_users = await User.filter(Q(username__in=[u["username"] for u in user_batch]) | Q(mobile__in=[u["mobile"] for u in user_batch])).values("username", "mobile")

            existing_usernames = {user["username"] for user in existing_users}
            existing_mobiles = {user["mobile"] for user in existing_users}

            # Second pass: 过滤掉已存在的用户
            users_to_create = []
            for user_data in user_batch:
                if user_data["username"] in existing_usernames or user_data["mobile"] in existing_mobiles:
                    error_rows.append({"row": user_data["_row"], "username": user_data["username"], "error": "用户名或手机号已存在"})
                    continue

                row = user_data["_row"]
                user_data.pop("_row")  # 移除临时字段
                try:
                    users_to_create.append(user_data)
                except Exception as e:
                    error_rows.append({"row": row, "username": user_data["username"], "error": f"创建用户失败: {str(e)}"})

            success_count = 0
            # 分批处理，每批100条
            async with User._meta.db._in_transaction():
                for i in range(0, len(users_to_create), BATCH_SIZE):
                    batch = users_to_create[i : i + BATCH_SIZE]
                    try:
                        await User.bulk_create([User(**data, user_id=await User.generate_business_id("user_id", 8)) for data in batch])
                        success_count += len(batch)
                    except Exception as e:
                        # 如果批量创建失败，记录错误并继续下一批
                        for user_data in batch:
                            error_rows.append({"row": "未知", "username": user_data["username"], "error": f"批量创建失败: {str(e)}"})

            return {"total": len(rows), "success": success_count, "failed": len(error_rows), "errors": error_rows}

        except UnicodeDecodeError:
            raise BusinessException(msg="文件编码错误，请使用UTF-8编码的CSV文件")
        except Exception as e:
            raise BusinessException(msg=f"处理文件时出错: {str(e)}")

    async def get_user_enum(current_user: User):
        """
        获取当前用户枚举
        """
        return await User.filter(company_id=current_user.company_id, is_deleted=False).only("user_id", "username").all()
