from app.models.common_models import Shops
from app.models.user_models import User
from app.schemas.shops_schemas import ShopPydantic
from app.services.base_service import BusinessException


class ShopService:
    @staticmethod
    async def create_shop(create_data: ShopPydantic, current_user: User):
        if await Shops.get_or_none(shop_id=create_data.shop_id):
            raise BusinessException(msg="店铺id已存在")

        shop = Shops(
            shop_id=create_data.shop_id,
            shop_name=create_data.shop_name,
            company_id=current_user.company_id,
            create_user_id=current_user.id,
        )
        await shop.save()
        return shop

    @staticmethod
    async def update_shop(shop_id: str, update_data: ShopPydantic, current_user: User):
        shop = await Shops.get_or_none(shop_id=shop_id)
        if not shop:
            raise BusinessException(msg="店铺不存在")

        shop.shop_name = update_data.shop_name
        shop.update_user_id = current_user.id
        await shop.save()
        return shop

    @staticmethod
    async def delete_shop(shop_id: str, current_user: User):
        shop = await Shops.get_or_none(shop_id=shop_id)
        if not shop:
            raise BusinessException(msg="店铺不存在")

        shop.is_deleted = True
        shop.update_user_id = current_user.id
        await shop.save()
