from typing import List, Optional

from pydantic import BaseModel, Field, field_validator
from tortoise.contrib.pydantic import <PERSON>ydanticModel, pydantic_model_creator

from app.core.exceptions import BusinessException
from app.core.role_codes import ROLES_MAP
from app.models import User

UserInfoPydantic: PydanticModel = pydantic_model_creator(User, name="User", exclude=("id", "password"))


class UserCreateRequest(BaseModel):
    username: str = Field("", description="用户名")
    # real_name: Optional[str] = Field(None, description="真实姓名")
    # nickname: Optional[str] = Field(None, description="昵称")
    # avatar: Optional[str] = Field(None, description="头像")
    password: str = Field("", description="密码")
    mobile: str = Field("", description="手机号")
    # status: int = Field(1, description="激活状态(1:正常, 2:禁用)")
    # is_superuser: bool = Field(False, description="是否为超级管理员")
    company_id: Optional[str] = Field(None, description="公司ID(当前用户为is_superuser时，公司ID不能为空)")
    roles: List[str] = Field([], description="角色列表")

    @field_validator("username")
    def validate_username(cls, v):
        if v.isdigit():
            raise BusinessException(msg="用户名不能为纯数字")

        return v

    @field_validator("password")
    def validate_password(cls, v):
        if len(v) < 6:
            raise BusinessException(msg="密码至少需要6位")
        return v

    @field_validator("mobile")
    def validate_mobile(cls, v):
        if not v.isdigit() or len(v) != 11:
            raise BusinessException(msg="手机号格式不正确")
        return v

    @field_validator("roles")
    def validate_roles(cls, v):
        valid_roles = ROLES_MAP.keys()
        if not all(role in valid_roles for role in v):
            raise BusinessException(msg=f"无效的角色，有效角色为: {list(valid_roles)}")
        return v


class UserUpdateRequest(BaseModel):
    username: Optional[str] = Field(..., description="用户名")
    # real_name: Optional[str] = Field(None, description="真实姓名")
    # nickname: Optional[str] = Field(None, description="昵称")
    # avatar: Optional[str] = Field(None, description="头像")
    password: Optional[str] = Field(..., description="密码")
    mobile: Optional[str] = Field(..., description="手机号")
    status: Optional[int] = Field(..., description="激活状态(1:正常, 2:禁用)")
    company_id: Optional[str] = Field(..., description="公司ID")
    roles: Optional[List[str]] = Field([], description="角色列表")

    @field_validator("username")
    def validate_username(cls, v):
        if v and v.isdigit():
            raise BusinessException(msg="用户名不能为纯数字")
        return v

    @field_validator("password")
    def validate_password(cls, v):
        if v and len(v) < 6:
            raise BusinessException(msg="密码至少需要6位")
        return v

    @field_validator("mobile")
    def validate_mobile(cls, v):
        if v and (not v.isdigit() or len(v) != 11):
            raise BusinessException(msg="手机号格式不正确")
        return v

    @field_validator("roles")
    def validate_roles(cls, v):
        if v:
            valid_roles = ROLES_MAP.keys()
            if not all(role in valid_roles for role in v):
                raise BusinessException(msg=f"无效的角色，有效角色为: {list(valid_roles)}")
        return v


class UserBatchOperationRequest(BaseModel):
    user_ids: List[int] = Field(default_factory=list, description="用户user_id列表")

    @field_validator("user_ids")
    def validate_user_ids(cls, v):
        if not v:
            raise BusinessException(msg="用户ID列表不能为空")
        if len(v) > 100:
            raise BusinessException(msg="一次操作的用户数量不能超过100个")
        return v


class UserEnumResponse(BaseModel):
    user_id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
