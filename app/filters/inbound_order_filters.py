import datetime
from typing import Optional

from tortoise.queryset import QuerySet

from app.filters.base import BaseFilterSet, FilterField, PageSizePaginatorParams
from app.models.inbound_order_models import InboundOrder


class InboundOrderFilterParams(BaseFilterSet, PageSizePaginatorParams):
    code: Optional[str | None] = FilterField(None, description="入库单号", lookup_expr="icontains")
    check_company_name: Optional[str | None] = FilterField(None, description="检查公司名称", lookup_expr="icontains")
    check_user_id: Optional[str | None] = FilterField(None, description="清点人员user_id", method="filter_check_user_id")
    status: Optional[str | None] = FilterField(None, description="状态(1:待扫描, 2:待发货, 3:发货中, 4:已完成)", lookup_expr="exact")
    create_time_start: Optional[datetime.datetime | None] = FilterField(
        None,
        description="创建时间开始",
        lookup_expr="gte",
        field_name="create_time",
    )
    create_time_end: Optional[datetime.datetime | None] = FilterField(
        None,
        description="创建时间结束",
        lookup_expr="lte",
        field_name="create_time",
    )

    class Meta:
        model = InboundOrder

    @staticmethod
    async def filter_check_user_id(queryset: QuerySet, field_name: str, value: int) -> QuerySet:
        """自定义过滤方法：根据检查人员ID过滤"""
        from app.models.user_models import User

        if not value:
            return queryset.filter(pk__isnull=True)

        user = await User.get_or_none(user_id=value, is_deleted=False)
        if not user:
            return queryset.filter(pk__isnull=True)
        return queryset.filter(check_user_id=user.id)
