# -*- coding: utf-8 -*-
from tortoise import fields
from tortoise.contrib.pydantic import pydantic_model_creator

from app.models.base import BasicFieldsModel


class Company(BasicFieldsModel):
    name = fields.CharField(max_length=100, description="公司名称")
    company_id = fields.CharField(max_length=100, description="公司ID", unique=True, db_index=True)
    province_id = fields.CharField(max_length=12, description="省份ID", null=True, default=None)
    city_id = fields.CharField(max_length=12, description="城市ID", null=True, default=None)
    district_id = fields.CharField(max_length=12, description="区县ID", null=True, default=None)
    address = fields.CharField(max_length=255, description="详细地址", null=True, default=None)
    contact_user = fields.CharField(max_length=32, description="联系人", null=True, default=None)
    contact_phone = fields.CharField(max_length=20, description="联系电话", null=True, default=None)

    async def save(self, *args, **kwargs):
        if not self.company_id:
            self.company_id = await self.generate_business_id("company_id", length=8)

        return await super().save(*args, **kwargs)

    class Meta:
        table = "cert_companies"
        table_description = "公司信息表"


CompanyPydantic = pydantic_model_creator(
    Company,
    name="Company",
    exclude=(
        "id",
        "create_user_id",
        "update_user_id",
        "is_deleted",
    ),
)
