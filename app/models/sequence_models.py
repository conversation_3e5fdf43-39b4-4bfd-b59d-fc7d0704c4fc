from tortoise import fields, models


class Sequence(models.Model):
    name = fields.CharField(max_length=100, primary_key=True, description="序号名称")
    last = fields.BigIntField(default=0, description="当前序号")

    class Meta:
        table = "cert_sequence"
        table_description = "序号生成器"

    @classmethod
    async def get_last_value(cls, name: str):
        """
        获取序号
        :param name:
        :return:
        """
        seq = await cls.get_or_none(name=name)
        if not seq:
            return None
        return seq.last

    @classmethod
    async def get_next_value(cls, name: str, initial_value: int = 1):
        """
        获取下一个序号值
        :param name: 序列名称
        :param initial_value: 初始值
        :return: 下一个值
        """
        seq = await cls.get_or_none(name=name)

        if not seq:
            seq = await cls.create(name=name, last=initial_value)
            return initial_value

        # 更新序列值
        seq.last += 1
        await seq.save()
        return seq.last
