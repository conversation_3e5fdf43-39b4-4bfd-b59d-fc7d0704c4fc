import random
import string
from datetime import datetime

from tortoise import fields, models

from app.models.sequence_models import Sequence


class BasicFieldsModel(models.Model):
    id = fields.BigIntField(primary_key=True, description="自增主键")
    create_user_id = fields.IntField(description="创建人ID", null=True, default=None)
    update_user_id = fields.IntField(description="更新人ID", null=True, default=None)
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        abstract = True

    @classmethod
    async def generate_business_id(cls, lookup_field, length=9, random_str: str = string.digits, prefix: str = "1"):
        """
        生成业务ID， 例如user_id/company_id等等
        :param lookup_field: 查询字段
        :param length: 随机字符串长度
        :param random_str: 随机字符串
        :param prefix: 前缀
        :return: 业务ID
        """
        for i in range(120):
            ret = "{}{}".format(prefix, "".join(random.sample(random_str, length)))
            result = await cls.exists(**{lookup_field: ret})
            if not result:
                return ret
        else:
            raise ValueError("Can't generate business id")

    @classmethod
    async def generate_sequence_code(cls, company_id, prefix: str = "", length: int = 3):
        """
        生成序号， 例如user_id/company_id等等
        :param company_id: 公司ID
        :param prefix: 前缀
        :param length: 随机字符串长度
        :return: 序号
        """
        date_str = datetime.now().strftime("%Y%m%d")
        sequence_name = f"{prefix}{company_id}{date_str}"
        sequence_id = await Sequence.get_next_value(sequence_name)
        return f"{prefix}{date_str}" + str(sequence_id).zfill(length)
