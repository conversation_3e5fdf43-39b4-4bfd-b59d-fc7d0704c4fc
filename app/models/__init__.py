# 导入所有模型，以便在其他地方可以通过 app.models 导入
from app.models.base import BasicFieldsModel
from app.models.company_models import Company
from app.models.inbound_order_models import (
    CertTypeEnum,
    CertificateTypeEnum,
    InboundOrder,
    InboundOrderDetail,
    InboundOrderStatusEnum,
    ItemTypeEnum,        # 记录类型枚举
    ShippingWaybill,     # 面单模型
    WaybillProduct,      # 面单商品模型（合并证书绑定）
    WayBillCodeBindCertStatusEnum,
)
from app.models.sequence_models import Sequence
# OrderShippingRelate and ShippingQueryService have been removed
from app.models.subscribe_models import ShippingSubscribe
from app.models.user_models import User

__all__ = [
    "BasicFieldsModel",
    "Company",
    "CertTypeEnum",
    "CertificateTypeEnum",
    "InboundOrder",
    "InboundOrderDetail",
    "ItemTypeEnum",        # 记录类型枚举
    "ShippingWaybill",     # 面单模型
    "WaybillProduct",      # 面单商品模型（合并证书绑定）
    "WayBillCodeBindCertStatusEnum",
    "InboundOrderStatusEnum",
    "Sequence",
    "ShippingSubscribe",
    "User",
]
