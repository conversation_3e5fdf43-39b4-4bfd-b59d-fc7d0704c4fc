# -*- coding: utf-8 -*-
from tortoise import fields, models

# {
#   "wms_co_id": 13806146, // 仓库编码
#   "order_from": "open", // 订单来源
#   "o_id": 3897982, //内部单号
#   "lc_id": "ZTO", // 物流公司编码
#   "logistics_company": "中通速递", // 物流公司
#   "l_id": "78916203106481", //物流单号
#   "so_id": "25061695218936756111",//线上单号
#   "send_date": "2025-06-17 09:35:42",   // 发货时间
#   "items": [
#     {
#       "so_id": "25061695218936756111", //线上单号
#       "qty": 2, //数量
#       "name": "PETKIT", //商品名称
#       "sku_id": "test", //规格Id
#       "oi_id": 6357114,//内部子订单号
#       "outer_oi_id": "2025061600355108000" //外部子订单号
#     }
#   ]
# }


class ShippingSubscribe(models.Model):
    msg_from = fields.CharField(max_length=100, null=True, description="消息来源:JST(聚水潭)/DD(抖店)")
    logistics_company_id = fields.CharField(max_length=100, null=True, description="物流公司编码")
    logistics_company = fields.CharField(max_length=100, null=True, description="物流公司")
    logistics_order_id = fields.CharField(max_length=100, db_index=True, description="物流单号")
    order_id = fields.CharField(max_length=100, db_index=True, description="线上单号")
    send_date = fields.DatetimeField(null=True, description="发货时间")
    total_qty = fields.IntField(description="总数量", default=0)
    items = fields.JSONField(null=True, description="商品信息")
    create_date = fields.DatetimeField(auto_now_add=True, description="创建时间")

    class Meta:
        table = "cert_shipping_subscribe"
        table_description = "发货订阅表"

    async def save(self, *args, **kwargs):
        if self.msg_from == "JST":
            self.total_qty = sum([item["qty"] for item in self.items])
        return await super().save(*args, **kwargs)
