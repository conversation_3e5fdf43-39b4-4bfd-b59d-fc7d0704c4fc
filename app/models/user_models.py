from tortoise import fields

from app.models.base import BasicFieldsModel
from app.utils.hashes import pwd_context


class User(BasicFieldsModel):
    """
    用户模型
    """

    user_id = fields.BigIntField(unique=True, description="用户ID")
    username = fields.Char<PERSON>ield(max_length=50, unique=True, description="用户名")
    real_name = fields.CharField(max_length=12, description="真实姓名", null=True)
    nickname = fields.CharField(max_length=64, description="昵称", null=True)
    avatar = fields.CharField(max_length=255, description="头像", null=True)
    password = fields.CharField(max_length=255, description="密码")
    mobile = fields.CharField(max_length=20, unique=True, description="手机号")
    status = fields.SmallIntField(default=1, description="激活状态(1:正常, 2:禁用)")
    is_superuser = fields.BooleanField(default=False, description="是否为超级管理员")
    last_login_time = fields.DatetimeField(null=True, description="最后登录时间")
    company_id: int
    company = fields.ForeignKeyField(
        "models.Company",
        on_delete=fields.CASCADE,
        db_constraint=False,
        description="公司ID",
        null=True,
    )
    roles = fields.JSONField(default=[], null=True, description="角色列表")

    class Meta:
        table = "cert_users"
        table_description = "用户信息表"
        ordering = ["id"]

    async def save(self, *args, **kwargs):
        if not self.user_id:
            self.user_id = await self.generate_business_id("user_id", 8)

        await super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.username}"

    @classmethod
    def get_by_username(cls, username: str):
        """
        通过用户名获取用户
        """
        return cls.get(username=username)

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """
        验证密码
        """
        if not isinstance(hashed_password, str) or not hashed_password:
            return False

        try:
            return pwd_context.verify(plain_password, hashed_password)
        except (ValueError, TypeError, AttributeError):
            # Handle cases where the hash is invalid or corrupted
            return False

    @staticmethod
    def get_password_hash(password: str) -> str:
        """
        获取密码哈希
        """
        return pwd_context.hash(password)



