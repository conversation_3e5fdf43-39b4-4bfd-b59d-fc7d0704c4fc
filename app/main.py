from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi_pagination import add_pagination
from tortoise.contrib.fastapi import register_tortoise

from app.api.v1_router import api_router as v1_api_router
from app.core import logging
from app.core.config import settings
from app.core.redis import redis_lifespan
from app.core.tortoise_config import TORTOISE_ORM
from app.middlewares import register_middlewares

# 初始化日志
logger = logging.setup()
# 获取全局logger
app_logger = logging.get_logger()


@asynccontextmanager
async def app_lifespan(app: FastAPI):
    """
    应用程序生命周期管理器
    """
    app_logger.info("应用程序启动中...")
    try:
        # 启动时初始化 Redis
        async with redis_lifespan(app):
            app_logger.info("应用程序已成功启动")
            yield
    finally:
        # 应用关闭时的清理工作
        app_logger.info("开始应用程序关闭清理...")

        # 导入清理函数
        from app.core.logging import cleanup_logging, force_cleanup_multiprocessing

        # 清理日志系统
        cleanup_logging()

        # 清理多进程资源
        force_cleanup_multiprocessing()

        app_logger.info("应用程序已完成关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=app_lifespan,
)


# 注册中间件
register_middlewares(app)
# 配置 CORS
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 注册路由
app.include_router(v1_api_router, prefix=settings.API_V1_STR)


# 添加分页注册
add_pagination(app)


# 注册 Tortoise ORM
register_tortoise(
    app,
    config=TORTOISE_ORM,
    generate_schemas=True,
    add_exception_handlers=True,
)
