from typing import Any, Dict

from fastapi import APIRouter, Body, Query

from app.services.subscribe_service import SubscribeService

subscribe_router = APIRouter()


@subscribe_router.post(
    "/shipping_subscribe",
    tags=["订阅管理"],
    response_model=Dict[str, Any],
    summary="发货订阅",
)
async def shipping_subscribe(
    state: str = Query("JST", description="消息来源:JST(聚水潭)/DD(抖店)"),
    body: Dict[str, Any] = Body(
        ...,
        description="消息体数据,暂时只支持JST的订阅信息",
        examples=[
            {
                "lc_id": "ZTO",
                "wms_co_id": 13806146,
                "order_from": "open",
                "send_date": "2025-06-17 09:35:42",
                "so_id": "25061695218936756111",
                "o_id": 3897982,
                "logistics_company": "中通速递",
                "l_id": "78916203106481",
                "items": [{"so_id": "25061695218936756111", "qty": 2, "name": "PETKIT", "sku_id": "test", "oi_id": 6357114, "outer_oi_id": "2025061600355108000"}],
            },
        ],
    ),
):
    if state == "JST":
        ret = await SubscribeService.create_jst_shipping_subscribe(body)
        return ret
    return {"code": 0, "msg": "success"}
