# -*- coding: utf-8 -*-
"""
订单查询测试端点
用于测试跨数据库查询功能
"""

from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException
from loguru import logger

from app.api.dependencies import get_current_user
from app.models import User
from app.schemas.base import RestfulResponse
from app.services.order_service import OrderService

order_test_router = APIRouter()


@order_test_router.get("/test-cross-db", response_model=RestfulResponse[Dict[str, Any]], summary="测试跨数据库查询能力")
async def test_cross_db_capability():
    """
    测试跨数据库查询能力
    """
    try:
        result = await OrderService.test_cross_db_capability()
        return RestfulResponse(data=result)
    except Exception as e:
        logger.error(f"测试跨数据库查询能力失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@order_test_router.get("/orders/basic", response_model=RestfulResponse[List[Dict[str, Any]]], summary="基础分步查询方法")
async def get_orders_basic(current_user: User = Depends(get_current_user)):
    """
    使用基础分步查询方法获取订单数据
    """
    try:
        result = await OrderService.get_orders(current_user)
        return RestfulResponse(data=result, message=f"查询成功，共找到 {len(result)} 条记录")
    except Exception as e:
        logger.error(f"基础查询失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@order_test_router.get("/orders/raw-sql", response_model=RestfulResponse[List[Dict[str, Any]]], summary="原生SQL跨数据库查询方法")
async def get_orders_raw_sql(current_user: User = Depends(get_current_user)):
    """
    使用原生SQL进行跨数据库查询
    """
    try:
        result = await OrderService.get_orders_with_raw_sql(current_user)
        return RestfulResponse(data=result, message=f"跨数据库查询成功，共找到 {len(result)} 条记录")
    except Exception as e:
        logger.error(f"原生SQL查询失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@order_test_router.get("/orders/optimized", response_model=RestfulResponse[List[Dict[str, Any]]], summary="优化版批量查询方法")
async def get_orders_optimized(current_user: User = Depends(get_current_user)):
    """
    使用优化版批量查询方法
    """
    try:
        result = await OrderService.get_orders_optimized(current_user)
        return RestfulResponse(data=result, message=f"优化查询成功，共找到 {len(result)} 条记录")
    except Exception as e:
        logger.error(f"优化查询失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@order_test_router.get("/orders/helper", response_model=RestfulResponse[List[Dict[str, Any]]], summary="助手类查询方法")
async def get_orders_helper(current_user: User = Depends(get_current_user)):
    """
    使用跨数据库查询助手类
    """
    try:
        result = await OrderService.get_orders_with_helper(current_user)
        return RestfulResponse(data=result, message=f"助手类查询成功，共找到 {len(result)} 条记录")
    except Exception as e:
        logger.error(f"助手类查询失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@order_test_router.get("/orders/compare", response_model=RestfulResponse[Dict[str, Any]], summary="比较不同查询方法的性能")
async def compare_query_methods(current_user: User = Depends(get_current_user)):
    """
    比较不同查询方法的性能和结果
    """
    import time

    results = {"methods": {}, "summary": {}}

    methods = [
        ("basic", OrderService.get_orders),
        ("raw_sql", OrderService.get_orders_with_raw_sql),
        ("optimized", OrderService.get_orders_optimized),
        ("helper", OrderService.get_orders_with_helper),
    ]

    for method_name, method_func in methods:
        try:
            start_time = time.time()
            data = await method_func(current_user)
            end_time = time.time()

            results["methods"][method_name] = {
                "success": True,
                "count": len(data),
                "execution_time": round(end_time - start_time, 4),
                "sample_data": data[:3] if data else [],  # 只返回前3条作为样本
                "error": None,
            }

        except Exception as e:
            results["methods"][method_name] = {"success": False, "count": 0, "execution_time": 0, "sample_data": [], "error": str(e)}
            logger.error(f"方法 {method_name} 执行失败: {e}")

    # 生成总结
    successful_methods = [name for name, result in results["methods"].items() if result["success"]]
    if successful_methods:
        fastest_method = min(successful_methods, key=lambda x: results["methods"][x]["execution_time"])
        results["summary"] = {
            "successful_methods": successful_methods,
            "fastest_method": fastest_method,
            "fastest_time": results["methods"][fastest_method]["execution_time"],
            "recommendation": f"推荐使用 {fastest_method} 方法",
        }
    else:
        results["summary"] = {"successful_methods": [], "fastest_method": None, "fastest_time": 0, "recommendation": "所有方法都失败了，请检查数据库配置"}

    return RestfulResponse(data=results)


@order_test_router.get("/database-info", response_model=RestfulResponse[Dict[str, Any]], summary="获取数据库连接信息")
async def get_database_info():
    """
    获取数据库连接信息
    """
    try:
        from app.utils.cross_db_query import CrossDatabaseQueryHelper

        db_names = await CrossDatabaseQueryHelper.get_database_names()
        cross_db_available = await CrossDatabaseQueryHelper.test_cross_db_connection()

        result = {"database_connections": db_names, "cross_db_available": cross_db_available, "total_connections": len(db_names)}

        return RestfulResponse(data=result)

    except Exception as e:
        logger.error(f"获取数据库信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
