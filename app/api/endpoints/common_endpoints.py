from typing import Annotated

from fastapi import APIRouter, Depends, Query
from fastapi_pagination import Page

from app.api.dependencies import get_current_user
from app.filters.common_filters import LiveAuthFilterParams
from app.models.user_models import User
from app.schemas.base import RestfulResponse
from app.schemas.live_author_schemas import BulkCreateLiveAuthorPydantic, LiveAuthorPydantic
from app.services.live_auth_service import LiveAuthService

common_router = APIRouter()


@common_router.post("/live-authors", tags=["达人管理"], summary="创建达人", response_model=RestfulResponse[LiveAuthorPydantic])
async def create_live_auth(create_live_auth: LiveAuthorPydantic, current_user: User = Depends(get_current_user)):
    service = LiveAuthService()
    data = await service.create_live_auth(create_live_auth, current_user)
    return RestfulResponse(data=data)


@common_router.get("/live-authors", tags=["达人管理"], summary="获取达人列表", response_model=RestfulResponse[Page[LiveAuthorPydantic]])
async def get_live_auth_list(filter_query: Annotated[LiveAuthFilterParams, Query()], current_user: User = Depends(get_current_user)):
    service = LiveAuthService()
    data = await service.get_live_auth_list(filter_query, current_user)
    return RestfulResponse(data=data)


@common_router.get("/live-authors/enum", tags=["达人管理"], summary="获取达人枚举", response_model=RestfulResponse[list[LiveAuthorPydantic]])
async def get_live_auth_enum(current_user: User = Depends(get_current_user)):
    service = LiveAuthService()
    data = await service.get_live_auth_enum(current_user)
    return RestfulResponse(data=data)


@common_router.put("/live-authors/{author_id}", tags=["达人管理"], summary="更新达人", response_model=RestfulResponse[LiveAuthorPydantic])
async def update_live_auth(author_id: str, update_live_auth: LiveAuthorPydantic, current_user: User = Depends(get_current_user)):
    service = LiveAuthService()
    data = await service.update_live_auth(author_id, update_live_auth, current_user)
    return RestfulResponse(data=data)


@common_router.delete("/live-authors/{author_id}", tags=["达人管理"], summary="删除达人", response_model=RestfulResponse[None])
async def delete_live_auth(author_id: str, current_user: User = Depends(get_current_user)):
    service = LiveAuthService()
    await service.delete_live_auth(author_id, current_user)
    return RestfulResponse(data=None)


@common_router.post("/live-authors/batch", tags=["达人管理"], summary="批量创建达人", response_model=RestfulResponse[None])
async def batch_create_live_auth(batch_create_live_auth: BulkCreateLiveAuthorPydantic, current_user: User = Depends(get_current_user)):
    service = LiveAuthService()
    await service.batch_create_live_auth(batch_create_live_auth, current_user)
    return RestfulResponse(data=None)
