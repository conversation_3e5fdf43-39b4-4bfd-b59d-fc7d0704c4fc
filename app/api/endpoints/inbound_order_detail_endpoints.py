# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Depends, Query
from fastapi_pagination import Page

from app.api.dependencies import get_current_user
from app.filters.inbound_order_detail_filters import InboundOrderDetailFilterParams
from app.models.user_models import User
from app.schemas.base import RestfulResponse
from app.schemas.inbound_order_detail_schemas import (
    InboundOrderDetailBatchUpdateRequest,
    InboundOrderDetailCreateRequest,
    InboundOrderDetailInfoResponse,
)
from app.services.inbound_order_detail_service import InboundOrderDetailService

inbound_order_detail_router = APIRouter()


@inbound_order_detail_router.post(
    "",
    summary="创建",
    response_model=RestfulResponse[None],
)
async def create_inbound_order_detail(
    code: str,
    create_data: InboundOrderDetailCreateRequest,
    current_user: User = Depends(get_current_user),
    service: InboundOrderDetailService = Depends(InboundOrderDetailService),
):
    detail = await service.create(code, create_data, current_user)
    return RestfulResponse(data=None)


@inbound_order_detail_router.get(
    "",
    summary="获取入库记录清点明细列表",
    response_model=RestfulResponse[Page[InboundOrderDetailInfoResponse]],
)
async def get_inbound_order_detail_list(
    code: str,
    filter_query: Annotated[InboundOrderDetailFilterParams, Query()],
    current_user: User = Depends(get_current_user),
    service: InboundOrderDetailService = Depends(InboundOrderDetailService),
):
    data = await service.list(code, filter_query, current_user)
    return RestfulResponse(data=data)


@inbound_order_detail_router.get(
    "/{detail_code}",
    summary="获取入库记录清点明细",
    response_model=RestfulResponse[InboundOrderDetailInfoResponse],
)
async def get_inbound_order_detail_detail(
    code: str,
    detail_code: str,
    current_user: User = Depends(get_current_user),
    service: InboundOrderDetailService = Depends(InboundOrderDetailService),
):
    data = await service.detail(code, detail_code, current_user)
    return RestfulResponse(data=data)


@inbound_order_detail_router.delete(
    "/{detail_code}",
    summary="删除入库记录清点明细",
    response_model=RestfulResponse[None],
)
async def delete_inbound_order_detail(
    code: str,
    detail_code: str,
    current_user: User = Depends(get_current_user),
    service: InboundOrderDetailService = Depends(InboundOrderDetailService),
):
    await service.delete(code, detail_code, current_user)
    return RestfulResponse(data=None)


@inbound_order_detail_router.patch(
    "/{detail_code}",
    summary="更新入库记录清点明细",
    response_model=RestfulResponse[InboundOrderDetailInfoResponse],
)
async def batch_update_inbound_order_detail(
    code: str,
    detail_code: str,
    update_data: InboundOrderDetailBatchUpdateRequest,
    current_user: User = Depends(get_current_user),
    service: InboundOrderDetailService = Depends(InboundOrderDetailService),
):
    """只允许修改入库记录明细的发货人"""
    data = await service.batch_update(code, detail_code, update_data, current_user)
    return RestfulResponse(data=data)
