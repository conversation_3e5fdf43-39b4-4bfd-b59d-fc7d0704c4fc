# -*- coding: utf-8 -*-
from fastapi import APIRouter, BackgroundTasks
from jose import JWTError, jwt
from tortoise.expressions import Q

from app.core.config import settings
from app.core.exceptions import BusinessException, InvalidTokenException
from app.models import User
from app.schemas.base import RestfulResponse
from app.schemas.login_schemas import LoginRequest, LoginResponse, RefreshTokenRequest, SendSmsCodeRequest, SmsLoginRequest
from app.services.login_service import LoginServices

login_router = APIRouter()


@login_router.post(
    "/login",
    response_model=RestfulResponse[LoginResponse],
    summary="账号密码登录",
)
async def login(login_request: LoginRequest):
    username = login_request.username
    user = await User.get_or_none(Q(username=username) | Q(mobile=username))

    if not user or not user.verify_password(login_request.password, user.password):
        raise BusinessException(msg="用户名或密码错误")

    token_info = await LoginServices.login(user)
    login_response = LoginResponse(**token_info)
    return RestfulResponse(data=login_response)


@login_router.post(
    "/login/sms",
    response_model=RestfulResponse[LoginResponse],
    summary="短信验证码登录",
)
async def login_by_sms(sms_login: SmsLoginRequest):
    user = await User.get_or_none(mobile=sms_login.mobile)
    if not user:
        raise BusinessException(msg="手机号未注册")

    # 验证短信验证码
    if not await LoginServices.verify_sms_code(sms_login.mobile, sms_login.code):
        raise BusinessException(msg="验证码错误或已过期")

    token_info = await LoginServices.login(user)
    return RestfulResponse(data=LoginResponse(**token_info))


@login_router.post(
    "/sms/send",
    response_model=RestfulResponse,
    summary="发送短信验证码",
)
async def send_sms_code(request: SendSmsCodeRequest, background_tasks: BackgroundTasks):
    # 检查手机号是否已注册
    user = await User.get_or_none(mobile=request.mobile)
    if not user:
        raise BusinessException(msg="手机号未注册")

    # 发送短信验证码
    success, message = await LoginServices.send_sms_code(request.mobile, background_tasks)
    return RestfulResponse()


@login_router.post(
    "/refresh",
    response_model=RestfulResponse[LoginResponse],
    summary="刷新token",
)
async def refresh_token(request: RefreshTokenRequest):
    try:
        # Decode the refresh token from the request body
        payload = jwt.decode(request.refresh_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])

        # Verify it's a refresh token
        if not payload.get("refresh"):
            raise InvalidTokenException

        username = payload.get("sub")
        if not username:
            raise InvalidTokenException

        # Get the user
        user = await User.get_or_none(username=username)
        if not user:
            raise BusinessException(msg="用户不存在")

        # Generate new tokens
        token_info = await LoginServices.login(user)
        return RestfulResponse(data=LoginResponse(**token_info))

    except JWTError:
        raise InvalidTokenException
