# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Depends, Query
from fastapi_pagination import Page

from app.api.dependencies import get_current_user
from app.filters.inbound_order_filters import InboundOrderFilterParams
from app.models.inbound_order_models import (
    CertTypeEnum,
    InboundOrderStatusEnum,
    ShippingTypeEnum,
    WayBillCodeBindCertStatusEnum,
)
from app.models.user_models import User
from app.schemas.base import RestfulResponse
from app.schemas.inbound_order_schemas import (
    BatchDeleteInboundOrderRequest,
    InboundOrderCreateRequest,
    InboundOrderInfoPydantic,
    InboundOrderListResponse,
    InboundOrderUpdateRequest,
)
from app.services.inbound_order_service import InboundOrderService

inbound_order_router = APIRouter()


@inbound_order_router.get(
    "/enums",
    summary="获取入库记录相关枚举选项",
    response_model=RestfulResponse[dict],
)
async def get_inbound_order_enums():
    """
    获取入库记录相关的所有枚举选项
    返回格式：
    {
        "status": [{"value": 1, "label": "待扫描"}, ...],
        "shipping_type": [{"value": 1, "label": "一单一件"}, ...],
        "cert_type": [{"value": 1, "label": "一物一证"}, ...],
        "bind_cert_status": [{"value": 1, "label": "未绑定"}, ...]
    }
    """
    data = {
        "status": InboundOrderStatusEnum.choices(),
        "shipping_type": ShippingTypeEnum.choices(),
        "cert_type": CertTypeEnum.choices(),
        "bind_cert_status": WayBillCodeBindCertStatusEnum.choices(),
    }
    return RestfulResponse(data=data)


@inbound_order_router.post(
    "",
    summary="创建入库记录",
    response_model=RestfulResponse[InboundOrderInfoPydantic],
)
async def create_inbound_order(create_data: InboundOrderCreateRequest, current_user: User = Depends(get_current_user)):
    service = InboundOrderService()
    data = await service.create(create_data, current_user)
    return RestfulResponse(data=data)


@inbound_order_router.get(
    "",
    summary="获取入库记录列表",
    response_model=RestfulResponse[Page[InboundOrderListResponse]],
)
async def get_inbound_order_list(filter_query: Annotated[InboundOrderFilterParams, Query()], current_user: User = Depends(get_current_user)):
    service = InboundOrderService()
    data = await service.list(filter_query, current_user)
    return RestfulResponse(data=data)


@inbound_order_router.get(
    "/{code}",
    summary="获取入库记录详情",
    response_model=RestfulResponse[InboundOrderInfoPydantic],
)
async def get_inbound_order(code: str, current_user: User = Depends(get_current_user)):
    service = InboundOrderService()
    data = await service.detail(code, current_user)
    return RestfulResponse(data=data)


@inbound_order_router.put(
    "/{code}",
    summary="更新入库记录",
    response_model=RestfulResponse[InboundOrderInfoPydantic],
)
async def update_inbound_order(code: str, update_data: InboundOrderUpdateRequest, current_user: User = Depends(get_current_user)):
    service = InboundOrderService()
    data = await service.update(code, update_data, current_user)
    return RestfulResponse(data=data)


@inbound_order_router.delete(
    "/{code}",
    summary="删除入库记录",
    response_model=RestfulResponse[None],
)
async def delete_inbound_order(code: str, current_user: User = Depends(get_current_user)):
    service = InboundOrderService()
    await service.delete(code, current_user)
    return RestfulResponse()


@inbound_order_router.post(
    "/batch_delete",
    summary="批量删除入库记录",
    response_model=RestfulResponse[None],
)
async def batch_delete_inbound_order(batch_delete_inbound_order_request: BatchDeleteInboundOrderRequest, current_user: User = Depends(get_current_user)):
    service = InboundOrderService()
    await service.batch_delete(batch_delete_inbound_order_request.code_list, current_user)
    return RestfulResponse()
