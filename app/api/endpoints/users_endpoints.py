import os.path
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, status
from fastapi.responses import FileResponse
from fastapi_pagination import Page

from app.api.dependencies import get_current_user
from app.core.config import settings
from app.core.exceptions import BusinessException
from app.core.permissions import permission_required
from app.filters.user_filters import UserFilterParams
from app.models.user_models import User
from app.schemas.base import RestfulResponse
from app.schemas.user_schemas import (
    UserBatchOperationRequest,
    UserCreateRequest,
    UserEnumResponse,
    UserInfoPydantic,
    UserUpdateRequest,
)
from app.services.user_service import UserService

user_router = APIRouter()


@user_router.get(
    "/me",
    summary="获取当前用户信息",
    response_model=RestfulResponse[UserInfoPydantic],
)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """
    获取当前用户信息
    """
    data = await UserService.get_current_user_info(current_user)
    return RestfulResponse(data=data)


# 获取当前用户枚举
@user_router.get("/enum", summary="获取当前用户枚举", response_model=RestfulResponse[list[UserEnumResponse]])
async def get_user_enum(current_user: User = Depends(get_current_user)):
    """
    获取当前用户枚举
    """
    data = await UserService.get_user_enum(current_user)
    return RestfulResponse(data=data)


@user_router.get("/export-template", summary="导出用户导入模板")
async def export_user_template(current_user: User = Depends(get_current_user)):
    """
    导出用户导入模板CSV文件
    """

    return FileResponse(os.path.join(settings.PROJECT_ROOT, "statics", "user_import_template.csv"))


@user_router.get(
    "/{user_id}",
    response_model=RestfulResponse[UserInfoPydantic],
    summary="获取用户详情",
)
async def get_user(user_id: int, current_user: User = Depends(get_current_user)):
    """
    获取用户详情
    """
    data = await UserService.get_user(current_user, user_id)
    return RestfulResponse(data=data)


@user_router.get(
    "",
    response_model=RestfulResponse[Page[UserInfoPydantic]],
    summary="获取用户列表",
)
async def get_users(
    filter_query: Annotated[UserFilterParams, Query()],
    current_user: User = Depends(get_current_user),
):
    """
    获取用户列表

    可以通过q参数搜索用户名或手机号，也可以使用过滤器进行高级过滤
    """
    paginate_data = await UserService.get_users(current_user, filter_query)
    return RestfulResponse(data=paginate_data)


@user_router.post(
    "",
    response_model=RestfulResponse[UserInfoPydantic],
    summary="创建用户",
)
async def create_user(user_data: UserCreateRequest, current_user: User = Depends(get_current_user)):
    """
    创建新用户
    """
    data = await UserService.create_user(user_data, current_user)
    return RestfulResponse(data=data)


@user_router.put(
    "/{user_id}",
    response_model=RestfulResponse[UserInfoPydantic],
    summary="更新用户信息",
)
async def update_user(user_id: int, update_data: UserUpdateRequest, current_user: User = Depends(get_current_user)):
    """
    更新用户信息
    """
    data = await UserService.update_user(user_id, update_data, current_user)
    return RestfulResponse(data=data)


@user_router.delete(
    "/{user_id}",
    response_model=RestfulResponse[None],
    summary="删除用户",
)
async def delete_user(user_id: int, current_user: User = Depends(get_current_user)):
    """
    删除用户
    """
    await UserService.delete_user(user_id, current_user)
    return RestfulResponse()


@user_router.post(
    "/batch/disable",
    response_model=RestfulResponse[None],
    summary="批量禁用用户",
)
@permission_required("用户管理")
async def batch_disable_users(batch_data: UserBatchOperationRequest, current_user: User = Depends(get_current_user)):
    """
    批量禁用用户
    """
    await UserService.batch_disable_users(batch_data.user_ids, current_user)
    return RestfulResponse()


@user_router.post(
    "/batch/enable",
    response_model=RestfulResponse[None],
    summary="批量启用用户",
)
@permission_required("用户管理")
async def batch_enable_users(batch_data: UserBatchOperationRequest, current_user: User = Depends(get_current_user)):
    """
    批量启用用户
    """
    await UserService.batch_enable_users(batch_data.user_ids, current_user)
    return RestfulResponse()


@user_router.post(
    "/batch/delete",
    response_model=RestfulResponse[None],
    summary="批量删除用户",
)
@permission_required("用户管理")
async def batch_delete_users(batch_data: UserBatchOperationRequest, current_user: User = Depends(get_current_user)):
    """
    批量删除用户
    """
    await UserService.batch_delete_users(batch_data.user_ids, current_user)
    return RestfulResponse()


@user_router.post("/import", response_model=RestfulResponse[dict], summary="导入用户")
async def import_users(
    file: UploadFile,
    current_user: User = Depends(get_current_user),
):
    """
    todo: 优化分块读取文件
    从CSV文件导入用户
    """
    if not file.filename:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="请上传文件")

    try:
        result = await UserService.import_users(file, current_user)
        return RestfulResponse(data=result)
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"导入失败: {str(e)}")
