from fastapi import Depends
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from jose import jwt
from jose.exceptions import JWTError

from app.core.config import settings
from app.core.exceptions import InvalidTokenException
from app.models.user_models import User

security = HTTPBearer(auto_error=False)


async def get_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """
    从请求头获取 token
    """
    if not credentials:
        raise InvalidTokenException

    return credentials.credentials


async def get_current_user(token: str = Depends(get_token)) -> User:
    """
    获取当前用户
    """

    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        username: str = payload.get("sub")
        if not username:
            raise InvalidTokenException

        # 检查是否为刷新令牌
        if payload.get("refresh"):
            raise InvalidTokenException

    except JWTError:
        raise InvalidTokenException

    user = await User.get_or_none(username=username)
    if user is None:
        raise InvalidTokenException

    return user
