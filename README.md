# 珠凌zhulinks 项目服务后端配置开发

## 开发配置

### 环境配置

1. Windows下开启WSL2
2. 安装[PDM](https://pdm.fming.dev/latest/#installation)
3. Clone项目到本地
4. 在项目根目录下执行`pdm install`安装依赖

### 项目开发

1. 在项目根目录下执行`pdm run start`启动项目
2. 浏览器打开(http://127.0.0.1:8888/adminislinyun)进入后台管理界面

#### 新增模块

项目根目录下运行`pdm add 模块`


### pdm migrate遇到错误 is_deleted/is_founder column does not exist

先执行`pdm migrate users 0016 / pdm migrate users 0017 / pdm migrate users 0019`

### 用户类型
```
SP: 供应商
OP: 运营商
DB: 分销商

# 2.7.0 新增
DB_M2: 分销商-分销商模式
```

### 分销商
    2.7.0版本

    自营模式： 过滤供应商为臻品的商品，订单、售后、评论都是关联到该分销商的商品
    分销模式：
        1. 商品从分销市场加入，加入的时候默认加入到分销代号为FX的副本商品，其他分销类型(distributor_mode=2)的所有用户看到的选品池是分销市场里面的商品
        2. 【我的商品】不是副本内容，是有关联关系，然后副本是FX的副本商品
        * 注意 SubProduct.objects 所有数据库操作都会过滤is_deleted=False条件，如果需要查询所有数据，使用SubProduct.original_objects


## 订单模块
1. 会根据供应商拆单，页面显示的是供应商拆的订单，订单可以用ex_order_id进行关联
2. OriginalOrder是SKU级别的订单

## v2.8订单改造
```text
1.  刷新settings, 迁移数据，然后设置两个订单表的最大值
from scripts.versions.v280 import flush_settings
flush_settins()
```

<h2>状态码</h2>

| code | Description | fronted       |
|------|-------------|---------------|
| 401  | token过期     | 重定向到登录页       |
| 425  | 用户未微信授权     | 微信授权          |
| 1001 | 供应商未创建公司信息  | 重定向到供应商入驻界面   |
| 1002 | 供应商公司未通过审核  | 重定向到供应商审核信息界面 |

----

<h2>售后状态</h2>

| 类型         | 文案                         | as_type | as_status | refund_state |
| ------------ | ---------------------------- | ------- | --------- | ------------ |
| 未发货仅退款 | 待商家审核                   | 2       | 6         | 1            |
| 未发货仅退款 | 用户撤销                     | 2       | 28        | 1            |
| 未发货仅退款 | 商家拒绝售后                 | 2       | 27        | 4            |
| 未发货仅退款 | 退款中                       | 2       | 12        | 2            |
| 未发货仅退款 | 退款成功                     | 2       | 12        | 3            |
| 未发货仅退款 | 退款失败                     | 2       | 12        | 4            |

-----
| 类型         | 文案                         | as_type | as_status | refund_state |
| ------------ | ---------------------------- | ------- | --------- | ------------ |
| 发货仅退款   | 待商家审核                   | 1       | 6         | 1            |
| 发货仅退款   | 用户撤销                     | 1       | 28        | 1            |
| 发货仅退款   | 商家拒绝售后                 | 1       | 27        | 4            |
| 发货仅退款   | 退款中                       | 1       | 12        | 2            |
| 发货仅退款   | 退款成功                     | 1       | 12        | 3            |
| 发货仅退款   | 退款失败                     | 1       | 12        | 4            |

-----
| 类型         | 文案                         | as_type | as_status | refund_state |
| ------------ | ---------------------------- | ------- | --------- | ------------ |
| 退货退款     | 待商家审核                   | 0       | 6         | 1            |
| 退货退款     | 用户撤销                     | 0       | 28        | 1            |
| 退货退款     | 商家拒绝售后                 | 0       | 27        | 4            |
| 退货退款     | 待退货（等待用户退货）       | 0       | 7         | 1            |
| 退货退款     | 待收退货（等待商家确认收货） | 0       | 11        | 1            |
| 退货退款     | 退款中                       | 0       | 12        | 2            |
| 退货退款     | 退款成功                     | 0       | 12        | 3            |
| 退货退款     | 退款失败                     | 0       | 12        | 4            |

-----
| 类型         | 文案                         | as_type | as_status | refund_state |
| ------------ | ---------------------------- | ------- | --------- | ------------ |
| 换货         | 待商家审核                   | 3       | 6         | 1            |
| 换货         | 待退货（等待用户退货）       | 3       | 7         | 1            |
| 换货         | 待收退货（等待商家确认收货） | 3       | 11        | 1            |
| 换货         | 已收换货                     | 3       | 15        | 1            |
| 换货         | 商家发货                     | 3       | 13        | 1            |
| 换货         | 换货完成                     | 3       | 14        | 1            |