# ZhuLinks 认证系统

这是一个基于 FastAPI 构建的企业级认证管理系统，主要用于处理公司认证、用户管理、订单处理等业务。系统采用现代化的架构设计，支持高并发访问和分布式部署。

## 技术栈

- **Python 3.10+**
- **FastAPI**: 现代、快速的 Web 框架
- **Tortoise-ORM**: 异步 ORM 框架
- **PostgreSQL**: 主数据库
- **Aerich**: 数据库迁移工具
- **Pydantic**: 数据验证和设置管理
- **Loguru**: 日志管理
- **Python-jose**: JWT 认证
- **Passlib**: 密码哈希
- **Uvicorn**: ASGI 服务器

## 项目结构

```
app/
├── __init__.py
├── main.py                 # 应用程序入口
├── api/                    # API 路由
│   ├── endpoints/         # API 端点实现
│   └── v1_router.py       # V1 API 路由注册
├── core/                   # 核心配置
│   ├── config.py          # 配置管理
│   ├── exceptions.py      # 异常定义
│   └── tortoise_config.py # ORM 配置
├── middlewares/           # 中间件
│   ├── error_handler.py   # 错误处理
│   └── request_log.py     # 请求日志
├── models/                # 数据模型
│   ├── base.py           # 基础模型
│   ├── company.py        # 公司模型
│   └── user.py           # 用户模型
├── schemas/               # Pydantic 模型
│   ├── base.py           # 基础响应模型
│   └── login_schema.py   # 登录相关模型
└── services/             # 业务逻辑
    ├── base_service.py   # 基础服务
    └── login_services.py # 登录服务
```

## 核心功能

1. **用户认证系统**
    - JWT 认证机制
    - 用户注册/登录
    - 权限管理

2. **公司认证管理**
    - 企业信息管理
    - 认证状态追踪
    - 认证文档管理

3. **订单管理系统**
    - 入库订单管理
    - 订单状态追踪
    - 订单明细管理

4. **订阅服务**
    - 服务订阅管理
    - 订阅状态追踪
    - 订阅续费提醒

## 系统架构

1. **API 层**
    - RESTful API 设计
    - 统一的响应格式
    - 完整的文档支持

2. **服务层**
    - 业务逻辑处理
    - 数据验证
    - 事务管理

3. **数据层**
    - PostgreSQL 数据库
    - Tortoise-ORM
    - Redis 缓存

4. **中间件**
    - 请求日志
    - 错误处理
    - CORS 支持
    - JWT 认证

## 开发环境配置

1. **依赖管理**
    - 使用 pyproject.toml 管理依赖
    - 支持 Poetry 或 uv 作为包管理工具
    - 阿里云镜像加速

2. **数据库配置**
    - PostgreSQL 作为主数据库
    - Redis 用于缓存
    - Aerich 用于数据库迁移

3. **环境配置**
    - 使用 local_settings.py 管理配置
    - 支持开发、测试、生产环境
    - 环境变量配置

## 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd zhulinks_cert
```

2. **设置环境**
```bash
# 创建虚拟环境
uv venv .venv

# 同步依赖
uv sync
```

3. **配置环境变量**
复制 `local_settings.py.example` 到 `local_settings.py` 并根据需要修改配置：
```bash
cp local_settings.py.example local_settings.py
```

4. **数据库初始化**
```bash
# 初始化数据库配置
aerich init -t app.core.tortoise_config.TORTOISE_ORM

# 初始化数据库
aerich init-db

# 创建初始迁移
aerich migrate

# 应用迁移
aerich upgrade
```

5. **运行服务**
```bash
python run.py
```

服务将在 `http://localhost:8000` 启动，API 文档可在 `http://localhost:8000/api/v1/docs` 访问。

## API 文档

系统提供完整的 API 文档：
- Swagger UI: `/api/v1/docs`
- ReDoc: `/api/v1/redoc`

## 数据库结构

1. **用户相关表**
    - 用户信息表
    - 用户角色表
    - 权限表

2. **公司相关表**
    - 公司信息表
    - 认证状态表
    - 认证文档表

3. **订单相关表**
    - 入库订单表
    - 订单明细表
    - 订单状态表

4. **订阅相关表**
    - 服务订阅表
    - 订阅状态表
    - 服务套餐表

## 性能优化

1. **缓存策略**
    - Redis 缓存常用数据
    - 缓存失效策略

2. **数据库优化**
    - 索引优化
    - 查询优化
    - 连接池配置

3. **异步处理**
    - 异步数据库操作
    - 异步任务处理
    - 异步文件处理

## 安全特性

1. **认证授权**
    - JWT 认证
    - 密码哈希加密
    - 会话管理

2. **数据安全**
    - 数据加密
    - 敏感信息处理
    - 安全审计

3. **API 保护**
    - 请求验证
    - 速率限制
    - CORS 配置

## 开发规范

1. **代码规范**
    - PEP 8 遵循
    - 代码注释
    - 错误处理

2. **测试规范**
    - 单元测试
    - 集成测试
    - API 测试

3. **部署规范**
    - 环境配置
    - 数据库迁移
    - 容器化部署

## 常见问题

1. **数据库连接问题**
    - 检查数据库配置
    - 检查数据库服务
    - 检查防火墙设置

2. **认证问题**
    - 检查 JWT 配置
    - 检查密码策略
    - 检查会话配置

3. **性能问题**
    - 检查缓存配置
    - 检查数据库索引
    - 检查异步处理

## 贡献指南

1. **提交代码**
    - Fork 项目
    - 创建特性分支
    - 提交代码
    - 创建 Pull Request

2. **代码审查**
    - 代码规范检查
    - 功能测试
    - 安全审查

3. **文档更新**
    - 更新 README
    - 更新 API 文档
    - 更新配置文档

## 许可证

[MIT License](LICENSE)

2. **设置环境**

```bash
# 创建虚拟环境
uv venv .venv

# 同步依赖文件
uv sync
```

3. **配置环境变量**

复制 `.env.example` 到 `.env` 并根据需要修改配置：

```bash
cp local_settings.py.example local_settings.py
```

4. **数据库迁移**

```bash
* 初始化数据库配置
aerich init -t app.core.tortoise_config.TORTOISE_ORM

* 初始化数据库
aerich init-db

* 新的数据库迁移
aerich migrate

*更新迁移文件
aerich upgrade
```

5. **运行服务**

```bash
python run.py
```

服务将在 `http://localhost:8000` 启动，API 文档可在 `http://localhost:8000/api/v1/docs` 访问。

## API 文档

- Swagger UI: `/api/v1/docs`
- ReDoc: `/api/v1/redoc`

## 安全特性

- JWT 认证
- 密码哈希加密
- CORS 保护
- 请求验证

## 开发指南

1. **新增 API 端点**
    - 在 `app/api/endpoints` 创建新的路由文件
    - 在 `app/api/v1_router.py` 注册路由

2. **添加新模型**
    - 在 `app/models` 创建模型文件
    - 在 `app/core/tortoise_config.py` 注册模型
    - 使用 Aerich 创建迁移

3. **自定义中间件**
    - 在 `app/middlewares` 添加新的中间件
    - 在 `app/middlewares/__init__.py` 注册

4. **数据库规范**
    - 所有外键关联使用自增id进行关联

## 测试

```bash
pytest
```

## 许可证

[MIT License](LICENSE)
