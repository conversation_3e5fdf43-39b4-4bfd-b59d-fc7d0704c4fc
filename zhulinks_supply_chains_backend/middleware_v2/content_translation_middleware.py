import json
import re
from urllib.parse import urlencode

from django.utils.deprecation import MiddlewareMixin

from common import logger
from common.models_v2 import InternationalizedDictionaryModel
from common.utils_v2.processing_translation import json_content_translation_replacement
from utils.redis_lock import redis_conn


class ContentTranslationMiddleware(MiddlewareMixin):
    def process_request(self, request):
        # 去除前端请求接口的参数 __mobile
        language = request.headers.get("Language", "zh")
        if language == "en":
            query_params = request.GET.copy()
            style = query_params.pop("style", None)
            if style:
                style = style[0]
                query_params.update({"style": InternationalizedDictionaryModel.objects.get_zh_translation(style)})
                request.GET = query_params
                request.META["QUERY_STRING"] = urlencode(query_params, doseq=True)

    def process_view(self, request, view_func, view_args, view_kwargs):
        pass

    def process_exception(self, request, exception):
        pass

    def process_response(self, request, response):
        language = request.headers.get("Language", "zh")

        if language == "zh":
            return response

        user = getattr(request, "user", None)
        distributor_mode = getattr(getattr(user, "distributor", None), "distributor_mode", None)

        if distributor_mode == 1:
            return response
        try:
            # if is_distributor_mode
            if request.path in ["/v1/common/areas", "/v1/products/attrlist"]:
                # 款式特殊返回中英结合
                return response

            # 确保响应内容是 JSON 格式
            content = response.content
            if not isinstance(content, bytes):
                return response

            content_string = content.decode("utf-8")
            result_content = json.loads(content_string)

            translated_content = json_content_translation_replacement(result_content)

            match = re.match(r"^/v1/products/(op|db)_product/handcard/\d+$", request.path)
            if match:
                data = translated_content.get("data", None)
                if data:
                    key = f"{data['product_id']}_handcard"
                    redis_conn.set(key, json.dumps(data), 120)
            # 将翻译后的内容重新设置到响应中
            response.content = json.dumps(translated_content, ensure_ascii=False).encode("utf-8")
            return response

        except json.JSONDecodeError:
            # 无法解析为 JSON，直接返回原始响应
            return response
        except Exception as e:
            # 记录异常并返回原始响应
            print(f"国际化接口翻译报错如下: {e}")
            logger.error(f"国际化中间件翻译报错如下: {e}")
            return response
