from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_client", "0003_erpcompanycustomerinfo_company"),
        ("erp_purchase", "0076_alter_erpoutboundorder_options_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="erpoutboundorder",
            options={
                "verbose_name": "ERP供应链出库单",
                "verbose_name_plural": "ERP供应链出库单",
            },
        ),
        migrations.AddField(
            model_name="erpoutboundorder",
            name="settle_status",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "待结算"), (2, "已结算")],
                default=1,
                verbose_name="结算状态",
            ),
        ),
        migrations.AddField(
            model_name="inboundorder",
            name="settle_status",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "待结算"), (2, "已结算")],
                default=1,
                verbose_name="结算状态",
            ),
        ),
        migrations.AddConstraint(
            model_name="erpoutboundorder",
            constraint=models.UniqueConstraint(
                fields=("company", "code"),
                name="erp_erpoutboundorder_unique_company_code",
            ),
        ),
    ]
