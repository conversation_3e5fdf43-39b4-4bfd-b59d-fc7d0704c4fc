# Generated by Django 5.1.7 on 2025-04-19 06:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_purchase", "0072_erpoutboundorder_inventory_id"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="erpoutboundorder",
            name="inventory_id",
        ),
        migrations.AddField(
            model_name="erpoutboundorder",
            name="inbound_order",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_purchase.inboundorder",
                verbose_name="所属入库单",
            ),
        ),
    ]
