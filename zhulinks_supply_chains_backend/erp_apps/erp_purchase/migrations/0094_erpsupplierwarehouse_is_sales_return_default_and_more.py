# Generated by Django 5.0.8 on 2025-05-28 02:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_purchase", "0093_alter_erpoutboundorderdetailinventoryrelate_quantity"),
    ]

    operations = [
        migrations.AddField(
            model_name="erpsupplierwarehouse",
            name="is_sales_return_default",
            field=models.BooleanField(
                default=False, verbose_name="是否为销售退默认供应商"
            ),
        ),
        migrations.AddField(
            model_name="inboundorder",
            name="order_type",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "正常采购"), (2, "销售退入库")],
                default=1,
                verbose_name="采购单类型",
            ),
        ),
        migrations.AlterField(
            model_name="erpskuinventory",
            name="order_type",
            field=models.IntegerField(
                choices=[
                    (1, "采购入库"),
                    (2, "退货入库"),
                    (3, "调拨入库"),
                    (4, "还货入库"),
                    (5, "补货入库"),
                    (6, "流转入库"),
                ],
                default=1,
                verbose_name="订单来源",
            ),
        ),
    ]
