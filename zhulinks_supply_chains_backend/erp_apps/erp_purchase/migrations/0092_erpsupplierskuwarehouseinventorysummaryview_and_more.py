# Generated by Django 5.1.7 on 2025-05-27 03:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_purchase", "0091_erpskuinventory_other_return_quantity"),
    ]

    operations = [
        migrations.CreateModel(
            name="ErpSupplierSkuWarehouseInventorySummaryView",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "sku_pk",
                    models.BigIntegerField(
                        blank=True, null=True, verbose_name="SKU主键"
                    ),
                ),
                (
                    "warehouse_id",
                    models.BigIntegerField(blank=True, null=True, verbose_name="仓库"),
                ),
                (
                    "company_id",
                    models.BigIntegerField(
                        blank=True, null=True, verbose_name="公司company_ID"
                    ),
                ),
                (
                    "total_in_warehouse_quantity",
                    models.BigIntegerField(blank=True, null=True),
                ),
                (
                    "total_loaned_quantity",
                    models.BigIntegerField(blank=True, null=True),
                ),
                ("total_sold_quantity", models.BigIntegerField(blank=True, null=True)),
                (
                    "total_purchase_return_quantity",
                    models.BigIntegerField(blank=True, null=True),
                ),
                (
                    "total_stock_transfer_quantity",
                    models.BigIntegerField(blank=True, null=True),
                ),
                (
                    "total_sold_return_quantity",
                    models.BigIntegerField(blank=True, null=True),
                ),
                (
                    "total_loaned_return_quantity",
                    models.BigIntegerField(blank=True, null=True),
                ),
            ],
            options={
                "verbose_name": "ERP_SKU仓库库存汇总视图",
                "verbose_name_plural": "ERP_SKU仓库库存汇总视图",
                "db_table": "erp_supplier_sku_warehouse_inventory_summary_view",
                "managed": False,
            },
        ),
        migrations.AddField(
            model_name="erpoutboundorder",
            name="warehouse",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_purchase.erpsupplierwarehouse",
                verbose_name="出库仓库",
            ),
        ),
        migrations.AddField(
            model_name="erpoutboundorder",
            name="warehouse_position",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_purchase.erpsupplierwarehouseposition",
                verbose_name="仓位",
            ),
        ),
        migrations.AlterField(
            model_name="erpoutboundorderdetail",
            name="warehouse",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_purchase.erpsupplierwarehouse",
                verbose_name="出库仓库：废弃",
            ),
        ),
        migrations.AlterField(
            model_name="erpoutboundorderdetail",
            name="warehouse_position",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_purchase.erpsupplierwarehouseposition",
                verbose_name="仓位：废弃",
            ),
        ),
        migrations.CreateModel(
            name="ERPOutboundOrderDetailInventoryRelate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("quantity", models.IntegerField(default=0, verbose_name="销售数量")),
                (
                    "tid_info",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="TID信息"
                    ),
                ),
                (
                    "outbound_order",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.erpoutboundorder",
                        verbose_name="关联销售订单",
                    ),
                ),
                (
                    "outbound_order_detail",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.erpoutboundorderdetail",
                        verbose_name="关联销售订单明细",
                    ),
                ),
                (
                    "sku_inventory",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.erpskuinventory",
                        verbose_name="关联SKU库存",
                    ),
                ),
            ],
            options={
                "verbose_name": "ERP供应链出库明细与库存单关联",
                "verbose_name_plural": "ERP供应链出库明细与库存单关联",
                "unique_together": {
                    ("outbound_order", "outbound_order_detail", "sku_inventory")
                },
            },
        ),
        migrations.RunSQL(
            """
             CREATE OR REPLACE VIEW erp_supplier_sku_warehouse_inventory_summary_view AS
                SELECT sku_id AS sku_pk,
                    warehouse_id,
                    company_id,
                    sum(in_warehouse_quantity) AS total_in_warehouse_quantity,
                    sum(loaned_quantity) AS total_loaned_quantity,
                    sum(sold_quantity) AS total_sold_quantity,
                    sum(purchase_return_quantity) AS total_purchase_return_quantity,
                    sum(stock_transfer_quantity) AS total_stock_transfer_quantity,
                    sum(sold_return_quantity) AS total_sold_return_quantity,
                    sum(loaned_return_quantity) AS total_loaned_return_quantity
                FROM erp_purchase_erpskuinventory t
                WHERE is_deleted IS FALSE AND status = 1
                GROUP BY sku_id, company_id, warehouse_id
            """
        ),
    ]
