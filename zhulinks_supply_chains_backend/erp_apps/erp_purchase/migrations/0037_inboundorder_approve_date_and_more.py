# Generated by Django 5.0.8 on 2025-03-26 06:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_purchase", "0036_remove_inboundorder_print_barcode_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="inboundorder",
            name="approve_date",
            field=models.DateTimeField(blank=True, null=True, verbose_name="审核时间"),
        ),
        migrations.AddField(
            model_name="inboundorder",
            name="approve_status",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "待审核"), (2, "已审核")],
                db_index=True,
                default=1,
                verbose_name="审核状态",
            ),
        ),
        migrations.AddField(
            model_name="inboundorder",
            name="approve_user",
            field=models.CharField(
                blank=True, db_index=True, default="", null=True, verbose_name="审核人"
            ),
        ),
        migrations.AddField(
            model_name="inboundorder",
            name="remark",
            field=models.TextField(
                blank=True, default="", null=True, verbose_name="入库单备注"
            ),
        ),
        migrations.AddField(
            model_name="inboundorderdetail",
            name="batch_no",
            field=models.CharField(
                blank=True, max_length=25, null=True, verbose_name="批次号"
            ),
        ),
        migrations.AddField(
            model_name="inboundorderdetail",
            name="inbound_detail_code",
            field=models.CharField(
                blank=True,
                editable=False,
                max_length=32,
                null=True,
                unique=True,
                verbose_name="明细单号",
            ),
        ),
        migrations.AddField(
            model_name="inboundorderdetail",
            name="label_price",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="标签价"
            ),
        ),
        migrations.AddField(
            model_name="inboundorderdetail",
            name="market_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="市场|吊牌价",
            ),
        ),
        migrations.AddField(
            model_name="inboundorderdetail",
            name="purchase_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="采购价",
            ),
        ),
        migrations.AddField(
            model_name="inboundorderdetail",
            name="remark",
            field=models.TextField(
                blank=True, default="", null=True, verbose_name="备注信息"
            ),
        ),
        migrations.AlterField(
            model_name="erpgoodsreceiptsorder",
            name="receipt_date",
            field=models.DateTimeField(blank=True, null=True, verbose_name="入库时间"),
        ),
        migrations.AlterField(
            model_name="inboundorderdetail",
            name="cost_price",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="成本价"
            ),
        ),
    ]
