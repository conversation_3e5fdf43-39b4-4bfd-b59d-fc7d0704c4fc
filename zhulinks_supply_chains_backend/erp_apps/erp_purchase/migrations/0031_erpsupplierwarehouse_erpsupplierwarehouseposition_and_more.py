# Generated by Django 5.0.8 on 2025-03-24 07:23

import django.db.models.deletion
import erp_apps.erp_purchase.models.warehouse_models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_purchase", "0030_remove_erpreturnorders_quantity_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ERPSupplierWarehouse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("name", models.CharField(max_length=50, verbose_name="仓库名称")),
                (
                    "allow_out",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "允许"), (2, "禁止")],
                        default=1,
                        verbose_name="允许出仓",
                    ),
                ),
                (
                    "allow_in",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "允许"), (2, "禁止")],
                        default=1,
                        verbose_name="允许入仓",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True,
                        default=erp_apps.erp_purchase.models.warehouse_models._supplier_warehouse_code_generator,
                        editable=False,
                        verbose_name="仓库编码",
                    ),
                ),
                (
                    "remark",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="备注信息"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "供应商仓库资料",
                "verbose_name_plural": "供应商仓库资料",
            },
        ),
        migrations.CreateModel(
            name="ERPSupplierWarehousePosition",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("name", models.CharField(max_length=50, verbose_name="仓库名称")),
                (
                    "code",
                    models.CharField(
                        db_index=True, max_length=16, verbose_name="仓位码"
                    ),
                ),
                (
                    "remark",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="备注信息"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.erpsupplierwarehouse",
                        verbose_name="所属仓库",
                    ),
                ),
            ],
            options={
                "verbose_name": "仓位信息",
                "verbose_name_plural": "仓位信息",
            },
        ),
        migrations.AddConstraint(
            model_name="erpsupplierwarehouse",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("code", "company"),
                name="unique_warehouse_code_in_company",
            ),
        ),
        migrations.AddConstraint(
            model_name="erpsupplierwarehouseposition",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("code", "company"),
                name="unique_warehouse_position_code_in_company",
            ),
        ),
    ]
