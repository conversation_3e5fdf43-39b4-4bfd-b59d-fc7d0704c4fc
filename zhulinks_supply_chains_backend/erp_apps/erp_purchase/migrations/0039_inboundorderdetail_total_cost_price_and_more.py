# Generated by Django 5.0.8 on 2025-03-26 08:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_purchase", "0038_alter_inboundorderdetail_inbound_detail_code"),
    ]

    operations = [
        migrations.AddField(
            model_name="inboundorderdetail",
            name="total_cost_price",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=20, verbose_name="成本价"
            ),
        ),
        migrations.AddField(
            model_name="inboundorderdetail",
            name="total_label_price",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=20, verbose_name="标签价"
            ),
        ),
        migrations.AddField(
            model_name="inboundorderdetail",
            name="total_market_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="市场|吊牌价",
            ),
        ),
        migrations.AddField(
            model_name="inboundorderdetail",
            name="total_purchase_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="采购价",
            ),
        ),
    ]
