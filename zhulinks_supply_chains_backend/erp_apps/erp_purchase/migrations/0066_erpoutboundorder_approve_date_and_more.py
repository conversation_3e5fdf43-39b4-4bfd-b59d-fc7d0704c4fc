# Generated by Django 5.1.7 on 2025-04-17 05:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_purchase", "0065_merge_20250417_1343"),
    ]

    operations = [
        migrations.AddField(
            model_name="erpoutboundorder",
            name="approve_date",
            field=models.DateTimeField(blank=True, null=True, verbose_name="审核时间"),
        ),
        migrations.AddField(
            model_name="erpoutboundorder",
            name="approve_status",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "待出库审核"), (2, "待入库审核"), (3, "调拨完成")],
                db_index=True,
                default=1,
                verbose_name="审核状态",
            ),
        ),
        migrations.AddField(
            model_name="erpoutboundorder",
            name="approve_user",
            field=models.CharField(
                blank=True, db_index=True, default="", null=True, verbose_name="审核人"
            ),
        ),
    ]
