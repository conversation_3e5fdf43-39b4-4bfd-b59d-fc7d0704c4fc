# Generated by Django 5.1.7 on 2025-04-16 12:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_purchase", "0062_alter_erpskuinventory_order_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="erpoutboundorderdetail",
            name="tag_type",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "RFID标签"), (2, "普通标签")],
                default=1,
                verbose_name="标签类型",
            ),
        ),
        migrations.AlterField(
            model_name="erpskuinventory",
            name="order_type",
            field=models.IntegerField(
                choices=[(1, "采购入库"), (3, "调拨入库")], verbose_name="订单来源"
            ),
        ),
    ]
