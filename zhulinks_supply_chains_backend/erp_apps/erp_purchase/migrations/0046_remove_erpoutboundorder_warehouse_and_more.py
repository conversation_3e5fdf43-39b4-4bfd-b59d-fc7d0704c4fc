# Generated by Django 5.0.8 on 2025-04-07 06:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_purchase", "0045_erpoutboundorder_erpoutboundorderdetail"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="erpoutboundorder",
            name="warehouse",
        ),
        migrations.RemoveField(
            model_name="erpoutboundorder",
            name="warehouse_position",
        ),
        migrations.AddField(
            model_name="erpoutboundorderdetail",
            name="warehouse",
            field=models.ForeignKey(
                db_constraint=False,
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_purchase.purchasewarehouse",
                verbose_name="出库仓库",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="erpoutboundorderdetail",
            name="warehouse_position",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_purchase.erpsupplierwarehouseposition",
                verbose_name="仓位",
            ),
        ),
        migrations.AlterField(
            model_name="erpoutboundorder",
            name="code",
            field=models.CharField(
                db_index=True, editable=False, max_length=32, verbose_name="出库单号"
            ),
        ),
        migrations.AddConstraint(
            model_name="erpoutboundorder",
            constraint=models.UniqueConstraint(
                fields=("company", "code"),
                name="erp_erpoutboundorder_unique_company_code",
            ),
        ),
    ]
