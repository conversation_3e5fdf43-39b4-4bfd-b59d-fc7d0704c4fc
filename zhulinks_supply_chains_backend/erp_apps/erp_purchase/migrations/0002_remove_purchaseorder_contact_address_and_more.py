# Generated by Django 5.1.2 on 2025-03-03 06:37

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_purchase", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="purchaseorder",
            name="contact_address",
        ),
        migrations.AlterField(
            model_name="purchaseorder",
            name="payment_period_days",
            field=models.PositiveIntegerField(
                blank=True,
                null=True,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(9999),
                ],
                verbose_name="账期天数",
            ),
        ),
    ]
