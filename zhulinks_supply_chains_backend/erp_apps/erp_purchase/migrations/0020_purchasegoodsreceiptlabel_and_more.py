# Generated by Django 5.0.8 on 2025-03-13 06:12

import django.db.models.deletion
import erp_apps.erp_purchase.models.goods_receipts_models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_products", "0018_erpproductrelations"),
        (
            "erp_purchase",
            "0019_remove_purchaseorderdetail_unique_purchase_order_detail_sku_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="PurchaseGoodsReceiptLabel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("name", models.CharField(max_length=32, verbose_name="标签名称")),
                (
                    "type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "系统标签"), (2, "自定义标签")],
                        default=1,
                        verbose_name="标签类型",
                    ),
                ),
                (
                    "order",
                    models.PositiveSmallIntegerField(
                        default=0, help_text="从小到大排序", verbose_name="排序"
                    ),
                ),
            ],
            options={
                "verbose_name": "采购入库标签",
                "verbose_name_plural": "采购入库标签",
            },
        ),
        migrations.AlterModelOptions(
            name="purchasereceiptlabel",
            options={
                "ordering": ["order", "id"],
                "verbose_name": "采购管理标签",
                "verbose_name_plural": "采购管理标签",
            },
        ),
        migrations.AddField(
            model_name="purchasewarehouse",
            name="create_user",
            field=models.CharField(
                blank=True, default="", null=True, verbose_name="创建者"
            ),
        ),
        migrations.AddField(
            model_name="purchasewarehouse",
            name="is_deleted",
            field=models.BooleanField(default=False, verbose_name="是否删除"),
        ),
        migrations.AddField(
            model_name="purchasewarehouse",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="children",
                to="erp_purchase.purchasewarehouse",
                verbose_name="父级仓库",
            ),
        ),
        migrations.AddField(
            model_name="purchasewarehouse",
            name="update_user",
            field=models.CharField(
                blank=True, default="", null=True, verbose_name="最后更新人"
            ),
        ),
        migrations.AddField(
            model_name="purchasewarehouse",
            name="warehouse_type",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "仓储方"), (2, "仓库")], default=2, verbose_name="仓库类型"
            ),
        ),
        migrations.CreateModel(
            name="ERPGoodsReceiptsOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "code",
                    models.CharField(
                        default=erp_apps.erp_purchase.models.goods_receipts_models._goods_receipt_order_code_generator,
                        editable=False,
                        max_length=20,
                        unique=True,
                        verbose_name="采购入库单号",
                    ),
                ),
                (
                    "order_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "正常入库单"), (2, "手工入库单")],
                        default=1,
                        verbose_name="入库单类型",
                    ),
                ),
                (
                    "product_type",
                    models.CharField(
                        choices=[("FN", "成品"), ("SF", "半成品")],
                        default="FN",
                        max_length=2,
                        verbose_name="单据商品类型",
                    ),
                ),
                (
                    "receipt_status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "待入库"), (2, "已入库")],
                        default=1,
                        verbose_name="入库状态",
                    ),
                ),
                (
                    "financial_status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "未审核"), (2, "已审核")],
                        default=1,
                        verbose_name="财务审核状态",
                    ),
                ),
                (
                    "receipt_date",
                    models.DateField(blank=True, null=True, verbose_name="入库时间"),
                ),
                (
                    "receipt_user",
                    models.CharField(blank=True, null=True, verbose_name="入库人"),
                ),
                (
                    "remark",
                    models.CharField(blank=True, null=True, verbose_name="备注信息"),
                ),
                (
                    "actual_quantity",
                    models.IntegerField(default=0, verbose_name="实际入库数量"),
                ),
                (
                    "actual_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="实际入库金额",
                    ),
                ),
                (
                    "entry_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "采购入仓")], default=1, verbose_name="进仓类型"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="关联供应商",
                    ),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.purchaseorders",
                        to_field="code",
                        verbose_name="关联的采购订单",
                    ),
                ),
                (
                    "storage_party",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="storage_party_orders",
                        to="erp_purchase.purchasewarehouse",
                        verbose_name="仓储方",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.purchasewarehouse",
                        verbose_name="入库仓库",
                    ),
                ),
                (
                    "labels",
                    models.ManyToManyField(
                        to="erp_purchase.purchasegoodsreceiptlabel",
                        verbose_name="关联入库标签",
                    ),
                ),
            ],
            options={
                "verbose_name": "采购入库单",
                "verbose_name_plural": "采购入库单",
            },
        ),
        migrations.CreateModel(
            name="ERPGoodsReceiptOrderDetail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "detail_code",
                    models.CharField(
                        default=erp_apps.erp_purchase.models.goods_receipts_models._goods_receipt_order_detail_code_generator,
                        editable=False,
                        max_length=12,
                        unique=True,
                        verbose_name="明细单号",
                    ),
                ),
                (
                    "cost_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="成本价"
                    ),
                ),
                (
                    "quantity",
                    models.PositiveIntegerField(default=1, verbose_name="入库数"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="总金额",
                    ),
                ),
                (
                    "erp_sku",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpstockkeepingunit",
                        to_field="sku_id",
                        verbose_name="关联SKU",
                    ),
                ),
                (
                    "goods_receipt_order",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.erpgoodsreceiptsorder",
                        verbose_name="关联入库单",
                    ),
                ),
            ],
            options={
                "verbose_name": "入库明细",
                "verbose_name_plural": "入库明细",
            },
        ),
        migrations.AddConstraint(
            model_name="erpgoodsreceiptorderdetail",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("erp_sku",),
                name="unique_receipt_order_erp_sku",
            ),
        ),
    ]
