from django.urls import path, include
from rest_framework.routers import DefaultRouter

from erp_apps.erp_purchase.views import *
from erp_purchase.views.inbound_order_viewset import InboundOrderDetailViewSet

# 创建路由器
router = DefaultRouter(trailing_slash=False)
# 注册库存查询视图集
router.register(r"inventory", InventoryViewSet, basename="inventory")
# 库存调整单视图集
router.register(r"inventory_adjustment", InventoryAdjustmentOrderViewSet, basename="inventory_adjustment")
# 库存明细记录
router.register(r"inventory_change", InventoryChangeDetailsViewSet, basename="inventory_change")
# 注册出库单视图集
router.register(r"outbound_order", OutboundOrderViewSet, basename="outbound_order")
# 注册仓库视图集
router.register(r"warehouse", WarehouseViewSet, basename="warehouse")
# 注册仓位视图集
router.register(r"warehouse_position", WarehousePositionViewSet, basename="warehouse_position")
# 注册入库单视图集
router.register(r"inbound_order", InboundOrderViewSet, basename="inbound_order")
# 注册入库单明细视图集
router.register(r"inbound_order_details", InboundOrderDetailViewSet, basename="inbound_order_details")


urlpatterns = [
    # 视图集路由
    path("", include(router.urls)),
    # 以下视图为运营商ERP，作废
    # 采购【管理】标签)
    path("receipt_labels", ERPPurchaseReceiptLabelsView.as_view(), name="receipt_labels"),
    # 采购入库标签
    path("goods_receipt_labels", ERPPurchaseGoodsReceiptLabelsView.as_view(), name="goods_receipt_labels"),
    # 采购退货标签
    path("return_labels", ERPPurchaseReturnLabelsView.as_view(), name="return_labels"),
    # 采购单列表  采购单操作 审核、取消审核、完成、取消完成、作废、删除
    path("order", PurchaseOrderView.as_view(), name="order"),
    # 采购单导出
    path("order/download", PurchaseOrderDownloadView.as_view(), name="order.download"),
    # 采购单详情
    path("order/<str:purchase_order_code>", PurchaseOrderDetailView.as_view(), name="order.detail"),
    # 采购单复制
    path("order/<str:purchase_order_code>/copy", PurchaseOrderCopyView.as_view(), name="order.copy"),
    # 采购单明细
    path("order/<str:purchase_order_code>/product_detail", PurchaseOrderProductDetailView.as_view(), name="order.product_detail"),
    # 采购单操作日志
    path("order/<str:purchase_order_code>/log", PurchaseOrderLogView.as_view(), name="order.detail.log"),
    # 入库订单
    path("receipt_order", ERPGoodsReceiptOrderView.as_view(), name="receipt_order"),
    # 入库订单导出
    path("receipt_order/download", ERPGoodsReceiptOrderDownloadView.as_view(), name="receipt_order.download"),
    # 入库订单详情
    path("receipt_order/<str:receipt_code>", ERPGoodsReceiptOrderDetailView.as_view(), name="receipt_order.detail"),
    # 入库单复制
    path("receipt_order/<str:receipt_code>/copy", ERPGoodsReceiptOrderCopyView.as_view(), name="receipt_order.copy"),
    # 入库明细
    path("receipt_order/<str:receipt_code>/product_detail", ERPGoodsReceiptOrderProductDetailView.as_view(), name="receipt_order.product_detail"),
    # 入库订单日志
    path("receipt_order/<str:receipt_code>/log", ERPGoodsReceiptOrderLogView.as_view(), name="receipt_order.log"),
    # 退货订单
    path("return_order", ERPReturnOrderView.as_view(), name="return_order"),
    # 退货订单导出
    path("return_order/download", ERPReturnOrderDownloadView.as_view(), name="return_order.download"),
    # 退货单详情
    path("return_order/<str:return_code>", ERPReturnOrderDetailView.as_view(), name="return_order.detail"),
    # 退货单明细
    path("return_order/<str:return_code>/product_detail", ERPReturnOrderProductDetailView.as_view(), name="return_order.product_detail"),
    # 退货单操作日志
    path("return_order/<str:return_code>/log", ERPReturnOrderLogView.as_view(), name="return_order.detail.log"),
]
