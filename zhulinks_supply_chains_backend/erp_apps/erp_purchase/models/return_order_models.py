# -*- coding: utf-8 -*-
import time
import random
import string
from decimal import Decimal
from typing import TYPE_CHECKING

from django.db import models

from common.basics.models import BaseFieldsModel
from erp_purchase.mixins import ERPReturnOrderQueryMixin

if TYPE_CHECKING:
    from django.db.models import Manager
    from erp_purchase.models import ERPReturnOrders


def _return_code_generator(prefix: str = "TH"):
    random_digits = "".join(random.choices(string.digits, k=6))
    for _ in range(120):
        code = f"{prefix}{random_digits}"

        if not ERPReturnOrders.objects.filter(code=code).exists():
            return code
        time.sleep(0.2)
    raise ValueError("failed to generate purchase code for {prefix}, pls try again")


class ERPReturnOrders(BaseFieldsModel, ERPReturnOrderQueryMixin):
    code = models.CharField(
        max_length=20,
        unique=True,
        editable=False,
        verbose_name="退货单号",
        default=_return_code_generator,
    )
    return_date = models.DateTimeField("退货日期", null=True, blank=True, db_index=True)
    order_status = models.PositiveSmallIntegerField(
        "退货单状态",
        choices=(
            (1, "草拟"),
            (2, "生效"),
            (3, "作废"),
        ),
        db_default=1,
    )
    confirm_valid_user = models.CharField("确认生效人", null=True, blank=True)
    confirm_valid_date = models.DateField("确认生效时间", null=True, blank=True)
    return_type = models.PositiveSmallIntegerField("退货类型", choices=((1, "普通退货"),), default=1)
    financial_status = models.PositiveSmallIntegerField(
        "财务状态",
        choices=(
            (1, "待审核"),
            (2, "已审核"),
        ),
        default=1,
    )
    last_financial_date = models.DateField("最后财务审核时间", null=True, blank=True)
    last_financial_user = models.CharField("最新财务审核人", null=True, blank=True)
    # 合计数据
    product_quantity = models.IntegerField("总商品数量", default=0)
    total_amount = models.DecimalField("总金额", max_digits=20, decimal_places=2, default=Decimal(0))
    total_retail_amount = models.DecimalField("总销售金额", max_digits=20, decimal_places=2, default=Decimal(0))

    storage_party = models.ForeignKey(
        "erp_purchase.PurchaseWarehouse",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="仓储方",
        related_name="storage_party_return_orders",
    )
    warehouse = models.ForeignKey(
        "erp_purchase.PurchaseWarehouse",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="入库仓库",
    )
    labels = models.ManyToManyField("erp_purchase.PurchaseReturnLabel", blank=True, verbose_name="关联退货标签")
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="关联供应商",
    )
    receiver = models.CharField("收货人", max_length=16, null=True, blank=True, default="")
    remark = models.TextField("备注信息", null=True, blank=True, default="")

    if TYPE_CHECKING:
        erpreturnorderdetail_set: Manager["ERPReturnOrderDetail"]

    class Meta:
        verbose_name = "采购退货单"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.code

    def save_product_quantity_and_total_amount(self):
        """
        保存统计数据
        :return:
        """
        aggregated_ret = self.erpreturnorderdetail_set.filter(is_deleted=False).aggregate(
            total_quantity=models.Sum("quantity"),
            total_amount=models.Sum("amount"),
            total_retail_amount=models.Sum("retail_amount"),
        )
        self.product_quantity = aggregated_ret["total_quantity"] or 0
        self.total_amount = aggregated_ret["total_amount"] or 0
        self.total_retail_amount = aggregated_ret["total_retail_amount"] or 0
        self.save(update_fields=["product_quantity", "total_amount", "total_retail_amount"])


class ERPReturnOrderDetail(BaseFieldsModel):
    return_order = models.ForeignKey(
        ERPReturnOrders,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联退货单",
    )
    return_order_id: int
    erp_sku = models.ForeignKey(
        "erp_products.ERPStockKeepingUnit",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="sku_id",
        verbose_name="关联SKU",
    )
    cost_price = models.DecimalField("成本价", max_digits=10, decimal_places=2)
    quantity = models.PositiveIntegerField("退货数量", default=1)
    amount = models.DecimalField("退货总金额", max_digits=20, decimal_places=2, default=Decimal(0))
    retail_price = models.DecimalField(
        "基本售价", max_digits=10, decimal_places=2, null=True, blank=True, default=Decimal(0)
    )
    retail_amount = models.DecimalField(
        "基本销售额", max_digits=20, decimal_places=2, null=True, blank=True, default=Decimal(0)
    )

    class Meta:
        verbose_name = "退货明细"
        verbose_name_plural = verbose_name

    def save(self, *args, **kwargs):
        self.retail_amount = (self.retail_price or 0) * self.quantity
        self.amount = (self.cost_price or 0) * self.quantity
        super().save(*args, **kwargs)
