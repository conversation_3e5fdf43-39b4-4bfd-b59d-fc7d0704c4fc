# -*- coding: utf-8 -*-
import time
import random
import string
from decimal import Decimal
from typing import TYPE_CHECKING

from django.db import models

from erp_apps.erp_purchase.mixins import ERPPurchaseOrderDetailQueryMixin

if TYPE_CHECKING:
    from django.db.models import Manager
    from erp_purchase.models import ERPGoodsReceiptsOrder, PurchaseOrderDetail, ErpDeliveryAddress


def _purchase_code_generator(prefix: str = "CG"):
    random_digits = "".join(random.choices(string.digits, k=6))
    for _ in range(120):
        code = f"{prefix}{random_digits}"

        if not PurchaseOrders.objects.filter(code=code).exists():
            return code
        time.sleep(0.2)
    raise ValueError("failed to generate purchase code for {prefix}, pls try again")


def _purchase_order_detail_generator():
    for _ in range(120):
        code = "".join(random.choices(string.digits, k=6))
        if not PurchaseOrderDetail.objects.filter(detail_code=code).exists():
            return code
        time.sleep(0.2)
    raise ValueError("failed to generate purchase order detail code, pls try again")


class PurchaseOrders(models.Model):
    code = models.CharField(
        max_length=20,
        unique=True,
        editable=False,
        verbose_name="采购单号",
        default=_purchase_code_generator,
    )
    order_type = models.PositiveSmallIntegerField(
        "采购单类型",
        choices=((1, "普通采购单"),),
        default=1,
    )
    purchase_date = models.DateTimeField(verbose_name="采购日期", db_index=True)
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="关联供应商",
    )
    product_type = models.CharField(
        "商品类型",
        max_length=2,
        choices=(
            ("FN", "成品"),
            ("SF", "半成品"),
        ),
        default="FN",
    )
    order_status = models.PositiveSmallIntegerField(
        "单据状态",
        choices=(
            (1, "待确认"),
            (2, "已确认"),
            (3, "完成"),
            (4, "作废"),
        ),
        default=1,
    )
    receipt_status = models.PositiveSmallIntegerField(
        "入库状态",
        choices=(
            (1, "未入库"),
            (2, "部分入库"),
            (3, "全部入库"),
        ),
        default=1,
    )
    is_overload = models.PositiveSmallIntegerField(
        "是否超入",
        choices=(
            (1, "未超入"),
            (2, "超入"),
        ),
        default=1,
    )
    purchase_user = models.CharField("采购员", max_length=20, null=True, blank=True, db_index=True)
    warehouse = models.ForeignKey(
        "erp_purchase.PurchaseWarehouse",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联仓库",
    )
    pay_type = models.PositiveSmallIntegerField(
        "付款方式",
        choices=(
            (1, "现结-立付"),
            (2, "现结-到付"),
            (3, "账期结算"),
        ),
        default=None,
        null=True,
        blank=True,
    )
    payment_term_days = models.PositiveSmallIntegerField("账期天数", default=0)
    labels = models.ManyToManyField("erp_purchase.PurchaseReceiptLabel", verbose_name="关联标签")
    delivery_address = models.ForeignKey["ErpDeliveryAddress"](
        "erp_purchase.ErpDeliveryAddress",
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        verbose_name="关联收货信息",
    )
    delivery_address_id: int
    remark = models.TextField("备注信息", blank=True)
    product_quantity = models.IntegerField("订单总商品数量", default=0)
    total_amount = models.DecimalField("订单总金额", max_digits=20, decimal_places=2, default=Decimal(0))
    in_warehouse_quantity = models.IntegerField("总入库商品数量", default=0)
    in_warehouse_amount = models.DecimalField("订单总金额", max_digits=20, decimal_places=2, default=Decimal(0))

    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    update_user = models.CharField(verbose_name="最后更新人", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    approve_user = models.CharField("审核人", max_length=20, null=True, blank=True, db_index=True)
    approve_date = models.DateTimeField("审核时间", null=True, blank=True)
    confirm_finish_user = models.CharField("确认完成的人", max_length=20, null=True, blank=True, db_index=True)
    finish_date = models.DateTimeField("完成时间", null=True, blank=True)
    is_deleted = models.BooleanField("是否删除", default=False)

    if TYPE_CHECKING:
        erpgoodsreceiptsorder_set: Manager["ERPGoodsReceiptsOrder"]
        purchaseorderdetail_set: Manager["PurchaseOrderDetail"]

    class Meta:
        verbose_name = "采购单"
        verbose_name_plural = verbose_name

    def save(self, *args, **kwargs):
        # 更新入库信息
        if self.in_warehouse_quantity > (self.product_quantity or 0):
            self.is_overload = 2
            self.receipt_status = 3
        elif self.in_warehouse_quantity == (self.product_quantity or 0):
            self.is_overload = 1
            if self.in_warehouse_quantity == 0:
                self.receipt_status = 1
            else:
                self.receipt_status = 3
        elif self.in_warehouse_amount < (self.product_quantity or 0):
            self.is_overload = 1
            self.receipt_status = 2

        super().save(*args, **kwargs)

    @property
    def diff_quantity(self):
        return (self.product_quantity or 0) - (self.in_warehouse_quantity or 0)

    def update_receipt_data(self):
        """
        更新入库数据
        :return:
        """
        receipt_order_info = self.erpgoodsreceiptsorder_set.filter(receipt_status=2).aggregate(
            receipt_quantity=models.Sum("receipt_quantity"),
            receipt_amount=models.Sum("actual_amount"),
        )
        self.in_warehouse_quantity = receipt_order_info["receipt_quantity"] or 0
        self.in_warehouse_amount = receipt_order_info["receipt_amount"] or 0

        self.save(
            update_fields=[
                "is_overload",
                "receipt_status",
                "in_warehouse_quantity",
                "in_warehouse_amount",
            ]
        )

    def update_product_quantity_and_amount(self):
        """
        更新明细统计的数据，添加、编辑、删除明细重新统计
        :return:
        """
        aggregated_ret = self.purchaseorderdetail_set.filter(is_deleted=False).aggregate(
            total_quantity=models.Sum("quantity"),
            total_amount=models.Sum("amount"),
        )
        self.total_amount = aggregated_ret["total_amount"] or 0
        self.product_quantity = aggregated_ret["total_quantity"] or 0
        self.save(update_fields=["total_amount", "product_quantity", "is_overload", "receipt_status"])


class PurchaseOrderDetail(models.Model, ERPPurchaseOrderDetailQueryMixin):
    detail_code = models.CharField(
        verbose_name="明细单号",
        max_length=12,
        unique=True,
        editable=False,
        default=_purchase_order_detail_generator,
    )
    purchase_order = models.ForeignKey(
        PurchaseOrders,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联采购单",
    )
    purchase_order_id: int
    erp_sku = models.ForeignKey(
        "erp_products.ERPStockKeepingUnit",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="sku_id",
        verbose_name="关联SKU",
    )
    cost_price = models.DecimalField("成本价", max_digits=10, decimal_places=2)
    quantity = models.PositiveIntegerField("采购数量", default=1)
    amount = models.DecimalField("总金额", max_digits=20, decimal_places=2, default=Decimal(0))
    receipt_quantity = models.PositiveIntegerField("入库数量", default=0)
    receipt_amount = models.DecimalField("总金额", max_digits=20, decimal_places=2, default=Decimal(0))
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_user = models.CharField(verbose_name="最后更新人", default="", null=True, blank=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "采购明细"
        verbose_name_plural = verbose_name

    def save(self, *args, **kwargs):
        self.amount = (self.quantity or 0) * Decimal(self.cost_price or 0)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.detail_code

    @property
    def diff_quantity(self):
        return (self.quantity or 0) - (self.receipt_quantity or 0)
