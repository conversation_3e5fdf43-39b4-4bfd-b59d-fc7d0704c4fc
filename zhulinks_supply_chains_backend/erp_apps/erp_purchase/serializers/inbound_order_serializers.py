# -*- coding: utf-8 -*-
import copy
import decimal
import json
import math
from decimal import ROUND_HALF_UP

from common.basic import CustomizeSerializer
from common.basics.exceptions import APIViewException
from common.formats import DATETIME_FORMAT
from common.utils import CategoryFindAncestorsCache, diff_models
from django.core.paginator import Page
from django.db import IntegrityError, transaction
from django.db.models import Case, F, Func, JSONField, Value, When
from django.utils import timezone
from erp_client.models import ERPCompanySupplierInfo
from erp_client.serializers import ERPSupplierInfoDownloadSerializer
from erp_products.models import ERPProductAttrValue, ERPProductCategory, ERPProductSKUTID, ERPProductUnit, ERPSupplierProduct, ERPSupplierProductSKU, ERPSupplierSKU
from erp_products.tasks import update_main_images_with_tmp_image_url
from erp_products.utils import decimal_to_base62
from erp_purchase.models import (
    ERPSKUInventory,
    ERPSupplierWarehouse,
    ERPSupplierWarehousePosition,
    InboundOrder,
    InboundOrderDetail,
)
from rest_framework import serializers
from utils.caches import get_user_real_name_by_user_id
from utils.http_handle import FieldsError


class SupplierQuickInboundOrderProductCreateSer(CustomizeSerializer):
    category_id = serializers.IntegerField(required=False, allow_null=True)
    unit_id = serializers.IntegerField(required=False, allow_null=True)
    supplier_id = serializers.CharField(required=True)
    company_id = serializers.CharField(required=True)

    class Meta:
        model = ERPSupplierProduct
        fields = (
            "product_type",
            "name",
            "category_id",
            "code",
            "unit_id",
            "main_images",
            "remark",
            "supplier_id",
            "company_id",
            "create_user",
        )


class SupplierQuickInboundOrderSKUCreateSer(CustomizeSerializer):
    category = serializers.ListSerializer(
        child=serializers.CharField(),
        required=False,
        allow_empty=True,
        allow_null=True,
    )
    # supplier_id = serializers.CharField(required=False)
    attr_value_ids = serializers.ListSerializer(
        child=serializers.CharField(),
        required=False,
        allow_empty=True,
        allow_null=True,
    )
    category_id = serializers.IntegerField(required=False, allow_null=True)
    unit_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    code = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = ERPSupplierProductSKU
        fields = (
            "name",
            "spec_code",
            "code",
            "spec_value",
            "cost_price",
            "label_price",
            "market_price",
            "purchase_price",
            "category",
            "category_id",
            "unit_id",
            "size",
            "weight",
            "min_inventory_capacity",
            "max_inventory_capacity",
            "remark",
            "main_images",
            "other_price1",
            "other_price2",
            "other_price3",
            "other_price4",
            "other_price5",
            "other_price6",
            "session_date",
            "other_attributes",
            "other_attributes2",
            "other_attributes3",
            "other_attributes4",
            "other_attributes5",
            "label_price_multiple",
            "label_price_multiple_type",
            # "supplier_id",
            "attr_value_ids",
        )

    def validate_category(self, value):
        if not value:
            return
        _request = self.context["request"]

        last_category_id = value[-1]

        if not ERPProductCategory.objects.filter(
            id=last_category_id,
            is_deleted=False,
            company=_request.user.company,
        ).exists():
            raise APIViewException(err_message="invalid category")

        return value

    def validate_attr_value_ids(self, value):
        if not value:
            return
        _request = self.context["request"]
        if (
            len(value)
            != ERPProductAttrValue.objects.filter(
                pk__in=value,
                is_deleted=False,
                company=_request.user.company,
            ).count()
        ):
            raise APIViewException(err_message="invalid attr value")
        return value

    def validate_supplier_id(self, value):
        _request = self.context["request"]
        if not ERPCompanySupplierInfo.objects.filter(supplier_id=value, company=_request.user.company, is_deleted=False).exists():
            raise APIViewException(err_message="invalid supplier id")
        return value

    def validate(self, attrs):
        label_price_multiple = attrs.get("label_price_multiple")
        label_price_multiple_type = attrs.get("label_price_multiple_type")

        if label_price_multiple_type and label_price_multiple_type not in (1, 2):
            raise APIViewException(err_message="invalid label_price_multiple_type")

        if label_price_multiple:
            if label_price_multiple_type:
                if label_price_multiple_type == 1:
                    # 向上取整
                    label_price_multiple = math.ceil(label_price_multiple)
                elif label_price_multiple_type == 2:
                    label_price_multiple = math.floor(label_price_multiple)

            # 替换标签价格
            attrs["label_price"] = decimal.Decimal((attrs["cost_price"] or 0) * label_price_multiple).quantize(
                decimal.Decimal("0.01"),
                rounding=ROUND_HALF_UP,
            )

        return attrs


class SupplierQuickInboundOrderDetailCreateSer(CustomizeSerializer):
    supplier_id = serializers.IntegerField(required=True, allow_null=False)
    warehouse_id = serializers.IntegerField(required=True, allow_null=False)
    warehouse_position_id = serializers.IntegerField(required=False, allow_null=True)
    tag_type = serializers.IntegerField(required=False, default=1)  # 默认为RFID标签

    class Meta:
        model = InboundOrderDetail
        fields = (
            "supplier_id",
            "inbound_order",
            "purchase_type",
            "warehouse_id",
            "warehouse_position_id",
            "print_barcode",
            "print_count",
            "quantity",
            "in_warehouse_quantity",
            "cost_price",
            "label_price",
            "market_price",
            "purchase_price",
            "total_cost_price",
            "total_label_price",
            "total_market_price",
            "total_purchase_price",
            "batch_no",
            "sku",
            "create_user",
            "tag_type",
        )


class SupplierQuickInboundOrderCreateSer(CustomizeSerializer):
    warehouse_id = serializers.IntegerField(required=True, allow_null=False)
    warehouse_position_id = serializers.IntegerField(required=False, allow_null=True)
    product_info = serializers.DictField(
        allow_null=False,
        required=True,
        allow_empty=False,
    )
    quantity = serializers.IntegerField(required=False, min_value=1)
    print_barcode = serializers.BooleanField(required=False, default=False)
    print_count = serializers.IntegerField(required=False, default=0)
    purchase_type = serializers.IntegerField(required=False, default=None, min_value=1, max_value=2)
    auto_increase = serializers.IntegerField(required=False, allow_null=True)
    tag_type = serializers.IntegerField(required=False, default=1)  # 默认为RFID标签

    class Meta:
        model = InboundOrder
        fields = (
            "purchase_type",
            "warehouse_id",
            "warehouse_position_id",
            "quantity",
            "print_barcode",
            "print_count",
            "product_info",
            "auto_increase",
            "tag_type",
        )

    def validate(self, attrs):
        _request = self.context["request"]
        warehouse_id = attrs.get("warehouse_id", None)
        warehouse_position_id = attrs.get("warehouse_position_id", None)

        # 校验仓库
        try:
            warehouse = ERPSupplierWarehouse.objects.get(
                id=warehouse_id,
                company=_request.user.company,
                is_deleted=False,
            )
        except ERPSupplierWarehouse.DoesNotExist:
            raise APIViewException(err_message="invalid warehouse id")

        if warehouse.allow_in == 2:
            raise APIViewException(err_message=f"{warehouse.name}禁止入仓")

        # 校验仓位
        if warehouse_position_id:
            if not ERPSupplierWarehousePosition.objects.filter(
                id=warehouse_position_id,
                company=_request.user.company,
                is_deleted=False,
                warehouse_id=warehouse_id,
            ).exists():
                raise APIViewException(err_message="invalid warehouse position id")

        return attrs

    @staticmethod
    def process_tid_info(sku_pk_list, print_count, inbound_code):
        """
        处理tid信息
        :param sku_pk_list:
        :param print_count:
        :param inbound_code:
        :return:
        """
        # 获取已存在的SKU ID列表
        exist_sku_id_list = ERPProductSKUTID.objects.filter(sku_id__in=sku_pk_list).values_list("sku_id", flat=True)

        # 新增的sku
        new_sku_id_list = [sku_pk for sku_pk in sku_pk_list if sku_pk not in exist_sku_id_list]

        # 为已存在的SKU生成新的TID信息
        exist_sku_new_tid_map = {exist_sku_id: ERPProductSKUTID.batch_generate_tid_info(print_count, inbound_code) for exist_sku_id in exist_sku_id_list}

        # 构造case_when条件用于更新
        case_when_update = Case(
            *[
                When(
                    id=sku_id,
                    then=Func(
                        F("tid_info"),
                        Value(["{}"]),
                        Value(json.dumps(tid_info), output_field=JSONField()),
                        function="jsonb_set",
                        output_field=JSONField(),
                    ),
                )
                for sku_id, tid_info in exist_sku_new_tid_map.items()
            ],
            output_field=JSONField(),
            default=Value({}, output_field=JSONField()),
        )

        # 更新已存在的TID信息
        ERPProductSKUTID.objects.filter(sku_id__in=sku_pk_list).update(tid_info=case_when_update)

        # 为新的SKU生成TID信息
        new_create_tid_map = {new_sku_id: ERPProductSKUTID.batch_generate_tid_info(print_count, inbound_code) for new_sku_id in new_sku_id_list}

        # 批量创建新的TID记录
        ERPProductSKUTID.objects.bulk_create(
            [
                ERPProductSKUTID(
                    sku_id=new_sku_id,
                    tid_info=tid_info or {},
                )
                for new_sku_id, tid_info in new_create_tid_map.items()
            ]
        )

        return exist_sku_new_tid_map, new_create_tid_map

    def create(self, validated_data):
        _request = self.context["request"]
        _request_user = _request.user

        product_info = validated_data.pop("product_info", {})
        print_barcode = validated_data.get("print_barcode")
        print_count = validated_data.get("print_count")
        auto_increase = validated_data.pop("auto_increase", 0)
        quantity = validated_data.get("quantity") or 0

        spec_code = product_info.get("spec_code")

        if not spec_code:
            raise APIViewException(err_message="missing spec code")

        if print_barcode and len(spec_code) > 17:
            raise APIViewException(err_message="product spec_code length cannot more than 17 if print barcode")

        tag_type = validated_data.get("tag_type", 1)

        # 如果是普通标签，需要有print_count, 把print_count赋值给quantity
        if tag_type == 2:
            if not print_count:
                raise APIViewException(err_message="print_count is required for regular tags")
            validated_data["quantity"] = print_count

        # 如果是RFID标签，并且不是自动增长，打印数量不能大于入库数量
        if tag_type == 1 and not auto_increase and print_count > quantity:
            raise APIViewException(err_message="print_count cannot be greater than quantity")

        validated_data["create_user"] = _request_user.user_id
        validated_data["company"] = _request_user.company

        batch_no = product_info.pop("batch_no", "")
        tag_type = validated_data.pop("tag_type", 1)  # 默认为RFID标签
        inbound_detail_default_data = {
            f: validated_data.pop(f, None)
            for f in [
                "purchase_type",
                "warehouse_id",
                "warehouse_position_id",
                "quantity",
                "print_barcode",
                "print_count",
            ]
        }
        inbound_detail_default_data["tag_type"] = tag_type

        exist_sku = ERPSupplierProductSKU.objects.filter(
            spec_code=spec_code,
            company=_request_user.company,
            is_deleted=False,
        ).first()
        with transaction.atomic():
            # 创建入库单
            try:
                inbound_order, inbound_order_created = InboundOrder.objects.get_or_create(
                    inbound_code=InboundOrder.get_pre_inbound_code(_request_user.company.company_id),
                    company=_request_user.company,
                    defaults={"create_user": _request_user.user_id, "supplier_id": product_info["supplier_id"]},
                )
                if not inbound_order_created and inbound_order.approve_status == 2:
                    raise APIViewException(err_message="inbound order already approved, cannot be changed")
                setattr(inbound_order, "__created__", inbound_order_created)
            except IntegrityError:
                # 捕捉唯一约束异常，重新生成
                raise APIViewException(err_message="inbound code already exists, pls regenerate")

            if exist_sku:
                if exist_sku.tag_type:
                    if exist_sku.tag_type != tag_type:
                        raise APIViewException(err_message=f"{exist_sku.name}的标签类型与之前不一致")

                if not exist_sku.tag_type:
                    exist_sku.tag_type = tag_type
                    exist_sku.save()

            # 校验sku信息
            product_sku_valid_ser = SupplierQuickInboundOrderSKUCreateSer(data=product_info, context=self.context)
            if not product_sku_valid_ser.is_valid():
                raise FieldsError(product_sku_valid_ser.errors)

            valid_product_sku_data = product_sku_valid_ser.validated_data

            category_id_list = valid_product_sku_data.pop("category", [])
            attr_value_ids = valid_product_sku_data.pop("attr_value_ids", [])

            product_info["category_id"] = category_id_list[-1] if category_id_list else None
            product_info["create_user"] = _request_user.user_id
            product_info["company_id"] = _request_user.company.company_id

            # 获取商品
            if exist_sku:
                product = exist_sku.product
            else:
                product_ser = SupplierQuickInboundOrderProductCreateSer(data=product_info, context=self.context)
                if not product_ser.is_valid():
                    raise FieldsError(product_ser.errors)
                product = product_ser.save()
                if attr_value_ids:
                    product.attr_value.add(*attr_value_ids)

            # 批量生成sku
            if auto_increase:
                spec_codes = ERPSupplierProductSKU.batch_generate_spec_codes(_request_user.company.company_id, spec_code, print_count or 1)
            else:
                spec_codes = [spec_code]

            batch_sku_list = []
            for _spec_code in spec_codes:
                tmp_sku_info = copy.deepcopy(product_info)
                tmp_sku_info["spec_code"] = _spec_code
                batch_sku_list.append(tmp_sku_info)
            ser = SupplierQuickInboundOrderSKUCreateSer(data=batch_sku_list, context=self.context, many=True)
            if not ser.is_valid():
                # 删除占用
                ERPSupplierProductSKU.delete_pre_use_code_count_cache(_request_user.company.company_id)
                raise FieldsError(ser.errors)

            batch_create_skus = []
            for i in ser.data:
                i.pop("category", None)
                i.pop("attr_value_ids", None)
                i.pop("code", None)
                # 设置后端的值
                i["product_id"] = product.product_id
                i["company_id"] = _request_user.company.company_id
                i["create_user"] = _request_user.user_id
                i["tag_type"] = tag_type
                batch_create_skus.append(ERPSupplierProductSKU(**i))

            ERPSupplierProductSKU.objects.bulk_create(
                batch_create_skus,
                ignore_conflicts=True,
                unique_fields=["spec_code", "company"],
            )

            # 批量生成入库信息
            batch_created_skus = ERPSupplierProductSKU.objects.filter(
                company=_request_user.company,
                is_deleted=False,
                spec_code__in=spec_codes,
            ).only("id", "spec_code", "name", "sku_id")

            # 判断批量生成的入库单是否存在价格相同的商品
            if inbound_order.inboundorderdetail_set.filter(
                sku__in=batch_created_skus,
                is_deleted=False,
                warehouse_id=inbound_detail_default_data.get("warehouse_id"),
                warehouse_position_id=inbound_detail_default_data.get("warehouse_position_id"),
                cost_price=inbound_detail_default_data.get("cost_price"),
                label_price=inbound_detail_default_data.get("label_price"),
                market_price=inbound_detail_default_data.get("market_price"),
                purchase_type=inbound_detail_default_data.get("purchase_type"),
            ).exists():
                raise APIViewException(err_message="sku with same price already exists")

            if attr_value_ids:
                sku_attr_through_model = ERPSupplierProductSKU.attr_value.through

                through_objs = []
                for attr_value_id in attr_value_ids:
                    for sku in batch_created_skus:
                        through_objs.append(sku_attr_through_model(erpsupplierproductsku_id=sku.pk, erpproductattrvalue_id=attr_value_id))

                sku_attr_through_model.objects.bulk_create(
                    through_objs,
                    ignore_conflicts=True,
                    unique_fields=[
                        "erpsupplierproductsku",
                        "erpproductattrvalue",
                    ],
                )

            batch_created_sku_pk_list = [k.pk for k in batch_created_skus]

            # 生成tid号, 处理TID信息
            exist_sku_new_tid_map, new_create_tid_map = self.process_tid_info(batch_created_sku_pk_list, print_count, inbound_order.inbound_code)

            # 查询当前批次的所有tid，关联到入库明细
            batch_created_tids_map = {}
            for sku_pk, tid_info in exist_sku_new_tid_map.items():
                if sku_pk not in batch_created_tids_map:
                    batch_created_tids_map[sku_pk] = list(tid_info.keys())
                else:
                    batch_created_tids_map[sku_pk].extend(list(tid_info.keys()))

            for sku_pk, tid_info in new_create_tid_map.items():
                if sku_pk not in batch_created_tids_map:
                    batch_created_tids_map[sku_pk] = list(tid_info.keys())
                else:
                    batch_created_tids_map[sku_pk].extend(list(tid_info.keys()))

            need_return_product_info = [
                {
                    "name": created_sku.name,
                    "spec_code": created_sku.spec_code,
                    "code": product.code,
                    "tids": [f"{decimal_to_base62(created_sku.sku_id)}{tid}" for tid in (batch_created_tids_map.get(created_sku.pk) or [])],
                    "print_count": len(batch_created_tids_map.get(created_sku.pk) or []),
                    "tag_type": tag_type,
                }
                for created_sku in batch_created_skus
            ]

            exist_inbound_skus = inbound_order.inboundorderdetail_set.filter(
                sku_id__in=batch_created_sku_pk_list,
                cost_price=product_info.get("cost_price"),
                label_price=product_info.get("label_price"),
                market_price=product_info.get("market_price"),
                purchase_price=product_info.get("purchase_price"),
                is_deleted=False,
            ).values_list("sku_id", flat=True)

            need_create_inbound_detail_objs = []
            supplier_sku_bulk_objs = []
            for sku_pk in batch_created_sku_pk_list:
                if sku_pk in exist_inbound_skus:
                    continue

                tmp_detail_data = copy.deepcopy(inbound_detail_default_data)
                # 添加明细
                cost_price = decimal.Decimal(product_info.get("cost_price") or 0).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                label_price = decimal.Decimal(product_info.get("label_price") or 0).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                market_price = decimal.Decimal(product_info.get("market_price") or 0).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                purchase_price = decimal.Decimal(product_info.get("purchase_price") or 0).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)

                tmp_detail_data["cost_price"] = cost_price
                tmp_detail_data["label_price"] = label_price
                tmp_detail_data["market_price"] = market_price
                tmp_detail_data["purchase_price"] = purchase_price

                _quantity = tmp_detail_data.get("quantity") or 0

                tmp_detail_data["in_warehouse_quantity"] = _quantity
                # 统计的数据, 保留两位小数
                tmp_detail_data["total_cost_price"] = decimal.Decimal(cost_price * _quantity).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                tmp_detail_data["total_label_price"] = decimal.Decimal(label_price * _quantity).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                tmp_detail_data["total_market_price"] = decimal.Decimal(market_price * _quantity).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                tmp_detail_data["total_purchase_price"] = decimal.Decimal(purchase_price * _quantity).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                tmp_detail_data["batch_no"] = batch_no
                tmp_detail_data["sku"] = sku_pk
                tmp_detail_data["inbound_order"] = inbound_order.pk
                tmp_detail_data["supplier_id"] = product_info["supplier_id"]
                need_create_inbound_detail_objs.append(tmp_detail_data)

                # supplier、sku关联记录
                supplier_sku_bulk_objs.append(
                    {
                        "product_id": product.product_id,
                        "supplier_id": product_info["supplier_id"],
                        "company_id": product.company_id,
                        "name": product.name,
                        "sku_id": {sku_pk: sku.sku_id for sku in batch_create_skus}[sku_pk],
                        "cost_price": cost_price,
                        "label_price": label_price,
                        "market_price": market_price,
                        "purchase_price": purchase_price,
                        "create_user": _request_user.user_id,
                    }
                )

            detail_create_ser = SupplierQuickInboundOrderDetailCreateSer(data=need_create_inbound_detail_objs, context=self.context, many=True)
            if not detail_create_ser.is_valid():
                raise FieldsError(detail_create_ser.errors)

            # 批量创建入库明细
            detail_data_bulk_objs = []
            for detail_data in detail_create_ser.data:
                detail_data["inbound_order_id"] = detail_data.pop("inbound_order")
                detail_data["sku_id"] = detail_data.pop("sku")
                detail_data["create_user"] = _request_user.user_id

                # 记录tid info
                detail_data["tid_info"] = InboundOrderDetail.batch_generate_tid_info(
                    batch_created_tids_map.get(detail_data["sku_id"]) or [],
                    create_user=_request_user.user_id,
                    print_status="printed" if print_barcode else "unprinted",
                )
                detail_data_bulk_objs.append(InboundOrderDetail(**detail_data))

            InboundOrderDetail.objects.bulk_create(detail_data_bulk_objs)
            # 统计数据
            inbound_order.calculate_total_data()

            # 创建关联sku+supplier记录
            ERPSupplierSKU.bulk_create_or_update(supplier_sku_bulk_objs)

        # 记录商品
        setattr(inbound_order, "__product__", product)
        setattr(inbound_order, "__need_return_product_info__", need_return_product_info)
        return inbound_order


class SupplierInboundOrderListSer(CustomizeSerializer):
    supplier_info = serializers.SerializerMethodField()
    approve_status_display = serializers.CharField(source="get_approve_status_display")
    purchase_return_quantity = serializers.SerializerMethodField()
    purchase_not_return_quantity = serializers.SerializerMethodField()
    approve_user = serializers.SerializerMethodField()
    verified_quantity = serializers.SerializerMethodField()
    unverified_quantity = serializers.SerializerMethodField()
    total_print_quantity = serializers.SerializerMethodField()
    tag_type_display = serializers.SerializerMethodField()
    status_display = serializers.CharField(source="get_status_display")

    class Meta:
        model = InboundOrder
        fields = (
            "id",
            "inbound_code",
            "supplier_info",
            "total_quantity",
            "tag_type_display",
            "verified_quantity",
            "unverified_quantity",
            "total_print_quantity",
            "total_cost_price",
            "total_label_price",
            "approve_status",
            "approve_status_display",
            "purchase_return_quantity",
            "purchase_not_return_quantity",
            "awaiting_settle_amount",
            "create_user",
            "create_date",
            "approve_user",
            "approve_date",
            "remark",
            "remark_images",
            "settle_status",
            "status",
            "status_display",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if self.instance is None:
            return

        if isinstance(self.instance, Page):
            inbound_order_id_list = [i.pk for i in self.instance.object_list]
        elif isinstance(self.instance, InboundOrder):
            inbound_order_id_list = [self.instance.pk]
        else:
            inbound_order_id_list = [i.pk for i in self.instance]

        # 获取所有供应商信息
        detail_infos = InboundOrderDetail.objects.filter(
            inbound_order_id__in=inbound_order_id_list,
            is_deleted=False,
        ).values(
            "inbound_order_id",
            "supplier_id",
            "tid_info",
            "print_count",
            "tag_type",
        )
        supplier_id_list = set()

        # 标签类型
        self.tag_type_map = {}
        for detail_info in detail_infos:
            if detail_info["inbound_order_id"] not in self.tag_type_map:
                self.tag_type_map[detail_info["inbound_order_id"]] = {detail_info["tag_type"]}
            else:
                self.tag_type_map[detail_info["inbound_order_id"]].add(detail_info["tag_type"])

        # 供应商id
        self.inbound_order_supplier_id_map = {}
        for detail_info in detail_infos:
            supplier_id_list.add(detail_info["supplier_id"])
            if detail_info["inbound_order_id"] not in self.inbound_order_supplier_id_map:
                self.inbound_order_supplier_id_map[detail_info["inbound_order_id"]] = {detail_info["supplier_id"]}
            else:
                self.inbound_order_supplier_id_map[detail_info["inbound_order_id"]].add(detail_info["supplier_id"])

        supplier_infos = ERPCompanySupplierInfo.objects.filter(supplier_id__in=supplier_id_list).only("supplier_id", "name", "contact_user_mobile")
        self.supplier_info_map = {
            supplier_info.supplier_id: {
                "name": supplier_info.name,
                "contact_user_mobile": supplier_info.contact_user_mobile,
                "contact_user_name": supplier_info.contact_user_name,
            }
            for supplier_info in supplier_infos
        }

        # 获取校验和未校验的商品数量 todo:优化
        self.verify_result = {}
        self.print_counts_map = {}
        for detail_info in detail_infos:
            # 不统计普通标签的数量
            if detail_info["tag_type"] == 2:
                continue

            inbound_order_id = detail_info["inbound_order_id"]
            if inbound_order_id not in self.verify_result:
                self.verify_result[inbound_order_id] = {
                    "verified_count": 0,
                    "unverified_count": 0,
                }

            for tid, tid_data in detail_info["tid_info"].items():
                # 普通标签，默认每个key都加1
                if detail_info["tag_type"] == 2:
                    self.verify_result[inbound_order_id]["verified_count"] += 1
                    continue
                verify_status = tid_data.get("verify_status")
                if verify_status == "verified":
                    self.verify_result[inbound_order_id]["verified_count"] += 1
                elif verify_status == "unverified":
                    self.verify_result[inbound_order_id]["unverified_count"] += 1

            if inbound_order_id not in self.print_counts_map:
                self.print_counts_map[inbound_order_id] = detail_info["print_count"] or 0
            else:
                self.print_counts_map[inbound_order_id] += detail_info["print_count"] or 0

    def get_tag_type_display(self, obj):
        """获取标签类型"""
        tag_types = self.tag_type_map.get(obj.id) or []
        if not tag_types:
            return "未知标签"
        if 1 in tag_types:
            return "RFID标签"
        return "普通标签"

    def get_supplier_info(self, obj: InboundOrder):
        return [
            {
                "supplier_id": i,
                "name": self.supplier_info_map[i]["name"],
                "contact_user_mobile": self.supplier_info_map[i]["contact_user_mobile"],
                "contact_user_name": self.supplier_info_map[i]["contact_user_name"],
            }
            for i in self.inbound_order_supplier_id_map.get(obj.pk, [])
            if i in self.supplier_info_map
        ]

    @staticmethod
    def get_approve_user(obj: InboundOrder):
        return get_user_real_name_by_user_id(obj.approve_user)

    @staticmethod
    def get_purchase_return_quantity(obj: InboundOrder):
        # 采购退数量
        return 0

    @staticmethod
    def get_purchase_not_return_quantity(obj: InboundOrder):
        # 采购未退数量
        return 0

    def get_verified_quantity(self, obj: InboundOrder):
        # 校验的数量
        return self.verify_result.get(obj.pk, {}).get("verified_count", 0)

    def get_unverified_quantity(self, obj: InboundOrder):
        # 未校验的数量
        return self.verify_result.get(obj.pk, {}).get("unverified_count", 0)

    def get_total_print_quantity(self, obj: InboundOrder):
        # 总打印数量
        return self.print_counts_map.get(obj.pk, 0) or 0


class SupplierInboundOrderDetailInventoryQueryListSer(CustomizeSerializer):
    main_images = serializers.ListField(source="sku.main_images")
    name = serializers.CharField(source="sku.name")
    code = serializers.CharField(source="sku.product.code")
    warehouse_name = serializers.CharField(source="warehouse.name")
    warehouse_position_name = serializers.CharField(source="warehouse_position.name", allow_null=True)
    spec_value = serializers.CharField(source="sku.spec_value")
    spec_code = serializers.CharField(source="sku.spec_code")
    unit_id = serializers.IntegerField(source="sku.unit_id")
    unit_name = serializers.CharField(source="sku.unit.name", allow_null=True)
    weight = serializers.FloatField(source="sku.weight")
    supplier_id = serializers.IntegerField()
    supplier_name = serializers.CharField(source="supplier.name")
    inbound_code = serializers.CharField(source="inbound_order.inbound_code")
    min_inventory_capacity = serializers.IntegerField(source="sku.min_inventory_capacity")
    max_inventory_capacity = serializers.IntegerField(source="sku.max_inventory_capacity")
    category = serializers.SerializerMethodField()
    purchase_type = serializers.CharField(source="get_purchase_type_display")

    class Meta:
        model = InboundOrderDetail
        fields = (
            "inbound_detail_code",
            "main_images",
            "name",
            "code",
            "category",
            "spec_code",
            "in_warehouse_quantity",
            "loaned_quantity",
            "sold_quantity",
            "purchase_return_quantity",
            "stock_transfer_quantity",
            "warehouse_id",
            "warehouse_name",
            "warehouse_position_id",
            "warehouse_position_name",
            "spec_value",
            "cost_price",
            "label_price",
            "total_label_price",
            "unit_id",
            "unit_name",
            "weight",
            "remark",
            "supplier_id",
            "supplier_name",
            "purchase_type",
            "inbound_code",
            "create_date",
            "create_user",
            "min_inventory_capacity",
            "max_inventory_capacity",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        category_id_list = list({d.sku.category_id for d in self.instance.object_list if d.sku.category_id})  # type: ignore
        category_qs = ERPProductCategory.get_ancestors_with_category_id_list(category_id_list).values("id", "name", "parent_id")
        self.category_cache = CategoryFindAncestorsCache(category_qs)

    def get_category(self, obj: InboundOrderDetail):
        if not obj.sku.category_id:
            return []

        return self.category_cache.find_parents(obj.sku.category_id)


class SupplierInboundOrderDetailSer(SupplierInboundOrderDetailInventoryQueryListSer):
    attrs = serializers.SerializerMethodField()
    purchase_return_quantity = serializers.SerializerMethodField()
    purchase_not_return_quantity = serializers.SerializerMethodField()
    purchase_type = serializers.IntegerField(allow_null=True)
    purchase_type_display = serializers.CharField(source="get_purchase_type_display", allow_null=True)
    tag_type_display = serializers.CharField(source="get_tag_type_display", allow_null=True)
    remark = serializers.CharField(source="sku.remark")
    can_print_quantity = serializers.IntegerField(source="get_can_print_quantity")
    can_reprint_quantity = serializers.IntegerField(source="get_reprint_quantity")
    sku_id = serializers.IntegerField(source="sku.sku_id")

    class Meta:
        model = InboundOrderDetail
        fields = (
            "inbound_detail_code",
            "sku_id",
            "main_images",
            "spec_code",
            "code",
            "name",
            "batch_no",
            "quantity",
            "category",
            "attrs",
            "unit_id",
            "unit_name",
            "cost_price",
            "total_cost_price",
            "market_price",
            "total_market_price",
            "label_price",
            "total_label_price",
            "can_print_quantity",
            "can_reprint_quantity",
            "warehouse_id",
            "warehouse_name",
            "warehouse_position_id",
            "warehouse_position_name",
            "purchase_return_quantity",
            "purchase_not_return_quantity",
            "create_user",
            "purchase_type",
            "purchase_type_display",
            "tag_type",
            "tag_type_display",
            "remark",
            "create_date",
        )

    @staticmethod
    def get_attrs(obj: InboundOrderDetail):
        return obj.sku.get_attr_display()

    @staticmethod
    def get_purchase_return_quantity(obj: InboundOrderDetail):
        # 采购退货
        return 0

    @staticmethod
    def get_purchase_not_return_quantity(obj: InboundOrderDetail):
        # 采购未退货
        return 0


class SupplierInboundOrderCreateSerializer(CustomizeSerializer):
    """创建入库单序列化器"""

    supplier_id = serializers.IntegerField(required=True)
    purchase_type = serializers.IntegerField(required=True)
    warehouse_id = serializers.IntegerField(required=True, allow_null=False)
    warehouse_position_id = serializers.IntegerField(required=False, allow_null=True)
    product_info = serializers.ListField(child=serializers.DictField(), required=True, allow_empty=False)
    total_cost_price = serializers.DecimalField(required=False, allow_null=True, max_digits=20, decimal_places=2)

    class Meta:
        model = InboundOrder
        fields = (
            "supplier_id",
            "purchase_type",
            "warehouse_id",
            "warehouse_position_id",
            "product_info",
            "remark",
            "remark_images",
            "total_cost_price",
        )

    def validate(self, attrs):
        _request = self.context["request"]
        warehouse_id = attrs.get("warehouse_id", None)
        warehouse_position_id = attrs.get("warehouse_position_id", None)
        purchase_type = attrs.get("purchase_type", None)
        if purchase_type not in [1, 2]:
            raise APIViewException(err_message="invalid purchase type")

        # 校验供应商
        if not ERPCompanySupplierInfo.objects.filter(
            supplier_id=attrs.get("supplier_id"),
            company=_request.user.company,
            is_deleted=False,
        ).exists():
            raise APIViewException(err_message="invalid supplier id")

        # 校验仓库
        try:
            warehouse = ERPSupplierWarehouse.objects.get(
                id=warehouse_id,
                company=_request.user.company,
                is_deleted=False,
            )
        except ERPSupplierWarehouse.DoesNotExist:
            raise APIViewException(err_message="invalid warehouse id")

        if warehouse.allow_in == 2:
            raise APIViewException(err_message=f"{warehouse.name}禁止入仓")

        # 校验仓位
        if warehouse_position_id:
            if not ERPSupplierWarehousePosition.objects.filter(
                id=warehouse_position_id,
                company=_request.user.company,
                is_deleted=False,
                warehouse_id=warehouse_id,
            ).exists():
                raise APIViewException(err_message="invalid warehouse position id")

        return attrs

    def create(self, validated_data):
        _request_user = self.context["request"].user
        supplier_id = validated_data.get("supplier_id")
        purchase_type = validated_data.get("purchase_type")
        warehouse_id = validated_data.get("warehouse_id")
        warehouse_position_id = validated_data.get("warehouse_position_id")
        product_info_list = validated_data.get("product_info", [])
        total_cost_price = validated_data.get("total_cost_price")

        with transaction.atomic():
            # 创建入库单
            try:
                default_create_data = {
                    "create_user": _request_user.user_id,
                    "remark": validated_data.get("remark", ""),
                    "remark_images": validated_data.get("remark_images", ""),
                    "supplier_id": supplier_id,
                }
                #
                if total_cost_price is not None:
                    default_create_data["total_cost_price"] = total_cost_price

                inbound_order, inbound_order_created = InboundOrder.objects.get_or_create(
                    inbound_code=InboundOrder.get_pre_inbound_code(_request_user.company.company_id),
                    company=_request_user.company,
                    defaults=default_create_data,
                )
                if not inbound_order_created and inbound_order.approve_status == 2:
                    raise APIViewException(err_message="inbound order already approved, cannot be changed")
                setattr(inbound_order, "__created__", inbound_order_created)
            except IntegrityError:
                # 捕捉唯一约束异常，重新生成
                raise APIViewException(err_message="inbound code already exists, pls regenerate")

            # 创建入库明细
            detail_data_bulk_objs = []
            supplier_sku_bulk_objs = []

            for product_item in product_info_list:
                sku_id = product_item.get("sku_id")
                quantity = product_item.get("quantity")
                cost_price = product_item.get("cost_price") or decimal.Decimal("0.00")
                #
                tag_type = product_item.get("tag_type")
                if tag_type not in [1, 2]:
                    raise APIViewException(err_message="invalid tag type")

                if str(cost_price).isalpha():
                    raise APIViewException(err_message="invalid cost_price")

                # 转换成decimal, 防止传字符串类型，下面代码cost_price*quantity报错
                cost_price = decimal.Decimal(cost_price)

                # 检查SKU是否存在
                try:
                    sku = ERPSupplierProductSKU.objects.get(sku_id=sku_id, is_deleted=False)
                except ERPSupplierProductSKU.DoesNotExist:
                    raise APIViewException(err_message="sku does not exist")

                if sku.tag_type and sku.tag_type != tag_type:
                    raise APIViewException(err_message=f"{sku.name}的标签类型与之前不一致")

                if not sku.tag_type:
                    sku.tag_type = tag_type
                    sku.save()

                label_price = sku.label_price or decimal.Decimal("0.00")
                market_price = sku.market_price or decimal.Decimal("0.00")
                purchase_price = sku.purchase_price or decimal.Decimal("0.00")

                # RFID不生成tid
                if tag_type == 1:
                    tid_dict = {}
                    print_count = 0
                    tid_info = {}
                else:
                    # 普通标签默认是已打印的标签
                    tid_dict = ERPProductSKUTID.batch_generate_tid_info(quantity, inbound_order.inbound_code)
                    tid_info = InboundOrderDetail.batch_generate_tid_info(
                        list(tid_dict.keys()),
                        create_user=_request_user.user_id,
                        print_status="printed",
                    )
                    print_count = quantity

                # 创建入库明细
                detail_data = {
                    "inbound_order": inbound_order,
                    "purchase_type": purchase_type,
                    "warehouse_id": warehouse_id,
                    "warehouse_position_id": warehouse_position_id,
                    "sku": sku,
                    "quantity": quantity,
                    "in_warehouse_quantity": quantity,  # 初始化为入库数量
                    "create_user": _request_user.user_id,
                    "tag_type": tag_type,  # 默认为RFID标签
                    "tid_info": tid_info,  # 初始化空的tid_info
                    "print_count": print_count,  # 默认打印数量等于入库数量
                    "cost_price": cost_price,
                    "label_price": label_price,
                    "market_price": market_price,
                    "purchase_price": purchase_price,
                    "total_cost_price": cost_price * quantity,
                    "total_label_price": label_price * quantity,
                    "total_market_price": market_price * quantity,
                    "total_purchase_price": purchase_price * quantity,
                    "supplier_id": supplier_id,
                }

                detail_data_bulk_objs.append(InboundOrderDetail(**detail_data))

                # supplier、sku关联记录
                supplier_sku_bulk_objs.append(
                    {
                        "product_id": sku.product_id,
                        "supplier_id": supplier_id,
                        "company_id": sku.company_id,
                        "name": sku.name,
                        "sku_id": sku.sku_id,
                        "cost_price": cost_price,
                        "label_price": label_price,
                        "market_price": market_price,
                        "purchase_price": purchase_price,
                        "create_user": _request_user.user_id,
                    }
                )

            # 批量创建入库明细
            InboundOrderDetail.objects.bulk_create(detail_data_bulk_objs)

            # 统计数据
            # 默认不用计算total_cost_price, 判断None,有可能输入的是0
            exclude_update_fields = ["total_cost_price"]
            if total_cost_price is None:
                exclude_update_fields = None

            inbound_order.calculate_total_data(exclude_update_fields)

            # 创建关联sku+supplier记录
            ERPSupplierSKU.bulk_create_or_update(supplier_sku_bulk_objs)

            return inbound_order


class InboundOrderDetailUpdateSerializer(serializers.Serializer):
    """入库明细更新序列化器"""

    inbound_detail_code = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    sku_id = serializers.IntegerField(required=True)
    quantity = serializers.IntegerField(required=True, min_value=1)
    cost_price = serializers.DecimalField(max_digits=20, decimal_places=2, required=True, min_value=decimal.Decimal("0.00"))


class InboundOrderUpdateSerializer(CustomizeSerializer):
    """入库单更新序列化器"""

    product_info = serializers.ListField(child=InboundOrderDetailUpdateSerializer(), required=False, allow_empty=False)
    remark = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    remark_images = serializers.ListField(child=serializers.CharField(), required=False)
    status = serializers.ChoiceField(required=False, choices=((1, "正常"), (2, "取消")))
    total_cost_price = serializers.DecimalField(required=False, allow_null=True, max_digits=20, decimal_places=2)

    class Meta:
        model = InboundOrder
        fields = (
            "product_info",
            "remark",
            "remark_images",
            "status",
            "total_cost_price",
        )

    def validate(self, attrs):
        # 检查入库单是否已审核
        # 检查产品信息
        product_info = attrs.get("product_info", [])

        # 检查是否有重复的sku_id
        sku_ids = [item.get("sku_id") for item in product_info]
        if len(sku_ids) != len(set(sku_ids)):
            raise APIViewException(err_message="duplicate sku_id found in product_info")

        # 检查是否有重复的inbound_detail_code
        detail_codes = [item.get("inbound_detail_code") for item in product_info if item.get("inbound_detail_code")]
        if len(detail_codes) != len(set(detail_codes)):
            raise APIViewException(err_message="duplicate inbound_detail_code found in product_info")

        # 取消
        if attrs.get("status") and self.instance.approve_status != 1:
            raise APIViewException(err_message="已审核的入库单不能修改")

        return attrs

    def update(self, instance, validated_data):
        request = self.context.get("request")
        user = request.user

        product_info_list = validated_data.pop("product_info", [])
        remark = validated_data.get("remark", "")
        remark_images = validated_data.get("remark_images", [])
        total_cost_price = validated_data.get("total_cost_price")
        status = validated_data.get("status")
        if not product_info_list and not remark and not remark_images and not status:
            raise APIViewException()

        detail_change_content_list = []

        with transaction.atomic():
            # 更新入库单备注信息

            for field_name, value in validated_data.items():
                # 已审核的话，不允许修改status和总金额
                if instance.approve_status == 2:
                    if field_name in ["status", "total_cost_price"]:
                        continue

                # 如果为null默认是从detail进行计算
                if field_name == "total_cost_price" and total_cost_price is None:
                    continue

                setattr(instance, field_name, value)
            instance.update_user = user.user_id
            instance.update_date = timezone.now()
            instance.save()

            # 待结算的时候才会编辑商品信息
            if instance.approve_status == 1:
                need_save_inbound_detail_pk_list = []
                supplier_sku_bulk_objs = []
                # 处理入库明细
                for product_item in product_info_list:
                    sku_id = product_item.get("sku_id")
                    quantity = product_item.get("quantity")
                    inbound_detail_code = product_item.get("inbound_detail_code")
                    cost_price = product_item.get("cost_price")

                    if str(cost_price).isalpha():
                        raise APIViewException(err_message="invalid cost_price")

                    # 检查SKU是否存在
                    try:
                        sku = ERPSupplierProductSKU.objects.get(sku_id=sku_id, is_deleted=False, company=user.company)
                    except ERPSupplierProductSKU.DoesNotExist:
                        raise APIViewException(err_message=f"{sku_id}不存在")

                    if inbound_detail_code:
                        # 编辑现有明细
                        try:
                            detail = InboundOrderDetail.objects.get(
                                inbound_detail_code=inbound_detail_code,
                                inbound_order=instance,
                                is_deleted=False,
                            )
                        except InboundOrderDetail.DoesNotExist:
                            raise APIViewException(err_message=f"inbound detail with code {inbound_detail_code} does not exist")

                        old_detail = copy.deepcopy(detail)
                        # 检查是否有关联的库存记录
                        if ERPSKUInventory.objects.filter(inbound_order_detail=detail, status=1).exists():
                            raise APIViewException(err_message="商品已入过库，无法修改")
                        need_save_inbound_detail_pk_list.append(detail.pk)

                        setattr(instance, "__tag_type__", detail.tag_type)
                        setattr(instance, "__purchase_type__", detail.purchase_type)
                        setattr(instance, "__warehouse_id__", detail.warehouse_id)
                        setattr(instance, "__warehouse_position_id__", detail.warehouse_position_id)

                        # 如果数量相同，不更新
                        if detail.quantity != quantity:
                            # RFID不生成tid
                            if detail.tag_type == 1:
                                tid_dict = {}
                                print_count = 0
                                tid_info = {}
                            else:
                                # 普通标签默认是已打印的标签
                                tid_dict = ERPProductSKUTID.batch_generate_tid_info(quantity, instance.inbound_code)
                                tid_info = InboundOrderDetail.batch_generate_tid_info(
                                    list(tid_dict.keys()),
                                    create_user=user.user_id,
                                    print_status="printed",
                                )
                                print_count = quantity

                            # 更新数量并清空tid_info
                            detail.quantity = quantity
                            detail.print_count = print_count
                            detail.in_warehouse_quantity = quantity
                            detail.tid_info = tid_info

                        detail.cost_price = cost_price
                        detail.update_user = user.user_id
                        detail.update_date = timezone.now()
                        detail.save()

                        # 操作日志
                        detail_diff_content = diff_models(old_detail, detail)
                        if detail_diff_content:
                            detail_change_content_list.append(f"修改商品【{sku.name}】：{detail_diff_content}")
                    else:
                        tag_type = getattr(instance, "__tag_type__", None)
                        purchase_type = getattr(instance, "__purchase_type__", None)
                        warehouse_id = getattr(instance, "__warehouse_id__", None)
                        warehouse_position_id = getattr(instance, "__warehouse_position_id__", None)
                        if not tag_type:
                            first_detail = InboundOrderDetail.objects.filter(inbound_order=instance).first()
                            if not first_detail:
                                raise APIViewException(err_message="Missing rfid tag info")
                            tag_type = first_detail.tag_type
                            purchase_type = first_detail.purchase_type
                            warehouse_id = first_detail.warehouse_id
                            warehouse_position_id = first_detail.warehouse_position_id

                            setattr(instance, "__tag_type__", first_detail.tag_type)
                            setattr(instance, "__purchase_type__", first_detail.purchase_type)
                            setattr(instance, "__warehouse_id__", first_detail.warehouse_id)
                            setattr(instance, "__warehouse_position_id__", first_detail.warehouse_position_id)

                        if sku.tag_type and sku.tag_type != tag_type:
                            tag_type_display = "RFID标签" if tag_type == 1 else "普通标签"
                            sku_tag_type_display = "RFID标签" if sku.tag_type == 1 else "普通标签"
                            raise APIViewException(err_message=f"入库订单标签类型是{tag_type_display}, 商品: {sku.name}标签类型是{sku_tag_type_display}")

                        # RFID不生成tid
                        if tag_type == 1:
                            tid_dict = {}
                            print_count = 0
                            tid_info = {}
                        else:
                            # 普通标签默认是已打印的标签
                            tid_dict = ERPProductSKUTID.batch_generate_tid_info(quantity, instance.inbound_code)
                            tid_info = InboundOrderDetail.batch_generate_tid_info(
                                list(tid_dict.keys()),
                                create_user=user.user_id,
                                print_status="printed",
                            )
                            print_count = quantity

                        # 创建新明细
                        new_detail = InboundOrderDetail.objects.create(
                            inbound_order=instance,
                            sku=sku,
                            purchase_type=purchase_type,
                            warehouse_id=warehouse_id,
                            warehouse_position_id=warehouse_position_id,
                            quantity=quantity,
                            print_count=print_count,
                            in_warehouse_quantity=quantity,  # 可以初始化，在save的时候 rfid会自动校验
                            cost_price=cost_price,
                            label_price=sku.label_price or 0,
                            market_price=sku.market_price or 0,
                            purchase_price=sku.purchase_price or 0,
                            total_cost_price=(cost_price or 0) * quantity,
                            total_label_price=(sku.label_price or 0) * quantity,
                            total_market_price=(sku.market_price or 0) * quantity,
                            total_purchase_price=(sku.purchase_price or 0) * quantity,
                            tag_type=tag_type,
                            tid_info=tid_info,
                            create_user=user.user_id,
                        )

                        detail_change_content_list.append(f"新增商品【{sku.name}】：数量{quantity}, 成本价{cost_price}")
                        need_save_inbound_detail_pk_list.append(new_detail.pk)

                        # supplier、sku关联记录
                        supplier_sku_bulk_objs.append(
                            {
                                "product_id": sku.product_id,
                                "supplier_id": instance.supplier_id,
                                "company_id": instance.company_id,
                                "name": sku.name,
                                "sku_id": sku.sku_id,
                                "cost_price": sku.cost_price,
                                "label_price": sku.label_price,
                                "market_price": sku.market_price,
                                "purchase_price": sku.purchase_price,
                                "create_user": user.user_id,
                            }
                        )

                # 如果有传递数据，删除旧的信息
                if product_info_list and need_save_inbound_detail_pk_list:
                    need_remove_details = (
                        InboundOrderDetail.objects.prefetch_related("sku")
                        .filter(
                            inbound_order=instance,
                            is_deleted=False,
                        )
                        .exclude(pk__in=need_save_inbound_detail_pk_list)
                    )
                    for need_remove_detail in need_remove_details:
                        sku = need_remove_detail.sku
                        detail_change_content_list.append(f"删除商品【{sku.name}】")

                    need_remove_details.update(
                        is_deleted=True,
                        update_user=user.user_id,
                        update_date=timezone.now(),
                    )

                # 更新入库单总数据
                exclude_save_fields = ["total_cost_price"]
                if total_cost_price is None:
                    exclude_save_fields = None

                instance.calculate_total_data(exclude_save_fields)

                # 创建关联sku+supplier记录
                ERPSupplierSKU.bulk_create_or_update(supplier_sku_bulk_objs)

            instance.__detail_update_content__ = detail_change_content_list

            return instance


class InboundOrderProductInfoSerializer(CustomizeSerializer):
    """入库单商品信息序列化器"""

    name = serializers.CharField(required=True)
    spec_code = serializers.CharField(required=True)
    code = serializers.CharField(required=True)
    spec_value = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    cost_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=True)
    label_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=True)
    market_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=True)
    purchase_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=True)
    quantity = serializers.IntegerField(required=True, min_value=1)
    size = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    unit_name = serializers.CharField(required=True)

    class Meta:
        fields = (
            "name",
            "spec_code",
            "code",
            "spec_value",
            "cost_price",
            "label_price",
            "market_price",
            "purchase_price",
            "quantity",
            "size",
            "unit_name",
        )


class BatchInboundOrderProductInfoSerializer(serializers.Serializer):
    """批量入库商品信息序列化器"""

    name = serializers.CharField(required=True, max_length=100, help_text="商品名称")
    spec_code = serializers.CharField(required=True, max_length=100, help_text="商品规格编码")
    code = serializers.CharField(required=False, max_length=100, help_text="货号", allow_blank=True, allow_null=True)
    spec_value = serializers.CharField(required=False, max_length=100, help_text="商品规格值", allow_blank=True, allow_null=True)
    cost_price = serializers.DecimalField(required=True, max_digits=10, decimal_places=2, help_text="成本价")
    label_price = serializers.DecimalField(required=True, max_digits=10, decimal_places=2, help_text="标签价")
    market_price = serializers.DecimalField(required=False, max_digits=10, decimal_places=2, help_text="市场价", allow_null=True)
    purchase_price = serializers.DecimalField(required=False, max_digits=10, decimal_places=2, help_text="采购价", allow_null=True)
    quantity = serializers.IntegerField(required=True, min_value=1, help_text="数量")
    size = serializers.CharField(required=False, max_length=50, help_text="尺寸", allow_blank=True, allow_null=True)
    unit_name = serializers.CharField(required=False, max_length=50, help_text="单位名称", allow_blank=True, allow_null=True)
    main_images = serializers.ListField(child=serializers.CharField(), required=False, help_text="商品主图列表", allow_empty=True, allow_null=True)


class BatchInboundOrderCreateSerializer(serializers.Serializer):
    """批量创建入库单序列化器"""

    supplier_id = serializers.CharField(required=True, max_length=50, help_text="供应商ID")
    purchase_type = serializers.IntegerField(required=True, help_text="采购类型")
    warehouse_id = serializers.CharField(required=True, max_length=50, help_text="仓库ID")
    warehouse_position_id = serializers.CharField(required=False, max_length=50, help_text="仓位ID", allow_null=True)
    product_infos = BatchInboundOrderProductInfoSerializer(many=True, required=True, help_text="商品信息列表")
    tag_type = serializers.IntegerField(required=True, help_text="标签类型")
    print_barcode = serializers.BooleanField(required=False, default=False, help_text="是否打印条码")

    def validate(self, attrs):
        """验证数据"""

        # 验证采购类型
        if attrs["purchase_type"] not in [1, 2]:
            raise APIViewException(err_message="invalid purchase type")

        # 验证标签类型
        tag_type = attrs["tag_type"]
        if tag_type not in [1, 2]:
            raise APIViewException(err_message="invalid tag type")

        if not attrs["product_infos"]:
            raise APIViewException(err_message="please select products first")

        company = self.context["request"].user.company

        # 验证供应商
        try:
            supplier = ERPCompanySupplierInfo.objects.get(supplier_id=attrs["supplier_id"], is_deleted=False, company=company)
        except ERPCompanySupplierInfo.DoesNotExist:
            raise APIViewException(err_message="invalid supplier id")

        # 验证仓库
        try:
            warehouse = ERPSupplierWarehouse.objects.get(
                id=attrs["warehouse_id"],
                is_deleted=False,
                company=company,
            )
            if warehouse.allow_in == 2:
                raise APIViewException(err_message=f"{warehouse.name}禁止入仓")
        except ERPSupplierWarehouse.DoesNotExist:
            raise APIViewException(err_message="invalid warehouse id")

        # 验证仓位
        if attrs.get("warehouse_position_id"):
            try:
                warehouse_position = ERPSupplierWarehousePosition.objects.get(
                    id=attrs["warehouse_position_id"],
                    warehouse=warehouse,
                    is_deleted=False,
                )
            except ERPSupplierWarehousePosition.DoesNotExist:
                raise APIViewException(err_message="invalid warehouse position id")

        # 验证商品信息
        for product_info in attrs["product_infos"]:
            # 验证规格编码是否与供应商匹配
            try:
                sku = ERPSupplierProductSKU.objects.get(
                    spec_code=product_info["spec_code"],
                    is_deleted=False,
                    company=company,
                )

                # if sku.supplier != supplier:
                #     raise APIViewException(err_message=f"商品规格编码 {product_info['spec_code']} 与供应商不匹配")

                if sku.tag_type and sku.tag_type != tag_type:
                    raise APIViewException(err_message=f"{sku.name}的标签类型与之前不一致")

            except ERPSupplierProductSKU.DoesNotExist:
                pass

        return attrs

    def create(self, validated_data):
        """创建入库单"""

        need_upload_oss_skus = []
        tag_type = validated_data["tag_type"]
        with transaction.atomic():
            # 创建入库单
            try:
                inbound_order = InboundOrder.objects.create(
                    inbound_code=InboundOrder.get_pre_inbound_code(self.context["request"].user.company.company_id),
                    supplier_id=validated_data["supplier_id"],
                    company=self.context["request"].user.company,
                    create_user=str(self.context["request"].user.user_id),
                )
            except IntegrityError:
                # 捕捉唯一约束异常，重新生成
                raise APIViewException(err_message="inbound code already exists, pls regenerate")

            # 创建入库单明细
            inbound_order_details = []
            supplier_sku_bulk_objs = []
            for product_info in validated_data["product_infos"]:
                # 获取或创建单位
                unit, _ = ERPProductUnit.objects.get_or_create(
                    name=product_info["unit_name"],
                    company=self.context["request"].user.company,
                    defaults={
                        "create_user": str(
                            self.context["request"].user.user_id,
                        )
                    },
                )

                # 获取SKU，如果不存在则创建
                try:
                    sku = ERPSupplierProductSKU.objects.get(
                        spec_code=product_info["spec_code"],
                        is_deleted=False,
                    )

                    if sku.tag_type:
                        if sku.tag_type != tag_type:
                            raise APIViewException(err_message=f"{sku.name}的标签类型与之前不一致")

                    if not sku.tag_type:
                        sku.tag_type = tag_type
                        sku.save()

                except ERPSupplierProductSKU.DoesNotExist:
                    # 创建新的产品和SKU
                    company = self.context["request"].user.company
                    supplier_id = validated_data["supplier_id"]

                    # 检查是否有相同货号的产品
                    code = product_info.get("code", "")
                    existing_product = None
                    if code:
                        existing_product = ERPSupplierProduct.objects.filter(
                            code=code,
                            supplier_id=supplier_id,
                            company=company,
                            is_deleted=False,
                        ).first()

                    # 如果不存在相同货号的产品，创建新产品
                    if not existing_product:
                        product_data = {
                            "name": product_info.get("name", ""),
                            "code": code,
                            "product_type": "FN",  # 默认为成品
                            "remark": "",
                            "company": company,
                            "supplier_id": supplier_id,
                            "create_user": str(self.context["request"].user.user_id),
                        }

                        # 处理主图
                        main_images = product_info.get("main_images", [])
                        if main_images:
                            product_data["main_images"] = main_images

                        # 创建产品
                        product = ERPSupplierProduct.objects.create(**product_data)
                    else:
                        # 使用已存在的产品
                        product = existing_product

                    # 创建SKU
                    sku_data = {
                        "product": product,
                        "product_type": "FN",  # 默认为成品
                        "name": product_info.get("name", ""),
                        "spec_code": product_info["spec_code"],
                        "spec_value": product_info.get("spec_value", ""),
                        "cost_price": product_info["cost_price"],
                        "label_price": product_info["label_price"],
                        "market_price": product_info.get("market_price"),
                        "purchase_price": product_info.get("purchase_price"),
                        "size": product_info.get("size", ""),
                        "unit": unit,
                        "company": company,
                        "create_user": str(self.context["request"].user.user_id),
                        "tag_type": tag_type,
                    }

                    # 处理主图
                    if main_images:
                        sku_data["main_images"] = main_images

                    # 创建SKU
                    sku = ERPSupplierProductSKU.objects.create(**sku_data)

                    # 需要替换oss
                    if sku.main_images:
                        need_upload_oss_skus.append((sku.sku_id, sku.main_images[0]))

                # 生成TID信息（如果是RFID标签）

                # RFID不生成tid

                quantity = product_info["quantity"]

                print_count = 0
                tid_info = {}
                print_barcode = validated_data.get("print_barcode", False)

                if print_barcode:
                    # 普通标签默认是已打印的标签
                    tid_dict = ERPProductSKUTID.batch_generate_tid_info(quantity, inbound_order.inbound_code)
                    tid_info = InboundOrderDetail.batch_generate_tid_info(
                        list(tid_dict.keys()),
                        create_user=self.context["request"].user.user_id,
                        print_status="printed",
                    )
                    print_count = quantity

                # 添加明细
                cost_price = decimal.Decimal(product_info.get("cost_price") or 0).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                label_price = decimal.Decimal(product_info.get("label_price") or 0).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                market_price = decimal.Decimal(product_info.get("market_price") or 0).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
                purchase_price = decimal.Decimal(product_info.get("purchase_price") or 0).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)

                # 创建入库单明细
                inbound_order_detail = InboundOrderDetail(
                    inbound_order=inbound_order,
                    sku=sku,
                    warehouse_id=validated_data["warehouse_id"],
                    warehouse_position_id=validated_data.get("warehouse_position_id"),
                    quantity=product_info["quantity"],
                    cost_price=cost_price,
                    label_price=label_price,
                    market_price=market_price,
                    purchase_price=purchase_price,
                    # 统计的数据, 保留两位小数
                    total_cost_price=decimal.Decimal(cost_price * quantity).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP),
                    total_label_price=decimal.Decimal(label_price * quantity).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP),
                    total_market_price=decimal.Decimal(market_price * quantity).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP),
                    total_purchase_price=decimal.Decimal(purchase_price * quantity).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP),
                    tag_type=tag_type,
                    tid_info=tid_info,
                    create_user=str(self.context["request"].user.user_id),
                    supplier_id=validated_data["supplier_id"],
                    purchase_type=validated_data["purchase_type"],
                    print_barcode=validated_data.get("print_barcode", False),
                    print_count=print_count,
                )
                inbound_order_details.append(inbound_order_detail)

                # supplier、sku关联记录
                supplier_sku_bulk_objs.append(
                    {
                        "product_id": sku.product_id,
                        "supplier_id": validated_data["supplier_id"],
                        "company_id": inbound_order.company_id,
                        "name": sku.name,
                        "sku_id": sku.sku_id,
                        "cost_price": sku.cost_price,
                        "label_price": sku.label_price,
                        "market_price": sku.market_price,
                        "purchase_price": sku.purchase_price,
                        "create_user": str(self.context["request"].user.user_id),
                    }
                )

            # 批量创建入库单明细
            InboundOrderDetail.objects.bulk_create(inbound_order_details)

            # 更新入库单总数据
            inbound_order.calculate_total_data()

            # 创建关联sku+supplier记录
            ERPSupplierSKU.bulk_create_or_update(supplier_sku_bulk_objs)

        # 从临时文件夹的图片更新到其他文件夹
        if need_upload_oss_skus:
            for sku_id, image_url in need_upload_oss_skus:
                update_main_images_with_tmp_image_url.delay(sku_id, image_url)

        return inbound_order


class InboundOrderDetailDownloadSerializer(CustomizeSerializer):
    name = serializers.CharField(source="sku.name")
    spec_code = serializers.CharField(source="sku.spec_code")
    code = serializers.CharField(source="sku.product.code")
    main_image = serializers.SerializerMethodField()
    spec_value = serializers.CharField(source="sku.spec_value")
    size = serializers.CharField(source="sku.size")
    weight = serializers.CharField(source="sku.weight")
    unit_name = serializers.CharField(source="sku.unit.name", allow_null=True)
    remark = serializers.CharField(source="sku.remark", allow_null=True)
    product_type_display = serializers.CharField(source="sku.get_product_type_display")
    category = serializers.SerializerMethodField()
    other_info = serializers.SerializerMethodField()
    warehouse_name = serializers.CharField(source="warehouse.name", allow_null=True)
    warehouse_position_name = serializers.CharField(source="warehouse_position.name", allow_null=True)

    class Meta:
        model = InboundOrderDetail
        fields = (
            "name",
            "spec_code",
            "main_image",
            "code",
            "spec_value",
            "cost_price",
            "label_price",
            "market_price",
            "purchase_price",
            "category",
            "size",
            "weight",
            "unit_name",
            "remark",
            "product_type_display",
            "total_cost_price",
            "total_market_price",
            "total_label_price",
            "warehouse_name",
            "warehouse_position_name",
            "quantity",
            "purchase_return_quantity",
            "purchase_not_return_quantity",
            "other_info",
            "batch_no",
        )

    @staticmethod
    def get_main_image(obj):
        if not obj.sku.main_images:
            return ""
        return obj.sku.main_images[0]

    def get_category(self, obj: InboundOrderDetail):
        category_cache = self.context["category_cache"]

        if not obj.sku.category_id:
            return ""
        return "/".join([i["name"] or "" for i in category_cache.find_parents(obj.sku.category_id)])

    @staticmethod
    def get_other_info(obj: InboundOrderDetail):
        return {
            "other_price1": obj.sku.other_price1,
            "other_price2": obj.sku.other_price2,
            "other_price3": obj.sku.other_price3,
            "other_price4": obj.sku.other_price4,
            "other_price5": obj.sku.other_price5,
            "other_price6": obj.sku.other_price6,
            "other_attributes": obj.sku.other_attributes,
            "other_attributes2": obj.sku.other_attributes2,
            "other_attributes3": obj.sku.other_attributes3,
            "other_attributes4": obj.sku.other_attributes4,
            "other_attributes5": obj.sku.other_attributes5,
        }


class InboundOrderDownloadSerializer(CustomizeSerializer):
    supplier_info = serializers.SerializerMethodField()
    create_date = serializers.DateTimeField(format=DATETIME_FORMAT)
    approve_date = serializers.DateTimeField(format=DATETIME_FORMAT, allow_null=True)
    approve_user = serializers.SerializerMethodField()
    approve_status_display = serializers.CharField(source="get_approve_status_display")
    remark_images = serializers.SerializerMethodField()
    details = serializers.SerializerMethodField()

    class Meta:
        model = InboundOrder
        fields = (
            "supplier_info",
            "inbound_code",
            "create_user",
            "create_date",
            "approve_user",
            "approve_date",
            "total_quantity",
            "total_cost_price",
            "total_label_price",
            "approve_status_display",
            "remark",
            "remark_images",
            "details",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        skus = []
        for obj in self.instance:
            inbound_details = obj.inboundorderdetail_set.all()
            skus.extend([detail.sku for detail in inbound_details if detail.sku])

        self.category_cache = ERPSupplierProductSKU.get_batch_category_display_cache(skus)

    @staticmethod
    def get_supplier_info(obj):
        """获取供应商信息"""
        if not obj.supplier_id:
            return {}
        ser_data = ERPSupplierInfoDownloadSerializer(obj.supplier, many=False).data
        return ser_data

    @staticmethod
    def get_approve_user(obj):
        return get_user_real_name_by_user_id(obj.approve_user) if obj.approve_user else ""

    @staticmethod
    def get_remark_images(obj):
        """获取备注图片"""
        if not obj.remark_images:
            return ""
        return obj.remark_images[0]

    def get_details(self, obj):
        """获取入库单明细"""
        details = InboundOrderDetailDownloadSerializer(
            obj.inboundorderdetail_set.all(),
            many=True,
            context={"category_cache": self.category_cache},
        ).data
        return details

