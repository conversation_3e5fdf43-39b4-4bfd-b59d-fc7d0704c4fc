# -*- coding: utf-8 -*-
from django.db import transaction
from django.utils import timezone
from rest_framework import serializers

from common.basic import CustomizeSerializer
from common.basics.views import set_log_params
from common.basics.exceptions import APIViewException
from companies.models import Company
from erp_apps import logger
from erp_purchase.models import ERPReturnOrders, PurchaseWarehouse, PurchaseReturnLabel, ERPReturnOrderDetail
from erp_purchase.serializers.label_serializers import PurchaseReturnLabelListSer
from users.models import User
from utils.caches import get_user_real_name_by_user_id


class ERPReturnOrderListSer(CustomizeSerializer):
    labels = PurchaseReturnLabelListSer(many=True)
    warehouse_name = serializers.CharField(source="warehouse.name")
    storage_party_name = serializers.Char<PERSON>ield(source="storage_party.name")
    company_name = serializers.Char<PERSON>ield(source="company.name")
    create_user_id = serializers.Char<PERSON><PERSON>(source="create_user")
    create_user_name = serializers.SerializerMethodField()

    class Meta:
        model = ERPReturnOrders
        fields = (
            "code",
            "return_date",
            "order_status",
            "return_type",
            "financial_status",
            "product_quantity",
            "total_amount",
            "warehouse_id",
            "warehouse_name",
            "storage_party_id",
            "storage_party_name",
            "company_id",
            "company_name",
            "receiver",
            "create_user_id",
            "create_user_name",
            "remark",
            "labels",
            "create_date",
            "update_date",
        )

    @staticmethod
    def get_create_user_name(obj):
        return get_user_real_name_by_user_id(obj.create_user)


class ERPReturnOrderCreateSer(CustomizeSerializer):
    storage_party_id = serializers.IntegerField(required=False)
    warehouse_id = serializers.IntegerField(required=False)
    company_ids = serializers.ListField()

    class Meta:
        model = ERPReturnOrders
        fields = (
            "storage_party_id",
            "warehouse_id",
            "company_ids",
        )

    @staticmethod
    def validate_company_ids(value):
        if not value:
            raise APIViewException(err_message="missing company_ids")

        if Company.objects.filter(company_id__in=value, is_deleted=False).count() != len(value):
            raise APIViewException(err_message="invalid company_ids")

        return value

    def create(self, validated_data):
        request = self.context["request"]
        create_user_id = request.user.user_id

        company_id_list = validated_data.pop("company_ids")
        storage_party_id = validated_data.get("storage_party_id")
        warehouse_id = validated_data.get("warehouse_id")

        default_warehouse = PurchaseWarehouse.get_default_warehouse()
        if not storage_party_id:
            storage_party_id = default_warehouse.pk

        if not warehouse_id:
            warehouse_id = default_warehouse.pk

        users = User.objects.filter(
            company_id__in=company_id_list,
            is_supplier_superuser=True,
        ).values("real_name", "company__company_id")

        users_map = {str(user["company__company_id"]): user["real_name"] for user in users}

        now_time = timezone.now()
        need_create_objs = [
            ERPReturnOrders(
                return_date=now_time,
                storage_party_id=storage_party_id,
                warehouse_id=warehouse_id,
                company_id=company_id,
                receiver=users_map.get(str(company_id)) or "",
                create_date=now_time,
                create_user=create_user_id,
            )
            for company_id in company_id_list
        ]

        created_objs = None
        if need_create_objs:
            created_objs = ERPReturnOrders.objects.bulk_create(need_create_objs)

            # 日志记录
            log_codes = [i.code for i in created_objs]
            for log_code in log_codes:
                try:
                    set_log_params(
                        request,
                        resource_id=log_code,
                        model=ERPReturnOrders,
                        describe="新增退货单",
                        is_success_input=True,
                    )
                except Exception as e:
                    logger.error(f"set log params failed, {e}")
                    pass

        return created_objs


class ERPReturnOrderUpdateSer(CustomizeSerializer):
    labels = serializers.ListField(child=serializers.IntegerField(), allow_null=True, allow_empty=True, required=False)

    class Meta:
        model = ERPReturnOrders
        fields = (
            "warehouse_id",
            "remark",
            "receiver",
            "labels",
            "update_user",
        )

    def validate_labels(self, value):
        if not value:
            return value

        if len(value) != PurchaseReturnLabel.objects.filter(id__in=value).count():
            raise APIViewException(err_message="invalid labels, pls refresh the page and try again")

        return value

    def update(self, instance: ERPReturnOrders, validated_data):
        has_labels_params = "labels" in validated_data
        labels = validated_data.pop("labels", [])

        if instance.order_status == 1:
            warehouse_id = validated_data.get("warehouse_id", None)
            if warehouse_id and not PurchaseWarehouse.objects.filter(id=warehouse_id).exists():
                raise APIViewException(err_message="invalid warehouse_id")
        elif instance.order_status == 2:
            # 生效了只能改备注
            validated_data = {
                "remark": validated_data.get("remark", ""),
                "update_user": validated_data.get("update_user", None),
            }

        with transaction.atomic():
            updated_instance = super().update(instance, validated_data)

            # 如果前端没传参，默认不处理
            if has_labels_params:
                updated_instance.labels.clear()
                if labels:
                    for label_id in labels:
                        # 保留顺序
                        updated_instance.labels.add(label_id)

        return updated_instance


class ERPReturnOrderProductDetailListSer(CustomizeSerializer):
    product_image = serializers.SerializerMethodField()
    product_name = serializers.CharField(source="erp_sku.product.name")
    product_code = serializers.CharField(source="erp_sku.product.code")
    spec_code = serializers.CharField(source="erp_sku.spec_code")
    specs = serializers.ListField(source="erp_sku.specs")
    detail_code = serializers.CharField(source="id")

    class Meta:
        model = ERPReturnOrderDetail
        fields = (
            "detail_code",
            "erp_sku_id",
            "product_image",
            "product_name",
            "product_code",
            "spec_code",
            "specs",
            "quantity",
            "cost_price",
            "amount",
            "retail_price",
            "retail_amount",
        )

    @staticmethod
    def get_product_image(obj: ERPReturnOrderDetail):
        if obj.erp_sku.image:
            return obj.erp_sku.image
        return obj.erp_sku.product.main_images[0]


class ERPReturnOrderDetailUpdateSer(CustomizeSerializer):
    cost_price = serializers.DecimalField(required=True, max_digits=10, decimal_places=2)
    quantity = serializers.IntegerField(required=True, min_value=1)
    retail_price = serializers.DecimalField(required=True, max_digits=10, decimal_places=2)

    class Meta:
        model = ERPReturnOrderDetail
        fields = (
            "cost_price",
            "quantity",
            "retail_price",
            "update_user",
        )
