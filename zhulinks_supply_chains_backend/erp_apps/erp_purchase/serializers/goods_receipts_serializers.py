# -*- coding: utf-8 -*-
import decimal

from django.db import transaction
from rest_framework import serializers

from common.basic import CustomizeSerializer
from erp_apps.erp_purchase.models import ERPGoodsReceiptsOrder, ERPGoodsReceiptOrderDetail
from utils.caches import get_user_real_name_by_user_id


class ERPGoodsReceiptOrderListSer(CustomizeSerializer):
    company_name = serializers.CharField(source="company.name")
    purchase_order_code = serializers.CharField(source="purchase_order_id", allow_null=True)
    storage_party_name = serializers.CharField(source="storage_party.name")
    warehouse_name = serializers.CharField(source="warehouse.name")
    labels = serializers.ListField(source="get_labels_display")
    create_user_id = serializers.CharField(source="create_user")
    create_user_name = serializers.SerializerMethodField()

    class Meta:
        model = ERPGoodsReceiptsOrder
        fields = (
            "code",
            "company_id",
            "company_name",
            "purchase_order_code",
            "create_date",
            "receipt_date",
            "receipt_status",
            "order_type",
            "financial_status",
            "storage_party_id",
            "storage_party_name",
            "warehouse_id",
            "warehouse_name",
            "labels",
            "remark",
            "actual_quantity",
            "actual_amount",
            "create_user_id",
            "create_user_name",
            "update_date",
            "product_type",
            "entry_type",
        )

    @staticmethod
    def get_create_user_name(obj: ERPGoodsReceiptsOrder):
        return get_user_real_name_by_user_id(obj.create_user)


class ERPGoodsReceiptOrderUpdateSer(CustomizeSerializer):
    labels = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_null=True,
        allow_empty=True,
    )

    class Meta:
        model = ERPGoodsReceiptsOrder
        fields = (
            "remark",
            "labels",
            "update_user",
        )

    def update(self, instance, validated_data):
        from_fronted = "labels" in validated_data

        label_id_list = validated_data.pop("labels", [])

        with transaction.atomic():
            if from_fronted:
                # 只有待入库才进行标签操作
                if instance.receipt_status == 1:
                    instance.labels.clear()
                    for label_id in label_id_list:
                        instance.labels.add(label_id)

            new_instance = super().update(instance, validated_data)

        return new_instance


class ERPReceiptOrderProductDetailListSer(CustomizeSerializer):
    product_image = serializers.SerializerMethodField()
    product_name = serializers.CharField(source="erp_sku.product.name")
    product_code = serializers.CharField(source="erp_sku.product.code")
    spec_code = serializers.CharField(source="erp_sku.spec_code")
    specs = serializers.ListField(source="erp_sku.specs")

    class Meta:
        model = ERPGoodsReceiptOrderDetail
        fields = (
            "detail_code",
            "erp_sku_id",
            "product_image",
            "product_name",
            "product_code",
            "spec_code",
            "specs",
            "quantity",
            "cost_price",
            "amount",
        )

    @staticmethod
    def get_product_image(obj: ERPGoodsReceiptOrderDetail):
        if obj.erp_sku.image:
            return obj.erp_sku.image
        return obj.erp_sku.product.main_images[0]


class ERPGoodsReceiptOrderDetailUpdateSer(CustomizeSerializer):
    cost_price = serializers.DecimalField(required=True, max_digits=10, decimal_places=2)
    quantity = serializers.IntegerField(required=True, min_value=1)

    class Meta:
        model = ERPGoodsReceiptOrderDetail
        fields = (
            "cost_price",
            "quantity",
            "update_user",
        )

    def update(self, instance, validated_data):
        validated_data["amount"] = decimal.Decimal(validated_data["cost_price"]) * validated_data["quantity"]
        return super().update(instance, validated_data)
