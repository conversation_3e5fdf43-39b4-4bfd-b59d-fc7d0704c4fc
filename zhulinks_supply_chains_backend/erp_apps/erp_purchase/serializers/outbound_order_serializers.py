# -*- coding: utf-8 -*-
import decimal

from common.basic import CustomizeSerializer
from common.basics.exceptions import APIViewException
from common.formats import DATETIME_FORMAT
from common.utils import CategoryFindAncestorsCache
from django.core.paginator import Page
from django.db import transaction
from django.db.models import Count, F, Sum
from erp_client.models import ERPCompanySupplierInfo
from erp_client.serializers import ERPSupplierInfoDownloadSerializer
from erp_products.models import ERPProductCategory, ERPSupplierProductSKU
from erp_products.utils import decimal_to_base62
from erp_purchase.models import ERPOutboundOrder, ERPOutboundOrderDetail, ERPSKUInventory, InventoryAdjustmentOrderDetail
from rest_framework import serializers
from utils.caches import get_user_real_name_by_user_id


class ItemsDetailSerializer(CustomizeSerializer):
    """出库订单明细序列化器"""

    sku_id = serializers.IntegerField(required=True)
    cost_price = serializers.DecimalField(max_digits=20, decimal_places=2, required=True)
    quantity = serializers.IntegerField(min_value=1, required=True)

    class Meta:
        model = ERPOutboundOrderDetail
        fields = (
            "sku_id",
            "cost_price",
            "quantity",
        )

    def validate_sku_id(self, value):
        """
        验证商品sku是否存在
        :param value:
        :return:
        """
        request = self.context.get("request")

        if not ERPSupplierProductSKU.objects.filter(
            sku_id=value,
            is_deleted=False,
            company=request.user.company,
        ).exists():
            raise APIViewException(err_message="sku does not exist")

        return value


class ERPOutboundOrderCreateSerializer(CustomizeSerializer):
    supplier_id = serializers.IntegerField(required=False, allow_null=True)
    remark = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    inbound_order_id = serializers.IntegerField(required=False, allow_null=True)
    warehouse_id = serializers.IntegerField(required=False, allow_null=True)
    warehouse_position_id = serializers.IntegerField(required=False, allow_null=True)
    total_cost_price = serializers.DecimalField(max_digits=20, decimal_places=2, required=False)
    remark_images = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        default=list,
    )
    outbound_type = serializers.ChoiceField(
        choices=[
            (4, "采购退出库"),
            (5, "其他出库"),
        ],
        default=5,
    )
    details = ItemsDetailSerializer(many=True, required=True)

    work_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = ERPOutboundOrder
        fields = (
            "outbound_type",
            "supplier_id",
            "remark",
            "remark_images",
            "inbound_order_id",
            "warehouse_id",
            "warehouse_position_id",
            "total_cost_price",
            "details",
            "work_id",
        )

    def validate_supplier_id(self, value):
        """验证供应商是否存在"""
        request = self.context.get("request")
        # 领料单不需要供应商
        if (
            self.initial_data["outbound_type"] != 6
            and not ERPCompanySupplierInfo.objects.filter(
                supplier_id=value,
                is_deleted=False,
                company=request.user.company,
            ).exists()
        ):
            raise APIViewException(err_message="供应商不存在")

        return value

    def create(self, validated_data):
        request = self.context.get("request")
        current_user = request.user
        warehouse_id = validated_data.get("warehouse_id")
        outbound_type = validated_data.get("outbound_type")
        total_cost_price = validated_data.get("total_cost_price", None)
        from_order_id = validated_data.get("work_id") if outbound_type == 6 else ""

        # 计算订单总金额
        total_amount = decimal.Decimal("0.00")
        total_quantity = total_label_price = total_market_price = total_purchase_price = 0

        details_data = validated_data.get("details", [])

        # 批量获取sku信息
        sku_id_list = [detail.get("sku_id") for detail in details_data]
        skus = ERPSupplierProductSKU.objects.filter(
            sku_id__in=sku_id_list,
            is_deleted=False,
            company=request.user.company,
        )
        skus_map = {str(sku.sku_id): sku for sku in skus}

        # 判断商品是否在调整
        sku_pk_list = [sku.id for sku in skus]
        InventoryAdjustmentOrderDetail.judge_sku_is_adjusting(sku_pk_list)

        # 批量获取库存信息
        sku_inventorys = ERPSupplierProductSKU.get_batch_sku_total_in_sku_warehouse_quantity(skus, warehouse_id)
        if not sku_inventorys:
            raise APIViewException(err_message="无库存信息")

        sku_warehouse_inventory_map = {}
        for item in sku_inventorys:
            sku_warehouse_inventory_map[f"{item['sku_pk']}:{item['warehouse_id']}"] = item["total_in_warehouse_quantity"]

        # 验证库存并计算总金额
        for detail in details_data:
            sku_id = detail.get("sku_id")
            quantity = detail.get("quantity")

            if not quantity:
                raise APIViewException(err_message="数量不能为0")

            # 获取库存信息
            if str(sku_id) not in skus_map:
                raise APIViewException(err_message=f"SKU {sku_id} 不存在")

            sku = skus_map[str(sku_id)]

            inventory_key = f"{sku.pk}:{warehouse_id}"

            in_warehouse_quantity = sku_warehouse_inventory_map.get(inventory_key) or 0

            # 如果是销售订单和借货订单，检查库存数量
            if in_warehouse_quantity < quantity:
                raise APIViewException(err_message=f"商品 {sku.name} 库存不足")

            # 精确到仓位库存
            if validated_data.get("warehouse_position_id"):
                if not ERPSKUInventory.objects.filter(
                    sku_id=sku.pk, warehouse_id=warehouse_id, warehouse_position_id=validated_data.get("warehouse_position_id"), in_warehouse_quantity__gte=quantity
                ):
                    raise APIViewException(err_message=f"商品 {sku.name} 库存不足")

            # 累加总数
            total_quantity += quantity
            total_amount += detail["cost_price"] * quantity
            # total_label_price += sku.label_price * quantity
            # total_market_price += sku.market_price * quantity
            # total_purchase_price += sku.purchase_price * quantity

        with transaction.atomic():
            # 创建主单
            outbound_order = ERPOutboundOrder.objects.create(
                company_id=current_user.company.company_id,
                supplier_id=validated_data.get("supplier_id"),
                outbound_type=outbound_type,
                remark=validated_data.get("remark"),
                remark_images=validated_data.get("remark_images"),
                inbound_order_id=validated_data.get("inbound_order_id"),
                warehouse_id=validated_data["warehouse_id"],
                warehouse_position_id=validated_data.get("warehouse_position_id"),
                quantity=total_quantity,
                total_cost_price=total_amount if total_cost_price is None else total_cost_price,
                # total_label_price=total_label_price,
                # total_market_price=total_market_price,
                # total_purchase_price=total_purchase_price,
                create_user=current_user.user_id,
                from_order_id=from_order_id,
            )

            # 创建明细
            for detail in details_data:
                sku_id = detail.get("sku_id")
                quantity = detail.get("quantity")
                cost_price = detail.get("cost_price")
                sku = skus_map[str(sku_id)]

                if not sku.tag_type:
                    raise APIViewException(err_message=f"商品{sku.name}未设置标签类型")

                # 创建订单明细
                outbound_detail = ERPOutboundOrderDetail(
                    sku=sku,
                    tag_type=sku.tag_type,
                    quantity=quantity,
                    cost_price=cost_price,
                    label_price=sku.label_price,
                    market_price=sku.market_price,
                    purchase_price=sku.purchase_price,
                    outbound_order=outbound_order,
                    create_user=current_user.user_id,
                    # 废弃，使用主单warehouse_id
                    # warehouse_id=validated_data["warehouse_id"],
                    # warehouse_position_id=validated_data.get("warehouse_position_id"),
                )
                outbound_detail.save()

            return outbound_order


class ERPOutboundOrderListSerializer(CustomizeSerializer):
    create_user = serializers.SerializerMethodField()
    outbound_type_display = serializers.CharField(source="get_outbound_type_display")
    # status_display = serializers.CharField(source="get_status_display")
    total_sku_count = serializers.SerializerMethodField()
    total_verified_quantity = serializers.SerializerMethodField()
    total_quantity_count = serializers.SerializerMethodField()
    total_cost_price = serializers.SerializerMethodField()
    total_rfid_count = serializers.SerializerMethodField()
    approve_user_name = serializers.SerializerMethodField()

    supplier_id = serializers.CharField(source="supplier.supplier_id", allow_null=True)
    supplier_name = serializers.CharField(source="supplier.name", allow_null=True)
    contact_user_name = serializers.CharField(source="supplier.contact_user_name", allow_null=True)
    contact_user_mobile = serializers.CharField(source="supplier.contact_user_mobile", allow_null=True)

    tag_type = serializers.SerializerMethodField()
    rfid_prefix = serializers.SerializerMethodField()

    warehouse_name = serializers.SerializerMethodField()
    warehouse_position_name = serializers.SerializerMethodField()

    class Meta:
        model = ERPOutboundOrder
        fields = (
            "code",
            "inbound_order",
            "outbound_type",
            "outbound_type_display",
            "approve_status",
            "approve_user",
            "approve_user_name",
            # "status_display",
            "total_sku_count",
            "total_verified_quantity",
            "total_quantity_count",
            "total_rfid_count",
            "total_cost_price",
            "awaiting_settle_amount",
            "remark",
            "remark_images",
            "create_user",
            "create_date",
            "approve_date",
            # "submit_date",
            # "finish_date",
            "supplier_id",
            "supplier_name",
            "contact_user_name",
            "contact_user_mobile",
            "tag_type",
            "rfid_prefix",
            "status",
            "settle_status",
            "warehouse_id",
            "warehouse_name",
            "warehouse_position_id",
            "warehouse_position_name",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        from django.db.models import QuerySet

        if isinstance(self.instance, Page):
            outbound_orders = self.instance.object_list
        elif isinstance(self.instance, QuerySet):
            outbound_orders = self.instance
        else:
            outbound_orders = [self.instance]

        details = ERPOutboundOrderDetail.objects.filter(
            outbound_order__in=outbound_orders,
        )
        rfid_count_mp = {}
        rfid_prefix_mp = {}
        self.tag_type_mp = {}
        for detail in details:
            rfid_count_mp.setdefault(detail.outbound_order_id, 0)
            rfid_count_mp[detail.outbound_order_id] += detail.quantity if detail.tag_type == 1 else 0
            rfid_prefix_mp.setdefault(detail.outbound_order_id, [])
            rfid_prefix_mp[detail.outbound_order_id].append(decimal_to_base62(detail.sku.sku_id))

            self.tag_type_mp.setdefault(detail.outbound_order_id, False)
            if detail.tag_type == 1:
                self.tag_type_mp[detail.outbound_order_id] = True

        agg_rets = details.values("outbound_order_id").annotate(
            sku_count=Count("quantity"),
            total_quantity_count=Sum("quantity"),
            total_verified_count=Sum("verified_quantity"),
            total_cost_price=Sum(F("cost_price") * F("quantity")),
        )

        self.agg_rets_dict = {}
        for agg_ret in agg_rets:
            doc = {
                "sku_count": agg_ret["sku_count"],
                "total_quantity_count": agg_ret["total_quantity_count"],
                "total_verified_count": agg_ret["total_verified_count"],
                "total_cost_price": agg_ret["total_cost_price"],
                "total_rfid_count": rfid_count_mp[agg_ret["outbound_order_id"]],
                "rfid_prefix": rfid_prefix_mp[agg_ret["outbound_order_id"]],
            }

            self.agg_rets_dict[agg_ret["outbound_order_id"]] = doc

    def get_total_sku_count(self, obj: ERPOutboundOrder):
        return self.agg_rets_dict.get(obj.pk, {}).get("sku_count", 0) or 0

    def get_total_verified_quantity(self, obj: ERPOutboundOrder):
        return self.agg_rets_dict.get(obj.pk, {}).get("total_verified_count", 0) or 0

    def get_total_cost_price(self, obj: ERPOutboundOrder):
        return obj.total_cost_price  # self.agg_rets_dict.get(obj.pk, {}).get("total_cost_price", 0) or 0

    def get_approve_user_name(self, obj: ERPOutboundOrder):
        return get_user_real_name_by_user_id(obj.approve_user)

    def get_total_quantity_count(self, obj: ERPOutboundOrder):
        return self.agg_rets_dict.get(obj.pk, {}).get("total_quantity_count", 0) or 0

    def get_total_rfid_count(self, obj: ERPOutboundOrder):
        return self.agg_rets_dict.get(obj.pk, {}).get("total_rfid_count", 0) or 0

    def get_tag_type(self, obj: ERPOutboundOrder):
        return 1 if self.tag_type_mp.get(obj.pk) else 2

    def get_rfid_prefix(self, obj: ERPOutboundOrder):
        return self.agg_rets_dict.get(obj.pk, {}).get("rfid_prefix", [])

    def get_warehouse_name(self, obj: ERPOutboundOrder):
        return obj.warehouse.name if obj.warehouse else ""

    def get_warehouse_position_name(self, obj: ERPOutboundOrder):
        return obj.warehouse_position.name if obj.warehouse_position else ""


class ERPOutboundOrderDetailSerializer(CustomizeSerializer):
    name = serializers.CharField(source="sku.name")
    sku_id = serializers.IntegerField(source="sku.sku_id")
    code = serializers.CharField(source="sku.product.code")
    spec_code = serializers.CharField(source="sku.spec_code")
    size = serializers.CharField(source="sku.size")
    main_images = serializers.ListSerializer(child=serializers.CharField(), source="sku.main_images")
    unit_name = serializers.CharField(source="sku.unit.name", allow_null=True)
    label_price = serializers.DecimalField(max_digits=10, decimal_places=2, source="sku.label_price")

    verified_quantity = serializers.IntegerField()
    rfid = serializers.SerializerMethodField()
    # supplier_name = serializers.CharField(source="supplier.name", allow_null=True)
    # contact_user_name = serializers.CharField(source="supplier.contact_user_name", allow_null=True)
    # contact_user_mobile = serializers.CharField(source="supplier.contact_user_mobile", allow_null=True)

    class Meta:
        model = ERPOutboundOrderDetail
        fields = (
            "id",
            # "supplier_name",
            # "contact_user_name",
            # "contact_user_mobile",
            "name",
            "code",
            "main_images",
            "spec_code",
            "size",
            "label_price",
            "cost_price",
            "quantity",
            "unit_name",
            "verified_quantity",
            "sku_id",
            "rfid",
            "inventory_id",
            "tag_type",
        )

    def get_rfid(self, instand: ERPOutboundOrderDetail):
        return [decimal_to_base62(instand.sku.sku_id) + tid for tid in instand.tid_info.keys()]


class ERPOutboundOrderUpdateSerializer(CustomizeSerializer):
    # supplier_id = serializers.IntegerField(required=False, allow_null=True)
    # inbound_order_id = serializers.IntegerField(required=False, allow_null=True)
    # warehouse_id = serializers.IntegerField(required=False, allow_null=True)
    # warehouse_position_id = serializers.IntegerField(required=False, allow_null=True)
    total_cost_price = serializers.DecimalField(max_digits=20, decimal_places=2, required=False)

    status = serializers.ChoiceField(required=False, choices=((1, "正常"), (2, "取消")))
    remark = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    remark_images = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        default=list,
    )
    details = serializers.ListField(child=serializers.DictField(), required=False)

    class Meta:
        model = ERPOutboundOrder
        fields = (
            # "outbound_type",
            # "create_user",
            # "company_id",
            # "supplier_id",
            "status",
            "remark",
            "remark_images",
            # "create_user",
            # "submit_date",
            # "warehouse_id",
            # "warehouse_position_id",
            # "inbound_order_id",
            "details",
            "total_cost_price",
        )

    def validate(self, attrs):
        outbound_order = self.instance
        if attrs.get("status") and outbound_order.approve_status != 1:
            raise APIViewException(err_message="只允许操作未审核的订单")
        return attrs

    def update(self, instance, validated_data):
        with transaction.atomic():
            for key, value in validated_data.items():
                setattr(instance, key, value)

            details = validated_data.pop("details", [])
            detail_total_cost_price = None
            if details:
                detail_instance_mp = {i.id: i for i in instance.erpoutboundorderdetail_set.all()}

                # 判断是否在调整中
                sku_pk_list = [d.sku_id for d in detail_instance_mp.values()]
                InventoryAdjustmentOrderDetail.judge_sku_is_adjusting(sku_pk_list)

                # 明细
                for detail in details:
                    id = detail.pop("id")
                    if id not in detail_instance_mp:
                        continue
                    # detail_instance = ERPOutboundOrderDetail.objects.get(pk=detail["id"])
                    detail_instance = detail_instance_mp[id]
                    detail_instance.cost_price = detail["cost_price"]
                    detail_instance.save()

                # 总金额
                detail_total_cost_price = sum([item.cost_price * item.quantity for item in detail_instance_mp.values()])
                # detail_total_label_price = sum([item.label_price * item.quantity for item in detail_instance_mp.values()])
                # detail_total_market_price = sum([item.market_price * item.quantity for item in detail_instance_mp.values()])
                # detail_total_purchase_price = sum([item.purchase_price * item.quantity for item in detail_instance_mp.values()])

            # 优先使用提交的总价
            if validated_data.get("total_cost_price", None) is not None:
                total_cost_price = validated_data.pop("total_cost_price")
            else:
                total_cost_price = detail_total_cost_price

            if total_cost_price is not None:
                instance.total_cost_price = total_cost_price

            instance.save()
            return instance


class ERPOutboundOrderDetailsDownloadSerializer(CustomizeSerializer):
    name = serializers.CharField(source="sku.name")
    code = serializers.CharField(source="sku.product.code")
    spec_code = serializers.CharField(source="sku.spec_code")
    main_image = serializers.SerializerMethodField()
    spec_value = serializers.CharField(source="sku.spec_value")
    category = serializers.SerializerMethodField()
    size = serializers.CharField(source="sku.size")
    weight = serializers.CharField(source="sku.weight")
    unit_name = serializers.CharField(source="sku.unit.name", allow_null=True)
    remark = serializers.CharField(source="sku.remark", allow_null=True, allow_blank=True)
    product_type_display = serializers.CharField(source="sku.get_product_type_display")
    warehouse_name = serializers.SerializerMethodField()
    warehouse_position_name = serializers.SerializerMethodField()
    quantity = serializers.IntegerField()
    other_info = serializers.SerializerMethodField()
    total_cost_price = serializers.SerializerMethodField()
    total_label_price = serializers.SerializerMethodField()
    total_market_price = serializers.SerializerMethodField()
    total_purchase_price = serializers.SerializerMethodField()

    class Meta:
        model = ERPOutboundOrderDetail
        fields = (
            "name",
            "code",
            "spec_code",
            "main_image",
            "spec_value",
            "cost_price",
            "label_price",
            "market_price",
            "purchase_price",
            "category",
            "size",
            "weight",
            "unit_name",
            "remark",
            "product_type_display",
            "warehouse_name",
            "warehouse_position_name",
            "quantity",
            "other_info",
            "total_cost_price",
            "total_label_price",
            "total_market_price",
            "total_purchase_price",
        )

    @staticmethod
    def get_total_purchase_price(obj: ERPOutboundOrderDetail):
        return (obj.purchase_price or 0) * (obj.quantity or 0)

    @staticmethod
    def get_total_market_price(obj: ERPOutboundOrderDetail):
        return (obj.market_price or 0) * (obj.quantity or 0)

    @staticmethod
    def get_total_label_price(obj: ERPOutboundOrderDetail):
        return (obj.label_price or 0) * (obj.quantity or 0)

    @staticmethod
    def get_total_cost_price(obj: ERPOutboundOrderDetail):
        return (obj.cost_price or 0) * (obj.quantity or 0)

    @staticmethod
    def get_main_image(obj: ERPOutboundOrderDetail):
        return obj.sku.main_images[0] if obj.sku.main_images else ""

    def get_category(self, obj: ERPOutboundOrderDetail):
        category_cache = self.context["category_cache"]

        return "/".join([i["name"] for i in category_cache.find_parents(obj.sku.category_id)])

    def get_warehouse_name(self, obj: ERPOutboundOrderDetail):
        return self.context.get("warehouse_name") or ""

    def get_warehouse_position_name(self, obj: ERPOutboundOrderDetail):
        return self.context.get("warehouse_position_name") or ""

    @staticmethod
    def get_other_info(obj: ERPOutboundOrderDetail):
        sku = obj.sku
        return {
            "other_price1": sku.other_price1,
            "other_price2": sku.other_price2,
            "other_price3": sku.other_price3,
            "other_price4": sku.other_price4,
            "other_price5": sku.other_price5,
            "other_price6": sku.other_price6,
            "other_attributes": sku.other_attributes,
            "other_attributes2": sku.other_attributes2,
            "other_attributes3": sku.other_attributes3,
            "other_attributes4": sku.other_attributes4,
            "other_attributes5": sku.other_attributes5,
        }


class ERPOutboundOrderDownloadSerializer(CustomizeSerializer):
    supplier_info = serializers.SerializerMethodField()
    create_date = serializers.DateTimeField(format=DATETIME_FORMAT)
    approve_status_display = serializers.CharField(source="get_approve_status_display")
    status_display = serializers.CharField(source="get_status_display")
    pay_status_display = serializers.SerializerMethodField()
    total_rfid_quantity = serializers.SerializerMethodField()
    total_normal_quantity = serializers.SerializerMethodField()
    total_rfid_not_verified_quantity = serializers.SerializerMethodField()
    total_rfid_verified_quantity = serializers.SerializerMethodField()
    details = serializers.SerializerMethodField()

    class Meta:
        model = ERPOutboundOrder
        fields = (
            "supplier_info",
            "code",
            "create_date",
            "create_user",
            "total_label_price",
            "status_display",
            "approve_status_display",
            "pay_status_display",
            "remark",
            "total_rfid_quantity",
            "total_normal_quantity",
            "total_rfid_not_verified_quantity",
            "total_rfid_verified_quantity",
            "details",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.quantity_calculate_map = {}

        category_id_list = set()
        for i in self.instance:
            total_rfid_quantity = 0
            total_normal_quantity = 0
            total_rfid_not_verified_quantity = 0
            total_rfid_verified_quantity = 0
            for detail in i.erpoutboundorderdetail_set.all():
                if detail.tag_type == 1:
                    total_rfid_quantity += detail.quantity
                    total_rfid_verified_quantity += detail.verified_quantity
                    total_rfid_not_verified_quantity += detail.quantity - detail.verified_quantity
                elif detail.tag_type == 2:
                    total_normal_quantity += detail.quantity

                if detail.sku.category_id:
                    category_id_list.add(detail.sku.category_id)

            self.quantity_calculate_map[i.pk] = {
                "total_rfid_quantity": total_rfid_quantity,
                "total_normal_quantity": total_normal_quantity,
                "total_rfid_not_verified_quantity": total_rfid_not_verified_quantity,
                "total_rfid_verified_quantity": total_rfid_verified_quantity,
            }
        category_qs = ERPProductCategory.get_ancestors_with_category_id_list(category_id_list).values("id", "name", "parent_id")
        self.category_cache = CategoryFindAncestorsCache(category_qs)

    @staticmethod
    def get_pay_status_display(obj: ERPOutboundOrder):
        if obj.settle_status == 2:
            return "已支付"
        return "待支付"

    @staticmethod
    def get_supplier_info(obj: ERPOutboundOrder):
        if not obj.supplier_id:
            return {}
        return ERPSupplierInfoDownloadSerializer(instance=obj.supplier, many=False).data

    def get_total_rfid_quantity(self, obj: ERPOutboundOrder):
        return self.quantity_calculate_map.get(obj.pk, {}).get("total_rfid_quantity") or 0

    def get_total_normal_quantity(self, obj: ERPOutboundOrder):
        return self.quantity_calculate_map.get(obj.pk, {}).get("total_normal_quantity") or 0

    def get_total_rfid_not_verified_quantity(self, obj: ERPOutboundOrder):
        return self.quantity_calculate_map.get(obj.pk, {}).get("total_rfid_not_verified_quantity") or 0

    def get_total_rfid_verified_quantity(self, obj: ERPOutboundOrder):
        return self.quantity_calculate_map.get(obj.pk, {}).get("total_rfid_verified_quantity") or 0

    def get_details(self, obj: ERPOutboundOrder):
        details = obj.erpoutboundorderdetail_set.all()
        return ERPOutboundOrderDetailsDownloadSerializer(
            instance=details,
            many=True,
            context={
                "category_cache": self.category_cache,
                "warehouse_name": obj.warehouse.name if obj.warehouse_id else "",
                "warehouse_position_name": obj.warehouse_position.name if obj.warehouse_position_id else "",
            },
        ).data
