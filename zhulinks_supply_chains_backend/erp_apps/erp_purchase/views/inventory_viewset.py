# -*- coding: utf-8 -*-
import copy

from common.basics.exceptions import APIViewException
from common.basics.viewsets import SPModelViewSet
from common.models import DownloadTasksTypeChoices
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.paginator import Paginator
from django.db import models, transaction
from django.utils import timezone
from erp_products.models import ERPSupplierProductSKU
from erp_products.utils import decimal_to_base62, generate_tid
from erp_purchase.filters.erp_sku_inventory_filter import ERPSKUInventoryFilterSet
from erp_purchase.filters.supplier_inventory_query_filter import SupplierInventoryChangeDetailsFilter, SupplierSKUInventoryListFilter
from erp_purchase.models import ERPSKUInventory, InventoryChangeDetails, InventoryAdjustmentOrderDetail
from erp_purchase.serializers.inventory_order_serializers import (
    SupplierRfidQuerySKUInventoryListSer,
    SupplierRFIDQuerySKUListSer,
    SupplierSKUInventoryBySkuListSer,
    SupplierSKUInventoryByWarehouseListSer,
    SupplierSKUInventoryChangeDetailsListSer,
    SupplierSKUInventoryDownloadSerializer,
    SupplierSKUInventoryListSer,
    SupplierSKUInventoryWarehousePositionViewListSer,
)
from erp_purchase.tasks.async_download_tasks import async_download_sku_inventory_task
from rest_framework.decorators import action
from rest_framework.request import Request
from stock_take.utils import tid_to_sku_map
from utils.erp_download_utils.erp_async_download_task import ERPAsyncDownloadTaskReceiver
from utils.http_handle import EmptyListResponse, IResponse, custom_django_filter


class InventoryViewSet(SPModelViewSet):
    """
    库存查询视图集

    提供库存查询和RFID找货功能
    """

    serializer_class = SupplierSKUInventoryListSer
    filterset_class = SupplierSKUInventoryListFilter
    force_order = False
    lookup_field = "pk"

    def get_queryset(self):
        """
        获取当前用户公司的库存列表
        """
        qs = ERPSKUInventory.objects.filter(
            status=1,
            company=self.current_user.company,
        )

        if self.action == "query_by_warehouse_position":
            # 按照sku、仓库、仓位、不同的标签类型 进行聚合
            return (
                qs.values(
                    "sku_id",
                    "warehouse_id",
                    "warehouse_position_id",
                    "tag_type",
                )
                .annotate(
                    in_warehouse_quantity=models.Sum("in_warehouse_quantity"),
                    loaned_quantity=models.Sum("loaned_quantity"),
                    sold_quantity=models.Sum("sold_quantity"),
                    sold_return_quantity=models.Sum("sold_return_quantity"),
                    purchase_return_quantity=models.Sum("purchase_return_quantity"),
                    stock_transfer_quantity=models.Sum("stock_transfer_quantity"),
                )
                .values(
                    "sku_id",
                    "warehouse_id",
                    "warehouse_position_id",
                    "tag_type",
                    "in_warehouse_quantity",
                    "loaned_quantity",
                    "sold_quantity",
                    "sold_return_quantity",
                    "purchase_return_quantity",
                    "stock_transfer_quantity",
                    inbound_detail_codes=ArrayAgg("id"),
                    warehouse_name=models.F("warehouse__name"),
                    warehouse_position_name=models.F("warehouse_position__name"),
                    main_images=models.F("sku__main_images"),
                    name=models.F("sku__name"),
                    code=models.F("sku__product__code"),
                    business_sku_id=models.F("sku__sku_id"),
                    spec_value=models.F("sku__spec_value"),
                    spec_code=models.F("sku__spec_code"),
                    unit_id=models.F("sku__unit_id"),
                    unit_name=models.F("sku__unit__name"),
                    weight=models.F("sku__weight"),
                    cost_price=models.F("sku__cost_price"),
                    label_price=models.F("sku__label_price"),
                    market_price=models.F("sku__market_price"),
                    purchase_price=models.F("sku__purchase_price"),
                    # supplier_id=models.F("supplier_id"),
                    # supplier_name=models.F("supplier__name"),
                    category_id=models.F("sku__category_id"),
                    min_inventory_capacity=models.F("sku__min_inventory_capacity"),
                    max_inventory_capacity=models.F("sku__max_inventory_capacity"),
                    other_attributes=models.F("sku__other_attributes"),
                    other_attributes2=models.F("sku__other_attributes2"),
                    other_attributes3=models.F("sku__other_attributes3"),
                    other_attributes4=models.F("sku__other_attributes4"),
                    other_attributes5=models.F("sku__other_attributes5"),
                    max_create_date=models.Max("create_date"),
                )
                .order_by("-max_create_date")
            )
        elif self.action == "query_by_warehouse":
            return (
                qs.prefetch_related("sku")
                .values("sku_id", "warehouse_id")
                .annotate(
                    inbound_detail_codes=ArrayAgg("id"),
                    stock_transfer_quantity=models.Sum("stock_transfer_quantity"),
                    in_warehouse_quantity=models.Sum("in_warehouse_quantity"),
                    loaned_quantity=models.Sum("loaned_quantity"),
                    sold_quantity=models.Sum("sold_quantity"),
                    sold_return_quantity=models.Sum("sold_return_quantity"),
                    purchase_return_quantity=models.Sum("purchase_return_quantity"),
                    # total_cost_price=models.Sum(models.F("in_warehouse_quantity") * models.F("cost_price")),
                    # total_label_price=models.Sum(models.F("in_warehouse_quantity") * models.F("label_price")),
                    max_create_date=models.Max("create_date"),
                )
                .order_by("-max_create_date")
            )
        elif self.action == "query_by_sku":
            return (
                qs.prefetch_related("sku")
                .values("sku_id")
                .annotate(
                    inbound_detail_codes=ArrayAgg("id"),
                    stock_transfer_quantity=models.Sum("stock_transfer_quantity"),
                    in_warehouse_quantity=models.Sum("in_warehouse_quantity"),
                    loaned_quantity=models.Sum("loaned_quantity"),
                    sold_quantity=models.Sum("sold_quantity"),
                    sold_return_quantity=models.Sum("sold_return_quantity"),
                    purchase_return_quantity=models.Sum("purchase_return_quantity"),
                    # total_cost_price=models.Sum(models.F("in_warehouse_quantity") * models.F("cost_price")),
                    # total_label_price=models.Sum(models.F("in_warehouse_quantity") * models.F("label_price")),
                    max_create_date=models.Max("create_date"),
                )
                .order_by("-max_create_date")
            )
        elif self.action == "download":
            # 导出是以SKU维度进行统计
            return (
                qs.prefetch_related("sku")
                .values("sku_id")
                .annotate(
                    stock_transfer_quantity=models.Sum("stock_transfer_quantity"),
                    in_warehouse_quantity=models.Sum("in_warehouse_quantity"),
                    loaned_quantity=models.Sum("loaned_quantity"),
                    sold_quantity=models.Sum("sold_quantity"),
                    sold_return_quantity=models.Sum("sold_return_quantity"),
                    purchase_return_quantity=models.Sum("purchase_return_quantity"),
                    max_create_date=models.Max("create_date"),
                )
                .order_by("-max_create_date")
            )
        else:
            return qs.order_by("-create_date")

    def get_serializer_class(self):
        if self.action == "query_by_warehouse_position":
            return SupplierSKUInventoryWarehousePositionViewListSer
        elif self.action == "query_by_warehouse":
            return SupplierSKUInventoryByWarehouseListSer
        elif self.action == "query_by_sku":
            return SupplierSKUInventoryBySkuListSer
        elif self.action == "download":
            return SupplierSKUInventoryDownloadSerializer

        return self.serializer_class

    @action(detail=False, methods=["get"])
    def query(self, request: Request):
        """
        库存查询列表
        :param request:
        :return:
        """
        return super().list(request)

    @action(detail=False, methods=["get"])
    def query_by_warehouse_position(self, request: Request):
        """
        库存查询列表-根据sku_id、仓库、仓位、标签类型 聚合
        :param request:
        :return:
        """
        return super().list(request)

    @action(detail=False, methods=["get"])
    def query_by_warehouse(self, request: Request):
        """
        库存查询列表-根据sku_id、仓库聚合
        :param request:
        :return:
        """
        return super().list(request)

    @action(detail=False, methods=["get"])
    def query_by_sku(self, request: Request):
        """
        小程序
        库存查询列表-根据sku_id聚合
        :param request:
        :return:
        """
        return super().list(request)

    @action(detail=False, methods=["post"])
    def rfid_query(self, request: Request):
        """
        RFID找货
        """
        post_data = request.data
        spec_code = post_data.get("spec_code")
        output_type = post_data.get("output_type", "tids")
        query_from = post_data.get("query_from")

        tids = post_data.get("tids")
        if not spec_code and not tids:
            return IResponse(data=EmptyListResponse)

        if query_from == "sales_return":
            sku_tid_map = tid_to_sku_map(tids)

            skus = ERPSupplierProductSKU.objects.filter(
                sku_id__in=sku_tid_map.keys(),
                is_deleted=False,
                company=self.current_user.company,
            )
            re_data, *_ = custom_django_filter(
                request,
                skus,
                ERPSKUInventoryFilterSet,
                SupplierRFIDQuerySKUListSer,
                ser_context={
                    "sku_tid_map": sku_tid_map,
                    "output_type": output_type,
                },
            )
            info_data = re_data["data"]

            new_info_data = []
            if output_type == "tids":
                for d in info_data:
                    if d["sku_id"] in sku_tid_map:
                        for sku_id, tid_set in sku_tid_map.items():
                            if d["sku_id"] == sku_id:
                                for tid in tid_set:
                                    tmp = copy.deepcopy(d)
                                    tmp["tid"] = decimal_to_base62(sku_id) + tid
                                    new_info_data.append(tmp)
            re_data["data"] = new_info_data
            return IResponse(data=re_data)

        # 库存查询
        inventories = (
            ERPSKUInventory.objects.filter(
                status=1,
                company=self.current_user.company,
            )
            .prefetch_related(
                "sku",
                "sku__product",
                "sku__unit",
                "warehouse",
                "warehouse_position",
            )
            .order_by("-update_date")
        )

        if spec_code:
            inventories = inventories.filter(sku__spec_code__icontains=spec_code)

        sku_tid_map = {}
        if tids:
            sku_tid_map = tid_to_sku_map(tids)

            if not sku_tid_map:
                return IResponse(data=EmptyListResponse)

            or_q = models.Q()

            for tid_values in sku_tid_map.values():
                or_q |= models.Q(tid_info__has_any_keys=tid_values)

            inventories = inventories.filter(sku__sku_id__in=list(sku_tid_map.keys())).filter(or_q)

            if output_type == "tids":
                tid_list = [t for i in list(sku_tid_map.values()) for t in i]
                inventories = ERPSKUInventory.expand_tid_info_query(tid_list, sku_inventory_obj=inventories)

        re_data, *_ = custom_django_filter(
            request,
            inventories,
            ERPSKUInventoryFilterSet,
            SupplierRfidQuerySKUInventoryListSer,
            ser_context={
                "sku_tid_map": sku_tid_map,
                "output_type": output_type,
            },
        )
        return IResponse(data=re_data)

    @action(detail=True, methods=["get"])
    def tid_details(self, request: Request, pk=None):
        """
        库存详情
        """
        erp_sku_inventory = self.get_no_filter_object()
        status = request.query_params.get("status", "normal")
        tid = request.query_params.get("tid")
        is_preprint_tid = request.query_params.get("is_preprint_tid")

        try:
            page = int(request.query_params.get("page", 1))
            page_size = int(request.query_params.get("page_size", 20))
        except ValueError:
            page = 1
            page_size = 20
        sku_id = erp_sku_inventory.sku.sku_id
        if status == "normal":
            # 正常库存
            tid_list = erp_sku_inventory.get_in_warehouse_tid_list()
        elif status == "preprint":
            # 待打印的标签
            tid_list = erp_sku_inventory.get_preprint_tid_list()
        else:
            tid_list = []
        # 处理好前端使用的tid信息，用sku_id + tid拼接
        handle_tid_list = [{"{}{}".format(decimal_to_base62(sku_id), k): v} for i in tid_list for k, v in i.items()]

        # 过滤tid, 查询前端传来的tid
        if tid:
            handle_tid_list = [i for i in handle_tid_list for k, v in i.items() if tid in k]

        # 过滤待打印的tid
        # 1: 待打印 2: 已打印
        if is_preprint_tid:
            if is_preprint_tid == "1":
                handle_tid_list = [i for i in handle_tid_list for k, v in i.items() if v["print_status"] == "unprint"]
            elif is_preprint_tid == "2":
                handle_tid_list = [i for i in handle_tid_list for k, v in i.items() if v["print_status"] != "unprint"]

        paginator = Paginator(handle_tid_list, page_size)
        page_obj = paginator.get_page(page)

        re_data = [
            {
                "status": "在仓",
                "tid": tid,
                "count": 1,
                "is_preprint_tid": 1 if tid_val["print_status"] == "unprint" else 0,
                "is_preprint_tid_display": "待打印" if tid_val["print_status"] == "unprint" else "已打印",
                "preprint_count": 1 if tid_val["print_status"] == "unprint" else 0,
                "warehouse_name": erp_sku_inventory.warehouse.name,
                "warehouse_position_name": erp_sku_inventory.warehouse_position.name if erp_sku_inventory.warehouse_position_id else "",
            }
            for i in page_obj.object_list
            for tid, tid_val in i.items()
        ]

        re_data = {
            "count": paginator.count,
            "total_pages": paginator.num_pages,
            "current_page": page,
            "data": re_data,
        }
        return IResponse(data=re_data)

    @action(detail=False, methods=["post"])
    def invalidate_tids(self, request: Request):
        """
        TID作废接口

        前端传来的数据格式: {"tids": ["tid1", "tid2", ...]}
        将指定的TID状态修改为invalid，同时更新库存数量
        """
        tids = request.data.get("tids", [])
        if not tids:
            return IResponse(code=400, message="TIDs are required")

        # 使用tid_to_sku_map解析TID，获取SKU ID和对应的TID映射
        sku_tid_map = tid_to_sku_map(tids)
        if not sku_tid_map:
            return IResponse(code=400, message="Invalid TIDs format")

        # 记录处理结果
        result = {"success": 0, "failed": 0, "details": []}

        # 判断是否正在调整库存
        sku_id_list = list(sku_tid_map.keys())
        sku_pk_list = ERPSupplierProductSKU.objects.filter(
            sku_id__in=sku_id_list,
            is_deleted=False,
            company=self.current_user.company,
        ).values_list("id", flat=True)
        InventoryAdjustmentOrderDetail.judge_sku_is_adjusting(sku_pk_list)

        # 明细
        inventories_mp = {}
        inventory_id_list = []
        with transaction.atomic():
            # 遍历每个SKU ID和对应的TID列表
            for sku_id, tid_list in sku_tid_map.items():
                # 查找对应的库存记录
                inventories = ERPSKUInventory.objects.filter(
                    status=1,
                    company=self.current_user.company,
                    sku__sku_id=sku_id,
                    tid_info__has_any_keys=tid_list,
                )

                if not inventories.exists():
                    # 如果找不到对应的库存记录，记录失败
                    for tid in tid_list:
                        result["failed"] += 1
                        result["details"].append({"tid": f"{decimal_to_base62(sku_id)}{tid}", "status": "failed", "message": "Inventory not found"})
                    continue

                inventories_mp.update({inventory.id: {"inventory": inventory, "before_quantity": inventory.in_warehouse_quantity, "quantity": 0} for inventory in inventories})

                # 遍历每个TID，更新状态
                for tid in tid_list:
                    tid_found = False

                    # 遍历每个库存记录，查找包含该TID的记录
                    for inventory in inventories:
                        if tid in inventory.tid_info and inventory.tid_info[tid]["verify_status"] in ["verified", "unverified"]:
                            # 找到了TID，更新状态为invalid
                            inventory.set_tid_invalid_status(tid, self.current_user.user_id)

                            # 更新库存数量
                            inventory.calculate_quantity_data()

                            # 保存更新
                            inventory.save()

                            # 记录成功
                            result["success"] += 1
                            result["details"].append(
                                {
                                    "tid": f"{decimal_to_base62(sku_id)}{tid}",
                                    "status": "success",
                                    "message": "TID invalidated successfully",
                                }
                            )

                            tid_found = True
                            inventory_id_list.append(inventory.pk)

                            inventories_mp[inventory.id]["quantity"] += 1

                            break

                    if not tid_found:
                        # 如果找不到TID，记录失败
                        result["failed"] += 1
                        result["details"].append(
                            {
                                "tid": f"{decimal_to_base62(sku_id)}{tid}",
                                "status": "failed",
                                "message": "TID not found in inventory or already invalidated",
                            }
                        )

            # 明细log
            for inv_id, item in inventories_mp.items():
                if not item["quantity"]:
                    continue
                inventory = item["inventory"]
                InventoryChangeDetails.create_detail(
                    InventoryChangeDetails.CHANGE_TYPE_1,
                    inventory.id,
                    force_insert=True,  # 每次都写入新纪录
                    **{
                        "inventory_id": inventory.id,
                        "company_id": self.current_user.company.company_id,
                        "sku_id": inventory.sku_id,
                        "warehouse_id": inventory.warehouse_id,
                        "warehouse_position_id": inventory.warehouse_position_id,
                        "quantity": item["quantity"],
                        "before_quantity": item["before_quantity"],
                        "after_quantity": item["before_quantity"] - item["quantity"],
                        "create_user": self.current_user.user_id,
                        "supplier_id": inventory.supplier_id,
                        "remark": "作废TID",
                    },
                )

            # 记录操作日志
            for inventory_id in inventory_id_list:
                self.write_log(
                    resource_id=inventory_id,
                    resource_name=self.resource_name,
                    request=request,
                    describe=f"作废TID: {', '.join(tids[:5])}{' 等' if len(tids) > 5 else ''}",
                    platform=self.current_user_type,
                    content_type_id=ContentType.objects.get_for_model(ERPSKUInventory).pk,
                    fronted_page=self.fronted_page,
                    operate_content="",
                    user_mobile=self.current_user.user_id,
                )

        return IResponse(data=result)

    @action(detail=False, methods=["post"])
    def reprint_tids(self, request: Request):
        """
        TID补打接口

        前端传来的数据格式: {"tids": ["tid1", "tid2", ...]}
        将指定的TID状态修改为invalid，并生成新的TID
        """
        tids = request.data.get("tids", [])
        if not tids:
            return IResponse(code=400, message="TIDs are required")

        # 使用tid_to_sku_map解析TID，获取SKU ID和对应的TID映射
        sku_tid_map = tid_to_sku_map(tids)
        if not sku_tid_map:
            return IResponse(code=400, message="Invalid TIDs format")

        # 判断是否正在调整库存
        sku_id_list = list(sku_tid_map.keys())
        sku_pk_list = ERPSupplierProductSKU.objects.filter(
            sku_id__in=sku_id_list,
            is_deleted=False,
            company=self.current_user.company,
        ).values_list("id", flat=True)
        InventoryAdjustmentOrderDetail.judge_sku_is_adjusting(sku_pk_list)

        # 记录处理结果
        result = {
            "success": 0,
            "failed": 0,
            "new_tids": [],
            "details": [],
        }

        # 明细
        inventories_mp = {}

        with transaction.atomic():
            # 遍历每个SKU ID和对应的TID列表
            for sku_id, tid_list in sku_tid_map.items():
                # 查找对应的库存记录
                inventories = ERPSKUInventory.objects.filter(status=1, company=self.current_user.company, sku__sku_id=sku_id)

                if not inventories.exists():
                    # 如果找不到对应的库存记录，记录失败
                    for tid in tid_list:
                        result["failed"] += 1
                        result["details"].append(
                            {
                                "tid": f"{decimal_to_base62(sku_id)}{tid}",
                                "status": "failed",
                                "message": "Inventory not found",
                            }
                        )
                    continue

                inventories_mp.update({inventory.id: {"inventory": inventory, "before_quantity": inventory.in_warehouse_quantity, "quantity": 0} for inventory in inventories})

                # 遍历每个TID，更新状态并生成新的TID
                for tid in tid_list:
                    tid_found = False

                    # 遍历每个库存记录，查找包含该TID的记录
                    for inventory in inventories:
                        if tid in inventory.tid_info and inventory.tid_info[tid]["verify_status"] in ["verified", "unverified"]:
                            # 找到了TID，更新状态为invalid
                            inventory.set_tid_invalid_status(tid, self.current_user.user_id)

                            # 生成新的TID
                            new_tid = generate_tid()

                            # 添加新的TID信息
                            inventory.tid_info[new_tid] = {
                                "print_status": "unprint",  # 未打印状态
                                "verify_status": "verified",  # 已校验状态
                                "source": "reprint",  # 来源为补打
                                "create_user": self.current_user.user_id,
                                "create_date": str(timezone.now()),
                                "update_user": "",
                                "update_date": "",
                                "remark": f"补打原标签: {tid}",
                            }

                            # 更新库存数量
                            inventory.calculate_quantity_data()

                            # 保存更新
                            inventory.save()

                            # 记录成功
                            result["success"] += 1
                            new_tid_with_prefix = f"{decimal_to_base62(sku_id)}{new_tid}"
                            result["new_tids"].append(new_tid_with_prefix)
                            result["details"].append(
                                {
                                    "old_tid": f"{decimal_to_base62(sku_id)}{tid}",
                                    "new_tid": new_tid_with_prefix,
                                    "status": "success",
                                    "message": "TID reprinted successfully",
                                }
                            )

                            tid_found = True
                            inventories_mp[inventory.id]["quantity"] += 1
                            break

                    if not tid_found:
                        # 如果找不到TID，记录失败
                        result["failed"] += 1
                        result["details"].append(
                            {
                                "tid": f"{decimal_to_base62(sku_id)}{tid}",
                                "status": "failed",
                                "message": "TID not found in inventory or already invalidated",
                            }
                        )

            # 明细log
            for inv_id, item in inventories_mp.items():
                if not item["quantity"]:
                    continue
                inventory = item["inventory"]
                # # 作废标签，取消流水
                # InventoryChangeDetails.create_detail(
                #     InventoryChangeDetails.CHANGE_TYPE_1,
                #     inventory.id,
                #     **{
                #         "inventory_id": inventory.id,
                #         "company_id": self.current_user.company.company_id,
                #         "sku_id": inventory.sku_id,
                #         "warehouse_id": inventory.warehouse_id,
                #         "warehouse_position_id": inventory.warehouse_position_id,
                #         "quantity": item["quantity"],
                #         "before_quantity": item["before_quantity"],
                #         "after_quantity": item["before_quantity"] - item["quantity"],
                #         "create_user": self.current_user.user_id,
                #         "supplier_id": inventory.supplier_id,
                #         "remark": "作废TID",
                #     },
                # )

            # 记录操作日志
            self.log_operation(
                request,
                ERPSKUInventory,
                describe=f"补打TID: {', '.join(tids[:5])}{' 等' if len(tids) > 5 else ''}",
                is_success_input=True,
            )

        return IResponse(data=result)

    @action(detail=True, methods=["get", "post"])
    def print_labels(self, request: Request, pk=None):
        """
        标签打印接口

        GET方法: 获取可打印的TID列表，不分页
        POST方法: 更新TID的打印状态，前端传来的数据格式: {"tids": ["tid1", "tid2", ...]}
        """
        erp_sku_inventory = self.get_object()

        # 判断是否正在调整库存
        sku_pk_list = erp_sku_inventory.sku_id
        InventoryAdjustmentOrderDetail.judge_sku_is_adjusting(sku_pk_list)

        if request.method == "GET":
            # 获取所有未打印的TID列表

            sku = erp_sku_inventory.sku
            product = sku.product

            sku_id = sku.sku_id
            re_data = [{"name": sku.name, "spec_code": sku.spec_code, "code": product.code, "tids": [], "tag_type": erp_sku_inventory.tag_type}]
            for tid, info in erp_sku_inventory.tid_info.items():
                # 只获取未打印且状态正常的TID
                if info.get("print_status") == "unprint" and info.get("verify_status") in ["verified", "unverified"]:
                    full_tid = f"{decimal_to_base62(sku_id)}{tid}"
                    re_data[0]["tids"].append(full_tid)
            return IResponse(data=re_data)

        elif request.method == "POST":
            # 更新TID的打印状态
            tids = request.data.get("tids", [])
            if not tids:
                return IResponse(code=400, message="TIDs are required")

            # 使用tid_to_sku_map解析TID，获取SKU ID和对应的TID映射
            sku_tid_map = tid_to_sku_map(tids)
            if not sku_tid_map:
                return IResponse(code=400, message="Invalid TIDs format")

            # 记录处理结果
            result = {"success": 0, "failed": 0, "details": []}

            quantity = 0
            before_quantity = erp_sku_inventory.in_warehouse_quantity

            with transaction.atomic():
                # 遍历每个SKU ID和对应的TID列表
                for sku_id, tid_list in sku_tid_map.items():
                    # 遍历每个TID，更新打印状态
                    for tid in tid_list:
                        tid_found = False

                        # 遍历每个库存记录，查找包含该TID的记录
                        if tid in erp_sku_inventory.tid_info and erp_sku_inventory.tid_info[tid]["verify_status"] in ["verified", "unverified"]:
                            # 找到了TID，更新打印状态
                            erp_sku_inventory.tid_info[tid].update(
                                {
                                    "print_status": "printed",
                                    "update_user": self.current_user.user_id,
                                    "update_date": str(timezone.now()),
                                }
                            )

                            # 保存更新
                            erp_sku_inventory.save()

                            # 记录成功
                            result["success"] += 1
                            result["details"].append({"tid": f"{decimal_to_base62(sku_id)}{tid}", "status": "success", "message": "TID print status updated successfully"})

                            quantity += 1
                            tid_found = True

                        if not tid_found:
                            # 如果找不到TID，记录失败
                            result["failed"] += 1
                            result["details"].append(
                                {"tid": f"{decimal_to_base62(sku_id)}{tid}", "status": "failed", "message": "TID not found in inventory or already invalidated"}
                            )

                # # 明细log 补打
                # if quantity:
                #     InventoryChangeDetails.create_detail(
                #         InventoryChangeDetails.CHANGE_TYPE_2,
                #         erp_sku_inventory.id,
                #         **{
                #             "inventory_id": erp_sku_inventory.id,
                #             "company_id": self.current_user.company.company_id,
                #             "sku_id": erp_sku_inventory.sku_id,
                #             "warehouse_id": erp_sku_inventory.warehouse_id,
                #             "warehouse_position_id": erp_sku_inventory.warehouse_position_id,
                #             "quantity": quantity,
                #             "before_quantity": before_quantity,
                #             "after_quantity": before_quantity + quantity,
                #             "create_user": self.current_user.user_id,
                #             "supplier_id": erp_sku_inventory.supplier_id,
                #             "remark": "补打TID",
                #         },
                #     )

            # 记录操作日志
            self.log_operation(
                request,
                ERPSKUInventory,
                resource_id=pk,
                describe=f"打印TID标签: {', '.join(tids[:5])}{' 等' if len(tids) > 5 else ''}",
                is_success_input=True,
            )

            return IResponse(data=result)

        return IResponse()

    @action(detail=False, methods=["post"])
    def download(self, request, *args, **kwargs):
        task = ERPAsyncDownloadTaskReceiver(
            request,
            "库存查询",
            DownloadTasksTypeChoices.SKU_INVENTORY,
            self.current_user.user_id,
            self.current_user_type,
            self.current_user.company,
            async_download_sku_inventory_task,
        ).process_task()
        return IResponse(code=201, message="后台正在处理下载任务", data={"task_id": task.id})

    @action(detail=False, methods=["get"])
    def query_tids(self, request: Request):
        """
        查询所有的tid信息
        :param request:
        :return:
        """
        inbound_detail_codes = request.query_params.get("inbound_detail_codes")
        if not inbound_detail_codes:
            raise APIViewException

        cleaned_codes = set([inbound_detail_code for inbound_detail_code in inbound_detail_codes.split(",") if inbound_detail_code])
        inventories = ERPSKUInventory.objects.filter(
            pk__in=cleaned_codes,
            status=1,
            is_deleted=False,
            in_warehouse_quantity__gt=0,
        ).values("sku__sku_id", "tid_info")

        re_data = []
        for inventory in inventories:
            sku_id = inventory["sku__sku_id"]
            tid_info = inventory["tid_info"]

            tid_prefix = decimal_to_base62(sku_id)

            for tid, info in tid_info.items():
                if info["verify_status"] in ERPSKUInventory.in_warehouse_status:
                    re_data.append(f"{tid_prefix}{tid}")

        return IResponse(data={"tids": re_data})


class InventoryChangeDetailsViewSet(SPModelViewSet):
    """
    库存查询视图集

    提供库存查询和RFID找货功能
    """

    serializer_class = SupplierSKUInventoryChangeDetailsListSer
    filterset_class = SupplierInventoryChangeDetailsFilter
    force_order = False

    def get_queryset(self):
        """
        获取当前用户公司的库存列表
        """
        return InventoryChangeDetails.objects.filter(is_deleted=False, company=self.current_user.company).order_by("-update_date")

    @action(detail=False, methods=["get"])
    def query(self, request: Request):
        sku_id = request.query_params.get("sku_id")
        if not sku_id:
            return IResponse(code=400, message="sku_id参数必传且不为空")
        return super().list(request)
