# -*- coding: utf-8 -*-
import copy
from typing import Type

from common.basics.exceptions import APIViewException
from common.basics.viewsets import SPModelViewSet
from common.utils import diff_models
from django.db import transaction
from django.utils import timezone
from erp_products.models import ERPProductSKUTID
from erp_products.utils import decimal_to_base62
from erp_purchase.filters.inventory_adjustment_order_filter import InventoryAdjustmentOrderFilter
from erp_purchase.models import ERPSKUInventory, InboundOrderDetail, InventoryAdjustmentOrder, InventoryChangeDetails
from erp_purchase.serializers.inventory_adjustment_order_serializers import (
    InventoryAdjustmentOrderCreateSerializer,
    InventoryAdjustmentOrderInfoSerializer,
    InventoryAdjustmentOrderListSerializer,
    InventoryAdjustmentOrderUpdateSerializer,
)
from rest_framework.decorators import action
from rest_framework.request import Request
from rest_framework.serializers import Serializer
from utils.http_handle import FieldsError, IResponse


class InventoryAdjustmentOrderViewSet(SPModelViewSet):
    fronted_page = "库存调整"
    resource_name = "库存调整"
    lookup_field = "code"
    serializer_class = InventoryAdjustmentOrderListSerializer
    filterset_class = InventoryAdjustmentOrderFilter

    def get_queryset(self):
        return (
            InventoryAdjustmentOrder.objects.filter(
                company=self.current_user.company,
                is_deleted=False,
            )
            .prefetch_related("warehouse", "warehouse_position")
            .order_by("-id")
        )

    def get_serializer_class(self) -> Type[Serializer]:
        if self.action == "create":
            return InventoryAdjustmentOrderCreateSerializer
        elif self.action == "update":
            return InventoryAdjustmentOrderUpdateSerializer
        elif self.action == "retrieve":
            return InventoryAdjustmentOrderInfoSerializer

        return self.serializer_class

    def create(self, request, *args, **kwargs):
        ser = self.get_serializer(data=request.data, context={"request": request})
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        instance = ser.save()

        self.log_operation(
            request,
            InventoryAdjustmentOrder,
            describe=f"新增调整订单:{instance.code}",
            resource_id=instance.code,
            is_success_input=True,
        )

        return IResponse(data={"code": instance.code})

    @action(detail=True, methods=["delete"])
    def cancel(self, request: Request, *args, **kwargs):
        instance = self.get_object()
        if instance.approve_status == 2:
            raise APIViewException(err_message="调整单已审批，无法取消")

        if instance.status == 2:
            raise APIViewException(err_message="调整单已取消，无法取消")

        instance.status = 2
        instance.update_user = self.current_user.user_id
        instance.save()

        self.log_operation(
            request,
            InventoryAdjustmentOrder,
            describe=f"取消调整订单:{instance.code}",
            resource_id=instance.code,
            is_success_input=True,
        )
        return IResponse()

    @action(detail=True, methods=["post"])
    def approve(self, request: Request, *args, **kwargs):
        instance = self.get_object()
        if instance.approve_status == 2:
            raise APIViewException(err_message="调整单已审批，无法审批")
        if instance.status == 2:
            raise APIViewException(err_message="调整单已取消，无法审批")

        def create_inventory_and_log(**kwargs):
            """
            创建库存单核对应的流水
            :param kwargs:
            :return:
            """
            inventory = ERPSKUInventory.objects.create(**kwargs)
            InventoryChangeDetails.create_detail(
                InventoryChangeDetails.CHANGE_TYPE_3,
                kwargs.get("from_order_detail_id"),
                force_insert=True,
                **{
                    "inventory_id": inventory.id,
                    "company_id": self.current_user.company.company_id,
                    "sku_id": inventory.sku_id,
                    "warehouse_id": inventory.warehouse_id,
                    "warehouse_position_id": inventory.warehouse_position_id,
                    "quantity": kwargs["quantity"],
                    "before_quantity": kwargs.get("before_quantity", 0),
                    "after_quantity": kwargs.get("after_quantity", kwargs["quantity"]),
                    "create_user": self.current_user.user_id,
                    "supplier_id": getattr(inventory, "supplier_id", None),
                    "remark": "库存调整",
                },
            )
            return inventory

        with transaction.atomic():
            instance.approve_status = 2
            instance.approve_user = self.current_user.user_id
            instance.approve_date = timezone.now()
            instance.update_user = self.current_user.user_id
            instance.save()

            all_details = instance.inventoryadjustmentorderdetail_set.filter(is_deleted=False)
            for detail in all_details:
                sku = detail.sku

                # 普通标签处理
                if detail.tag_type == 2:
                    if detail.adjustment_type == 1:
                        # 增仓的时候，直接添加库存和流水
                        inventory = create_inventory_and_log(
                            sku=detail.sku,
                            warehouse_id=instance.warehouse_id,
                            warehouse_position_id=instance.warehouse_position_id,
                            status=1,
                            from_order_id=instance.id,
                            from_order_detail_id=detail.id,
                            order_type=7,
                            tag_type=detail.tag_type,
                            cost_price=detail.cost_price,
                            label_price=detail.label_price,
                            market_price=detail.market_price,
                            purchase_price=detail.purchase_price,
                            quantity=detail.adjustment_quantity,
                            company=self.current_user.company,
                            create_user=self.current_user.user_id,
                            update_user=self.current_user.user_id,
                        )
                        # 记录关联关系
                        detail.sku_inventory_relates = {inventory.id: {"qty": float(detail.adjustment_quantity)}}
                        detail.save()
                    elif detail.adjustment_type == 2:
                        # 减仓，先进先出处理
                        inventories = ERPSKUInventory.objects.filter(
                            sku=detail.sku,
                            status=1,
                            is_deleted=False,
                            warehouse_id=instance.warehouse_id,
                            warehouse_position_id=instance.warehouse_position_id,
                            in_warehouse_quantity__gt=0,
                        ).order_by("id")

                        if instance.warehouse_position_id:
                            inventories


                        if not inventories:
                            raise APIViewException(err_message=f"库存不足: {sku.name}")

                        # 可分配数量
                        remain_quantity = detail.adjustment_quantity

                        #
                        for inventory in inventories:
                            if remain_quantity <= 0:
                                break

                            old_qty = inventory.in_warehouse_quantity

                            # 在仓数量大于等于可分配数量
                            if old_qty >= remain_quantity:
                                adjust_qty = remain_quantity
                                inventory.stock_transfer_quantity -= remain_quantity
                                inventory.save()
                                # 剩余是0
                                remain_quantity = 0
                                # 生成流水
                                InventoryChangeDetails.create_detail(
                                    InventoryChangeDetails.CHANGE_TYPE_3,
                                    detail.id,
                                    force_insert=True,
                                    **{
                                        "inventory_id": inventory.id,
                                        "company_id": self.current_user.company.company_id,
                                        "sku_id": inventory.sku_id,
                                        "warehouse_id": inventory.warehouse_id,
                                        "warehouse_position_id": inventory.warehouse_position_id,
                                        "quantity": -adjust_qty,
                                        "before_quantity": old_qty,
                                        "after_quantity": inventory.in_warehouse_quantity,
                                        "create_user": self.current_user.user_id,
                                        "supplier_id": getattr(inventory, "supplier_id", None),
                                        "remark": "库存调整",
                                    },
                                )
                                # 记录关联关系
                                detail.sku_inventory_relates.update({inventory.id: {"qty": -float(adjust_qty)}})
                                detail.save()
                                break
                            else:
                                # 小于在仓的时候，调整的数量 = 在仓数量
                                adjust_qty = old_qty
                                remain_quantity -= old_qty
                                inventory.stock_transfer_quantity -= old_qty
                                inventory.save()

                                # 增加流水
                                InventoryChangeDetails.create_detail(
                                    InventoryChangeDetails.CHANGE_TYPE_3,
                                    detail.id,
                                    force_insert=True,
                                    **{
                                        "inventory_id": inventory.id,
                                        "company_id": self.current_user.company.company_id,
                                        "sku_id": inventory.sku_id,
                                        "warehouse_id": inventory.warehouse_id,
                                        "warehouse_position_id": inventory.warehouse_position_id,
                                        "quantity": -adjust_qty,
                                        "before_quantity": old_qty,
                                        "after_quantity": inventory.in_warehouse_quantity,
                                        "create_user": self.current_user.user_id,
                                        "supplier_id": getattr(inventory, "supplier_id", None),
                                        "remark": "库存调整",
                                    },
                                )

                                detail.sku_inventory_relates.update({inventory.id: {"qty": -float(adjust_qty)}})

                        if remain_quantity > 0:
                            raise APIViewException(err_message=f"库存不足: {sku.name}")

                        # 保存关联关系
                        detail.save()
                elif detail.tag_type == 1:
                    if detail.adjustment_type == 1:
                        # 新增tid
                        tid_infos = ERPProductSKUTID.batch_generate_tid_info(int(detail.adjustment_quantity))
                        created_tids = list(tid_infos.keys())

                        # 默认未打印已审核
                        new_tid_info = InboundOrderDetail.batch_generate_tid_info(
                            created_tids,
                            "adjustment",
                            str(self.current_user.user_id),
                            "unprint",
                            "verified",
                        )

                        # 创建明细和流水
                        inventory = create_inventory_and_log(
                            sku=detail.sku,
                            warehouse_id=instance.warehouse_id,
                            warehouse_position_id=instance.warehouse_position_id,
                            status=1,
                            from_order_id=instance.id,
                            from_order_detail_id=detail.id,
                            order_type=7,
                            tag_type=detail.tag_type,
                            cost_price=detail.cost_price,
                            label_price=detail.label_price,
                            market_price=detail.market_price,
                            purchase_price=detail.purchase_price,
                            quantity=detail.adjustment_quantity,
                            company=self.current_user.company,
                            create_user=self.current_user.user_id,
                            update_user=self.current_user.user_id,
                            tid_info=new_tid_info,
                        )

                        # 记录关联关系, 保存新增的tid信息
                        detail.tid_info = {created_tid: {"iid": inventory.id} for created_tid in created_tids}
                        detail.sku_inventory_relates = {inventory.id: {"qty": float(detail.adjustment_quantity)}}
                        detail.save()

                    elif detail.adjustment_type == 2:
                        # 减仓
                        tid_info = detail.tid_info
                        #
                        erp_inventory_pk_list = [v["iid"] for v in tid_info.values()]
                        detail_inventories = ERPSKUInventory.objects.filter(
                            pk__in=erp_inventory_pk_list,
                            status=1,
                            is_deleted=False,
                        )

                        # 再次校验库存
                        detail_inventories_map = {v.id: v for v in detail_inventories}
                        old_qty_map = {v.id: v.in_warehouse_quantity for v in detail_inventories}
                        encrypt_tid_prefix = decimal_to_base62(sku.sku_id)
                        need_update_inventories = set()

                        # 判断tid信息
                        for tid, info in tid_info.items():
                            iid = info["iid"]

                            if iid not in detail_inventories_map:
                                raise APIViewException(err_message=f"库存信息错误: iid={iid}")

                            #
                            inventory = detail_inventories_map[iid]
                            if tid not in inventory.tid_info:
                                raise APIViewException(err_message=f"标签{encrypt_tid_prefix}{tid}信息错误")
                            #
                            if inventory.tid_info[tid]["verify_status"] not in ERPSKUInventory.in_warehouse_status:
                                raise APIViewException(err_message=f"标签{encrypt_tid_prefix}{tid}{ERPSKUInventory.get_tid_verify_status_display(tid)}")

                            # 设置盘亏状态、调整
                            inventory.set_tid_loss_adjusted_status(tid, self.current_user.user_id, remark="库存调整")
                            inventory.save()

                            # 需要更新的，只产生一个流水
                            need_update_inventories.add(inventory)

                        # 记录关联关系
                        tmp_relates = {}
                        for k, v in tid_info.items():
                            iid = v["iid"]

                            # 负数，减法
                            if iid not in tmp_relates:
                                tmp_relates[iid] = {"qty": -1}
                            else:
                                tmp_relates[iid]["qty"] -= 1

                        detail.sku_inventory_relates = tmp_relates
                        detail.save()

                        for inv in need_update_inventories:
                            before_quantity = old_qty_map[inv.id]
                            adjust_quantity = inv.in_warehouse_quantity - before_quantity
                            after_quantity = inv.in_warehouse_quantity

                            InventoryChangeDetails.create_detail(
                                InventoryChangeDetails.CHANGE_TYPE_3,
                                detail.id,
                                force_insert=True,
                                **{
                                    "inventory_id": inv.id,
                                    "company_id": self.current_user.company.company_id,
                                    "sku_id": inv.sku_id,
                                    "warehouse_id": inv.warehouse_id,
                                    "warehouse_position_id": inv.warehouse_position_id,
                                    "quantity": adjust_quantity,
                                    "before_quantity": before_quantity,
                                    "after_quantity": after_quantity,
                                    "create_user": self.current_user.user_id,
                                    "supplier_id": getattr(inv, "supplier_id", None),
                                    "remark": "库存调整",
                                },
                            )

        self.log_operation(
            request,
            InventoryAdjustmentOrder,
            describe=f"审批调整订单:{instance.code}",
            resource_id=instance.code,
            is_success_input=True,
        )
        return IResponse()

    def update(self, request, *args, **kwargs):
        """
        修改备注信息
        :param request:
        :param args:
        :param kwargs:
        :return:
        """

        if request.method == "PATCH":
            instance = self.get_object()

            old_instance = copy.deepcopy(instance)

            post_data = request.data

            remark = post_data.get("remark")
            remark_images = post_data.get("remark_images")

            instance.remark = remark
            instance.remark_images = remark_images
            instance.update_user = self.current_user.user_id
            instance.save()
            self.log_operation(
                request,
                InventoryAdjustmentOrder,
                describe=f"修改调整订单备注:{instance.code}",
                operate_content=diff_models(old_instance, instance),
                resource_id=instance.code,
                is_success_input=True,
            )
            return IResponse()
        else:
            instance = self.get_object()
            old_instance = copy.deepcopy(instance)

            ser = self.get_serializer(
                instance,
                data=request.data,
                context={"request": request},
            )
            if not ser.is_valid():
                raise FieldsError(ser.errors)
            instance = ser.save()

            self.log_operation(
                request,
                InventoryAdjustmentOrder,
                describe=f"修改调整订单:{instance.code}",
                operate_content=diff_models(old_instance, instance),
                resource_id=instance.code,
                is_success_input=True,
            )
            return IResponse()
