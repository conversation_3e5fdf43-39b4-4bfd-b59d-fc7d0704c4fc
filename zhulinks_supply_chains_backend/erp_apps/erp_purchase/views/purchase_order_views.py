# -*- coding: utf-8 -*-
import copy
import decimal
from copy import deepcopy
from datetime import datetime
from urllib.parse import quote

from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.db.models import Count, Q, Prefetch
from django.http import HttpResponse
from django.utils import timezone
from rest_framework.request import Request

from common.basic import OPAPIView
from common.basics.views import OperateLogAPIViewMixin
from common.basics.exceptions import APIViewException, DataNotFoundException
from common.models import OperationLog
from common.utils import diff_models
from erp_products.models import ERPStockKeepingUnit
from erp_purchase.filters import PurchaseOrderFilter
from erp_purchase.filters.operation_log_filter import OperationLogFilter
from erp_purchase.filters.order_filter import ERPPurchaseOrderProductDetailFilter
from erp_purchase.models import PurchaseOrders, PurchaseOrderDetail, PurchaseReceiptLabel, ErpDeliveryAddress
from erp_purchase.resources import PurchaseOrderDetailDownloadResource
from erp_purchase.serializers.operation_log_serializers import OperationLogSer
from erp_purchase.serializers.purchase_order_detail_serializers import ERPPurchaseOrderDetailUpdateSer
from erp_purchase.serializers.purchase_order_serializers import (
    PurchaseOrderCreateOrUpdateSer,
    PurchaseOrderListSer,
    PurchaseOrderPatchUpdateSer,
    PurchaseOrderDetailInfoSer,
    ERPPurchaseProductDetailListSer,
)
from utils.http_handle import FieldsError, IResponse, custom_django_filter


class PurchaseOrderView(OPAPIView, OperateLogAPIViewMixin):
    resource_name = "采购单"
    fronted_page = "采购列表"
    need_format_resource_name = True

    ACTION_MAPPING = {
        "approve": "_handle_approve",
        "cancel_approve": "_handle_cancel_approve",
        "complete": "_handle_complete",
        "cancel_complete": "_handle_cancel_complete",
        "invalid": "_handle_invalid",
    }

    def get(self, request: Request):
        purchase_orders = PurchaseOrders.objects.filter(is_deleted=False).order_by("-update_date")

        re_data, _, _ = custom_django_filter(
            request,
            purchase_orders,
            PurchaseOrderFilter,
            PurchaseOrderListSer,
            force_order=False,
        )
        return IResponse(data=re_data)

    def post(self, request: Request):
        post_data = request.data
        post_data["create_user"] = self.current_user.user_id
        ser = PurchaseOrderCreateOrUpdateSer(data=post_data)
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        instance = ser.save()
        # 更新统计数据
        instance.update_product_quantity_and_amount()
        # 日志记录

        # 如果是确认状态，代表已经审核
        operate_content = f"新增并审核采购单: {instance.code}" if instance.order_status == 2 else f"新增采购单: {instance.code}"

        self.log_operation(
            request,
            resource_id=instance.code,
            model=PurchaseOrders,
            describe="新增采购单",
            operate_content=operate_content,
            is_success_input=True,
        )

        return IResponse()

    def patch(self, request: Request):
        """
        采购单操作
        :param request:
        :return:
        """
        action = request.data.get("action")
        if not action:
            raise APIViewException(err_message="action is required")
        handler = self.ACTION_MAPPING.get(action)

        if not handler or not hasattr(self, handler):
            raise APIViewException

        return getattr(self, handler)(request)

    def delete(self, request: Request):
        order_codes = request.data.get("codes", [])
        if not order_codes:
            raise APIViewException(err_message="pls select orders")
        # 删除订单, 作废的订单才能删除
        orders = PurchaseOrders.objects.filter(
            code__in=order_codes,
            is_deleted=False,
            order_status=4,
        ).only("id", "code", "order_status")
        if not orders:
            raise APIViewException(err_message="only voided purchase orders can be deleted")
        need_log_codes = [o.code for o in orders]
        # 更新状态, 置空审核信息
        orders.update(
            is_deleted=True,
            update_date=timezone.now(),
            update_user=self.current_user.user_id,
        )

        # 日志记录，需要到每个采购单下面
        for order_code in need_log_codes:
            self.log_operation(
                request,
                resource_id=order_code,
                model=PurchaseOrders,
                describe="删除采购单",
                operate_content=f"删除采购单：{order_code}",
                is_success_input=True,
            )
        return IResponse()

    def _handle_approve(self, request):
        """
        审核采购单
        :param request:
        :return:
        """
        order_codes = request.data.get("codes", [])
        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        # 需要审核的订单
        orders = (
            PurchaseOrders.objects.filter(
                code__in=order_codes,
                is_deleted=False,
                order_status=1,
            )
            .annotate(count=Count("purchaseorderdetail", filter=Q(purchaseorderdetail__is_deleted=False)))
            .values("id", "code", "order_status", "count")
        )
        if not orders:
            raise APIViewException(err_message="no purchase order need to approve")

        can_approve_codes = []
        for order in orders:
            if (order["count"] or 0) > 0:
                can_approve_codes.append(order["code"])

        if not can_approve_codes:
            raise APIViewException(err_message="采购单:{}，无采购商品，无法审核生效".format("、".join(order_codes)))

        # 更新状态
        PurchaseOrders.objects.filter(code__in=can_approve_codes).update(
            order_status=2,
            approve_user=self.current_user.user_id,
            approve_date=timezone.now(),
            update_date=timezone.now(),
            update_user=self.current_user.user_id,
        )
        # 日志记录，需要到每个采购单下面
        for order_code in can_approve_codes:
            self.log_operation(
                request,
                resource_id=order_code,
                model=PurchaseOrders,
                describe="审核采购单",
                operate_content=f"审核采购单:{order_code}",
                is_success_input=True,
            )

        return IResponse()

    def _handle_cancel_approve(self, request):
        """
        取消审核
        :param request:
        :return:
        """
        order_codes = request.data.get("codes", [])
        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        # 需要取消审核的订单
        orders = PurchaseOrders.objects.filter(
            code__in=order_codes,
            is_deleted=False,
            order_status=2,
        ).only("id", "code", "order_status")
        if not orders:
            raise APIViewException(err_message="no purchase order need to approve")

        need_log_codes = [o.code for o in orders]
        # 更新状态, 置空审核信息
        orders.update(
            order_status=1,
            approve_user=None,
            approve_date=None,
            update_date=timezone.now(),
            update_user=self.current_user.user_id,
        )

        # 日志记录，需要到每个采购单下面
        for order_code in need_log_codes:
            self.log_operation(
                request,
                resource_id=order_code,
                model=PurchaseOrders,
                describe="取消审核采购单",
                operate_content=f"取消审核采购单: {order_code}",
                is_success_input=True,
            )

        return IResponse()

    def _handle_complete(self, request):
        """
        完成逻辑
        :param request:
        :return:
        """
        order_codes = request.data.get("codes", [])

        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        # 点击完成的采购单, 只有已确认的才有效
        orders = PurchaseOrders.objects.filter(
            code__in=order_codes,
            is_deleted=False,
            order_status=2,
        ).only("id", "code", "order_status")
        if not orders:
            raise APIViewException(err_message="no purchase order need to approve")
        need_log_codes = [o.code for o in orders]
        # 更新状态, 置空审核信息, 单据状态变成完成
        orders.update(
            order_status=3,
            finish_date=timezone.now(),
            confirm_finish_user=self.current_user.user_id,
            update_date=timezone.now(),
            update_user=self.current_user.user_id,
        )

        # 日志记录，需要到每个采购单下面
        for order_code in need_log_codes:
            self.log_operation(
                request,
                resource_id=order_code,
                model=PurchaseOrders,
                describe="完成采购单",
                operate_content=f"完成采购单:{order_code}",
                is_success_input=True,
            )

        return IResponse()

    def _handle_cancel_complete(self, request):
        """
        完成/取消完成逻辑
        :param request:
        :return:
        """
        order_codes = request.data.get("codes", [])

        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        orders = PurchaseOrders.objects.filter(
            code__in=order_codes,
            is_deleted=False,
            order_status=3,
        ).only("id", "code", "order_status")
        if not orders:
            raise APIViewException(err_message="no purchase order need to approve")
        need_log_codes = [o.code for o in orders]
        # 更新状态, 取消确认
        orders.update(
            order_status=2,
            finish_date=None,
            confirm_finish_user=None,
            update_date=timezone.now(),
            update_user=self.current_user.user_id,
        )

        # 日志记录，需要到每个采购单下面
        for order_code in need_log_codes:
            self.log_operation(
                request,
                resource_id=order_code,
                model=PurchaseOrders,
                describe="取消完成采购单",
                operate_content=f"取消完成采购单:{order_code}",
                is_success_input=True,
            )

        return IResponse()

    def _handle_invalid(self, request):
        """
        作废/删除逻辑
        :param request:
        :return:
        """
        order_codes = request.data.get("codes", [])
        # sub_action_type = request.data.get("sub_action")

        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        # 只有待审核才能作废,
        orders = PurchaseOrders.objects.filter(
            code__in=order_codes,
            is_deleted=False,
            order_status=1,
        ).only("id", "code", "order_status")
        if not orders:
            raise APIViewException(err_message="no purchase order need to approve")
        need_log_codes = [o.code for o in orders]
        # 更新状态, 置空审核信息, 订单状态改成作废
        orders.update(
            order_status=4,
            finish_date=timezone.now(),
            update_date=timezone.now(),
            update_user=self.current_user.user_id,
        )

        # 日志记录，需要到每个采购单下面
        for order_code in need_log_codes:
            self.log_operation(
                request,
                resource_id=order_code,
                model=PurchaseOrders,
                describe="作废采购单",
                operate_content=f"作废采购单:{order_code}",
                is_success_input=True,
            )
        return IResponse()


class PurchaseOrderDetailView(OPAPIView, OperateLogAPIViewMixin):
    resource_name = "采购单"
    fronted_page = "采购列表"
    need_format_resource_name = True

    @staticmethod
    def _get_object(purchase_order_code) -> PurchaseOrders:
        try:
            purchase_order = PurchaseOrders.objects.get(code=purchase_order_code)
            return purchase_order
        except PurchaseOrders.DoesNotExist:
            raise DataNotFoundException

    def get(self, request: Request, purchase_order_code: str):
        """
        采购单详情
        :param request:
        :param purchase_order_code:
        :return:
        """
        order = self._get_object(purchase_order_code)
        ser = PurchaseOrderDetailInfoSer(instance=order, many=False)
        return IResponse(data=ser.data)

    def put(self, request: Request, purchase_order_code: str):
        """
        采购单全量编辑
        :param request:
        :param purchase_order_code:
        :return:
        """
        order = self._get_object(purchase_order_code)
        if order.order_type != 1:
            raise APIViewException(err_message="editable only in pending status")

        raw_obj = copy.deepcopy(order)
        post_data = request.data
        post_data["update_user"] = self.current_user.user_id
        update_ser = PurchaseOrderCreateOrUpdateSer(
            instance=order,
            data=post_data,
            many=False,
            partial=True,
        )
        if not update_ser.is_valid():
            raise FieldsError(update_ser.errors)
        updated_obj = update_ser.save()
        # 日志记录
        self.log_operation(
            request,
            resource_id=order.code,
            model=PurchaseOrders,
            describe="编辑采购单",
            operate_content=diff_models(raw_obj, updated_obj),
            is_success_input=True,
        )

        return IResponse()

    def patch(self, request: Request, purchase_order_code: str):
        """
        修改备注、供应商；
        不是待审核的时候，无法修改供应商信息
        :param request:
        :param purchase_order_code:
        :return:
        """
        order = self._get_object(purchase_order_code)
        post_data = request.data
        raw_obj = deepcopy(order)
        post_data["update_user"] = self.current_user
        ser = PurchaseOrderPatchUpdateSer(instance=order, data=post_data, partial=True)
        ser.is_valid(raise_exception=True)
        updated_obj = ser.save()
        # 日志记录
        self.log_operation(
            request,
            resource_id=order.code,
            model=PurchaseOrders,
            describe="编辑采购单部分信息",
            operate_content=diff_models(raw_obj, updated_obj),
            is_success_input=True,
        )
        return IResponse()


class PurchaseOrderLogView(OPAPIView):
    def get(self, request: Request, purchase_order_code: str):
        content_type = ContentType.objects.get_for_model(PurchaseOrders)
        operation_logs = OperationLog.objects.filter(
            content_type=content_type,
            resource_id=purchase_order_code,
        ).order_by("-operation_time")
        re_data, _, _ = custom_django_filter(
            request,
            operation_logs,
            OperationLogFilter,
            OperationLogSer,
            force_order=False,
        )
        return IResponse(data=re_data)


class PurchaseOrderDownloadView(OPAPIView):
    def get(self, request: Request):
        purchase_orders = PurchaseOrders.objects.filter(is_deleted=False).order_by("-update_date").only("code")
        _, page_obj_queryset, _ = custom_django_filter(
            request,
            purchase_orders,
            PurchaseOrderFilter,
            need_serialize=False,
            force_order=False,
        )

        # 预取关联数据（保持原有预取逻辑）
        label_prefetch = Prefetch("purchase_order__labels", PurchaseReceiptLabel.objects.filter(is_deleted=False))
        # Convert QuerySet to list to enable appending
        order_details = list(
            PurchaseOrderDetail.objects.prefetch_related(
                "purchase_order",
                "purchase_order__company",
                label_prefetch,
                "purchase_order__delivery_address",
                "purchase_order__warehouse",
            ).filter(purchase_order__in=page_obj_queryset)
        )

        # 按分页顺序处理采购单
        existing_order_map = {d.purchase_order_id: d for d in order_details}
        final_details = []

        # 按分页顺序遍历采购单
        for order in page_obj_queryset:
            # 如果存在明细则直接添加
            if order.id in existing_order_map:
                final_details.append(existing_order_map[order.id])
            # 没有明细时创建虚拟记录
            else:
                final_details.append(
                    PurchaseOrderDetail(
                        purchase_order=order,
                        detail_code="",
                        id=None,
                        erp_sku=None,
                        cost_price=None,
                        quantity=None,
                        amount=None,
                    )
                )

        # 初始化导出资源时使用最终处理后的列表
        dataset = PurchaseOrderDetailDownloadResource().export(queryset=final_details)
        xls_data = dataset.export("xlsx")
        response = HttpResponse(xls_data, content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chinese_prefix = f"采购单导出_{timestamp}.xlsx"
        encoded_filename = quote(chinese_prefix)
        response["Content-Disposition"] = f"attachment; filename*=UTF-8''{encoded_filename}"
        return response


class PurchaseOrderProductDetailView(OPAPIView, OperateLogAPIViewMixin):
    resource_name = "采购明细"
    fronted_page = "采购列表"
    need_format_resource_name = True

    @staticmethod
    def _get_object(purchase_order_code) -> PurchaseOrders:
        try:
            purchase_order = PurchaseOrders.objects.get(code=purchase_order_code)
            if purchase_order.order_status != 1:
                raise APIViewException(err_message="editable only in pending status")
            return purchase_order
        except PurchaseOrders.DoesNotExist:
            raise DataNotFoundException

    def get(self, request: Request, purchase_order_code: str):
        order = self._get_object(purchase_order_code)

        order_details = order.purchaseorderdetail_set.prefetch_related(
            "erp_sku",
            "erp_sku__product",
        ).filter(is_deleted=False)
        re_data, _, _ = custom_django_filter(
            request,
            order_details,
            ERPPurchaseOrderProductDetailFilter,
            ERPPurchaseProductDetailListSer,
        )
        return IResponse(data=re_data)

    def patch(self, request: Request, purchase_order_code: str):
        order = self._get_object(purchase_order_code)

        post_data = request.data
        detail_code = post_data.get("detail_code")
        if not detail_code:
            raise APIViewException(err_message="Invalid purchase detail codes")

        try:
            order_detail = order.purchaseorderdetail_set.get(detail_code=detail_code, is_deleted=False)
        except PurchaseOrderDetail.DoesNotExist:
            raise DataNotFoundException

        raw_detail = copy.deepcopy(order_detail)
        post_data["update_user"] = self.current_user.user_id
        update_ser = ERPPurchaseOrderDetailUpdateSer(instance=order_detail, data=post_data, many=False)
        if not update_ser.is_valid():
            raise FieldsError(update_ser.errors)

        with transaction.atomic():
            updated_obj = update_ser.save()
            # 更新统计数据
            order.update_product_quantity_and_amount()

        # 日志记录
        self.log_operation(
            request,
            resource_id=order.code,
            model=PurchaseOrders,
            describe=f"编辑采购单明细，明细单号:{detail_code}",
            operate_content=diff_models(raw_detail, updated_obj),
            is_success_input=True,
        )

        return IResponse()

    def delete(self, request: Request, purchase_order_code: str):
        detail_codes = request.data.get("detail_codes")
        if not detail_codes or not isinstance(detail_codes, list):
            raise APIViewException
        purchase_order = self._get_object(purchase_order_code)

        purchase_order_details = purchase_order.purchaseorderdetail_set.filter(is_deleted=False, detail_code__in=detail_codes)
        if len(purchase_order_details) != len(detail_codes):
            raise APIViewException(err_message="Invalid purchase detail codes")

        purchase_order_details.update(is_deleted=True, update_user=self.current_user.user_id, update_date=timezone.now())
        # 更新统计数据
        purchase_order.update_product_quantity_and_amount()
        # 日志记录
        self.log_operation(
            request,
            resource_id=purchase_order_code,
            model=PurchaseOrders,
            describe="删除采购单明细商品",
            operate_content="明细单号：" + "、".join(detail_codes),
            is_success_input=True,
        )
        return IResponse()

    def post(self, request: Request, purchase_order_code: str):
        purchase_order = self._get_object(purchase_order_code)
        purchase_detail_list = request.data.get("purchase_detail")
        if not purchase_detail_list or not isinstance(purchase_detail_list, list):
            raise APIViewException

        erp_sku_id_list = []
        for purchase_detail in purchase_detail_list:
            if "erp_sku_id" not in purchase_detail or "cost_price" not in purchase_detail or "quantity" not in purchase_detail:
                raise APIViewException(err_message="Invalid product info")
            erp_sku_id_list.append(purchase_detail["erp_sku_id"])

        conditions = {
            "sku_id__in": erp_sku_id_list,
            "become_history": False,
            "product__is_deleted": False,
        }

        if ERPStockKeepingUnit.objects.filter(**conditions).count() != len(erp_sku_id_list):
            raise APIViewException(err_message="Invalid product info")

        exist_erp_sku_id_list = purchase_order.purchaseorderdetail_set.filter(is_deleted=False).values_list("erp_sku_id", flat=True)

        bulk_created_objs = PurchaseOrderDetail.objects.bulk_create(
            [
                PurchaseOrderDetail(
                    purchase_order=purchase_order,
                    erp_sku_id=purchase_detail["erp_sku_id"],
                    cost_price=purchase_detail["cost_price"],
                    quantity=purchase_detail["quantity"],
                    amount=decimal.Decimal(purchase_detail["cost_price"]) * purchase_detail["quantity"],
                )
                for purchase_detail in purchase_detail_list
                if purchase_detail["erp_sku_id"] not in exist_erp_sku_id_list
            ],
        )

        # 更新统计数据
        purchase_order.update_product_quantity_and_amount()

        self.log_operation(
            request,
            resource_id=purchase_order_code,
            model=PurchaseOrders,
            describe="采购单增加明细商品",
            operate_content="明细单号：" + "、".join([created_obj.detail_code for created_obj in bulk_created_objs]),
            is_success_input=True,
        )
        return IResponse()


class PurchaseOrderCopyView(OPAPIView, OperateLogAPIViewMixin):
    resource_name = "采购单"
    fronted_page = "采购列表"
    need_format_resource_name = True

    @staticmethod
    def _get_object(purchase_order_code) -> PurchaseOrders:
        try:
            purchase_order = PurchaseOrders.objects.get(code=purchase_order_code)
            return purchase_order
        except PurchaseOrders.DoesNotExist:
            raise DataNotFoundException

    def post(self, request, purchase_order_code: str):
        original_order = self._get_object(purchase_order_code)

        with transaction.atomic():
            # 创建新的PurchaseOrder

            create_fields = [
                "order_type",
                "purchase_date",
                "company",
                "product_type",
                "warehouse",
                "pay_type",
                "payment_term_days",
                "remark",
            ]

            new_order_data = {
                field.name: getattr(
                    original_order,
                    field.name,
                )
                for field in original_order._meta.fields
                if field.name in create_fields
            }
            new_order_data["create_user"] = self.current_user.user_id
            new_order_data["purchase_user"] = self.current_user.user_id

            # 创建收货地址
            if original_order.delivery_address_id:
                old_delivery_address = original_order.delivery_address
                assert old_delivery_address is not None
                address_info = {
                    "receiver": old_delivery_address.receiver,
                    "receiver_mobile": old_delivery_address.receiver_mobile,
                    "receiver_province_id": old_delivery_address.receiver_province_id,
                    "receiver_city_id": old_delivery_address.receiver_city_id,
                    "receiver_town_id": old_delivery_address.receiver_town_id,
                    "receiver_address": old_delivery_address.receiver_address,
                    "create_user": self.current_user.user_id,
                }
                new_address = ErpDeliveryAddress.objects.create(**address_info)
                new_order_data["delivery_address_id"] = new_address.pk

            new_order = PurchaseOrders.objects.create(**new_order_data)

            # 复制PurchaseOrderDetail
            original_details = PurchaseOrderDetail.objects.filter(purchase_order=original_order, is_deleted=False)
            new_details = []
            new_data_fields = ["cost_price", "quantity", "amount"]

            for detail in original_details:
                new_detail_data = {field.name: getattr(detail, field.name) for field in detail._meta.fields if field.name in new_data_fields}
                new_detail_data["purchase_order"] = new_order
                new_detail_data["create_user"] = self.current_user.user_id
                # 处理外键字段
                new_detail_data["erp_sku"] = detail.erp_sku

                new_detail = PurchaseOrderDetail(**new_detail_data)
                new_details.append(new_detail)

            PurchaseOrderDetail.objects.bulk_create(new_details)

            # 处理多对多字段
            new_order.labels.add(*[label.id for label in new_order.labels.all()])

            # 更新商品数量、金额
            new_order.update_product_quantity_and_amount()

        # 日志记录
        operate_content = f"新增采购单: {new_order.code}"
        self.log_operation(
            request,
            resource_id=new_order.code,
            model=PurchaseOrders,
            describe="新增采购单",
            operate_content=operate_content,
            is_success_input=True,
        )

        return IResponse()
