# -*- coding: utf-8 -*-
import copy
import re
import uuid
from typing import Type

from common.basics.exceptions import APIViewException, DataNotFoundException
from common.basics.viewsets import SPModelViewSet
from common.models import DownloadTasksTypeChoices, OperationLog, get_inbound_excel_parse_config
from common.utils import diff_models
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import F, Func, Prefetch
from django.utils import timezone
from erp_apps import logger
from erp_orders.models import ERPOrderDetailInventoryRelates
from erp_produce.models import ProduceWork
from erp_products.models import ERPProductSKUTID, ERPSupplierProductSKU
from erp_products.utils import decimal_to_base62
from erp_purchase.filters import InboundOrderListFilter
from erp_purchase.filters.operation_log_filter import OperationLogFilter
from erp_purchase.filters.supplier_inventory_query_filter import SupplierInboundDetailFilter
from erp_purchase.models import (
    ERPOutboundOrderDetailInventoryRelate,
    ERPSKUInventory,
    InboundOrder,
    InboundOrderDetail,
    InventoryChangeDetails,
    InventoryAdjustmentOrderDetail,
)
from erp_purchase.serializers.inbound_order_serializers import (
    BatchInboundOrderCreateSerializer,
    InboundOrderUpdateSerializer,
    SupplierInboundOrderCreateSerializer,
    SupplierInboundOrderDetailSer,
    SupplierInboundOrderListSer,
    SupplierOtherInboundOrderCreateSerializer,
    SupplierQuickInboundOrderCreateSer,
)
from erp_purchase.serializers.operation_log_serializers import OperationLogSer
from erp_purchase.tasks import sync_inbound_details_to_data_analyze_tasks
from erp_purchase.tasks.async_download_tasks import async_download_inbound_order_task
from erp_purchase.tasks.inbound_order_tasks import revoke_oss_cloud_parse_excel_fc_task
from erp_transfer.models import TransferOrderDetailInventoryRelate
from oss2 import exceptions as oss_exceptions
from rest_framework.decorators import action
from rest_framework.serializers import Serializer
from stock_take.models import StockTakeOrderResultInboundOrderRelate
from stock_take.utils import tid_to_sku_map
from utils.erp_download_utils.erp_async_download_task import ERPAsyncDownloadTaskReceiver
from utils.http_handle import EmptyListResponse, FieldsError, IResponse, custom_django_filter
from utils.oss_v2 import get_oss_bucket_client, parse_oss_url


class InboundOrderViewSet(SPModelViewSet):
    """
    入库单视图集

    提供入库单的增删改查功能
    """

    fronted_page = "采购入库"
    resource_name = "入库单"
    lookup_field = "inbound_code"
    serializer_class = SupplierInboundOrderListSer
    filterset_class = InboundOrderListFilter
    ordering = ["-update_date"]

    def get_queryset(self):
        """
        获取当前用户公司的入库单列表
        销售退、借货退不需要显示在列表
        """
        return InboundOrder.objects.filter(company=self.current_user.company, is_deleted=False).exclude(order_type__in=[2, 3])

    def get_serializer_class(self) -> Type[Serializer]:
        if self.action == "create":
            return SupplierInboundOrderCreateSerializer
        elif self.action == "update":
            return InboundOrderUpdateSerializer
        return self.serializer_class

    @action(detail=False, methods=["get"])
    def code_generate(self, request):
        """
        获取预加载的入库单号（废弃）
        """
        company = self.current_user.company
        if company is None:
            raise APIViewException(err_message="Invalid company")
        company_id = company.company_id
        pre_inbound_code = InboundOrder.get_pre_inbound_code(company_id)
        data = {"inbound_code": pre_inbound_code}
        return IResponse(data=data)

    @action(detail=False, methods=["post"])
    def quick(self, request):
        """
        极速入库
        """
        company_id = self.current_user.company.company_id

        try:
            ser = SupplierQuickInboundOrderCreateSer(data=request.data, context={"request": request})
            if not ser.is_valid():
                raise FieldsError(ser.errors)

            inbound_order = ser.save()
            created = getattr(inbound_order, "__created__", False)
            product = getattr(inbound_order, "__product__", None)
            need_return_product_info = getattr(inbound_order, "__need_return_product_info__", [])
            if not created:
                desc = f"极速入库商品：{product}"
            else:
                desc = f"新增入库订单：{inbound_order.inbound_code}"

            self.log_operation(
                request,
                InboundOrder,
                describe=desc,
                operate_content="商品编码：{}".format("、".join([i.get("spec_code") for i in need_return_product_info if i.get("spec_code")])),
                resource_id=inbound_order.inbound_code,
                is_success_input=True,
            )

            return IResponse(data=need_return_product_info)
        except APIViewException as e:
            return IResponse(code=400, message=str(e))
        except FieldsError as e:
            return IResponse(code=400, message=str(e))
        except Exception as e:
            import traceback

            logger.warning(f"{e}{traceback.format_exc()}")
            return IResponse(code=400, message="failed create inbound order")
        finally:
            pass

    def retrieve(self, request, *args, **kwargs):
        """
        获取入库单详情
        """
        inbound_order = self.get_no_filter_object()

        inbound_order_details = (
            inbound_order.inboundorderdetail_set.filter(
                is_deleted=False,
            )
            .prefetch_related(
                "sku",
                "sku__product",
                "sku__unit",
                "sku__attr_value",
                "sku__attr_value__attr_key",
                "supplier",
                "inbound_order",
                "warehouse",
                "warehouse_position",
            )
            .order_by("id")
        )

        if request.query_params.get("no_pagination") == "1":
            request.query_params._mutable = True
            request.query_params["page_size"] = inbound_order_details.count()
            request.query_params._mutable = False

        re_data, *_ = custom_django_filter(
            request,
            inbound_order_details,
            SupplierInboundDetailFilter,
            SupplierInboundOrderDetailSer,
        )
        inbound_order_info = self.get_serializer(inbound_order).data
        re_data["inbound_order_info"] = inbound_order_info
        return IResponse(data=re_data)

    def update(self, request, *args, **kwargs):
        """
        编辑入库单
        """
        inbound_order = self.get_object()

        raw_instance = copy.deepcopy(inbound_order)
        if inbound_order.status == 2:
            raise APIViewException(err_message="Order has already canceled, cannot be edited")

        serializer = self.get_serializer(inbound_order, data=request.data, context={"request": request})
        if not serializer.is_valid():
            raise FieldsError(serializer.errors)

        new_instance = serializer.save()

        detail_update_content_list = getattr(new_instance, "__detail_update_content__", [])
        operate_content = ""
        order_change = diff_models(raw_instance, new_instance)

        if order_change:
            operate_content = f"单据修改: {order_change}"

        if detail_update_content_list:
            detail_update = "单据商品修改：{}".format("、".join(detail_update_content_list))
            if not operate_content:
                operate_content = detail_update
            else:
                operate_content = f"{operate_content}\n{detail_update}"

        self.log_operation(
            request,
            InboundOrder,
            describe=f"编辑{inbound_order.get_order_type_display()}单：{inbound_order.inbound_code}",
            resource_id=inbound_order.inbound_code,
            operate_content=operate_content,
            is_success_input=True,
        )

        return IResponse()

    def patch(self, request, *args, **kwargs):
        """
        批量审核
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        approve_action = request.data.get("action")
        if approve_action is None:
            raise APIViewException(err_message="action is required")

        action_map = {
            "approve": self._approve,
            "cancel_approve": self._cancel_approve,
        }
        if approve_action not in action_map:
            raise APIViewException

        return action_map[approve_action](request)

    def destroy(self, request, inbound_code=None, *args, **kwargs):
        inbound_order = self.get_no_filter_object()

        if inbound_order.exist_stack_order_record:
            raise APIViewException(err_message="inbound orders contain stock_take order record, cannot be deleted")

        inbound_order.is_deleted = True
        inbound_order.update_user = str(self.current_user.user_id)
        inbound_order.save()

        self.log_operation(
            request,
            InboundOrder,
            "删除入库订单",
            resource_id=inbound_code,
            is_success_input=True,
        )
        return IResponse()

    def _approve(self, request):
        """
        审核入库单
        """
        order_codes = request.data.get("inbound_codes", [])
        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        inbound_detail_prefetch = Prefetch("inboundorderdetail_set", InboundOrderDetail.objects.filter(is_deleted=False))

        inbound_orders = InboundOrder.objects.prefetch_related(inbound_detail_prefetch).filter(
            inbound_code__in=order_codes,
            company=self.current_user.company,
            is_deleted=False,
            approve_status=1,
        )
        if not inbound_orders:
            raise APIViewException(err_message="pls select valid orders")

        valid_inbound_pk_list = [io.pk for io in inbound_orders]
        valid_inbound_codes = [io.inbound_code for io in inbound_orders]

        for inbound_order in inbound_orders:
            # 已取消的跳过
            if inbound_order.status != 1:
                continue
            total_verified_rfid_quantity = 0
            details = inbound_order.inboundorderdetail_set.all()

            # 判断是否正在调整库存
            sku_pk_list = [i.sku_id for i in details]
            InventoryAdjustmentOrderDetail.judge_sku_is_adjusting(sku_pk_list)

            for detail in details:
                total_verified_rfid_quantity += detail.verified_rfid_quantity

                if detail.verified_rfid_quantity != detail.in_warehouse_quantity:
                    raise APIViewException(err_message="pls complete all labels verification first")

            if inbound_order.order_tag_type == 1 and total_verified_rfid_quantity == 0:
                raise APIViewException(err_message="pls complete the RFID verification on the mini program first")

        # 对应InventoryChangeDetails.change_type
        outbound_type2change_type = {
            1: InventoryChangeDetails.CHANGE_TYPE_11,
            # 2: InventoryChangeDetails.CHANGE_TYPE_12,
            4: InventoryChangeDetails.CHANGE_TYPE_16,
            5: InventoryChangeDetails.CHANGE_TYPE_17,
        }

        approve_inbound_codes = []
        with transaction.atomic():
            # 创建或更新库存查询记录
            need_create_inventory_objects = []
            need_update_inventory_objects = []

            for inbound_order in inbound_orders:
                inbound_order_details = inbound_order.inboundorderdetail_set.all()

                change_type = outbound_type2change_type[inbound_order.order_type]

                has_flag = False
                new_inventory_changes = []
                # 判断是否存在库存查询表的关联
                for inbound_detail in inbound_order_details:
                    quantity = inbound_detail.quantity
                    before_quantity = 0
                    # 检查是否已存在相应的记录
                    # 正常来说一个入库明细只有一个库存查询记录
                    # 多条记录的情况只有调拨，无法反审核
                    existing_inventory = ERPSKUInventory.objects.filter(
                        sku=inbound_detail.sku,
                        warehouse=inbound_detail.warehouse,
                        warehouse_position=inbound_detail.warehouse_position,
                        cost_price=inbound_detail.cost_price,
                        label_price=inbound_detail.label_price,
                        market_price=inbound_detail.market_price,
                        purchase_price=inbound_detail.purchase_price,
                        company=self.current_user.company,
                        inbound_order_detail=inbound_detail,
                    ).last()

                    if existing_inventory:
                        # 更新已存在的记录
                        existing_inventory.status = 1
                        existing_inventory.quantity = inbound_detail.quantity
                        existing_inventory.tag_type = inbound_detail.tag_type
                        existing_inventory.cost_price = inbound_detail.cost_price
                        existing_inventory.label_price = inbound_detail.label_price
                        existing_inventory.market_price = inbound_detail.market_price
                        existing_inventory.purchase_price = inbound_detail.purchase_price
                        existing_inventory.in_warehouse_quantity = inbound_detail.in_warehouse_quantity
                        existing_inventory.loaned_quantity = inbound_detail.loaned_quantity
                        existing_inventory.sold_quantity = inbound_detail.sold_quantity
                        existing_inventory.purchase_return_quantity = inbound_detail.purchase_return_quantity
                        existing_inventory.stock_transfer_quantity = inbound_detail.stock_transfer_quantity
                        existing_inventory.tid_info = inbound_detail.tid_info
                        existing_inventory.remark = inbound_detail.remark

                        existing_inventory.update_user = self.current_user.user_id
                        existing_inventory.update_date = timezone.now()
                        need_update_inventory_objects.append(existing_inventory)
                        if has_flag is False:
                            has_flag = True

                        before_quantity = existing_inventory.quantity

                        # 明细log
                        InventoryChangeDetails.create_detail(
                            change_type,
                            inbound_detail.id,
                            **{
                                "inventory_id": existing_inventory.id,
                                "company_id": self.current_user.company.company_id,
                                "sku_id": inbound_detail.sku.id,
                                "supplier_id": inbound_detail.supplier_id,
                                "warehouse_id": inbound_detail.warehouse.id,
                                "warehouse_position_id": None if not inbound_detail.warehouse_position else inbound_detail.warehouse_position.id,
                                "quantity": quantity,
                                "before_quantity": before_quantity,
                                "after_quantity": before_quantity + quantity,
                                "create_user": self.current_user.user_id,
                                "remark": inbound_order.get_order_type_display(),
                                "cost_price": inbound_detail.cost_price,
                            },
                        )
                    else:
                        # 创建新记录
                        if has_flag is False:
                            has_flag = True

                        need_create_inventory_objects.append(
                            ERPSKUInventory(
                                inbound_order=inbound_detail.inbound_order,
                                inbound_order_detail=inbound_detail,
                                purchase_type=inbound_detail.purchase_type,
                                sku=inbound_detail.sku,
                                warehouse=inbound_detail.warehouse,
                                warehouse_position=inbound_detail.warehouse_position,
                                status=1,
                                quantity=inbound_detail.quantity,
                                tag_type=inbound_detail.tag_type,
                                cost_price=inbound_detail.cost_price,
                                label_price=inbound_detail.label_price,
                                market_price=inbound_detail.market_price,
                                purchase_price=inbound_detail.purchase_price,
                                in_warehouse_quantity=inbound_detail.in_warehouse_quantity,
                                loaned_quantity=inbound_detail.loaned_quantity,
                                sold_quantity=inbound_detail.sold_quantity,
                                purchase_return_quantity=inbound_detail.purchase_return_quantity,
                                stock_transfer_quantity=inbound_detail.stock_transfer_quantity,
                                tid_info=inbound_detail.tid_info,
                                remark=inbound_detail.remark,
                                company=self.current_user.company,
                                supplier_id=inbound_detail.supplier_id,
                                # 入库信息
                                inbound_user=inbound_order.create_user,
                                inbound_date=inbound_order.create_date,
                                # 创建人，审核人信息
                                create_user=self.current_user.user_id,
                            )
                        )

                        new_inventory_changes.append(
                            {
                                "inbound_detail_id": inbound_detail.id,
                                "change_detail": {
                                    "company_id": self.current_user.company.company_id,
                                    "sku_id": inbound_detail.sku.id,
                                    "supplier_id": inbound_detail.supplier_id,
                                    "warehouse_id": inbound_detail.warehouse.id,
                                    "warehouse_position_id": None if not inbound_detail.warehouse_position else inbound_detail.warehouse_position.id,
                                    "quantity": quantity,
                                    "before_quantity": before_quantity,
                                    "after_quantity": before_quantity + quantity,
                                    "create_user": self.current_user.user_id,
                                    "remark": f"{inbound_order.get_order_type_display()}",
                                    "cost_price": inbound_detail.cost_price,
                                },
                            }
                        )

                approve_inbound_codes.append(inbound_order.inbound_code)

            # 批量更新已存在的库存查询记录
            if need_update_inventory_objects:
                ERPSKUInventory.objects.bulk_update(
                    need_update_inventory_objects,
                    [
                        "status",
                        "quantity",
                        "tag_type",
                        "cost_price",
                        "label_price",
                        "market_price",
                        "purchase_price",
                        "in_warehouse_quantity",
                        "loaned_quantity",
                        "sold_quantity",
                        "purchase_return_quantity",
                        "stock_transfer_quantity",
                        "tid_info",
                        "remark",
                        "inbound_user",
                        "inbound_date",
                    ],
                )

            # 批量创建新的库存查询记录
            if need_create_inventory_objects:
                new_inventory_datas = ERPSKUInventory.objects.bulk_create(need_create_inventory_objects)
                for index, inventory in enumerate(new_inventory_datas):
                    new_inventory_changes[index]["change_detail"]["inventory_id"] = inventory.id

                # 明细log
                for index, inventory_change in enumerate(new_inventory_changes):
                    InventoryChangeDetails.create_detail(
                        change_type,
                        new_inventory_changes[index]["inbound_detail_id"],
                        **new_inventory_changes[index]["change_detail"],
                    )

            # 生产管理-计划单
            for inbound_order in inbound_orders:
                if inbound_order.order_type == 4:
                    work = ProduceWork.objects.get(pk=inbound_order.from_order_id)
                    work.plan.inbound_approve(inbound_order.total_quantity)
                    work.plan.save()

            # TODO 更新状态, 完成后inbound_orders被置空
            inbound_orders.update(
                approve_status=2,
                approve_date=timezone.now(),
                approve_user=self.current_user.user_id,
            )

        # 触发推送任务
        for inbound_order_pk in valid_inbound_pk_list:
            sync_inbound_details_to_data_analyze_tasks.delay(inbound_order_pk)

        for approve_code in approve_inbound_codes:
            self.write_log(
                approve_code,
                self.resource_name,
                request,
                "审核入库单",
                self.current_user_type,
                ContentType.objects.get_for_model(InboundOrder).pk,
                fronted_page=self.fronted_page,
                operate_content="",
                user_mobile=self.current_user.mobile,
            )

        return IResponse()

    def _cancel_approve(self, request):
        """
        取消审核入库单
        """
        order_codes = request.data.get("inbound_codes", [])
        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        inbound_orders = InboundOrder.objects.filter(
            company=self.current_user.company,
            inbound_code__in=order_codes,
            is_deleted=False,
            approve_status=2,
        )
        if not inbound_orders:
            raise APIViewException(err_message="pls select valid orders")

        # 有盘点单关联 无法反审核
        if StockTakeOrderResultInboundOrderRelate.objects.filter(inbound_order__in=inbound_orders).exists():
            raise APIViewException(err_message="inbound orders contain stock_take order record, cannot be reserved")

        erp_sku_inventory_id_list = ERPSKUInventory.objects.filter(inbound_order__in=inbound_orders).values_list("id")

        # 有调拨单无法反审核
        if TransferOrderDetailInventoryRelate.objects.filter(
            sku_inventory_id__in=erp_sku_inventory_id_list,
            transfer_order__status=1,
        ).exists():
            raise APIViewException(err_message="inbound orders contain transfer order record, cannot be reserved")

        if ERPOrderDetailInventoryRelates.objects.filter(
            sku_inventory_id__in=erp_sku_inventory_id_list,
            erp_order__status=1,
        ).exists():
            raise APIViewException(err_message="inbound orders contain sales order record, cannot be reserved")

        # 有出库单无法反审核
        if ERPOutboundOrderDetailInventoryRelate.objects.filter(
            sku_inventory_id__in=erp_sku_inventory_id_list,
            outbound_order__status=1,
        ).exists():
            raise APIViewException(err_message="inbound orders contain outbound order record, cannot be reserved")

        valid_inbound_codes = [io.inbound_code for io in inbound_orders]

        # 判断是否正在调整库存
        sku_pk_list = InboundOrderDetail.objects.filter(inbound_order__in=inbound_orders, is_deleted=False).values_list("sku_id")
        InventoryAdjustmentOrderDetail.judge_sku_is_adjusting(sku_pk_list)

        with transaction.atomic():
            # 更新库存查询状态
            ERPSKUInventory.objects.filter(inbound_order__in=inbound_orders).update(
                update_user=self.current_user.user_id,
                status=2,
                update_date=timezone.now(),
            )

            for inbound_order in inbound_orders:
                # 更新入库单状态
                inbound_order.approve_status = 1
                inbound_order.approve_date = None
                inbound_order.approve_user = ""
                inbound_order.save()

                # 计划单
                if inbound_order.order_type == 4:
                    work = ProduceWork.objects.get(pk=inbound_order.from_order_id)
                    work.plan.inbound_cancel_approve(inbound_order.total_quantity)
                    work.plan.save()

                # 明细
                details = InboundOrderDetail.objects.filter(inbound_order=inbound_order)
                for detail in details:
                    InventoryChangeDetails.delete_detail(InventoryChangeDetails.CHANGE_TYPE_11, detail.id, update_user=self.current_user.user_id)

        for approve_code in valid_inbound_codes:
            self.write_log(
                approve_code,
                self.resource_name,
                request,
                "反审核入库单",
                self.current_user_type,
                ContentType.objects.get_for_model(InboundOrder).pk,
                fronted_page=self.fronted_page,
                operate_content="",
                user_mobile=self.current_user.mobile,
            )

        return IResponse()

    @action(detail=True, methods=["get"])
    def log(self, request, inbound_code=None):
        """
        获取入库单日志
        """
        if not InboundOrder.objects.filter(inbound_code=inbound_code, company=self.current_user.company).exists():
            return IResponse(data=EmptyListResponse)

        content_type = ContentType.objects.get_for_model(InboundOrder)
        operation_logs = OperationLog.objects.filter(
            content_type=content_type,
            resource_id=inbound_code,
        ).order_by("-operation_time")

        re_data, _, _ = custom_django_filter(
            request,
            operation_logs,
            OperationLogFilter,
            OperationLogSer,
            force_order=False,
        )

        return IResponse(data=re_data)

    @action(detail=True, methods=["delete"])
    def product_detail(self, request, inbound_code=None):
        """
        入库订单明细详情(删除)
        """
        inbound_detail_code = request.data.get("inbound_detail_code")
        if not inbound_detail_code:
            raise APIViewException

        inbound_order = self.get_object()
        if inbound_order.exist_stack_order_record:
            raise APIViewException(err_message="inbound orders contain stock_take order record, cannot be deleted")

        inbound_detail = inbound_order.inboundorderdetail_set.get(inbound_detail_code=inbound_detail_code, is_deleted=False)
        if not inbound_detail:
            raise DataNotFoundException

        inbound_detail.is_deleted = True
        inbound_detail.update_user = str(self.current_user.user_id)
        inbound_detail.save()

        self.log_operation(
            request,
            InboundOrder,
            "删除入库订单明细",
            resource_id=inbound_code,
            operate_content=f"删除明细: {inbound_detail}, 商品: {inbound_detail.sku}",
            is_success_input=True,
        )

        return IResponse()

    @action(detail=True, methods=["post"])
    def tid_verify(self, request, inbound_code=None):
        """
        入库订单TID校验
        """
        tids = request.data.get("tids")
        if not tids:
            raise APIViewException(err_message="tids is required")

        sku_tid_map = tid_to_sku_map(tids)
        if not sku_tid_map:
            raise APIViewException

        inbound_order = self.get_object()
        if inbound_order.approve_status == 2:
            raise APIViewException(err_message="inbound order already approved, cannot be verified again")

        skus = ERPSupplierProductSKU.objects.filter(sku_id__in=list(sku_tid_map.keys())).only("id", "sku_id")
        skus_map = {sku.sku_id: sku.pk for sku in skus}

        inbound_order_details = inbound_order.inboundorderdetail_set.filter(
            is_deleted=False,
            sku_id__in=list(skus_map.values()),
        )

        new_sku_tid_map = {skus_map[sku_id]: val for sku_id, val in sku_tid_map.items() if sku_id in skus_map}

        need_update_objs = []
        verify_sku_names = []
        for inbound_order_detail in inbound_order_details:
            sku_pk = inbound_order_detail.sku_id
            if sku_pk not in new_sku_tid_map:
                continue

            verified_tids = new_sku_tid_map[sku_pk]
            for tid, status_data in inbound_order_detail.tid_info.items():
                if tid in verified_tids:
                    inbound_order_detail.set_tid_verified_status(tid, self.current_user.user_id)
                else:
                    inbound_order_detail.set_tid_unverified_status(tid, self.current_user.user_id)

            verify_sku_names.append(inbound_order_detail.sku.name)
            need_update_objs.append(inbound_order_detail)

        if need_update_objs:
            InboundOrderDetail.objects.bulk_update(need_update_objs, fields=["tid_info"])

        self.log_operation(
            request,
            InboundOrder,
            describe=f"校验标签：入库单号:{inbound_code}",
            operate_content="校验商品：{}".format("、".join(verify_sku_names)),
            resource_id=inbound_code,
            is_success_input=True,
        )

        return IResponse()

    @action(detail=True, methods=["get"])
    def tids(self, request, inbound_code=None):
        """
        获取入库订单可以矫正的tid列表, 前端用作扫描过滤
        """
        inbound_order = self.get_object()
        if inbound_order.approve_status == 2:
            return IResponse(data=[])

        details = (
            inbound_order.inboundorderdetail_set.filter(
                is_deleted=False,
                tag_type=1,
            )
            .annotate(keys=Func(F("tid_info"), function="jsonb_object_keys"))
            .values("keys", "sku__sku_id")
        )
        re_data = ["{}{}".format(decimal_to_base62(detail["sku__sku_id"]), detail["keys"]) for detail in details]

        return IResponse(data=re_data)

    @action(detail=True, methods=["post"])
    def reprint_labels(self, request, inbound_code=None):
        """
        入库订单标签重打
        续打标签：打印数量比入库数量少的时候，继续打剩余的标签
        补打标签：全部打印后，获取未校验的标签
        """
        # 获取入库单并验证状态
        inbound_order = self.get_object()
        if inbound_order.approve_status == 2:
            raise APIViewException(err_message="inbound order already approved, cannot reprint labels")

        # 获取并验证输入参数
        inbound_detail_code = request.data.get("inbound_detail_code")
        if not inbound_detail_code:
            raise APIViewException(err_message="inbound_detail_code is required")

        reprint_quantity = request.data.get("reprint_quantity")
        if not reprint_quantity:
            raise APIViewException(err_message="reprint_quantity is required")

        reprint_type = request.data.get("reprint_type", 1)  # 默认为续打(1)

        # 获取入库单明细
        try:
            inbound_order_detail = inbound_order.inboundorderdetail_set.get(inbound_detail_code=inbound_detail_code, is_deleted=False)
        except InboundOrderDetail.DoesNotExist:
            raise DataNotFoundException(err_message="inbound order detail not found")

        # 根据重打类型处理
        if reprint_type == 1:  # 续打标签
            # 如果是RFID标签，验证打印数量
            if inbound_order_detail.tag_type == 1 and reprint_quantity > inbound_order_detail.get_can_print_quantity():
                raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

            # 生成TID信息
            tid_infos = ERPProductSKUTID.batch_generate_tid_info(reprint_quantity, inbound_code)
            sku = inbound_order_detail.sku
            created_tids = list(tid_infos.keys())

            # 在事务中更新数据库
            with transaction.atomic():
                # 更新SKU TID信息
                try:
                    sku_tid_relate = sku.erpproductskutid
                    old_sku_tid_info = sku_tid_relate.tid_info
                    old_sku_tid_info.update(tid_infos)
                    sku_tid_relate.tid_info = old_sku_tid_info
                    sku_tid_relate.save()
                except ERPProductSKUTID.DoesNotExist:
                    ERPProductSKUTID.objects.create(
                        sku_id=inbound_order_detail.sku_id,
                        tid_info=tid_infos,
                    )

                # 更新入库单明细TID信息
                new_tid_info = InboundOrderDetail.batch_generate_tid_info(
                    created_tids,
                    "reprint",
                    str(self.current_user.user_id),
                    "printed",
                )

                old_tid_info = inbound_order_detail.tid_info or {}
                old_tid_info.update(new_tid_info)
                inbound_order_detail.tid_info = old_tid_info
                inbound_order_detail.print_count += reprint_quantity
                inbound_order_detail.save()

            # 记录操作日志
            self.log_operation(
                request,
                InboundOrder,
                describe=f"续打标签：入库单号:{inbound_code}, 商品: {sku.name}. 数量: {reprint_quantity}",
                resource_id=inbound_code,
                is_success_input=True,
            )

            # 准备响应数据
            return IResponse(
                data=[
                    {
                        "name": sku.name,
                        "spec_code": sku.spec_code,
                        "code": sku.product.code,
                        "tids": [f"{decimal_to_base62(sku.sku_id)}{tid}" for tid in created_tids],
                    }
                ]
            )

        elif reprint_type == 2:  # 补打标签
            # 验证补打数量
            if reprint_quantity > inbound_order_detail.get_reprint_quantity():
                raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

            # 获取未验证的TID用于补打
            tid_infos = inbound_order_detail.tid_info or {}
            need_reprint_tid_list = []
            for tid, info in tid_infos.items():
                if info.get("verify_status") == "unverified" and len(need_reprint_tid_list) < reprint_quantity:
                    need_reprint_tid_list.append(tid)
            sku = inbound_order_detail.sku
            # 记录操作日志
            self.log_operation(
                request,
                InboundOrder,
                describe=f"补打标签：入库单号:{inbound_code}, 商品: {sku.name}. 数量: {reprint_quantity}",
                resource_id=inbound_code,
                is_success_input=True,
            )

            # 准备响应数据
            sku = inbound_order_detail.sku
            return IResponse(
                data=[
                    {
                        "name": sku.name,
                        "spec_code": sku.spec_code,
                        "code": sku.product.code,
                        "tids": [f"{decimal_to_base62(sku.sku_id)}{tid}" for tid in need_reprint_tid_list],
                    }
                ]
            )
        else:
            raise APIViewException(err_message="invalid reprint_type, must be 1 or 2")

    @action(detail=True, methods=["get"])
    def product_list(self, request, inbound_code=None):
        """
        入库单未校验的商品列表
        """

        def prepare_combine_qs(combine_tid, detail):
            return {
                "tid": combine_tid,
                "name": detail["sku__name"],
                "code": detail["sku__product__code"],
                "spec_code": detail["sku__spec_code"],
                "warehouse_name": detail["warehouse__name"],
                "warehouse_position_name": detail["warehouse_position__name"],
                "label_price": detail["label_price"],
                "main_images": detail["sku__main_images"] or [],
            }

        inbound_order = self.get_object()
        if inbound_order.approve_status == 2:
            return IResponse(data=EmptyListResponse)

        details = inbound_order.inboundorderdetail_set.filter(is_deleted=False, tag_type=1).values(
            "sku__name",
            "sku__product__code",
            "sku__sku_id",
            "sku__spec_code",
            "warehouse__name",
            "warehouse_position__name",
            "label_price",
            "tid_info",
            "sku__main_images",
        )

        verify_status = request.query_params.get("verify_status")
        if not verify_status:
            verify_status = "unverified"

        # 添加hybrid_search功能
        hybrid_search = request.query_params.get("hybrid_search")
        combine_qs = []
        for detail in details:
            sku_id = detail["sku__sku_id"]
            for k, v in detail["tid_info"].items():
                if v["verify_status"] == verify_status:
                    combine_tid = "{}{}".format(decimal_to_base62(sku_id), k)
                    if hybrid_search:
                        if (hybrid_search in detail["sku__spec_code"]) or (hybrid_search in combine_tid) or (hybrid_search in detail["sku__product__code"]):
                            combine_qs.append(prepare_combine_qs(combine_tid, detail))
                    else:
                        combine_qs.append(prepare_combine_qs(combine_tid, detail))

        # 检查是否需要分页
        no_pagination = request.query_params.get("no_pagination", "0")

        # 如果no_pagination参数为1，则不进行分页处理
        if no_pagination == "1":
            # 更新request.query_params中的count值
            if hasattr(request.query_params, "_mutable"):
                request.query_params._mutable = True
                request.query_params["count"] = str(len(combine_qs))
                request.query_params._mutable = False

            return IResponse(data={"count": len(combine_qs), "total_pages": 1, "current_page": 1, "data": combine_qs})

        # 正常分页处理
        page = request.query_params.get("page", 1)
        page_size = request.query_params.get("page_size", 10)

        paginator = Paginator(combine_qs, page_size)
        page_obj = paginator.get_page(page)

        # 更新request.query_params中的count值
        if hasattr(request.query_params, "_mutable"):
            request.query_params._mutable = True
            request.query_params["count"] = str(paginator.count)
            request.query_params._mutable = False

        return IResponse(
            data={
                "count": paginator.count,
                "total_pages": paginator.num_pages,
                "current_page": page,
                "data": page_obj.object_list,
            }
        )

    def create(self, request, *args, **kwargs):
        """
        创建入库单
        """
        post_data = request.data

        order_type = post_data.get("order_type")
        if order_type and order_type == 5:
            # 其他入库使用单独的序列化器
            ser = SupplierOtherInboundOrderCreateSerializer(data=post_data, context={"request": request})
        else:
            ser = self.get_serializer(data=request.data, context={"request": request})
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        inbound_order = ser.save()

        self.log_operation(
            request,
            InboundOrder,
            describe=f"新建{inbound_order.get_order_type_display()}单: {inbound_order.inbound_code}",
            resource_id=inbound_order.inbound_code,
            is_success_input=True,
        )
        return IResponse(data={"inbound_code": inbound_order.inbound_code})

    @action(detail=False, methods=["post"])
    def bulk_quick(self, request):
        """
        批量创建入库单
        """
        ser = BatchInboundOrderCreateSerializer(data=request.data, context={"request": request})
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        inbound_order = ser.save()

        self.log_operation(
            request,
            InboundOrder,
            describe=f"创建入库单: {inbound_order.inbound_code}",
            resource_id=inbound_order.inbound_code,
            is_success_input=True,
        )
        return IResponse(data={"inbound_code": inbound_order.inbound_code})

    @action(detail=False, methods=["get", "post"], url_path="parse_excel_task")
    def parse_inbound_order_detail_excel(self, request, *args, **kwargs):
        """
        解析excel
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        if request.method == "GET":
            task_id = request.query_params.get("task_id")
            if not task_id:
                raise APIViewException

            cache_key = f"oss_fc:{task_id}"
            error_cache_key = f"{cache_key}_error"
            fail_val = cache.get(error_cache_key)
            if fail_val:
                return IResponse(code=400, message="Failed to parse excel, pls try later")

            data = cache.get(cache_key)
            if data:
                # 获取商品编码
                spec_codes = [_d.get("商品编码") or _d.get("货号") for _d in data]

                # 处理空的商编
                for d in data:
                    if not d.get("商品编码"):
                        d["商品编码"] = d.get("货号")

                # 根据商编合并数量
                clean_data_dict = {}
                for d in data:
                    spec_code = d.get("商品编码")
                    if not spec_code:
                        continue

                    if spec_code in clean_data_dict:
                        d["总量"] = (d.get("总量") or 0) + (clean_data_dict[spec_code].get("总量") or 0)

                    clean_data_dict[spec_code] = d

                #
                skus = ERPSupplierProductSKU.objects.prefetch_related("product").filter(
                    spec_code__in=spec_codes,
                    is_deleted=False,
                    company=self.current_user.company,
                )

                skus_map = {sku.spec_code: sku for sku in skus}

                resp_data = []

                data = clean_data_dict.values()

                for idx, d in enumerate(data):
                    spec_code = d.get("商品编码") or d.get("货号")

                    exist_sku = None
                    if spec_code in skus_map:
                        exist_sku = skus_map[spec_code]

                    unit_name = d.get("单位")
                    if exist_sku:
                        if exist_sku.unit_id:
                            unit_name = exist_sku.unit.name

                    cost_price_str = d.get("成本") or 0
                    if exist_sku:
                        cost_price = exist_sku.cost_price
                    if not cost_price_str:
                        cost_price = 0
                    elif str(cost_price_str).isdigit():
                        cost_price = float(cost_price_str)
                    else:
                        numeric_match = re.search(r"\d+(\.\d+)?", cost_price_str)
                        if numeric_match:
                            cost_price = float(numeric_match.group())
                        else:
                            cost_price = 0

                    label_price = cost_price * 2
                    market_price = 0
                    purchase_price = 0
                    if exist_sku:
                        label_price = exist_sku.label_price
                        market_price = exist_sku.market_price
                        purchase_price = exist_sku.purchase_price

                    img_url = d["extracted_image"]
                    if exist_sku and exist_sku.main_images:
                        img_url = exist_sku.main_images[0]

                    supplier_id = ""
                    supplier_name = ""
                    if exist_sku:
                        supplier_id = exist_sku.supplier_id
                        supplier_name = "" if not exist_sku.supplier else exist_sku.supplier.name

                    tmp_data = {
                        "serial_no": idx + 1,
                        "image_url": img_url,
                        "name": d.get("货品名称") if not exist_sku else exist_sku.name,
                        "code": d.get("货号") if not exist_sku else exist_sku.product.code,
                        "spec_code": spec_code,
                        "spec_val": d.get("规格") if not exist_sku else exist_sku.spec_value,
                        "size": "" if not exist_sku else exist_sku.size,
                        "unit_name": unit_name,
                        "quantity": d.get("总量") or 0,
                        "cost_price": cost_price,
                        "label_price": label_price,
                        "market_price": market_price,
                        "purchase_price": purchase_price,
                        "supplier_id": supplier_id,
                        "supplier_name": supplier_name,
                    }

                    resp_data.append(tmp_data)

                return IResponse(data=resp_data)

            return IResponse()

        if request.method == "POST":
            oss_link = request.data.get("oss_link")
            if not oss_link:
                raise APIViewException

            try:
                bucket_name, object_key, endpoint = parse_oss_url(oss_link)
            except Exception:
                raise APIViewException(err_message="Invalid file link")

            try:
                info_data = get_oss_bucket_client(endpoint, bucket_name).get_object_meta(object_key)
            except oss_exceptions.NoSuchKey:
                raise APIViewException(err_message="Fail to get object")
            except oss_exceptions.NoSuchBucket:
                raise APIViewException(err_message="Fail to get object, pls contact administrator")
            except oss_exceptions.NotFound:
                raise APIViewException(err_message="Fail to get object")
            except Exception:
                raise APIViewException(err_message="Fail to get object, pls contact administrator")
            resp = info_data.resp
            status_code = resp.status
            if status_code != 200:
                raise APIViewException(err_message="Fail to get object, pls try later")

            # 判断Content-Length是否大于阈值，太大不允许操作, 默认10Mb
            config_val = get_inbound_excel_parse_config()

            if int(resp.headers["Content-Length"]) > config_val * 1024 * 1024:
                raise APIViewException(err_message=f"Max size of file is {config_val}Mb")

            task_id = uuid.uuid4().hex
            revoke_oss_cloud_parse_excel_fc_task.delay(task_id, oss_link)
            return IResponse(data={"task_id": task_id})

        return IResponse()

    @action(detail=False, methods=["post"])
    def download(self, request, *args, **kwargs):
        task = ERPAsyncDownloadTaskReceiver(
            request,
            "采购入库订单",
            DownloadTasksTypeChoices.INBOUND_ORDER,
            self.current_user.user_id,
            self.current_user_type,
            self.current_user.company,
            async_download_inbound_order_task,
        ).process_task()
        return IResponse(code=201, message="后台正在处理下载任务", data={"task_id": task.id})

    @action(detail=True, methods=["patch"])
    def cancel(self, request, inbound_code=None, *args, **kwargs):
        """
        取消采购入库单
        :param inbound_code:
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        inbound_order = self.get_no_filter_object()

        if inbound_order.approve_status == 2:
            raise APIViewException(err_message="Inbound order already approved, cannot cancel")

        if inbound_order.status == 2:
            raise APIViewException(err_message="Inbound order already canceled")

        inbound_order.status = 2
        inbound_order.update_user = self.current_user.user_id
        inbound_order.save()

        # 加工单数量扣除
        if inbound_order.order_type == 4:
            work = ProduceWork.objects.filter(pk=inbound_order.from_order_id)
            if work.exists():
                work = work.first()
                work.inbound_cancel(inbound_order.total_quantity)
                work.save()

        self.log_operation(
            request,
            InboundOrder,
            f"取消{inbound_order.get_order_type_display()}订单：{inbound_order.inbound_code}",
            resource_id=inbound_code,
            is_success_input=True,
        )
        return IResponse()


class InboundOrderDetailViewSet(SPModelViewSet):
    fronted_page = "入库明细"
    resource_name = "入库明细"
    lookup_field = "inbound_detail_code"

    def get_queryset(self):
        return InboundOrderDetail.objects.filter(
            is_deleted=False,
            inbound_order__company=self.current_user.company,
            inbound_order__is_deleted=False,
        ).exclude(inbound_order__order_type=2)

    @action(detail=True, methods=["post", "get"])
    def reprint_labels(self, request, inbound_detail_code=None):
        inbound_detail = self.get_object()

        inbound_order = inbound_detail.inbound_order
        if inbound_order.approve_status == 2:
            raise APIViewException(err_message="inbound order already approved, cannot reprint labels")

        # 打印标签类型
        if request.method == "GET":
            reprint_type = request.query_params.get("reprint_type", "1")
            if reprint_type not in ["1", "2"]:
                raise APIViewException(err_message="reprint_type must be 1 or 2")
            reprint_type = int(reprint_type)
        else:
            reprint_type = request.data.get("reprint_type", 1)  # 默认为续打(1)
            if reprint_type not in [1, 2]:
                raise APIViewException(err_message="reprint_type must be 1 or 2")

        sku = inbound_detail.sku
        if request.method == "GET":
            # 只获取标签，不加入到明细中
            reprint_quantity = request.query_params.get("reprint_quantity")
            if not reprint_quantity:
                raise APIViewException(err_message="reprint_quantity is required")
            if not str(reprint_quantity).isdigit():
                raise APIViewException(err_message="reprint_quantity must be a number")

            reprint_quantity = int(reprint_quantity)

            can_reprint_tids = []
            # 根据重打类型处理
            if reprint_type == 1:  # 续打标签
                # 如果是RFID标签，验证打印数量
                if inbound_detail.tag_type == 1 and reprint_quantity > inbound_detail.get_can_print_quantity():
                    raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

                # 生成TID信息
                tid_infos = ERPProductSKUTID.batch_generate_tid_info(reprint_quantity, inbound_order.inbound_code)
                can_reprint_tids = list(tid_infos.keys())

            elif reprint_type == 2:  # 补打标签
                # 验证补打数量
                if reprint_quantity > inbound_detail.get_reprint_quantity():
                    raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

                # 获取未验证的TID用于补打
                tid_infos = inbound_detail.tid_info or {}
                for tid, info in tid_infos.items():
                    if info.get("verify_status") == "unverified" and len(can_reprint_tids) < reprint_quantity:
                        can_reprint_tids.append(tid)

            return IResponse(
                data=[
                    {
                        "name": sku.name,
                        "spec_code": sku.spec_code,
                        "code": sku.product.code,
                        "tids": [f"{decimal_to_base62(sku.sku_id)}{tid}" for tid in can_reprint_tids],
                    }
                ]
            )
        elif request.method == "POST":
            tids = request.data.get("tids")
            if not tids:
                raise APIViewException(err_message="tids is required")
            # base62解密
            sku_tid_map = tid_to_sku_map(tids)
            if not sku_tid_map:
                raise APIViewException

            if sku.sku_id not in sku_tid_map:
                raise APIViewException(err_message="invalid tids")

            tid_list = [tid for v in sku_tid_map.values() for tid in v]
            # 是否在其他入库明细
            inbound_detail_exist_tid_list = InboundOrderDetail.expand_tid_info_query(tid_list).values_list("tid", flat=True)
            if inbound_detail_exist_tid_list:
                exist_tid_msg = ",".join([f"{decimal_to_base62(sku.sku_id)}{i}" for i in inbound_detail_exist_tid_list])
                logger.info(f"标签:{exist_tid_msg}已被使用，请刷新页面后重新获取标签1")
                raise APIViewException(err_message=f"标签:{exist_tid_msg}已被使用，请刷新页面后重新获取标签")

            inventory_tid_list = ERPSKUInventory.expand_tid_info_query(tid_list).values_list("tid", flat=True)
            if inventory_tid_list:
                exist_tid_msg = ",".join([f"{decimal_to_base62(sku.sku_id)}{i}" for i in inventory_tid_list])
                logger.info(f"标签:{exist_tid_msg}已被使用，请刷新页面后重新获取标签2")
                raise APIViewException(err_message=f"标签:{exist_tid_msg}已被使用，请刷新页面后重新获取标签")

            printed_tids = sku_tid_map[sku.sku_id]
            # 上传的tid数量
            reprint_quantity = len(printed_tids)
            if reprint_type == 1:
                # 如果是RFID标签，验证打印数量
                if inbound_detail.tag_type == 1 and reprint_quantity > inbound_detail.get_can_print_quantity():
                    raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

                # 在事务中更新数据库
                with transaction.atomic():
                    # 更新入库单明细TID信息
                    new_tid_info = InboundOrderDetail.batch_generate_tid_info(
                        printed_tids,
                        "reprint",
                        str(self.current_user.user_id),
                        "printed",
                    )

                    old_tid_info = inbound_detail.tid_info or {}
                    old_tid_info.update(new_tid_info)
                    inbound_detail.tid_info = old_tid_info
                    inbound_detail.print_count += len(printed_tids)
                    inbound_detail.save()

                # 记录操作日志
                self.log_operation(
                    request,
                    InboundOrder,
                    describe=f"续打标签：入库单号:{inbound_order.inbound_code}, 商品: {sku.name}. 数量: {len(printed_tids)}",
                    resource_id=inbound_detail_code,
                    is_success_input=True,
                )
                return IResponse()
            else:
                # 验证补打数量
                if reprint_quantity > inbound_detail.get_reprint_quantity():
                    raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

                # 校验tid是否在明细
                for printed_tid in printed_tids:
                    if printed_tid not in inbound_detail.tid_info:
                        raise APIViewException(err_message="非法TID：{}".format(decimal_to_base62(sku.sku_id)) + printed_tid)

                # 记录操作日志
                self.log_operation(
                    request,
                    InboundOrder,
                    describe=f"补打标签：入库单号:{inbound_order.inbound_code}, 商品: {sku.name}. 数量: {reprint_quantity}",
                    resource_id=inbound_detail_code,
                    is_success_input=True,
                )

                return IResponse()
