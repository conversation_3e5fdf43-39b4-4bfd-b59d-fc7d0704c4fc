# -*- coding: utf-8 -*-
import copy
import decimal
from datetime import datetime
from urllib.parse import quote

from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.db.models import Prefetch
from django.http import HttpResponse
from django.utils import timezone
from rest_framework.request import Request

from common.basic import OPAPIView
from common.basics.views import set_log_params, OperateLogAPIViewMixin
from common.basics.exceptions import DataNotFoundException, APIViewException
from common.models import OperationLog
from common.utils import diff_models
from erp_apps import logger
from erp_products.models import ERPStockKeepingUnit
from erp_purchase.filters import ReturnOrder<PERSON><PERSON><PERSON><PERSON>er, ERPReturnOrderProductDetailFilter
from erp_purchase.filters.operation_log_filter import OperationLogFilter
from erp_purchase.models import ERPReturnOrders, PurchaseReturnLabel, ERPReturnOrderDetail
from erp_purchase.resources import ERPReturnOrderDetailDownloadResource
from erp_purchase.serializers.operation_log_serializers import OperationLogSer
from erp_purchase.serializers.return_order_serializer import (
    ERPReturnOrderListSer,
    ERPReturnOrderCreateSer,
    ERPReturnOrderUpdateSer,
    ERPReturnOrderProductDetailListSer,
    ERPReturnOrderDetailUpdateSer,
)
from utils.http_handle import custom_django_filter, IResponse, FieldsError


class ERPReturnOrderView(OPAPIView, OperateLogAPIViewMixin):
    resource_name = "退货单"
    fronted_page = "采购退货"
    need_format_resource_name = True

    def get(self, request: Request):
        label_prefetch = Prefetch("labels", PurchaseReturnLabel.objects.filter(is_deleted=False))
        return_orders = (
            ERPReturnOrders.objects.filter(is_deleted=False)
            .prefetch_related(
                "warehouse",
                "storage_party",
                "company",
                label_prefetch,
            )
            .order_by("-update_date")
        )

        re_data, _, _ = custom_django_filter(
            request,
            return_orders,
            ReturnOrderListFilter,
            ERPReturnOrderListSer,
            force_order=False,
        )
        return IResponse(data=re_data)

    def post(self, request: Request):
        """
        生成退货单
        :param request:
        :return:
        """
        create_ser = ERPReturnOrderCreateSer(data=request.data, context={"request": request})
        if not create_ser.is_valid():
            raise FieldsError(create_ser.errors)
        create_ser.save()
        return IResponse()

    def _confirm_valid(self, request: Request):
        """
        确认生效
        :param request:
        :return:
        """
        order_codes = request.data.get("codes", [])
        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        # 退货订单
        return_orders = ERPReturnOrders.objects.filter(order_status=1, is_deleted=False, code__in=order_codes)
        if not return_orders:
            raise APIViewException(err_message="pls select valid orders")

        return_order_code_list = [o.code for o in return_orders]
        return_order_agg_data = ERPReturnOrders.in_bulk_exist_order_detail(return_order_code_list)  # type: ignore

        can_valid_order_code_list = []
        for code in return_order_code_list:
            if (return_order_agg_data.get(code, 0) or 0) > 0:
                can_valid_order_code_list.append(code)

        if not can_valid_order_code_list:
            raise APIViewException(err_message="cannot operate with return product quantity is 0")

        now_time = timezone.now()
        ERPReturnOrders.objects.filter(code__in=can_valid_order_code_list).update(
            order_status=2,
            confirm_valid_user=self.current_user.user_id,
            confirm_valid_date=now_time,
            update_date=now_time,
            update_user=self.current_user.user_id,
        )
        # 日志记录，需要到每个采购单下面
        try:
            for code in can_valid_order_code_list:
                set_log_params(
                    request,
                    resource_id=code,
                    model=ERPReturnOrders,
                    describe="审核退货单",
                    operate_content=f"审核退货单：{code}",
                    is_success_input=True,
                )
        except Exception as e:
            logger.error(f"set log params failed, {e}")
            pass

        return IResponse()

    def _invalid(self, request: Request):
        """
        作废
        :param request:
        :return:
        """
        order_codes = request.data.get("codes", [])
        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        # 退货订单
        return_orders = ERPReturnOrders.objects.filter(
            order_status=1,
            is_deleted=False,
            code__in=order_codes,
        )
        if not return_orders:
            raise APIViewException(err_message="pls select valid orders")

        invalid_codes = [return_order.code for return_order in return_orders]
        now_time = timezone.now()
        return_orders.update(
            order_status=3,
            update_date=now_time,
            update_user=self.current_user.user_id,
        )

        # 日志记录，需要到每个采购单下面
        try:
            for code in invalid_codes:
                set_log_params(
                    request,
                    resource_id=code,
                    model=ERPReturnOrders,
                    describe="作废退货单",
                    operate_content=f"作废退货单：{code}",
                    is_success_input=True,
                )
        except Exception as e:
            logger.error(f"set log params failed, {e}")
            pass

        return IResponse()

    def _financial_approve(self, request: Request):
        """
        财务审核
        :param request:
        :return:
        """
        order_codes = request.data.get("codes", [])
        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        # 生效的退货单才能财务审核
        return_orders = ERPReturnOrders.objects.filter(
            order_status=2,
            financial_status=1,
            is_deleted=False,
            code__in=order_codes,
        )
        if not return_orders:
            raise APIViewException(err_message="pls select valid orders")

        can_financial_code_list = [o.code for o in return_orders]

        # 更新审核状态
        ERPReturnOrders.objects.filter(code__in=can_financial_code_list).update(
            last_financial_date=timezone.now(),
            last_financial_user=self.current_user.user_id,
            financial_status=2,
            update_date=timezone.now(),
        )

        # 日志记录，需要到每个采购单下面
        try:
            for code in can_financial_code_list:
                set_log_params(
                    request,
                    resource_id=code,
                    model=ERPReturnOrders,
                    describe="财务审核",
                    operate_content=f"财务审核退货单：{code}",
                    is_success_input=True,
                )
        except Exception as e:
            logger.error(f"set log params failed, {e}")
            pass

        return IResponse()

    def _financial_cancel_approve(self, request: Request):
        """
        财务反审核
        :param request:
        :return:
        """
        order_codes = request.data.get("codes", [])
        if not order_codes:
            raise APIViewException(err_message="pls select orders")

        # 生效的退货单才能财务审核
        return_orders = ERPReturnOrders.objects.filter(
            order_status=2,
            financial_status=2,
            is_deleted=False,
            code__in=order_codes,
        )
        if not return_orders:
            raise APIViewException(err_message="pls select valid orders")

        can_financial_code_list = [o.code for o in return_orders]

        # 更新审核状态
        ERPReturnOrders.objects.filter(code__in=can_financial_code_list).update(
            last_financial_date=timezone.now(),
            last_financial_user=self.current_user.user_id,
            financial_status=1,
            update_date=timezone.now(),
        )

        # 日志记录，需要到每个采购单下面
        try:
            for code in can_financial_code_list:
                set_log_params(
                    request,
                    resource_id=code,
                    model=ERPReturnOrders,
                    describe="财务反审核",
                    operate_content=f"财务反审核入库单：{code}",
                    is_success_input=True,
                )
        except Exception as e:
            logger.error(f"set log params failed, {e}")
            pass

        return IResponse()

    ACTION_MAPPING = {
        "confirm_valid": "_confirm_valid",
        "invalid": "_invalid",
        "financial_approve": "_financial_approve",
        "financial_cancel_approve": "_financial_cancel_approve",
    }

    def patch(self, request: Request):
        """
        采购退货操作
        :param request:
        :return:
        """
        action = request.data.get("action")
        if not action:
            raise APIViewException(err_message="action is required")

        handler = self.ACTION_MAPPING.get(action)

        if not handler or not hasattr(self, handler):
            raise APIViewException

        return getattr(self, handler)(request)


class ERPReturnOrderDetailView(OPAPIView, OperateLogAPIViewMixin):
    resource_name = "退货单"
    fronted_page = "采购退货"
    need_format_resource_name = True

    @staticmethod
    def _get_object(return_code: str) -> ERPReturnOrders:
        try:
            return ERPReturnOrders.objects.get(
                code=return_code,
                is_deleted=False,
                order_status__in=[1, 2],
            )
        except ERPReturnOrders.DoesNotExist:
            raise DataNotFoundException

    def patch(self, request: Request, return_code: str):
        return_order = self._get_object(return_code)
        raw_obj = copy.deepcopy(return_order)
        post_data = request.data
        post_data["update_user"] = self.current_user.user_id
        update_ser = ERPReturnOrderUpdateSer(instance=return_order, data=post_data, partial=True)
        if not update_ser.is_valid():
            raise FieldsError(update_ser.errors)
        updated_obj = update_ser.save()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=return_code,
                model=ERPReturnOrders,
                describe="编辑退货单",
                operate_content=diff_models(raw_obj, updated_obj),
                is_success_input=True,
            )
        except Exception as e:
            logger.error(f"set log params failed, {e}")
            pass

        return IResponse()


class ERPReturnOrderLogView(OPAPIView):
    def get(self, request: Request, return_code: str):
        content_type = ContentType.objects.get_for_model(ERPReturnOrders)
        operation_logs = OperationLog.objects.filter(
            content_type=content_type,
            resource_id=return_code,
        ).order_by("-operation_time")
        re_data, _, _ = custom_django_filter(
            request,
            operation_logs,
            OperationLogFilter,
            OperationLogSer,
            force_order=False,
        )
        return IResponse(data=re_data)


class ERPReturnOrderProductDetailView(OPAPIView, OperateLogAPIViewMixin):
    resource_name = "退货明细"
    fronted_page = "采购退货"
    need_format_resource_name = True

    @staticmethod
    def _get_object(return_order_code) -> ERPReturnOrders:
        try:
            return_order = ERPReturnOrders.objects.get(code=return_order_code)
            if return_order.order_status != 1:
                raise APIViewException(err_message="editable only in pending status")
            return return_order
        except ERPReturnOrders.DoesNotExist:
            raise DataNotFoundException

    def get(self, request: Request, return_code: str):
        return_order = self._get_object(return_code)

        return_order_details = return_order.erpreturnorderdetail_set.prefetch_related(
            "erp_sku",
            "erp_sku__product",
        ).filter(is_deleted=False)
        re_data, _, _ = custom_django_filter(
            request,
            return_order_details,
            ERPReturnOrderProductDetailFilter,
            ERPReturnOrderProductDetailListSer,
        )
        return IResponse(data=re_data)

    def post(self, request: Request, return_code: str):
        return_detail_list = request.data.get("return_detail")
        if not return_detail_list or not isinstance(return_detail_list, list):
            raise APIViewException

        return_order = self._get_object(return_code)
        erp_sku_id_list = []
        for purchase_detail in return_detail_list:
            if "erp_sku_id" not in purchase_detail:
                raise APIViewException(err_message="Invalid product info")
            erp_sku_id_list.append(purchase_detail["erp_sku_id"])

        conditions = {
            "sku_id__in": erp_sku_id_list,
            "become_history": False,
            "product__is_deleted": False,
        }

        erp_skus = ERPStockKeepingUnit.objects.filter(**conditions)
        if len(erp_skus) != len(erp_sku_id_list):
            raise APIViewException(err_message="Invalid product info")

        erp_skus_map = {erp_sku.sku_id: erp_sku for erp_sku in erp_skus}

        exist_erp_sku_id_list = return_order.erpreturnorderdetail_set.filter(
            is_deleted=False,
        ).values_list("erp_sku_id", flat=True)

        bulk_created_objs = []
        for return_detail in return_detail_list:
            erp_sku_id = return_detail["erp_sku_id"]
            if erp_sku_id in exist_erp_sku_id_list:
                continue

            cost_price = 0
            quantity = return_detail.get("quantity") or 1

            retail_price = 0
            if erp_sku_id in erp_skus_map:
                erp_sku = erp_skus_map[erp_sku_id]
                retail_price = erp_sku.retail_price or 0
                cost_price = erp_sku.cost_price or 0

            bulk_created_objs.append(
                ERPReturnOrderDetail(
                    return_order=return_order,
                    erp_sku_id=erp_sku_id,
                    cost_price=cost_price,
                    quantity=quantity,
                    amount=decimal.Decimal(cost_price * quantity),
                    retail_price=retail_price,
                    retail_amount=retail_price * quantity,
                )
            )
        with transaction.atomic():
            ERPReturnOrderDetail.objects.bulk_create(bulk_created_objs)
            # 统计数据
            return_order.save_product_quantity_and_total_amount()

        try:
            set_log_params(
                request,
                resource_id=return_code,
                model=ERPReturnOrders,
                describe="退货单增加明细商品",
                operate_content="明细单号：" + "、".join([str(created_obj.erp_sku_id) for created_obj in bulk_created_objs]),
                is_success_input=True,
            )
        except Exception as e:
            logger.error(f"set log params failed, {e}")
            pass
        return IResponse()

    def patch(self, request: Request, return_code: str):
        return_order = self._get_object(return_code)

        post_data = request.data
        detail_code = post_data.get("detail_code")
        if not detail_code:
            raise APIViewException(err_message="Invalid purchase detail codes")

        try:
            return_order_detail = return_order.erpreturnorderdetail_set.get(id=detail_code, is_deleted=False)
        except ERPReturnOrderDetail.DoesNotExist:
            raise DataNotFoundException

        raw_detail = copy.deepcopy(return_order_detail)
        post_data["update_user"] = self.current_user.user_id
        update_ser = ERPReturnOrderDetailUpdateSer(
            instance=return_order_detail,
            data=post_data,
            partial=True,
            many=False,
        )
        if not update_ser.is_valid():
            raise FieldsError(update_ser.errors)

        with transaction.atomic():
            updated_obj = update_ser.save()
            # 统计数据
            return_order.save_product_quantity_and_total_amount()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=return_code,
                model=ERPReturnOrders,
                describe=f"编辑退货单明细，明细单号:{detail_code}",
                operate_content=diff_models(raw_detail, updated_obj),
                is_success_input=True,
            )
        except Exception as e:
            logger.error(f"set log params failed, {e}")
            pass
        return IResponse()

    def delete(self, request: Request, return_code: str):
        detail_codes = request.data.get("detail_codes")
        if not detail_codes or not isinstance(detail_codes, list):
            raise APIViewException
        return_order = self._get_object(return_code)

        return_order_details = return_order.erpreturnorderdetail_set.filter(is_deleted=False, id__in=detail_codes)
        if len(return_order_details) != len(detail_codes):
            raise APIViewException(err_message="Invalid purchase detail codes")

        with transaction.atomic():
            return_order_details.update(
                is_deleted=True,
                update_user=self.current_user.user_id,
                update_date=timezone.now(),
            )
            # 统计数据
            return_order.save_product_quantity_and_total_amount()
        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=return_code,
                model=ERPReturnOrders,
                describe="删除退货单明细商品",
                operate_content="明细单号：" + "、".join([str(i) for i in detail_codes]),
                is_success_input=True,
            )
        except Exception as e:
            logger.error(f"set log params failed, {e}")
            pass
        return IResponse()


class ERPReturnOrderDownloadView(OPAPIView):
    def get(self, request: Request):
        label_prefetch = Prefetch("labels", PurchaseReturnLabel.objects.filter(is_deleted=False))
        return_orders = (
            ERPReturnOrders.objects.filter(is_deleted=False)
            .prefetch_related(
                "warehouse",
                "storage_party",
                "company",
                label_prefetch,
            )
            .order_by("-update_date")
        )

        _, page_obj_queryset, _ = custom_django_filter(
            request,
            return_orders,
            ReturnOrderListFilter,
            ERPReturnOrderListSer,
            need_serialize=False,
            force_order=False,
        )

        order_details = ERPReturnOrderDetail.objects.prefetch_related(
            "return_order",
            "return_order__storage_party",
            "return_order__company",
            "return_order__warehouse",
            "erp_sku",
            label_prefetch,
        ).filter(return_order__in=page_obj_queryset)

        # 按分页顺序处理采购单
        existing_order_map = {d.return_order_id: d for d in order_details}
        final_details = []

        # 按分页顺序遍历采购单
        for order in page_obj_queryset:
            # 如果存在明细则直接添加
            if order.id in existing_order_map:
                final_details.append(existing_order_map[order.id])
            # 没有明细时创建虚拟记录
            else:
                final_details.append(
                    ERPReturnOrderDetail(
                        return_order=order,
                        id=None,
                        erp_sku=None,
                        cost_price=None,
                        quantity=None,
                        amount=None,
                        retail_price=None,
                        retail_amount=None,
                    )
                )

        # 初始化导出资源时使用最终处理后的列表
        dataset = ERPReturnOrderDetailDownloadResource().export(queryset=final_details)
        xls_data = dataset.export("xlsx")
        response = HttpResponse(xls_data, content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chinese_prefix = f"退货单导出_{timestamp}.xlsx"
        encoded_filename = quote(chinese_prefix)
        response["Content-Disposition"] = f"attachment; filename*=UTF-8''{encoded_filename}"
        return response
