from erp_apps.erp_purchase.views.goods_receipt_order_views import (
    ERPGoodsReceiptOrderView,
    ERPGoodsReceiptOrderDetailView,
    ERPGoodsReceiptOrderLogView,
    ERPGoodsReceiptOrderProductDetailView,
    ERPGoodsReceiptOrderDownloadView,
    ERPGoodsReceiptOrderCopyView,
)
from erp_apps.erp_purchase.views.inbound_order_viewset import InboundOrderViewSet
from erp_apps.erp_purchase.views.inventory_viewset import (
    InventoryViewSet,
    InventoryChangeDetailsViewSet,
)
from erp_apps.erp_purchase.views.labels_views import (
    ERPPurchaseReceiptLabelsView,
    ERPPurchaseGoodsReceiptLabelsView,
    ERPPurchaseReturnLabelsView,
)
from erp_apps.erp_purchase.views.outbound_order_viewset import OutboundOrderViewSet
from erp_apps.erp_purchase.views.purchase_order_views import (
    PurchaseOrderView,
    PurchaseOrderDetailView,
    PurchaseOrderLogView,
    PurchaseOrderDownloadView,
    PurchaseOrderProductDetailView,
    PurchaseOrderCopyView,
)
from erp_apps.erp_purchase.views.return_order_views import (
    ERPReturnOrderView,
    ERPReturnOrderDetailView,
    ERPReturnOrderLogView,
    ERPReturnOrderProductDetailView,
    ERPReturnOrderDownloadView,
)
from erp_apps.erp_purchase.views.warehouse_position_viewset import WarehousePositionViewSet
from erp_apps.erp_purchase.views.warehouse_viewset import WarehouseViewSet
from erp_purchase.views.inventory_adjustment_order_viewset import InventoryAdjustmentOrderViewSet
