# -*- coding: utf-8 -*-
from collections import defaultdict

from django.db.models import F

from common.basics.exceptions import APIViewException
from common.basics.viewsets import SPModelViewSet
from common.models import DownloadTasksTypeChoices
from django.db import transaction
from django.utils import timezone
from erp_apps import logger
from erp_products.utils import decimal_to_base62
from erp_purchase.filters.outbound_order_filter import ERPOutboundOrderListFilter
from erp_purchase.models import (
    ERPOutboundOrder,
    ERPOutboundOrderDetail,
    ERPOutboundOrderDetailInventoryRelate,
    ERPSKUInventory,
    InventoryChangeDetails,
    InventoryAdjustmentOrderDetail,
)
from erp_purchase.models import ERPOutboundOrderPrintLog
from erp_purchase.serializers.outbound_order_serializers import (
    ERPOutboundOrderCreateSerializer,
    ERPOutboundOrderDetailSerializer,
    ERPOutboundOrderListSerializer,
    ERPOutboundOrderUpdateSerializer,
)
from erp_purchase.tasks.async_download_tasks import async_download_outbound_order_task
from erp_produce.models import ProduceWork
from erp_products.models import ERPProductSKUTID
from rest_framework.decorators import action
from stock_take.utils import tid_to_sku_map
from utils.caches import get_user_real_name_by_user_id
from utils.erp_download_utils.erp_async_download_task import ERPAsyncDownloadTaskReceiver
from utils.http_handle import FieldsError, IResponse, custom_django_filter


class OutboundOrderViewSet(SPModelViewSet):
    """
    出库单视图集

    采购退货
    """

    fronted_page = "出库单"
    resource_name = "出库单"
    lookup_field = "code"
    serializer_class = ERPOutboundOrderListSerializer
    filterset_class = ERPOutboundOrderListFilter

    def get_queryset(self):
        """
        获取当前用户公司的出库单列表
        """
        return (
            ERPOutboundOrder.objects.filter(
                company=self.current_user.company,
                is_deleted=False,
            )
            .prefetch_related(
                "warehouse",
                "warehouse_position",
                "supplier",
            )
            .order_by("-create_date")
        )

    def get_serializer_class(self):
        """
        根据不同的操作返回不同的序列化器
        """
        if self.action == "create":
            return ERPOutboundOrderCreateSerializer
        elif self.action == "edit":
            return ERPOutboundOrderUpdateSerializer
        return self.serializer_class

    def create(self, request, *args, **kwargs):
        """
        创建出库单
        """
        ser = self.get_serializer(data=request.data)
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        outbound = ser.save()

        self.log_operation(
            request,
            ERPOutboundOrder,
            describe=f"新建出库单: {outbound.code}",
            resource_id=outbound.code,
            is_success_input=True,
        )

        return IResponse(data={"code": outbound.code})

    def retrieve(self, request, *args, **kwargs):
        """
        获取出库单详情
        """
        outbound = self.get_no_filter_object()
        outbound_order_info = self.get_serializer(instance=outbound, many=False).data
        details_qs = outbound.erpoutboundorderdetail_set.all().order_by("-create_date")
        re_data, *_ = custom_django_filter(
            request,
            details_qs,
            None,
            ERPOutboundOrderDetailSerializer,
            force_order=False,
        )

        outbound_order_info["approve_user_name"] = get_user_real_name_by_user_id(outbound_order_info["approve_user"])
        outbound_order_info["print_times"] = 0
        # 获取打印数量
        print_log = ERPOutboundOrderPrintLog.objects.filter(outbound_order=outbound).first()
        if print_log:
            outbound_order_info["print_times"] = print_log.print_times
        return IResponse(data=re_data, outbound_order_info=outbound_order_info)

    def destroy(self, request, *args, **kwargs):
        """
        删除出库单
        """
        outbound_order = self.get_no_filter_object()
        if outbound_order.approve_status != 1:
            return IResponse(message="当前单据已完成,无法删除")
        if outbound_order.is_deleted:
            return IResponse(message="请勿重复操作")
        outbound_order.is_deleted = True
        outbound_order.update_user = self.current_user.user_id
        outbound_order.save()
        self.log_operation(
            request,
            ERPOutboundOrder,
            describe=f"删除采购退货单: {outbound_order.code}",
            resource_id=outbound_order.code,
            is_success_input=True,
        )
        return IResponse()

    def verify_rfid(self, request):
        """
        校验TID列表，判断是否在所属单的sku_inventory的tid_info中

        校验规则：
        1、判断TID是否在erp_sku_inventory库存中
        2、扣除对应出库单在仓数量
        3、新增对应库存流水明细

        请求参数：
        - code: 订单编号（路径参数）
        - tids: TID列表

        返回：
        - verified_count: 校验成功的TID数量
        - total_verified_count: 总校验数量
        """

        outbound_order = self.get_no_filter_object()
        if outbound_order.approve_status == 2 or outbound_order.status == 2:
            return IResponse(message="当前单据已完成/取消")

        tids = request.data.get("tids", [])
        if not tids:
            raise APIViewException(err_message="请选择RFID标签")

        # {141111326: {'fbVeYDl'}}
        sku_tid_map = tid_to_sku_map(tids)
        if not sku_tid_map:
            raise APIViewException(err_message="扫描信息有误，请重新扫描")

        # 获取订单的所有明细
        order_details = ERPOutboundOrderDetail.objects.prefetch_related("sku").filter(
            outbound_order=outbound_order,
            is_deleted=False,
            sku__sku_id__in=list(sku_tid_map.keys()),
        )

        # 判断是否在调整中
        InventoryAdjustmentOrderDetail.judge_sku_is_adjusting([detail.sku_id for detail in order_details])

        if not order_details.exists():
            raise APIViewException(err_message="所扫描的RFID标签无法用于该订单校验")

        # if sum([detail.verified_quantity for detail in order_details]) >= outbound_order.quantity:
        #     raise APIViewException(err_message="已全部校验完成，请勿重复校验")

        # 创建订单明细与SKU ID的映射
        order_detail_sku_map = {detail.sku.sku_id: detail for detail in order_details}

        # 需要关联的库存
        inventory_query_set = ERPSKUInventory.objects.filter(warehouse_id=outbound_order.warehouse_id)
        if outbound_order.warehouse_position_id:
            inventory_query_set = inventory_query_set.filter(warehouse_position_id=outbound_order.warehouse_position_id)
        basic_sku_inventory_qs = inventory_query_set.filter(sku__sku_id__in=list(sku_tid_map.keys()))

        tid_list = [iv for v in sku_tid_map.values() for iv in v]
        inventories = ERPSKUInventory.expand_tid_info_query(tid_list, sku_inventory_obj=basic_sku_inventory_qs)
        inventories_map = {inv.tid: inv for inv in inventories}

        # 遍历每个SKU的TID
        error_msg_list = []
        detail_inventory_relates = {}
        need_update_details = []
        verified_count = 0

        for sku_id, tids_set in sku_tid_map.items():
            # 检查该SKU是否在订单明细中
            if sku_id not in order_detail_sku_map:
                # logger.warning(f"SKU {decimal_to_base62(sku_id)} not found in order details")
                raise APIViewException(err_message=f"非法TID{decimal_to_base62(sku_id)}")

            detail = order_detail_sku_map[sku_id]
            # 每次提交都重置tid_info
            detail.tid_info = {}

            for tid in tids_set:
                # 检查库存明细
                if tid not in inventories_map:
                    error_msg_list.append(f"{decimal_to_base62(sku_id)}{tid}不在仓库明细中")
                    continue

                inventory = inventories_map[tid]
                verify_status = inventory.verify_status

                if inventory.status != 1:
                    error_msg_list.append(f"{decimal_to_base62(sku_id)}{tid}对应的库存已禁用")
                    continue

                # 非在仓状态不允许出库
                if verify_status not in ERPSKUInventory.in_warehouse_status:
                    error_msg_list.append(f"{decimal_to_base62(sku_id)}{tid}{ERPSKUInventory.get_tid_verify_status_display(verify_status)}")
                    continue

                # 构建关联键
                inventory_pk = inventory.pk
                relate_key = (detail.pk, inventory_pk)
                info = inventory.tid_info[tid]
                # tid_info = {tid: {"verify_status": "verified", "inventory_id": inventory_pk}}
                tid_info = {tid: info}

                # 更新detail的tid_info
                detail.tid_info.update(tid_info)
                verified_count += 1

                # 更新或创建库存关联
                if relate_key not in detail_inventory_relates:
                    detail_inventory_relates[relate_key] = ERPOutboundOrderDetailInventoryRelate(
                        outbound_order=outbound_order,
                        outbound_order_detail=detail,
                        sku_inventory_id=inventory_pk,
                        quantity=1,
                        tid_info=tid_info,
                    )
                else:
                    detail_inventory_relates[relate_key].quantity += 1
                    detail_inventory_relates[relate_key].tid_info.update(tid_info)

            need_update_details.append(detail)

        if error_msg_list:
            return IResponse(code=400, message=";".join(error_msg_list))

        # 判断需要更新的
        more_or_less_error_msg_list = []
        for need_update_detail in need_update_details:
            quantity = need_update_detail.quantity
            # 还没真正更新
            verified_quantity = len(need_update_detail.tid_info.keys())
            if quantity != verified_quantity:
                if verified_quantity > quantity:
                    more_than_quantity = verified_quantity - quantity
                    more_or_less_error_msg_list.append(
                        "{product_name}多扫了{more_than_quantity}件".format(
                            product_name=need_update_detail.sku.name,
                            more_than_quantity=more_than_quantity,
                        )
                    )
                else:
                    less_than_quantity = quantity - verified_quantity

                    more_or_less_error_msg_list.append(
                        "{product_name}少扫了{less_than_quantity}件".format(
                            product_name=need_update_detail.sku.name,
                            less_than_quantity=less_than_quantity,
                        )
                    )

        if more_or_less_error_msg_list:
            return IResponse(code=400, message=";".join(more_or_less_error_msg_list))

        with transaction.atomic():
            # 批量更新订单明细
            for detail in need_update_details:
                ERPOutboundOrderDetailInventoryRelate.objects.filter(outbound_order_detail=detail).delete()

                detail.verified_quantity = len(detail.tid_info)
                detail.save(update_fields=["tid_info", "verified_quantity"])

            # 明细关联
            ERPOutboundOrderDetailInventoryRelate.objects.bulk_create(
                detail_inventory_relates.values(),
                update_conflicts=True,
                unique_fields=["outbound_order", "outbound_order_detail", "sku_inventory"],
                update_fields=["quantity", "tid_info"],
            )

        # 计算总校验数量
        total_verified_count = sum(detail.verified_quantity for detail in order_details)
        return IResponse(data={"verified_count": verified_count, "total_verified_count": total_verified_count})

    def approveout(self, request):
        """
        审核
        :return:
        """
        outbound_order = self.get_no_filter_object()
        if outbound_order.approve_status == 2 or outbound_order.status == 2:
            return IResponse(message="当前单据已完成/取消")

        outbound_detail_mp = {}
        outbound_to_detail_mp = defaultdict(list)

        # 审核->待入库状态
        with transaction.atomic():
            for outbound_order in [outbound_order]:
                outbound_order_detail_list = ERPOutboundOrderDetail.objects.filter(outbound_order__in=[outbound_order])

                sku_pk_list = [detail.sku_id for detail in outbound_order_detail_list]
                InventoryAdjustmentOrderDetail.judge_sku_is_adjusting(sku_pk_list)

                for detail in outbound_order_detail_list:
                    outbound_detail_mp[detail.id] = detail
                    outbound_to_detail_mp[detail.outbound_order_id].append(detail)

                    if detail.tag_type == 1:
                        if len(detail.tid_info) != detail.quantity:
                            # raise APIViewException(err_message="请先完成RIFD校验！需要数量%s" % detail.quantity)
                            if detail.verified_quantity > detail.quantity:
                                more_than_quantity = detail.verified_quantity - detail.quantity
                                raise APIViewException(
                                    err_message="{product_name}多扫了{more_than_quantity}件".format(
                                        product_name=detail.sku.name,
                                        more_than_quantity=more_than_quantity,
                                    )
                                )
                            else:
                                less_than_quantity = detail.quantity - detail.verified_quantity
                                raise APIViewException(
                                    err_message="{product_name}少扫了{less_than_quantity}件".format(
                                        product_name=detail.sku.name,
                                        less_than_quantity=less_than_quantity,
                                    )
                                )

                    if detail.tag_type == 2:
                        # 获取所有可用库存，按创建日期排序（FIFO）
                        sku = detail.sku
                        q = dict(
                            warehouse=outbound_order.warehouse,
                            is_deleted=False,
                            in_warehouse_quantity__gt=0,
                            status=1,
                        )
                        if outbound_order.warehouse_position:
                            q["warehouse_position"] = outbound_order.warehouse_position

                        inventories = list(sku.erpskuinventory_set.filter(**q).order_by("create_date").only("id", "in_warehouse_quantity"))

                        quantity = detail.quantity
                        # 检查总可用库存是否足够
                        total_available = sum(inv.in_warehouse_quantity for inv in inventories)
                        if total_available < quantity:
                            raise APIViewException(err_message=f"商品 {sku.name} 库存不足，需要: {quantity}，可用: {total_available}")

                        # 创建库存关联记录的列表
                        inventory_relates = []
                        remaining_quantity = quantity

                        # 按FIFO规则分配库存
                        for inventory in inventories:
                            if remaining_quantity <= 0:
                                break

                            # 确定从当前库存分配的数量
                            allocation_quantity = min(remaining_quantity, inventory.in_warehouse_quantity)

                            # 添加到批量创建列表
                            inventory_relates.append(
                                ERPOutboundOrderDetailInventoryRelate(
                                    sku_inventory=inventory,
                                    outbound_order_id=detail.outbound_order_id,
                                    outbound_order_detail=detail,
                                    quantity=allocation_quantity,
                                )
                            )

                            # 更新剩余需要分配的数量
                            remaining_quantity -= allocation_quantity

                        # 批量创建库存关联记录
                        if inventory_relates:
                            ERPOutboundOrderDetailInventoryRelate.objects.bulk_create(inventory_relates)

                    # 库存关联
                    detail_inventories_relates = detail.erpoutboundorderdetailinventoryrelate_set.prefetch_related("sku_inventory").all()
                    for detail_inventories_relate in detail_inventories_relates:
                        inventory = detail_inventories_relate.sku_inventory

                        if not inventory:
                            continue

                        # 库存在仓数量
                        if inventory.in_warehouse_quantity < detail_inventories_relate.quantity:
                            raise APIViewException(err_message=f"商品({inventory.sku.name})库存已不足{detail.quantity},无法通过审核")

                        # 状态tid核验
                        if detail.tag_type == 1:
                            for tid, info in inventory.tid_info.items():
                                if tid in detail.tid_info and info["verify_status"] not in inventory.in_warehouse_status:
                                    raise APIViewException(err_message="该tid(%s)状态不是已验证状态，不允许操作" % tid)

                        # inventory扣除数量和删减tid_info
                        before_quantity = inventory.in_warehouse_quantity
                        after_quantity = inventory.in_warehouse_quantity - detail_inventories_relate.quantity

                        if inventory.tag_type == 1:
                            # leave_tid_info = {inv_tid: inv_info for inv_tid, inv_info in inventory.tid_info.items() if inv_tid not in detail.tid_info}
                            # inventory.tid_info = leave_tid_info

                            # 更新tid_info中的verify_status状态
                            for tid, tid_data in detail_inventories_relate.tid_info.items():
                                if tid not in inventory.tid_info:
                                    raise APIViewException(err_message=f"TID {tid} not found in inventory")

                                if outbound_order.outbound_type == 4:
                                    inventory.set_tid_purchase_status(tid, self.current_user.user_id)
                                elif outbound_order.outbound_type == 5:
                                    inventory.set_tid_other_status(tid, self.current_user.user_id)
                                elif outbound_order.outbound_type == 6:
                                    inventory.set_tid_extract_status(tid, self.current_user.user_id)
                        else:
                            if outbound_order.outbound_type == 4:
                                inventory.purchase_return_quantity += detail_inventories_relate.quantity
                            elif outbound_order.outbound_type == 5:
                                inventory.other_return_quantity += detail_inventories_relate.quantity
                            elif outbound_order.outbound_type == 6:
                                inventory.extract_quantity += detail_inventories_relate.quantity

                        inventory.save()

                        # 明细log
                        # 对应InventoryChangeDetails.change_type
                        outbound_type2change_type = {
                            4: InventoryChangeDetails.CHANGE_TYPE_24,
                            5: InventoryChangeDetails.CHANGE_TYPE_25,
                            6: InventoryChangeDetails.CHANGE_TYPE_26,
                        }
                        change_type = outbound_type2change_type[outbound_order.outbound_type]

                        InventoryChangeDetails.create_detail(
                            change_type,
                            detail.id,
                            **{
                                "inventory_id": inventory.id,
                                "company_id": self.current_user.company.company_id,
                                "sku_id": detail.sku_id,
                                "warehouse_id": outbound_order.warehouse_id,
                                "warehouse_position_id": outbound_order.warehouse_position_id,
                                "quantity": detail_inventories_relate.quantity,
                                "before_quantity": before_quantity,
                                "after_quantity": after_quantity,
                                "create_user": self.current_user.user_id,
                                "supplier_id": inventory.supplier_id,
                                "remark": "采购退/其他退货出库/领料出库",
                                "cost_price": detail.cost_price,
                            },
                        )

                        # 加工单领料完成
                        if outbound_order.outbound_type == 6:
                            work = ProduceWork.objects.get(pk=outbound_order.from_order_id)
                            work.claim_status = 1
                            work.save()

                # 主单
                outbound_order.approve_status = 2
                outbound_order.approve_date = timezone.now()
                outbound_order.approve_user = self.current_user.user_id
                outbound_order.save()

                self.log_operation(
                    request,
                    ERPOutboundOrder,
                    describe="出库审核",
                    operate_content="出库单已审核(%s)" % (outbound_order.code),
                    resource_id=outbound_order.code,
                    is_success_input=True,
                )

        return IResponse()

    def cancel_approveout(self, request):
        """
        反审核
        还原库存状态，减少售出数量，增加在仓数量
        还原入库数量，移除tid_info信息
        :return:
        """
        outbound_order = self.get_no_filter_object()
        if outbound_order.approve_status != 2:
            return IResponse(message="请先审核订单")

        outbound_order_detail_list = ERPOutboundOrderDetail.objects.filter(
            outbound_order__in=[outbound_order],
        )

        InventoryAdjustmentOrderDetail.judge_sku_is_adjusting([sku.pk for sku in outbound_order_detail_list])

        outbound_detail_mp = {}
        outbound_to_detail_mp = defaultdict(list)
        inventory_detail_mp = {}
        inventory_ids = []
        # 对应InventoryChangeDetails.change_type
        outbound_type2change_type = {4: InventoryChangeDetails.CHANGE_TYPE_24, 5: InventoryChangeDetails.CHANGE_TYPE_25}
        change_type = outbound_type2change_type[outbound_order.outbound_type]

        with transaction.atomic():
            for detail in outbound_order_detail_list:
                outbound_detail_mp[detail.id] = detail
                inventory_ids.append(detail.inventory_id)
                outbound_to_detail_mp[detail.outbound_order_id].append(detail)

                detail_inventories_relates = detail.erpoutboundorderdetailinventoryrelate_set.prefetch_related("sku_inventory").all()
                for detail_inventories_relate in detail_inventories_relates:
                    sku_inventory = detail_inventories_relate.sku_inventory

                    if not sku_inventory:
                        continue

                    if sku_inventory.tag_type == 1:
                        sku_inventory.tid_info.update(detail_inventories_relate.tid_info)

                        # 还原为已验证状态
                        for tid, tid_data in detail_inventories_relate.tid_info.items():
                            if tid in sku_inventory.tid_info and sku_inventory.tid_info[tid].get("verify_status") in ["purchase_return", "other_return"]:
                                sku_inventory.set_tid_verified_status(tid, self.current_user.user_id)

                        # 反审不清空
                        # detail_inventories_relate.tid_info = {}
                        # detail_inventories_relate.save()

                    else:
                        if outbound_order.outbound_type == 4:
                            sku_inventory.purchase_return_quantity -= detail.quantity
                        elif outbound_order.outbound_type == 5:
                            sku_inventory.other_return_quantity -= detail.quantity

                        # 普通标签物理删除关联关系
                        detail_inventories_relate.delete()

                    # 明细
                    InventoryChangeDetails.delete_detail(change_type, detail.id, update_user=self.current_user.user_id)

                    # 加工单领料状态：反审不变更领料状态

                    # detail.verified_quantity = 0
                    # detail.save()
                    sku_inventory.save()

            # 主单
            outbound_order.approve_status = 1
            outbound_order.approve_date = timezone.now()
            outbound_order.approve_user = self.current_user.user_id
            outbound_order.save()

            self.log_operation(
                request,
                ERPOutboundOrder,
                describe="出库反审",
                operate_content="出库单已反审(%s)" % (outbound_order.code),
                resource_id=outbound_order.code,
                is_success_input=True,
            )
        return IResponse()

    def update(self, request, code=None, *args, **kwargs):
        """
        校验 & 结束出库
        """
        if request.method == "PATCH":
            return self.verify_rfid(request)
        elif request.method == "PUT":
            # 审核
            return {
                "approveout": self.approveout,
                "cancel_approveout": self.cancel_approveout,
            }[request.GET.get("action")](request)
        return IResponse()

    @action(detail=True, methods=["put"])
    def edit(self, request, code=None):
        """
        编辑
        """
        outbound_order = self.get_no_filter_object()

        serializer = self.get_serializer(outbound_order, data=request.data, context={"request": request})
        if not serializer.is_valid():
            raise FieldsError(serializer.errors)

        serializer.save()

        self.log_operation(
            request,
            ERPOutboundOrder,
            describe=f"编辑出库单: {outbound_order.code}",
            resource_id=outbound_order.code,
            is_success_input=True,
        )

        return IResponse()

    @action(detail=False, methods=["post"])
    def download(self, request, *args, **kwargs):
        task = ERPAsyncDownloadTaskReceiver(
            request,
            "采购退货订单",
            DownloadTasksTypeChoices.OUTBOUND_ORDER,
            self.current_user.user_id,
            self.current_user_type,
            self.current_user.company,
            async_download_outbound_order_task,
        ).process_task()
        return IResponse(code=201, message="后台正在处理下载任务", data={"task_id": task.id})

    @action(detail=True, methods=["post"])
    def print_log(self, request, *args, **kwargs):
        outbound_order = self.get_no_filter_object()

        print_log = ERPOutboundOrderPrintLog.objects.filter(outbound_order=outbound_order).first()
        if print_log:
            ERPOutboundOrderPrintLog.objects.filter(outbound_order=outbound_order).update(print_times=F("print_times") + 1)
        else:
            ERPOutboundOrderPrintLog.objects.create(outbound_order=outbound_order, print_times=1)

        return IResponse()


class OutboundOrderDetailViewSet(SPModelViewSet):
    fronted_page = "出库明细"
    resource_name = "出库明细"
    lookup_field = "id"

    def get_queryset(self):
        return ERPOutboundOrderDetail.objects.filter(
            is_deleted=False,
            outbound_order__company=self.current_user.company,
            outbound_order__is_deleted=False,
        )

    @action(detail=True, methods=["post", "get"])
    def reprint_labels(self, request, outbound_detail_code=None):
        outbound_detail = self.get_object()

        outbound_order = outbound_detail.outbound_order
        if outbound_order.approve_status == 2:
            raise APIViewException(err_message="outbound order already approved, cannot reprint labels")

        # 打印标签类型
        if request.method == "GET":
            reprint_type = request.query_params.get("reprint_type", "1")
            if reprint_type not in ["1", "2"]:
                raise APIViewException(err_message="reprint_type must be 1 or 2")
            reprint_type = int(reprint_type)
        else:
            reprint_type = request.data.get("reprint_type", 1)  # 默认为续打(1)
            if reprint_type not in [1, 2]:
                raise APIViewException(err_message="reprint_type must be 1 or 2")

        sku = outbound_detail.sku
        if request.method == "GET":
            # 只获取标签，不加入到明细中
            reprint_quantity = request.query_params.get("reprint_quantity")
            if not reprint_quantity:
                raise APIViewException(err_message="reprint_quantity is required")
            if not str(reprint_quantity).isdigit():
                raise APIViewException(err_message="reprint_quantity must be a number")

            reprint_quantity = int(reprint_quantity)

            can_reprint_tids = []
            # 根据重打类型处理
            if reprint_type == 1:  # 续打标签
                # 如果是RFID标签，验证打印数量
                if outbound_detail.tag_type == 1 and reprint_quantity > outbound_detail.get_can_print_quantity():
                    raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

                # 生成TID信息
                tid_infos = ERPProductSKUTID.batch_generate_tid_info(reprint_quantity, outbound_order.outbound_code)
                can_reprint_tids = list(tid_infos.keys())

            elif reprint_type == 2:  # 补打标签
                # 验证补打数量
                if reprint_quantity > outbound_detail.get_reprint_quantity():
                    raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

                # 获取未验证的TID用于补打
                tid_infos = outbound_detail.tid_info or {}
                for tid, info in tid_infos.items():
                    if info.get("verify_status") == "unverified" and len(can_reprint_tids) < reprint_quantity:
                        can_reprint_tids.append(tid)

            return IResponse(
                data=[
                    {
                        "name": sku.name,
                        "spec_code": sku.spec_code,
                        "code": sku.product.code,
                        "tids": [f"{decimal_to_base62(sku.sku_id)}{tid}" for tid in can_reprint_tids],
                    }
                ]
            )
        elif request.method == "POST":
            tids = request.data.get("tids")
            if not tids:
                raise APIViewException(err_message="tids is required")
            # base62解密
            sku_tid_map = tid_to_sku_map(tids)
            if not sku_tid_map:
                raise APIViewException

            if sku.sku_id not in sku_tid_map:
                raise APIViewException(err_message="invalid tids")

            tid_list = [tid for v in sku_tid_map.values() for tid in v]
            # 是否在其他入库明细
            outbound_detail_exist_tid_list = ERPOutboundOrderDetail.expand_tid_info_query(tid_list).values_list("tid", flat=True)
            if outbound_detail_exist_tid_list:
                exist_tid_msg = ",".join([f"{decimal_to_base62(sku.sku_id)}{i}" for i in outbound_detail_exist_tid_list])
                logger.info(f"标签:{exist_tid_msg}已被使用，请刷新页面后重新获取标签1")
                raise APIViewException(err_message=f"标签:{exist_tid_msg}已被使用，请刷新页面后重新获取标签")

            inventory_tid_list = ERPSKUInventory.expand_tid_info_query(tid_list).values_list("tid", flat=True)
            if inventory_tid_list:
                exist_tid_msg = ",".join([f"{decimal_to_base62(sku.sku_id)}{i}" for i in inventory_tid_list])
                logger.info(f"标签:{exist_tid_msg}已被使用，请刷新页面后重新获取标签2")
                raise APIViewException(err_message=f"标签:{exist_tid_msg}已被使用，请刷新页面后重新获取标签")

            printed_tids = sku_tid_map[sku.sku_id]
            # 上传的tid数量
            reprint_quantity = len(printed_tids)
            if reprint_type == 1:
                # 如果是RFID标签，验证打印数量
                if outbound_detail.tag_type == 1 and reprint_quantity > outbound_detail.get_can_print_quantity():
                    raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

                # 在事务中更新数据库
                with transaction.atomic():
                    # 更新入库单明细TID信息
                    new_tid_info = ERPOutboundOrderDetail.batch_generate_tid_info(
                        printed_tids,
                        "reprint",
                        str(self.current_user.user_id),
                        "printed",
                    )

                    old_tid_info = outbound_detail.tid_info or {}
                    old_tid_info.update(new_tid_info)
                    outbound_detail.tid_info = old_tid_info
                    outbound_detail.print_count += len(printed_tids)
                    outbound_detail.save()

                # 记录操作日志
                self.log_operation(
                    request,
                    ERPOutboundOrder,
                    describe=f"续打标签：入库单号:{outbound_order.outbound_code}, 商品: {sku.name}. 数量: {len(printed_tids)}",
                    resource_id=outbound_detail_code,
                    is_success_input=True,
                )
                return IResponse()
            else:
                # 验证补打数量
                if reprint_quantity > outbound_detail.get_reprint_quantity():
                    raise APIViewException(err_message="print_quantity cannot be greater than can_print_quantity")

                # 校验tid是否在明细
                for printed_tid in printed_tids:
                    if printed_tid not in outbound_detail.tid_info:
                        raise APIViewException(err_message="非法TID：{}".format(decimal_to_base62(sku.sku_id)) + printed_tid)

                # 记录操作日志
                self.log_operation(
                    request,
                    ERPOutboundOrder,
                    describe=f"补打标签：入库单号:{outbound_order.outbound_code}, 商品: {sku.name}. 数量: {reprint_quantity}",
                    resource_id=outbound_detail_code,
                    is_success_input=True,
                )

                return IResponse()
