# -*- coding: utf-8 -*-
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.utils import timezone

from erp_purchase.models import InboundOrder, InboundOrderDetail, ERPSKUInventory
from erp_products.models import ERPSupplierProductSKU
from companies.models import Company
from users.models import User


class InboundOrderUpdateTestCase(TestCase):
    def setUp(self):
        # Create test company
        self.company = Company.objects.create(
            company_id=1,
            name="Test Company",
        )
        
        # Create test user
        self.user = User.objects.create(
            user_id="test_user",
            company=self.company,
        )
        
        # Create test inbound order
        self.inbound_order = InboundOrder.objects.create(
            inbound_code="TEST001",
            company=self.company,
            approve_status=1,  # Not approved
            create_user=self.user.user_id,
        )
        
        # Create test SKU
        self.sku1 = ERPSupplierProductSKU.objects.create(
            sku_id=1001,
            company=self.company,
            name="Test SKU 1",
            spec_code="SKU1001",
            cost_price=100,
            label_price=200,
        )
        
        self.sku2 = ERPSupplierProductSKU.objects.create(
            sku_id=1002,
            company=self.company,
            name="Test SKU 2",
            spec_code="SKU1002",
            cost_price=150,
            label_price=250,
        )
        
        # Create test inbound order detail
        self.inbound_detail = InboundOrderDetail.objects.create(
            inbound_detail_code="DETAIL001",
            inbound_order=self.inbound_order,
            sku=self.sku1,
            quantity=5,
            in_warehouse_quantity=5,
            cost_price=100,
            label_price=200,
            total_cost_price=500,
            total_label_price=1000,
            create_user=self.user.user_id,
        )
        
        # Setup API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
    def test_update_inbound_order(self):
        """Test updating an inbound order"""
        url = reverse('inbound-order-detail', kwargs={'inbound_code': self.inbound_order.inbound_code})
        
        data = {
            "product_info": [
                {
                    "inbound_detail_code": self.inbound_detail.inbound_detail_code,
                    "sku_id": self.sku1.sku_id,
                    "quantity": 10  # Changing quantity from 5 to 10
                },
                {
                    "sku_id": self.sku2.sku_id,
                    "quantity": 3  # Adding a new detail
                }
            ],
            "remark": "Updated remark",
            "remark_images": ["updated_image.png"]
        }
        
        response = self.client.put(url, data, format='json')
        
        # Check response status
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Refresh inbound order from database
        self.inbound_order.refresh_from_db()
        
        # Check if remark was updated
        self.assertEqual(self.inbound_order.remark, "Updated remark")
        self.assertEqual(self.inbound_order.remark_images, ["updated_image.png"])
        
        # Check if existing detail was updated
        self.inbound_detail.refresh_from_db()
        self.assertEqual(self.inbound_detail.quantity, 10)
        self.assertEqual(self.inbound_detail.in_warehouse_quantity, 10)
        self.assertEqual(self.inbound_detail.tid_info, {})  # Should be cleared
        
        # Check if new detail was created
        new_detail = InboundOrderDetail.objects.filter(
            inbound_order=self.inbound_order,
            sku=self.sku2
        ).first()
        
        self.assertIsNotNone(new_detail)
        self.assertEqual(new_detail.quantity, 3)
        self.assertEqual(new_detail.in_warehouse_quantity, 3)
        
    def test_update_inbound_order_with_inventory(self):
        """Test updating an inbound order detail that has inventory records"""
        # Create inventory record for the detail
        inventory = ERPSKUInventory.objects.create(
            inbound_order=self.inbound_order,
            inbound_order_detail=self.inbound_detail,
            sku=self.sku1,
            company=self.company,
            quantity=5,
            create_user=self.user.user_id,
        )
        
        url = reverse('inbound-order-detail', kwargs={'inbound_code': self.inbound_order.inbound_code})
        
        data = {
            "product_info": [
                {
                    "inbound_detail_code": self.inbound_detail.inbound_detail_code,
                    "sku_id": self.sku1.sku_id,
                    "quantity": 10  # Trying to change quantity from 5 to 10
                }
            ],
            "remark": "Updated remark",
            "remark_images": ["updated_image.png"]
        }
        
        response = self.client.put(url, data, format='json')
        
        # Should return error because detail has inventory record
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Refresh inbound detail from database
        self.inbound_detail.refresh_from_db()
        
        # Check that quantity was not updated
        self.assertEqual(self.inbound_detail.quantity, 5)
        
    def test_update_approved_inbound_order(self):
        """Test updating an already approved inbound order"""
        # Approve the inbound order
        self.inbound_order.approve_status = 2
        self.inbound_order.approve_date = timezone.now()
        self.inbound_order.approve_user = self.user.user_id
        self.inbound_order.save()
        
        url = reverse('inbound-order-detail', kwargs={'inbound_code': self.inbound_order.inbound_code})
        
        data = {
            "product_info": [
                {
                    "inbound_detail_code": self.inbound_detail.inbound_detail_code,
                    "sku_id": self.sku1.sku_id,
                    "quantity": 10
                }
            ],
            "remark": "Updated remark",
            "remark_images": ["updated_image.png"]
        }
        
        response = self.client.put(url, data, format='json')
        
        # Should return error because order is already approved
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
