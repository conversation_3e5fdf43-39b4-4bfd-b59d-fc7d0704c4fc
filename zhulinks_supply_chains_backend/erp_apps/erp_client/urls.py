# -*- coding: utf-8 -*-

from django.urls.conf import path

from erp_client.views import (
    ERPCompanyCustomerView,
    ERPCompanyCustomerDetailView,
    ERPCompanySupplierView,
    ERPCompanySupplierDetailView,
    ERPCompanySupplierEnumView,
    ERPCompanyCustomerEnumView,
)


urlpatterns = [
    # 供应商管理
    path("supplier", ERPCompanySupplierView.as_view(), name="supplier"),
    # 供应商枚举列表
    path("supplier/enum", ERPCompanySupplierEnumView.as_view(), name="supplier.enum"),
    # 供应商详情
    path("supplier/<int:supplier_id>", ERPCompanySupplierDetailView.as_view(), name="supplier.detail"),
    # 新增客户、列表
    path("customer", ERPCompanyCustomerView.as_view(), name="customer"),
    # 客户枚举列表
    path("customer/enum", ERPCompanyCustomerEnumView.as_view(), name="customer.enum"),
    # 客户详情、编辑、删除
    path("customer/<int:customer_id>", ERPCompanyCustomerDetailView.as_view(), name="customer.detail"),
]
