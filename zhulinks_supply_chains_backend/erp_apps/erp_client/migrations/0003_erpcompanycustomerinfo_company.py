# Generated by Django 5.1.7 on 2025-04-02 06:18

import django.db.models.deletion
from django.db import migrations, models


def rm_customer_info(apps, schema_editor):
    ERPCompanyCustomerInfo = apps.get_model("erp_client", "ERPCompanyCustomerInfo")
    ERPCompanyCustomerInfo.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_client", "0002_erpcompanycustomerinfo"),
    ]

    operations = [
        migrations.RunPython(rm_customer_info, reverse_code=migrations.RunPython.noop),
        migrations.AddField(
            model_name="erpcompanycustomerinfo",
            name="company",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="companies.company",
                to_field="company_id",
                verbose_name="所属供应商",
            ),
            preserve_default=False,
        ),
    ]
