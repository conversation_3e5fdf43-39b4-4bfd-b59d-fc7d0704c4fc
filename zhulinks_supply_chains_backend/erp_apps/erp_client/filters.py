# -*- coding: utf-8 -*-
import django_filters
from django_filters import FilterSet

from common.basic import HybridSearchFilterV2
from common.basics.filtersets import IContainCharField, DateTimeStartToEndFilter
from erp_client.models import ERPCompanySupplierInfo, ERPCompanyCustomerInfo


class ERPCompanySupplierInfoFilterSet(FilterSet):
    name = IContainCharField()
    code = IContainCharField()
    contact_user_mobile = IContainCharField()
    create_date = DateTimeStartToEndFilter()

    class Meta:
        model = ERPCompanySupplierInfo
        fields = (
            "name",
            "code",
            "contact_user_mobile",
        )


class ERPCompanyCustomerInfoFilterSet(FilterSet):
    name = IContainCharField()
    customer_id = django_filters.CharFilter(method="filter_customer_id")
    contact_user_mobile = IContainCharField()
    create_date = DateTimeStartToEndFilter()
    hybrid_search = HybridSearchFilterV2(field_names=("name", "customer_id"))

    class Meta:
        model = ERPCompanyCustomerInfo
        fields = (
            "name",
            "customer_id",
            "contact_user_mobile",
            "create_date",
            "hybrid_search",
        )

    @staticmethod
    def filter_customer_id(qs, name, value):
        try:
            int(value)
            return qs.filter(customer_id=value)
        except (ValueError, TypeError):
            return qs.none()
