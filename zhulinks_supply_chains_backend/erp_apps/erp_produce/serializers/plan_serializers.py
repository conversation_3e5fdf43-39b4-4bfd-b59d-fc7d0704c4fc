# -*- coding: utf-8 -*-
from django.db import transaction
from collections import defaultdict
from rest_framework import serializers
from utils.caches import get_user_real_name_by_user_id
from common.basic import CustomizeSerializer
from common.basics.exceptions import APIViewException
from erp_produce.models import ProducePlan, ProduceBom
from erp_products.models import ERPSupplierProductSKU
from erp_orders.models.erp_sales_orders_models import ERPSalesOrder
from erp_produce.serializers.bom_serializers import ProduceBomDetailSer


class ProducePlanCreateSer(CustomizeSerializer):
    bom_id = serializers.IntegerField(required=True, allow_null=False)
    # name = serializers.CharField(required=True, allow_null=False)
    plan_nums = serializers.IntegerField(required=True, allow_null=False)
    sales_order_id = serializers.IntegerField(required=False, allow_null=True)
    start_date = serializers.DateTimeField(required=True, allow_null=False)
    plan_done_date = serializers.DateTimeField(required=True, allow_null=False)
    remark = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = ProducePlan
        fields = (
            "bom_id",
            # "name",
            "plan_nums",
            "sales_order_id",
            "start_date",
            "plan_done_date",
            "remark",
        )

    def validate(self, attrs):
        _request = self.context["request"]

        bom = ProduceBom.objects.filter(pk=attrs["bom_id"])
        if not bom.exists():
            raise APIViewException(err_message="bom does not exist")

        attrs["bom"] = bom.first()

        if attrs.get("sales_order_id") and not ERPSalesOrder.objects.filter(pk=attrs["sales_order_id"]).exists():
            raise APIViewException(err_message="关联销售单不存在")

        return attrs

    def create(self, validated_data):
        _request = self.context["request"]
        _request_user = _request.user

        bom = validated_data["bom"]

        mrp = 0

        with transaction.atomic():
            plan = ProducePlan.objects.create(
                company=_request_user.company,
                bom=bom,
                sku=bom.sku,
                code=ProducePlan.generate_code(company_id=_request_user.company_id),
                sales_order_id=validated_data.get("sales_order_id"),
                mrp=mrp,
                plan_nums=validated_data["plan_nums"],
                remark=validated_data.get("remark", ""),
                start_date=validated_data["start_date"],
                plan_done_date=validated_data["plan_done_date"],
                create_user=_request_user.user_id,
                update_user=_request_user.user_id,
            )

            return plan


class ProducePlanUpdateSer(CustomizeSerializer):
    plan_nums = serializers.IntegerField(required=False, allow_null=True)
    sales_order_id = serializers.IntegerField(required=False, allow_null=True)
    start_date = serializers.DateTimeField(required=False, allow_null=True)
    plan_done_date = serializers.DateTimeField(required=False, allow_null=True)
    remark = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = ProducePlan
        fields = ("plan_nums", "sales_order_id", "start_date", "plan_done_date", "remark")

    def validate(self, attrs):
        _request = self.context["request"]

        if attrs.get("sales_order_id") and not ERPSalesOrder.objects.filter(pk=attrs["sales_order_id"]).exists():
            raise APIViewException(err_message="关联销售单不存在")

        return attrs

    def update(self, instance, validated_data):
        _request = self.context["request"]
        _request_user = _request.user

        with transaction.atomic():
            with transaction.atomic():
                for k, v in validated_data.items():
                    setattr(instance, k, v)
                    instance.save()

            return instance


class ProducePlanListSer(CustomizeSerializer):
    name = serializers.CharField(source="sku.name", allow_null=False)
    main_images = serializers.ListField(source="sku.main_images", allow_null=True)
    sku_code = serializers.CharField(source="sku.product.code", allow_null=True)
    spec_code = serializers.CharField(source="sku.spec_code", allow_null=True)
    spec_value = serializers.CharField(source="sku.spec_value", allow_null=True)
    approve_status_display = serializers.CharField(source="get_approve_status_display")
    done_status = serializers.SerializerMethodField()
    done_status_display = serializers.CharField(source="get_done_status_display")
    approve_user = serializers.SerializerMethodField()
    sales_order_code = serializers.CharField(source="sales_order.code", allow_null=True)

    class Meta:
        model = ProducePlan
        fields = (
            "id",
            "sku_id",
            "name",
            "main_images",
            "code",
            "sku_code",
            "spec_code",
            "spec_value",
            "approve_status",
            "approve_user",
            "approve_status_display",
            "mrp",
            "plan_nums",
            "done_nums",
            "sales_order_id",
            "sales_order_code",
            "start_date",
            "plan_done_date",
            "remark",
            "create_user",
            "done_status",
            "done_status_display",
            "create_date",
            "update_date",
        )

    @staticmethod
    def get_approve_user(obj):
        if obj.approve_user:
            return get_user_real_name_by_user_id(obj.approve_user)

    def get_done_status(self, obj):
        obj.set_status_expired()
        return obj.done_status


class ProducePlanDetailSer(CustomizeSerializer):
    # name = serializers.CharField(source="sku.name", allow_null=False)
    # main_images = serializers.ListField(source="sku.main_images", allow_null=True)
    # sku_code = serializers.CharField(source="sku.product.code", allow_null=True)
    # spec_code = serializers.CharField(source="sku.spec_code", allow_null=True)
    # spec_value = serializers.CharField(source="sku.spec_value", allow_null=True)
    # size = serializers.CharField(source="sku.size", allow_null=True)
    approve_status_display = serializers.CharField(source="get_approve_status_display")
    done_status_display = serializers.CharField(source="get_done_status_display")
    approve_user = serializers.SerializerMethodField()
    sales_order_code = serializers.CharField(source="sales_order.code", allow_null=True)

    bom = ProduceBomDetailSer()

    class Meta:
        model = ProducePlan
        fields = (
            "code",
            "bom_id",
            "sku_id",
            # "name",
            # "main_images",
            # "sku_code",
            # "spec_code",
            # "spec_value",
            # "size",
            "approve_status",
            "approve_user",
            "approve_status_display",
            "mrp",
            "plan_nums",
            "done_nums",
            "sales_order_id",
            "sales_order_code",
            "start_date",
            "plan_done_date",
            "remark",
            "create_user",
            "done_status",
            "done_status_display",
            "bom",
            "create_date",
            "update_date",
        )

    @staticmethod
    def get_approve_user(obj):
        if obj.approve_user:
            return get_user_real_name_by_user_id(obj.approve_user)

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        bom = ret["bom"]
        bom["sum_total_cose_price"] = float(bom["total_cost_price"]) * instance.plan_nums
        for detail in bom["details"]:
            # 商品所需总量
            detail["sum_nums"] = instance.plan_nums * detail["nums"]

        # MRP：物料商品的MRP数=物料商品的计划数量-物料商品库存数量-物料采购订单数量
        mrp = 0
        for detail in bom["details"]:
            mrp += detail["nums"] - detail["total_in_warehouse_quantity"]  # - detail["unapproved_inbound_quantity"]

        ret["mrp"] = mrp

        return ret


class ProducePlanStatisticsSer(CustomizeSerializer):
    # accessory_name = serializers.CharField(source="sku.product.accessory.name", allow_null=True)
    # purpose_name = serializers.CharField(source="purpose.name")
    main_images = serializers.ListField(source="sku.main_images", allow_null=True)
    sku_pk = serializers.IntegerField(source="sku_id", allow_null=True)
    sku_code = serializers.CharField(source="sku.product.code", allow_null=True)
    spec_code = serializers.CharField(source="sku.spec_code", allow_null=True)
    size = serializers.CharField(source="sku.size", allow_null=True)
    name = serializers.CharField(source="sku.name")
    spec_value = serializers.CharField(source="sku.spec_value", allow_null=True)
    unit_name = serializers.CharField(source="sku.unit.name", allow_null=True)
    # category_name = serializers.CharField(source="sku.category.name", allow_null=True)
    cost_price = serializers.DecimalField(source="sku.cost_price", max_digits=20, decimal_places=2)
    # 供货价
    label_price = serializers.DecimalField(source="sku.label_price", max_digits=20, decimal_places=2)
    # 供应价
    purchase_price = serializers.DecimalField(source="sku.purchase_price", max_digits=20, decimal_places=2)

    mrp = serializers.SerializerMethodField()
    main_image = serializers.SerializerMethodField()

    class Meta:
        model = ProducePlan
        fields = (
            "id",
            "code",
            "main_images",
            "main_image",
            "sku_pk",
            "sku_code",
            "spec_code",
            "size",
            "name",
            "spec_value",
            "unit_name",
            # "category_name",
            # "nums",
            "cost_price",
            "label_price",
            "purchase_price",
            "mrp",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 物料所需总量
        self.need_total_nums = defaultdict(int)
        self.producebomdetail_set = []
        self.plan2sku_map = defaultdict(list)

        for plan in self.instance:
            for detail in plan.bom.producebomdetail_set.all():
                if detail.sku_id not in self.need_total_nums:
                    self.producebomdetail_set.append(detail)

                self.need_total_nums[detail.sku_id] += detail.nums * plan.plan_nums

                self.plan2sku_map[plan.id].append(detail.sku_id)

        sku_pks = self.need_total_nums.keys()

        skus_qs = ERPSupplierProductSKU.objects.filter(pk__in=sku_pks)

        # 可用库存
        self.total_in_warehouse_quantity_map = ERPSupplierProductSKU.get_batch_sku_total_in_warehouse_quantity(skus_qs)

        # 冻结库存： 未审核的出库单
        # self.outbound_quantity_map = ERPOutboundOrderDetail.get_batch_extract_quantity(sku_pks)

        # 物料采购订单数量：未审核的入库单
        # self.inbound_quantity_map = InboundOrderDetail.get_batch_unapproved_quantity(sku_pks)

        # 物料
        # self.details_mp = {detail.sku_pk: detail for detail in ProduceBomDetailSkuSer(self.producebomdetail_set, many=True).data}

    def get_main_image(self, obj):
        if obj.sku.main_images:
            return obj.sku.main_images[0]
        return ""

    def get_mrp(self, obj):
        """
        所有计划的物料总mrp，
        物料缺货总数量=物料商品库存数量 + 物料采购订单数量- 生产计划中的商品所需总数量
        """

        mrp = 0
        for sku_id in self.plan2sku_map[obj.id]:
            child_mrp = (
                self.total_in_warehouse_quantity_map.get(sku_id, 0)
                # + self.inbound_quantity_map.get(sku_id, 0)
                - self.need_total_nums.get(sku_id, 0)
            )

            if child_mrp < 0:
                mrp += child_mrp

        return mrp


class ProducePlanSKUStatisticsSer(CustomizeSerializer):
    main_images = serializers.ListField(source="sku.main_images", allow_null=True)
    sku_pk = serializers.IntegerField(source="sku_id", allow_null=True)
    sku_code = serializers.CharField(source="sku.product.code", allow_null=True)
    spec_code = serializers.CharField(source="sku.spec_code", allow_null=True)
    size = serializers.CharField(source="sku.size", allow_null=True)
    name = serializers.CharField(source="sku.name")
    spec_value = serializers.CharField(source="sku.spec_value", allow_null=True)
    unit_name = serializers.CharField(source="sku.unit.name", allow_null=True)
    # category_name = serializers.CharField(source="sku.category.name", allow_null=True)
    cost_price = serializers.DecimalField(source="sku.cost_price", max_digits=20, decimal_places=2)
    # 供货价
    label_price = serializers.DecimalField(source="sku.label_price", max_digits=20, decimal_places=2)
    # 供应价
    purchase_price = serializers.DecimalField(source="sku.purchase_price", max_digits=20, decimal_places=2)

    mrp = serializers.SerializerMethodField()

    sales_order_ids = serializers.SerializerMethodField()
    # main_image导出使用
    main_image = serializers.SerializerMethodField()

    class Meta:
        model = ProduceBom
        fields = (
            "id",
            "main_images",
            "main_image",
            "sku_pk",
            "sku_code",
            "spec_code",
            "size",
            "name",
            "spec_value",
            "unit_name",
            # "category_name",
            # "nums",
            "cost_price",
            "label_price",
            "purchase_price",
            "mrp",
            "sales_order_ids",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 物料所需总量
        self.need_total_nums = defaultdict(int)
        self.producebomdetail_set = []
        self.bom2sku_map = defaultdict(list)
        self.bom2plan_nums = defaultdict(int)
        self.sales_order_id_list = []

        for bom in self.instance:
            # 多个计划指向同一个bom, 需要乘以计划生产的数量
            for plan in bom.produceplan_set.all():
                self.bom2plan_nums[bom.id] += plan.plan_nums
                self.sales_order_id_list.append(plan.sales_order_id)

            for detail in bom.producebomdetail_set.all():
                if detail.sku_id not in self.need_total_nums:
                    self.producebomdetail_set.append(detail)

                # 物料总数 = 计划生产总数量 * 加工单件所需数量
                self.need_total_nums[detail.sku_id] += detail.nums * self.bom2plan_nums[bom.id]

                self.bom2sku_map[bom.id].append(detail.sku_id)

        sku_pks = self.need_total_nums.keys()

        skus_qs = ERPSupplierProductSKU.objects.filter(pk__in=sku_pks)

        # 可用库存
        self.total_in_warehouse_quantity_map = ERPSupplierProductSKU.get_batch_sku_total_in_warehouse_quantity(skus_qs)

        # 冻结库存： 未审核的出库单
        # self.outbound_quantity_map = ERPOutboundOrderDetail.get_batch_extract_quantity(sku_pks)

        # 物料采购订单数量：未审核的入库单
        # self.inbound_quantity_map = InboundOrderDetail.get_batch_unapproved_quantity(sku_pks)

        # 销售单
        self.sales_order_id_mp = {sales_order.id: sales_order.code for sales_order in ERPSalesOrder.objects.filter(id__in=self.sales_order_id_list)}

    def get_main_image(self, obj):
        if not obj.sku.main_images:
            return ""
        return list(obj.sku.main_images)[0]

    def get_mrp(self, obj):
        """
        所有计划的物料总mrp，
        物料缺货总数量=物料商品库存数量 + 物料采购订单数量 - 生产计划中的商品所需总数量
        """

        mrp = 0
        for sku_id in self.bom2sku_map[obj.id]:
            child_mrp = (
                self.total_in_warehouse_quantity_map.get(sku_id, 0)
                # + self.inbound_quantity_map.get(sku_id, 0)
                - self.need_total_nums.get(sku_id, 0)
            )

            if child_mrp < 0:
                mrp += child_mrp

        return mrp

    def get_sales_order_ids(self, obj):
        """
        所有计划中的销售单
        """
        sales_order_ids = []
        for plan in obj.produceplan_set.all():
            code = self.sales_order_id_mp.get(plan.sales_order_id)
            if code:
                sales_order_ids.append(code)

        return ".".join(sales_order_ids)
