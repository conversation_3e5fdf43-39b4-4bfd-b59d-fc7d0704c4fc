from django.db import models
from datetime import datetime
from common.basics.models import BaseFieldsModel
from django.contrib.postgres.indexes import GinIndex
from utils.common import get_random, get_random_number_str




class ProduceProcess(BaseFieldsModel):
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属系统用户",
    )

    name = models.CharField("工序名称", max_length=80, blank=False, null=False, help_text="请输入工序名称")  # 必填项，前端可验证

    price = models.DecimalField("工序单价", max_digits=10, decimal_places=2, blank=True, null=True)  # 非必填，保留2位小数

    task_time = models.DecimalField("工时", max_digits=10, decimal_places=2, blank=True, null=True)  # 非必填

    dept = models.ForeignKey(
        "dept.Dept",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="部门",
        null=True,
        blank=True,
    )
    content = models.TextField("工序内容", max_length=200, blank=True, null=True)  # 新增内容字段

    remark = models.TextField("备注", blank=True, null=True)  # 新增备注字段

    class Meta:
        verbose_name = "工序"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=("company", "name", "dept"),
                condition=models.Q(is_deleted=False),
                name="unique_company_process_name_dept",
            )
        ]

    def __str__(self):
        return f"process_{self.pk}"

class ProducePurpose(BaseFieldsModel):
    name = models.CharField("用途", max_length=80, blank=False, null=False, help_text="请输入用途")  # 必填项，前端可验证
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属系统用户",
    )

    class Meta:
        verbose_name = "功能用途"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=("name", ),
                condition=models.Q(is_deleted=False),
                name="unique_purpose_name",
            )
        ]

    def __str__(self):
        return f"purpose_{self.pk}"