from django.db import models
from datetime import datetime
from common.basics.models import BaseFieldsModel
from django.contrib.postgres.indexes import GinIndex
from utils.common import get_random, get_random_number_str


class ProduceBom(BaseFieldsModel):
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属系统用户",
    )
    sku = models.ForeignKey(
        "erp_products.ERPSupplierProductSKU",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="指定生成目标SKU",
    )
    process = models.ManyToManyField("erp_produce.ProduceProcess", verbose_name="工序,一个或多个", blank=True)
    total_bom_nums = models.IntegerField("Bom数量", default=0)
    total_process_nums = models.IntegerField("工序数量", default=0)
    total_cost_price = models.DecimalField("成本价合计", max_digits=10, decimal_places=2, blank=True, null=True)
    remark = models.TextField("备注", max_length=200, blank=True, null=True, default="")

    class Meta:
        verbose_name = "BOM表"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=("company", "sku"),
                condition=models.Q(is_deleted=False),
                name="unique_company_sku_bom",
            )
        ]

    def __str__(self):
        return f"bom_{self.company}_{self.pk}"


class ProduceBomDetail(BaseFieldsModel):
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属系统用户",
    )
    bom = models.ForeignKey(
        "erp_produce.ProduceBom",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="bom",
    )
    sku = models.ForeignKey(
        "erp_products.ERPSupplierProductSKU",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="物料SKU",
    )
    name = models.CharField("物料名称", max_length=80, blank=False, null=False)
    purpose = models.ForeignKey(
        "erp_produce.ProducePurpose",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="功能",
    )
    nums = models.IntegerField("所需数量", default=1)
    cost_price = models.DecimalField("成本价(加权平均)", max_digits=10, decimal_places=2, blank=True, null=True)

    class Meta:
        verbose_name = "Bom物料表"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=("company", "bom", "sku"),
                condition=models.Q(is_deleted=False),
                name="unique_company_sku_bom_detail",
            )
        ]

    def __str__(self):
        return f"bom_detail_{self.pk}"

