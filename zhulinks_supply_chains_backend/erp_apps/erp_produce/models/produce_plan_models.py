from django.db import models
from django.utils import timezone
from datetime import datetime
from common.basics.models import BaseFieldsModel
from django.contrib.postgres.indexes import GinIndex
from utils.common import get_random, get_random_number_str



class ProducePlan(BaseFieldsModel):
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属系统用户",
    )

    code = models.CharField("计划单号", max_length=32, db_index=True)

    bom = models.ForeignKey(
        "erp_produce.ProduceBom",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="指定生成目标BOM",
    )

    sku = models.ForeignKey(
        "erp_products.ERPSupplierProductSKU",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="指定生成目标SKU",
    )

    sales_order = models.ForeignKey(
        "erp_orders.ERPSalesOrder",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联销售单",
        null=True,
        blank=True,
    )

    # MRP=物料商品的计划数量-物料商品库存数量-物料采购订单数量(各物料MRP相加之和)
    mrp = models.IntegerField("MRP运算", default=1)

    plan_nums = models.IntegerField("计划数量", default=0)

    done_nums = models.IntegerField("完成数量", default=0)

    approve_status = models.PositiveSmallIntegerField(
        "审核状态",
        choices=(
            (1, "待审核"),
            (2, "已审核"),
        ),
        default=1,
        db_index=True,
    )

    approve_user = models.CharField(verbose_name="审核人", default="", null=True, blank=True)

    done_status = models.PositiveSmallIntegerField(
        "完成状态",
        choices=(
            (1, "未完成"),
            (2, "已超时"),
            (3, "已完成"),
        ),
        default=1,
        db_index=True,
    )

    remark = models.TextField("备注", blank=True, null=True, default="")

    start_date = models.DateTimeField("计划开始时间", blank=True, null=True)

    plan_done_date = models.DateTimeField("计划完成时间", blank=True, null=True)

    class Meta:
        verbose_name = "生成计划"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=("code", "company"),
                condition=models.Q(is_deleted=False),
                name="unique_company_plan_code",
            )
        ]

    def __str__(self):
        return f"plan_{self.pk}"

    @classmethod
    def generate_code(cls, company_id, prefix: str = "SC"):
        return cls.generate_sequence_code(company_id, prefix)

    def get_done_status(self):
        if self.done_nums >= self.plan_nums:
            return 3
        elif timezone.now() > self.plan_done_date:
            return 2
        else:
            return 1

    def set_status_expired(self):
        """
        设为过期
        """
        status = self.get_done_status()
        if status == 2 and self.done_status != 2:
            self.done_status = 2
            self.save()
    def inbound_approve(self, nums):
        """
        审核入库单变更完成数量与状态
        :param company_id:
        :param inbound_code:
        :return:
        """
        self.done_nums += nums
        self.done_status = self.get_done_status()

    def inbound_cancel_approve(self, nums):
        """
        反审入库单变更完成数量与状态
        :param company_id:
        :param inbound_code:
        :return:
        """
        self.done_nums -= nums
        self.done_status = self.get_done_status()



