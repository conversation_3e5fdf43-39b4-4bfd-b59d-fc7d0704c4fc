# -*- coding: utf-8 -*-
from common.basic import HybridSearchFilter
from common.basics.filtersets import DateTimeStartToEnd<PERSON>ilter, IContainCharField
from django_filters import FilterSet, filters, Char<PERSON>ilter, NumberFilter
from erp_produce.models import ProduceWork


class ProduceWorkFilter(FilterSet):
    code = filters.CharFilter()
    plan_code = filters.CharFilter(field_name="plan__code")
    claim_status = filters.CharFilter()
    done_status = filters.CharFilter()
    start_date = DateTimeStartToEndFilter()
    plan_done_date = DateTimeStartToEndFilter()
    hybrid_search = HybridSearchFilter(
        field_names=[
            "plan__sku__name",
            "plan__sku__spec_code",
            "plan__sku__product__code",
        ]
    )

    class Meta:
        model = ProduceWork
        fields = (
            "code",
            "plan_code",
            "claim_status",
            "done_status",
            "start_date",
            "plan_done_date",
            "hybrid_search",
        )
