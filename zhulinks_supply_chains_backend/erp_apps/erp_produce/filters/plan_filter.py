# -*- coding: utf-8 -*-
from common.basic import HybridSearchFilter
from common.basics.filtersets import DateTimeStartToEndFilter
from django_filters import FilterSet, filters, BaseInFilter
from erp_produce.models import ProducePlan


class ProducePlanFilter(FilterSet):
    # code = filters.CharFilter()
    sales_order_id = filters.CharFilter()
    done_status = filters.CharFilter()
    start_date = DateTimeStartToEndFilter()
    plan_done_date = DateTimeStartToEndFilter()
    hybrid_search = HybridSearchFilter(
        field_names=[
            "code",
            "sku__name",
            "sku__spec_code",
            "sku__product__code",
        ]
    )
    ids = BaseInFilter(field_name="id")

    class Meta:
        model = ProducePlan
        fields = (
            "ids",
            "code",
            "sales_order_id",
            "done_status",
            "start_date",
            "plan_done_date",
            "hybrid_search",
        )
