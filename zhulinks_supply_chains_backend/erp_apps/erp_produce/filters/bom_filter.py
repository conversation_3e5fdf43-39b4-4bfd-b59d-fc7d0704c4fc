# -*- coding: utf-8 -*-
from common.basic import HybridSearchFilter
from common.basics.filtersets import DateTimeStartToEndFilter, IContainCharField
from django_filters import FilterSet, filters, Char<PERSON>ilter, NumberFilter, BaseInFilter
from erp_produce.models import ProduceBom, ProduceBomDetail


class ProduceBomFilter(FilterSet):
    # category = CharFilter(field_name="sku__category")
    # cost_price_min = NumberFilter(field_name="cost_price", lookup_expr="gte")
    # cost_price_max = NumberFilter(field_name="cost_price", lookup_expr="lte")

    create_date = DateTimeStartToEndFilter()
    update_date = DateTimeStartToEndFilter()

    hybrid_search = HybridSearchFilter(
        field_names=[
            "sku__name",
            "sku__spec_code",
            "sku__product__code",
        ]
    )

    ids = BaseInFilter(field_name="id")

    class Meta:
        model = ProduceBom
        fields = (
            "ids",
            "create_date",
            "update_date",
            "hybrid_search",
        )


class ProduceBomDetailFilter(FilterSet):
    create_date = DateTimeStartToEndFilter()
    update_date = DateTimeStartToEndFilter()

    hybrid_search = HybridSearchFilter(
        field_names=[
            "sku__name",
            "sku__spec_code",
            "sku__product__code",
        ]
    )

    class Meta:
        model = ProduceBomDetail
        fields = (
            "create_date",
            "update_date",
            "hybrid_search",
        )