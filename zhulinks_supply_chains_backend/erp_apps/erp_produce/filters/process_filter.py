# -*- coding: utf-8 -*-
from common.basic import HybridSearchFilter
from common.basics.filtersets import DateTimeStartToEnd<PERSON>ilter, IContainCharField
from django_filters import FilterSet, filters, CharFilter, NumberFilter
from erp_produce.models import ProduceBom


class ProduceProcessFilter(FilterSet):

    id = filters.CharFilter(field_name="pk")
    create_user = CharFilter(field_name="create_user")
    dept_id = filters.CharFilter(field_name="dept_id")

    create_date = DateTimeStartToEndFilter()
    update_date = DateTimeStartToEndFilter()

    hybrid_search = HybridSearchFilter(
        field_names=[
            "name",
        ]
    )

    class Meta:
        model = ProduceBom
        fields = (
            "id",
            "dept_id",
            "create_user",
            "create_date",
            "update_date",
            "hybrid_search",
        )


class ProducePurposeFilter(FilterSet):
    hybrid_search = HybridSearchFilter(
        field_names=[
            "name",
        ]
    )

    class Meta:
        model = ProduceBom
        fields = (
            "hybrid_search",
        )
