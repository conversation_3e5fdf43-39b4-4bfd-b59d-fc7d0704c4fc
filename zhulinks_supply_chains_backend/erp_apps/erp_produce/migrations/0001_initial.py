# Generated by Django 5.1.7 on 2025-06-14 08:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("dept", "0002_alter_dept_parent_delete_deptuser"),
        ("erp_orders", "0032_erporderdetailinventoryrelates_erp_order_and_more"),
        ("erp_products", "0050_alter_erpsupplierproductsku_label_price_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProduceBom",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.<PERSON>r<PERSON>ield(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "total_bom_nums",
                    models.IntegerField(default=0, verbose_name="Bom数量"),
                ),
                (
                    "total_process_nums",
                    models.IntegerField(default=0, verbose_name="工序数量"),
                ),
                (
                    "total_cost_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="成本价合计",
                    ),
                ),
                (
                    "remark",
                    models.TextField(
                        blank=True,
                        default="",
                        max_length=200,
                        null=True,
                        verbose_name="备注",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统用户",
                    ),
                ),
                (
                    "sku",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpsupplierproductsku",
                        verbose_name="指定生成目标SKU",
                    ),
                ),
            ],
            options={
                "verbose_name": "BOM表",
                "verbose_name_plural": "BOM表",
            },
        ),
        migrations.CreateModel(
            name="ProducePlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True, max_length=32, verbose_name="计划单号"
                    ),
                ),
                ("mrp", models.IntegerField(default=1, verbose_name="MRP运算")),
                ("plan_nums", models.IntegerField(default=0, verbose_name="计划数量")),
                ("done_nums", models.IntegerField(default=0, verbose_name="完成数量")),
                (
                    "approve_status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "待审核"), (2, "已审核")],
                        db_index=True,
                        default=1,
                        verbose_name="审核状态",
                    ),
                ),
                (
                    "approve_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="审核人"
                    ),
                ),
                (
                    "done_status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "未完成"), (2, "已超时"), (3, "已完成")],
                        db_index=True,
                        default=1,
                        verbose_name="完成状态",
                    ),
                ),
                (
                    "remark",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="备注"
                    ),
                ),
                (
                    "start_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="计划开始时间"
                    ),
                ),
                (
                    "plan_done_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="计划完成时间"
                    ),
                ),
                (
                    "bom",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_produce.producebom",
                        verbose_name="指定生成目标BOM",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统用户",
                    ),
                ),
                (
                    "sales_order",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_orders.erpsalesorder",
                        verbose_name="关联销售单",
                    ),
                ),
                (
                    "sku",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpsupplierproductsku",
                        verbose_name="指定生成目标SKU",
                    ),
                ),
            ],
            options={
                "verbose_name": "生成计划",
                "verbose_name_plural": "生成计划",
            },
        ),
        migrations.CreateModel(
            name="ProduceProcess",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="请输入工序名称",
                        max_length=80,
                        verbose_name="工序名称",
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="工序单价",
                    ),
                ),
                (
                    "task_time",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="工时",
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        blank=True, max_length=200, null=True, verbose_name="工序内容"
                    ),
                ),
                (
                    "remark",
                    models.TextField(blank=True, null=True, verbose_name="备注"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统用户",
                    ),
                ),
                (
                    "dept",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="dept.dept",
                        verbose_name="部门",
                    ),
                ),
            ],
            options={
                "verbose_name": "工序",
                "verbose_name_plural": "工序",
            },
        ),
        migrations.AddField(
            model_name="producebom",
            name="process",
            field=models.ManyToManyField(
                blank=True,
                to="erp_produce.produceprocess",
                verbose_name="工序,一个或多个",
            ),
        ),
        migrations.CreateModel(
            name="ProducePurpose",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="请输入用途", max_length=80, verbose_name="用途"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "功能用途",
                "verbose_name_plural": "功能用途",
            },
        ),
        migrations.CreateModel(
            name="ProduceBomDetail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("name", models.CharField(max_length=80, verbose_name="物料名称")),
                ("nums", models.IntegerField(default=1, verbose_name="所需数量")),
                (
                    "cost_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="成本价(加权平均)",
                    ),
                ),
                (
                    "bom",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_produce.producebom",
                        verbose_name="bom",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统用户",
                    ),
                ),
                (
                    "sku",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpsupplierproductsku",
                        verbose_name="物料SKU",
                    ),
                ),
                (
                    "purpose",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_produce.producepurpose",
                        verbose_name="功能",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bom物料表",
                "verbose_name_plural": "Bom物料表",
            },
        ),
        migrations.CreateModel(
            name="ProduceWork",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True, max_length=32, verbose_name="加工单"
                    ),
                ),
                ("plan_nums", models.IntegerField(default=0, verbose_name="计划数量")),
                (
                    "done_nums",
                    models.IntegerField(default=0, verbose_name="已验收数量"),
                ),
                (
                    "claim_status",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "待领料"), (1, "已领料")],
                        default=0,
                        verbose_name="领料状态",
                    ),
                ),
                (
                    "done_status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "未完成"), (2, "已超时"), (3, "已完成")],
                        db_index=True,
                        default=1,
                        verbose_name="完成状态",
                    ),
                ),
                (
                    "start_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="计划开始时间"
                    ),
                ),
                (
                    "plan_done_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="计划完成时间"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统用户",
                    ),
                ),
                (
                    "plan",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_produce.produceplan",
                        verbose_name="生产计划",
                    ),
                ),
            ],
            options={
                "verbose_name": "加工的单",
                "verbose_name_plural": "加工的单",
            },
        ),
        migrations.AddConstraint(
            model_name="produceplan",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("code",),
                name="unique_company_plan_code",
            ),
        ),
        migrations.AddConstraint(
            model_name="produceprocess",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("company", "name", "dept"),
                name="unique_company_process_name_dept",
            ),
        ),
        migrations.AddConstraint(
            model_name="producebom",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("company", "sku"),
                name="unique_company_sku_bom",
            ),
        ),
        migrations.AddConstraint(
            model_name="producepurpose",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("name",),
                name="unique_purpose_name",
            ),
        ),
        migrations.AddConstraint(
            model_name="producebomdetail",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("company", "bom", "sku"),
                name="unique_company_sku_bom_detail",
            ),
        ),
        migrations.AddConstraint(
            model_name="producework",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("code",),
                name="unique_company_work_order",
            ),
        ),
    ]
