# -*- coding: utf-8 -*-
from typing import Type
from django.db import transaction
from common.basics.exceptions import APIViewException
from common.basics.viewsets import SPModelViewSet
from rest_framework.decorators import action
from rest_framework.serializers import Serializer

from common.models import DownloadTasksTypeChoices
from erp_apps.erp_produce.models import ProducePlan, ProduceBom
from erp_apps.erp_produce.serializers.plan_serializers import (
    ProducePlanCreateSer,
    ProducePlanListSer,
    ProducePlanDetailSer,
    ProducePlanUpdateSer,
    ProducePlanStatisticsSer,
    ProducePlanSKUStatisticsSer,
)
from erp_apps.erp_produce.models import ProduceWork
from erp_apps.erp_produce.filters.plan_filter import ProducePlanFilter
from erp_apps.erp_produce.filters.bom_filter import ProduceBomFilter
from erp_produce.tasks.async_download_tasks import async_download_erp_product_plan_task
from utils.erp_download_utils.erp_async_download_task import ERPAsyncDownloadTaskReceiver
from utils.http_handle import FieldsError, IResponse


class PlanViewSet(SPModelViewSet):
    """
    1、审核后创建入库单；
        - 入库单审核后，完成数量增加；
        - 入库单反审后，完成数量减少
    2、反审后，清除所有入库单

    """

    fronted_page = "生产计划"
    resource_name = "生产计划"
    lookup_field = "pk"
    serializer_class = ProducePlanListSer
    filterset_class = ProducePlanFilter
    ordering = ["-create_date"]

    def get_queryset(self):
        queryset = ProducePlan.objects.filter(company=self.current_user.company, is_deleted=False)
        if self.action == "list":
            return queryset.prefetch_related("sku", "sku__product")
        elif self.action in ["statistics", "statistics_by_plan"]:
            return queryset.prefetch_related("sku")
        elif self.action == "statistics_by_sku":
            return ProduceBom.objects.filter(company_id=self.current_user.company, is_deleted=False).prefetch_related("sku")
        else:
            return queryset

    def get_serializer_class(self) -> Type[Serializer]:
        if self.action == "create":
            return ProducePlanCreateSer
        elif self.action == "update":
            return ProducePlanUpdateSer
        elif self.action == "statistics_by_plan":
            return ProducePlanStatisticsSer
        elif self.action == "statistics_by_sku":
            return ProducePlanSKUStatisticsSer
        return self.serializer_class

    def create(self, request, *args, **kwargs):
        ser = self.get_serializer(data=request.data, context={"request": request})
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        plan = ser.save()

        self.log_operation(
            request,
            ProducePlan,
            describe=f"新建生产计划: {plan.id}",
            resource_id=plan.id,
            is_success_input=True,
        )
        return IResponse(data={"id": plan.id})

    def retrieve(self, request, *args, **kwargs):
        plan = self.get_no_filter_object()
        data = ProducePlanDetailSer(plan, many=False).data
        return IResponse(data=data)

    def update(self, request, *args, **kwargs):
        plan = self.get_no_filter_object()
        if plan.approve_status == 2:
            raise APIViewException(err_message="该计划已审核通过，禁止编辑")

        serializer = self.get_serializer(plan, data=request.data, context={"request": request})
        if not serializer.is_valid():
            raise FieldsError(serializer.errors)

        serializer.save()

        self.log_operation(
            request,
            plan,
            describe=f"更新生产计划: {plan.id}",
            resource_id=plan.id,
            is_success_input=True,
        )

        return IResponse()

    def destroy(self, request, pk=None, *args, **kwargs):
        plan = self.get_no_filter_object()

        plan.is_deleted = True
        plan.update_user = str(self.current_user.user_id)
        plan.save()

        self.log_operation(
            request,
            plan,
            "删除生产计划",
            resource_id=plan.id,
            is_success_input=True,
        )
        return IResponse()

    @action(methods=["POST"], detail=False, url_path="approve")
    def approve(self, request):
        """审核"""

        ids = request.data.get("ids", [])
        if not ids:
            raise APIViewException(err_message="pls select orders")

        plans = ProducePlan.objects.filter(id__in=ids)

        for plan in plans:
            if plan.approve_status == 2:
                return IResponse(code=400, message=f"该计划已审核通过:{plan.id}")

        with transaction.atomic():
            for plan in plans:
                plan.approve_status = 2
                plan.approve_user = self.current_user.user_id
                plan.save()

                # 同步创建加工单
                work = ProduceWork.create_work_order(
                    plan.company_id,
                    plan.id,
                    plan_nums=plan.plan_nums,
                    create_user=self.current_user.user_id,
                    start_date=plan.start_date,
                    plan_done_date=plan.plan_done_date,
                )

                self.log_operation(
                    request,
                    ProduceWork,
                    describe="审核生产计划",
                    operate_content=f"生产计划已审核通过, 同步创建加工单:{work.id}",
                    resource_id=plan.id,
                    is_success_input=True,
                )

        return IResponse()

    @action(methods=["POST"], detail=False, url_path="cancel_approve")
    def cancel_approve(self, request):
        """
        反审核
        """

        ids = request.data.get("ids", [])
        if not ids:
            raise APIViewException(err_message="pls select orders")

        plans = ProducePlan.objects.filter(id__in=ids)

        for plan in plans:
            if plan.approve_status != 2:
                return IResponse(code=400, message=f"该计划未通过审核: {plan.id}")

            if plan.producework_set.filter(claim_status=1):
                return IResponse(code=400, message="该计划已领料加工，不允许反审")

        with transaction.atomic():
            for plan in plans:
                plan.approve_status = 1
                plan.save()

                # 同步清除加工单
                ProduceWork.delete_work_order(plan.company_id, plan.id)

                self.log_operation(
                    request,
                    ProduceWork,
                    describe="反审核生产计划",
                    operate_content="生产计划已审核通过, 同步删除加工单",
                    resource_id=plan.id,
                    is_success_input=True,
                )

        return IResponse()

    @action(methods=["GET"], detail=False, url_path="statistics")
    def statistics(self, request):
        """
        统计
        """
        queryset = self.get_queryset()

        nums = queryset.count()

        done_nums = queryset.filter(done_status=3).count()

        undone_nums = nums - done_nums

        # need_total_nums = defaultdict(int)
        # for plan in queryset:
        #     for detail in plan.bom.producebomdetail_set.all():
        #         need_total_nums[detail.sku_id] += detail.nums * plan.plan_nums
        #
        # fun = SkuMrpBySummaryFun()
        # mrp = fun.get_mrp(need_total_nums)

        mrp_list = ProducePlanStatisticsSer(queryset, many=True).data

        # 物料所需总量
        mrp_nums = sum(item["mrp"] for item in mrp_list)

        data = {
            "nums": nums,
            "done_nums": done_nums,
            "undone_nums": undone_nums,
            "mrp": mrp_nums,
        }
        return IResponse(data)

    @action(methods=["GET"], detail=False, url_path="statistics_by_plan")
    def statistics_by_plan(self, request):
        """
        统计mrp: 计划视角
        """
        return super().list(request)

    @action(methods=["GET"], detail=False, url_path="statistics_by_sku")
    def statistics_by_sku(self, request):
        """
        统计mrp: 商品视角
        """
        self.filterset_class = ProduceBomFilter
        return super().list(request)

    @action(detail=False, methods=["post"])
    def download(self, request, *args, **kwargs):
        """
        导出功能
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        task = ERPAsyncDownloadTaskReceiver(
            request,
            "生成计划",
            DownloadTasksTypeChoices.ERP_PRODUCT_PLAN,
            self.current_user.user_id,
            self.current_user_type,
            self.current_user.company,
            async_download_erp_product_plan_task,
        ).process_task()
        return IResponse(code=201, message="后台正在处理下载任务", data={"task_id": task.id})
