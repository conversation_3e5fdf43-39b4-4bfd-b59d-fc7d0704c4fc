# -*- coding: utf-8 -*-
import re
import uuid
from typing import Type
from rest_framework.decorators import action
from common.basics.viewsets import SPModelViewSet
from rest_framework.serializers import Serializer
from erp_apps.erp_produce.models import ProduceProcess, ProduceBom, ProducePurpose
from erp_apps.erp_produce.serializers.process_serializers import (
    ProduceProcessCreateSer,
    ProduceProcessListSer,
    ProduceProcessDetailSer,
    ProducePurposeListSer,
)
from erp_apps.erp_produce.filters.process_filter import ProduceProcessFilter, ProducePurposeFilter
from utils.http_handle import EmptyListResponse, FieldsError, IResponse, custom_django_filter



class ProcessViewSet(SPModelViewSet):

    fronted_page = "工序"
    resource_name = "工序"
    lookup_field = "pk"
    serializer_class = ProduceProcessListSer
    filterset_class = ProduceProcessFilter
    ordering = ["-create_date"]

    def get_queryset(self):
        if self.action == "bom_process_list":
            bom = ProduceBom.objects.filter(pk=self.request.query_params["bom_id"])
            if not bom.exists():
                return IResponse(code=400, message="bom does not exist")
            else:
                return bom.first().process.all()
        else:
            return ProduceProcess.objects.filter(company=self.current_user.company, is_deleted=False)

    def get_serializer_class(self) -> Type[Serializer]:
        if self.action == "create":
            return ProduceProcessCreateSer
        elif self.action == "update":
            return ProduceProcessCreateSer
        return self.serializer_class

    def create(self, request, *args, **kwargs):
        ser = self.get_serializer(data=request.data, context={"request": request})
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        process = ser.save()

        self.log_operation(
            request,
            ProduceProcess,
            describe=f"新建工序: {process.id}",
            resource_id=process.id,
            is_success_input=True,
        )
        return IResponse(data={"id": process.id})

    def retrieve(self, request, *args, **kwargs):
        process = self.get_no_filter_object()
        process = ProduceProcessDetailSer(process, many=False).data
        return IResponse(data=process)

    def update(self, request, *args, **kwargs):
        process = self.get_no_filter_object()
        serializer = self.get_serializer(process, data=request.data, context={"request": request})
        if not serializer.is_valid():
            raise FieldsError(serializer.errors)

        serializer.save()

        self.log_operation(
            request,
            process,
            describe=f"更新工序: {process.id}",
            resource_id=process.id,
            is_success_input=True,
        )

        return IResponse()

    def destroy(self, request, pk=None, *args, **kwargs):
        process = self.get_no_filter_object()

        process.is_deleted = True
        process.update_user = str(self.current_user.user_id)
        process.save()

        self.log_operation(
            request,
            process,
            "删除工序",
            resource_id=process.id,
            is_success_input=True,
        )
        return IResponse()

    @action(methods=["GET"], detail=False, url_path="bom_process_list")
    def bom_process_list(self, request):
        return super().list(request)


class PurposeViewSet(SPModelViewSet):

    fronted_page = "功能"
    resource_name = "功能"
    lookup_field = "pk"
    serializer_class = ProducePurposeListSer
    filterset_class = ProducePurposeFilter
    ordering = ["-create_date"]

    def get_queryset(self):
        return ProducePurpose.objects.filter(is_deleted=False, company=self.current_user.company)

    def get_serializer_class(self) -> Type[Serializer]:
        return self.serializer_class
