# -*- coding: utf-8 -*-
import re
import uuid
from typing import Type

from common.basics.exceptions import APIViewException, DataNotFoundException
from common.basics.viewsets import SPModelViewSet
from common.models import DownloadTasksTypeChoices, OperationLog, get_inbound_excel_parse_config
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import F, Func, Prefetch
from django.utils import timezone
from erp_apps import logger
from oss2 import exceptions as oss_exceptions
from rest_framework.decorators import action
from rest_framework.serializers import Serializer
from erp_apps.erp_produce.models import ProduceBom
from erp_apps.erp_produce.serializers.bom_serializers import (
    ProduceBomCreateSer,
    ProduceBomListSer,
    ProduceBomDetailSer,
)
from erp_apps.erp_produce.filters.bom_filter import ProduceBomFilter
from erp_products.models import ERPSupplierProductSKU
from erp_products.filters.erp_supplier_product_filters import ERPSupplierProductSKUFilter
from utils.http_handle import EmptyListResponse, FieldsError, IResponse, custom_django_filter
from erp_products.serializers_v2.erp_supplier_sku_serializers import (
    ERPSupplierProductSKUListSer,
    ERPSupplierProductSKUForBomSerializer
)


class BomViewSet(SPModelViewSet):

    fronted_page = "Bom表"
    resource_name = "Bom表"
    lookup_field = "pk"
    serializer_class = ProduceBomListSer
    filterset_class = ProduceBomFilter
    ordering = ["-create_date"]

    def get_queryset(self):
        queryset = ProduceBom.objects.filter(company=self.current_user.company, is_deleted=False)
        if self.action == "list":
            return queryset.prefetch_related("sku", "sku__product")
        elif self.action == "sku_list":
            return queryset.distinct("sku_id")
        else:
            return queryset

    def get_serializer_class(self) -> Type[Serializer]:
        if self.action == "create":
            return ProduceBomCreateSer
        elif self.action == "update":
            return ProduceBomCreateSer
        return self.serializer_class

    def create(self, request, *args, **kwargs):
        ser = self.get_serializer(data=request.data, context={"request": request})
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        bom = ser.save()

        self.log_operation(
            request,
            ProduceBom,
            describe=f"新建Bom: {bom.id}",
            resource_id=bom.id,
            is_success_input=True,
        )
        return IResponse(data={"id": bom.id})

    def retrieve(self, request, *args, **kwargs):
        bom = self.get_no_filter_object()
        data = ProduceBomDetailSer(bom, many=False).data
        return IResponse(data=data)

    def update(self, request, *args, **kwargs):
        bom = self.get_no_filter_object()
        serializer = self.get_serializer(bom, data=request.data, context={"request": request})
        if not serializer.is_valid():
            raise FieldsError(serializer.errors)

        serializer.save()

        self.log_operation(
            request,
            bom,
            describe=f"更新Bom: {bom.id}",
            resource_id=bom.id,
            is_success_input=True,
        )

        return IResponse()

    def destroy(self, request, pk=None, *args, **kwargs):
        bom = self.get_no_filter_object()

        bom.is_deleted = True
        bom.update_user = str(self.current_user.user_id)
        bom.save()

        self.log_operation(
            request,
            bom,
            "删除Bom表",
            resource_id=bom.id,
            is_success_input=True,
        )
        return IResponse()


    @action(methods=["GET"], detail=False, url_path="sku_list")
    def bom_sku_list(self, request):
        """
        已存在bom表的sku
        """
        bom_list = self.get_queryset().values("id", "sku_id")
        bom_id_mp = {i["sku_id"]: i["id"] for i in bom_list}
        sku_ids = [i["sku_id"] for i in bom_list]
        queryset = ERPSupplierProductSKU.objects.filter(pk__in=sku_ids)

        re_data, _, _ = custom_django_filter(
            request,
            queryset,
            ERPSupplierProductSKUFilter,
            ERPSupplierProductSKUListSer,
            force_order=False,
        )

        for data in re_data["data"]:
            data["bom_id"] = bom_id_mp.get(data["skuid"], None)

        return IResponse(data=re_data)

    @action(methods=["GET"], detail=False, url_path="sku_sf_list")
    def sku_sf_list(self, request):
        """
        bom表的物料sku
        """
        # product_types = request.query_params.get("product_type", "SF,RM,PM").split(",")
        queryset = ERPSupplierProductSKU.objects.filter(
            company=self.current_user.company,
            is_deleted=False
        ).order_by("-create_date")

        re_data, _, _ = custom_django_filter(
            request,
            queryset,
            ERPSupplierProductSKUFilter,
            # ERPSupplierProductSKUListSer,
            ERPSupplierProductSKUForBomSerializer,
        )

        return IResponse(data=re_data)