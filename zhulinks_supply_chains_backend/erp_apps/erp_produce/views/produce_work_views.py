# -*- coding: utf-8 -*-
import re
import uuid
from typing import Type
from common.basics.exceptions import APIViewException, DataNotFoundException
from common.basics.viewsets import SPModelViewSet
from rest_framework.decorators import action
from rest_framework.serializers import Serializer
from erp_apps.erp_produce.models import ProduceWork
from erp_apps.erp_produce.serializers.work_serializers import (
    ProduceWorkListSer,
    ProduceWorkVerifySer,
)
from erp_apps.erp_produce.filters.work_filter import ProduceWorkFilter
from utils.http_handle import EmptyListResponse, FieldsError, IResponse, custom_django_filter



class WorkViewSet(SPModelViewSet):
    """
    1、加工单验收后，完成数量增加，同时新建入库单
    2、入库单取消后，完成数量扣除
    """

    fronted_page = "加工单"
    resource_name = "加工单"
    lookup_field = "pk"
    serializer_class = ProduceWorkListSer
    filterset_class = ProduceWorkFilter
    ordering = ["-create_date"]

    def get_queryset(self):
        queryset = ProduceWork.objects.filter(company=self.current_user.company, is_deleted=False)
        if self.action == "list":
            return queryset.prefetch_related("plan", "plan__sku")
        else:
            return queryset

    def get_serializer_class(self) -> Type[Serializer]:
        return self.serializer_class

    def create(self, request, *args, **kwargs):
        ser = self.get_serializer(data=request.data, context={"request": request})
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        work = ser.save()

        self.log_operation(
            request,
            ProduceWork,
            describe=f"新建生产计划: {work.id}",
            resource_id=work.id,
            is_success_input=True,
        )
        return IResponse(data={"id": work.id})

    def retrieve(self, request, *args, **kwargs):
        work = self.get_no_filter_object()
        data = ProduceWorkListSer(work, many=False).data
        return IResponse(data=data)

    def update(self, request, *args, **kwargs):
        work = self.get_no_filter_object()
        serializer = self.get_serializer(work, data=request.data, context={"request": request})
        if not serializer.is_valid():
            raise FieldsError(serializer.errors)

        serializer.save()

        self.log_operation(
            request,
            work,
            describe=f"更新生产计划: {work.id}",
            resource_id=work.id,
            is_success_input=True,
        )

        return IResponse()

    def destroy(self, request, pk=None, *args, **kwargs):
        work = self.get_no_filter_object()

        work.is_deleted = True
        work.update_user = str(self.current_user.user_id)
        work.save()

        self.log_operation(
            request,
            work,
            "删除生产计划",
            resource_id=work.id,
            is_success_input=True,
        )
        return IResponse()

    @action(methods=["POST"], detail=False, url_path="verify")
    def verify_work(self, request):
        """
        加工单验收: 创建入库单
        """
        ser = ProduceWorkVerifySer(data=request.data, context={"request": request})
        if not ser.is_valid():
            raise FieldsError(ser.errors)

        work = ser.save()

        self.log_operation(
            request,
            ProduceWork,
            describe=f"验收加工单id={work.id}, 入库单code={work.inbound_order.inbound_code}",
            resource_id=work.id,
            is_success_input=True,
        )
        return IResponse(data={"id": work.id})
