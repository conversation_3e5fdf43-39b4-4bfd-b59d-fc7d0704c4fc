from decimal import Decimal

from django.db import models
from django.utils import timezone

from common.basics.models import BaseFieldsModel


class StockTakeAdjustOrder(BaseFieldsModel):
    stock_take_order = models.ForeignKey(
        "stock_take.StockTakeOrder",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联盘点单",
    )

    code = models.CharField("调整单号", max_length=32, db_index=True, editable=False)
    status = models.PositiveSmallIntegerField(
        "审核状态",
        choices=(
            (1, "待审核"),
            (2, "已审核"),
        ),
        default=1,
    )
    original_quantity = models.IntegerField("原库存数", default=0)
    adjusted_quantity = models.IntegerField("调整后库存数", default=0)
    remark = models.TextField("备注", blank=True)
    approve_user = models.CharField(verbose_name="审核人员", default="", null=True, blank=True)
    approve_time = models.DateTimeField("审核时间", null=True, blank=True)
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="关联系统用户供应商",
    )

    def save(self, *args, **kwargs):
        if not self.code:
            self.code = self.generate_code()
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "盘点单调整单"
        verbose_name_plural = verbose_name
        unique_together = ("code", "company")

    def generate_code(self):
        return self.generate_sequence_code(self.stock_take_order.company_id, "KT")


class StockTakeAdjustOrderDetail(BaseFieldsModel):
    adjust_order = models.ForeignKey(
        StockTakeAdjustOrder,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联调整单",
        related_name="details",
    )
    stock_take_order_result = models.ForeignKey(
        "stock_take.StockTakeOrderResult",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联盘点单结果",
    )
    sku = models.ForeignKey(
        "erp_products.ERPSupplierProductSKU",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联sku",
    )
    warehouse = models.ForeignKey(
        "erp_purchase.ERPSupplierWarehouse",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="仓库",
    )
    warehouse_position = models.ForeignKey(
        "erp_purchase.ERPSupplierWarehousePosition",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="仓位",
        null=True,
        blank=True,
    )
    erp_sku_inventory = models.ForeignKey(
        "erp_purchase.ERPSKUInventory",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联库存明细",
    )
    cost_price = models.DecimalField("成本价", max_digits=10, decimal_places=2, default=Decimal(0))
    label_price = models.DecimalField("标签价", max_digits=10, decimal_places=2, default=Decimal(0))
    market_price = models.DecimalField("市场|吊牌价", max_digits=10, decimal_places=2, null=True, blank=True)
    purchase_price = models.DecimalField("采购价", max_digits=10, decimal_places=2, null=True, blank=True)
    original_quantity = models.IntegerField("原库存", default=0)
    adjust_quantity = models.IntegerField("调整量", default=0)
    adjusted_quantity = models.IntegerField("调整后库存", default=0)
    remark = models.TextField("调整备注", blank=True)
    # 用jsonfield方便扩展
    # {
    #     "tid": {
    #         "create_date": datetime_str
    #     }
    # }
    tid_info = models.JSONField("标签信息", null=True, blank=True, default=dict)

    def save(self, *args, **kwargs):
        # 计算盘点后的库存
        self.adjusted_quantity = self.original_quantity + self.adjust_quantity
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "盘点调整单明细"
        verbose_name_plural = verbose_name
        ordering = ["-id"]

    @staticmethod
    def generate_tid_info(tid):
        return {tid: {"create_date": str(timezone.now())}}

    @classmethod
    def batch_generate_tid_info(cls, tid_list):
        re_data = {}
        for tid in tid_list:
            re_data.update(cls.generate_tid_info(tid))

        return re_data
