from rest_framework import serializers

from common.basic import CustomizeSerializer
from utils.caches import get_user_real_name_by_user_id
from ..models import StockTakeAdjustOrder, StockTakeAdjustOrderDetail


class StockTakeAdjustOrderSer(CustomizeSerializer):
    stock_take_code = serializers.CharField(source="stock_take_order.order_code")
    adjusted_loss_quantity = serializers.SerializerMethodField()
    adjusted_plus_quantity = serializers.SerializerMethodField()
    approve_user = serializers.SerializerMethodField()
    status_display = serializers.CharField(source="get_status_display")

    class Meta:
        model = StockTakeAdjustOrder
        fields = (
            "code",
            "stock_take_code",
            "original_quantity",
            "adjusted_quantity",
            "adjusted_loss_quantity",
            "adjusted_plus_quantity",
            "status",
            "status_display",
            "create_user",
            "remark",
            "create_date",
            "approve_user",
            "approve_time",
        )

    @staticmethod
    def get_adjusted_loss_quantity(obj: StockTakeAdjustOrder):
        if obj.adjusted_quantity > obj.original_quantity:
            return 0
        return obj.original_quantity - obj.adjusted_quantity

    @staticmethod
    def get_adjusted_plus_quantity(obj: StockTakeAdjustOrder):
        if obj.adjusted_quantity < obj.original_quantity:
            return 0
        return obj.original_quantity - obj.adjusted_quantity

    @staticmethod
    def get_approve_user(obj: StockTakeAdjustOrder):
        return get_user_real_name_by_user_id(obj.approve_user)


class StockTakeAdjustOrderProductDetailSer(CustomizeSerializer):
    # 商品编码
    spec_code = serializers.CharField(source="sku.spec_code")
    # 货号
    code = serializers.CharField(source="sku.product.code", allow_null=True)
    # 商品名称
    product_name = serializers.CharField(source="sku.name")
    # 仓库
    warehouse_name = serializers.CharField(source="warehouse.name")
    # 仓位
    warehouse_position_name = serializers.CharField(source="warehouse_position.name", allow_null=True)
    # 原库存
    original_quantity = serializers.IntegerField()
    # 调整后库存
    adjusted_quantity = serializers.IntegerField()
    # 调整量
    adjust_quantity = serializers.IntegerField()
    # 单位
    unit_name = serializers.CharField(source="sku.unit.name", allow_null=True)
    # 审核状态
    status = serializers.IntegerField(source="adjust_order.status")
    status_display = serializers.CharField(source="adjust_order.get_status_display")
    # 批次
    batch_no = serializers.CharField(source="erp_sku_inventory.inbound_order_detail.batch_no")
    # 供应商
    supplier_name = serializers.CharField(source="erp_sku_inventory.supplier.name", allow_null=True)
    # 标签单价
    label_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    # 成本单价
    cost_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    # 市场单价
    market_price = serializers.DecimalField(max_digits=10, decimal_places=2, allow_null=True)
    # 入库单号
    inbound_order_code = serializers.CharField(source="erp_sku_inventory.inbound_order.inbound_code")
    # 采购方式
    purchase_type = serializers.IntegerField(source="erp_sku_inventory.purchase_type", allow_null=True)
    purchase_type_display = serializers.IntegerField(source="erp_sku_inventory.get_purchase_type_display", allow_null=True)

    class Meta:
        model = StockTakeAdjustOrderDetail
        fields = (
            "spec_code",
            "code",
            "product_name",
            "warehouse_name",
            "warehouse_position_name",
            "original_quantity",
            "adjusted_quantity",
            "adjust_quantity",
            "unit_name",
            "status",
            "status_display",
            "batch_no",
            "supplier_name",
            "label_price",
            "cost_price",
            "market_price",
            "inbound_order_code",
            "purchase_type",
            "purchase_type_display",
        )
