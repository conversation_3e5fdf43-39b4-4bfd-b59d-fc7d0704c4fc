# -*- coding: utf-8 -*-
from common import logger
from common.basics.exceptions import APIViewException
from common.serializers import CustomizeSerializer
from common.utils import CategoryFindAncestorsCache
from django.core.paginator import Page
from django.db import transaction
from django.db.models import Q
from erp_products.models import ERPProductCategory
from erp_products.utils import decimal_to_base62
from erp_purchase.models import ERPSKUInventory, ERPSupplierWarehouse, ERPSupplierWarehousePosition
from rest_framework import serializers
from stock_take.models import StockTakeOrder, StockTakeOrderLocations, StockTakeOrderResult, StockTakeOrderResultInboundOrderRelate
from utils.caches import get_user_real_name_by_user_id
from utils.http_handle import FieldsError


class StockTakeOrderLocationsSer(CustomizeSerializer):
    warehouse_id = serializers.IntegerField(required=True)
    warehouse_position_id = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        model = StockTakeOrderLocations
        fields = (
            "stock_take_order",
            "warehouse_id",
            "warehouse_position_id",
        )

    def validate(self, attrs):
        _user = self.context["request"].user

        warehouse_id = attrs.get("warehouse_id")
        warehouse_position_id = attrs.get("warehouse_position_id")

        if not warehouse_position_id:
            if not ERPSupplierWarehouse.objects.filter(
                id=warehouse_id,
                company=_user.company,
                is_deleted=False,
            ).exists():
                raise APIViewException(err_message="invalid warehouse id")
        elif not ERPSupplierWarehousePosition.objects.filter(
            id=warehouse_position_id,
            company=_user.company,
            is_deleted=False,
        ).exists():
            raise APIViewException(err_message="invalid warehouse position id")

        return attrs


class StockTakeOrderCreateSer(CustomizeSerializer):
    locations = serializers.ListSerializer(child=serializers.DictField(), required=True)
    sku_inventory_ids = serializers.ListSerializer(child=serializers.CharField(), required=False)
    check_method = serializers.IntegerField(required=False)

    class Meta:
        model = StockTakeOrder
        fields = (
            "name",
            "check_type",
            "check_method",
            "locations",
            "sku_inventory_ids",
            "remark",
        )

    def validate_locations(self, locations):
        warehouse_id_list = [loc.get("warehouse_id") for loc in locations if loc.get("warehouse_id")]
        if not warehouse_id_list:
            raise APIViewException(err_message="invalid warehouse id")

        _request = self.context["request"]
        request_user = _request.user
        sku_inventory_ids = self.initial_data.get("sku_inventory_ids")

        # 如果是选品进行盘点，盘点该品是否存在盘点
        if sku_inventory_ids:
            exist_stock_take_sku_names = StockTakeOrderResultInboundOrderRelate.objects.filter(
                stock_take_order__company=request_user.company,
                stock_take_order__status=1,
                erp_sku_inventory_id__in=sku_inventory_ids,
            ).values_list("erp_sku_inventory__sku__name", flat=True)

            if exist_stock_take_sku_names:
                raise APIViewException(err_message="{} 正在盘点，无法继续盘点.".format("、".join(exist_stock_take_sku_names)))

            return locations

        for loc in locations:
            warehouse_id = loc.get("warehouse_id")
            warehouse_position_id = loc.get("warehouse_position_id")

            # 如果没有仓位id， 判断该仓库是否有进行中的盘点单
            if not warehouse_position_id:
                # 判断仓库是否有进行中盘点单
                if StockTakeOrder.objects.filter(
                    stocktakeorderlocations__warehouse_id=warehouse_id,
                    status=1,
                    company=_request.user.company,
                ).exists():
                    raise APIViewException(err_message="warehouse has a stock take order in processing")
            else:
                # 如果有仓位id，先判断有仓库且仓位为null的是否有进行中的盘点单
                if StockTakeOrder.objects.filter(
                    stocktakeorderlocations__warehouse_id=warehouse_id,
                    stocktakeorderlocations__warehouse_position_id__isnull=True,
                    status=1,
                    company=_request.user.company,
                ).exists():
                    raise APIViewException(err_message="warehouse has a stock take order in processing")

                # 判断仓位是否有进行中盘点单
                if StockTakeOrder.objects.filter(
                    stocktakeorderlocations__warehouse_id=warehouse_id,
                    stocktakeorderlocations__warehouse_position_id=warehouse_position_id,
                    status=1,
                    company=_request.user.company,
                ).exists():
                    raise APIViewException(err_message="warehouse has a stock take order in processing")

        return locations

    def create(self, validated_data):
        _request = self.context["request"]
        _request_user = _request.user

        validated_data["create_user"] = _request_user.user_id
        validated_data["company"] = _request_user.company

        locations = validated_data.pop("locations", [])
        sku_inventory_ids = validated_data.pop("sku_inventory_ids", [])

        # 盘点方式默认是1
        if "check_method" not in validated_data:
            validated_data["check_method"] = 1
        elif validated_data["check_method"] is None:
            validated_data["check_method"] = 1
        elif validated_data["check_method"] not in [1, 2]:
            raise APIViewException(err_message="invalid check method")

        with transaction.atomic():
            stock_order = super().create(validated_data)

            # 处理仓库仓位信息
            loc_objects = self._process_locations(locations, stock_order, _request)

            # 库存查询表里所有的数据
            inventories = self._get_inventories(loc_objects, _request_user, sku_inventory_ids, validated_data["check_method"])

            # 合并库存数据
            inventories_combine_map = self._combine_inventories(inventories, _request_user)

            # 创建盘点结果和关联记录
            self._create_stock_take_results(inventories_combine_map, stock_order, _request_user)

        return stock_order

    @staticmethod
    def _process_locations(locations, stock_order, request):
        """
        处理仓库仓位信息
        :param locations: 仓库仓位信息
        [
            {
                "warehouse_id": 1,
                "warehouse_position_id": null
            }
        ]
        :param stock_order:
        :param request:
        :return:
        """
        for location in locations:
            location["stock_take_order"] = stock_order.id

        loc_ser = StockTakeOrderLocationsSer(data=locations, many=True, context={"request": request})
        if not loc_ser.is_valid():
            raise FieldsError(loc_ser.errors)

        return loc_ser.save()

    @staticmethod
    def _get_inventories(
        loc_objects,
        request_user,
        sku_inventory_ids,
        check_method,
    ):
        """
        获取现有的库存数据
        :param request_user: 当前用户
        :param sku_inventory_ids: 小程序支持品的选择进行盘点
        :param check_method: 盘点方式  1RFID标签  2普通标签
        :return:
        """
        inventories = ERPSKUInventory.objects.filter(
            status=1,
            company=request_user.company,
            tag_type=check_method,
            in_warehouse_quantity__gt=0,
        ).prefetch_related("inbound_order_detail")

        or_q = Q()
        for loc in loc_objects:
            tmp_q = Q(warehouse_id=loc.warehouse_id)

            if loc.warehouse_position_id:
                tmp_q &= Q(warehouse_position_id=loc.warehouse_position_id)

            or_q |= tmp_q

        inventories = inventories.filter(or_q)

        if sku_inventory_ids:
            inventories = inventories.filter(pk__in=sku_inventory_ids)

        return inventories

    @staticmethod
    def _combine_inventories(inventories, request_user):
        """
        合并库存数据
        :param inventories:
        :param request_user:
        :return:
        """
        inventories_combine_map = {}

        for inventory in inventories:
            sku_id = inventory.sku_id
            warehouse_id = inventory.warehouse_id
            warehouse_position_id = inventory.warehouse_position_id or None

            unique_ret_key = (sku_id, warehouse_id, warehouse_position_id)

            tid_info = inventory.tid_info
            inbound_detail_code = inventory.inbound_order_detail.inbound_detail_code

            # 批量生成result的tid信息
            tids_map = StockTakeOrderResult.generate_stock_take_tid_info(tid_info, inbound_detail_code, request_user.user_id)

            if unique_ret_key not in inventories_combine_map:
                inventories_combine_map[unique_ret_key] = {
                    "total_quantity": inventory.in_warehouse_quantity,
                    "sum_total_cost_price": inventory.total_cost_price,
                    "sum_total_label_price": inventory.total_label_price,
                    "sum_total_market_price": inventory.total_market_price,
                    "sum_total_purchase_price": inventory.total_purchase_price,
                    "tid_infos": tids_map,
                    "ids": [inventory.pk],
                    "inbound_order_ids": [inventory.inbound_order_id],
                    "inbound_order_detail_ids": [inventory.inbound_order_detail_id],
                    # "supplier_id": [inventory.supplier_id],
                }
            else:
                data = inventories_combine_map[unique_ret_key]
                data["total_quantity"] += inventory.in_warehouse_quantity
                data["sum_total_cost_price"] += inventory.total_cost_price
                data["sum_total_label_price"] += inventory.total_label_price
                data["sum_total_market_price"] += inventory.total_market_price
                data["sum_total_purchase_price"] += inventory.total_purchase_price
                data["tid_infos"].update(tids_map)
                data["ids"].append(inventory.pk)
                data["inbound_order_ids"].append(inventory.inbound_order_id)
                data["inbound_order_detail_ids"].append(inventory.inbound_order_detail_id)
                # if inventory.supplier_id not in data["supplier_id"]:
                #     data["supplier_id"].append(inventory.supplier_id)

        return inventories_combine_map

    def _create_stock_take_results(self, inventories_combine_map, stock_order, request_user):
        """创建盘点结果和关联记录"""
        stock_take_order_results = []
        stock_take_order_result_inbound_order_relates = []

        for unique_ret_key, data in inventories_combine_map.items():
            sku_id, warehouse_id, warehouse_position_id = unique_ret_key

            total_quantity = data["total_quantity"]
            sum_total_cost_price = data["sum_total_cost_price"] or 0
            sum_total_label_price = data["sum_total_label_price"] or 0
            sum_total_market_price = data["sum_total_market_price"] or 0
            sum_total_purchase_price = data["sum_total_purchase_price"] or 0

            # 计算平均价格
            avg_prices = self._calculate_avg_prices(
                total_quantity,
                sum_total_cost_price,
                sum_total_label_price,
                sum_total_market_price,
                sum_total_purchase_price,
            )

            result = StockTakeOrderResult(
                stock_take_order=stock_order,
                sku_id=sku_id,
                warehouse_id=warehouse_id,
                warehouse_position_id=warehouse_position_id,
                origin_inventory_quantity=total_quantity,
                avg_cost_price=avg_prices["avg_cost_price"],
                avg_label_price=avg_prices["avg_label_price"],
                avg_market_price=avg_prices["avg_market_price"],
                avg_purchase_price=avg_prices["avg_purchase_price"],
                tid_info=data["tid_infos"],
                create_user=request_user.user_id,
            )
            stock_take_order_results.append(result)

            # 创建关联记录
            relates = [
                StockTakeOrderResultInboundOrderRelate(
                    stock_take_order=stock_order,
                    stock_take_order_ret=result,
                    inbound_order_detail_id=detail_id,
                    inbound_order_id=inbound_order_id,
                    erp_sku_inventory_id=sku_inventory_obj_id,
                )
                for sku_inventory_obj_id, detail_id, inbound_order_id in zip(data["ids"], data["inbound_order_detail_ids"], data["inbound_order_ids"])
            ]
            stock_take_order_result_inbound_order_relates.extend(relates)

        StockTakeOrderResult.objects.bulk_create(stock_take_order_results)
        StockTakeOrderResultInboundOrderRelate.objects.bulk_create(stock_take_order_result_inbound_order_relates)

    @staticmethod
    def _calculate_avg_prices(total_quantity, cost_price, label_price, market_price, purchase_price):
        """计算平均价格"""
        if total_quantity > 0:
            return {
                "avg_cost_price": cost_price / total_quantity,
                "avg_label_price": label_price / total_quantity,
                "avg_market_price": market_price / total_quantity,
                "avg_purchase_price": purchase_price / total_quantity,
            }
        else:
            return {
                "avg_cost_price": 0,
                "avg_label_price": 0,
                "avg_market_price": 0,
                "avg_purchase_price": 0,
            }


class StockTakeOrderListSer(CustomizeSerializer):
    warehouse_info = serializers.SerializerMethodField()
    warehouse_position_info = serializers.SerializerMethodField()
    check_type_display = serializers.CharField(source="get_check_type_display")
    status_display = serializers.CharField(source="get_status_display")
    check_result_display = serializers.CharField(source="get_check_result_display", allow_null=True)
    check_method_display = serializers.CharField(source="get_check_method_display")
    total_stock_take_quantity = serializers.SerializerMethodField()
    surplus_stock_take_quantity = serializers.SerializerMethodField()
    loss_stock_take_quantity = serializers.SerializerMethodField()
    exception_product_quantity = serializers.SerializerMethodField()
    location_stock_take_info = serializers.SerializerMethodField()
    total_sku_count = serializers.SerializerMethodField()

    class Meta:
        model = StockTakeOrder
        fields = (
            "order_code",
            "name",
            "warehouse_info",
            "warehouse_position_info",
            "check_type",
            "check_type_display",
            "create_user",
            "remark",
            "status",
            "status_display",
            "check_result",
            "check_result_display",
            "check_method",
            "check_method_display",
            "total_stock_take_quantity",
            "surplus_stock_take_quantity",
            "loss_stock_take_quantity",
            "exception_product_quantity",
            "total_sku_count",
            "location_stock_take_info",
            "create_date",
            "finish_date",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        stock_take_order_pk_list = [i.pk for i in self.instance.object_list]
        # 获取盘点结果的统计
        self.stat_data = StockTakeOrder.get_stock_take_result_stat_data(stock_take_order_pk_list)

    def get_location_stock_take_info(self, obj: StockTakeOrder):
        locs = obj.stocktakeorderlocations_set.all()
        re_data = []
        for loc in locs:
            if obj.pk not in self.stat_data:
                total_stock_take_quantity = 0
                total_loss_quantity = 0
                total_surplus_quantity = 0
                total_sku_count = 0
            else:
                if (loc.warehouse_id, loc.warehouse_position_id) not in self.stat_data[obj.pk]:
                    total_stock_take_quantity = 0
                    total_loss_quantity = 0
                    total_surplus_quantity = 0
                    total_sku_count = 0
                else:
                    warehouse_stat_data = self.stat_data[obj.pk][(loc.warehouse_id, loc.warehouse_position_id)]

                    total_stock_take_quantity = warehouse_stat_data.get("total_stock_take_quantity") or 0
                    total_loss_quantity = warehouse_stat_data.get("total_loss_quantity") or 0
                    total_surplus_quantity = warehouse_stat_data.get("total_surplus_quantity") or 0
                    total_sku_count = warehouse_stat_data.get("total_sku_count") or 0

            re_data.append(
                {
                    "warehouse_id": loc.warehouse_id,
                    "warehouse_name": loc.warehouse.name,
                    "warehouse_position_id": loc.warehouse_position_id,
                    "warehouse_position_name": loc.warehouse_position.name if loc.warehouse_position_id else None,
                    "total_stock_take_quantity": total_stock_take_quantity,
                    "total_loss_quantity": total_loss_quantity,
                    "total_surplus_quantity": total_surplus_quantity,
                    "total_sku_count": total_sku_count,
                }
            )
        return re_data

    @staticmethod
    def get_warehouse_info(obj: StockTakeOrder):
        locations = obj.stocktakeorderlocations_set.all()

        return [
            {
                "warehouse_id": loc.warehouse_id,
                "name": loc.warehouse.name,
            }
            for loc in locations
        ]

    @staticmethod
    def get_warehouse_position_info(obj: StockTakeOrder):
        locations = obj.stocktakeorderlocations_set.all()

        return [
            {
                "warehouse_position_id": loc.warehouse_position_id,
                "name": loc.warehouse_position.name,
            }
            for loc in locations
            if loc.warehouse_position_id
        ]

    def get_total_stock_take_quantity(self, obj: StockTakeOrder):
        # 已盘点数量
        if obj.pk not in self.stat_data:
            return 0

        return sum([i.get("total_stock_take_quantity") or 0 for i in self.stat_data[obj.pk].values()])

    def get_loss_stock_take_quantity(self, obj: StockTakeOrder):
        # 盘亏数量
        if obj.pk not in self.stat_data:
            return 0
        return sum([i.get("total_loss_quantity") or 0 for i in self.stat_data[obj.pk].values()])

    def get_surplus_stock_take_quantity(self, obj: StockTakeOrder):
        # 盘盈数量
        if obj.pk not in self.stat_data:
            return 0
        return sum([i.get("total_surplus_quantity") or 0 for i in self.stat_data[obj.pk].values()])

    def get_total_sku_count(self, obj: StockTakeOrder):
        """
        获取总的sku数量
        :param obj:
        :return:
        """
        if obj.pk not in self.stat_data:
            return 0
        return sum([i.get("total_sku_count") or 0 for i in self.stat_data[obj.pk].values()])

    @staticmethod
    def get_exception_product_quantity(obj: StockTakeOrder):
        """
        异常数量
        :param obj:
        :return:
        """
        return 0


class StockTakeOrderDetailSer(CustomizeSerializer):
    locations = serializers.SerializerMethodField()

    class Meta:
        model = StockTakeOrder
        fields = (
            "order_code",
            "name",
            "locations",
            "status",
            "check_type",
            "remark",
        )

    @staticmethod
    def get_locations(obj: StockTakeOrder):
        locs = obj.stocktakeorderlocations_set.all()

        return [
            {
                "warehouse_id": loc.warehouse_id,
                "warehouse_name": loc.warehouse.name,
                "warehouse_position_id": loc.warehouse_position_id,
                "warehouse_position_name": loc.warehouse_position.name if loc.warehouse_position_id else "",
            }
            for loc in locs
        ]


class StockTakeOrderUpdateSer(CustomizeSerializer):
    class Meta:
        model = StockTakeOrder
        fields = (
            "name",
            "remark",
            "update_user",
        )


class StockTakeResultListSer(CustomizeSerializer):
    main_images = serializers.ListSerializer(child=serializers.CharField(), source="sku.main_images", default=list)
    order_code = serializers.CharField(source="stock_take_order.order_code")
    spec_code = serializers.CharField(source="sku.spec_code")
    code = serializers.CharField(source="sku.product.code")
    name = serializers.CharField(source="sku.name")
    warehouse_name = serializers.CharField(source="warehouse.name")
    warehouse_position_name = serializers.CharField(source="warehouse_position.name", allow_null=True)
    weight = serializers.CharField(source="sku.weight")
    category = serializers.SerializerMethodField()
    attrs = serializers.SerializerMethodField()
    supplier_id = serializers.IntegerField(source="sku.supplier.id", allow_null=True)
    supplier_name = serializers.SerializerMethodField()
    purchase_types = serializers.SerializerMethodField()
    stock_take_status_display = serializers.CharField(source="get_stock_take_status_display")
    is_abnormal_display = serializers.CharField(source="get_is_abnormal_display")
    stock_take_user = serializers.SerializerMethodField()
    unverified_tids = serializers.SerializerMethodField()

    class Meta:
        model = StockTakeOrderResult
        fields = (
            "id",
            "main_images",
            "order_code",
            "spec_code",
            "code",
            "name",
            "warehouse_id",
            "warehouse_name",
            "warehouse_position_id",
            "warehouse_position_name",
            "origin_inventory_quantity",
            "stock_take_quantity",
            "avg_cost_price",
            "avg_label_price",
            "avg_market_price",
            "weight",
            "category",
            "attrs",
            "supplier_id",
            "supplier_name",
            "purchase_types",
            "stock_take_status",
            "stock_take_status_display",
            "is_abnormal",
            "is_abnormal_display",
            "stock_take_user",
            "loss_quantity",
            "surplus_quantity",
            "unverified_tids",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if isinstance(self.instance, Page):
            category_id_list = list({d.sku.category_id for d in self.instance.object_list if d.sku.category_id})
            stock_take_order_id_list = [d.pk for d in self.instance.object_list]
        else:
            category_id_list = list({d.sku.category_id for d in self.instance if d.sku.category_id})
            stock_take_order_id_list = [d.pk for d in self.instance]

        category_qs = ERPProductCategory.get_ancestors_with_category_id_list(category_id_list).values("id", "name", "parent_id")
        self.category_cache = CategoryFindAncestorsCache(category_qs)

        #
        relates = StockTakeOrderResultInboundOrderRelate.objects.prefetch_related(
            "inbound_order_detail",
            "erp_sku_inventory",
            "erp_sku_inventory__supplier",
        ).filter(stock_take_order_ret_id__in=stock_take_order_id_list)
        self.relates_map = {}
        for relate in relates:
            if relate.stock_take_order_ret_id not in self.relates_map:
                self.relates_map[relate.stock_take_order_ret_id] = [relate]
            else:
                self.relates_map[relate.stock_take_order_ret_id].append(relate)

    def get_supplier_name(self, obj: StockTakeOrderResult):
        if obj.pk not in self.relates_map:
            return ""
        try:
            return "、".join([i.erp_sku_inventory.supplier.name for i in self.relates_map[obj.pk] if i.erp_sku_inventory.supplier_id])
        except Exception as e:
            logger.warning(f">>获取供应商信息错误:{e}")
            return ""

    def get_category(self, obj: StockTakeOrderResult):
        return self.category_cache.find_parents(obj.sku.category_id)

    @staticmethod
    def get_attrs(obj: StockTakeOrderResult):
        return obj.sku.get_attr_display()

    def get_purchase_types(self, obj: StockTakeOrderResult):
        if obj.pk not in self.relates_map:
            return []

        try:
            return [
                {
                    "purchase_type": i.inbound_order_detail.purchase_type,
                    "purchase_type_display": "自购" if i.inbound_order_detail.purchase_type == 1 else "借入",
                }
                for i in self.relates_map[obj.pk]
            ]
        except Exception as e:
            logger.warning(f">>获取采购方式信息错误:{e}")
            return ""

    @staticmethod
    def get_stock_take_user(obj: StockTakeOrderResult):
        return get_user_real_name_by_user_id(obj.stock_take_user)

    @staticmethod
    def get_unverified_tids(obj: StockTakeOrderResult):
        return ["{}{}".format(decimal_to_base62(obj.sku.sku_id), k) for k in obj.not_check_tid_info.keys()]


class StockTakeResultProductEnumSer(StockTakeResultListSer):
    tids = serializers.ListSerializer(child=serializers.CharField(), source="get_display_tid_list")

    class Meta:
        model = StockTakeOrderResult
        fields = (
            "id",
            "name",
            "spec_code",
            "code",
            "avg_label_price",
            "main_images",
            "warehouse_id",
            "warehouse_name",
            "warehouse_position_id",
            "warehouse_position_name",
            "tids",
        )
