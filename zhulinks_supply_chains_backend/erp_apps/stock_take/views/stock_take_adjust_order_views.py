from django.db import transaction
from django.db.models import QuerySet
from django.utils import timezone
from rest_framework.decorators import action

from common.basics.exceptions import APIViewException, DataNotFoundException
from common.basics.viewsets import SPModelViewSet
from erp_products.utils import decimal_to_base62
from stock_take.filters import StockTakeAdjustOrderFilter
from stock_take.models import StockTakeOrder, StockTakeAdjustOrder, StockTakeAdjustOrderDetail
from stock_take.serializers import StockTakeAdjustOrderSer, StockTakeAdjustOrderProductDetailSer
from stock_take.utils import tid_to_sku_map
from utils.http_handle import IResponse, custom_django_filter


# class StockTakeAdjustOrderListView(SPAPIView):
#     def get(self, request: Request):
#         stock_take_adjust_orders = StockTakeAdjustOrder.objects.filter(
#             company=self.current_user.company,
#         ).order_by("-update_date")
#
#         re_data, *_ = custom_django_filter(
#             request,
#             stock_take_adjust_orders,
#             StockTakeAdjustOrderFilter,
#             StockTakeAdjustOrderSer,
#             force_order=False,
#         )
#         return IResponse(data=re_data)
#
#     def post(self, request: Request):
#         # {
#         #     "stock_take_order_code": "PD25040800001",
#         #     "details": [
#         #         {
#         #             "stock_take_ret_id": 1,
#         #             "tids": [
#         #                 "12312321"
#         #             ]
#         #         }
#         #     ]
#         # }
#         stock_take_order_code = request.data.get("stock_take_order_code")
#         if not stock_take_order_code:
#             raise APIViewException
#
#         details = request.data.get("details")
#         details_map = {}
#         try:
#             for detail in details:
#                 if not detail.get("stock_take_ret_id") and not detail.get("tids"):
#                     raise APIViewException
#
#                 details_map[detail["stock_take_ret_id"]] = detail["tids"]
#         except ValueError:
#             raise APIViewException
#
#         try:
#             stock_take_order = StockTakeOrder.objects.get(
#                 order_code=stock_take_order_code,
#                 company=self.current_user.company,
#             )
#         except StockTakeOrder.DoesNotExist:
#             raise DataNotFoundException
#
#         if stock_take_order.status != 2:
#             raise APIViewException(err_message="stock take order processing, pls complete it first")
#
#         stock_take_results = stock_take_order.stocktakeorderresult_set.prefetch_related(
#             "stocktakeorderresultinboundorderrelate_set",
#         ).filter(pk__in=details_map.keys())
#         if len(stock_take_results) != len(details_map):
#             raise APIViewException(err_message="invalid stock take results, pls refresh and try again")
#
#         remark = request.data.get("remark", "")
#         # 判断tid是否已经调整过
#         tids = [j for i in details_map.values() for j in i]
#         for sku_id, decrypt_tids in tid_to_sku_map(tids).items():
#             detail_tid_infos = StockTakeAdjustOrderDetail.objects.filter(
#                 adjust_order__status=2,
#                 tid_info__has_any_keys=decrypt_tids,
#             ).only("tid_info")
#
#             for detail_tid_info in detail_tid_infos:
#                 for tid in decrypt_tids:
#                     if tid in detail_tid_info.tid_info:
#                         raise APIViewException(err_message="{}{}已调整过".format(decimal_to_base62(sku_id), tid))
#
#         with transaction.atomic():
#             # 创建盘点单
#             adjust_order = StockTakeAdjustOrder(
#                 stock_take_order=stock_take_order,
#                 status=2,
#                 remark=remark,
#                 approve_user=self.current_user.user_id,
#                 approve_time=timezone.now(),
#                 company=self.current_user.company,
#                 create_user=self.current_user.user_id,
#             )
#
#             need_create_adjust_order_details = []
#             for stock_take_result in stock_take_results:
#                 if stock_take_result.pk not in details_map:
#                     continue
#
#                 tids = tid_to_sku_map(details_map[stock_take_result.pk]).get(stock_take_result.sku.sku_id)
#                 if not tids:
#                     continue
#
#                 # 关联关系
#                 relates = stock_take_result.stocktakeorderresultinboundorderrelate_set.all()
#                 for relate in relates:
#                     related_tids = []
#
#                     erp_sku_inventory = relate.erp_sku_inventory
#                     for decrypt_tid in tids:
#                         if decrypt_tid not in stock_take_result.tid_info:
#                             raise APIViewException(err_message=f"非法唯一ID：{decrypt_tid}")
#
#                         if stock_take_result.tid_info[decrypt_tid]["status"] == "checked":
#                             raise APIViewException(err_message=f"唯一ID：{decrypt_tid}已盘点，无法调整")
#
#                         # 更新盘点单的的tid信息
#                         stock_take_result.set_loss_adjusted_status(decrypt_tid, self.current_user.user_id)
#
#                         # 获取所有的SKU库存记录
#                         if decrypt_tid not in erp_sku_inventory.tid_info:
#                             continue
#
#                         # 修改库存查询表的tid info信息
#                         erp_sku_inventory.set_tid_loss_adjusted_status(decrypt_tid, self.current_user.user_id)
#
#                         # 添加关联的tid信息
#                         related_tids.append(decrypt_tid)
#
#                     if related_tids:
#                         need_create_adjust_order_details.append(
#                             StockTakeAdjustOrderDetail(
#                                 adjust_order=adjust_order,
#                                 stock_take_order_result=stock_take_result,
#                                 sku=stock_take_result.sku,
#                                 warehouse=stock_take_result.warehouse,
#                                 warehouse_position_id=stock_take_result.warehouse_position_id,  # 可能为null
#                                 erp_sku_inventory=erp_sku_inventory,
#                                 cost_price=erp_sku_inventory.cost_price,
#                                 label_price=erp_sku_inventory.label_price,
#                                 market_price=erp_sku_inventory.market_price,
#                                 purchase_price=erp_sku_inventory.purchase_price,
#                                 original_quantity=erp_sku_inventory.in_warehouse_quantity,
#                                 adjust_quantity=-len(related_tids),
#                                 remark=remark,
#                                 tid_info=StockTakeAdjustOrderDetail.batch_generate_tid_info(related_tids),
#                                 create_user=self.current_user.user_id,
#                             )
#                         )
#
#                 # 更新在仓...的数量
#                 erp_sku_inventory.calculate_quantity_data()
#                 erp_sku_inventory.save()
#
#                 # 更新
#                 stock_take_result.calculate_adjusted_quantity()
#                 stock_take_result.save()
#
#             original_quantity = sum([i.original_quantity for i in need_create_adjust_order_details])
#             adjusted_quantity = sum([i.adjust_quantity for i in need_create_adjust_order_details])
#
#             adjust_order.original_quantity = original_quantity
#             adjust_order.adjusted_quantity = adjusted_quantity
#             adjust_order.save()
#
#             StockTakeAdjustOrderDetail.objects.bulk_create(need_create_adjust_order_details)
#
#         return IResponse()


# class StockTakeAdjustOrderDetailView(SPAPIView):
#     def _get_object(self, code) -> StockTakeAdjustOrder:
#         adjust_order = StockTakeAdjustOrder.objects.filter(
#             code=code,
#             company=self.current_user.company,
#             is_deleted=False,
#         ).last()
#
#         if not adjust_order:
#             raise DataNotFoundException
#
#         return adjust_order
#
#     def get(self, request: Request, code: str):
#         adjust_order = self._get_object(code)
#         adjust_order_info = StockTakeAdjustOrderSer(adjust_order, many=False).data
#         adjust_order_details = adjust_order.details.prefetch_related(
#             "sku",
#             "sku__product",
#             "sku__unit",
#             "supplier",
#             "warehouse",
#             "warehouse_position",
#             "erp_sku_inventory",
#             "erp_sku_inventory",
#             "erp_sku_inventory__inbound_order_detail",
#         ).filter(is_deleted=False)
#
#         re_data, *_ = custom_django_filter(
#             request,
#             adjust_order_details,
#             None,
#             StockTakeAdjustOrderProductDetailSer,
#             force_order=False,
#         )
#
#         return IResponse(data=re_data, adjust_order_info=adjust_order_info)


class StockTakeAdjustOrderViewSet(SPModelViewSet):
    fronted_page = "盘点调整单"
    resource_name = "调整单"
    ordering = ["-update_date"]
    force_order = False
    lookup_field = "code"
    serializer_class = StockTakeAdjustOrderSer
    filterset_class = StockTakeAdjustOrderFilter

    def get_queryset(self) -> QuerySet:
        return StockTakeAdjustOrder.objects.filter(
            company=self.current_user.company,
            is_deleted=False,
        ).order_by("-update_date")

    def create(self, request, *args, **kwargs):
        # {
        #     "stock_take_order_code": "PD25040800001",
        #     "details": [
        #         {
        #             "stock_take_ret_id": 1,
        #             "tids": [
        #                 "12312321"
        #             ]
        #         }
        #     ]
        # }
        stock_take_order_code = request.data.get("stock_take_order_code")
        if not stock_take_order_code:
            raise APIViewException

        details = request.data.get("details")
        if not details or not isinstance(details, list):
            raise APIViewException

        details_map = {}
        try:
            for detail in details:
                if not detail.get("stock_take_ret_id") and not detail.get("tids"):
                    raise APIViewException

                details_map[detail["stock_take_ret_id"]] = detail["tids"]
        except ValueError:
            raise APIViewException

        try:
            stock_take_order = StockTakeOrder.objects.get(
                order_code=stock_take_order_code,
                company=self.current_user.company,
            )
        except StockTakeOrder.DoesNotExist:
            raise DataNotFoundException

        if stock_take_order.status != 2:
            raise APIViewException(err_message="stock take order processing, pls complete it first")

        stock_take_results = stock_take_order.stocktakeorderresult_set.prefetch_related(
            "stocktakeorderresultinboundorderrelate_set",
        ).filter(pk__in=details_map.keys())
        if len(stock_take_results) != len(details_map):
            raise APIViewException(err_message="invalid stock take results, pls refresh and try again")

        remark = request.data.get("remark", "")
        # 判断tid是否已经调整过
        tids = [j for i in details_map.values() for j in i]
        for sku_id, decrypt_tids in tid_to_sku_map(tids).items():
            detail_tid_infos = StockTakeAdjustOrderDetail.objects.filter(
                adjust_order__status=2,
                tid_info__has_any_keys=decrypt_tids,
            ).only("tid_info")

            for detail_tid_info in detail_tid_infos:
                for tid in decrypt_tids:
                    if tid in detail_tid_info.tid_info:
                        raise APIViewException(err_message="{}{}已调整过".format(decimal_to_base62(sku_id), tid))

        with transaction.atomic():
            # 创建盘点单
            adjust_order = StockTakeAdjustOrder(
                stock_take_order=stock_take_order,
                status=2,
                remark=remark,
                approve_user=self.current_user.user_id,
                approve_time=timezone.now(),
                company=self.current_user.company,
                create_user=self.current_user.user_id,
            )

            need_create_adjust_order_details = []
            for stock_take_result in stock_take_results:
                if stock_take_result.pk not in details_map:
                    continue

                tids = tid_to_sku_map(details_map[stock_take_result.pk]).get(stock_take_result.sku.sku_id)
                if not tids:
                    continue

                # 关联关系
                relates = stock_take_result.stocktakeorderresultinboundorderrelate_set.all()
                for relate in relates:
                    related_tids = []

                    erp_sku_inventory = relate.erp_sku_inventory
                    for decrypt_tid in tids:
                        if decrypt_tid not in stock_take_result.tid_info:
                            raise APIViewException(err_message=f"非法唯一ID：{decrypt_tid}")

                        if stock_take_result.tid_info[decrypt_tid]["status"] == "checked":
                            raise APIViewException(err_message=f"唯一ID：{decrypt_tid}已盘点，无法调整")

                        # 更新盘点单的的tid信息
                        stock_take_result.set_loss_adjusted_status(decrypt_tid, self.current_user.user_id)

                        # 获取所有的SKU库存记录
                        if decrypt_tid not in erp_sku_inventory.tid_info:
                            continue

                        # 修改库存查询表的tid info信息
                        erp_sku_inventory.set_tid_loss_adjusted_status(decrypt_tid, self.current_user.user_id)

                        # 添加关联的tid信息
                        related_tids.append(decrypt_tid)

                    if related_tids:
                        need_create_adjust_order_details.append(
                            StockTakeAdjustOrderDetail(
                                adjust_order=adjust_order,
                                stock_take_order_result=stock_take_result,
                                sku=stock_take_result.sku,
                                warehouse=stock_take_result.warehouse,
                                warehouse_position_id=stock_take_result.warehouse_position_id,  # 可能为null
                                erp_sku_inventory=erp_sku_inventory,
                                cost_price=erp_sku_inventory.cost_price,
                                label_price=erp_sku_inventory.label_price,
                                market_price=erp_sku_inventory.market_price,
                                purchase_price=erp_sku_inventory.purchase_price,
                                original_quantity=erp_sku_inventory.in_warehouse_quantity,
                                adjust_quantity=-len(related_tids),
                                remark=remark,
                                tid_info=StockTakeAdjustOrderDetail.batch_generate_tid_info(related_tids),
                                create_user=self.current_user.user_id,
                            )
                        )

                    # 更新在仓...的数量
                    erp_sku_inventory.calculate_quantity_data()
                    erp_sku_inventory.save()

                # 更新
                stock_take_result.calculate_adjusted_quantity()
                stock_take_result.save()

            original_quantity = sum([i.original_quantity for i in need_create_adjust_order_details])
            adjusted_quantity = sum([i.adjust_quantity for i in need_create_adjust_order_details])

            adjust_order.original_quantity = original_quantity
            adjust_order.adjusted_quantity = adjusted_quantity
            adjust_order.save()

            StockTakeAdjustOrderDetail.objects.bulk_create(need_create_adjust_order_details)

        self.log_operation(
            request,
            StockTakeAdjustOrder,
            "创建盘点调整单",
            resource_id=adjust_order.code,
            is_success_input=True,
        )
        return IResponse()

    @action(detail=True, methods=["get"])
    def details(self, request, code=None):
        """
        获取库存盘点调整单详情
        """
        adjust_order = self.get_no_filter_object()
        adjust_order_info = self.get_serializer(adjust_order).data
        adjust_order_details = adjust_order.details.prefetch_related(
            "sku",
            "sku__product",
            "sku__unit",
            "erp_sku_inventory__supplier",
            "warehouse",
            "warehouse_position",
            "erp_sku_inventory",
            "erp_sku_inventory",
            "erp_sku_inventory__inbound_order_detail",
        ).filter(is_deleted=False)

        # 使用 custom_django_filter 处理分页和序列化
        re_data, *_ = custom_django_filter(
            request,
            adjust_order_details,
            None,  # 这里可以添加过滤器类如果需要
            StockTakeAdjustOrderProductDetailSer,
            force_order=False,
        )

        return IResponse(data=re_data, adjust_order_info=adjust_order_info)
