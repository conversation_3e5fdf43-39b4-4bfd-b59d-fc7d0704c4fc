# -*- coding: utf-8 -*-
from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>

from stock_take.views.stock_take_adjust_order_views import StockTakeAdjustOrderViewSet
from stock_take.views.stock_take_order_viewset import StockTakeOrderViewSet

router = DefaultRouter(trailing_slash=False)
# 盘点单路由
router.register(r"order", StockTakeOrderViewSet, basename="stock_take.order")
# 调整单路由
router.register(r"adjust_order", StockTakeAdjustOrderViewSet, basename="stock_take.adjust_order")


urlpatterns = [
    path("", include(router.urls)),
]
