# -*- coding: utf-8 -*-
from django_filters import FilterSet, filters

from common.basic import HybridSearchFilterV2
from common.basics.filtersets import IContainCharField
from stock_take.models import StockTakeOrder, StockTakeOrderResult


class StockTakeOrderFilter(FilterSet):
    order_code = IContainCharField()
    warehouse_id = filters.CharFilter(field_name="stocktakeorderlocations__warehouse_id")
    warehouse_position_id = filters.CharFilter(field_name="stocktakeorderlocations__warehouse_position_id")
    name = IContainCharField()
    hybrid_search = HybridSearchFilterV2(
        field_names=[
            "name",
            "order_code",
            "stocktakeorderresult__sku__spec_code",
            "stocktakeorderresult__sku__product__code",
            "stocktakeorderresult__sku__name",
        ]
    )

    class Meta:
        model = StockTakeOrder
        fields = (
            "order_code",
            "warehouse_id",
            "warehouse_position_id",
            "name",
            "status",
            "check_type",
            "check_method",
            "hybrid_search",
        )


class StockTakeResultFilter(FilterSet):
    spec_code = IContainCharField("sku__spec_code")
    name = IContainCharField("sku__name")
    code = IContainCharField("sku__product__name")
    warehouse_id = filters.NumberFilter()
    warehouse_position_id = filters.NumberFilter()
    supplier_id = filters.CharFilter(field_name="sku__supplier_id")
    hybrid_search = HybridSearchFilterV2(
        field_names=[
            "sku__spec_code",
            "sku__product__code",
            "sku__name",
        ]
    )

    class Meta:
        model = StockTakeOrderResult
        fields = (
            "spec_code",
            "name",
            "code",
            "warehouse_id",
            "warehouse_position_id",
            "supplier_id",
            "stock_take_status",
            "is_abnormal",
            "stock_take_user",
            "hybrid_search",
        )


class StockTakeResultProductEnumFilter(FilterSet):
    warehouse_id = filters.NumberFilter()
    warehouse_position_id = filters.NumberFilter()
    status = filters.CharFilter(method="status_filter")

    class Meta:
        model = StockTakeOrderResult
        fields = (
            "warehouse_id",
            "warehouse_position_id",
            "status",
        )

    @staticmethod
    def status_filter(qs, name, value):
        if value == "has_stock_check":
            # 已盘点
            return qs.filter(stock_take_status=2)
        elif value == "surplus":
            # 盘盈
            return qs.filter(surplus_quantity__gt=0)
        elif value == "loss":
            # 盘亏
            return qs.filter(loss_quantity__gt=0)
        return qs.none()
