# -*- coding: utf-8 -*-
from django.urls import path, include


"""
erp app路由, 可以不加route uri，通过custom_path进行路由控制, 防止系统slash问题警告提示
"""


urlpatterns = [
    # 部门路由
    path("", include("erp_apps.dept.urls"), name="dept_routers"),
    # 配置路由
    path("", include("erp_apps.erp_configs.urls"), name="erp_config_routers"),
    # 采购商品路由
    path("", include("erp_apps.erp_products.urls"), name="erp_products_routers"),
    # 采购
    path("purchase/", include("erp_purchase.urls"), name="erp_purchase_routers"),
    # 客户管理
    path("client/", include("erp_client.urls"), name="erp_client_routers"),
    # 盘点
    path("stock_take/", include("stock_take.urls"), name="stock_take_routers"),
    # 库存调拨
    path("transfer/", include("erp_transfer.urls"), name="erp_transfer_routers"),
    # 销售订单
    path("orders/", include("erp_orders.urls"), name="erp_orders_routers"),
    # 统计
    path("statistics/", include("erp_statistics.urls"), name="erp_statistics_routers"),
    # 供应商结算（入库单/采购退货单）
    path("settle/", include("erp_settle.urls"), name="erp_settle_routers"),
    # 报表 | 资金 | 汇总
    path("report/", include("erp_report.urls"), name="erp_report_routers"),
    # 生产管理
    path("produce/", include("erp_produce.urls"), name="erp_produce_routers"),

]
