# Generated by Django 5.1.7 on 2025-04-21 05:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_client", "0003_erpcompanycustomerinfo_company"),
    ]

    operations = [
        migrations.CreateModel(
            name="SupplierSettleOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "settle_code",
                    models.CharField(
                        db_index=True,
                        max_length=32,
                        unique=True,
                        verbose_name="结算单号",
                    ),
                ),
                (
                    "settle_type",
                    models.IntegerField(
                        choices=[
                            (1, "现金"),
                            (2, "微信"),
                            (3, "支付宝"),
                            (4, "银行卡"),
                        ],
                        default=1,
                        verbose_name="结算类型",
                    ),
                ),
                (
                    "inbound_order",
                    models.CharField(
                        blank=True,
                        null=True,
                        verbose_name="对应采购入库单,多条用逗号隔开",
                    ),
                ),
                (
                    "outbound_order",
                    models.CharField(
                        blank=True,
                        null=True,
                        verbose_name="对应采购退货单,多条用逗号隔开",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="应结算金额",
                    ),
                ),
                (
                    "done_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="实际结算金额",
                    ),
                ),
                (
                    "settle_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="结算时间"
                    ),
                ),
                (
                    "remark",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="备注"
                    ),
                ),
                (
                    "remark_images",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="图列表"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统用户",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_client.erpcompanysupplierinfo",
                        to_field="supplier_id",
                        verbose_name="供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "供应商结算订单",
                "verbose_name_plural": "供应商结算订单",
                "constraints": [
                    models.UniqueConstraint(
                        fields=("settle_code", "company"),
                        name="unique_settle_code_for_company",
                    )
                ],
            },
        ),
    ]
