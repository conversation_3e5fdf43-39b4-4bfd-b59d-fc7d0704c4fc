# -*- coding: utf-8 -*-
from import_export import resources, fields
from import_export.widgets import DateTimeWidget

from erp_apps.erp_purchase.models import PurchaseOrderDetail, ERPGoodsReceiptOrderDetail, ERPReturnOrderDetail
from utils.caches import get_user_real_name_by_user_id


class PurchaseOrderDetailDownloadResource(resources.ModelResource):
    code = fields.Field(attribute="purchase_order__code", column_name="采购单号")
    purchase_date = fields.Field(attribute="purchase_order__purchase_date", column_name="采购日期", widget=DateTimeWidget())
    company = fields.Field(attribute="purchase_order__company__name", column_name="供应商")
    order_status = fields.Field(attribute="purchase_order__get_order_status_display", column_name="单据状态")
    is_overload = fields.Field(attribute="purchase_order__get_is_overload_display", column_name="是否超入")
    product_type = fields.Field(attribute="purchase_order__get_product_type_display", column_name="商品类型")
    labels = fields.Field(column_name="标记|多标签")
    total_quantity = fields.Field(attribute="purchase_order__product_quantity", column_name="总商品量")
    total_amount = fields.Field(attribute="purchase_order__total_amount", column_name="总金额")
    total_received = fields.Field(attribute="purchase_order__in_warehouse_quantity", column_name="总入库数")
    difference = fields.Field(attribute="purchase_order__diff_quantity", column_name="差异数")
    receipt_status = fields.Field(attribute="get_receipt_status_display", column_name="收货状态")
    purchase_user = fields.Field(column_name="采购员")
    remark = fields.Field(attribute="purchase_order__remark", column_name="备注")
    delivery_address = fields.Field(attribute="purchase_order__delivery_address__full_address", column_name="收货地址")
    order_type = fields.Field(attribute="purchase_order__get_order_type_display", column_name="采购单类型")
    warehouse = fields.Field(attribute="purchase_order__warehouse__name", column_name="仓储方")
    pay_type = fields.Field(attribute="purchase_order__get_pay_type_display", column_name="支付类型")
    payment_term_days = fields.Field(attribute="purchase_order__payment_term_days", column_name="账期天数")
    approve_user = fields.Field(column_name="审核人")
    approve_date = fields.Field(attribute="purchase_order__approve_date", column_name="审核日期", widget=DateTimeWidget())
    finish_date = fields.Field(attribute="purchase_order__finish_date", column_name="完成日期", widget=DateTimeWidget())
    product_name = fields.Field(attribute="erp_sku__product__name", column_name="商品名称")
    article_number = fields.Field(attribute="erp_sku__product__code", column_name="货号")
    sku_code = fields.Field(attribute="erp_sku__spec_code", column_name="商品编码")
    sku_spec_value = fields.Field(column_name="规格")
    quantity = fields.Field(attribute="quantity", column_name="数量")
    sku_receipt_quantity = fields.Field(attribute="receipt_quantity", column_name="已入库数量")
    detail_difference = fields.Field(attribute="diff_quantity", column_name="差异数")
    sku_receipt_amount = fields.Field(attribute="receipt_amount", column_name="已入库金额")
    cost_price = fields.Field(attribute="cost_price", column_name="成本价")
    amount = fields.Field(attribute="amount", column_name="金额")
    detail_code = fields.Field(attribute="detail_code", column_name="明细单号")
    confirm_finish_user = fields.Field(column_name="确认完成人")

    class Meta:
        model = PurchaseOrderDetail
        skip_unchanged = True
        report_skipped = False
        force_init_instance = True
        exclude = (
            "id",
            "create_user",
            "create_date",
            "update_user",
            "update_date",
            "is_deleted",
            "purchase_order",
            "erp_sku",
            "receipt_quantity",
            "receipt_amount",
        )
        export_order = (
            "code",
            "purchase_date",
            "company",
            "order_status",
            "is_overload",
            "product_type",
            "labels",
            "total_quantity",
            "total_amount",
            "total_received",
            "difference",
            "receipt_status",
            "purchase_user",
            "remark",
            "delivery_address",
            "order_type",
            "warehouse",
            "pay_type",
            "payment_term_days",
            "approve_user",
            "approve_date",
            "finish_date",
            "product_name",
            "article_number",
            "sku_code",
            "sku_spec_value",
            "quantity",
            "sku_receipt_quantity",
            "detail_difference",
            "sku_receipt_amount",
            "cost_price",
            "amount",
            "detail_code",
            "confirm_finish_user",
        )

    @staticmethod
    def dehydrate_purchase_user(obj):
        return get_user_real_name_by_user_id(obj.purchase_order.purchase_user)

    @staticmethod
    def dehydrate_confirm_finish_user(obj):
        return get_user_real_name_by_user_id(obj.purchase_order.confirm_finish_user)

    @staticmethod
    def dehydrate_approve_user(obj):
        return get_user_real_name_by_user_id(obj.purchase_order.approve_user)

    @staticmethod
    def dehydrate_labels(obj):
        return "|".join([label.name for label in obj.purchase_order.labels.all()])

    @staticmethod
    def dehydrate_sku_spec_value(obj):
        if not obj.erp_sku_id:
            return ""

        if not obj.erp_sku.specs:
            return "默认规格"
        return ",".join([i["value"] for i in obj.erp_sku.specs if i.get("value")])


class ERPGoodsReceiptOrderDetailDownloadResource(resources.ModelResource):
    code = fields.Field(attribute="goods_receipt_order__code", column_name="入库单号")
    company = fields.Field(attribute="goods_receipt_order__company__name", column_name="供应商")
    purchase_code = fields.Field(attribute="goods_receipt_order__purchase_order_id", column_name="关联采购单号")
    create_date = fields.Field(attribute="goods_receipt_order__create_date", column_name="创建日期", widget=DateTimeWidget())
    receipt_date = fields.Field(attribute="goods_receipt_order__receipt_date", column_name="入库日期", widget=DateTimeWidget())
    receipt_status = fields.Field(attribute="goods_receipt_order__get_receipt_status__display", column_name="入库状态")
    financial_status = fields.Field(attribute="goods_receipt_order__get_financial_status__display", column_name="财审状态")
    storage_party = fields.Field(attribute="goods_receipt_order__storage_party__name", column_name="仓储方")
    warehouse = fields.Field(attribute="goods_receipt_order__warehouse__name", column_name="仓库")
    labels = fields.Field(column_name="标记|多标签")
    remark = fields.Field(attribute="goods_receipt_order__remark", column_name="备注")
    actual_quantity = fields.Field(attribute="goods_receipt_order__actual_quantity", column_name="实际入库数")
    actual_amount = fields.Field(attribute="goods_receipt_order__actual_amount", column_name="实际入库金额")
    create_user = fields.Field(column_name="制单人")
    update_date = fields.Field(attribute="goods_receipt_order__update_date", column_name="最后修改日期", widget=DateTimeWidget())
    product_type = fields.Field(attribute="goods_receipt_order__get_product_type_display", column_name="单据类型")
    entry_type = fields.Field(attribute="goods_receipt_order__get_entry_type_display", column_name="进仓类型")
    # 商品信息
    product_name = fields.Field(attribute="erp_sku__product__name", column_name="商品名称")
    article_number = fields.Field(attribute="erp_sku__product__code", column_name="货号")
    sku_code = fields.Field(attribute="erp_sku__spec_code", column_name="商品编码")
    sku_spec_value = fields.Field(attribute="erp_sku__spec_code", column_name="规格")
    quantity = fields.Field(attribute="quantity", column_name="入库数")
    cost_price = fields.Field(attribute="cost_price", column_name="成本价")
    amount = fields.Field(attribute="amount", column_name="金额")
    detail_code = fields.Field(attribute="detail_code", column_name="明细单号")

    class Meta:
        model = ERPGoodsReceiptOrderDetail
        exclude = ("id", "is_deleted", "update_user", "goods_receipt_order", "erp_sku")
        export_order = (
            "code",
            "company",
            "purchase_code",
            "create_date",
            "receipt_date",
            "receipt_status",
            "financial_status",
            "storage_party",
            "warehouse",
            "labels",
            "remark",
            "actual_quantity",
            "actual_amount",
            "create_user",
            "update_date",
            "product_type",
            "entry_type",
            "product_name",
            "article_number",
            "sku_code",
            "sku_spec_value",
            "quantity",
            "cost_price",
            "amount",
            "detail_code",
        )

    @staticmethod
    def dehydrate_labels(obj):
        return "|".join([label.name for label in obj.goods_receipt_order.labels.all()])

    @staticmethod
    def dehydrate_create_user(obj):
        return get_user_real_name_by_user_id(obj.goods_receipt_order.create_user)

    @staticmethod
    def dehydrate_sku_spec_value(obj):
        if not obj.erp_sku_id:
            return ""

        if not obj.erp_sku.specs:
            return "默认规格"
        return ",".join([i["value"] for i in obj.erp_sku.specs if i.get("value")])


class ERPReturnOrderDetailDownloadResource(resources.ModelResource):
    code = fields.Field(attribute="return_order__code", column_name="退货单号")
    return_date = fields.Field(attribute="return_order__return_date", column_name="退货日期", widget=DateTimeWidget())
    order_status = fields.Field(attribute="return_order__get_order_status_display", column_name="状态")
    order_type = fields.Field(attribute="return_order__get_return_type_display", column_name="退货类型")
    product_quantity = fields.Field(attribute="return_order__product_quantity", column_name="总商品量")
    total_amount = fields.Field(attribute="return_order__total_amount", column_name="总金额")
    storage_party = fields.Field(attribute="return_order__storage_party__name", column_name="仓储方")
    warehouse = fields.Field(attribute="return_order__warehouse__name", column_name="仓库")
    labels = fields.Field(column_name="标记|多标签")
    company = fields.Field(attribute="return_order__company__name", column_name="供应商")
    receiver = fields.Field(attribute="return_order__receiver", column_name="收货人")
    create_user = fields.Field(column_name="创建人")
    remark = fields.Field(attribute="return_order__remark", column_name="备注")
    create_date = fields.Field(attribute="return_order__create_date", column_name="创建日期", widget=DateTimeWidget())
    update_date = fields.Field(attribute="return_order__update_date", column_name="最后修改日期", widget=DateTimeWidget())
    # 商品信息
    product_name = fields.Field(attribute="erp_sku__product__name", column_name="商品名称")
    article_number = fields.Field(attribute="erp_sku__product__code", column_name="货号")
    sku_code = fields.Field(attribute="erp_sku__spec_code", column_name="商品编码")
    sku_spec_value = fields.Field(attribute="erp_sku__spec_code", column_name="规格")
    quantity = fields.Field(attribute="quantity", column_name="退货数量")
    cost_price = fields.Field(attribute="cost_price", column_name="成本价")
    amount = fields.Field(attribute="amount", column_name="金额")
    retail_price = fields.Field(attribute="retail_price", column_name="基本售价")
    retail_amount = fields.Field(attribute="retail_amount", column_name="基本销售额")

    class Meta:
        model = ERPReturnOrderDetail
        exclude = (
            "id",
            "is_deleted",
            "update_user",
            "goods_receipt_order",
            "erp_sku",
            "detail_code",
        )
        export_order = (
            "code",
            "return_date",
            "order_status",
            "order_type",
            "product_quantity",
            "total_amount",
            "storage_party",
            "warehouse",
            "labels",
            "company",
            "receiver",
            "create_user",
            "remark",
            "create_date",
            "update_date",
            "product_name",
            "article_number",
            "sku_code",
            "sku_spec_value",
            "quantity",
            "cost_price",
            "amount",
            "retail_price",
            "retail_amount",
        )

    @staticmethod
    def dehydrate_labels(obj):
        return "|".join([label.name for label in obj.return_order.labels.all()])

    @staticmethod
    def dehydrate_create_user(obj):
        return get_user_real_name_by_user_id(obj.return_order.create_user)

    @staticmethod
    def dehydrate_sku_spec_value(obj):
        if not obj.erp_sku_id:
            return ""

        if not obj.erp_sku.specs:
            return "默认规格"
        return ",".join([i["value"] for i in obj.erp_sku.specs if i.get("value")])
