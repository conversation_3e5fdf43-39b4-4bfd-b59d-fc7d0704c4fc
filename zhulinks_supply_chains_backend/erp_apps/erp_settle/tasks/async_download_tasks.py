# -*- coding: utf-8 -*-
from erp_settle.filters import ERPSettleOrderListFilter
from erp_settle.models import SupplierSettleOrder
from erp_settle.serializers.settle_order_serializers import ERPSupplierSettleOrderDownloadSerializer
from utils.erp_download_utils.erp_async_download_task import ERPAsyncDownloadTaskHandler
from utils.erp_download_utils.supplier_settle_order_download_utils import process_export_data

from zhulinks_supply_chains_backend.celery import app


@app.task(queue="common_tasks")
def async_download_erp_supplier_settle_order_task(task_id: int):
    """
    异步下载erp供应商结算订单
    :param task_id: 下载中心异步任务id
    :return:
    """

    handler = ERPAsyncDownloadTaskHandler(
        ERPSettleOrderListFilter,
        ERPSupplierSettleOrderDownloadSerializer,
        async_download_erp_supplier_settle_order_task,
    )

    task = handler.get_task(task_id)
    if not task:
        return False, f"任务:{task_id}不存在"

    # 构建主查询

    queryset = SupplierSettleOrder.objects.prefetch_related(
        "supplier",
        "company",
    ).filter(company=task.company, is_deleted=False)

    return handler.process_task(queryset, task_id, process_export_data)
