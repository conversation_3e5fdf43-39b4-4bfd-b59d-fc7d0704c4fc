from django.apps import AppConfig


class ErpPurchaseConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "erp_apps.erp_transfer"
    verbose_name = "库存管理"

    # def ready(self):
    #     from common.basic import m2m_through_handler
    #     from django.db.models.signals import m2m_changed
    #     from erp_apps.erp_transfer.models import ERPGoodsReceiptsOrder
    #
    #     m2m_changed.connect(m2m_through_handler, sender=ERPGoodsReceiptsOrder.labels.through)
