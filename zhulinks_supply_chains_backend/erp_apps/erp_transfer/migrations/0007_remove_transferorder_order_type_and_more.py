# Generated by Django 5.1.7 on 2025-04-14 06:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_transfer", "0006_remove_transferorderdetail_transfer_in_order_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="transferorder",
            name="order_type",
        ),
        migrations.RemoveField(
            model_name="transferorder",
            name="transfer_order1",
        ),
        migrations.RemoveField(
            model_name="transferorder",
            name="transfer_order2",
        ),
        migrations.RemoveField(
            model_name="transferorder",
            name="transfer_order3",
        ),
        migrations.RemoveField(
            model_name="transferorderdetail",
            name="transfer_order_in",
        ),
        migrations.RemoveField(
            model_name="transferorderdetail",
            name="transfer_order_out",
        ),
        migrations.AlterField(
            model_name="transferorder",
            name="approve_status",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "待出库审核"), (2, "待入库审核"), (2, "调拨完成")],
                db_index=True,
                default=1,
                verbose_name="审核状态",
            ),
        ),
    ]
