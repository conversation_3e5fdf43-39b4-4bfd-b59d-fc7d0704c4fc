# Generated by Django 5.1.7 on 2025-04-09 10:29

import django.db.models.deletion
import erp_apps.erp_transfer.models.transfer_order_models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_products", "0038_alter_erpsupplierproduct_attr_value_and_more"),
        (
            "erp_purchase",
            "0057_remove_erpoutboundorderdetailinbounddetailrelate_erp_outbound_detail_unique_inventory_price_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="TransferOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "transfer_code",
                    models.CharField(
                        db_index=True, max_length=32, verbose_name="调拨单号"
                    ),
                ),
                (
                    "order_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "调拨单"), (2, "调拨出库单"), (3, "调拨入库单")],
                        db_index=True,
                        default=1,
                        verbose_name="类型",
                    ),
                ),
                (
                    "approve_status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "待审核"), (2, "已审核")],
                        db_index=True,
                        default=1,
                        verbose_name="审核状态",
                    ),
                ),
                (
                    "approve_user",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default="",
                        null=True,
                        verbose_name="审核人",
                    ),
                ),
                (
                    "total_quantity",
                    models.PositiveIntegerField(default=0, verbose_name="调拨数量"),
                ),
                (
                    "total_cost_price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="调拨总成本价",
                    ),
                ),
                (
                    "total_label_price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="调拨总标签价",
                    ),
                ),
                (
                    "total_market_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="调拨总市场价",
                    ),
                ),
                (
                    "approve_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="审核时间"
                    ),
                ),
                (
                    "remark",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="备注"
                    ),
                ),
                (
                    "transfer_code1",
                    models.CharField(
                        db_index=True, max_length=32, verbose_name="调拨主单号"
                    ),
                ),
                (
                    "transfer_code2",
                    models.CharField(
                        db_index=True, max_length=32, verbose_name="调拨出库单号"
                    ),
                ),
                (
                    "transfer_code3",
                    models.CharField(
                        db_index=True, max_length=32, verbose_name="调拨入库单号"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统用户",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.erpsupplierwarehouse",
                        verbose_name="仓库",
                    ),
                ),
                (
                    "warehouse_position",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.erpsupplierwarehouseposition",
                        verbose_name="仓位",
                    ),
                ),
                (
                    "warehouse_position_to",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transfer_warehouse_position_to",
                        to="erp_purchase.erpsupplierwarehouseposition",
                        verbose_name="调入仓位",
                    ),
                ),
                (
                    "warehouse_to",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transfer_warehouse_to",
                        to="erp_purchase.erpsupplierwarehouse",
                        verbose_name="调入仓库",
                    ),
                ),
            ],
            options={
                "verbose_name": "库存调拨",
                "verbose_name_plural": "库存调拨",
            },
        ),
        migrations.CreateModel(
            name="TransferOrderDetail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "transfer_detail_code",
                    models.CharField(
                        default=erp_apps.erp_transfer.models.transfer_order_models._transfer_detail_code_generator,
                        editable=False,
                        max_length=32,
                        unique=True,
                        verbose_name="明细单号",
                    ),
                ),
                (
                    "purchase_type",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[(1, "自购"), (2, "借入")],
                        null=True,
                        verbose_name="采购方式",
                    ),
                ),
                ("quantity", models.PositiveIntegerField(verbose_name="入库数量")),
                (
                    "cost_price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="成本价",
                    ),
                ),
                (
                    "label_price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="标签价",
                    ),
                ),
                (
                    "market_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="市场|吊牌价",
                    ),
                ),
                (
                    "purchase_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="采购价",
                    ),
                ),
                (
                    "total_cost_price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="总成本价",
                    ),
                ),
                (
                    "total_label_price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="总标签价",
                    ),
                ),
                (
                    "total_market_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="总市场|吊牌价",
                    ),
                ),
                (
                    "total_purchase_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="总采购价",
                    ),
                ),
                (
                    "batch_no",
                    models.CharField(
                        blank=True, max_length=25, null=True, verbose_name="批次号"
                    ),
                ),
                (
                    "remark",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="备注信息"
                    ),
                ),
                (
                    "tid_info",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="标签信息"
                    ),
                ),
                (
                    "sku",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpsupplierproductsku",
                        verbose_name="关联sku",
                    ),
                ),
                (
                    "transfer_in_order",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transfer_in_order",
                        to="erp_transfer.transferorder",
                        verbose_name="所属调拨入库单",
                    ),
                ),
                (
                    "transfer_order",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_transfer.transferorder",
                        verbose_name="所属调拨单",
                    ),
                ),
                (
                    "transfer_out_order",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transfer_out_order",
                        to="erp_transfer.transferorder",
                        verbose_name="所属调拨出库单",
                    ),
                ),
                (
                    "warehouse",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.erpsupplierwarehouse",
                        verbose_name="仓库",
                    ),
                ),
                (
                    "warehouse_position",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_purchase.erpsupplierwarehouseposition",
                        verbose_name="仓位",
                    ),
                ),
                (
                    "warehouse_position_to",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transferdetail_warehouse_position_to",
                        to="erp_purchase.erpsupplierwarehouseposition",
                        verbose_name="调入仓位",
                    ),
                ),
                (
                    "warehouse_to",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transferodetail_warehouse_to",
                        to="erp_purchase.erpsupplierwarehouse",
                        verbose_name="调入仓库",
                    ),
                ),
            ],
            options={
                "verbose_name": "供应商ERP入库单明细",
                "verbose_name_plural": "供应商ERP入库单明细",
            },
        ),
        migrations.AddConstraint(
            model_name="transferorder",
            constraint=models.UniqueConstraint(
                fields=("transfer_code", "company"),
                name="unique_transfer_code_for_company",
            ),
        ),
    ]
