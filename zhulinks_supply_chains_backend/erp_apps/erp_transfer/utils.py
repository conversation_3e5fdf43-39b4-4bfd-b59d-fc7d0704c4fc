import random
import string
from datetime import datetime

from django.utils import timezone

from utils.redis_lock import redis_cache


def generate_purchase_code(prefix=None):
    """
    生成采购单号，规则为：CG开头，然后再加上6位随机数字
    """
    # 生成 6 位随机数字
    random_digits = "".join(random.choices(string.digits, k=6))

    if not prefix:
        return f"{random_digits}"
    else:
        # 组合成采购单号
        return f"{prefix}{random_digits}"
