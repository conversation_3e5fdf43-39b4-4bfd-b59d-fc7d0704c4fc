# -*- coding: utf-8 -*-
from datetime import date
from typing import TypedDict

from django.db.models import Sum
from django.utils import timezone

from erp_orders.models import ERPOrder
from erp_purchase.models import InboundOrder, InboundOrderDetail

from .models import ERPDailyCompanyStatistics


class StatisticsData(TypedDict):
    inbound_quantity: int
    outbound_quantity: int
    in_warehouse_quantity: int


def get_today_statistics(company_id: int) -> StatisticsData:
    """Calculates statistics for the current day directly from source models."""
    today = timezone.localdate()

    # Today's Inbound (Approved Inbound Orders today)
    today_inbound = (
        InboundOrder.objects.filter(
            company_id=company_id, approve_status=2, approve_date__date=today, is_deleted=False
        ).aggregate(total=Sum("total_quantity"))["total"]
        or 0
    )

    # Today's Outbound (Approved Sales/Loan Orders today)
    today_outbound = (
        ERPOrder.objects.filter(
            company_id=company_id, approve_status=2, approve_time__date=today, sales_type__in=[1, 2], is_deleted=False
        ).aggregate(total=Sum("erporderdetails__quantity"))["total"]
        or 0
    )

    # Current In Warehouse (Sum of in_warehouse_quantity from InboundOrderDetail)
    # This represents the *current* stock level, irrespective of today's movements.
    current_in_warehouse = (
        InboundOrderDetail.objects.filter(inbound_order__company_id=company_id, is_deleted=False).aggregate(
            total=Sum("in_warehouse_quantity")
        )["total"]
        or 0
    )

    return {
        "inbound_quantity": today_inbound,
        "outbound_quantity": today_outbound,
        "in_warehouse_quantity": current_in_warehouse,
    }


def get_month_statistics(company_id: int, target_date: date) -> StatisticsData:
    """Calculates statistics for the month containing target_date using DailyCompanyStatistics."""
    # No need to calculate month_end, Django's __year and __month lookups handle it.

    monthly_stats = ERPDailyCompanyStatistics.objects.filter(
        company_id=company_id, date__year=target_date.year, date__month=target_date.month
    ).aggregate(total_inbound=Sum("total_inbound_quantity"), total_outbound=Sum("total_outbound_quantity"))

    month_inbound = monthly_stats.get("total_inbound") or 0
    month_outbound = monthly_stats.get("total_outbound") or 0

    # In-warehouse for the month is calculated as specified: monthly inbound - monthly outbound
    month_in_warehouse = month_inbound - month_outbound

    return {
        "inbound_quantity": month_inbound,
        "outbound_quantity": month_outbound,
        "in_warehouse_quantity": month_in_warehouse,
    }