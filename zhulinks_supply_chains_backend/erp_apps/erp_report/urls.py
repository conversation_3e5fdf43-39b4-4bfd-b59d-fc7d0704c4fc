from django.urls import path

from erp_apps.erp_report.views import *  # noqa: F403

# 报表|汇总
report_urls = [
    # 供应商付款汇总
    path("supplier/settle", SupplierReportAmountSummaryView.as_view(), name="supplier_report_summary"),
    # 供应商资金流水
    path("supplier/settle/flow", SupplierReportSettleOrderView.as_view(), name="supplier_report_flow"),
    # 库存报表-库存统计
    path("inventory/summary", InventoryReportSummaryView.as_view(), name="inventory_report_summary"),
    # 库存报表-库存周转
    path("inventory/sku/list", InventoryReportSkuListView.as_view(), name="inventory_report_sku_list"),

    # 客户资金统计
    path("customer/fund_stat", CustomerFundStatisticsView.as_view(), name="customer_report.fund_stat"),
    # 客户资金流水
    path("customer/fund_flow", CustomerFundFlowView.as_view(), name="customer_report.fund_flow"),
    # 销售报表
    path("sales_stat/panel", SalesStatisticsPanelView.as_view(), name="sales_statistics.panel"),
    # 销售总额
    path("sales_stat/total_amount", SalesTotalAmountStatisticsView.as_view(), name="sales_statistics.total_amount"),
    # 销售报表列表数据
    path("sales_stat/list", SalesStatisticsListView.as_view(), name="sales_statistics.list"),
]


urlpatterns = report_urls
