import random
import threading
from datetime import datetime

from rest_framework import serializers


def decimal_to_base62(num):
    """10 进制转 62 进制"""
    if not num:
        return ""

    chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
    if num == 0:
        return "0"

    base62_str = ""
    base = 62
    while num > 0:
        remainder = num % base
        base62_str = chars[remainder] + base62_str
        num //= base

    return base62_str


def base62_to_decimal(base62_str):
    """62 进制转 10 进制"""
    chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
    base = 62
    num = 0

    for char in base62_str:
        num = num * base + chars.index(char)

    return num


class UserTrackingModelSerializer(serializers.ModelSerializer):
    """
    基础序列化器，自动为模型的 `create_user` 和 `update_user` 字段设置当前用户。
    """

    def get_current_user(self):
        request = self.context.get("request")
        if request and hasattr(request, "user"):
            return request.user
        return None  # 如果没有用户（例如匿名用户），则返回 None

    def create(self, validated_data):
        # 获取当前用户
        user = self.get_current_user()

        # 调用父类的 create 方法，创建实例
        instance = super().create(validated_data)

        # 如果是创建操作，设置 `create_user`
        if user and hasattr(instance, "create_user"):
            instance.create_user = user.user_id
            instance.save()

        return instance

    def update(self, instance, validated_data):
        # 获取当前用户
        user = self.get_current_user()

        # 调用父类的 update 方法，更新实例
        instance = super().update(instance, validated_data)

        # 如果是更新操作，设置 `update_user`
        if user and hasattr(instance, "update_user"):
            instance.update_user = user.user_id
            instance.save()

        return instance


class CompactSnowflake:
    def __init__(self, node_id):
        """
        :param node_id: 节点ID (59-99)
        """
        if not 58 <= node_id <= 99:
            raise ValueError("节点ID必须在59-99之间")
        self.node_id = node_id
        self.epoch = datetime(2025, 4, 1, 0, 0)  # 自定义纪元时间
        self.sequence = 0
        self.last_timestamp = -1
        # 使用文件锁以支持多进程
        self.lock = threading.Lock()
        # 增加一个随机偏移量，减少不同进程间的冲突
        self.offset = random.randint(0, 99)

    def _time_since_epoch(self):
        """获取当前时间与纪元时间的毫秒差"""
        delta = datetime.now() - self.epoch
        # 使用毫秒级时间戳提高精度
        return int(delta.total_seconds() * 1000)

    def generate(self):
        with self.lock:
            timestamp = self._time_since_epoch()

            if timestamp < self.last_timestamp:
                # 时钟回拨，使用上一次的时间戳并增加序列号
                timestamp = self.last_timestamp

            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) % 10000  # 增加到4位序列号
                # 如果序列号溢出，等待下一毫秒
                if self.sequence == 0:
                    # 等待下一毫秒
                    new_timestamp = self._time_since_epoch()
                    while new_timestamp <= timestamp:
                        new_timestamp = self._time_since_epoch()
                    timestamp = new_timestamp
            else:
                # 不同的时间戳，使用随机初始序列号减少冲突
                self.sequence = (self.offset + random.randint(0, 99)) % 10000

            self.last_timestamp = timestamp

            # 构造ID：节点ID(2) + 时间戳(8) + 序列号(4)
            # 使用毫秒时间戳的后8位
            return int(f"{self.node_id}{timestamp % 10**8:08d}{self.sequence:04d}")


# 使用更高的节点ID以减少冲突
tid_snow_flake_id_client = CompactSnowflake(78)

# 为了支持多进程，使用一个进程安全的计数器
_tid_counter = 0
_tid_counter_lock = threading.Lock()


def pad_base62(base62_str, length=7):
    """确保base62字符串达到指定长度，不足则在前面补'0'"""
    return base62_str.zfill(length)


def generate_tid():
    """
    生成唯一的7位tid
    :return: 7位base62字符串
    """
    global _tid_counter

    # 生成基础ID
    snowflake_id = tid_snow_flake_id_client.generate()

    # 添加一个进程安全的计数器，确保即使在同一毫秒内也能生成不同的ID
    with _tid_counter_lock:
        _tid_counter = (_tid_counter + 1) % 1000
        # 将计数器添加到ID中，确保唯一性
        unique_id = snowflake_id * 1000 + _tid_counter

    # 转换为base62
    base62_id = decimal_to_base62(unique_id)

    # 处理长度问题
    if len(base62_id) < 7:
        # 如果长度不足7位，在前面补0
        base62_id = pad_base62(base62_id, 7)
    elif len(base62_id) > 7:
        # 如果长度超过7位，使用哈希函数确保唯一性
        # 使用原始ID的哈希值的一部分，保留高位信息
        import hashlib

        hash_obj = hashlib.md5(str(unique_id).encode())
        hash_int = int(hash_obj.hexdigest(), 16)
        # 取哈希值的一部分，转换为base62，确保7位长度
        hash_base62 = decimal_to_base62(hash_int % (62**7))
        base62_id = pad_base62(hash_base62, 7)

    return base62_id
