from django.apps import AppConfig


class ErpProductsConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "erp_apps.erp_products"
    verbose_name = "ERP商品管理"

    def ready(self):
        from erp_apps.erp_products import signals
        from common.basic import m2m_through_handler
        from erp_apps.erp_products.models import ERPProduct, ERPStockKeepingUnit, ERPSupplierProductSKU
        from django.db.models.signals import m2m_changed

        signals.load()

        m2m_changed.connect(m2m_through_handler, sender=ERPProduct.specs.through)
        m2m_changed.connect(m2m_through_handler, sender=ERPProduct.specs_value.through)
        m2m_changed.connect(m2m_through_handler, sender=ERPStockKeepingUnit.specs_name.through)
        m2m_changed.connect(m2m_through_handler, sender=ERPStockKeepingUnit.specs_value.through)

        #
        m2m_changed.connect(m2m_through_handler, sender=ERPSupplierProductSKU.attr_value.through)
