# -*- coding: utf-8 -*-
import copy
import decimal

from django.db import transaction

from common.basics.views import set_log_params
from common.basics.exceptions import APIViewException
from common.utils import diff_models
from erp_apps import logger
from erp_apps.erp_products.models import ERPProduct, ERPStockKeepingUnit
from erp_apps.erp_products.serializers import ERPProductCreateOrUpdateSer, ERPSKUCreateOrUpdateSer
from erp_apps.erp_products.signals import update_product_min_max_data_signal
from products.models import SpecsKey, SpecsValue
from utils.http_handle import FieldsError


class ERPProductService:

    @classmethod
    def clean_specs(cls, specs_list, skus_data, user, user_type):
        # 校验specs_list
        if not isinstance(specs_list, list):
            raise APIViewException(err_message="非法规格")

        if skus_data is None:
            skus_data = []

        spu_spec_key_ids = set()
        spu_spec_value_ids = set()

        # value更改map, old_id: {"new_value_id": 123}
        spec_value_change_map = {}

        for specs in specs_list:
            key_id = specs.get("source_id")
            key_name = specs.get("name")
            values = specs.get("values")

            if not isinstance(values, list):
                raise APIViewException(err_message="非法规格值")

            # 校验name
            if not SpecsKey.objects.filter(id=key_id, name=key_name).exists():
                raise APIViewException(err_message=f"规格名称:[{key_name}]不存在,请刷新页面后重试")

            spu_spec_key_ids.add(key_id)
            for val in values:
                if not isinstance(val, dict):
                    raise APIViewException(err_message=f"规格值类型错误")

                value_id = val.get("value_id")
                text = val.get("text")

                # key_id可能为新数据, 要新建value
                if not SpecsValue.objects.filter(id=value_id, value=text, spec_key_id=key_id).exists():
                    tmp_params = {
                        "data_source": user_type,
                        "value": text,
                        "spec_key_id": key_id,
                        "defaults": {
                            "create_user": user.user_id,
                        },
                    }
                    if user_type == "DB":
                        tmp_params["distributor_id"] = user.distributor.distributor_id
                    elif user_type == "SP":
                        tmp_params["supplier_id"] = user.company.company_id

                    val_instance, created = SpecsValue.objects.get_or_create(**tmp_params)
                    # 重新赋值
                    _old_value_id, value_id = value_id, val_instance.id
                    val["value_id"] = value_id
                    val["old_value_id"] = _old_value_id
                    spec_value_change_map[_old_value_id] = value_id

                spu_spec_value_ids.add(value_id)
        # 判断sku的specs是否与spu信息对应
        sku_spec_key_ids = set()
        sku_spec_value_ids = set()

        for sku_data in skus_data:
            specs = sku_data.get("specs", [])
            for spec in specs:
                key_id = spec.get("name_id")
                key_name = spec.get("name")
                value_id = spec.get("value_id")
                value = spec.get("value")

                if not SpecsKey.objects.filter(id=key_id, name=key_name).exists():
                    raise APIViewException(err_message=f"SKU规格名称:[{key_name}]不存在,请刷新页面后重试")

                if not SpecsValue.objects.filter(id=value_id, value=value, spec_key_id=key_id).exists():
                    if not spec_value_change_map.get(value_id):
                        raise APIViewException(err_message=f"SKU规格值:{value}不存在,请刷新页面后重试")

                    new_value_id = spec_value_change_map.get(value_id)
                    # 修改
                    spec["value_id"] = new_value_id
                    value_id = new_value_id

                if key_id:
                    sku_spec_key_ids.add(key_id)

                if value_id:
                    sku_spec_value_ids.add(value_id)
            # 处理好顺序
            # 重新排列第二个列表
            reordered_list_2 = []
            for item_1 in specs_list:
                source_id = item_1["source_id"]
                for item_2 in specs:
                    if item_2["name_id"] == source_id:
                        reordered_list_2.append(item_2)
                        break
            sku_data["specs"] = reordered_list_2

        if spu_spec_key_ids.difference(sku_spec_key_ids):
            raise APIViewException(err_message="商品规格名与SKU信息不符合,请刷新页面后重试")

        if spu_spec_value_ids.difference(sku_spec_value_ids):
            raise APIViewException(err_message="商品规格值与SKU信息不符合,请刷新页面后重试")

    @classmethod
    def create_erp_product(cls, post_data, user, user_type, request=None, internal=False):
        post_data["create_user"] = user.user_id

        skus_data = post_data.pop("skus", [])
        if not skus_data:
            raise APIViewException(err_message="missing skus")

        spec_lists = post_data.pop("spec_lists", [])
        with transaction.atomic():
            # 清洗specs
            cls.clean_specs(spec_lists, skus_data, user, user_type)
            # 重新赋值新的spec_list
            post_data["spec_lists"] = spec_lists
            # 处理商品
            product_create_ser = ERPProductCreateOrUpdateSer(data=post_data, context={"request": request})
            if not product_create_ser.is_valid():
                raise FieldsError(product_create_ser.errors)
            product_instance = product_create_ser.save()

            # 获取供应商code, 生成商编使用
            company_code = getattr(product_instance, "_company_code_", None)

            multiple = product_instance.multiple
            # 赋值SKU并创建sku
            for idx, sku_data in enumerate(skus_data):
                sku_data["product_id"] = product_instance.product_id
                sku_data["order"] = idx
                sku_data["create_user"] = user.user_id

                # 内部调用不用重新赋值
                if not internal:
                    sku_data["spec_code"] = ERPStockKeepingUnit.generate_spec_code(company_code)

                if multiple:
                    sku_data["label_price"] = (decimal.Decimal(sku_data.get("cost_price") or 0)) * int(multiple)
            sku_create_ser = ERPSKUCreateOrUpdateSer(data=skus_data, many=True, context={"request": request})
            if not sku_create_ser.is_valid():
                raise FieldsError(sku_create_ser.errors)
            sku_create_ser.save()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(product_instance.product_id),
                model=ERPProduct,
                describe=f"新增了商品: {product_instance}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass
        return product_instance

    @classmethod
    def update_erp_product(cls, instance, post_data, user, user_type, request=None):
        post_data["update_user"] = user.user_id
        skus_data = post_data.pop("skus", [])
        if not skus_data:
            raise APIViewException(err_message="missing skus")

        raw_product = copy.deepcopy(instance)
        product_id = raw_product.product_id
        spec_lists = post_data.pop("spec_lists", [])

        sku_change_log_list = []

        with transaction.atomic():
            # 清洗specs
            cls.clean_specs(spec_lists, skus_data, user, user_type)
            # 重新赋值新的spec_list
            post_data["spec_lists"] = spec_lists
            # 处理商品
            product_update_ser = ERPProductCreateOrUpdateSer(
                instance=instance,
                data=post_data,
                partial=True,
                context={"request": request},
            )
            if not product_update_ser.is_valid():
                raise FieldsError(product_update_ser.errors)

            updated_product = product_update_ser.save()

            # 获取供应商code, 生成商编使用
            company_code = getattr(updated_product, "_company_code_", None)

            multiple = updated_product.multiple
            existing_erp_skus = instance.erpstockkeepingunit_set.filter(become_history=False)
            existing_erp_skus_map = {existing_erp_sku.sku_id: existing_erp_sku for existing_erp_sku in existing_erp_skus}

            keep_sku_pk_list = []
            # 赋值SKU并创建sku
            for idx, sku_data in enumerate(skus_data):
                sku_instance = None
                if "sku_id" in sku_data:
                    if int(sku_data["sku_id"]) not in existing_erp_skus_map:
                        raise APIViewException(err_message="invalid sku info")
                    sku_instance = existing_erp_skus_map[int(sku_data["sku_id"])]
                else:
                    # 判断规格id是否相同，相同默认是原来的数据
                    post_key_ids = {i["name_id"] for i in sku_data["specs"] if i.get("name_id")}
                    post_val_ids = {i["value_id"] for i in sku_data["specs"] if i.get("value_id")}
                    for existing_erp_sku in existing_erp_skus:
                        exist_erp_sku_key_ids = set(existing_erp_sku.specs_name.values_list("id", flat=True))
                        exist_erp_sku_val_ids = set(existing_erp_sku.specs_value.values_list("id", flat=True))
                        if post_key_ids == exist_erp_sku_key_ids and post_val_ids == exist_erp_sku_val_ids:
                            sku_instance = existing_erp_sku

                sku_data["order"] = idx

                if multiple:
                    sku_data["label_price"] = decimal.Decimal(sku_data.get("cost_price") or 0) * int(multiple)

                # 新增sku
                if not sku_instance:
                    sku_data["product_id"] = raw_product.product_id
                    sku_data["create_user"] = user.user_id
                    sku_data["spec_code"] = ERPStockKeepingUnit.generate_spec_code(company_code)
                    sku_create_ser = ERPSKUCreateOrUpdateSer(data=sku_data, context={"request": request})
                    if not sku_create_ser.is_valid():
                        raise FieldsError(sku_create_ser.errors)
                    new_sku = sku_create_ser.save()
                    sku_change_log_list.append(f"新增SKU：{new_sku}")
                    keep_sku_pk_list.append(new_sku.pk)
                else:
                    sku_data["update_user"] = user.user_id
                    sku_data["spec_code"] = sku_instance.spec_code
                    sku_update_ser = ERPSKUCreateOrUpdateSer(
                        instance=sku_instance,
                        data=sku_data,
                        context={"request": request},
                        partial=True,
                    )
                    if not sku_update_ser.is_valid():
                        raise FieldsError(sku_update_ser.errors)
                    new_sku = sku_update_ser.save()

                    diff_content = diff_models(sku_instance, new_sku)
                    if diff_content:
                        sku_change_log_list.append(f"更新SKU{sku_instance}-->{new_sku}: {diff_content}")

                    keep_sku_pk_list.append(sku_instance.pk)

                # 删除sku
                if keep_sku_pk_list:
                    need_update_skus = instance.erpstockkeepingunit_set.filter(become_history=False).exclude(pk__in=keep_sku_pk_list)
                    for need_update_sku in need_update_skus:
                        need_update_sku.become_history = False
                        need_update_sku.update_user = user.user_id
                        need_update_sku.save()

            # 更新
            update_product_min_max_data_signal.send(sender=ERPProduct, product_id=product_id)

            # 日志记录
            try:
                product_changed_content = diff_models(raw_product, updated_product)
                sku_changed_content = "、".join(sku_change_log_list) if sku_change_log_list else ""
                operate_content = ""
                if product_changed_content and sku_changed_content:
                    operate_content = product_changed_content + "\n" + sku_changed_content
                elif product_changed_content and not sku_changed_content:
                    operate_content = product_changed_content
                elif not product_changed_content and sku_changed_content:
                    operate_content = sku_changed_content

                set_log_params(
                    request,
                    resource_id=str(product_id),
                    model=ERPProduct,
                    describe=f"更新了商品: {updated_product}",
                    operate_content=operate_content,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

        return raw_product, updated_product

    @classmethod
    def generate_product_sync_data(cls, sub_product, product):
        from products.serializers import get_product_specs_detail
        from products.bulk_query import get_skus_specs_detail
        from products.serializers import ProductAttrOptionInfoSer

        skus = product.stockkeepingunit_set.all()

        distributor = sub_product.owner
        attr_options = product.productattroption_set.select_related("attr", "attr_value")

        data = {
            "name": sub_product.name,
            "category": product.category,
            "code": sub_product.code,
            "attr_options": ProductAttrOptionInfoSer(instance=attr_options, many=True).data,
            "address": product.address_id,
            "size": sub_product.size,
            "unit": product.unit_id,
            "company": [product.company.company_id],
            "remark": sub_product.remark,
            "spec_lists": get_product_specs_detail(product),
            "skus": [
                {
                    "spec_code": f"{sku.spec_code}{distributor.letters}",
                    "physical_inventory": sku.physical_inventory,
                    "safety_inventory": sku.safety_inventory,
                    "cost_price": sku.cost_price,
                    "retail_price": sku.retail_price,
                    "weight": sku.weight,
                    "image": sku.image,
                    "specs": get_skus_specs_detail(sku),
                }
                for sku in skus
            ],
            "main_images": sub_product.main_images,
            "detail_images": sub_product.detail_images,
            "distributor_id": sub_product.owner_id,
        }
        return data
