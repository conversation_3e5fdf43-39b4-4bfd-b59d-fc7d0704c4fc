# Generated by Django 5.1.2 on 2025-02-26 07:11

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("erp_products", "0003_alter_erpproduct_options_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="erpproduct",
            name="expire_date",
        ),
        migrations.RemoveField(
            model_name="erpproduct",
            name="is_combine",
        ),
        migrations.RemoveField(
            model_name="erpproduct",
            name="is_new",
        ),
        migrations.RemoveField(
            model_name="erpproduct",
            name="origin_product",
        ),
        migrations.RemoveField(
            model_name="erpproduct",
            name="state",
        ),
        migrations.RemoveField(
            model_name="erpproduct",
            name="state_reason",
        ),
        migrations.RemoveField(
            model_name="erpproduct",
            name="state_remark",
        ),
        migrations.RemoveField(
            model_name="historicalerpproduct",
            name="expire_date",
        ),
        migrations.RemoveField(
            model_name="historicalerpproduct",
            name="is_combine",
        ),
        migrations.RemoveField(
            model_name="historicalerpproduct",
            name="is_new",
        ),
        migrations.RemoveField(
            model_name="historicalerpproduct",
            name="origin_product",
        ),
        migrations.RemoveField(
            model_name="historicalerpproduct",
            name="state",
        ),
        migrations.RemoveField(
            model_name="historicalerpproduct",
            name="state_reason",
        ),
        migrations.RemoveField(
            model_name="historicalerpproduct",
            name="state_remark",
        ),
    ]
