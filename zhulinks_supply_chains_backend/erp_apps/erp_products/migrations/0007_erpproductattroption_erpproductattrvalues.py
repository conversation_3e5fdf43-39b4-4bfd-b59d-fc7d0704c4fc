# Generated by Django 5.1.2 on 2025-02-27 10:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_products", "0006_erpproduct_owner_erpstockkeepingunit_owner_and_more"),
        ("products", "0381_productlabels_fronted_display"),
    ]

    operations = [
        migrations.CreateModel(
            name="ERPProductAttrOption",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=8, verbose_name="选择的属性名")),
                ("value", models.Char<PERSON>ield(max_length=8, verbose_name="选择的属性值")),
                (
                    "attr",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.productattrlist",
                    ),
                ),
                (
                    "attr_value",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.productattrvalues",
                        verbose_name="属性值",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpproduct",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品属性",
                "verbose_name_plural": "商品属性",
                "constraints": [
                    models.UniqueConstraint(
                        fields=("product", "attr", "attr_value"),
                        name="unique_erp_product_attr_value",
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="ERPProductAttrValues",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "name",
                    models.CharField(default="", max_length=32, verbose_name="值名称"),
                ),
                (
                    "order",
                    models.PositiveSmallIntegerField(
                        default=0, help_text="数字越大排越后", verbose_name="排序"
                    ),
                ),
                (
                    "attr",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.productattrlist",
                        verbose_name="所属属性",
                    ),
                ),
            ],
            options={
                "verbose_name": "属性值映射表",
                "verbose_name_plural": "属性值映射表",
                "ordering": ("order",),
                "constraints": [
                    models.UniqueConstraint(
                        condition=models.Q(("is_deleted", False)),
                        fields=("attr_id", "name", "is_deleted"),
                        name="unique_erp_attr_name_is_not_deleted",
                    )
                ],
            },
        ),
    ]
