# Generated by Django 5.0.8 on 2025-03-25 08:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_client", "0001_initial"),
        ("erp_products", "0023_alter_erpproductattrkey_options_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="erpsupplierproduct",
            old_name="category_id",
            new_name="category",
        ),
        migrations.AddField(
            model_name="erpsupplierproduct",
            name="supplier",
            field=models.ForeignKey(
                db_constraint=False,
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_client.erpcompanysupplierinfo",
                to_field="supplier_id",
                verbose_name="系统用户供应商端的供应商",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="supplier",
            field=models.ForeignKey(
                db_constraint=False,
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_client.erpcompanysupplierinfo",
                to_field="supplier_id",
                verbose_name="系统用户供应商端的供应商",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="erpsupplierproductsku",
            name="company",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="companies.company",
                to_field="company_id",
                verbose_name="所属供应商端",
            ),
        ),
    ]
