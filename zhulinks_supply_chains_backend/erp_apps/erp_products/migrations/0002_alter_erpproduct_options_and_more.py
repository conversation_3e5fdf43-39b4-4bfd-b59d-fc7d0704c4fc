# Generated by Django 5.1.2 on 2025-02-24 08:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_products", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="erpproduct",
            options={
                "permissions": (
                    ("view_erp_min_cost_price_Product", "查看商品最小推广价"),
                    ("change_erp_min_cost_price_Product", "修改商品最小推广价"),
                    ("view_erp_max_cost_price_Product", "查看商品最大推广价"),
                    ("change_erp_max_cost_price_Product", "修改商品最大推广价"),
                    ("show_erp_max_cost_price_Product", "最大推广价、供应商不显示"),
                    ("show_erp_min_cost_price_Product", "最小推广价、供应商不显示"),
                ),
                "verbose_name": "ERP商品",
                "verbose_name_plural": "ERP商品",
            },
        ),
        migrations.AlterModelOptions(
            name="erpstockkeepingunit",
            options={
                "ordering": ("order", "id"),
                "permissions": (
                    ("view_erp_cost_price_StockKeepingUnit", "查看sku推广价"),
                    ("change_erp_cost_price_StockKeepingUnit", "修改sku推广价"),
                    ("show_erp_cost_price_StockKeepingUnit", "推广价、供应商不显示"),
                ),
                "verbose_name": "ERP商品sku",
                "verbose_name_plural": "ERP商品sku",
            },
        ),
        migrations.AddField(
            model_name="erpproduct",
            name="other_attributes",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性"
            ),
        ),
        migrations.AddField(
            model_name="erpproduct",
            name="other_prices1",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格1",
            ),
        ),
        migrations.AddField(
            model_name="erpproduct",
            name="other_prices2",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格2",
            ),
        ),
        migrations.AddField(
            model_name="erpproduct",
            name="session_date",
            field=models.DateTimeField(blank=True, null=True, verbose_name="场次日期"),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="other_attributes",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性"
            ),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="other_prices1",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格1",
            ),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="other_prices2",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格2",
            ),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="session_date",
            field=models.DateTimeField(blank=True, null=True, verbose_name="场次日期"),
        ),
    ]
