# Generated by Django 5.0.8 on 2025-03-25 02:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_products", "0020_alter_erpstockkeepingunit_remark_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ERPProductAttrKey",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("name", models.CharField(max_length=32, verbose_name="属性KEY")),
                (
                    "order",
                    models.PositiveSmallIntegerField(default=0, verbose_name="排序"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "供应商ERP商品属性",
                "verbose_name_plural": "供应商ERP商品属性",
                "ordering": ("-order",),
            },
        ),
        migrations.CreateModel(
            name="ERPProductAttrValue",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("value", models.CharField(max_length=32, verbose_name="属性值")),
                (
                    "order",
                    models.PositiveSmallIntegerField(default=0, verbose_name="排序"),
                ),
                (
                    "attr_key",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="values",
                        to="erp_products.erpproductattrkey",
                        verbose_name="属性键",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "ERP商品属性值",
                "verbose_name_plural": "ERP商品属性值",
                "ordering": ("-order",),
            },
        ),
        migrations.CreateModel(
            name="ERPProductCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("name", models.CharField(max_length=28, verbose_name="分类名称")),
                ("level", models.PositiveIntegerField(default=1, verbose_name="层级")),
                (
                    "order",
                    models.PositiveSmallIntegerField(
                        default=0, help_text="数字越大排越前", verbose_name="排序"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpproductcategory",
                        verbose_name="父级分类",
                    ),
                ),
            ],
            options={
                "verbose_name": "供应商ERP分类",
                "verbose_name_plural": "供应商ERP分类",
                "ordering": ("-order",),
            },
        ),
        migrations.CreateModel(
            name="ERPProductUnit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("name", models.CharField(max_length=16, verbose_name="单位名称")),
                (
                    "order",
                    models.PositiveSmallIntegerField(default=0, verbose_name="排序"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "ERP商品单位",
                "verbose_name_plural": "ERP商品单位",
                "ordering": ("-order",),
            },
        ),
    ]
