# Generated by Django 5.0.8 on 2025-04-07 11:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_products", "0037_remove_erpproductskutid_tid_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="erpsupplierproduct",
            name="attr_value",
            field=models.ManyToManyField(
                to="erp_products.erpproductattrvalue", verbose_name="关联属性值"
            ),
        ),
        migrations.AlterField(
            model_name="erpsupplierproductsku",
            name="attr_value",
            field=models.ManyToManyField(
                to="erp_products.erpproductattrvalue", verbose_name="关联属性值"
            ),
        ),
    ]
