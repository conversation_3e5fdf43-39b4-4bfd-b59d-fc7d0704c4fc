# Generated by Django 5.0.8 on 2025-03-10 10:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "erp_products",
            "0014_erpproductlabels_remove_erpproductlabelsrelate_label_and_more",
        ),
    ]

    operations = [
        migrations.RemoveField(
            model_name="erpstockkeepingunit",
            name="labels",
        ),
        migrations.AddField(
            model_name="erpproduct",
            name="max_label_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="最高标签价",
            ),
        ),
        migrations.AddField(
            model_name="erpproduct",
            name="min_label_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="最低标签价",
            ),
        ),
        migrations.AddField(
            model_name="erpproduct",
            name="multiple",
            field=models.IntegerField(
                blank=True, default=None, null=True, verbose_name="标签价倍率"
            ),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="max_label_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="最高标签价",
            ),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="min_label_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="最低标签价",
            ),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="multiple",
            field=models.IntegerField(
                blank=True, default=None, null=True, verbose_name="标签价倍率"
            ),
        ),
    ]
