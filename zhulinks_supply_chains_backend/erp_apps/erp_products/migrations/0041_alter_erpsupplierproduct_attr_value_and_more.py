# Generated by Django 5.0.8 on 2025-04-18 07:55

import erp_apps.erp_products.models.erp_supplier_product_models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_products", "0040_erpsupplierproductsku_tag_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="erpsupplierproduct",
            name="attr_value",
            field=models.ManyToManyField(
                blank=True,
                to="erp_products.erpproductattrvalue",
                verbose_name="关联属性值",
            ),
        ),
        migrations.AlterField(
            model_name="erpsupplierproductsku",
            name="attr_value",
            field=models.ManyToManyField(
                blank=True,
                to="erp_products.erpproductattrvalue",
                verbose_name="关联属性值",
            ),
        ),
        migrations.AlterField(
            model_name="erpsupplierproductsku",
            name="sku_id",
            field=models.BigIntegerField(
                db_index=True,
                default=erp_apps.erp_products.models.erp_supplier_product_models._erp_product_sku_id_generator,
                editable=False,
                unique=True,
                verbose_name="SKU_ID",
            ),
        ),
    ]
