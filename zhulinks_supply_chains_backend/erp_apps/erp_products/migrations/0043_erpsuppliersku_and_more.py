# Generated by Django 5.0.8 on 2025-05-13 07:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_client", "0003_erpcompanycustomerinfo_company"),
        ("erp_products", "0042_erpsupplierproductsku_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="ERPSupplierSKU",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product_type",
                    models.CharField(
                        choices=[
                            ("FN", "成品"),
                            ("SF", "半成品"),
                            ("RM", "原材料"),
                            ("PM", "包材"),
                        ],
                        default="FN",
                        verbose_name="商品类型",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=60,
                        null=True,
                        verbose_name="商品名称",
                    ),
                ),
                (
                    "cost_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="成本价"
                    ),
                ),
                (
                    "label_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="标签价"
                    ),
                ),
                (
                    "market_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="市场|吊牌价",
                    ),
                ),
                (
                    "purchase_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="采购价",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpproductcategory",
                        verbose_name="所属分类",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商端",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpsupplierproduct",
                        to_field="product_id",
                        verbose_name="关联商品",
                    ),
                ),
                (
                    "sku",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpsupplierproductsku",
                        verbose_name="关联sku信息",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_client.erpcompanysupplierinfo",
                        to_field="supplier_id",
                        verbose_name="系统用户供应商端的供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "供应商SKU信息",
                "verbose_name_plural": "供应商SKU信息",
            },
        ),
        migrations.AddConstraint(
            model_name="erpsuppliersku",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("company", "sku", "supplier"),
                name="unique_company_sku_supplier_in_supplier_sku",
            ),
        ),
    ]
