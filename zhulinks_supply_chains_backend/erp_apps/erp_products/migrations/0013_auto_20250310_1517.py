# Generated by Django 5.0.8 on 2025-03-10 07:17

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("erp_products", "0012_auto_20250310_1515"),
    ]

    operations = [
        migrations.RunSQL(
            "ALTER TABLE erp_products_historicalerpproduct ALTER COLUMN category TYPE jsonb USING to_jsonb(category);",
            reverse_sql="ALTER TABLE erp_products_historicalerpproduct ALTER COLUMN category TYPE varchar[] USING ARRAY[category::varchar];",
        ),
        migrations.RunSQL(
            "ALTER TABLE erp_products_historicalerpproduct ALTER COLUMN main_images TYPE jsonb USING to_jsonb(main_images);",
            reverse_sql="ALTER TABLE erp_products_historicalerpproduct ALTER COLUMN main_images TYPE varchar[] USING ARRAY[main_images::varchar];",
        ),
        migrations.RunSQL(
            "ALTER TABLE erp_products_historicalerpproduct ALTER COLUMN spec_lists TYPE jsonb USING to_jsonb(spec_lists);",
            reverse_sql="ALTER TABLE erp_products_historicalerpproduct ALTER COLUMN spec_lists TYPE varchar[] USING ARRAY[spec_lists::varchar];",
        ),
        migrations.RunSQL(
            "ALTER TABLE erp_products_historicalerpstockkeepingunit ALTER COLUMN specs TYPE jsonb USING to_jsonb(specs);",
            reverse_sql="ALTER TABLE erp_products_historicalerpstockkeepingunit ALTER COLUMN specs TYPE varchar[] USING ARRAY[specs::varchar];",
        ),
    ]
