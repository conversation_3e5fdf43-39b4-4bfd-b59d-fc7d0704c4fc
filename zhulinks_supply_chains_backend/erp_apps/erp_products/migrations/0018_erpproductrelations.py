# Generated by Django 5.0.8 on 2025-03-11 15:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_products", "0017_erpproduct_can_use_inventory_and_more"),
        ("products", "0381_productlabels_fronted_display"),
    ]

    operations = [
        migrations.CreateModel(
            name="ERPProductRelations",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "erp_product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_products.erpproduct",
                        to_field="product_id",
                        verbose_name="关联erp商品",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        to_field="product_id",
                        verbose_name="关联主商品",
                    ),
                ),
                (
                    "sub_product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.subproduct",
                        to_field="product_id",
                        verbose_name="关联副本商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "erp商品关联商品",
                "verbose_name_plural": "erp商品关联商品",
            },
        ),
    ]
