# Generated by Django 5.0.8 on 2025-03-29 03:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_products", "0031_alter_erpsupplierproductsku_weight_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="other_attributes2",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性2"
            ),
        ),
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="other_attributes3",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性3"
            ),
        ),
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="other_attributes4",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性4"
            ),
        ),
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="other_attributes5",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性5"
            ),
        ),
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="other_prices3",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格3",
            ),
        ),
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="other_prices4",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格4",
            ),
        ),
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="other_prices5",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格5",
            ),
        ),
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="other_prices6",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格6",
            ),
        ),
        migrations.AddField(
            model_name="historicalerpsupplierproductsku",
            name="other_attributes2",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性2"
            ),
        ),
        migrations.AddField(
            model_name="historicalerpsupplierproductsku",
            name="other_attributes3",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性3"
            ),
        ),
        migrations.AddField(
            model_name="historicalerpsupplierproductsku",
            name="other_attributes4",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性4"
            ),
        ),
        migrations.AddField(
            model_name="historicalerpsupplierproductsku",
            name="other_attributes5",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="其他属性5"
            ),
        ),
        migrations.AddField(
            model_name="historicalerpsupplierproductsku",
            name="other_prices3",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格3",
            ),
        ),
        migrations.AddField(
            model_name="historicalerpsupplierproductsku",
            name="other_prices4",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格4",
            ),
        ),
        migrations.AddField(
            model_name="historicalerpsupplierproductsku",
            name="other_prices5",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格5",
            ),
        ),
        migrations.AddField(
            model_name="historicalerpsupplierproductsku",
            name="other_prices6",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="其他价格6",
            ),
        ),
    ]
