# Generated by Django 5.0.8 on 2025-03-25 11:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_products", "0028_erpsupplierproductsku_attr_value_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="erpsupplierproductsku",
            name="category",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_products.erpproductcategory",
                verbose_name="所属分类",
            ),
        ),
        migrations.AddField(
            model_name="historicalerpsupplierproductsku",
            name="category",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="erp_products.erpproductcategory",
                verbose_name="所属分类",
            ),
        ),
    ]
