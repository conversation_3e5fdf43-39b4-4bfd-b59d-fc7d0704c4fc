# Generated by Django 5.0.8 on 2025-03-11 08:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "erp_products",
            "0016_alter_erpproductlabels_options_erpproduct_state_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="erpproduct",
            name="can_use_inventory",
            field=models.BigIntegerField(
                blank=True, default=0, null=True, verbose_name="可用库存"
            ),
        ),
        migrations.AddField(
            model_name="erpproduct",
            name="safety_inventory",
            field=models.BigIntegerField(
                blank=True, default=0, null=True, verbose_name="7天补货库存"
            ),
        ),
        migrations.AddField(
            model_name="erpproduct",
            name="warehouse_inventory",
            field=models.BigIntegerField(
                blank=True, default=0, null=True, verbose_name="仓库库存"
            ),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="can_use_inventory",
            field=models.BigIntegerField(
                blank=True, default=0, null=True, verbose_name="可用库存"
            ),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="safety_inventory",
            field=models.BigIntegerField(
                blank=True, default=0, null=True, verbose_name="7天补货库存"
            ),
        ),
        migrations.AddField(
            model_name="historicalerpproduct",
            name="warehouse_inventory",
            field=models.BigIntegerField(
                blank=True, default=0, null=True, verbose_name="仓库库存"
            ),
        ),
    ]
