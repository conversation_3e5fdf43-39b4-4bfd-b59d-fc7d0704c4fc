# Generated by Django 5.0.8 on 2025-03-25 11:08

import django.db.models.deletion
import erp_apps.erp_products.models.erp_supplier_product_models
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_client", "0001_initial"),
        ("erp_products", "0026_alter_erpsupplierproduct_category"),
        ("products", "0381_productlabels_fronted_display"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalERPSupplierProduct",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="更新时间"
                    ),
                ),
                (
                    "product_id",
                    models.BigIntegerField(
                        db_index=True,
                        default=erp_apps.erp_products.models.erp_supplier_product_models._erp_supplier_product_id_generator,
                        editable=False,
                        verbose_name="商品id",
                    ),
                ),
                (
                    "product_type",
                    models.CharField(
                        choices=[
                            ("FN", "成品"),
                            ("SF", "半成品"),
                            ("RM", "原材料"),
                            ("PM", "包材"),
                        ],
                        default="FN",
                        verbose_name="商品类型",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=60,
                        null=True,
                        verbose_name="商品名称",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default="",
                        max_length=20,
                        null=True,
                        verbose_name="货号",
                    ),
                ),
                (
                    "physical_inventory",
                    models.BigIntegerField(default=0, verbose_name="商品总现货库存"),
                ),
                (
                    "main_images",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="主图列表"
                    ),
                ),
                (
                    "remark",
                    models.TextField(blank=True, null=True, verbose_name="备注信息"),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="erp_products.erpproductcategory",
                        verbose_name="所属分类",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="erp_client.erpcompanysupplierinfo",
                        to_field="supplier_id",
                        verbose_name="系统用户供应商端的供应商",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="products.productunit",
                        verbose_name="商品单位",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品快照",
                "verbose_name_plural": "historical 供应商ERP商品信息",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalERPSupplierProduct_attr_value",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("m2m_history_id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "erpsupplierproduct",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        db_tablespace="",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="erp_products.erpsupplierproduct",
                    ),
                ),
                (
                    "history",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="erp_products.historicalerpsupplierproduct",
                    ),
                ),
                (
                    "productattrvalues",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        db_tablespace="",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="products.productattrvalues",
                    ),
                ),
            ],
            options={
                "verbose_name": "HistoricalERPSupplierProduct_attr_value",
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalERPSupplierProductSKU",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="更新时间"
                    ),
                ),
                (
                    "sku_id",
                    models.BigIntegerField(
                        db_index=True,
                        default=erp_apps.erp_products.models.erp_supplier_product_models._erp_product_sku_id_generator,
                        verbose_name="SKU_ID",
                    ),
                ),
                (
                    "spec_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=30,
                        null=True,
                        verbose_name="商品编码",
                    ),
                ),
                (
                    "physical_inventory",
                    models.BigIntegerField(default=0, verbose_name="现货库存"),
                ),
                (
                    "spec_value",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=60,
                        null=True,
                        verbose_name="颜色及规格",
                    ),
                ),
                (
                    "cost_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="成本价"
                    ),
                ),
                (
                    "label_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="标签价"
                    ),
                ),
                (
                    "market_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="市场|吊牌价",
                    ),
                ),
                (
                    "purchase_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="采购价",
                    ),
                ),
                (
                    "size",
                    models.CharField(
                        blank=True, max_length=25, null=True, verbose_name="尺寸"
                    ),
                ),
                (
                    "weight",
                    models.IntegerField(blank=True, null=True, verbose_name="重量(g)"),
                ),
                (
                    "batch_no",
                    models.CharField(
                        blank=True, max_length=25, null=True, verbose_name="批次号"
                    ),
                ),
                (
                    "image",
                    models.CharField(
                        blank=True, max_length=300, null=True, verbose_name="规格主图"
                    ),
                ),
                (
                    "min_inventory_capacity",
                    models.IntegerField(blank=True, null=True, verbose_name="库容下限"),
                ),
                (
                    "max_inventory_capacity",
                    models.IntegerField(blank=True, null=True, verbose_name="库容上限"),
                ),
                (
                    "remark",
                    models.TextField(blank=True, null=True, verbose_name="SKU备注信息"),
                ),
                (
                    "other_prices1",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="其他价格1",
                    ),
                ),
                (
                    "other_prices2",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="其他价格2",
                    ),
                ),
                (
                    "session_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="场次日期"
                    ),
                ),
                (
                    "other_attributes",
                    models.CharField(
                        blank=True, max_length=300, null=True, verbose_name="其他属性"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商端",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="erp_products.erpsupplierproduct",
                        to_field="product_id",
                        verbose_name="关联商品",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="erp_client.erpcompanysupplierinfo",
                        to_field="supplier_id",
                        verbose_name="系统用户供应商端的供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "sku快照表",
                "verbose_name_plural": "historical 供应商ERP商品SKU信息",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
