import json

import django_filters
from django.db.models import Q

from common.basic import BaseDateFilterSet, PriceRangeFilter, RangeFilter, HybridSearchFilterV2
from common.basics.exceptions import APIViewException
from common.basics.filtersets import IContainCharField
from companies.models import Company
from erp_apps.erp_products.models import ERPProduct
from products.models import ProductLabelsRelate


class ERPProductListFilterSet(BaseDateFilterSet):
    # 使用 ModelMultipleChoiceFilter 过滤 ManyToManyField
    company = django_filters.ModelMultipleChoiceFilter(
        queryset=Company.objects.all(),  # 可根据需要筛选 Company
        field_name="company",  # 关联的字段
        to_field_name="id",  # 可以指定过滤时使用的字段，通常是 id
        label="所属供应商",  # 标签
        conjoined=False,  # 是否连接多个过滤条件
        # method="filter_by_company",  # 自定义方法（可选）
    )

    product_id = IContainCharField()
    name = IContainCharField()
    category = django_filters.CharFilter(method="category_filter")
    cost_price = PriceRangeFilter()
    physical_inventory = RangeFilter()
    # has_DouDian_link = django_filters.BooleanFilter()
    code = django_filters.CharFilter(method="code_filter")
    spec_code = IContainCharField(field_name="stockkeepingunit__spec_code")
    spec_name = IContainCharField(field_name="specs__name")
    spec_value = IContainCharField(field_name="specs_value__value")
    style = django_filters.CharFilter(method="style_filter")
    labels_relate = django_filters.NumberFilter(method="labels_relate_filter")
    has_handcard = django_filters.BooleanFilter(method="has_handcard_filter")
    hybrid_search = HybridSearchFilterV2(
        field_names=(
            "name",
            "product_id",
            "__code",
            "remark",
            "stockkeepingunit__link_code",
            "stockkeepingunit__spec_code",
            "productlinkdistributor__code",
            "subproduct__alias_code",
        )
    )
    distributor_market_price = PriceRangeFilter()
    review_user = django_filters.CharFilter(method="review_user_filter")

    # 过滤分销推广价
    distributor_promotion_price = PriceRangeFilter()

    # 销量过滤
    sales_range = django_filters.RangeFilter(field_name="sales")

    # 供应商登记
    # company_grade = django_filters.AllValuesMultipleFilter(field_name="company__grade" )
    company_grade = django_filters.CharFilter(method="company_grade_filter")

    class Meta:
        model = ERPProduct
        fields = (
            "company",
            "product_id",
            "name",
            "category",
            "cost_price",
            "physical_inventory",
            # "has_DouDian_link",
            "code",
            "spec_code",
            # "state",
            # "company_id",
            "create_date",
            "update_date",
            "spec_name",
            "spec_value",
            "style",
            "address_id",
            # "is_centralized_purchasing",
            "labels_relate",
            "has_handcard",
            # "is_combine",
            "hybrid_search",
            "distributor_market_price",
            "distributor_promotion_price",
            "sales_range",
            "company_grade",
        )

    @staticmethod
    def category_filter(queryset, name, value):
        v = json.loads(value)
        if not isinstance(v, list):
            raise APIViewException(err_message="分类信息错误")

        return queryset.filter(category__contains=json.loads(value))

    @staticmethod
    def code_filter(queryset, name, value):
        if value and value[-1] == "J":
            value = value[:-1]

        return queryset.filter(
            Q(code__icontains=value)
            | Q(subproduct__alias_code__icontains=value)
            | Q(
                productlinkdistributor__code__icontains=value,
            ),
        ).distinct()

    @staticmethod
    def style_filter(queryset, name, value):
        return queryset.filter(productattroption__attr__name="款式", productattroption__attr_value__name=value)

    @staticmethod
    def labels_relate_filter(queryset, name, value):
        product_ids = list(set(ProductLabelsRelate.objects.filter(label_id=value, become_history=False).values_list("product_id", flat=True)))
        if not product_ids:
            return queryset.filter(pk=0)
        return queryset.filter(product_id__in=product_ids)

    @staticmethod
    def has_handcard_filter(queryset, name, value):
        return queryset.exclude(handcard__isnull=value)

    @staticmethod
    def review_user_filter(queryset, name, value):
        return queryset.filter(productreview__create_user=value).distinct()

    @staticmethod
    def company_grade_filter(queryset, name, value):
        return queryset.filter(company__grade__in=value)
