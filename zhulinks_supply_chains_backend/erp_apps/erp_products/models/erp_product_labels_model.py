# -*- coding: utf-8 -*-
from django.db import models


class ERPProductLabels(models.Model):
    name = models.CharField("标签名称", max_length=32)
    type = models.PositiveSmallIntegerField(
        "标签类型",
        choices=(
            (1, "系统标签"),
            (2, "自定义标签"),
        ),
        default=1,
    )
    create_user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        to_field="user_id",
        db_constraint=False,
        null=True,
        blank=True,
        verbose_name="创建人",
        related_name="created_erp_product_labels",
    )
    update_user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        to_field="user_id",
        db_constraint=False,
        null=True,
        blank=True,
        verbose_name="更新人",
        related_name="updated_erp_product_labels",
    )
    order = models.PositiveSmallIntegerField("排序", help_text="从小到大排序", default=0)
    is_deleted = models.BooleanField("是否删除", default=False)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品标签"
        verbose_name_plural = verbose_name
        ordering = ["order", "id"]

    def __str__(self):
        return f"{self.name}({self.pk})"
