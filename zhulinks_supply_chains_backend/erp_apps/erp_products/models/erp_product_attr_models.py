from django.db import models

from common.basics.models import BaseModel
from products.models import ProductAttrList, ProductAttrValues


class ERPProductAttrValues(BaseModel):
    attr = models.ForeignKey(
        ProductAttrList,
        verbose_name="所属属性",
        db_constraint=False,
        on_delete=models.CASCADE,
    )
    name = models.CharField(verbose_name="值名称", max_length=32, default="")
    order = models.PositiveSmallIntegerField("排序", help_text="数字越大排越后", default=0)

    class Meta:
        verbose_name = "属性值映射表"
        verbose_name_plural = verbose_name
        ordering = ("order",)

        constraints = (
            models.UniqueConstraint(
                fields=(
                    "attr_id",
                    "name",
                    "is_deleted",
                ),
                name="unique_erp_attr_name_is_not_deleted",
                condition=models.Q(is_deleted=False),
            ),
        )

    def __str__(self):
        return f"{self.name}({self.pk})"


class ERPProductAttrOption(models.Model):
    """
    商品属性，有商品关系, 已被商品选中
    """

    attr = models.ForeignKey(ProductAttrList, on_delete=models.CASCADE)
    product = models.ForeignKey("erp_products.ERPProduct", on_delete=models.CASCADE)
    name = models.CharField(max_length=8, verbose_name="选择的属性名")
    value = models.CharField("选择的属性值", max_length=8)
    attr_value = models.ForeignKey(
        ProductAttrValues,
        on_delete=models.CASCADE,
        verbose_name="属性值",
        db_constraint=False,
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = "商品属性"
        verbose_name_plural = verbose_name
        unique_together = [("product", "attr")]

    def __str__(self):
        return f"{self.name}({self.attr_id})"
