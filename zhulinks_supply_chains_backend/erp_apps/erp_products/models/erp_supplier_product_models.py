# -*- coding: utf-8 -*-
from datetime import datetime

from common.basics.models import BaseFieldsModel
from common.utils import CategoryFindAncestorsCache
from django.contrib.postgres.indexes import GinIndex
from django.core.paginator import Page
from django.db import models
from django.utils import timezone
from django_cte import CTEManager
from erp_apps.erp_products.models.erp_product_common_models import (
    ERPProductAttrValue,
    ERPProductCategory,
    ERPProductAccessory,
)
from erp_products.models import ERPProductUnit
from erp_products.utils import generate_tid
from utils.common import get_random
from utils.redis_lock import redis_cache


def _erp_supplier_product_id_generator():
    for i in range(120):
        random_product_id = get_random()

        if not ERPSupplierProduct.objects.filter(id=random_product_id).exists():
            return random_product_id
    else:
        raise ValueError("no random product id found")


def _erp_product_sku_id_generator():
    for i in range(120):
        generated_sku_id = get_random()

        if not ERPSupplierProductSKU.objects.filter(sku_id=generated_sku_id).exists():
            return generated_sku_id
    else:
        raise ValueError("erp sku_id cannot be generated")


class ERPSupplierProduct(BaseFieldsModel):
    # todo: 没实现update方法创建历史记录
    product_id = models.BigIntegerField(
        "商品id",
        unique=True,
        blank=False,
        null=False,
        editable=False,
        default=_erp_supplier_product_id_generator,
    )
    product_type = models.CharField(
        "商品类型",
        choices=[
            ("FN", "成品"),
            ("SF", "半成品"),
            ("RM", "原材料"),
            ("PM", "包材"),
        ],
        default="FN",
    )
    name = models.CharField("商品名称", max_length=60, blank=True, null=True, default="")
    category = models.ForeignKey(
        ERPProductCategory,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="所属分类",
        null=True,
        blank=True,
    )
    accessory = models.ForeignKey(
        ERPProductAccessory,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="半成品分类",
        null=True,
        blank=True,
    )
    code = models.CharField(
        "货号",
        max_length=20,
        blank=True,
        null=True,
        db_index=True,
        default="",
    )
    unit = models.ForeignKey(
        ERPProductUnit,
        on_delete=models.SET_NULL,
        db_constraint=False,
        blank=True,
        null=True,
        verbose_name="商品单位",
    )
    physical_inventory = models.BigIntegerField("商品总现货库存", default=0)
    attr_value = models.ManyToManyField(ERPProductAttrValue, verbose_name="关联属性值", blank=True)
    main_images = models.JSONField(
        verbose_name="主图列表",
        null=True,
        blank=True,
        default=list,
    )
    remark = models.TextField("备注信息", null=True, blank=True)
    supplier = models.ForeignKey(
        "erp_client.ERPCompanySupplierInfo",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="supplier_id",
        null=True,
        blank=True,
        verbose_name="系统用户供应商端的供应商",
    )
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属供应商",
    )
    # history = HistoricalRecords("商品快照", m2m_fields=[attr_value])

    class Meta:
        verbose_name = "供应商ERP商品信息"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.name}({self.product_id})"


class ERPSupplierProductSKU(BaseFieldsModel):
    product = models.ForeignKey(
        ERPSupplierProduct,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="product_id",
        verbose_name="关联商品",
    )
    product_type = models.CharField(
        "商品类型",
        choices=[
            ("FN", "成品"),
            ("SF", "半成品"),
            ("RM", "原材料"),
            ("PM", "包材"),
        ],
        default="FN",
    )
    status = models.PositiveSmallIntegerField(
        "商品状态",
        choices=(
            (1, "上架"),
            (2, "下架"),
        ),
        default=1,
    )
    name = models.CharField("商品名称", max_length=60, blank=True, null=True, default="")
    category = models.ForeignKey(
        ERPProductCategory,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="所属分类",
        null=True,
        blank=True,
    )
    unit = models.ForeignKey(
        ERPProductUnit,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name="商品单位",
    )
    sku_id = models.BigIntegerField(
        "SKU_ID",
        unique=True,
        blank=False,
        null=False,
        default=_erp_product_sku_id_generator,
        db_index=True,
        editable=False,
    )
    spec_code = models.CharField("商品编码", max_length=30, blank=True, null=True, db_index=True)
    # 现货库存不使用当前字段。用ErpSupplierSkuInventorySummaryView视图代替
    physical_inventory = models.BigIntegerField("现货库存", default=0)
    spec_value = models.CharField("颜色及规格", max_length=60, null=True, blank=True, default="")
    cost_price = models.DecimalField("成本价", max_digits=10, decimal_places=2, default=0)

    # 标签价 = 供货价
    label_price = models.DecimalField("供货价", max_digits=10, decimal_places=2, default=0)
    market_price = models.DecimalField("市场|吊牌价", max_digits=10, decimal_places=2, default=0)
    # 采购价 = 标签价
    purchase_price = models.DecimalField("采购价", max_digits=10, decimal_places=2, default=0)
    size = models.CharField("尺寸", max_length=25, blank=True, null=True)
    weight = models.FloatField("重量(g)", blank=True, null=True, default=0)
    image = models.CharField("规格主图", max_length=300, blank=True, null=True)
    min_inventory_capacity = models.IntegerField("库容下限", null=True, blank=True)
    max_inventory_capacity = models.IntegerField("库容上限", null=True, blank=True)
    remark = models.TextField("SKU备注信息", null=True, blank=True)
    attr_value = models.ManyToManyField(ERPProductAttrValue, verbose_name="关联属性值", blank=True)
    main_images = models.JSONField(
        verbose_name="主图列表",
        null=True,
        blank=True,
        default=list,
    )
    # 其他
    other_price1 = models.DecimalField("其他价格1", max_digits=10, decimal_places=2, blank=True, null=True)
    other_price2 = models.DecimalField("其他价格2", max_digits=10, decimal_places=2, blank=True, null=True)
    other_price3 = models.DecimalField("其他价格3", max_digits=10, decimal_places=2, blank=True, null=True)
    other_price4 = models.DecimalField("其他价格4", max_digits=10, decimal_places=2, blank=True, null=True)
    other_price5 = models.DecimalField("其他价格5", max_digits=10, decimal_places=2, blank=True, null=True)
    other_price6 = models.DecimalField("其他价格6", max_digits=10, decimal_places=2, blank=True, null=True)

    session_date = models.DateTimeField(verbose_name="场次日期", null=True, blank=True)
    other_attributes = models.CharField("其他属性", max_length=300, blank=True, null=True)
    other_attributes2 = models.CharField("其他属性2", max_length=300, blank=True, null=True)
    other_attributes3 = models.CharField("其他属性3", max_length=300, blank=True, null=True)
    other_attributes4 = models.CharField("其他属性4", max_length=300, blank=True, null=True)
    other_attributes5 = models.CharField("其他属性5", max_length=300, blank=True, null=True)

    label_price_multiple = models.IntegerField("标签价倍率", null=True, blank=True)
    label_price_multiple_type = models.IntegerField(
        "标签价倍率取整方式",
        choices=(
            (1, "向上取整"),
            (2, "向下取整"),
        ),
        null=True,
        blank=True,
    )

    supplier = models.ForeignKey(
        "erp_client.ERPCompanySupplierInfo",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="supplier_id",
        verbose_name="系统用户供应商端的供应商",
        null=True,
        blank=True,
    )
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属供应商端",
    )
    # 默认都是RFID标签，在入库的时候会更改
    tag_type = models.PositiveSmallIntegerField(
        "标签类型",
        choices=(
            (1, "RFID标签"),
            (2, "普通标签"),
        ),
        null=True,
        blank=True,
        default=None,
    )

    # history = HistoricalRecords(verbose_name="sku快照表", m2m_fields=[attr_value])

    class Meta:
        verbose_name = "供应商ERP商品SKU信息"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=("spec_code", "company"),
                condition=models.Q(is_deleted=False),
                name="unique_spec_code_and_company_in_sku",
            )
        ]

    def __str__(self):
        return f"{self.name}({self.spec_code})"

    @classmethod
    def generate_pre_spec_code(cls, company_id):
        """
        获取预加载的商编
        :param company_id: Company表的company_id
        :return:
        """
        now_t = datetime.now()
        date_str = timezone.make_aware(now_t).strftime("%y%m%d")

        auto_increase_key = f"auto_increase:sku_{company_id}_{date_str}"
        val = redis_cache.get(auto_increase_key)

        pre_use_code_count_key = f"pre_use_code_count:sku_{company_id}"
        pre_use_count = redis_cache.get(pre_use_code_count_key) or 0
        if val:
            return val

        last_sku = ERPSupplierProductSKU.objects.filter(company_id=company_id, create_date__date=now_t.date()).order_by("-create_date").first()
        if not last_sku:
            count = 1 + pre_use_count
        else:
            count = ERPSupplierProductSKU.objects.filter(company_id=company_id, create_date__date=now_t.date()).count() + 1 + pre_use_count

        for _ in range(120):
            pre_spec_code = "{}{:05d}".format(date_str, count)

            if not ERPSupplierProductSKU.objects.filter(spec_code=pre_spec_code, company_id=company_id).exists():
                redis_cache.set(auto_increase_key, pre_spec_code, 24 * 60 * 60)
                return pre_spec_code
            count += 1
        raise ValueError("generate preview spec_code failed, please try again later")

    @classmethod
    def batch_generate_spec_codes(cls, company_id: int, start_spec_code: str, count: int):
        """
        批量生成商品编码，自动跳过已存在的编码并继续递增
        :param company_id: 公司ID
        :param start_spec_code: 起始编码
        :param count: 需要生成的编码数量
        :return: 生成的编码列表
        """
        # 保留起始编码
        result_codes = [start_spec_code]

        # 根据不同格式生成后续编码
        if start_spec_code.isdigit():
            # 纯数字编码
            batch_codes = [str(int(start_spec_code) + i) for i in range(1, count)]
        elif any(c.isdigit() for c in start_spec_code):
            # 字母+数字编码
            prefix = ""
            num_str = ""
            for c in start_spec_code:
                if c.isdigit():
                    num_str += c
                else:
                    prefix += c
            start_num = int(num_str) if num_str else 0
            batch_codes = [f"{prefix}{start_num + i}" for i in range(1, count)]
        else:
            # 纯字母编码
            batch_codes = [f"{start_spec_code}{i + 1}" for i in range(1, count)]

        # 检查哪些编码已存在
        all_codes = [start_spec_code] + batch_codes
        existing_codes = set(ERPSupplierProductSKU.objects.filter(spec_code__in=all_codes, company_id=company_id).values_list("spec_code", flat=True))

        # 计算需要额外生成的编码数量
        exist_count = len(existing_codes)

        # 如果有已存在的编码，继续生成新的编码
        if exist_count > 0:
            # 生成额外的编码
            if start_spec_code.isdigit():
                extra_codes = [str(int(start_spec_code) + count - 1 + i) for i in range(1, exist_count + 1)]
            elif any(c.isdigit() for c in start_spec_code):
                prefix = ""
                num_str = ""
                for c in start_spec_code:
                    if c.isdigit():
                        num_str += c
                    else:
                        prefix += c
                start_num = int(num_str) if num_str else 0
                extra_codes = [f"{prefix}{start_num + count - 1 + i}" for i in range(1, exist_count + 1)]
            else:
                extra_codes = [f"{start_spec_code}{count - 1 + i}" for i in range(1, exist_count + 1)]

            # 将额外的编码添加到批次编码中
            batch_codes.extend(extra_codes)

        # 构建最终结果，始终包含起始编码
        result_codes = [start_spec_code]
        for code in batch_codes:
            if code not in existing_codes and len(result_codes) < count:
                result_codes.append(code)

        # 设置预留个数
        pre_use_code_count_key = f"pre_use_code_count:sku_{company_id}"
        redis_cache.set(pre_use_code_count_key, len(result_codes), 5 * 60)
        return result_codes

    @classmethod
    def delete_pre_use_code_count_cache(cls, company_id):
        pre_use_code_count_key = f"pre_use_code_count:sku_{company_id}"
        redis_cache.delete(pre_use_code_count_key)

    @classmethod
    def delete_spec_code_cache(cls, company_id):
        now_t = datetime.now()
        date_str = timezone.make_aware(now_t).strftime("%y%m%d")
        auto_increase_key = f"auto_increase:sku_{company_id}_{date_str}"
        redis_cache.delete(auto_increase_key)

    def get_attr_display(self):
        atts = self.attr_value.all()
        return [
            {
                "key_id": attr.attr_key_id,
                "key_name": attr.attr_key.name,
                "value_id": attr.id,
                "value_name": attr.value,
            }
            for attr in atts
        ]

    @classmethod
    def get_batch_category_display_cache(cls, instance) -> CategoryFindAncestorsCache:
        """
        批量获取商品分类
        :param instance:
        :return:
        """
        if isinstance(instance, Page):
            category_id_list = list({d.category_id for d in instance if d.category_id})
        elif isinstance(instance, ERPSupplierProductSKU):
            if instance.category_id:
                category_id_list = [instance.category_id]
            else:
                category_id_list = []
        else:
            category_id_list = list({d.category_id for d in instance if d.category_id})

        category_qs = ERPProductCategory.get_ancestors_with_category_id_list(category_id_list).values("id", "name", "parent_id")
        category_cache = CategoryFindAncestorsCache(category_qs)
        return category_cache

    @classmethod
    def get_batch_sku_total_in_warehouse_quantity(cls, instance):
        """
        批量获取sku的总在仓数量
        :param instance:
        :return: {sku_pk: 123123} sku主键和对应的总在仓数量字典
        """
        from erp_purchase.models import ErpSupplierSkuInventorySummaryView

        if isinstance(instance, Page):
            skus = instance.object_list
        elif isinstance(instance, ERPSupplierProductSKU):
            skus = [instance]
        else:
            skus = instance

        return dict(
            ErpSupplierSkuInventorySummaryView.objects.filter(
                sku_pk__in=[sku.pk for sku in skus],
            ).values_list("sku_pk", "total_in_warehouse_quantity")
        )

    @classmethod
    def get_batch_sku_total_in_sku_warehouse_quantity(cls, instance, warehouse_id):
        """
        批量获取指定仓库下sku总在仓数量
        :param instance:
        :return: {sku_pk: 123123} sku主键和对应的总在仓数量字典
        """
        from erp_purchase.models import ErpSupplierSkuWarehouseInventorySummaryView

        if isinstance(instance, Page):
            skus = instance.object_list
        elif isinstance(instance, ERPSupplierProductSKU):
            skus = [instance]
        else:
            skus = instance

        return ErpSupplierSkuWarehouseInventorySummaryView.objects.filter(sku_pk__in=[sku.pk for sku in skus], warehouse_id=warehouse_id).values(
            "sku_pk", "warehouse_id", "total_in_warehouse_quantity"
        )

    def get_category_display(self):
        if not self.category_id:
            return []

        return [{"id": c.id, "name": c.name} for c in self.category.get_all_parents()]

    def get_price_by_price_type(self, price_type):
        """
        根据价格类型获取价格
        :param price_type:
        :return:
        """
        if price_type == "label_price":
            price = self.label_price
        elif price_type == "cost_price":
            price = self.cost_price
        elif price_type == "market_price":
            price = self.market_price
        elif price_type == "purchase_price":
            price = self.purchase_price
        else:
            price = self.label_price
        return price


class ERPSupplierSKU(BaseFieldsModel):
    product = models.ForeignKey(
        ERPSupplierProduct,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="product_id",
        verbose_name="关联商品",
    )
    supplier = models.ForeignKey(
        "erp_client.ERPCompanySupplierInfo",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="supplier_id",
        verbose_name="系统用户供应商端的供应商",
    )
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属供应商端",
    )
    name = models.CharField("商品名称", max_length=60, blank=True, null=True, default="")
    sku = models.ForeignKey(
        ERPSupplierProductSKU,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="sku_id",
        verbose_name="关联sku信息",
    )
    cost_price = models.DecimalField("成本价", max_digits=10, decimal_places=2)
    label_price = models.DecimalField("供货价", max_digits=10, decimal_places=2, null=True, blank=True, default=0)
    market_price = models.DecimalField("市场|吊牌价", max_digits=10, decimal_places=2, null=True, blank=True)
    purchase_price = models.DecimalField("采购价", max_digits=10, decimal_places=2, null=True, blank=True)

    weight_avg_price = models.DecimalField("加权平均价", max_digits=10, decimal_places=2, null=True, blank=True)
    # # {
    # #   "last_one": {"weight_avg_price": 10, **inventory},
    # #   "last_two": {"weight_avg_price": 10, **inventory}
    # # }
    # inventory_json = models.JSONField("库存信息,含最后2次的库存数据快照", null=True, blank=True, default=dict)

    objects = CTEManager()

    class Meta:
        verbose_name = "供应商SKU信息"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=("company", "sku", "supplier"),
                condition=models.Q(is_deleted=False),
                name="unique_company_sku_supplier_in_supplier_sku",
            )
        ]

    def __str__(self):
        return f"{self.name}_{self.pk}"

    @classmethod
    def bulk_create_or_update(self, data_list: list):
        """
        批量写入或更新 ERPSupplierSKU 数据
        新增入库 > 选中某个供应商 > 添加若干个sku商品（仅限当前商户的sku）> 创建商品 > 将供应商与sku分别关联（已关联的忽略）
        :param data_list: 包含字典的列表，每个字典表示一条记录
        :return: 成功写入或更新的记录数量

        """
        created_datas = ERPSupplierSKU.objects.bulk_create(
            [ERPSupplierSKU(**obj) for obj in data_list if not isinstance(obj, ERPSupplierSKU)],
            ignore_conflicts=True,
            unique_fields=["company", "supplier", "sku_id"],
        )

        return created_datas

    @classmethod
    def get_batch_sku_weight_avg_price(cls, company_id, sku_ids: list):
        """
        获取一批sku的加权平均加
        """
        weight_avg_price_mp = {
            obj.sku_id: obj.weight_avg_price
            for obj in ERPSupplierSKU.objects
            .filter(
                company_id=company_id,
                sku_id__in=sku_ids
            )
        }
        return weight_avg_price_mp

    def get_batch_sku_weight_avg_price2(cls, company_id, sku_pks: list):
        """
        获取一批sku的加权平均加
        """
        weight_avg_price_mp = {
            obj.sku.id: obj.weight_avg_price
            for obj in ERPSupplierSKU.objects
            .filter(
                company_id=company_id,
                sku__id__in=sku_pks
            )
            .prefetch_related("sku")
        }
        return weight_avg_price_mp



class ERPProductSKUTID(models.Model):
    sku = models.OneToOneField(
        ERPSupplierProductSKU,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联sku信息",
    )
    # {
    #     "16进制tid": {
    #         "status": "normal", # normal/abnormal tid状态
    #         "inbound_detail_code": "", # 入库单明细
    #     }
    # }
    tid_info = models.JSONField("SKU标签信息", default=dict)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "ERP_SKU标签TID"
        verbose_name_plural = verbose_name
        indexes = [
            GinIndex(
                fields=["tid_info"],
                name="sku_tid_json_info_index",
            ),
        ]

    @classmethod
    def generate_tid_info(cls, inbound_code: str = ""):
        """构建sku_info信息"""
        return {
            generate_tid(): {
                "status": "normal",
                "inbound_code": inbound_code,
                "create_date": str(timezone.now()),
            }
        }

    @classmethod
    def batch_generate_tid_info(cls, times, inbound_code: str = "") -> dict[str, dict]:
        """
        批量创建sku_info
        :param times:
        :param inbound_code:
        :return:
        """
        re_data = {}
        for _ in range(times):
            re_data[generate_tid()] = {
                "status": "normal",
                "inbound_code": inbound_code,
                "create_date": str(timezone.now()),
            }

        return re_data
