from django.contrib.postgres.indexes import GinIndex
from django.db import models

from erp_apps.erp_products.sources import ERP_SKU_NO_NEED_TO_CREATE_HISTORY_FIELDS
from erp_products.managers import ERPSKUHistoricalWithExtraFields, ERPSKUHistoricalManager, ERPSKUSaveSkipHistoryManger
from products.model_managers import (
    CustomHistoricalRecords,
)
from products.models import SpecsKey, SpecsValue
from utils.common import get_random, get_random_number_str


def _erp_sku_id_generator():
    for i in range(120):
        generated_sku_id = get_random()

        if not ERPStockKeepingUnit.objects.filter(sku_id=generated_sku_id).exists():
            return generated_sku_id
    else:
        raise ValueError("erp sku_id cannot be generated")


class ERPStockKeepingUnit(models.Model):
    # 需要保存数据的字段，在signals文件进行存储
    NeedSaveHistoryFields = [
        "physical_inventory",
        "safety_inventory",
        "cost_price",
        "retail_price",
        "label_price",
    ]

    _log_private_field = ["specs"]

    sku_id = models.BigIntegerField(
        "SKU_ID",
        unique=True,
        blank=False,
        null=False,
        default=_erp_sku_id_generator,
        db_index=True,
    )
    product = models.ForeignKey(
        "erp_products.ERPProduct",
        on_delete=models.CASCADE,
        db_constraint=False,
        blank=False,
        null=False,
        to_field="product_id",
        verbose_name="商品",
    )
    physical_inventory = models.BigIntegerField("现货库存", default=0)
    safety_inventory = models.BigIntegerField("7天补货库存", default=0, blank=True, null=True)
    warehouse_inventory = models.BigIntegerField("仓库库存", default=0, blank=True, null=True)
    can_use_inventory = models.BigIntegerField(verbose_name="可用库存", default=0, blank=True, null=True)
    spec_code = models.CharField("商品编码", max_length=30, blank=True, null=True, db_index=True)
    # 价格信息
    cost_price = models.DecimalField("成本价", max_digits=10, decimal_places=2)
    retail_price = models.DecimalField("售价", max_digits=10, decimal_places=2, blank=True, null=True)
    label_price = models.DecimalField("标签价", max_digits=10, decimal_places=2, blank=True, null=True)
    history_price = models.DecimalField("历史价", max_digits=10, decimal_places=2, blank=True, null=True)
    #
    weight = models.FloatField("重量", blank=True, null=True)
    sales = models.BigIntegerField("SKU销量", default=0)
    image = models.CharField("规格主图", max_length=300, blank=True, null=True)
    remark = models.TextField("SKU备注", blank=True, null=True)

    # 规格信息, specs是前端传来默认的列表数据
    specs = models.JSONField(verbose_name="规格列表", null=True, blank=True, default=list)
    specs_name = models.ManyToManyField(SpecsKey, verbose_name="规格名")
    specs_value = models.ManyToManyField(SpecsValue, verbose_name="规格值")
    order = models.IntegerField(verbose_name="规格排序", default=0, blank=True, null=True)
    # 基础信息
    become_history = models.BooleanField("已经变成历史数据", blank=True, null=True, default=False)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True, blank=True, null=True)
    update_date = models.DateTimeField("更新时间", auto_now=True, blank=True, null=True)

    # 版本记录
    history = CustomHistoricalRecords(bases=[ERPSKUHistoricalWithExtraFields])
    objects = ERPSKUHistoricalManager()  # 使用自定义的 Manager
    skip_history_price_objects = ERPSKUSaveSkipHistoryManger()  # 不保留historyManger

    class Meta:
        verbose_name = "ERP商品sku"
        verbose_name_plural = verbose_name
        ordering = ("order", "id")
        indexes = [
            GinIndex(
                fields=["specs"],
                condition=models.Q(become_history=False),
                name="erp_specs_gin_index",
            ),
        ]

        constraints = [
            models.UniqueConstraint(
                fields=["spec_code"],
                condition=models.Q(become_history=False),
                name="unique_erp_spec_code_when_become_history_is_false",
            ),
        ]

    def __str__(self):
        if self.specs:
            return "-".join([i["value"] for i in self.specs]) + f"({self.sku_id})"
        return str(self.sku_id)

    @classmethod
    def generate_spec_code(cls, company_code):
        if not company_code:
            raise ValueError("company_code cannot be None")

        for i in range(120):
            spec_code = f"J{company_code}" + get_random_number_str(6)

            if not cls.objects.filter(spec_code=spec_code).exists():
                return spec_code
        else:
            raise ValueError("erp spec code cannot be generated")

    def save(self, *args, **kwargs):
        if not self.spec_code:
            raise ValueError("missing erp sku spec code")

        # 是否跳过历史
        skip_history_when_saving = False
        if len(set(kwargs.get("update_fields", [])) - ERP_SKU_NO_NEED_TO_CREATE_HISTORY_FIELDS) == 0:
            skip_history_when_saving = True

        setattr(self, "skip_history_when_saving", skip_history_when_saving)
        return super().save(*args, **kwargs)

    def set_mod_history_attr_condition(self):
        setattr(self, "__save_mod_history__", True)
