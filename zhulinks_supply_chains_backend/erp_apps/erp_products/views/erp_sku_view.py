import copy

from django.db.models import Prefetch
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter

from common.basics.views import set_log_params, OperateLogAPIViewMixin, OperateLogAPIViewSetMixin
from common.basics.exceptions import DataNotFoundException
from common.basics.permissions import OPPermissions
from common.utils import diff_models
from erp_apps import logger
from erp_apps.erp_products.filters import ERPSkuFilter
from erp_apps.erp_products.models import ERPStockKeepingUnit, ERPProductLabels, ERPProduct
from erp_apps.erp_products.serializers import ERPSkuSerializer
from erp_products.serializers import ERPSKUPatchUpdateSer
from erp_products.signals import update_product_min_max_data_signal
from utils.api_page_number_pagination import ApiPageNumberPagination
from utils.base_model_view_set import BaseMysqlModelViewSet
from utils.http_handle import IResponse, FieldsError


class ERPSKUViewSet(BaseMysqlModelViewSet, OperateLogAPIViewSetMixin):
    resource_name = "ERP商品"
    fronted_page = "ERP商品列表"

    queryset = (
        ERPStockKeepingUnit.objects.prefetch_related(
            "product",
            "product__unit",
            "product__address",
            "product__company",
            "product__distributor",
            Prefetch(
                "product__labels",
                ERPProductLabels.objects.filter(is_deleted=False),
            ),
        )
        .all()
        .order_by("-create_date")
    )
    pagination_class = ApiPageNumberPagination
    filterset_class = ERPSkuFilter
    serializer_class = ERPSkuSerializer
    permission_classes = [OPPermissions]

    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering = ["cost_price", "retail_price", "label_price"]
    lookup_field = "sku_id"

    def update(self, request, code=200, count=1, status=None, *args, **kwargs):
        try:
            erp_sku = self.get_queryset().filter(become_history=False).get(**{self.lookup_field: kwargs[self.lookup_field]})
        except ERPStockKeepingUnit.DoesNotExist:
            raise DataNotFoundException

        raw_sku = copy.deepcopy(erp_sku)

        post_data = request.data
        post_data["update_user"] = request.user.user_id
        update_ser = ERPSKUPatchUpdateSer(instance=erp_sku, data=post_data, partial=True)
        if not update_ser.is_valid():
            raise FieldsError(update_ser.errors)
        updated_sku = update_ser.save()

        # 更新最大最小值
        update_product_min_max_data_signal.send(sender=ERPProduct, product_id=erp_sku.product_id)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(erp_sku.product_id),
                model=ERPProduct,
                describe=f"更新了商品：{erp_sku.product}, 规格：{erp_sku}",
                operate_content=diff_models(raw_sku, updated_sku),
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()
