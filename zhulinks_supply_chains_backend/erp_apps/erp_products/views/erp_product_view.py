from django.contrib.contenttypes.models import ContentType
from rest_framework.request import Request

from common.basic import OPAPIView
from common.basics.views import OperateLogAPIViewMixin
from common.basics.exceptions import DataNotFoundException
from common.models import OperationLog
from erp_apps.erp_products.models import ERPProduct
from erp_apps.erp_products.serializers.erp_product_serializer import ERPProductDetailSer
from erp_products.filters.common_filter import ERPProductOperationLogFilter
from erp_products.serializers.common_serializer import ERPProductOperationLogSer
from erp_products.services.erp_product_service import ERPProductService
from utils.http_handle import IResponse, custom_django_filter


class ERPProductViews(OPAPIView):
    resource_name = "ERP商品"
    fronted_page = "ERP商品列表"

    def post(self, request: Request, *args, **kwargs):
        ERPProductService.create_erp_product(
            request.data,
            self.current_user,
            self.current_user_type,
            request,
        )
        return IResponse()


class ERPProductDetailViews(OPAPIView):
    resource_name = "ERP商品"
    fronted_page = "ERP商品列表"

    @staticmethod
    def get_object(product_id) -> ERPProduct:
        try:
            return ERPProduct.objects.get(product_id=product_id)
        except ERPProduct.DoesNotExist:
            raise DataNotFoundException

    def get(self, request: Request, product_id: int):
        product = self.get_object(product_id)
        ser = ERPProductDetailSer(instance=product, many=False, context={"request": request})
        return IResponse(data=ser.data)

    def put(self, request: Request, product_id: int):
        product = self.get_object(product_id)
        ERPProductService.update_erp_product(
            product,
            request.data,
            self.current_user,
            self.current_user_type,
            request,
        )
        return IResponse()


class ERPProductLogView(OPAPIView):
    def get(self, request: Request, product_id: int):
        content_type = ContentType.objects.get_for_model(ERPProduct)
        operation_logs = OperationLog.objects.filter(
            content_type=content_type,
            resource_id=product_id,
        ).order_by("-operation_time")
        re_data, _, _ = custom_django_filter(
            request,
            operation_logs,
            ERPProductOperationLogFilter,
            ERPProductOperationLogSer,
            force_order=False,
        )
        return IResponse(data=re_data)
