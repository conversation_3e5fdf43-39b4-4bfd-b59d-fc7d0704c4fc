# -*- coding: utf-8 -*-
import copy

from django.db.models import Prefetch
from rest_framework.request import Request

from common.basic import SPAPIView
from common.basics.exceptions import DataNotFoundException
from common.utils import list_to_tree, diff_models
from erp_products.filters.common_filter import ERPProductCategoryFilter
from erp_products.models import ERPProductCategory, ERPProductAttrKey, ERPProductAttrValue, ERPProductUnit, ERPProductAccessory
from erp_products.serializers.common_serializer import ERPProductCategoryCreateOrUpdateSer, ERPProductCategoryListSer
from utils.http_handle import IResponse, FieldsError


class ERPProductCategoryEnumView(SPAPIView):
    """
    供应商分类枚举
    """

    fronted_page = "分类管理"
    resource_name = "分类"

    def get(self, request: Request, *args, **kwargs):
        categories = ERPProductCategory.objects.filter(
            is_deleted=False,
            company=self.current_user.company,
        ).values("id", "name", "parent_id")
        re_data = list_to_tree(list(categories))
        return IResponse(data=re_data)

    def post(self, request: Request, *args, **kwargs):
        create_ser = ERPProductCategoryCreateOrUpdateSer(data=request.data, context={"request": request})
        if not create_ser.is_valid():
            raise FieldsError(create_ser.errors)
        category = create_ser.save()

        self.log_operation(
            request,
            ERPProductCategory,
            f"新建分类：{category}",
            resource_id=category.id,
            is_success_input=True,
        )
        return IResponse()


class ERPProductCategoryView(SPAPIView):
    """
    供应商分类枚举
    """

    fronted_page = "分类管理"
    resource_name = "分类"

    def get(self, request: Request, *args, **kwargs):
        categories = ERPProductCategory.objects.filter(
            is_deleted=False,
            company=self.current_user.company,
        )
        filtered_categories = ERPProductCategoryFilter(data=request.query_params, queryset=categories).qs
        re_data = ERPProductCategoryListSer(instance=filtered_categories, many=True).data
        return IResponse(data=re_data)

    def post(self, request: Request, *args, **kwargs):
        create_ser = ERPProductCategoryCreateOrUpdateSer(data=request.data, context={"request": request})
        if not create_ser.is_valid():
            raise FieldsError(create_ser.errors)
        category = create_ser.save()

        self.log_operation(
            request,
            ERPProductCategory,
            f"新建分类：{category}",
            resource_id=category.id,
            is_success_input=True,
        )
        return IResponse()


class ERPProductCategoryDetailView(SPAPIView):
    fronted_page = "分类管理"
    resource_name = "分类"

    def _get_object(self, category_id: int) -> ERPProductCategory:
        try:
            return ERPProductCategory.objects.get(
                pk=category_id,
                is_deleted=False,
                company=self.current_user.company,
            )
        except ERPProductCategory.DoesNotExist:
            raise DataNotFoundException

    def put(self, request: Request, category_id: int, *args, **kwargs):
        category = self._get_object(category_id)
        raw_category = copy.deepcopy(category)
        update_ser = ERPProductCategoryCreateOrUpdateSer(
            instance=category,
            data=request.data,
            context={"request": request},
        )
        if not update_ser.is_valid():
            raise FieldsError(update_ser.errors)
        category = update_ser.save()

        self.log_operation(
            request,
            ERPProductCategory,
            f"更新分类：{category}",
            operate_content=diff_models(raw_category, category),
            resource_id=str(category_id),
            is_success_input=True,
        )
        return IResponse()

    def delete(self, request: Request, category_id: int, *args, **kwargs):
        category = self._get_object(category_id)

        category.update_user = self.current_user.user_id
        category.is_deleted = True
        category.save()

        self.log_operation(
            request,
            ERPProductCategory,
            f"删除分类：{category}",
            resource_id=str(category_id),
            is_success_input=True,
        )
        return IResponse()


class ERPProductAttrEnumView(SPAPIView):
    def get(self, request, *args, **kwargs):

        attr_value_prefetch = Prefetch(
            "values",
            ERPProductAttrValue.objects.filter(
                is_deleted=False,
                company=self.current_user.company,
            ),
        )

        attr_keys = ERPProductAttrKey.objects.prefetch_related(attr_value_prefetch).filter(
            is_deleted=False,
            company=self.current_user.company,
        )
        re_data = []
        for attr_key in attr_keys:
            attr_tmp = {
                "id": attr_key.id,
                "name": attr_key.name,
                "subs": [],
            }

            for v in attr_key.values.all():
                attr_tmp["subs"].append(
                    {
                        "id": v.id,
                        "name": v.value,
                    }
                )
            re_data.append(attr_tmp)

        return IResponse(data=re_data)


class ERPProductUnitEnumView(SPAPIView):
    def get(self, request, *args, **kwargs):
        units_data = ERPProductUnit.objects.filter(
            is_deleted=False,
            company=self.current_user.company,
        ).values("id", "name")

        return IResponse(data=units_data)


class ERPProductAccessoryEnumView(SPAPIView):
    def get(self, request, *args, **kwargs):
        data = ERPProductAccessory.objects.filter(
            is_deleted=False,
            company=self.current_user.company,
        ).values("id", "name")

        return IResponse(data=data)