# -*- coding: utf-8 -*-
from django.utils import timezone
from rest_framework.request import Request

from common.basic import OPAPIView
from common.basics.exceptions import APIViewException
from erp_products.models import ERPProductLabels
from erp_products.serializers import ERPProductLabelsCreateSer, ERPProductLabelsListSer
from utils.http_handle import FieldsError, IResponse


class ERPProductLabelsView(OPAPIView):
    def get(self, request: Request):
        labels = ERPProductLabels.objects.filter(is_deleted=False)
        ser_data = ERPProductLabelsListSer(instance=labels, many=True).data

        re_data_dict = {
            1: {"id": 1, "type": "系统标签", "labels": []},
            2: {"id": 2, "type": "自定义标签", "labels": []},
        }
        for data in ser_data:
            if data["type"] in re_data_dict:
                re_data_dict[data["type"]]["labels"].append(data)

        re_data = re_data_dict.values()
        return IResponse(data=re_data)

    def post(self, request: Request):
        post_data = request.data
        post_data["type"] = 2
        post_data["create_user"] = self.current_user
        ser = ERPProductLabelsCreateSer(data=post_data)
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        ser.save()
        return IResponse()

    def delete(self, request: Request):
        post_data = request.data

        delete_id_list = post_data.get("delete_ids")
        if not delete_id_list:
            raise APIViewException

        labels = ERPProductLabels.objects.filter(type=2, is_deleted=False, pk__in=delete_id_list)
        if labels.count() != len(delete_id_list):
            raise APIViewException(err_message="invalid labels, pls refresh the page and try again")

        labels.update(is_deleted=True, update_user=self.current_user, update_date=timezone.now())
        return IResponse()
