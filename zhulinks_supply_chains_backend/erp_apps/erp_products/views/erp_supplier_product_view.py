# -*- coding: utf-8 -*-
import copy

from common.basic import SPAPIView
from common.basics.exceptions import APIViewException
from common.basics.paginator import custom_paginator
from common.basics.viewsets import SPModelViewSet
from common.models import DownloadTasksTypeChoices
from common.tasks import w_log
from common.utils import diff_models
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils import timezone
from erp_orders.models import ERPSalesOrderDetails
from erp_products.filters.erp_supplier_product_filters import ERPSupplierProductSKUFilter
from erp_products.models import ERPProductCategory, ERPSupplierProductSKU
from erp_products.serializers_v2.erp_supplier_sku_serializers import (
    ERPSupplierProductSKUCreateSerializer,
    ERPSupplierProductSKUListSer,
    ERPSupplierProductSKUUpdateSerializer,
)
from erp_products.tasks.async_download_tasks import async_download_erp_supplier_product_sku_task
from erp_purchase.models import ERPSKUInventory
from rest_framework.decorators import action
from rest_framework.request import Request
from utils.erp_download_utils.erp_async_download_task import ERPAsyncDownloadTaskReceiver
from utils.http_handle import FieldsError, IResponse, custom_django_filter


class ERPSupplierProductSKUSpecCodeGeneratorView(SPAPIView):
    """
    获取预加载的商编
    """

    def get(self, request: Request):
        pre_spec_code = ERPSupplierProductSKU.generate_pre_spec_code(self.current_user.company.company_id)

        data = {
            "spec_code": pre_spec_code,
        }
        return IResponse(data=data)


class ERPSupplierCheckSpecCodeExistView(SPAPIView):
    """
    判断是否存在商品编码
    """

    def get(self, request: Request):
        spec_code = request.query_params.get("spec_code")
        if not spec_code:
            raise APIViewException

        try:
            erp_sku = ERPSupplierProductSKU.objects.get(
                spec_code=spec_code,
                company=self.current_user.company,
                is_deleted=False,
            )
        except ERPSupplierProductSKU.DoesNotExist:
            return IResponse()

        data = {
            "name": erp_sku.name,
            "code": erp_sku.product.code,
            "spec_value": erp_sku.spec_value,
            "category": ERPProductCategory.get_ancestors_with_category_id(erp_sku.category_id),
            "unit_id": erp_sku.unit_id,
            "attr_value_ids": erp_sku.attr_value.values_list("id", flat=True),
            "weight": erp_sku.weight,
            "size": erp_sku.size,
            "product_type": erp_sku.product_type,
        }
        return IResponse(data=data)


class ERPSupplierSKUManagerViewSet(SPModelViewSet):
    """
    供应商商品和SKU管理

    支持以下操作：
    - 创建供应商商品和SKU
    - 更新供应商商品和SKU（货号和商编不可修改）
    - 查询供应商商品和SKU列表
    - 查询单个供应商商品和SKU详情
    """

    model = ERPSupplierProductSKU
    serializer_class = ERPSupplierProductSKUListSer
    ordering_fields = ["-update_date"]
    lookup_field = "sku_id"
    filterset_class = ERPSupplierProductSKUFilter
    fronted_page = "商品资料"
    resource_name = "商品资料"
    need_format_resource_name = False

    def get_queryset(self):
        return self.model.objects.prefetch_related(
            "supplier",
            "product",
            "unit",
            "attr_value",
            "attr_value__attr_key",
        ).filter(company=self.current_user.company, is_deleted=False)

    def get_serializer_class(self):
        if self.action == "create":
            return ERPSupplierProductSKUCreateSerializer
        elif self.action == "update" or self.action == "partial_update":
            return ERPSupplierProductSKUUpdateSerializer
        return self.serializer_class

    def list(self, request, *args, **kwargs) -> IResponse:
        queryset = self.get_queryset()

        re_data, page_objects, qs = custom_django_filter(
            request=request,
            target=queryset,
            filter_set=self.get_filterset_class(),
            iserializer=self.get_serializer_class(),
        )

        # 返回invenstory_id
        sku_pks = [sku["skuid"] for sku in re_data["data"]]
        inventory_map = {
            inventory["sku_id"]: inventory["inventory_id"]
            for inventory in ERPSKUInventory.objects.filter(sku_id__in=sku_pks).values("sku_id").annotate(inventory_id=models.Max("id"))
        }
        for obj in re_data["data"]:
            obj["inventory_id"] = inventory_map.get(obj["skuid"], None)

        return IResponse(data=re_data)

    def create(self, request: Request, *args, **kwargs):
        serializer = self.get_serializer_class()(data=request.data, context={"request": request})
        if not serializer.is_valid():
            raise FieldsError(serializer.errors)

        sku = serializer.save()
        self.log_operation(
            request,
            ERPSupplierProductSKU,
            resource_id=sku.sku_id,
            describe=f"创建商品SKU：{sku}",
            is_success_input=True,
        )
        re_data = {
            "product_id": sku.product_id,
            "product_name": sku.name,
            "sku_id": sku.sku_id,
            "spec_code": sku.spec_code,
            "is_new_product": getattr(sku, "__is_new_product__", False),
        }
        return IResponse(data=re_data)

    def update(self, request: Request, *args, **kwargs):
        instance = self.get_object()
        old_instance = copy.deepcopy(instance)

        serializer = self.get_serializer_class()(
            instance=instance,
            data=request.data,
            context={"request": request},
            partial=kwargs.pop("partial", False),
        )
        if not serializer.is_valid():
            raise FieldsError(serializer.errors)

        new_instance = serializer.save()

        operate_content = ""
        # 判断货号
        old_code = getattr(new_instance, "__old_code__", None)
        new_code = getattr(new_instance, "__new_code__", None)
        if old_code != new_code:
            operate_content += f"货号：{old_code}修改为{new_code}"

        diff_content = diff_models(old_instance, new_instance)
        if diff_content:
            if operate_content:
                operate_content += f"，{diff_content}"
            else:
                operate_content = diff_content

        self.log_operation(
            request,
            ERPSupplierProductSKU,
            resource_id=new_instance.sku_id,
            describe=f"更新商品SKU：{new_instance}",
            operate_content="更新商品SKU: 无变动" if operate_content == "" else f"更新商品SKU: {operate_content}",
            is_success_input=True,
        )
        return IResponse()

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        old_instance = copy.deepcopy(instance)
        if ERPSKUInventory.objects.filter(
            sku=instance,
            company=self.current_user.company,
            is_deleted=False,
        ).exists():
            return IResponse(code=400, message="商品已入库，无法删除")

        instance.is_deleted = True
        instance.update_user = self.current_user.user_id
        instance.save()

        self.log_operation(
            request,
            resource_id=instance.sku_id,
            model=ERPSupplierProductSKU,
            describe=f"删除商品SKU：{old_instance}",
        )
        return IResponse()

    @action(detail=False, methods=["delete"])
    def bulk_delete(self, request: Request):
        sku_id_list = request.data.get("sku_ids")
        if not sku_id_list:
            raise APIViewException(err_message="sku_id_list is required")

        if not isinstance(sku_id_list, list):
            raise APIViewException(err_message="sku_id_list is not a list")

        if len(sku_id_list) > 100:
            raise APIViewException(err_message="sku_id_list is too long, max length is 100")

        # 获取要删除的SKU列表
        skus_to_delete = ERPSupplierProductSKU.objects.filter(
            sku_id__in=sku_id_list,
            company=self.current_user.company,
            is_deleted=False,
        )

        if not skus_to_delete.exists():
            return IResponse(message="没有找到要删除的商品")

        # 检查是否有入库明细
        skus_with_inventory = (
            ERPSKUInventory.objects.filter(
                sku__in=skus_to_delete,
                company=self.current_user.company,
                is_deleted=False,
            )
            .values_list("sku__sku_id", flat=True)
            .distinct()
        )

        # 如果有入库明细，则不允许删除
        if skus_with_inventory.exists():
            # 获取不能删除的SKU信息
            cannot_delete_skus = skus_to_delete.filter(sku_id__in=skus_with_inventory)
            cannot_delete_info = [f"{sku.name}({sku.spec_code})" for sku in cannot_delete_skus]

            return IResponse(code=400, message=f"以下商品已入库，无法删除: {', '.join(cannot_delete_info)}")

        # 可以删除的SKU
        deletable_skus = skus_to_delete.values_list("sku_id", flat=True)

        # 执行删除操作（标记为已删除）
        delete_count = ERPSupplierProductSKU.objects.filter(
            sku_id__in=deletable_skus,
            company=self.current_user.company,
        ).update(
            is_deleted=True,
            update_user=self.current_user.user_id,
            update_date=timezone.now(),
        )

        # 记录成功删除的日志
        for deleted_sku in skus_to_delete:
            # 动态调用w_log创建出入库单日志
            log_data = {
                "resource_id": deleted_sku.sku_id,
                "resource_name": "商品资料",
                "content": request.data,
                "describe": "批量删除商品资料",
                "platform": "SP",
                "content_type_id": ContentType.objects.get_for_model(ERPSupplierProductSKU).pk,
                "fronted_page": self.fronted_page,
                "operate_content": f"成功删除商品SKU: {deleted_sku.name}({deleted_sku.spec_code})",
                "mobile": self.current_user.mobile,
            }
            w_log(request, **log_data)

        return IResponse(data={"deleted_count": delete_count})

    @action(detail=False, methods=["post"])
    def download(self, request, *args, **kwargs):
        task = ERPAsyncDownloadTaskReceiver(
            request,
            "商品SKU",
            DownloadTasksTypeChoices.ERP_PRODUCT_SKU,
            self.current_user.user_id,
            self.current_user_type,
            self.current_user.company,
            async_download_erp_supplier_product_sku_task,
        ).process_task()
        return IResponse(code=201, message="后台正在处理下载任务", data={"task_id": task.id})

    @action(detail=True, methods=["get"])
    def history_sales_price(self, request, *args, **kwargs):
        """
        sku售价历史记录
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        sku = self.get_no_filter_object()
        details = ERPSalesOrderDetails.objects.get_actual_details(sku.pk, sales_order__status=1).order_by("-main_sales_order_create_date")
        re_data, page_objects = custom_paginator(details, request)
        re_data["data"] = [
            {
                "create_date": i.main_sales_order_create_date,
                "discount_price": i.avg_discount_price,
                "quantity": i.use_quantity,
                "code": i.main_sales_order_code,
            }
            for i in page_objects
        ]
        return IResponse(data=re_data)
