# -*- coding: utf-8 -*-
from erp_produce.filters.bom_filter import ProduceBomFilter
from erp_produce.filters.plan_filter import ProducePlanFilter
from erp_produce.models import ProduceBom, ProducePlan
from erp_produce.serializers.plan_serializers import ProducePlanSKUStatisticsSer, ProducePlanStatisticsSer
from erp_products.filters.erp_supplier_product_filters import ERPSupplierProductSKUFilter
from erp_products.models import ERPSupplierProductSKU
from erp_products.serializers_v2.erp_supplier_sku_serializers import ERPSupplierProductSKUDownloadSerializer
from utils.erp_download_utils.erp_async_download_task import ERPAsyncDownloadTaskHandler
from zhulinks_supply_chains_backend.celery import app


@app.task(queue="common_tasks")
def async_download_erp_supplier_product_sku_task(task_id: int):
    """
    异步下载erp商品
    :param task_id: 下载中心异步任务id
    :return:
    """

    handler = ERPAsyncDownloadTaskHandler(
        ERPSupplierProductSKUFilter,
        ERPSupplierProductSKUDownloadSerializer,
        async_download_erp_supplier_product_sku_task,
    )

    task = handler.get_task(task_id)
    if not task:
        return False, f"任务:{task_id}不存在"

    queryset = ERPSupplierProductSKU.objects.prefetch_related(
        "supplier",
        "product",
        "unit",
        "attr_value",
        "attr_value__attr_key",
    ).filter(company=task.company, is_deleted=False)

    return handler.process_task(queryset, task_id)


@app.task(queue="common_tasks")
def async_download_erp_product_plan_task(task_id: int):
    """
    异步下载erp生产计划
    :param task_id: 下载中心异步任务id
    :return:
    """

    handler = ERPAsyncDownloadTaskHandler(
        ProduceBomFilter,
        ProducePlanSKUStatisticsSer,
        async_download_erp_supplier_product_sku_task,
    )

    task = handler.get_task(task_id)
    if not task:
        return False, f"任务:{task_id}不存在"

    # 默认是sku商品视图导出。 statistics_by_sku
    queryset = ProduceBom.objects.filter(company_id=task.company, is_deleted=False).prefetch_related("sku")
    # 设置默认导出字段，兼容旧公共逻辑
    task.post_data.update(
        {
            "export_fields": [
                {"key": "basic.main_image", "value": "商品图片"},
                {"key": "basic.name", "value": "商品名称"},
                {"key": "basic.sku_code", "value": "货号"},
                {"key": "basic.spec_code", "value": "商品编码"},
                {"key": "basic.sales_order_ids", "value": "关联生产单号"},
                {"key": "basic.spec_value", "value": "颜色及规格"},
                {"key": "basic.size", "value": "尺寸"},
                {"key": "basic.unit_nam", "value": "单位"},
                {"key": "basic.cost_price", "value": "成本价"},
                {"key": "basic.mrp", "value": "所需补货总数量"},
            ]
        }
    )
    task.save()

    if "view" in task.query_data and task.query_data["view"] == "statistics_by_plan":
        queryset = ProducePlan.objects.filter(company=task, is_deleted=False)
        # 重新设置
        handler.filterset_class = ProducePlanFilter
        handler.serializer_class = ProducePlanStatisticsSer
        # 设置默认导出字段，兼容旧公共逻辑
        task.post_data.update(
            {
                "export_fields": [
                    {"key": "basic.code", "value": "关联生产单号"},
                    {"key": "basic.main_image", "value": "商品图片"},
                    {"key": "basic.name", "value": "商品名称"},
                    {"key": "basic.sku_code", "value": "货号"},
                    {"key": "basic.spec_code", "value": "商品编码"},
                    {"key": "basic.spec_value", "value": "颜色及规格"},
                    {"key": "basic.size", "value": "尺寸"},
                    {"key": "basic.unit_nam", "value": "单位"},
                    {"key": "basic.cost_price", "value": "成本价"},
                    {"key": "basic.mrp", "value": "所需补货总数量"},
                ]
            }
        )
        task.save()

    return handler.process_task(queryset, task_id)
