# Generated by Django 5.0.8 on 2025-03-29 03:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
    ]

    operations = [
        migrations.CreateModel(
            name="FieldsConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("module", models.CharField(max_length=64, verbose_name="模块名称")),
                (
                    "field_name",
                    models.CharField(max_length=64, verbose_name="字段名称"),
                ),
                (
                    "field_display_name",
                    models.CharField(max_length=64, verbose_name="字段显示名称"),
                ),
                ("enable", models.BooleanField(default=True, verbose_name="是否启用")),
                (
                    "order",
                    models.PositiveSmallIntegerField(default=0, verbose_name="排序"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "erp供应商字段配置",
                "verbose_name_plural": "erp供应商字段配置",
                "unique_together": {("company", "module", "field_name")},
            },
        ),
    ]
