# Generated by Django 5.0.8 on 2025-03-31 06:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_configs", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="fieldsconfig",
            name="default_value",
            field=models.CharField(
                blank=True, max_length=255, null=True, verbose_name="默认值"
            ),
        ),
        migrations.AddField(
            model_name="fieldsconfig",
            name="field_type",
            field=models.CharField(
                choices=[("number", "数字"), ("text", "文本")],
                default="text",
                max_length=20,
                verbose_name="字段类型",
            ),
        ),
        migrations.AddField(
            model_name="fieldsconfig",
            name="is_readonly",
            field=models.BooleanField(default=False, verbose_name="是否只读"),
        ),
        migrations.AddField(
            model_name="fieldsconfig",
            name="is_required",
            field=models.Bo<PERSON>anField(default=False, verbose_name="是否必填"),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="fieldsconfig",
            name="rules",
            field=models.JSO<PERSON>ield(
                blank=True,
                help_text="选择类型字段的可选值，JSON格式",
                null=True,
                verbose_name="规则",
            ),
        ),
    ]
