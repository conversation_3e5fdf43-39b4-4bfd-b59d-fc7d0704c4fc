# Generated by Django 5.0.8 on 2025-06-05 06:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_configs", "0003_orderdownloadfieldsconfig"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderDownloadFieldsSequenceConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "module_name",
                    models.CharField(max_length=128, verbose_name="模块名称"),
                ),
                (
                    "fields_config",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="字段配置"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属系统供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "ERP自定义字段导出顺序配置",
                "verbose_name_plural": "ERP自定义字段导出顺序配置",
                "unique_together": {("company", "module_name")},
            },
        ),
    ]
