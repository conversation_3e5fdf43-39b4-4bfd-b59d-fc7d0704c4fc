from common.basics.models import BaseFieldsModel
from django.core.cache import cache
from django.db import models

from .resources import DEFAULT_SKU_DIV_FIELDS


class FieldsConfig(BaseFieldsModel):
    _cache_key_prefix = "erp_fields_config_{}"

    module = models.CharField("模块名称", max_length=64)
    field_name = models.CharField("字段名称", max_length=64)
    field_display_name = models.CharField("字段显示名称", max_length=64)

    # 添加字段类型选项,暂时两个
    FIELD_TYPE_CHOICES = (
        ("number", "数字"),
        ("text", "文本"),
    )
    field_type = models.CharField("字段类型", max_length=20, choices=FIELD_TYPE_CHOICES, default="text")
    is_required = models.BooleanField("是否必填", default=False)
    is_readonly = models.BooleanField("是否只读", default=False)
    default_value = models.Char<PERSON>ield("默认值", max_length=255, blank=True, null=True)
    rules = models.JSONField("规则", help_text="选择类型字段的可选值，JSON格式", blank=True, null=True)
    enable = models.BooleanField("是否启用", default=True)
    order = models.PositiveSmallIntegerField(verbose_name="排序", default=0)
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属系统供应商",
    )

    class Meta:
        verbose_name = "erp供应商字段配置"
        verbose_name_plural = verbose_name
        unique_together = ("company", "module", "field_name")

    def save(self, *args, **kwargs):
        super().save()

        cache_key = self._cache_key_prefix.format(self.company_id)
        cache.delete(cache_key)

    def __str__(self):
        return f"{self.module}.{self.field_name} -> {self.field_display_name}"

    @classmethod
    def get_cache_configs(cls, company_id):
        cache_key = cls._cache_key_prefix.format(company_id)
        val = cache.get(cache_key)
        if val:
            return val

        configs = cls.objects.filter(company_id=company_id, enable=True).order_by("order")
        cache.set(cache_key, configs, 12 * 60 * 60)
        return configs

    @classmethod
    def delete_cache_configs(cls, company_id):
        cache_key = cls._cache_key_prefix.format(company_id)
        cache.delete(cache_key)

    @classmethod
    def get_all_fields_configs(cls, company_id):
        fields_configs = FieldsConfig.get_cache_configs(company_id)
        combine_data = {}

        for fields_config in fields_configs:
            _d = {
                "field_name": fields_config.field_name,
                "field_display_name": fields_config.field_display_name,
                "field_type": fields_config.field_type,
                "is_required": fields_config.is_required,
                "is_readonly": fields_config.is_readonly,
                "default_value": fields_config.default_value,
                "rules": fields_config.rules,
                "order": fields_config.order,
            }
            if fields_config.module not in combine_data:
                combine_data[fields_config.module] = {"configs": [_d]}
            else:
                combine_data[fields_config.module]["configs"].append(_d)

        # 添加默认的价格、属性配置，极速入库字段使用
        if "erpsupplierproductsku" not in combine_data:
            combine_data["erpsupplierproductsku"] = DEFAULT_SKU_DIV_FIELDS
        return combine_data


class OrderDownloadFieldsConfig(BaseFieldsModel):
    cache_prefix = "erp_order_download_fields_config_{}_{}"

    module_name = models.CharField("模块名称", max_length=128)
    fields_config = models.JSONField("字段配置", blank=True, null=True, default=list)
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属系统供应商",
    )

    class Meta:
        verbose_name = "ERP自定义字段导出配置"
        verbose_name_plural = verbose_name
        unique_together = ("company", "module_name")

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        cache_key = self.cache_prefix.format(self.company_id, self.module_name)
        cache.delete(cache_key)

    @classmethod
    def get_module_config(cls, module_name, company_id=None, default_config=None):
        if company_id is None:
            return []

        cache_key = cls.cache_prefix.format(company_id, module_name)
        val = cache.get(cache_key)
        if val:
            return val

        try:
            config = cls.objects.get(module_name=module_name, company_id=company_id)
            cache.set(cache_key, config.fields_config, 12 * 60 * 60)
            return config.fields_config
        except cls.DoesNotExist:
            if default_config:
                cls.objects.create(
                    module_name=module_name,
                    company_id=company_id,
                    fields_config=default_config,
                )
                cache.set(cache_key, default_config, 12 * 60 * 60)
                return default_config

            return []


class OrderDownloadFieldsSequenceConfig(BaseFieldsModel):
    cache_prefix = "erp_order_download_fields_seq_config_{}_{}"

    module_name = models.CharField("模块名称", max_length=128)
    fields_config = models.JSONField("字段配置", blank=True, null=True, default=list)
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="company_id",
        verbose_name="所属系统供应商",
    )

    class Meta:
        verbose_name = "ERP自定义字段导出顺序配置"
        verbose_name_plural = verbose_name
        unique_together = ("company", "module_name")

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.delete_cache()

    def delete_cache(self):
        cache_key = self.cache_prefix.format(self.company_id, self.module_name)
        cache.delete(cache_key)

    @classmethod
    def get_module_config(cls, module_name, company_id=None):
        if company_id is None:
            return []

        cache_key = cls.cache_prefix.format(company_id, module_name)
        val = cache.get(cache_key)
        if val:
            return val

        try:
            config = cls.objects.get(module_name=module_name, company_id=company_id)
            cache.set(cache_key, config.fields_config, 12 * 60 * 60)
            return config.fields_config
        except cls.DoesNotExist:
            default_config = []
            cls.objects.create(
                module_name=module_name,
                company_id=company_id,
                fields_config=[],
            )
            cache.set(cache_key, default_config, 12 * 60 * 60)
            return default_config
