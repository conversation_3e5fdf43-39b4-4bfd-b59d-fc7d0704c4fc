from django.contrib import admin

from erp_configs.models import FieldsConfig, OrderDownloadFieldsConfig


# Register your models here.
@admin.register(FieldsConfig)
class FieldsConfigAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "module",
        "field_name",
        "field_display_name",
        "field_type",
        "is_required",
        "is_readonly",
        "default_value",
        "enable",
        "order",
        "company",
    )

    search_fields = ("field_name",)

    autocomplete_fields = ("company",)

    list_filter = ("module", "enable", "field_type")


@admin.register(OrderDownloadFieldsConfig)
class OrderDownloadFieldsConfigAdmin(admin.ModelAdmin):
    list_display = OrderDownloadFieldsConfig.get_all_fields_names("fields_config")
    search_fields = ("module_name",)
    autocomplete_fields = ("company",)
    list_filter = ("module_name",)
