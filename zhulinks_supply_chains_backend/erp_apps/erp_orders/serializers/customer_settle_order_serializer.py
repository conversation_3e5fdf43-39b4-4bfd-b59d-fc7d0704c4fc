# -*- coding: utf-8 -*-
from common.basic import CustomizeSerializer
from common.formats import DATETIME_FORMAT
from erp_client.serializers import ERPCustomerInfoDownloadSerializer
from erp_orders.models import (
    ERPCustomerSettleOrder,
    ERPCustomerSettleOrderDetails,
    ERPSalesOrder,
    ERPSalesOrderDetails,
)
from erp_orders.serializers.sales_order_serializer import ERPSalesOrderDetailInfoDownloadSerializer
from erp_products.models import ERPSupplierProductSKU
from rest_framework import serializers
from utils.caches import get_user_real_name_by_user_id


class ERPCustomerAwaitingSettleOrderListSer(CustomizeSerializer):
    sales_type_display = serializers.CharField(source="get_sales_type_display")
    customer_name = serializers.CharField(source="customer.name")
    total_amount = serializers.SerializerMethodField()
    receive_amount = serializers.DecimalField(source="receive_amount_display", decimal_places=2, max_digits=20)

    class Meta:
        model = ERPSalesOrder
        fields = (
            "code",
            "sales_type",
            "sales_type_display",
            "total_amount",
            "receive_amount",
            "rounding_amount",
            "customer_id",
            "customer_name",
            "create_date",
            "create_user",
            "await_settle_amount",
        )

    @staticmethod
    def get_total_amount(obj: ERPSalesOrder):
        use_total_amount = abs(getattr(obj, "use_total_amount", obj.total_amount))
        if obj.sales_type in [3, 4]:
            return -use_total_amount
        return use_total_amount


class ERPCustomerSettleOrderCreateSerializer(CustomizeSerializer):
    """创建客户结算单序列化器"""

    order_codes = serializers.ListField(
        child=serializers.CharField(),
        required=True,
    )
    total_settled_amount = serializers.DecimalField(max_digits=20, decimal_places=2, required=True)
    pay_type = serializers.ChoiceField(
        choices=[
            (1, "现金"),
            (2, "支付宝"),
            (3, "微信"),
            (4, "银行转账"),
            (5, "其他"),
        ],
        default=1,
    )
    settlement_date = serializers.DateTimeField(required=True)
    remark = serializers.CharField(required=False, allow_blank=True)
    remark_images = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        default=list,
    )

    class Meta:
        model = ERPCustomerSettleOrder
        fields = (
            "order_codes",
            "total_settled_amount",
            "pay_type",
            "settlement_date",
            "remark",
            "remark_images",
        )


class ERPCustomerSettleOrderDetailSerializer(CustomizeSerializer):
    """结算单明细序列化器"""

    erp_order_code = serializers.CharField(source="sales_order.code", allow_null=True)
    create_date = serializers.DateTimeField(source="sales_order.create_date")
    create_user = serializers.SerializerMethodField()
    erp_order_sales_type = serializers.IntegerField(source="sales_order.sales_type")
    erp_order_sales_type_display = serializers.CharField(source="sales_order.get_sales_type_display")

    class Meta:
        model = ERPCustomerSettleOrderDetails
        fields = (
            "erp_order_code",
            "amount",
            "settled_amount",
            "erp_order_sales_type",
            "erp_order_sales_type_display",
            "create_date",
            "create_user",
        )

    def get_create_user(self, instance):
        return get_user_real_name_by_user_id(instance.sales_order.create_user)


class ERPCustomerSettleOrderInfoSerializer(CustomizeSerializer):
    """结算单信息序列化器"""

    status_display = serializers.CharField(source="get_status_display")
    pay_type_display = serializers.CharField(source="get_pay_type_display")
    approve_status_display = serializers.CharField(source="get_approve_status_display")
    customer_name = serializers.CharField(source="customer.name")
    details = serializers.SerializerMethodField()
    approve_user = serializers.SerializerMethodField()
    settlement_user = serializers.SerializerMethodField()

    class Meta:
        model = ERPCustomerSettleOrder
        fields = (
            "code",
            "status",
            "status_display",
            "total_amount",
            "total_settled_amount",
            "total_pending_amount",
            "pay_type",
            "pay_type_display",
            "approve_status",
            "approve_status_display",
            "approve_user",
            "approve_time",
            "settlement_date",
            "settlement_user",
            "remark",
            "remark_images",
            "customer_id",
            "customer_name",
            "details",
            "create_user",
            "create_date",
            "update_date",
        )

    def get_details(self, obj: ERPCustomerSettleOrder):
        """获取结算单明细"""
        if getattr(self.parent, "many", False):
            return []

        details = obj.details.all()
        detail_data_list = ERPCustomerSettleOrderDetailSerializer(details, many=True).data
        # 根据类型重构
        details_tmp_map = {}

        for detail_data in detail_data_list:
            sales_type = detail_data["erp_order_sales_type"]
            if sales_type not in details_tmp_map:
                details_tmp_map[sales_type] = {
                    "erp_order_sales_type": sales_type,
                    "erp_order_sales_type_display": detail_data["erp_order_sales_type_display"],
                    "details": [],
                }
            details_tmp_map[sales_type]["details"].append(detail_data)
        return details_tmp_map.values()

    @staticmethod
    def get_approve_user(obj: ERPCustomerSettleOrder):
        return get_user_real_name_by_user_id(obj.approve_user)

    @staticmethod
    def get_settlement_user(obj: ERPCustomerSettleOrder):
        return get_user_real_name_by_user_id(obj.settlement_user)


class ERPCustomerSKUPickUpSerializer(CustomizeSerializer):
    latest_transaction_time = serializers.DateTimeField(source="create_date")
    latest_price = serializers.DecimalField(max_digits=20, decimal_places=2, source="price")
    last_price_type = serializers.CharField(source="price_type")
    last_discount = serializers.FloatField(source="discount")
    total_quantity = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()
    latest_discount_price = serializers.SerializerMethodField()
    inbound_detail_code = serializers.SerializerMethodField()
    warehouse_name = serializers.SerializerMethodField()
    warehouse_position_name = serializers.SerializerMethodField()
    in_warehouse_quantity = serializers.SerializerMethodField()
    latest_quantity = serializers.IntegerField(source="quantity")
    sku_id = serializers.IntegerField(source="sku.sku_id")
    spec_code = serializers.CharField(source="sku.spec_code")
    size = serializers.CharField(source="sku.size")
    name = serializers.CharField(source="sku.name")
    code = serializers.CharField(source="sku.product.code", allow_null=True)
    supplier_name = serializers.CharField(source="sku.supplier.name", allow_null=True)
    unit_name = serializers.CharField(source="sku.unit.name", allow_null=True)
    main_images = serializers.ListField(child=serializers.CharField(), source="sku.main_images", allow_null=True)
    other_attributes = serializers.CharField(source="sku.other_attributes")
    other_attributes2 = serializers.CharField(source="sku.other_attributes2")
    other_attributes3 = serializers.CharField(source="sku.other_attributes3")
    other_attributes4 = serializers.CharField(source="sku.other_attributes4")
    other_attributes5 = serializers.CharField(source="sku.other_attributes5")

    class Meta:
        model = ERPSalesOrderDetails
        fields = (
            "latest_transaction_time",
            "latest_price",
            "latest_discount_price",
            "latest_quantity",
            "total_quantity",
            "total_amount",
            "cost_price",
            "label_price",
            "market_price",
            "purchase_price",
            "inbound_detail_code",
            "last_price_type",
            "last_discount",
            "warehouse_name",
            "warehouse_position_name",
            "in_warehouse_quantity",
            "sku_id",
            "spec_code",
            "size",
            "name",
            "code",
            "supplier_name",
            "unit_name",
            "main_images",
            "other_price1",
            "other_price2",
            "other_price3",
            "other_price4",
            "other_price5",
            "other_price6",
            "other_attributes",
            "other_attributes2",
            "other_attributes3",
            "other_attributes4",
            "other_attributes5",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        skus = [i.sku for i in self.instance]
        self.skus_inventory_map = ERPSupplierProductSKU.get_batch_sku_total_in_warehouse_quantity(skus)

    def get_in_warehouse_quantity(self, obj: ERPSalesOrderDetails):
        return self.skus_inventory_map.get(obj.sku_id, 0) or 0

    def get_total_quantity(self, obj: ERPSalesOrderDetails):
        return self.context["sku_aggregate_data_map"].get(obj.sku_id, {}).get("total_quantity", 0) or 0

    def get_total_amount(self, obj: ERPSalesOrderDetails):
        return self.context["sku_aggregate_data_map"].get(obj.sku_id, {}).get("total_amount", 0) or 0

    @staticmethod
    def get_latest_discount_price(obj: ERPSalesOrderDetails):
        return getattr(obj, "avg_discount_price", 0)

    @staticmethod
    def get_inbound_detail_code(obj):
        return None

    @staticmethod
    def get_warehouse_name(obj):
        return ""

    @staticmethod
    def get_warehouse_position_name(obj):
        return ""


class ERPCustomerSettleOrderDetailsDownloadSerializer(CustomizeSerializer):
    sales_order_code = serializers.CharField(source="sales_order.code", allow_null=True)
    sales_type_display = serializers.CharField(source="sales_order.get_sales_type_display", allow_null=True)
    pay_status_display = serializers.CharField(source="sales_order.get_pay_status_display", allow_null=True)
    approve_status_display = serializers.CharField(source="sales_order.get_approve_status_display", allow_null=True)
    total_quantity = serializers.IntegerField(source="sales_order.use_total_quantity", allow_null=True)
    create_date = serializers.DateTimeField(format=DATETIME_FORMAT, source="sales_order.create_date", allow_null=True)
    approve_time = serializers.DateTimeField(format=DATETIME_FORMAT, source="sales_order.approve_time", allow_null=True)
    remark = serializers.CharField(source="sales_order.remark", allow_null=True)
    product_details = serializers.SerializerMethodField()

    class Meta:
        model = ERPCustomerSettleOrderDetails
        fields = (
            "sales_order_code",
            "sales_type_display",
            "pay_status_display",
            "approve_status_display",
            "amount",
            "total_quantity",
            "create_date",
            "approve_time",
            "remark",
            "product_details",
        )

    def get_product_details(self, obj: ERPCustomerSettleOrderDetails):
        # 货品信息
        product_details = getattr(obj, "product_details")
        category_cache = self.context["category_cache"]
        ser = ERPSalesOrderDetailInfoDownloadSerializer(product_details, many=True, context={"category_cache": category_cache})
        return ser.data


class ERPCustomerSettleOrderDownloadSerializer(CustomizeSerializer):
    customer_info = serializers.SerializerMethodField()
    settlement_user = serializers.SerializerMethodField()
    approve_user = serializers.SerializerMethodField()
    status_display = serializers.CharField(source="get_status_display")
    settlement_date = serializers.DateTimeField(format=DATETIME_FORMAT)
    approve_time = serializers.DateTimeField(format=DATETIME_FORMAT)
    create_date = serializers.DateTimeField(format=DATETIME_FORMAT)
    details = serializers.SerializerMethodField()

    class Meta:
        model = ERPCustomerSettleOrder
        fields = (
            "customer_info",
            "code",
            "status_display",
            "total_settled_amount",
            "settlement_date",
            "create_date",
            "approve_time",
            "settlement_user",
            "create_user",
            "approve_user",
            "remark",
            "details",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 批量查询订单
        sales_order_pk_list = [detail.sales_order_id for i in self.instance for detail in i.details.all()]
        main_sales_order = ERPSalesOrder.objects.filter(pk__in=sales_order_pk_list)
        use_sales_order = ERPSalesOrder.objects.aggregate_use_data_with_sub_orders(main_sales_order)

        self.use_sales_order_map = {i.pk: i for i in use_sales_order}

        # 查询商品信息
        use_sales_order_details = ERPSalesOrder.objects.get_actual_details_with_aggregate(sales_order_pk_list).prefetch_related(
            "sku",
            "sku__unit",
            "sku__product",
        )
        self.use_sales_order_details_map = {}
        skus = []
        for use_sales_order_detail in use_sales_order_details:
            skus.append(use_sales_order_detail.sku)

            main_sales_order_id = use_sales_order_detail.main_sales_order_id
            if main_sales_order_id not in self.use_sales_order_details_map:
                self.use_sales_order_details_map[main_sales_order_id] = [use_sales_order_detail]
            else:
                self.use_sales_order_details_map[main_sales_order_id].append(use_sales_order_detail)

        self.category_cache = ERPSupplierProductSKU.get_batch_category_display_cache(skus)

    @staticmethod
    def get_customer_info(obj: ERPCustomerSettleOrder):
        return ERPCustomerInfoDownloadSerializer(obj.customer, many=False).data

    @staticmethod
    def get_settlement_user(obj: ERPCustomerSettleOrder):
        return get_user_real_name_by_user_id(obj.settlement_user)

    @staticmethod
    def get_approve_user(obj: ERPCustomerSettleOrder):
        return get_user_real_name_by_user_id(obj.approve_user)

    def get_details(self, obj: ERPCustomerSettleOrder):
        use_details = obj.details.all()

        for d in use_details:
            d.sales_order = self.use_sales_order_map.get(d.sales_order_id)
            d.product_details = self.use_sales_order_details_map.get(d.sales_order_id)

        return ERPCustomerSettleOrderDetailsDownloadSerializer(
            instance=use_details,
            many=True,
            context={"category_cache": self.category_cache},
        ).data
