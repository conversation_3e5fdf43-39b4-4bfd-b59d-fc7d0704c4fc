# -*- coding: utf-8 -*-
from common.basics.exceptions import APIViewException
from django.db import transaction
from django.utils import timezone
from erp_orders.models import ERPOrder, ERPOrderDetails
from erp_orders.models.erp_sales_orders_models import ERPSalesOrderTidRelates
from erp_purchase.models import InventoryChangeDetails, InventoryAdjustmentOrderDetail
from rest_framework.request import Request

from users.models import User


class ErpOrderServices(object):
    def __init__(self, erp_order: ERPOrder, current_user: User, request: Request):
        self.erp_order = erp_order
        self.main_sales_order = self.erp_order.main_erp_sales_order
        self.current_user = current_user

    def validate_order_cancel_approve_status(self):
        if self.erp_order.status == 2:
            raise APIViewException(err_message="Canceled order cannot be unapproved")

        if self.erp_order.approve_status == 1:
            raise APIViewException(err_message="Order not approved, no need to unapprove")

        if self.erp_order.settle_status == 2:
            raise APIViewException(err_message="Order has been settled, cannot be unapproved")

        # 判断是否有比当前订单更新的出入库单
        if ERPOrder.objects.filter(
            id__gt=self.erp_order.id,
            main_erp_sales_order=self.main_sales_order,
            status=1,
            is_deleted=False,
            order_type=2,
        ).exists():
            raise APIViewException(err_message="Main sales order has other sub orders, cannot unapprove")

    def validate_order_cancel_status(self):
        # 状态验证
        if self.erp_order.status == 2:
            raise APIViewException(err_message="Order has been canceled, cannot cancel again")

        if self.erp_order.approve_status == 2:
            raise APIViewException(err_message="Approved order cannot be canceled")

        if self.erp_order.settle_status == 2:
            raise APIViewException(err_message="Order has been settled, cannot be canceled")

    def cancel_approve(self):
        # 入库订单主键id
        inbound_order_id = self.erp_order.inbound_order_id

        # 查询关联的销售单所有的tid信息
        sales_order_tid_relate = ERPSalesOrderTidRelates.objects.filter(erp_sales_order=self.main_sales_order).first()
        if not sales_order_tid_relate:
            sales_order_tid_relate = ERPSalesOrderTidRelates.objects.create(erp_sales_order=self.main_sales_order, tid_info={})

        # 转换为InventoryChangeDetails.CHANGE_TYPE_CHOICES的枚举
        change_type_mp = {
            1: InventoryChangeDetails.CHANGE_TYPE_21,
            2: InventoryChangeDetails.CHANGE_TYPE_23,
            3: InventoryChangeDetails.CHANGE_TYPE_12,
            4: InventoryChangeDetails.CHANGE_TYPE_14,
        }
        change_type = change_type_mp[self.erp_order.sales_type]
        # 获取订单明细
        order_details = ERPOrderDetails.objects.filter(erp_order=self.erp_order, is_deleted=False)

        # 判断是否有正在调整的sku
        InventoryAdjustmentOrderDetail.judge_sku_is_adjusting([d.sku_id for d in order_details])

        with transaction.atomic():
            # 根据订单类型进行不同的处理
            if self.erp_order.sales_type in [1, 2]:  # 1=销售订单，2=借货订单
                # 处理销售订单或借货订单
                for detail in order_details:
                    # sku关联
                    detail_inventories_relates = detail.erporderdetailinventoryrelates_set.prefetch_related("sku_inventory").all()

                    for detail_inventories_relate in detail_inventories_relates:
                        sku_inventory = detail_inventories_relate.sku_inventory
                        if not sku_inventory:
                            continue

                        # 根据标签类型处理，默认使用当前的数量
                        sold_quantity = detail_inventories_relate.quantity

                        if sku_inventory.tag_type == 1:  # RFID标签
                            # 更新tid_info中的verify_status状态
                            for tid, tid_data in detail.tid_info.items():
                                if tid in sku_inventory.tid_info:
                                    if self.erp_order.sales_type == 1 and sku_inventory.tid_info[tid].get("verify_status") == "sold":
                                        # 还原为已验证状态
                                        sku_inventory.set_tid_verified_status(tid, self.current_user.user_id)
                                    elif self.erp_order.sales_type == 2 and sku_inventory.tid_info[tid].get("verify_status") == "loaned":
                                        # 还原为已验证状态
                                        sku_inventory.set_tid_verified_status(tid, self.current_user.user_id)

                                # 主单出库反审核：检查子单并删除TID记录
                                if self.erp_order.order_type == 1:
                                    sales_order_tid_relate.pop_tid(tid)

                            # 更新库存数量 - 根据tid_info长度
                            sold_quantity = len(detail_inventories_relate.tid_info)
                        if sold_quantity > 0:
                            if self.erp_order.sales_type == 1:
                                # 减少售出数量
                                sku_inventory.sold_quantity = max(0, (sku_inventory.sold_quantity or 0) - sold_quantity)
                            else:
                                # 减少借出数量
                                sku_inventory.loaned_quantity = max(0, (sku_inventory.loaned_quantity or 0) - sold_quantity)
                            # 保存更新
                            sku_inventory.save()
                            sales_order_tid_relate.save()
                    # 删除明细
                    InventoryChangeDetails.delete_detail(change_type, detail.id)
            elif self.erp_order.sales_type in [3, 4]:  # 3=销售退货订单，4=借货退货订单
                # 正常处理退货订单
                for detail in order_details:
                    detail_inventories_relates = detail.erporderdetailinventoryrelates_set.prefetch_related("sku_inventory").all()

                    for detail_inventories_relate in detail_inventories_relates:
                        sku_inventory = detail_inventories_relate.sku_inventory
                        if not sku_inventory:
                            continue

                        # 根据标签类型处理, 默认使用计算好的数量
                        return_quantity = detail_inventories_relate.quantity

                        if sku_inventory.tag_type == 1:  # RFID标签
                            # 如果有tid_info，则从库存中移除这些tid
                            for tid in detail_inventories_relate.tid_info.keys():
                                if tid in sku_inventory.tid_info:
                                    if self.erp_order.sales_type == 3:
                                        # 销售退反审，改售出状态
                                        sku_inventory.set_tid_sold_status(tid, self.current_user.user_id)
                                    else:
                                        # 借货退反审，改借出状态
                                        sku_inventory.set_tid_loaned_status(tid, self.current_user.user_id)
                                    sales_order_tid_relate.create_or_update_out_tid(tid, sku_inventory.pk)
                                # 更新库存数量 - 根据tid_info长度
                                return_quantity = len(detail_inventories_relate.tid_info)
                        else:
                            if sku_inventory.in_warehouse_quantity < return_quantity:
                                # 如果原来的库存单不够库存，不能原路退回；找出其他品的所有大于0的库存单，先进先出
                                #
                                raise APIViewException(err_message=f"商品({sku_inventory.sku.name})库存已不足{return_quantity},无法反审")

                        if return_quantity > 0:
                            if self.erp_order.sales_type == 3:
                                # 减少销售退数量
                                sku_inventory.sold_return_quantity = max(0, (sku_inventory.sold_return_quantity or 0) - return_quantity)
                            else:
                                # 介绍借出退数量
                                sku_inventory.loaned_return_quantity = max(0, (sku_inventory.loaned_return_quantity or 0) - return_quantity)

                            # 保存更新
                            sku_inventory.save()
                            sales_order_tid_relate.save()

                    InventoryChangeDetails.delete_detail(change_type, detail.id)
            # 更新订单状态
            self.erp_order.approve_status = 1
            self.erp_order.approve_user = ""
            self.erp_order.approve_time = None  # 清除审核时间
            self.erp_order.update_user = self.current_user.user_id
            self.erp_order.save()

            # 更新关联开单的发货状态
            erp_sales_order = self.erp_order.erp_sales_order
            if erp_sales_order.sales_order_type == 2:
                erp_sales_order.approve_status = 1
                erp_sales_order.approve_user = ""
                erp_sales_order.approve_time = None

            erp_sales_order.shipping_status = 1  # 待发货
            erp_sales_order.shipping_user = ""
            erp_sales_order.shipping_time = None  # 清除发货时间
            erp_sales_order.save()

    def cancel_order(self):
        # 获取订单明细
        order_details = ERPOrderDetails.objects.filter(erp_order=self.erp_order, is_deleted=False)
        # 判断是否有正在调整的sku
        InventoryAdjustmentOrderDetail.judge_sku_is_adjusting([d.sku_id for d in order_details])

        current_time = timezone.now()

        if self.erp_order.order_type == 1:
            # 主订单取消，置为待审核状态
            with transaction.atomic():
                # 更新订单状态
                self.erp_order.status = 2
                self.erp_order.update_user = self.current_user.user_id
                self.erp_order.update_date = current_time
                self.erp_order.save(update_fields=["status", "update_user", "update_date"])

                # 更新主销售单状态
                self.main_sales_order.approve_status = 1  # 待审核
                self.main_sales_order.approve_user = ""
                self.main_sales_order.approve_time = None
                self.main_sales_order.update_user = self.current_user.user_id
                self.main_sales_order.update_date = current_time
                self.main_sales_order.save(update_fields=["approve_status", "approve_user", "approve_time", "update_user", "update_date"])
        else:
            # 副订单取消，关联的销售订单也是副单；该单也是取消
            sub_erp_sales_order = self.erp_order.erp_sales_order
            with transaction.atomic():
                self.erp_order.status = 2
                self.erp_order.update_user = self.current_user.user_id
                self.erp_order.update_date = current_time
                self.erp_order.save(update_fields=["status", "update_user", "update_date"])

                if sub_erp_sales_order.sales_order_type == 2:
                    # 更新副销售单状态
                    sub_erp_sales_order.status = 2
                sub_erp_sales_order.update_user = self.current_user.user_id
                sub_erp_sales_order.update_date = current_time
                sub_erp_sales_order.save(update_fields=["status", "update_user", "update_date"])
