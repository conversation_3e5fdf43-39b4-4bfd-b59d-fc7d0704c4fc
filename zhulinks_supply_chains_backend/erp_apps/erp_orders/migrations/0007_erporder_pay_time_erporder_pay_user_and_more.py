# Generated by Django 5.0.8 on 2025-04-21 09:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_client", "0003_erpcompanycustomerinfo_company"),
        ("erp_orders", "0006_alter_erporder_approve_time_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="erporder",
            name="pay_time",
            field=models.DateTimeField(blank=True, null=True, verbose_name="支付时间"),
        ),
        migrations.AddField(
            model_name="erporder",
            name="pay_user",
            field=models.CharField(
                blank=True, max_length=20, null=True, verbose_name="标记支付人"
            ),
        ),
        migrations.AddField(
            model_name="erporder",
            name="settle_status",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "待结算"), (2, "已结算")],
                default=1,
                verbose_name="结算状态",
            ),
        ),
        migrations.AddField(
            model_name="erporder",
            name="settle_time",
            field=models.DateTimeField(blank=True, null=True, verbose_name="结算时间"),
        ),
        migrations.AddField(
            model_name="erporder",
            name="settle_user",
            field=models.CharField(
                blank=True, max_length=20, null=True, verbose_name="结算人"
            ),
        ),
        migrations.AddField(
            model_name="erpsalesorder",
            name="settle_status",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "待结算"), (2, "已结算")],
                default=1,
                verbose_name="结算状态",
            ),
        ),
        migrations.AddField(
            model_name="erpsalesorder",
            name="settle_time",
            field=models.DateTimeField(blank=True, null=True, verbose_name="结算时间"),
        ),
        migrations.AddField(
            model_name="erpsalesorder",
            name="settle_user",
            field=models.CharField(
                blank=True, max_length=20, null=True, verbose_name="结算人"
            ),
        ),
        migrations.CreateModel(
            name="ERPCustomerSettleOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True,
                        editable=False,
                        max_length=64,
                        verbose_name="结算单编号",
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "待结算"),
                            (2, "部分结算"),
                            (3, "已结算"),
                            (4, "已取消"),
                        ],
                        default=1,
                        verbose_name="结算状态",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="销售退货订单或借货退货订单的应收金额",
                        max_digits=20,
                        verbose_name="应结算总金额",
                    ),
                ),
                (
                    "total_settled_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="已结算金额",
                    ),
                ),
                (
                    "total_pending_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="total_amount - total_settled_amount",
                        max_digits=20,
                        verbose_name="待结算金额",
                    ),
                ),
                (
                    "pay_type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "现金"),
                            (2, "支付宝"),
                            (3, "微信"),
                            (4, "银行转账"),
                            (5, "其他"),
                        ],
                        default=1,
                        verbose_name="支付方式",
                    ),
                ),
                (
                    "approve_status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "待审核"), (2, "已审核")],
                        default=1,
                        verbose_name="审核状态",
                    ),
                ),
                (
                    "approve_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="审核人"
                    ),
                ),
                (
                    "approve_time",
                    models.DateTimeField(
                        blank=True, default=None, null=True, verbose_name="审核时间"
                    ),
                ),
                (
                    "settlement_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="结算日期"
                    ),
                ),
                (
                    "settlement_user",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="结算人"
                    ),
                ),
                ("remark", models.TextField(blank=True, verbose_name="备注")),
                (
                    "remark_images",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="备注图片"
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商",
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_client.erpcompanycustomerinfo",
                        to_field="customer_id",
                        verbose_name="所属客户",
                    ),
                ),
            ],
            options={
                "verbose_name": "ERP订单结算",
                "verbose_name_plural": "ERP订单结算",
            },
        ),
        migrations.CreateModel(
            name="ERPCustomerSettleOrderDetails",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="销售退货订单或借货退货订单的应收金额",
                        max_digits=20,
                        verbose_name="应结算金额",
                    ),
                ),
                (
                    "settled_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="已结算金额",
                    ),
                ),
                (
                    "erp_order",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_orders.erporder",
                        verbose_name="关联出入库单",
                    ),
                ),
                (
                    "sales_order",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="erp_orders.erpsalesorder",
                        verbose_name="关联销售订单",
                    ),
                ),
                (
                    "settle_order",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="details",
                        to="erp_orders.erpcustomersettleorder",
                        verbose_name="关联结算单",
                    ),
                ),
            ],
            options={
                "verbose_name": "ERP订单结算明细",
                "verbose_name_plural": "ERP订单结算明细",
            },
        ),
        migrations.AddConstraint(
            model_name="erpcustomersettleorder",
            constraint=models.UniqueConstraint(
                fields=("code", "company"), name="unique_code_for_company_in_settlement"
            ),
        ),
    ]
