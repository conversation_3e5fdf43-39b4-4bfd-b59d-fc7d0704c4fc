# Generated by Django 5.0.8 on 2025-04-16 11:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0081_dataapplication_callback_url"),
        ("erp_client", "0003_erpcompanycustomerinfo_company"),
        ("erp_orders", "0002_remove_erpsalesorderdetails_tid_info_and_more"),
        ("erp_purchase", "0062_alter_erpskuinventory_order_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="ERPOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True, max_length=64, verbose_name="销售订单编号"
                    ),
                ),
                (
                    "status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "正常"), (2, "已取消")],
                        default=1,
                        verbose_name="订单状态",
                    ),
                ),
                (
                    "sales_type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "销售订单"),
                            (2, "借货订单"),
                            (3, "销售退货订单"),
                            (4, "借货退货订单"),
                        ],
                        default=1,
                        verbose_name="销售类型",
                    ),
                ),
                ("remark", models.TextField(blank=True, verbose_name="备注")),
                (
                    "remark_images",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="备注图片"
                    ),
                ),
                (
                    "approve_status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "待审核"), (2, "已审核")],
                        default=1,
                        verbose_name="审核状态",
                    ),
                ),
                (
                    "approve_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="审核人"
                    ),
                ),
                (
                    "approve_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="审核时间"),
                ),
                (
                    "pay_status",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "待付款"), (2, "已付款"), (3, "已取消")],
                        default=1,
                        verbose_name="支付状态",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=20, verbose_name="订单总金额"
                    ),
                ),
                (
                    "discount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="订单折扣",
                    ),
                ),
                (
                    "receive_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=20, verbose_name="实收金额"
                    ),
                ),
            ],
            options={
                "verbose_name": "ERP订单(开单)",
                "verbose_name_plural": "ERP订单(开单)",
            },
        ),
        migrations.CreateModel(
            name="ERPOrderDetails",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "price_type",
                    models.CharField(
                        choices=[
                            ("label_price", "标签价"),
                            ("cost_price", "成本价"),
                            ("market_price", "市场价"),
                            ("purchase_price", "供应价"),
                        ],
                        default="label_price",
                        verbose_name="价格类型",
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="销售价格",
                    ),
                ),
                (
                    "discount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="订单折扣",
                    ),
                ),
                (
                    "discount_price",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=20,
                        verbose_name="折扣后价格",
                    ),
                ),
                ("quantity", models.IntegerField(default=0, verbose_name="销售数量")),
                (
                    "tid_info",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="TID信息"
                    ),
                ),
            ],
            options={
                "verbose_name": "ERP销售订单明细(开单明细)",
                "verbose_name_plural": "ERP销售订单明细(开单明细)",
            },
        ),
        migrations.AlterModelOptions(
            name="erpsalesorder",
            options={
                "verbose_name": "ERP销售订单(开单)",
                "verbose_name_plural": "ERP销售订单(开单)",
            },
        ),
        migrations.AlterModelOptions(
            name="erpsalesorderdetails",
            options={
                "verbose_name": "ERP销售订单明细(开单明细)",
                "verbose_name_plural": "ERP销售订单明细(开单明细)",
            },
        ),
        migrations.AddField(
            model_name="erpsalesorder",
            name="status",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "正常"), (2, "已取消")], default=1, verbose_name="订单状态"
            ),
        ),
        migrations.AlterField(
            model_name="erpsalesorderdetails",
            name="sku_inventory",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_purchase.erpskuinventory",
                verbose_name="关联SKU库存",
            ),
        ),
        migrations.AddConstraint(
            model_name="erpsalesorder",
            constraint=models.UniqueConstraint(
                fields=("code", "company"),
                name="unique_code_for_company_in_sales_order",
            ),
        ),
        migrations.AddField(
            model_name="erporder",
            name="company",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="companies.company",
                to_field="company_id",
                verbose_name="所属供应商",
            ),
        ),
        migrations.AddField(
            model_name="erporder",
            name="customer",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_client.erpcompanycustomerinfo",
                to_field="customer_id",
                verbose_name="所属客户",
            ),
        ),
        migrations.AddField(
            model_name="erporder",
            name="erp_sales_order",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_orders.erpsalesorder",
                verbose_name="关联销售订单",
            ),
        ),
        migrations.AddField(
            model_name="erporderdetails",
            name="sale_order_detail",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_orders.erpsalesorderdetails",
                verbose_name="关联销售订单明细",
            ),
        ),
        migrations.AddField(
            model_name="erporderdetails",
            name="sales_order",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_orders.erpsalesorder",
                verbose_name="关联销售订单",
            ),
        ),
        migrations.AddField(
            model_name="erporderdetails",
            name="sku_inventory",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="erp_purchase.erpskuinventory",
                verbose_name="关联SKU库存",
            ),
        ),
        migrations.AddConstraint(
            model_name="erporder",
            constraint=models.UniqueConstraint(
                fields=("code", "company"), name="unique_code_for_company_in_order"
            ),
        ),
        migrations.AddConstraint(
            model_name="erporder",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("erp_sales_order",),
                name="unique_erp_sales_order_in_order",
            ),
        ),
        migrations.AddConstraint(
            model_name="erporderdetails",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("sale_order_detail",),
                name="unique_sale_order_detail_in_order_details",
            ),
        ),
    ]
