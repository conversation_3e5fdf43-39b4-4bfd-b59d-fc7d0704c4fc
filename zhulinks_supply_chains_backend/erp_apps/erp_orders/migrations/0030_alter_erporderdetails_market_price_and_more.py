# Generated by Django 5.1.7 on 2025-06-09 10:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_orders", "0029_alter_erporder_sales_type_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="erporderdetails",
            name="market_price",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="市场|吊牌价"
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price1",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="其他价格1"
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price2",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="其他价格2"
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price3",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="其他价格3"
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price4",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="其他价格4"
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price5",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="其他价格5"
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price6",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="其他价格6"
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="purchase_price",
            field=models.DecimalField(
                decimal_places=2, default=0, max_digits=10, verbose_name="采购价"
            ),
        ),
    ]
