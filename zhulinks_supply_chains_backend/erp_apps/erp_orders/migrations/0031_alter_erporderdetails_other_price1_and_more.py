# Generated by Django 5.0.8 on 2025-06-10 02:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("erp_orders", "0030_alter_erporderdetails_market_price_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price1",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0,
                max_digits=10,
                null=True,
                verbose_name="其他价格1",
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price2",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0,
                max_digits=10,
                null=True,
                verbose_name="其他价格2",
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price3",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0,
                max_digits=10,
                null=True,
                verbose_name="其他价格3",
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price4",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0,
                max_digits=10,
                null=True,
                verbose_name="其他价格4",
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price5",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0,
                max_digits=10,
                null=True,
                verbose_name="其他价格5",
            ),
        ),
        migrations.AlterField(
            model_name="erporderdetails",
            name="other_price6",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0,
                max_digits=10,
                null=True,
                verbose_name="其他价格6",
            ),
        ),
    ]
