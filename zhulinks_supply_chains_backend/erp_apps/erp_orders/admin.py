from django.contrib import admin

# Register your models here.
from erp_orders.models import (
    ERPOrder,
    ERPOrderDetailInventoryRelates,
    ERPOrderDetails,
    ERPSalesOrder,
    ERPSalesOrderDetails,
)


@admin.register(ERPSalesOrder)
class ERPSalesOrderAdmin(admin.ModelAdmin):
    list_display = ERPSalesOrder.get_all_fields_names()

    search_fields = ("code",)

    list_filter = (
        "status",
        "sales_order_type",
        "sales_type",
        "approve_status",
        "pay_status",
        "settle_status",
        "shipping_status",
    )

    autocomplete_fields = ("customer", "company")


@admin.register(ERPSalesOrderDetails)
class ERPSalesOrderDetailsAdmin(admin.ModelAdmin):
    list_display = ERPSalesOrderDetails.get_all_fields_names()

    search_fields = ("sku__name", "sku__spec_code", "sku__product__code")

    list_filter = (
        "tag_type",
        "price_type",
    )

    autocomplete_fields = ("sku_inventory", "sku", "sales_order")


@admin.register(ERPOrder)
class ERPOrderAdmin(admin.ModelAdmin):
    list_display = ERPOrder.get_all_fields_names()

    search_fields = ("code",)

    list_filter = (
        "status",
        "sales_type",
        "order_type",
        "approve_status",
        "pay_status",
        "settle_status",
    )

    autocomplete_fields = ("customer", "company", "erp_sales_order", "main_erp_sales_order")


@admin.register(ERPOrderDetails)
class ERPOrderDetailsAdmin(admin.ModelAdmin):
    list_display = ERPOrderDetails.get_all_fields_names(exclude_fields=["tid_info"])

    search_fields = ("erp_order__code", "sku__spec_code", "sku__name")

    autocomplete_fields = (
        "erp_order",
        "sale_order_detail",
        "sku_inventory",
        "sku",
        "sales_order",
    )


@admin.register(ERPOrderDetailInventoryRelates)
class ERPOrderDetailInventoryRelatesAdmin(admin.ModelAdmin):
    list_display = ("id", "order_detail", "sku_inventory", "quantity")

    search_fields = (
        "tid_info",
        "sku_inventory__sku__name",
        "sku_inventory__sku__spec_code",
        "sku_inventory__sku__product__code",
    )

    autocomplete_fields = (
        "order_detail",
        "sku_inventory",
    )
