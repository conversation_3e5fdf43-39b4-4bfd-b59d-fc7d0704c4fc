# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.product_getCategories.param.ProductGetCategoriesParam import ProductGetCategoriesParam


class ProductGetCategoriesRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = ProductGetCategoriesParam()

    def getUrlPath(
        self,
    ):
        return "/product/getCategories"

    def getParams(
        self,
    ):
        return self.params
