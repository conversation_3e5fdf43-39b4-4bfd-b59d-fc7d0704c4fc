# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.shop_getShopCategory.param.ShopGetShopCategoryParam import ShopGetShopCategoryParam


class ShopGetShopCategoryRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = ShopGetShopCategoryParam()

    def getUrlPath(
        self,
    ):
        return "/shop/getShopCategory"

    def getParams(
        self,
    ):
        return self.params
