# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.product_setOffline.param.ProductSetOfflineParam import ProductSetOfflineParam


class ProductSetOfflineRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = ProductSetOfflineParam()

    def getUrlPath(
        self,
    ):
        return "/product/setOffline"

    def getParams(
        self,
    ):
        return self.params
