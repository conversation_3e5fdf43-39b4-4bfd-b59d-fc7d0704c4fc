# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.trace_cancelShop.param.TraceCancelShopParam import TraceCancelShopParam


class TraceCancelShopRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = TraceCancelShopParam()

    def getUrlPath(
        self,
    ):
        return "/trace/cancelShop"

    def getParams(
        self,
    ):
        return self.params
