# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.order_addresSwitchConfig.param.OrderAddresSwitchConfigParam import OrderAddresSwitchConfigParam


class OrderAddresSwitchConfigRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = OrderAddresSwitchConfigParam()

    def getUrlPath(
        self,
    ):
        return "/order/addresSwitchConfig"

    def getParams(
        self,
    ):
        return self.params
