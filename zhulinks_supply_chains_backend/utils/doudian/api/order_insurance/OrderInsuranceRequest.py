# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.order_insurance.param.OrderInsuranceParam import OrderInsuranceParam


class OrderInsuranceRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = OrderInsuranceParam()

    def getUrlPath(
        self,
    ):
        return "/order/insurance"

    def getParams(
        self,
    ):
        return self.params
