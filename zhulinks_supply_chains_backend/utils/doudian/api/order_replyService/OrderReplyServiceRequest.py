# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.order_replyService.param.OrderReplyServiceParam import OrderReplyServiceParam


class OrderReplyServiceRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = OrderReplyServiceParam()

    def getUrlPath(
        self,
    ):
        return "/order/replyService"

    def getParams(
        self,
    ):
        return self.params
