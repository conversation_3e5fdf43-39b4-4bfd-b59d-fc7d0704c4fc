# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.order_policy.param.OrderPolicyParam import OrderPolicyParam


class OrderPolicyRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = OrderPolicyParam()

    def getUrlPath(
        self,
    ):
        return "/order/policy"

    def getParams(
        self,
    ):
        return self.params
