# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.order_queryLogisticsCompanyList.param.OrderQueryLogisticsCompanyListParam import OrderQueryLogisticsCompanyListParam


class OrderQueryLogisticsCompanyListRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = OrderQueryLogisticsCompanyListParam()

    def getUrlPath(
        self,
    ):
        return "/order/queryLogisticsCompanyList"

    def getParams(
        self,
    ):
        return self.params
