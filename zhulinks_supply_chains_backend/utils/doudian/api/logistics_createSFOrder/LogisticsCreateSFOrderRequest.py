# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.logistics_createSFOrder.param.LogisticsCreateSFOrderParam import LogisticsCreateSFOrderParam


class LogisticsCreateSFOrderRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = LogisticsCreateSFOrderParam()

    def getUrlPath(
        self,
    ):
        return "/logistics/createSFOrder"

    def getParams(
        self,
    ):
        return self.params
