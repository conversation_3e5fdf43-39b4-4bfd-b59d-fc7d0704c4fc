# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.antispam_orderSend.param.AntispamOrderSendParam import AntispamOrderSendParam


class AntispamOrderSendRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = AntispamOrderSendParam()

    def getUrlPath(
        self,
    ):
        return "/antispam/orderSend"

    def getParams(
        self,
    ):
        return self.params
