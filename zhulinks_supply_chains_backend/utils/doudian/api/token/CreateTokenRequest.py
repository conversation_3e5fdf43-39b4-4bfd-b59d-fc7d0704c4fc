from utils.doudian.api.token.CreateTokenParam import CreateT<PERSON>Param
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest


class CreateTokenRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = CreateTokenParam()

    def getParams(self):
        return self.params

    def getUrlPath(self):
        return "/token/create"
