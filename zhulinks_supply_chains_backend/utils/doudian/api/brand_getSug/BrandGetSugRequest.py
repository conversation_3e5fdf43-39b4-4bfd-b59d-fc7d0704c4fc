# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.brand_getSug.param.BrandGetSugParam import BrandGetSugParam


class BrandGetSugRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = BrandGetSugParam()

    def getUrlPath(
        self,
    ):
        return "/brand/getSug"

    def getParams(
        self,
    ):
        return self.params
