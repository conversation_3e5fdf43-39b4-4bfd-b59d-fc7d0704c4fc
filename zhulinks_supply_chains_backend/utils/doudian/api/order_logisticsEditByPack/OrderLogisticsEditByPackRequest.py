# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.order_logisticsEditByPack.param.OrderLogisticsEditByPackParam import OrderLogisticsEditByPackParam


class OrderLogisticsEditByPackRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = OrderLogisticsEditByPackParam()

    def getUrlPath(
        self,
    ):
        return "/order/logisticsEditByPack"

    def getParams(
        self,
    ):
        return self.params
