# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.logistics_appendSubOrder.param.LogisticsAppendSubOrderParam import LogisticsAppendSubOrderParam


class LogisticsAppendSubOrderRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = LogisticsAppendSubOrderParam()

    def getUrlPath(
        self,
    ):
        return "/logistics/appendSubOrder"

    def getParams(
        self,
    ):
        return self.params
