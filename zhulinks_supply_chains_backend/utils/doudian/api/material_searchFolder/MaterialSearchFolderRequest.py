# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.material_searchFolder.param.MaterialSearchFolderParam import MaterialSearchFolderParam


class MaterialSearchFolderRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = MaterialSearchFolderParam()

    def getUrlPath(
        self,
    ):
        return "/material/searchFolder"

    def getParams(
        self,
    ):
        return self.params
