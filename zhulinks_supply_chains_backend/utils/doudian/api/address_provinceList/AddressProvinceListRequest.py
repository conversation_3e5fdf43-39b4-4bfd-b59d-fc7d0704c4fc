# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.address_provinceList.param.AddressProvinceListParam import AddressProvinceListParam


class AddressProvinceListRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = AddressProvinceListParam()

    def getUrlPath(
        self,
    ):
        return "/address/provinceList"

    def getParams(
        self,
    ):
        return self.params
