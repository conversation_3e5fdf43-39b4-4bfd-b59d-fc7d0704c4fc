# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.order_logisticsEdit.param.OrderLogisticsEditParam import OrderLogisticsEditParam


class OrderLogisticsEditRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = OrderLogisticsEditParam()

    def getUrlPath(
        self,
    ):
        return "/order/logisticsEdit"

    def getParams(
        self,
    ):
        return self.params
