# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.afterSale_submitEvidence.param.AfterSaleSubmitEvidenceParam import AfterSaleSubmitEvidenceParam


class AfterSaleSubmitEvidenceRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = AfterSaleSubmitEvidenceParam()

    def getUrlPath(
        self,
    ):
        return "/afterSale/submitEvidence"

    def getParams(
        self,
    ):
        return self.params
