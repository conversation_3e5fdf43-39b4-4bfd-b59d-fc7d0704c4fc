# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.afterSale_timeExtend.param.AfterSaleTimeExtendParam import AfterSaleTimeExtendParam


class AfterSaleTimeExtendRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = AfterSaleTimeExtendParam()

    def getUrlPath(
        self,
    ):
        return "/afterSale/timeExtend"

    def getParams(
        self,
    ):
        return self.params
