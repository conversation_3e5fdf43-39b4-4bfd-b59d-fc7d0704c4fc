# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.logistics_customTemplateList.param.LogisticsCustomTemplateListParam import LogisticsCustomTemplateListParam


class LogisticsCustomTemplateListRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = LogisticsCustomTemplateListParam()

    def getUrlPath(
        self,
    ):
        return "/logistics/customTemplateList"

    def getParams(
        self,
    ):
        return self.params
