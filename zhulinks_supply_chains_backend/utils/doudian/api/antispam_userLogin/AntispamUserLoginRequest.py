# auto generated file
from utils.doudian.core.DoudianOpApiRequest import DoudianOpApiRequest
from utils.doudian.api.antispam_userLogin.param.AntispamUserLoginParam import AntispamUserLoginParam


class AntispamUserLoginRequest(DoudianOpApiRequest):

    def __init__(self):
        DoudianOpApiRequest.__init__(self)
        self.params = AntispamUserLoginParam()

    def getUrlPath(
        self,
    ):
        return "/antispam/userLogin"

    def getParams(
        self,
    ):
        return self.params
