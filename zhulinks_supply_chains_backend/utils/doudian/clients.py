# -*- coding: utf-8 -*-
from common import logger
from common.basics.exceptions import APIViewException
from utils.doudian.core.AccessToken import AccessToken
from utils.doudian.core.AccessTokenBuilder import AccessTokenBuilder
from utils.doudian.core.DoudianOpConfig import DoudianOpConfig
from utils.doudian.core.DoudianOpResponse import DoudianOpResponse
from utils.doudian.sessions import SessionStorage
from utils.doudian.sessions.memorystorage import MemoryStorage


class DyTokenClient:
    def __init__(self, app_key, app_secret, storage: SessionStorage = MemoryStorage()):
        self._config = DoudianOpConfig()
        self._config.appKey = app_key
        self._config.appSecret = app_secret
        self._storage = storage

    def get_config(self):
        return self._config

    def _set_cache(self, key, val, ttl=0):
        self._storage.set(key, val, ttl)

    def get_cache(self, key):
        return self._storage.get(key)

    def _cache_key(self, shop_id, suffix: str = "map_data"):
        app_key = self._config.appKey[:12]
        return f"{app_key}_{shop_id}_{suffix}"

    def get_token_by_shop_id(self, shop_id) -> AccessToken:
        return self._get_token(shop_id)

    def get_token_by_shop_id_and_code(self, shop_id, code) -> AccessToken:
        return self._get_token(shop_id, code)

    def get_token_by_code(self, code) -> AccessToken:
        return self._get_token(code)

    def _get_token(self, shop_id=None, code=None) -> AccessToken:
        key = self._cache_key(shop_id)
        refresh_key = self._cache_key(shop_id, suffix="refresh_token")

        cache_value = self.get_cache(key)
        refresh_cache_value = self.get_cache(refresh_key)

        if cache_value:
            return self._construct_access_token_from_cache(cache_value)

        if refresh_cache_value:
            access_token = self._refresh_access_token(refresh_cache_value)
            if access_token.accessTokenResp.code != 10000:
                logger.warning(f"刷新access_token失败, shop_id:{shop_id}, code:{code},{access_token.accessTokenResp.__dict__}")
                # 刷新时效，删除refresh_token
                self._storage.delete(refresh_key)
                raise APIViewException(err_message=f"店铺{shop_id}刷新授权失败,请联系管理员")
        else:
            access_token = self._build_new_access_token(shop_id, code)
            if access_token.accessTokenResp.code != 10000:
                logger.warning(f"获取access_token失败, shop_id:{shop_id}, code:{code},{access_token.accessTokenResp.__dict__}")
                raise APIViewException(err_message=f"店铺{shop_id}授权失败,请联系管理员")
        self._update_cache_with_token(key, refresh_key, access_token)
        return access_token

    @staticmethod
    def _construct_access_token_from_cache(cache_value) -> AccessToken:
        opResp = DoudianOpResponse()
        opResp.__dict__ = cache_value
        return AccessToken(opResp)

    def _refresh_access_token(self, refresh_token) -> AccessToken:
        return AccessTokenBuilder.refreshToken(refresh_token, config=self._config)

    def _build_new_access_token(self, shop_id, code=None) -> AccessToken:
        if code and shop_id:
            return AccessTokenBuilder.buildTestTokenByShopIdCode(code, shop_id, self._config)
        if code and not shop_id:
            return AccessTokenBuilder.buildTokenByCode(code, self._config)

        return AccessTokenBuilder.buildTokenByShopId(shop_id, self._config)

    def _update_cache_with_token(self, key, refresh_key, access_token):
        map_data = access_token.accessTokenResp.__dict__
        expires = access_token.getExpiresIn()

        if expires:
            self._set_cache(key, map_data, ttl=expires)

        refresh_token = access_token.getRefreshToken()
        if expires:
            self._set_cache(refresh_key, refresh_token, ttl=expires * 2 - 60 * 60)

    def clear_caches(self, shop_id):
        """
        清空缓存
        :param shop_id:
        :return:
        """
        access_key = self._cache_key(shop_id)
        refresh_key = self._cache_key(shop_id, suffix="refresh_token")

        self._storage.delete(access_key)
        self._storage.delete(refresh_key)
