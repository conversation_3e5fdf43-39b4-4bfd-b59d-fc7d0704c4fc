# -*- coding: utf-8 -*-
from typing import Dict

from ..core.base import BaseService


class OrderService(BaseService):
    def __init__(self, client):
        super().__init__(client)

    def get_order_list(
        self,
        start_time: int,
        end_time: int,
        page: int = 1,
        page_size: int = 50,
        time_type: int = 1,
        order_type: int = 0,
        order_status: int = 0,
    ) -> Dict:
        """
        获取订单列表
        start_time/end_time以秒为单位
        :param order_status: 订单状态，0全部 1已下单待付款 2已支付处理中 3清关中 4待发货 5部分发货 6待收货 7已完成 8已关闭 9已取消 10换货申请中
        :param order_type:订单类型，0/null 全部 1 现货 normal 2 定金预售 3 全款预售(废弃) 4 全款预售(新) 5 换货补发
        :param time_type: 时间类型 	startTime/endTime对应的时间类型，1 创建时间 限制 end-start<=24h、2 更新时间 限制 end-start<=30min 倒序拉取 最后一页到第一页
        :param start_time: 订单范围起点
        :param end_time: 订单范围终点
        :param page: 页码，限制100
        :param page_size: 页码总数，限制100
        :return:
        """

        if end_time <= start_time:
            raise ValueError("end_time must be greater than start_time")

        if time_type == 1 and (end_time - start_time) > 24 * 60 * 60:
            raise ValueError("time range must be less than 24h")

        if time_type == 2 and (end_time - start_time) > 60 * 30:
            raise ValueError("time range must be less than 30min")

        return self._client.post(
            "order.getOrderList",
            {
                "startTime": start_time,
                "endTime": end_time,
                "timeType": time_type,
                "orderType": order_type,
                "orderStatus": order_status,
                "pageNo": page,
                "pageSize": page_size,
            },
        )

    def get_order_detail(self, order_id: str) -> Dict:
        """
        获取订单详情信息
        :param order_id:
        :return:
        """
        return self._client.post(
            "order.getOrderDetail",
            {
                "orderId": order_id,
            },
        )

    def get_order_receiver_info(
        self,
        order_id: str,
        open_address_id: str,
        is_return: bool = False,
    ) -> Dict:
        """
        获取收件人详情信息
        :param order_id: 订单id
        :param open_address_id: 订单详情获取的收件人信息id
        :param is_return: 是否为换货单
        :return:
        """
        return self._client.post(
            "order.getOrderReceiverInfo",
            {
                "receiverQueries": [
                    {
                        "orderId": order_id,
                        "openAddressId": open_address_id,
                    }
                ],
                "isReturn": is_return,
            },
        )
