# -*- coding: utf-8 -*-


import requests

from utils.xhs.core.base import XHSBaseClient
from utils.xhs.core.execptions import APIRequestError


class ProductService:
    def __init__(self, auth_manager: XHSBaseClient):
        self.auth_manager = auth_manager

    def create_item(self, item_name):
        """创建商品"""

        payload = self.auth_manager.build_payload(
            "product.createItem",
            {"itemName": item_name},
        )
        response = requests.post(self.auth_manager.base_url, json=payload)

        if response.status_code != 200:
            raise APIRequestError(f"HTTP Error: {response.status_code}")

        result = response.json()
        if not result.get("success"):
            raise APIRequestError(f"Error: {result.get('error_code')}, {result.get('data')}")
        return result["data"]
