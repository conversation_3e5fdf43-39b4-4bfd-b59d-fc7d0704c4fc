# -*- coding: utf-8 -*-
from hashlib import md5


def verify_sign(uri: str, app_key: str, app_secret: str, params: dict, sign: str):
    """
    校验请求的sign值

    :param uri: 订阅的uri地址
    :param app_key:
    :param app_secret:
    :param params: 请求参数、timestamp
    :param sign: request headers signature
    :return:
    """

    # 排序
    sorted_params_str = "&".join([f"{k}={params[k]}" for k in sorted(params.keys())])

    encrypt_str = f"{uri}?{sorted_params_str}{app_secret}"

    encrypt_sign = md5(encrypt_str.encode()).hexdigest()

    return encrypt_sign == sign
