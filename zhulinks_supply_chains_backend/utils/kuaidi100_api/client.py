import hashlib
import json
import time

import requests

from utils import logger
from utils.kuaidi100_api.exceptions import KuaiDi100APIError


class KuaiDi100:
    def __init__(self, key=None, customer=None):
        """
        初始化 KuaiDi100 API 客户端。
        :param key: 客户授权 key（可选）
        :param customer: 查询公司编号（可选）
        """
        self.key = key or "uTPgCWBm8152"
        self.customer = customer or "505F19B7761E827F06D45CF22C7592D4"
        self.secret = "8ac84e1a752f4acd88bbad618008d44b"

    def generate_sign(self, param_str):
        """
        生成签名，用于身份验证。
        :param param_str: JSON 参数字符串
        :return: 加密后的签名
        """
        temp_sign = param_str + self.key + self.customer
        return hashlib.md5(temp_sign.encode()).hexdigest().upper()

    def electronic_manifest_generate_param(self, param, t):
        """
        生成电子面单接口的签名。
        :param param: 参数字符串
        :param t: 时间戳
        :return: 签名字符串
        """
        temp_sign = param + t + self.key + self.secret
        return hashlib.md5(temp_sign.encode()).hexdigest().upper()

    def send_post_request(self, url, params):
        """
        通用 POST 请求方法。
        :param url: API URL
        :param params: 请求参数
        :return: JSON 响应数据
        """
        try:
            response = requests.post(url, data=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            return data
        except requests.exceptions.RequestException as e:
            print(KuaiDi100APIError(f"Request failed: {e}"))
            # print(f"Request failed: {e}")
            return None

    def real_time_logistics_tracking(self, com, num, phone="", ship_from="", ship_to=""):
        """
        实时查询物流轨迹。
        :param com: 快递公司编码
        :param num: 快递单号
        :param phone: 收件人/寄件人手机号
        :param ship_from: 出发地城市（可选）
        :param ship_to: 目的地城市（可选）
        :return: JSON 格式的响应数据
        """
        url = "https://poll.kuaidi100.com/poll/query.do"
        param = {
            "com": com,
            "num": num,
            "phone": phone,
            "from": ship_from,
            "to": ship_to,
            "resultv2": 4,
            "show": "0",
            "order": "desc",
        }
        param_str = json.dumps(param)
        sign = self.generate_sign(param_str)
        request_data = {"customer": self.customer, "param": param_str, "sign": sign}
        return self.send_post_request(url, request_data)

    def electronic_manifest_submission(
        self, print_type, partner_id, kuaidi_com, cargo, temp_id, rec_man_name, rec_man_mobile, rec_man_addr, send_man_name, send_man_mobile, send_man_addr, ocr_include=None
    ):
        """
        提交电子面单信息到快递100 API。
        """
        if ocr_include is None:
            ocr_include = ["barcode", "receiver", "sender"]

        url = "https://api.kuaidi100.com/label/order"
        t = str(int(round(time.time() * 1000)))
        param = {
            "printType": print_type,
            "partnerId": partner_id,
            "kuaidicom": kuaidi_com,
            "recMan": {"name": rec_man_name, "mobile": rec_man_mobile, "printAddr": rec_man_addr},
            "sendMan": {"name": send_man_name, "mobile": send_man_mobile, "printAddr": send_man_addr},
            "cargo": cargo,
            "count": 1,
            "tempId": temp_id,
            "reorder": True,
            "needOcr": False,
            "ocrInclude": ocr_include,
            "sumUp": False,
            "elecToOfficial": {"callDoorPickUp": False},
        }
        param_str = json.dumps(param)
        sign = self.electronic_manifest_generate_param(param_str, t)
        params = {"key": self.key, "sign": sign, "method": "order", "t": t, "param": param_str}
        return self.send_post_request(url, params)

    def reprint_electronic_waybill(self, task_id):
        """
        重新打印电子面单。
        """
        url = "https://api.kuaidi100.com/label/order"
        t = str(int(round(time.time() * 1000)))
        param = {"taskId": task_id}
        param_str = json.dumps(param)
        sign = self.electronic_manifest_generate_param(param_str, t)
        params = {"key": self.key, "sign": sign, "method": "printOld", "t": t, "param": param_str}
        return self.send_post_request(url, params)

    def cancel_electronic_waybill(self, partner_id, kuaidicom, kuaidinum, reason):
        """
        取消电子面单。
        """
        url = "https://api.kuaidi100.com/label/order"
        t = str(int(round(time.time() * 1000)))
        param = {
            "partnerId": partner_id,
            "kuaidicom": kuaidicom,
            "kuaidinum": kuaidinum,
            "reason": reason,
        }
        param_str = json.dumps(param)
        sign = self.electronic_manifest_generate_param(param_str, t)
        params = {"key": self.key, "sign": sign, "method": "cancel", "t": t, "param": param_str}
        return self.send_post_request(url, params)

    def logistics_number_identification(self, num):
        """
        查询快递信息

        参数:
        - num: 快递单号 (str)

        返回:
        - 接口返回的 JSON 数据 (dict)
        """
        # 接口地址
        url = "http://www.kuaidi100.com/autonumber/auto"

        # 请求参数
        params = {"num": num, "key": self.key}  # 快递单号  # 授权码

        # 请求头
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        # 记录请求日志
        logger.info(f"请求快递信息: 单号={num}, 接口地址={url}, 参数={params}")

        try:
            # 发送 GET 请求
            response = requests.get(url, params=params, headers=headers, timeout=10)
            logger.info(f"请求成功: 状态码={response.status_code}")

            # 检查请求是否成功
            response.raise_for_status()

            # 解析 JSON 数据
            result = response.json()
            logger.info(f"接口返回数据: {result}")

            # 返回 JSON 数据
            return result

        except requests.exceptions.RequestException as e:
            # 处理请求异常
            logger.error(f"请求异常: {e}")
            return None
        except ValueError as e:
            # 处理 JSON 解析异常
            logger.error(f"JSON 解析异常: {e}")
            return None
        except Exception as e:
            # 处理其他未知异常
            logger.error(f"未知异常: {e}")
            return None

