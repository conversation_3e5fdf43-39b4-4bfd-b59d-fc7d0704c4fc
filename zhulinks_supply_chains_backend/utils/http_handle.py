# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-06-01 22:29:21
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-12-21 17:50:16


import calendar
import json
import traceback
import urllib
import warnings
from datetime import date, datetime
from functools import reduce, wraps
from operator import or_

from common.basics.exceptions import APIViewException
from django.core.cache import cache
from django.core.exceptions import FieldError
from django.core.paginator import Page, Paginator, UnorderedObjectListWarning
from django.db.models import F, Model, Q, QuerySet
from django.db.models.base import ModelBase
from django.utils import timezone
from rest_framework import status as http_status
from rest_framework.exceptions import ErrorDetail
from rest_framework.request import Request
from rest_framework.response import Response

from utils import logger

from .http_return_message import MSG


def user_type_required(*allowed_user_types):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # 用户不符合指定类型
            if request.current_user_type not in allowed_user_types:
                return IResponse(code=403, message="no permission to operate")
            # 用户类型符合，执行视图函数
            return view_func(request, *args, **kwargs)

        return _wrapped_view

    return decorator


class UserTypeMixin:
    """
    审计日志
    需要在 APIView 上新增类属性：
    resource_id_field: 指定一个用于获取资源主体的字段名
    比如：创建商品，虽然包含商品和商品sku，但只需指定"product_id"字段即可

    resource_name: 指定操作的资源名
    比如：创建商品，只需要指定为"商品"即可"

    举例：
    class ProductView(AuditLogAPIViewMixin, APIView):
        resource_id_field = "product_id"
        resource_name = "运营商商品"

    """

    @classmethod
    def as_view(cls, *args, **kwargs):
        view = super(UserTypeMixin, cls).as_view(*args, **kwargs)
        return user_type_required(cls.allowed_user_types)(view)


class IResponse(Response):
    """
    return customize response data
    """

    def __init__(self, data=None, message=None, code=None, status=None, template_name=None, headers=None, exception=False, content_type=None, *args, **kwargs):
        if status is None:
            status = http_status.HTTP_200_OK

        response_data = {
            "success": True if status < 500 else False,
            "code": code if code else status,
            "errorMessage": MSG.get(message, message),
            "data": data,
            **kwargs,
        }
        super(IResponse, self).__init__(
            data=response_data,
            status=status,
            template_name=template_name,
            headers=headers,
            exception=exception,
            content_type=content_type,
        )


def verification_code_is_valid(
    request,
    send_type,
    mobile: str = None,
    verification_code: str | int = None,
):
    """
    校验短信验证码
    Args:
        request (_type_): _description_
        send_type: 发送类型：
            1.供应商注册：SP-RG
            2.供应商登录: SP-LG
            3.运营商注册：OP-RG
            4.运营商登录: OP-LG
            5.分销商注册：DB-RG
            6.分销商登录: DB-LG
            7.供应商忘记密码: SP-FG
            8.运营商忘记密码: OP-FG
            9.分销忘记密码: DB-FG
            10.供应商修改密码: SP-UP
            11.运营商修改密码: OP-UP
            12.分销商修改密码: DB-UP
            13.绑定微信: DB-OAUTH

    Returns:
        _type_: _description_
        :param verification_code:
        :param mobile:
    """
    # return True
    country_code = request.data.get("country_code", "86")
    mobile = mobile or request.data.get("mobile")
    verification_code = verification_code or request.data.get("verification_code")
    if not mobile:
        raise APIViewException(err_message="请填写手机号信息")

    if not verification_code:
        raise APIViewException(err_message="请正确填写验证码")

    assert send_type in (
        "SP-RG",
        "SP-LG",
        "OP-RG",
        "OP-LG",
        "DB-RG",
        "DB-LG",
        "SP-FG",
        "OP-FG",
        "DB-FG",
        "SP-UP",
        "OP-UP",
        "DB-UP",
        "DB-OAUTH",
    ), "send_type invalid"

    send_event = f"{country_code}{mobile}_{send_type}"
    raw_code = cache.get(f"mobile:{send_event}:vericode")
    if raw_code and raw_code == verification_code:
        return True
    logger.info(f"mobile: {country_code}{mobile}, raw_code: {raw_code}, verification_code: {verification_code}")
    return False


class FieldsError(Exception):
    """
    error_value:
        example: company.errors
    """

    def __init__(self, error_value):
        self.error = []
        if isinstance(error_value, list):
            for val in error_value:
                for k, v in val.items():
                    if k == "non_field_errors":
                        self.error.append(MSG.get(str(v[0]), str(v[0])).replace(" ", ""))
                    else:
                        self.error.append(str(k) + ": " + MSG.get(str(v[0]), str(v[0])).replace(" ", ""))
        elif isinstance(error_value, dict):
            for k, v in error_value.items():
                if isinstance(v, list):
                    for vi in v:
                        if isinstance(vi, dict):
                            for vi_k, vi_v in vi.items():
                                self.error.append(f"{k}.{vi_k}: {vi_v[0]}")
                        elif isinstance(vi, ErrorDetail):
                            self.error.append(str(k) + ": " + MSG.get(str(v[0]), str(v[0])).replace(" ", ""))
                if k == "non_field_errors":
                    self.error.append(MSG.get(str(v[0]), str(v[0])).replace(" ", ""))
                else:
                    if isinstance(v, dict):
                        for _k, _v in v.items():
                            if isinstance(_v, dict):
                                for _k1, _v1 in _v.items():
                                    self.error.append(f"{str(k)}.{str(_k1)}" + ": " + MSG.get(str(_v1[0]), str(_v1[0])).replace(" ", ""))
                            else:
                                self.error.append(f"{str(k)}.{str(_k)}" + ": " + MSG.get(str(_v[0]), str(_v[0])).replace(" ", ""))
                    else:
                        self.error.append(str(k) + ": " + MSG.get(str(v[0]), str(v[0])).replace(" ", ""))
        else:
            self.error = error_value

        self.error = list(set(self.error))

    def __str__(self) -> str:
        return ";".join(self.error)


def clean_form(data_model, data):
    """
    排除前端来的脏数据
    """
    new_data = {}
    for k, v in data.items():
        if k in data_model.__dict__:
            new_data[k] = v
    return new_data


def convert_to_ware_time(native_time: str, format_str="%Y-%m-%d %H:%M:%S"):
    tz = timezone.get_current_timezone()
    res = timezone.make_aware(datetime.strptime(native_time, format_str), tz)
    return res


def get_bill_start_and_end(this_moment):
    """
    获取7日账单的开始和结束日期
    this_moment: ware_time
    """
    range_key = [8, 15, 22, 29, 32]
    date_map = {
        8: (1, 7),
        15: (8, 14),
        22: (15, 21),
        29: (22, 28),
        32: (29, 31),
    }
    this_day = this_moment.day
    bill_range = None
    for i in range_key:
        if this_day < i:
            bill_range = date_map.get(i)
            break
    if bill_range == (29, 31):
        weekDay, monthCountDay = calendar.monthrange(this_moment.year, this_moment.month)
        bill_range = (29, monthCountDay)
    bill_start = date(this_moment.year, this_moment.month, bill_range[0])
    bill_end = date(this_moment.year, this_moment.month, bill_range[1])
    return bill_start, bill_end


def get_range_query(min_field_name, max_field_name, range_value):
    """
    处理范围数据的查询
    比如：查询成本价 cost_price 在 [500, 1000]的数据，在商品列表中
    成本价有2个字段：min_cost_price 和 max_cost_price，则参数如下：
    min_field_name: min_cost_price
    max_field_name: max_cost_price
    range_value: [500, 1000]

    """
    assert isinstance(range_value, list) and len(range_value) == 2, "range_value should be a array[2]"
    and_Q = Q()
    or_Q = Q()
    if range_value[0] is None and range_value[1] is not None:
        and_Q.add(Q(**{f"{min_field_name}__lte": range_value[1]}), Q.AND)
    elif range_value[0] is not None and range_value[1] is None:
        and_Q.add(Q(**{f"{max_field_name}__gte": range_value[0]}), Q.AND)
    elif range_value[0] is not None and range_value[1] is not None:
        or_Q.add(Q(**{f"{min_field_name}__range": range_value}), Q.OR)
        or_Q.add(Q(**{f"{max_field_name}__range": range_value}), Q.OR)
        tmp_Q = Q(**{f"{min_field_name}__lte": range_value[0]}).add(Q(**{f"{max_field_name}__gte": range_value[1]}), Q.AND)
        or_Q.add(tmp_Q, Q.OR)
    if or_Q:
        and_Q.add(or_Q, Q.AND)

    return and_Q


def handle_range_filter(params_dict):
    """
    对范围筛选条件进行处理：
    1.日期数据处理
    2.只传一个值情况处理
    3.产品成本价和零售价价范围处理
    """
    date_fields = (
        "create_date__range",
        "update_date__range",
        "order_date__range",
        "as_date__range",
        "start_time__range",
        "operation_time__range",
    )
    product_price_fields = ("__cost_price__range", "__retail_price__range", "__history_price__range", "__distributor_market_price__range")
    start_end_fileds = ("__live_date__range",)
    and_Q = Q()
    or_Q = Q()
    for k, v in list(params_dict.items()):
        if k.find("__range") != -1:
            if not isinstance(v, list):
                v = json.loads(v)
            if len(v) == 0:
                continue
            field_name = k.split("__")[0]
            if k in product_price_fields:
                field_name = k.split("__")[1]
            if k in start_end_fileds:
                field_name = k.split("__")[1]

            params_dict.pop(k)
            assert isinstance(v, list), "field_range must be a array"
            assert len(v) == 2, "length of field_range must be 2"
            if v[0] is None and v[1] is not None:
                if k in product_price_fields:
                    and_Q.add(Q(**{f"min_{field_name}__lte": v[1]}), Q.AND)
                elif k in start_end_fileds:
                    and_Q.add(Q(**{f"{field_name}_start__lte": v[1]}), Q.AND)
                else:
                    and_Q.add(Q(**{f"{field_name}__lte": convert_to_ware_time(v[1]) if k in date_fields else v[1]}), Q.AND)

            elif v[0] is not None and v[1] is None:
                if k in product_price_fields:
                    and_Q.add(Q(**{f"max_{field_name}__gte": v[0]}), Q.AND)
                elif k in start_end_fileds:
                    and_Q.add(Q(**{f"{field_name}_end__gte": v[0]}), Q.AND)
                else:
                    and_Q.add(Q(**{f"{field_name}__gte": convert_to_ware_time(v[0]) if k in date_fields else v[0]}), Q.AND)

            elif v[0] is not None and v[1] is not None:
                if k in product_price_fields:
                    or_Q.add(Q(**{f"min_{field_name}__range": v}), Q.OR)
                    or_Q.add(Q(**{f"max_{field_name}__range": v}), Q.OR)
                    tmp_Q = Q(**{f"min_{field_name}__lte": v[0]}).add(Q(**{f"max_{field_name}__gte": v[1]}), Q.AND)
                    or_Q.add(tmp_Q, Q.OR)
                elif k in start_end_fileds:
                    or_Q.add(Q(**{f"{field_name}_start__range": v}), Q.OR)
                    or_Q.add(Q(**{f"{field_name}_end__range": v}), Q.OR)
                    tmp_Q = Q(**{f"{field_name}_start__lte": v[0]}).add(Q(**{f"{field_name}_end__gte": v[1]}), Q.AND)
                    or_Q.add(tmp_Q, Q.OR)
                elif k in date_fields:
                    params_dict[k] = [convert_to_ware_time(i) for i in v]
                else:
                    params_dict[k] = v

            else:
                raise ValueError("range value can not be all None")
    if or_Q:
        and_Q.add(or_Q, Q.AND)

    return and_Q


def custom_filter(
    raw_params,
    model_object,
    array_fields=None,
    like_fields=None,
    hybrid_fields=None,
    subset_fields=None,
    force_orders=True,
    query_Q=None,
    **extra_dict,
):
    """
    筛选器
    Args:
        raw_params (_type_): request.query_params.copy()
        model_object (_type_): 数据模型 或者 queryset, 可以在外面先处理
        array_fields (_type_): 数组类型字段列表
        like_fields (_type_): 模糊查询字段字段列表
        hybrid_fields (_type_): 多个字段混合查询字段列表
        subset_fields: 查询子表字段： {"字段名": "子表名"}
        force_orders: 明确是否需要排序，默认以-id强制排序

    Returns:
        _type_: Page Objects, re_data
    """
    if isinstance(model_object, ModelBase):
        model_object_qs = model_object.objects.all()
    else:
        model_object_qs = model_object

    and_Q = Q()
    # raw_params = request.query_params.copy()
    params = {}
    for k, v in raw_params.items():
        v = urllib.parse.unquote(v)
        if k.find("__range") != -1 or k.find("__in") != -1:
            v = json.loads(v)
        if len(v) != 0:
            params[k] = v

    page = params.pop("page") if params.get("page") else 1
    page_size = params.pop("page_size") if params.get("page_size") else 20

    if subset_fields:
        for k, v in subset_fields.items():
            if params.get(k):
                params_v = params.pop(k)
                params[f"{v}__{k}"] = params_v

    # handle array type data, exclude range field
    if array_fields:
        assert isinstance(array_fields, list), "array_fields must be a list"
        for array_field in array_fields:
            if params.get(array_field):
                if not isinstance(params[array_field], list):
                    value = json.loads(params[array_field])
                    if len(value) == 0:
                        params.pop(array_field)
                    else:
                        # 添加category二级查询
                        if array_field in ["category", "external_category"]:
                            params[f"{array_field}__contains"] = value
                            params.pop(array_field)
                            continue

                        params[array_field] = value

    # handle like field
    if like_fields:
        assert isinstance(like_fields, list), "like_fields must be a list"
        for like_field in like_fields:
            if params.get(like_field):
                params[f"{like_field}__icontains"] = params.pop(like_field)

    hybrid_query_set = []
    if hybrid_fields:
        assert isinstance(hybrid_fields, list), "combined_fields must be a list"
        if params.get("hybrid_search"):
            hybrid = params.pop("hybrid_search")
            for hybrid_field in hybrid_fields:
                if hybrid_field.startswith("__"):
                    # 去掉以J结尾的, 带有集采标识的在前端展示货号会+J
                    _hybrid_field = hybrid_field[2:]
                    _hybrid = hybrid[:-1] if hybrid[-1] == "J" else hybrid
                    if _hybrid_field in like_fields:
                        hybrid_query_set.append(Q(**{f"{_hybrid_field}__icontains": _hybrid}))
                    else:
                        hybrid_query_set.append(Q(**{_hybrid_field: _hybrid}))
                else:
                    if hybrid_field in like_fields:
                        hybrid_query_set.append(Q(**{f"{hybrid_field}__icontains": hybrid}))
                    else:
                        hybrid_query_set.append(Q(**{hybrid_field: hybrid}))
    if hybrid_query_set:
        and_Q.add(reduce(or_, hybrid_query_set), Q.AND)

    # handle orders
    orders = params.get("orders")
    if orders:
        orders = json.loads(params.pop("orders"))
    else:
        if force_orders:
            orders = ["-id"]

    # handle date field
    for date_field in ("create_date", "update_date"):
        if params.get(date_field):
            params[date_field] = convert_to_ware_time(params[date_field])

    # handle field range
    range_q = handle_range_filter(params)
    if range_q:
        and_Q.add(range_q, Q.AND)

    if query_Q:
        and_Q.add(query_Q, Q.AND)

    # 是否需要去重（优化counts查询）
    need_distinct = True

    # if not hybrid_query_set:
    #     need_distinct = False

    if extra_dict.get("need_distinct") is not None:
        need_distinct = extra_dict.pop("need_distinct")

    params.update(**extra_dict)
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=UnorderedObjectListWarning)
        try:
            if and_Q:
                # model_object_qs = model_object.objects.filter(**params).filter(and_Q).order_by(*orders)
                model_object_qs = model_object_qs.filter(**params).filter(and_Q)
                if need_distinct:
                    model_object_qs = model_object_qs.distinct()
            else:
                # model_object_qs = model_object.objects.filter(**params).order_by(*orders)
                model_object_qs = model_object_qs.filter(**params)
                if need_distinct:
                    model_object_qs = model_object_qs.distinct()

            if orders:
                order_by_query = []

                for order_field in orders:
                    if "-" in order_field:
                        _field_name = order_field.replace("-", "")
                        if _field_name == "id":
                            order_by_query.append(order_field)
                        else:
                            order_by_query.append(F(_field_name).desc(nulls_last=True))
                    else:
                        order_by_query.append(order_field)
                try:
                    model_object_qs = model_object_qs.order_by(*order_by_query)
                except Exception:
                    logger.warning(f"custom filter排序：{orders}")
                    raise APIViewException(err_message="暂不支持该字段排序")
        except FieldError:
            logger.warning(traceback.format_exc())
            raise Exception("系统暂不支持该查询,请联系管理员")
        except ValueError as e:
            logger.warning(e)
            return None, {"count": 0, "total_pages": 0, "current_page": 0, "data": []}, None

        # logger.info(f"model_object_qs: {model_object_qs.query}")
        paginator = Paginator(model_object_qs, page_size)
        page_model_objects = paginator.page(page)

    re_data = {}
    re_data["count"] = page_model_objects.paginator.count
    re_data["total_pages"] = page_model_objects.paginator.num_pages
    re_data["current_page"] = page_model_objects.number
    re_data["data"] = []

    return page_model_objects, re_data, model_object_qs


def custom_django_filter(
    request: Request,
    target: QuerySet | Model,
    filter_set=None,
    iserializer=None,
    order_fields=None,
    need_serialize: bool = True,
    force_order: bool = True,
    need_paginator: bool = True,
    order_map=None,
    paginator=Paginator,
    *args,
    **kwargs,
) -> (dict, Page, QuerySet):
    """
    使用django-filter 公用方法

    2025-01-22：在serializer增加了content={"request": request}

    :param paginator:
    :param order_map: 排序映射
    :param need_paginator: 有部分不需要分页
    :param request: drf Request
    :param target: Model或者经过筛选的queryset
    :param filter_set: django-filter 过滤器
    :param iserializer: 自定义的序列化器
    :param order_fields: 自定义排序字段
    :param need_serialize: 是否需要序列化
    :param force_order: 是否强制排序
    :return: (字典, Page数据, filter_queryset)
    """

    if order_map is None:
        order_map = {}
    if need_serialize and not iserializer:
        raise ValueError("must have Serializer while need_serialize is True")

    params = request.query_params.copy()
    if params.get("orders"):
        try:
            order_fields = json.loads(params.pop("orders")[0])
            # 映射表
            order_fields = [
                field.replace(field.replace("-", ""), order_map.get(field.replace("-", ""))) if order_map.get(field.replace("-", "")) else field for field in order_fields
            ]
        except Exception as e:
            print(e)
            raise ValueError("invalid order fields")
    else:
        if force_order:
            order_fields = ["-id"]

    if not issubclass(target.__class__, (ModelBase, QuerySet)):
        raise ValueError("invalid target type")

    if issubclass(target.__class__, ModelBase):
        target = target.objects.all()

    # 分页信息初始化
    page = params.pop("page")[0] if params.get("page") else 1
    page_size = params.pop("page_size")[0] if params.get("page_size") else 20

    if int(page_size) > 1000:
        page_size = 1000

    # 筛选
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=RuntimeWarning)
        if filter_set:
            qs = filter_set(params, queryset=target, request=request).qs
        else:
            qs = target

    # 排序, 由于filter_set有权重排序，所以先排
    if order_fields:
        order_by_query = []
        for order_field in order_fields:
            if "-" in order_field:
                _field_name = order_field.replace("-", "")
                if _field_name == "id":
                    order_by_query.append(order_field)
                else:
                    order_by_query.append(F(_field_name).desc(nulls_last=True))
            else:
                order_by_query.append(order_field)
        try:
            qs = qs.order_by(*order_by_query)
        except Exception:
            logger.warning(f"custom django filter排序：{order_fields}")
            raise APIViewException(err_message="暂不支持该字段排序")

    if not need_paginator:
        return None, None, qs

    # 分页数据
    paginator = paginator(qs, page_size)
    page_objects = paginator.page(page)
    # 响应数据
    re_data = {}
    re_data["count"] = page_objects.paginator.count
    re_data["total_pages"] = page_objects.paginator.num_pages
    re_data["current_page"] = page_objects.number
    if need_serialize:
        context = {
            "request": request,
        }
        if kwargs.get("ser_context"):
            context.update(kwargs.get("ser_context"))

        re_data["data"] = iserializer(instance=page_objects, many=True, context=context).data
    else:
        re_data["data"] = []
    return re_data, page_objects, qs


def build_rule_tree(rules):
    tree = []
    lookup = {}

    for rule in rules:
        rule_dict = {
            "id": rule.id,
            "name": rule.name,
            "parent": rule.parent.id if rule.parent else None,
            "is_menu": rule.is_menu,
            "front_path": rule.front_path,
            "miniprogram_path": rule.miniprogram_path,
            "icon": rule.icon,
            "condition": rule.condition,
            "method": rule.method,
            "state": rule.state,
            "order": rule.order,
            "create_date": rule.create_date,
            "update_date": rule.update_date,
            "subs": [],
        }

        lookup[rule.id] = rule_dict

    for rule in rules:
        if rule.parent_id and rule.parent_id in lookup:
            lookup[rule.parent_id]["subs"].append(lookup[rule.id])
        else:
            tree.append(lookup[rule.id])

    tree.sort(key=lambda x: x["order"])

    def sort_subs_by_order(rule):
        rule["subs"] = sorted(rule["subs"], key=lambda x: x["order"])
        for sub_rule in rule["subs"]:
            sort_subs_by_order(sub_rule)

    for tr in tree:
        sort_subs_by_order(tr)
    return tree


# 空列表返回
EmptyListResponse: dict = {
    "count": 0,
    "total_pages": 1,
    "current_page": 1,
    "data": [],
}
