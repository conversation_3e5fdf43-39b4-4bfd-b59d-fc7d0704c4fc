from collections import OrderedDict

from rest_framework import mixins
from rest_framework import viewsets
from rest_framework.status import HTTP_200_OK
from rest_framework.utils.serializer_helpers import ReturnList
from rest_framework.viewsets import GenericViewSet

from utils.json_response import JsonResponse


class BaseMysqlModelExportViewSet(mixins.ListModelMixin, GenericViewSet):
    def list(self, request, code=200, count=0, status=None, *args, **kwargs):
        if not status:
            status = HTTP_200_OK
        response = super().list(request, *args, **kwargs)

        if "count" in response.data:
            count = response.data["count"]
        results = list()
        if isinstance(response.data, ReturnList):
            results = response.data
        elif isinstance(response.data, OrderedDict):
            results = response.data["results"]
        elif isinstance(response.data, dict):
            results = response.data.get("results", "")

        paginator = self.paginator
        if paginator and hasattr(paginator, "page"):
            total_pages = paginator.page.paginator.num_pages
            current_page = paginator.page.number
        else:
            total_pages = 1
            current_page = 1

        return JsonResponse(results, code=code, count=count, status=status, total_pages=total_pages, current_page=current_page)


class BaseMysqlModelViewSet(viewsets.ModelViewSet):
    ordering_fields = ["-create_time"]

    def list(self, request, code=200, count=0, status=None, *args, **kwargs):
        if not status:
            status = HTTP_200_OK
        response = super().list(request, *args, **kwargs)

        # 获取 count 和 results
        if "count" in response.data:
            count = response.data["count"]
        results = list()
        if isinstance(response.data, ReturnList):
            results = response.data
            count = len(results)
        elif isinstance(response.data, OrderedDict):
            results = response.data["results"]
        elif isinstance(response.data, dict):
            results = response.data.get("results", "")

        # 获取分页信息
        paginator = self.paginator
        if paginator and hasattr(paginator, "page"):
            total_pages = paginator.page.paginator.num_pages
            current_page = paginator.page.number
        else:
            total_pages = 1
            current_page = 1

        # 返回带有分页信息的 JsonResponse
        return JsonResponse(results, code=code, count=count, status=status, total_pages=total_pages, current_page=current_page)

    def create(self, request, code=200, count=0, status=None, *args, **kwargs):
        if not status:
            status = HTTP_200_OK
        response = super().create(request, *args, **kwargs)
        return JsonResponse(response.data, code=code, message="success", status=status)

    def update(self, request, code=200, count=1, status=None, *args, **kwargs):
        if not status:
            status = HTTP_200_OK
        response = super().update(request, *args, **kwargs)
        return JsonResponse(response.data, code=code, status=status)

    def destroy(self, request, code=200, count=0, status=None, *args, **kwargs):
        if not status:
            status = HTTP_200_OK
        response = super().destroy(request, *args, **kwargs)
        return JsonResponse(response.data, code=code, status=status)

    def retrieve(self, request, code=200, count=0, status=None, *args, **kwargs):
        if not status:
            status = HTTP_200_OK
        response = super().retrieve(request, *args, **kwargs)
        return JsonResponse(response.data, code=code, status=status)
