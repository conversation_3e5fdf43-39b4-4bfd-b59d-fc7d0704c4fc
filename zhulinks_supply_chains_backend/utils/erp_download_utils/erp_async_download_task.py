# -*- coding: utf-8 -*-
import time
from datetime import datetime, timedelta

from common import logger
from django.http import HttpRequest
from django.utils import timezone
from rest_framework.request import Request
from utils.oss_v2 import upload_object_to_oss_with_bytes


class ERPAsyncDownloadTaskReceiver:
    def __init__(self, request, module_name, download_type, user_id, user_type, company, celery_func):
        """

        :param request: 当前view request
        :param module_name: 模块名称
        :param download_type: 下载类型
        :param user_id: 当前用户id
        :param user_type: 当前用户类型
        :param company: 当前用户公司
        :param celery_func: 异步任务
        """
        self._request = request
        self.module_name = module_name
        self.download_type = download_type
        self.user_id = user_id
        self.user_type = user_type
        self.company = company
        self.celery_func = celery_func

    def validate_export_fields(self):
        from common.basics.exceptions import APIViewException

        export_fields = self._request.data.get("export_fields")
        if not export_fields:
            raise APIViewException
        return export_fields

    def process_task(self):
        from common.models import DownloadTasks

        self.validate_export_fields()

        # 开启任务
        start_time = datetime.now()
        obj_save_fields = dict(
            filename="{}_{}.xlsx".format(self.module_name, start_time.strftime("%Y-%m-%d %H:%M:%S")),
            download_type=self.download_type,
            download_platform=self.user_type,
            source_id="",
            download_page=self.module_name,
            query_data=self._request.query_params,
            post_data=self._request.data,
            absolute_url=self._request.build_absolute_uri(),
            create_user=self.user_id,
            create_date=timezone.make_aware(start_time),
            expired_time=timezone.make_aware(start_time + timedelta(days=6, minutes=45)),
            company=self.company,
        )
        task = DownloadTasks.objects.create(**obj_save_fields)
        # 异步导出任务
        self.celery_func.delay(task.id)

        return task


class ERPAsyncDownloadTaskHandler:
    def __init__(self, filterset_class, serializer_class, celery_func):
        self.filterset_class = filterset_class
        self.serializer_class = serializer_class
        self.celery_func = celery_func

    @staticmethod
    def get_task(task_id):
        from common.models import DownloadTasks

        try:
            task = DownloadTasks.objects.get(pk=task_id)
        except DownloadTasks.DoesNotExist:
            return None
        return task

    def process_task(
        self,
        queryset,
        task_id,
        data_process_func=None,
    ):
        from common.models import DownloadTasks, get_download_max_tasks_count_config
        from users.models import User
        from utils.erp_download_utils.erp_order_download_handler import get_excel_byte_buffer
        from utils.http_handle import custom_django_filter

        start_time = time.time()

        task = self.get_task(task_id)
        if not task:
            return False, f"任务:{task_id}不存在"

        if task.status in ["progressing", "completed", "expired"]:
            return False, f"任务:{task_id}{task.get_status_display()}"

        # 任务队列
        create_user_id = task.create_user
        max_tasks_count = get_download_max_tasks_count_config()
        current_tasks_count = DownloadTasks.objects.filter(create_user=create_user_id, status="progressing").count()
        if current_tasks_count >= max_tasks_count:
            if task.status == "failed":
                task.status = "pending"
                task.save()
            return False, f"当前用户进行中数量: {max_tasks_count},等待中"

        try:
            current_user = User.objects.get(user_id=task.create_user, query_all=True)
        except User.DoesNotExist:
            msg = f"用户:{task.create_user}不存在"
            task.status = "failed"
            task.error_reason = msg
            task.save()
            return False, msg

        # 记录celery的任务
        task.celery_id = self.celery_func.request.id
        task.start_time = timezone.make_aware(datetime.now())

        # 自定义request
        origin_request = HttpRequest()
        origin_request.GET.update(**task.query_data)

        def builtin_absolute_url():
            return task.absolute_url

        setattr(origin_request, "build_absolute_uri", builtin_absolute_url)
        request = Request(origin_request)
        request.user = current_user
        request.auth = {"user_type": task.download_platform}
        request._full_data = task.post_data

        # 导出字段
        export_fields = task.post_data.get("export_fields")
        if not export_fields:
            msg = "导出字段错误"
            task.status = "failed"
            task.error_reason = msg
            task.save()
            return False, msg

        try:
            _, _, qs = custom_django_filter(
                request=request,
                target=queryset,
                filter_set=self.filterset_class,
                iserializer=None,
                need_serialize=False,
                force_order=False,
                need_paginator=False,
            )
            # supplier product sku导出使用
            export_key_list = [field.get("key") for field in export_fields]
            byte_buffer, _ = get_excel_byte_buffer(
                qs,
                export_fields,
                self.serializer_class,
                "",
                request=request,
                serializer_content_kwargs={"export_key_list": export_key_list},
                data_process_func=data_process_func,
            )
            if byte_buffer:
                byte_buffer.seek(0)

            # 上传oss
            download_url = upload_object_to_oss_with_bytes(task.filename, byte_buffer)
            task.status = "completed"
            task.finish_time = timezone.make_aware(datetime.now())
            task.download_url = download_url
            task.save()

            # 通知下一个任务
            next_task = DownloadTasks.objects.only("pk").filter(create_user=create_user_id, status="pending").first()
            if next_task:
                self.celery_func.delay(next_task.pk)
            return True, f"任务ID: {task_id},时间: {datetime.now()}, 耗时: {time.time() - start_time}"
        except Exception as e:
            import traceback

            logger.error(traceback.format_exc())

            task.status = "failed"
            task.error_reason = str(e)
            task.save()
            return False, str(e)
