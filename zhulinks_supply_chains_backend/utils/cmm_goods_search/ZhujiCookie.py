# -*- coding: utf-8 -*-
# @Author: <PERSON>
# @Date:   2023-12-18 11:08:35
# @Last Modified by:   <PERSON>
# @Last Modified time: 2023-12-18 13:54:08

# author: <PERSON><PERSON><PERSON>
# place: Guangzhou
# time:

import requests
from .config import supplier_name, supplier_password


class ZhujiCookie:
    def __init__(self):
        self.username = supplier_name
        self.password = supplier_password

    def supplier_login(self):
        url = "https://zhulinks.com/v1/users/account/login/supplier"
        data = {"username": self.username, "password": self.password}
        response = requests.post(url=url, json=data)
        jsobj = response.json()
        access = jsobj["data"]["access"]
        return access

    def get_cmm_cookies(self, access_token=None):
        url = "https://zhulinks.com/v1/zhuji/spider_cookie?task_type=CMM"
        if not access_token:
            access_token = self.supplier_login()
        zhuji_headers = {"Authorization": "Bearer " + access_token, "Content-Type": "application/json"}
        response = requests.get(url=url, headers=zhuji_headers)
        jsobj = response.json()
        task_cookie = jsobj["data"]["data"][0]["task_cookie"]
        return task_cookie


if __name__ == "__main__":
    c = ZhujiCookie()
    task_cookie = c.get_cmm_cookies()
    print(task_cookie)
