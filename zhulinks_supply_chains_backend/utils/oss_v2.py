# -*- coding: utf-8 -*-
import os
import uuid
from urllib.parse import urlparse

import oss2
from django.conf import settings
from oss2.credentials import StaticCredentialsProvider


def parse_oss_url(oss_url) -> tuple[str, str, str]:
    """
    解析oss url
    1. oss://bucket-name/path/to/object
    2. https://bucket-name.oss-region.aliyuncs.com/path/to/object
    :param oss_url:
    :return: bucket_name, object_key, endpoint
    """
    """Parses an OSS URL (e.g., oss://bucket-name/path/to/object or https://bucket-name.oss-region.aliyuncs.com/path/to/object)
    Returns: bucket_name, object_key, endpoint (or None if not determinable from URL)
    """
    parsed_url = urlparse(oss_url)
    bucket_name = None
    object_key = None
    endpoint = None

    if parsed_url.scheme in ["http", "https"]:
        # For HTTP/HTTPS URLs like https://bucket-name.oss-region.aliyuncs.com/object-key
        # or http://bucket-name.oss-region.aliyuncs.com/object-key
        hostname_parts = parsed_url.netloc.split(".", 1)
        if len(hostname_parts) > 1 and "aliyuncs.com" in hostname_parts[1]:
            bucket_name = hostname_parts[0]
            endpoint = f"{parsed_url.scheme}://{hostname_parts[1]}"
            object_key = parsed_url.path.lstrip("/")
        else:
            # Could be a custom domain or other format, try to infer if possible or raise error
            # For now, let's assume standard Aliyun OSS format
            print(f"Cannot determine bucket and endpoint from HTTP/S URL: {oss_url}. Assuming it might be a custom domain or non-standard format.")
            # Fallback or specific logic for custom domains might be needed here.
            # For this implementation, we'll treat it as an error if not standard Aliyun format.
            raise ValueError(f"Unsupported HTTP/S URL format for OSS: {oss_url}. Expected format like 'bucket-name.oss-region.aliyuncs.com'.")

    elif parsed_url.scheme == "oss":
        # For oss://bucket-name/path/to/object
        bucket_name = parsed_url.netloc
        object_key = parsed_url.path.lstrip("/")
        # Endpoint is not part of oss:// scheme, will be determined by environment or default
    else:
        raise ValueError(f"Invalid URL scheme: {parsed_url.scheme}. Expected 'oss://', 'http://', or 'https://'.")

    if not bucket_name or not object_key:
        raise ValueError(f"Invalid OSS URL format: {oss_url}. Bucket name or object key could not be parsed.")

    return bucket_name, object_key, endpoint


def get_oss_bucket_client(
    endpoint="http://oss-cn-guangzhou.aliyuncs.com",
    bucket_name="zhulinks",
):
    auth = oss2.ProviderAuth(
        StaticCredentialsProvider(
            access_key_id=settings.ALIYUN_ACCESS_ID,
            access_key_secret=settings.ALIYUN_ACCESS_KEY,
        )
    )

    return oss2.Bucket(auth, endpoint, bucket_name, connect_timeout=600)


def upload_object_to_oss_with_bytes(
    filename,
    byte_data: bytes,
    folder: str = "user-download",
):
    bucket = get_oss_bucket_client()
    full_file_path = os.path.join(folder, filename)
    bucket.put_object(full_file_path, byte_data)
    # 校验url
    return f"https://zhulinks.oss-cn-guangzhou.aliyuncs.com/{full_file_path}"


def delete_oss_object(
    filename,
    folder: str = "user-download",
):
    bucket = get_oss_bucket_client()
    full_file_path = os.path.join(folder, filename)
    bucket.delete_object(full_file_path)


def copy_oss_object(oss_link, target_folder: str = None):
    try:
        bucket_name, object_key, endpoint = parse_oss_url(oss_link)
    except ValueError as e:
        print(f"parse {oss_link} failed, {e}")
        return False

    if not target_folder:
        target_folder = "user-upload/prod/images/erpproductMgmt/imgs/"

    bucket = get_oss_bucket_client()
    target_object_key = f"{target_folder}{uuid.uuid4().hex}.png"
    bucket.copy_object(bucket_name, object_key, target_object_key)
    return f"https://zhulinks.oss-cn-guangzhou.aliyuncs.com/{target_object_key}"
