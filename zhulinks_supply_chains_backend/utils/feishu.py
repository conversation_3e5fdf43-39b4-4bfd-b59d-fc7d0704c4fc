# -*- coding: utf-8 -*-
import json
import traceback
import uuid
from datetime import datetime
from hashlib import md5
from typing import Union

import requests
from common import logger
from common.models import FeiShuMedias
from django.conf import settings
from requests_toolbelt import MultipartEncoder
from utils.redis_lock import gen_redis_cache

# cli_a62366ac793fd00c sNiEwu11wbaqPCY9rnfzOh4cp7AMBpzR 为测试飞书的app_id, 默认采用的生产的飞书app_id
FEISHU_APP_ID = getattr(settings, "FEISHU_APP_ID", "cli_a46717cc517a900b")
FEISHU_APP_SECRET = getattr(settings, "FEISHU_APP_SECRET", "kxbV8j7dqrHBJCxGwYhnn0umW0f5nBwB")
FEISHU_NOTICE_URL = getattr(settings, "FEISHU_NOTICE_URL", "https://open.feishu.cn/open-apis/bot/v2/hook/8b5e1c90-8161-41e6-829b-7d281e42f7fe")
FEISHU_PLAN_LOG_TABLE_ID = getattr(settings, "FEISHU_PLAN_LOG_TABLE_ID", "tblG7sujcpRxzxz8")  # default是正式环境参数
FEISHU_PLAN_LOG_TABLE_TOKEN = getattr(settings, "FEISHU_PLAN_LOG_TABLE_TOKEN", "XTcxbDuCZa3g4ZsbGDdcHQ3CnWd")  # default是正式环境参数
FEISHU_LOGIN_DEVICE_APP_ID = getattr(settings, "FEISHU_LOGIN_DEVICE_APP_ID", "tblx1OpzuXupYmqT")  # default是正式环境参数
FEISHU_LOGIN_DEVICE_TOKEN = getattr(settings, "FEISHU_LOGIN_DEVICE_TOKEN", "W8IebVZIua8aXjsarwVc1yMrnng")  # default是正式环境参数
SEND_LOGIN_DEVICE_WARING = getattr(settings, "SEND_LOGIN_DEVICE_WARING", "https://open.feishu.cn/open-apis/bot/v2/hook/966870bc-7868-475a-b9b7-51a8168c9325")
SEND_MODIFY_COST_PRICE_WARING = getattr(settings, "SEND_MODIFY_COST_PRICE_WARING", "https://open.feishu.cn/open-apis/bot/v2/hook/d1e886e4-d40f-49e4-9684-c1578c587b81")
FEISHU_COST_PRICE_APP_ID = getattr(settings, "FEISHU_COST_PRICE_APP_ID", "tblTiJVsgX1GYKs4")  # default是正式环境参数

# 质检审核
SEND_QA_PRODUCT_REVIEW_WARING = getattr(settings, "SEND_QA_PRODUCT_REVIEW_WARING", "https://open.feishu.cn/open-apis/bot/v2/hook/46fd2029-5bb8-4a4a-9a19-739dc3f565ce")
FEISHU_QA_PRODUCT_REVIEW_TOKEN = getattr(settings, "FEISHU_QA_PRODUCT_REVIEW_TOKEN", "RhZbbpEqSaiLr3sGX7VcjVBDn7T")  # default是正式环境参数
FEISHU_QA_PRODUCT_REVIEW_APP_ID = getattr(settings, "FEISHU_QA_PRODUCT_REVIEW_APP_ID", "tblwK3wuVKQ8MqSY")  # default是正式环境参数


class FeishuTokenInvalid(Exception):
    pass


redis_cache = gen_redis_cache()


class FeiShuDocx(object):
    def __init__(self, table_id: str = None, table_app_token: str = None):
        self.tenant_access_token = ""
        self.app_id = FEISHU_APP_ID
        self.app_secret = FEISHU_APP_SECRET
        # 货盘日志
        self.table_id = table_id if table_id else FEISHU_PLAN_LOG_TABLE_ID
        self.table_app_token = table_app_token if table_app_token else FEISHU_PLAN_LOG_TABLE_TOKEN
        self._cache_key = f"fscache:{self.app_id}"
        self.get_tenant_access_token()

    def _gen_token_headers(self, content_type: str = "application/json"):
        return {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": content_type,
        }

    def get_tenant_access_token(self):
        cache_val = redis_cache.get(self._cache_key)
        if cache_val:
            self.tenant_access_token = cache_val
            return

        headers = {
            "Content-Type": "application/json",
        }

        json_data = {
            "app_id": self.app_id,
            "app_secret": self.app_secret,
        }

        response = requests.post(
            "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal",
            headers=headers,
            json=json_data,
        )
        json_text = json.loads(response.text)
        self.tenant_access_token = json_text["tenant_access_token"]
        redis_cache.set(self._cache_key, self.tenant_access_token, json_text["expire"] - 10)

    def get_user_id(self, mobile):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.tenant_access_token}",
        }

        params = {
            "user_id_type": "open_id",
        }
        if isinstance(mobile, list):
            json_data = {"mobiles": mobile}
        else:
            json_data = {"mobiles": [mobile]}
        response = requests.post(
            "https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id",
            params=params,
            headers=headers,
            json=json_data,
        )
        return response.json()

    def send_user_message(self, feishu_id, file_url):
        headers = {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": "application/json",
        }

        params = {
            "receive_id_type": "open_id",
        }

        json_data = {
            "content": json.dumps({"text": file_url}),
            "msg_type": "text",
            "receive_id": feishu_id,
        }

        response = requests.post("https://open.feishu.cn/open-apis/im/v1/messages", params=params, headers=headers, json=json_data)
        print(response.text)

    def batch_send_user_message(self, feiuserid_list: list, file_url: str):
        """
        批量发送数据

        Args:
            feiuserid_list (list): 用户union_user id信息
            file_url (str): 多维表格url
        """
        _url = "https://open.feishu.cn/open-apis/message/v4/batch_send/"

        headers = {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": "application/json",
        }
        post_data = {
            "open_ids": feiuserid_list,
            "msg_type": "text",
            "content": {"text": file_url},
        }

        response = requests.post(url=_url, headers=headers, json=post_data, timeout=15)
        print(response.json())

    def batch_send_user_dimission_message(self, text: str):
        """
        批量发送用户离职数据

        Args:
            text (str): 文本说明
        """
        _url = "https://open.feishu.cn/open-apis/message/v4/batch_send/"

        headers = {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": "application/json",
        }
        post_data = {"open_ids": ["ou_ac3b65fddb6147574d93ec0545fb5dcb"], "msg_type": "text", "content": {"text": text}}

        response = requests.post(url=_url, headers=headers, json=post_data, timeout=15)
        print(response.json())

    def update_img(self, img_url):
        """
        :param img_url: https://....
        :return:  [{"file_token": img_token}]
        """
        if not img_url:
            raise ValueError("img_url is None")

        headers = {"user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
        response = requests.get(img_url, headers=headers, timeout=15)
        url = "https://open.feishu.cn/open-apis/im/v1/images"
        form = {
            "image_type": "message",
            "image": response.content,
        }
        multi_form = MultipartEncoder(form)
        headers = {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": multi_form.content_type,
        }
        response = requests.request("POST", url, headers=headers, data=multi_form)
        json_text = json.loads(response.text)
        if json_text["code"] == 99991663:
            redis_cache.delete(self._cache_key)
            self.get_tenant_access_token()
            raise requests.RequestException
        img_key = json_text["data"]["image_key"]
        return img_key

    def table_update_img(self, img_url):
        """
        多维表格附件
        :param img_url: https://....
        :return:
        """
        file_name = uuid.uuid4().hex
        headers = {"user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
        response = requests.get(img_url, headers=headers, timeout=5)
        url = "https://open.feishu.cn/open-apis/drive/v1/medias/upload_all"
        form = {
            "file_name": f"{file_name}.jpeg",
            "parent_type": "bitable_image",
            "parent_node": self.table_app_token,
            "size": str(len(response.content)),
            "file": response.content,
        }
        multi_form = MultipartEncoder(form)
        headers = {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": multi_form.content_type,
        }
        # with self.lock:
        response = requests.request("POST", url, headers=headers, data=multi_form)
        json_text = json.loads(response.text)
        if json_text["code"] == "99991663":
            self.get_tenant_access_token()
            raise requests.RequestException
        img_token = json_text["data"]["file_token"]
        return [{"file_token": img_token}]

    def send_product_create_msg(
        self,
        product_id,
        com_id,
        com_name,
        supplier_name,
        live_time,
        com_status,
        distributor_name,
        cost_price,
        img_url,
    ):
        """
        分销商创建商品时发送飞书通知
        :param product_id: 商品id
        :param com_id: 货号
        :param com_name: 商品名称
        :param supplier_name:供应商名称
        :param live_time:直播时间
        :param com_status:审核状态
        :param distributor_name:分销商名称
        :param cost_price:成本价
        :param img_url:商品地址
        :return:
        """
        ## 替换为你的自定义机器人的 webhook 地址。
        url = FEISHU_NOTICE_URL  # 测试链接
        # url = "https://open.feishu.cn/open-apis/bot/v2/hook/50e3ebfc-5db9-4a2c-a9b2-2c4f314e4f5a"  # 正式链接
        ## 将消息卡片内容粘贴至此处。
        img_key = self.update_img(img_url=img_url)
        msg_data = {
            "elements": [
                {
                    "tag": "div",
                    "text": {"tag": "lark_md", "content": f"货号：{com_id}\n品名：{com_name}\n供应商：{supplier_name}\n<font color='red'>**成本：{cost_price}**\n</font>"},
                    "extra": {"tag": "img", "img_key": f"{img_key}", "alt": {"tag": "plain_text", "content": "图片"}, "mode": "fit_horizontal", "compact_width": False},
                },
                {"tag": "div", "text": {"content": f"分销商：{distributor_name}\n直播时间：{live_time}\n<font color='red'>**商品状态：{com_status}**\n</font>", "tag": "lark_md"}},
            ],
            "header": {"template": "red", "title": {"content": f"珠凌编码：{product_id}", "tag": "plain_text"}},
        }
        card = json.dumps(msg_data)
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": card,
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=url, data=body, headers=headers)
        return res

    def table_record_insert_one(self, post_data: dict):
        """
        插入一条多维表格信息
        :param post_data:
        :return:
        """
        for i in range(3):
            try:
                json_data = {"fields": post_data}
                response = requests.post(
                    f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.table_app_token}/tables/{self.table_id}/records",
                    headers=self._gen_token_headers(),
                    json=json_data,
                ).json()
                if response["code"] == 99991663:
                    redis_cache.delete(self._cache_key)
                    raise FeishuTokenInvalid
                return response, True
            except FeishuTokenInvalid:
                self.get_tenant_access_token()
            except Exception as e:
                logger.error(f"插入多维表格失败.{e}{traceback.format_exc()}")
        else:
            return None, False

    @staticmethod
    def plan_record_decode(data: dict):
        """
        将字典转成飞书多维表格表头传输
        :param data:
        :return:
        """
        dic = {
            "用户id": data["user_id"],
            "请求url": data["url"],
            "请求页面": data["page_desc"],
            "货盘ID": data["plan_id"],
            "货盘名称": data["plan_name"],
            "操作端": data["platform"],
            "用户真实姓名": data["user_real_name"],
            "用户手机号": data["mobile"],
            "访问时间": data["access_time"],
            "日期": data["access_date"],
        }
        return dic

    def batch_insert_table_records(self, table_token, table_id, post_data: Union[list, tuple]):
        item_list = [{"fields": d} for d in post_data]

        for i in range(0, len(post_data), 500):
            for _ in range(3):
                try:
                    json_data = {"records": item_list[i : i + 500]}
                    response = requests.post(
                        f"https://open.feishu.cn/open-apis/bitable/v1/apps/{table_token}/tables/{table_id}/records/batch_create",
                        headers=self._gen_token_headers(),
                        json=json_data,
                    ).json()

                    if response["code"] == 99991663:
                        redis_cache.delete(self._cache_key)
                        raise FeishuTokenInvalid
                    if response["code"] != 0:
                        print("failed to insert many record. {}".format(response))
                        break

                    logger.info(f"insert many record response:{response}")
                    break
                except FeishuTokenInvalid:
                    self.get_tenant_access_token()
                except Exception as e:
                    logger.error(f"批量插入多维表格失败.{e}{traceback.format_exc()}")
                    break

    def send_sp_product_create_msg(
        self,
        product_id,
        com_id,
        com_name,
        supplier_name,
        physical_inventory,
        com_status,
        advice_price,
        cost_price,
        img_url,
    ):
        ## 替换为你的自定义机器人的 webhook 地址。
        url = FEISHU_NOTICE_URL  # 测试链接
        # url = "https://open.feishu.cn/open-apis/bot/v2/hook/50e3ebfc-5db9-4a2c-a9b2-2c4f314e4f5a"  # 正式链接
        ## 将消息卡片内容粘贴至此处。
        img_key = self.update_img(img_url=img_url)
        msg_data = {
            "elements": [
                {
                    "tag": "div",
                    "text": {"tag": "lark_md", "content": f"货号：{com_id}\n品名：{com_name}\n供应商：{supplier_name}\n<font color='red'>**成本：{cost_price}**\n</font>"},
                    "extra": {"tag": "img", "img_key": f"{img_key}", "alt": {"tag": "plain_text", "content": "图片"}, "mode": "fit_horizontal", "compact_width": False},
                },
                {
                    "tag": "div",
                    "text": {"content": f"建议售价：{advice_price}\n现货库存：{physical_inventory}\n<font color='red'>**商品状态：{com_status}**\n</font>", "tag": "lark_md"},
                },
            ],
            "header": {"template": "red", "title": {"content": f"珠凌编码：{product_id}", "tag": "plain_text"}},
        }
        card = json.dumps(msg_data)
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": card,
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=url, data=body, headers=headers)
        return res

    def send_main_image_score_greater_than_70(
        self,
        request_url,
        product_id,
        com_id,
        com_name,
        supplier_name,
        pic_score,
        img_url,
        create_user_real_name,
    ):
        img_key = self.update_img(img_url=img_url)
        msg_data = {
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": f"货号：{com_id}\n品名：{com_name}\n供应商：{supplier_name}\n创建人：{create_user_real_name}\n<font color='red'>**相似度：{pic_score}**\n</font>",
                    },
                    "extra": {"tag": "img", "img_key": f"{img_key}", "alt": {"tag": "plain_text", "content": "图片"}, "mode": "fit_horizontal", "compact_width": False},
                },
            ],
            "header": {"template": "red", "title": {"content": f"珠凌编码：{product_id}", "tag": "plain_text"}},
        }
        card = json.dumps(msg_data)
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": card,
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=request_url, data=body, headers=headers)
        return res

    def feishu_inventory_warning_notify(
        self,
        receive_id: str,
        img_url: str,
        plan_info: str,
        product_info: str,
        code: str,
        plan_physical_inventory,
        product_physical_inventory,
        product_plan_use_inventory,
        product_can_use_inventory,
    ):
        headers = {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": "application/json",
        }
        params = {
            "receive_id_type": "open_id",
        }
        img_key = self.update_img(img_url=img_url)
        post_data = {
            "receive_id": receive_id,
            "content": json.dumps(
                {
                    "config": {"wide_screen_mode": True},
                    "elements": [
                        {
                            "tag": "img",
                            "img_key": img_key,
                            "alt": {
                                "tag": "plain_text",
                                "content": "商品图片",
                            },
                            "mode": "medium",
                        },
                        {
                            "tag": "div",
                            "text": {
                                "content": f"货盘计划: {plan_info}    商品占用库存: {plan_physical_inventory}\n商品信息: {product_info}, 货号: {code}\n商品现货库存：{product_physical_inventory}, 总占用库存: {product_plan_use_inventory}, 可用库存: {product_can_use_inventory}",
                                "tag": "lark_md",
                            },
                        },
                    ],
                    "header": {"template": "red", "title": {"content": "⚠️ 库存不足", "tag": "plain_text", "i18n": {"en_us": "⚠️ Inventory Warning", "zh_cn": "⚠️️ 库存不足"}}},
                },
                ensure_ascii=False,
            ),
            "msg_type": "interactive",
        }
        response = requests.post("https://open.feishu.cn/open-apis/im/v1/messages", params=params, headers=headers, json=post_data)
        resp_json = response.json()
        return resp_json

    @staticmethod
    def views_warning_msg_send(content: str):
        url = "https://open.feishu.cn/open-apis/bot/v2/hook/42625dc8-a943-40b8-b90e-4be3b566335d"
        body = json.dumps(
            {
                "msg_type": "text",
                "content": {"text": content},
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=url, data=body, headers=headers)
        return res

    def get_all_user(self, page_token=None, page_size=100):
        """
        page_token 翻页使用
        """
        url = "https://open.feishu.cn/open-apis/ehr/v1/employees"
        params = {
            "view": "full",
            "status": 2,  # 在职
            "user_id_type": "open_id",
            "page_token": page_token,
            "page_size": page_size,
        }
        res = requests.get(url=url, params=params, headers=self._gen_token_headers())
        return res

    def user_login_log(self, data: dict):
        """
        用户登录日志多维表格表头
        :param data:
        :return:
        """
        self.table_id = FEISHU_LOGIN_DEVICE_APP_ID
        self.table_app_token = FEISHU_LOGIN_DEVICE_TOKEN
        dic = {
            "账户": data["username"],
            "用户名": data["real_name"],
            "时间": datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S"),
            "ip": data["ip"],
            "登录app": data["client"],
            "登录端": data["user_type"],
            "品牌": data["brand"],
            "型号": data["model"],
            "系统": data["system_version"],
            "GPU型号": data["gpu_model"],
            "设备存储": data["store"],
            "设备像素比": data["pixel_ratio"],
            "设备像素值（长x宽）": data["pixel"],
            "浏览器/app版本": data["app_version"],
            "其他信息": json.dumps(data["other_info"], ensure_ascii=False),
        }
        return dic

    @staticmethod
    def send_device_login_warning(content):
        """

        :return:
        """
        ## 替换为你的自定义机器人的 webhook 地址。
        url = SEND_LOGIN_DEVICE_WARING  # 测试链接
        # url = "https://open.feishu.cn/open-apis/bot/v2/hook/"  # 正式链接
        body = json.dumps(
            {
                "msg_type": "text",
                "content": {"text": content},
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=url, data=body, headers=headers)
        return res

    def modify_cost_price(self, data: dict):
        """
        用户登录日志多维表格表头
        :param data:
        :return:
        """
        self.table_id = FEISHU_COST_PRICE_APP_ID
        self.table_app_token = FEISHU_LOGIN_DEVICE_TOKEN
        dic = {
            "珠凌商品ID": str(data["product_id"]),
            "商品主图": self.table_update_img(data["main_images"][0]) if data["main_images"] else "",
            "珠凌新商品名称": data["product_name"],
            "珠凌货号": str(data["code"]),
            "规格名": data["sku_spec_text"],
            "商品编码": data["spec_code"],
            "改前推广价": data["apply_cost_price"],
            "改后推广价": data["after_change_cost_price"],
            "申请凭证": self.table_update_img(data["apply_image"]) if data["apply_image"] else "",
            "申请备注": data["apply_remark"],
            "修改人": data["create_user"],
            "修改时间": data["create_date"],
            "审核人": data["review_user"],
            "审核结果": data["state"],
            "审核时间": data["review_date"],
            "审核备注": data["remark"],
        }
        return dic

    def send_modify_cost_price_card(self, data):
        """
        推广价修改推送卡片
        :return:
        """
        ## 替换为你的自定义机器人的 webhook 地址。
        url = SEND_MODIFY_COST_PRICE_WARING  # 测试链接
        ## 将消息卡片内容粘贴至此处。
        img_key = self.update_img(img_url=data["main_images"][0])
        msg_data = {
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": f"商品名称：{data['product_name']}\n货号：{data['code']}\n规格名：{data['sku_spec_text']}\n<font color='red'>**修改前推广价：{data['apply_cost_price']}**\n</font><font color='red'>**修改后推广价：{data['after_change_cost_price']}**\n</font>\n修改人：{data['create_user']}\n修改时间：{data['create_date']}",
                    },
                    "extra": {"tag": "img", "img_key": f"{img_key}", "alt": {"tag": "plain_text", "content": "图片"}, "mode": "fit_horizontal", "compact_width": False},
                },
                {
                    "tag": "div",
                    "text": {
                        "content": f"审核人：{data['review_user']}\n审核结果：{data['state']}\n审核说明：{data['remark']}\n审核时间：{data['review_date']}\n",
                        "tag": "lark_md",
                    },
                },
                {
                    "tag": "action",
                    "actions": [
                        {
                            "tag": "button",
                            "text": {"tag": "plain_text", "content": "推广价修改记录"},
                            "url": f"https://yunshangxu.feishu.cn/base/{self.table_app_token}?table={self.table_id}&view=vewOxp0UES",
                            "type": "primary",
                        }
                    ],
                },
            ],
            "header": {"template": "red", "title": {"content": f"珠凌编码：{data['product_id']}", "tag": "plain_text"}},
        }
        card = json.dumps(msg_data)
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": card,
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=url, data=body, headers=headers)
        return res

    def qa_review_product(self, data: dict):
        """
        商品质量审核
        :param data:
        :return:
        """
        self.table_id = FEISHU_QA_PRODUCT_REVIEW_APP_ID
        self.table_app_token = FEISHU_QA_PRODUCT_REVIEW_TOKEN
        dic = {
            "商品ID": data["product_id"],
            "主图": self.table_update_img(data["main_images"][0]) if data["main_images"] else "",
            "商品名称": data["product_name"],
            "货号": str(data["code"]),
            # "规格": data["sku_spec_text"],
            "供应商名称": data["company_name"],
            "审核时间": data["create_date"],
            "审核人": data["create_user"],
            "是否通过": data["is_pass"],
            "审核备注": data["remark"],
        }
        return dic

    def send_qa_product_review_card(self, data):
        """
        质检审核
        :return:
        """
        ## 替换为你的自定义机器人的 webhook 地址。
        url = SEND_QA_PRODUCT_REVIEW_WARING  # 测试链接
        ## 将消息卡片内容粘贴至此处。
        img_key = self.update_img(img_url=data["main_images"][0])
        msg_data = {
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": f"商品名称：{data['product_name']}\n货号：{data['code']}\n供应商名称：{data['company_name']}",
                    },
                    "extra": {"tag": "img", "img_key": f"{img_key}", "alt": {"tag": "plain_text", "content": "图片"}, "mode": "fit_horizontal", "compact_width": False},
                },
                {
                    "tag": "div",
                    "text": {
                        "content": f"审核人：{data['create_user']}\n审核结果：{data['is_pass']}\n审核说明：{data['remark']}\n审核时间：{data['create_date']}\n",
                        "tag": "lark_md",
                    },
                },
                {
                    "tag": "action",
                    "actions": [
                        {
                            "tag": "button",
                            "text": {"tag": "plain_text", "content": "每日质检通过记录表格"},
                            "url": f"https://yunshangxu.feishu.cn/base/{self.table_app_token}?table={self.table_id}&view=vewOxp0UES",
                            "type": "primary",
                        }
                    ],
                },
            ],
            "header": {"template": "red", "title": {"content": f"珠凌编码：{data['product_id']}", "tag": "plain_text"}},
        }
        card = json.dumps(msg_data)
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": card,
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=url, data=body, headers=headers)
        return res

    def upload_image(self, image_url: str, doc_token: str):
        try:
            img_resp = requests.get(image_url, timeout=5)
            if img_resp.status_code != 200:
                logger.warning(f"获取oss图片失败:{img_resp.json()}")
                return None
            img_content = img_resp.content
            url = "https://open.feishu.cn/open-apis/drive/v1/medias/upload_all"

            hash_code = md5(img_content + doc_token.encode()).hexdigest()

            file_token = FeiShuMedias.get_with_cache_by_code(hash_code)
            if file_token:
                return file_token

            filename = f"{hash_code}.jpeg"
            size = len(img_content)
            form = {
                "file_name": filename,
                "parent_type": "bitable_image",
                "parent_node": doc_token,
                "size": str(size),
                "file": img_content,
            }
            multi_form = MultipartEncoder(form)
            headers = {
                "Authorization": f"Bearer {self.tenant_access_token}",
                "Content-Type": multi_form.content_type,
            }
            media_resp = requests.post(url, headers=headers, data=multi_form)
            media_data = media_resp.json()
            if media_resp.status_code != 200:
                logger.warning(f"上传图片到飞书失败:{media_data}")
                return None
            file_token = media_data["data"]["file_token"]
            info = {
                "file_token": file_token,
                "name": filename,
                "size": size,
                "tmp_url": f"https://open.feishu.cn/open-apis/drive/v1/medias/batch_get_tmp_download_url?file_tokens={file_token}",
                "type": "image/jpeg",
                "url": f"https://open.feishu.cn/open-apis/drive/v1/medias/{file_token}/download",
            }
            FeiShuMedias.objects.update_or_create(hash_code=hash_code, defaults=dict(info=info))
            return info
        except Exception as e:
            logger.warning(f">>上传图片到飞书失败:{e}")
            return None

    def insert_record_to_table(self, table_token, table_id, post_data: dict):
        """
        插入一条多维表格信息
        :param table_token:
        :param table_id:
        :param post_data:
        :return:
        """
        for i in range(3):
            try:
                json_data = {"fields": post_data}
                response = requests.post(
                    f"https://open.feishu.cn/open-apis/bitable/v1/apps/{table_token}/tables/{table_id}/records",
                    headers=self._gen_token_headers(),
                    json=json_data,
                ).json()
                if response["code"] == 99991663:
                    redis_cache.delete(self._cache_key)
                    raise FeishuTokenInvalid
                return response, True
            except FeishuTokenInvalid:
                self.get_tenant_access_token()
            except Exception as e:
                logger.error(f"插入多维表格失败.{e}{traceback.format_exc()}")
        else:
            return None, False

    def update_table_record(self, table_token, table_id, record_id, post_data: dict):
        """
        更新多维表格数据
        :param table_token:
        :param table_id:
        :param record_id:
        :param post_data:
        :return:
        """
        for i in range(3):
            try:
                json_data = {"fields": post_data}
                response = requests.put(
                    f"https://open.feishu.cn/open-apis/bitable/v1/apps/{table_token}/tables/{table_id}/records/{record_id}",
                    headers=self._gen_token_headers(),
                    json=json_data,
                ).json()
                if response["code"] == 99991663:
                    redis_cache.delete(self._cache_key)
                    raise FeishuTokenInvalid
                return response, True
            except FeishuTokenInvalid:
                self.get_tenant_access_token()
            except Exception as e:
                logger.error(f"插入多维表格失败.{e}{traceback.format_exc()}")
        else:
            return None, False

    def delete_table_record(self, table_token, table_id, record_id):
        """
        删除单条记录
        :param table_token:
        :param table_id:
        :param record_id:
        :return:
        """
        for i in range(3):
            try:
                response = requests.delete(
                    f"https://open.feishu.cn/open-apis/bitable/v1/apps/{table_token}/tables/{table_id}/records/{record_id}",
                    headers=self._gen_token_headers(),
                ).json()
                if response["code"] == 99991663:
                    redis_cache.delete(self._cache_key)
                    raise FeishuTokenInvalid
                return response, True
            except FeishuTokenInvalid:
                self.get_tenant_access_token()
            except Exception as e:
                logger.error(f"插入多维表格失败.{e}{traceback.format_exc()}")
        else:
            return None, False

    def send_afters_sales_order(
        self,
        sub_as_order_id,
        img_url,
        code,
        product_id,
        product_name,
        spec_code,
        reason_text,
        problem_desc,
        num,
        amount,
        create_date,
        request_url,
    ):
        """
        发送售后单通知
        :param spec_code:
        :param product_name:
        :param request_url:
        :param sub_as_order_id:
        :param img_url:
        :param code:
        :param product_id:
        :param reason_text:
        :param problem_desc:
        :param num:
        :param amount:
        :param create_date:
        :return:
        """
        img_key = self.update_img(img_url=img_url)
        msg_data = {
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": f"商品ID：{product_id}\n货号：{code}\n品名：{product_name}\n商编：{spec_code}\n数量：{num}\n价格：{amount}\n退货理由：{reason_text}\n售后问题描述：{problem_desc}\n<font color='red'>**创建时间：{create_date}**\n<font color='red'>**注意：同意后需要到聚水潭检查订单状态**\n</font>",
                    },
                    "extra": {"tag": "img", "img_key": f"{img_key}", "alt": {"tag": "plain_text", "content": "图片"}, "mode": "fit_horizontal", "compact_width": False},
                },
            ],
            "header": {"template": "red", "title": {"content": f"线上订单号：{sub_as_order_id}", "tag": "plain_text"}},
        }
        card = json.dumps(msg_data)
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": card,
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=request_url, data=body, headers=headers)
        return res

    def table_record_insert_dict(self, post_data):
        try:
            response = requests.post(
                f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.table_app_token}/tables/{self.table_id}/records/batch_create",
                headers=self._gen_token_headers(),
                json=post_data,
            ).json()
            print(f"response:{response}")
            if response["code"] == 99991663:
                redis_cache.delete(self._cache_key)
                raise FeishuTokenInvalid
            logger.info(f"insert many record response:{response}")
        except FeishuTokenInvalid:
            self.get_tenant_access_token()
        except Exception as e:
            logger.error(f"批量插入多维表格失败.{e}{traceback.format_exc()}")

    @staticmethod
    def send_daily_new_product():
        feishu_url = getattr(settings, "SEND_FEISHU_DAILY_NEW_PRODUCT", None)

        if not feishu_url:
            logger.error("还未配置飞书路径.")
            return None
        body = {
            "msg_type": "interactive",
            "card": {
                "elements": [
                    {
                        "tag": "div",
                        "text": {"content": "<at id=all></at>", "tag": "lark_md"},
                    },
                    {
                        "actions": [
                            {
                                "tag": "button",
                                "text": {"content": "查看详情", "tag": "lark_md"},
                                "url": "https://yunshangxu.feishu.cn/base/O47RbUM7EaNnQOscss5cDyVmn8f?table=tblaet4eHJH8ZRi4&view=vewu6JmEOY",
                                "type": "default",
                                "value": {},
                            }
                        ],
                        "tag": "action",
                    },
                    {"tag": "hr"},
                    {
                        "tag": "div",
                        "fields": [
                            {"is_short": False, "text": {"tag": "lark_md", "content": "来自  <font color=blue>珠凌甄选</font>"}},
                        ],
                    },
                ],
                "header": {
                    "title": {
                        "tag": "plain_text",
                        "content": "珠凌每日新品速递",
                    },
                    "template": "green",
                },
            },
        }
        json_body = json.dumps(body)
        headers = {
            "Content-Type": "application/json",
        }
        try:
            response = requests.post(url=feishu_url, data=json_body, headers=headers)
            response.raise_for_status()
            logger.info("飞书提醒发送成功.")
            return response
        except requests.RequestException as e:
            logger.error(f"飞书提醒发送失败: {e}")
            return None

    @staticmethod
    def send_distributor_register_log(
        robot_url,
        mobile,
        nickname,
        username,
        inviter,
        create_date,
    ):
        msg_data = {
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": f"手机号：   {mobile}\n昵称：       {nickname}\n用户名：    {username}\n邀请人：    {inviter}\n创建时间：{create_date}",
                    },
                },
            ],
            "header": {"template": "red", "title": {"content": "又有新分销商注册啦 🎉🎉🎉", "tag": "plain_text"}},
        }
        card = json.dumps(msg_data)
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": card,
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=robot_url, data=body, headers=headers)
        return res

    @staticmethod
    def feishu_internationalization_validation_reminder(data):
        feishu_url = getattr(settings, "SEND_FEISHU_INTERNATIONALIZATION_VALIDATION_REMINDER", None)

        if not feishu_url:
            logger.error("还未配置飞书路径.")
            return None
        body = {
            "msg_type": "interactive",
            "card": {
                "elements": [
                    {
                        "tag": "div",
                        "text": {"content": "<at id=all></at>", "tag": "lark_md"},
                    },
                    {"tag": "div", "text": {"content": f"{data} 需要人工进行翻译，请尽快翻译。", "tag": "lark_md"}},
                    {"tag": "hr"},
                    {
                        "tag": "div",
                        "fields": [
                            {"is_short": False, "text": {"tag": "lark_md", "content": "来自  <font color=blue>国际化翻译人工补全提示</font>"}},
                        ],
                    },
                ],
                "header": {
                    "title": {
                        "tag": "plain_text",
                        "content": "国际化翻译人工补全提示",
                    },
                    "template": "green",
                },
            },
        }
        json_body = json.dumps(body)
        headers = {
            "Content-Type": "application/json",
        }
        try:
            response = requests.post(url=feishu_url, data=json_body, headers=headers)
            print(f"response:{response.json()}")
            response.raise_for_status()
            logger.info("飞书提醒发送成功.")
            return response
        except requests.RequestException as e:
            logger.error(f"飞书提醒发送失败: {e}")
            print(f"飞书提醒发送失败: {e}")
            return None

    @staticmethod
    def send_order_create_msg(
        robot_url,
        redirect_table_token,
        ex_order_id,
        count,
        create_date,
        order_type: int = None,
    ):
        order_type_desc = "分销"
        if order_type and order_type == 98:
            order_type_desc = "臻品"

        msg_data = {
            "header": {"template": "red", "title": {"content": f"🎉{order_type_desc}线上订单号：{ex_order_id}", "tag": "plain_text"}},
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": f"{order_type_desc}线上订单号：{ex_order_id}\n下单商品数量：{count}\n创建时间：{create_date}",
                    },
                },
                {
                    "tag": "action",
                    "actions": [
                        {
                            "tag": "button",
                            "text": {"tag": "plain_text", "content": "查看多维表格"},
                            "url": f"https://yunshangxu.feishu.cn/base/{redirect_table_token}",
                            "type": "default",
                        }
                    ],
                },
            ],
        }
        card = json.dumps(msg_data)
        body = json.dumps({"msg_type": "interactive", "card": card})
        headers = {"Content-Type": "application/json"}
        res = requests.post(url=robot_url, data=body, headers=headers)
        return res

    def send_user_wish_msg(
        self,
        robot_url,
        product_id,
        product_name,
        product_img,
        purchase_count,
        linked_codes,
        user_id,
        mobile,
        real_name,
        create_date,
        redirect_table_token,
    ):
        img_key = self.update_img(img_url=product_img)
        msg_data = {
            "header": {"template": "red", "title": {"content": f"预购商品ID：{product_id}", "tag": "plain_text"}},
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": f"商品名称：{product_name}\n预购数量：{purchase_count}\n关联其他分销货号：{linked_codes}\n预购用户：{user_id}\n手机号：{mobile}\n昵称：{real_name}\n<font color='red'>**创建时间：{create_date}**\n</font>",
                    },
                    "extra": {"tag": "img", "img_key": f"{img_key}", "alt": {"tag": "plain_text", "content": "图片"}, "mode": "fit_horizontal", "compact_width": False},
                },
                {
                    "tag": "action",
                    "actions": [
                        {
                            "tag": "button",
                            "text": {"tag": "plain_text", "content": "查看多维表格"},
                            "url": f"https://yunshangxu.feishu.cn/base/{redirect_table_token}",
                            "type": "default",
                        }
                    ],
                },
            ],
        }
        card = json.dumps(msg_data)
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": card,
            }
        )
        headers = {
            "Content-Type": "application/json",
        }
        res = requests.post(url=robot_url, data=body, headers=headers)
        return res

    @staticmethod
    def send_abnormal_distribute_product_msg(
        robot_url,
        product_id,
        code,
        raw_product_name,
        new_product_name,
        abnormal_info,
        create_date_str,
    ):
        msg_data = {
            "header": {"template": "red", "title": {"content": f"⚠️异常商品ID：{product_id}", "tag": "plain_text"}},
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": f"商品ID：{product_id}\n货号：{code}\n原商品名称：{raw_product_name}\n新商品名称：{new_product_name}\n异常图片：{abnormal_info}\n分销时间：{create_date_str}",
                    },
                },
            ],
        }
        card = json.dumps(msg_data)
        body = json.dumps(
            {
                "msg_type": "interactive",
                "card": card,
            }
        )
        headers = {"Content-Type": "application/json"}
        res = requests.post(url=robot_url, data=body, headers=headers)
        return res

    @staticmethod
    def send_product_price_change_notification(
        product_id,
        product_name,
        code,
        before_price,
        after_price,
        action_type,
    ):
        feishu_url = getattr(settings, "SEND_FEISHU_PRODUCT_PRICE_CHANGE_NOTIFICATION", None)

        if not feishu_url:
            logger.error("还未配置飞书路径.")
            return None
        body = {
            "msg_type": "interactive",
            "card": {
                "elements": [
                    {
                        "tag": "markdown",
                        "content": f"商品ID: {product_id} <br>商品名称: {product_name} <br>商品编码: {code} <br>改价前: {before_price} <br>改价后: {after_price} <br>升降情况: {action_type} ",
                        "text_align": "left",
                        "text_size": "normal_v2",
                        "margin": "0px 0px 0px 0px",
                    },
                    {
                        "actions": [
                            {
                                "tag": "button",
                                "text": {"content": "查看详情", "tag": "lark_md"},
                                "url": "https://yunshangxu.feishu.cn/base/HqiKbHAQDaDx5OsSMBNcK4elnJa?table=tblCtWpo1ZFHzbe9&view=vewfZfuhAL",
                                "type": "default",
                                "value": {},
                            }
                        ],
                        "tag": "action",
                    },
                ],
                "header": {
                    "title": {
                        "tag": "plain_text",
                        "content": "原推广价变更",
                    },
                    "template": "green",
                },
            },
        }
        json_body = json.dumps(body)
        headers = {
            "Content-Type": "application/json",
        }
        try:
            response = requests.post(url=feishu_url, data=json_body, headers=headers)
            response.raise_for_status()
            logger.info("飞书提醒发送成功.")
            return response
        except requests.RequestException as e:
            logger.error(f"飞书提醒发送失败: {e}")
            return None

    def batch_delete_table_records(self, record_is_list: list):
        """
        批量删除飞书多维表格数据
        :param record_is_list:
        :return:
        """
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.table_app_token}/tables/{self.table_id}/records/batch_delete"
        headers = self._gen_token_headers()

        body = {"records": record_is_list}

        resp = requests.post(url=url, headers=headers, json=body)
        if resp.status_code != 200:
            raise Exception(f"删除多维表格记录失败, {resp.text}")

        resp_json = resp.json()
        if resp_json["code"] == 0:
            deleted_rows = len(resp_json["data"].get("records") or [])
            print(f"删除成功, {deleted_rows}")
        else:
            print(f"删除失败 -> {resp_json['msg']}")

    def batch_search_table_records(
        self,
        user_id_type: str = "",
        page_size: int = 20,
        page_token: str = None,
        view_id: str = None,
        field_names: list = None,
        sort: list = None,
        filter: dict = None,
        automatic_fields: bool = False,
        is_delete: bool = False,
        is_need_result: bool = True,
    ):
        """
        批量查询多维表格记录
        https://open.feishu.cn/document/docs/bitable-v1/app-table-record/search
        :param user_id_type:
        :param page_size:
        :param page_token:
        :param view_id: 视图id
        :param field_names:
        :param sort:
        :param filter:
        :param automatic_fields:
        :param is_delete: 是否同时删除
        :param is_need_result: 是否需要结果
        :return:
        """
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.table_app_token}/tables/{self.table_id}/records/search"
        headers = self._gen_token_headers()

        ret_ids = []
        has_more = True
        for i in range(10000):
            # 请求头参数
            params = {
                "page_size": page_size,
                "page_token": page_token,
            }
            if user_id_type:
                params["user_id_type"] = user_id_type

            # 请求体参数
            body = {}

            if view_id:
                body["view_id"] = view_id

            if field_names:
                body["field_names"] = field_names
            if sort:
                body["sort"] = sort
            if filter:
                body["filter"] = filter
            if automatic_fields:
                body["automatic_fields"] = automatic_fields

            res = requests.post(url=url, headers=headers, params=params, json=body)
            if res.status_code != 200:
                logger.error(f"查询表格记录失败，错误码：{res.status_code}，错误信息：{res.text}")
                raise Exception(f"查询表格记录失败，错误码：{res.status_code}，错误信息：{res.text}")

            resp_json = res.json()
            if resp_json.get("code") == 99991663:
                self.get_tenant_access_token()
                continue
            if "data" not in resp_json:
                logger.error(f"查询表格记录失败，错误码：{res.status_code}，错误信息：{resp_json}")
                raise Exception(f"查询表格记录失败，错误码：{res.status_code}，错误信息：{resp_json}")

            has_more = resp_json["data"].get("has_more", False)
            items = resp_json["data"].get("items", [])
            total = resp_json["data"].get("total", 0)

            record_ids = [item["record_id"] for item in items]
            if is_need_result:
                ret_ids.extend(record_ids)

            # 直接删除数据
            if is_delete and record_ids:
                self.batch_delete_table_records(record_ids)

            if not items and has_more is False and total == 0:
                break

            page_token = resp_json["data"].get("page_token") or ""

        if is_need_result:
            return ret_ids
        return None
