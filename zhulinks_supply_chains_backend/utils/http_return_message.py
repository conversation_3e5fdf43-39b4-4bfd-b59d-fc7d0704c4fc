# -*- coding: utf-8 -*-

CommonMsg = {
    "invalid area": "非法区域信息",
    "Invalid file link": "Excel链接错误，请重新上传",
    "Fail to get object": "获取文件失败，请重新上传",
    "Fail to get object, pls contact administrator": "获取文件失败，请重新上传",
    "Fail to get object, pls try later": "获取文件失败，请稍后重试",
    "Failed to parse excel, pls try later": "解析Excel失败，请稍后重试",
}

# 订单错误信息
OrderMSG = {
    "Not the owner, unable to pay for the order": "不是本人，无法支付订单",
    "The order is not in a pending payment status.": "订单不在待支付状态",
    "This order cannot be re-paid. Please place a new order.": "该订单无法重新支付，请重新下单",
    "Invalid product info": "错误商品信息",
    "Confirmation of receipt is not allowed.": "不允许确认收货",
    "Enter a more precise order creator name.": "请输入更精确的下单人姓名",
    "Enter a more precise product name.": "请输入更精确的商品名称",
    "Order unpaid, after-sales unavailable.": "订单未支付,无法申请售后",
    "Order canceled, after-sales unavailable.": "订单已取消,无法申请售后",
    "Order closed, after-sales unavailable.": "订单已关闭,无法申请售后",
    "AfterSales order exist.": "该订单已存在售后单",
    "Invalid address info": "收货人信息错误",
    "Invalid after-sales approve result": "请选择审核结果",
    "AfterSales canceled, cannot approve": "售后订单已取消,无需审核",
    "AfterSales approved, cannot approve again": "售后订单已审核过,无法继续审核",
    "Invalid order amount": "退款信息错误,请联系管理员",
    "Refund failed, please contact with administrator": "退款失败,请联系管理员",
    "Waiting for buyer to return the item": "等待买家填写退货物流",
    "Missing logistics code": "缺少物流单号",
    "After-sales still approving, please check later": "售后订单审核中,请稍后查询",
    "After-sales rejected": "售后订单已被商家拒绝",
    "After-sales create failed": "创建售后失败",
    "After-sales period has ended, please contact the seller": "已超过可申请售后的时间, 请联系商家",
    "Order not shipped, please select another after-sales type": "订单未发货，请选择其他售后类型",
    "Invalid order buyer info, please contact with administrator": "错误买家信息, 请联系管理员",
    "Unable to pay the same order with WeChat Pay on different accounts": "无法在不同的微信支付同一个订单",
    "Invalid price, please contact customer service": "价格错误，请联系客服",
    "error replace type": "错误替换类型",
    "order item not found": "子订单不存在",
    "order not found": "订单不存在",
    "no distributors can refer": "没有可参考的分销商，请联系管理员人工修改",
    "distributor not found": "分销商不存在，请联系管理员人工修改",
    "missing order item id list": "缺少子订单订单ID列表",
    # Settlement order error messages
    "Order code list cannot be empty": "订单编号列表不能为空",
    "Some orders do not exist or have been settled": "部分订单不存在或已结算",
    "Orders must belong to the same customer": "订单必须属于同一客户",
    "Settlement amount must be equal with total order amount": "结算金额必须等于订单总金额",
}

UserMsg = {
    "Missing mobile": "缺少手机号码",
    "invalid mobile": "手机号无效",
    "Mobile used": "手机号已被使用",
    "Mobile has register": "手机号已注册",
    "Username has register": "用户名已注册",
    "invalid user": "非法用户",
}

PermissionMsg = {
    "No permissions can be used": "无法使用任何权限",
    "Invalid permission": "非法权限",
}

ProductMsg = {
    "Product has been delisted": "商品已下架",
    "no sku found": "SKU不存在",
    "subproduct not found": "该商品不存在副本商品，请先加入副本商品",
    "ids should be a array and len less than 500": "每次操作不能超过500个商品",
    "pls enter the product ID or code in a standardized manner": "请规范输入商品ID或者货号",
    "company_id invalid": "非法供应商信息",
}

CouponMsg = {
    "Discount coupons must be set with valid discount thresholds": "满减券必须设置有效的满减门槛",
    "No-threshold coupons cannot be set with discount thresholds": "无门槛券不能设置满减门槛",
    "The validity period must be greater than 1": "有效天数必须大于1",
    "You cannot set a date range when choosing the validity period": "选择有效天数时不能设置日期范围",
    "A valid date range must be set": "必须设置有效的日期范围",
    "The start time of the validity period must be earlier than the end time": "有效期开始时间必须早于结束时间",
    "The total amount issued cannot be reduced, only increased": "发放总量只能增加，不能减少",
    "A list of product IDs must be provided when specifying products": "指定商品时必须提供商品ID列表",
    "A list of user IDs must be provided when specifying users": "指定用户时必须提供用户ID列表",
    "Specifying users count cannot more than coupon total quantity": "指定用户列表不能大于优惠券数量",
    "Missing discount amount": "缺少折扣金额",
    "Discount amount cannot greater than threshold amount": "折扣金额不能大于门槛金额",
    "Coupon has expired": "优惠券已过期",
    "Coupon has deprecated": "优惠券已作废",
    "Coupon used": "优惠券已使用过",
    "Unable to set all users": "无法设置全部用户使用",
    "Missing valid days": "缺少限制有效天数",
    "Valid days must be between 1 and 365": "限制有效天数只能填写1到365",
    "New total quantity cannot less than old total quantity": "新优惠券数量不能小于旧优惠券数量",
    "Coupon does not exist": "优惠券不存在",
    "Coupon cannot be used": "优惠券无法使用",
}

# 部门错误信息
DeptMsg = {
    "Cannot create sub-department beyond level 4": "不允许创建超过4级的子部门",
    "Name already exists in the same department": "名称已存在于同级部门中",
    "Cannot move current department to it's sub-department": "无法将当前部门移至其子部门",
    "Sub-department exists, cannot be deleted": "有下级分组暂不能删除",
    "Current department or sub-department related applications cannot be deleted": "当前部门或子部门有关联用户暂不能删除",
    "Cannot move departments below level 4 departments": "不能把部门移动到4级部门下面",
    "Invalid dept_id": "非法部门信息",
    "Dept does not exist": "部门不存在",
}

ERPProductMsg = {
    "missing skus": "缺少规格信息",
    "invalid category": "非法分类",
    "invalid parent category": "非法父级分类",
    "invalid attr value": "非法属性",
    "spec_code already exists": "商品编码已被使用，请重新输入",
    "category name exists in same level": "相同层级已存在该名称分类",
    "missing spec code": "缺少商品编码",
    "invalid label_price_multiple_type": "非法标签倍率取整方式",
    "sku does not exist": "商品SKU不存在",
    "Label price must be greater than or equal to cost price": "标签价必须大于或等于成本价",
    "invalid unit": "非法单位",
}

ERPWarehouseMsg = {
    "invalid warehouse id": "非法仓库信息",
    "invalid warehouse position id": "非法仓位信息",
    "warehouse name already exists": "仓库名称已存在",
    "warehouse position name already exists": "仓位名称已存在",
    "warehouse position code already exists": "仓位码已存在",
}

ERPCompanySupplierMsg = {
    "supplier name already exists": "供应商名称已存在",
}

ERPPurchaseMsg = {
    "label name already exists": "标签名称已存在",
    "invalid warehouse": "非法仓库",
    "invalid labels, pls refresh the page and try again": "标签信息错误，请刷新页面后重试",
    "pls select orders": "请选择单据",
    "pls select valid orders": "请选择有效的单据",
    "no purchase order need to approve": "没有需要审核的单据",
    "invalid sub_action": "无效的子操作类型",
    "cannot approve with no products": "未添加商品，无法审核生效",
    "editable only in pending status": "只有【待审核】状态的单据支持编辑 ",
    "invalid sku info": "非法规格信息",
    "Invalid purchase detail codes": "非法采购单明细信息",
    "pls select purchase orders": "请选择采购单",
    "invalid purchase orders": "非法采购单",
    "pls select company": "请选择供应商",
    "receipt quantity is 0, cannot be approved": "入库数量为0，无法入库",
    "cannot add products into order with this receipt status": "无法将商品添加到此收据状态的订单中",
    "only voided purchase orders can be deleted": "不是作废采购单，无法删除。请先作废单据",
    "missing company_ids": "缺少供应商信息",
    "cannot operate with return product quantity is 0": "商品退货数量为0，无法操作",
    "Please provide product list which need to be deleted": "请提供需要删除的商品列表",
    "Invalid order products": "非法订单商品信息",
    "Please add product first": "请先添加商品",
    "Please provide product list which need to be added": "请提供要添加的商品列表",
}

CompanyMsg = {
    "invalid company": "非法供应商",
}

InboundOrderMsg = {
    "inbound code already exists, pls regenerate": "入库单号已被使用，请点击重新生成",
    "product spec_code length cannot more than 17 if print barcode": "如需打印条形码请不要超过17位",
    "missing spec_code": "缺少商品编码",
    "same sku with price already exists in current inbound order": "入库单已存在相同价格的商品信息",
    "inbound orders contain stock_take order record, cannot be reserved": "入库订单含有盘点记录，无法反审核",
    "inbound orders contain stock_take order record, cannot be deleted": "入库订单含有盘点记录，无法删除",
    "inbound order already approved, cannot be changed": "入库订单已审核，无法再修改",
    "failed create inbound order": "创建入库订单失败，请稍后重试",
    "print_count cannot be greater than quantity": "打印数量不能大于入库数量",
    "inbound order already approved, cannot be verified again": "入库单已审核，无法重新校验",
    "inbound order already approved, cannot reprint labels": "入库单已审核，无法重新打印标签",
    "reprint_quantity is required": "必须填写打印数量",
    "invalid reprint_type": "非法打印类型",
    "print_quantity cannot be greater than can_print_quantity": "打印数量不能大于可打印数量",
    "pls complete the RFID verification on the mini program first": "请先在小程序端RFID校验完成后再审核入库！",
    "pls complete all labels verification first": "请先完成所有标签的校验再审核入库！",
    "inbound orders contain transfer order record, cannot be reserved": "入库订单含有调拨记录，无法反审核",
    "inbound orders contain sales order record, cannot be reserved": "入库订单含有销售记录，无法反审核",
    "inbound orders contain outbound order record, cannot be reserved": "入库订单含有出库记录，无法反审核",
    "invalid purchase type": "非法采购类型",
    "invalid tag type": "非法标签类型",
    "please select products first": "请先选择商品",
    "invalid cost_price": "错误的成本价",
    "Order has already canceled, cannot be edited": "订单已取消，无法编辑",
    "Inbound order already approved, cannot cancel": "入库单已审核，无法取消",
    "Inbound order already canceled": "入库单已取消",
    "tids is required": "请填写tids",
}

ERPClientMsg = {
    "invalid supplier id": "非法供应商信息",
    "invalid customer_id": "非法客户信息",
}

ERPStockTakeMsg = {
    "warehouse has a stock take order in processing": "当前仓库有进行中的盘点单",
    "stock take order has completed, cannot be processed further": "盘点订单已完成，无法继续盘点",
    "stock take quantity cannot less than 0": "盘点数量不能小于等于0",
    "increase quantity cannot less than 0": "增加盘点数量不能小于等于0",
    "missing tag ids": "没扫描到商品标签",
    "stock take order has completed": "盘点订单已结束盘点",
    "pls select products which need to be adjusted": "请选择需要调整的商品",
    "stock take order processing, pls complete it first": "盘点单进行中，请先结束该单",
    "invalid stock take results, pls refresh and try again": "非法盘点结果，请刷新后重试",
    "invalid unique id: {}": "非法唯一码：",
    "invalid check method": "非法盘点方式",
}

ERPOutboundOrderMsg = {
    "Invalid outbound_type": "非法出库单类型",
}

ERPSKUInventoryMsg = {
    "SKU Inventory does not exist": "SKU库存不存在",
}

ERPSalesOrderMsg = {
    "Data not change": "数据无改动",
    "Sales quantity cannot more than in_warehouse quantity": "销售数量不能大于在仓库存数量",
    "There are pending approval orders, cannot be edited": "该订单存在未审核的出入库单，请前往审核或取消，才可编辑",
    "Sub Order cannot be edited": "子售后单无法编辑",
    "Order has been paid, cannot be edited": "订单已付款，无法编辑",
    "Sub order does not need to be approved": "子订单无需审核",
    "Order already has related warehouse orders": "订单已关联出入库单",
    "Sub order cannot be paid separately, please pay through the main order": "子订单无法单独付款，请通过主订单付款",
    "Sub order cannot cancel pay": "子订单无法取消付款",
    "invalid remark_images type": "非法备注图片类型",
    "Sub sales order cannot be canceled": "副销售单无法被取消",
    "Related warehouse order has been approved, cannot reverse this order": "关联的ERP出入库单已审核，无法反审核销售订单",
    "Related warehouse order has been approved, cannot cancel this order": "关联的出入库单已审核，无法取消销售订单",
    "The current order has been settled with other orders and payment cannot be canceled": "当前订单已和其他订单进行结算，无法退款",
    "The current order has been settled with other orders and payment cannot be paid": "当前订单已和其他订单进行结算，无法付款",
    "Cannot change the discount percentage after payment": "订单已付款，无法修改折扣",
    "Receive amount cannot be greater than payable amount": "收款金额不能大于应付金额",
    "Rounding amount must be less than payable amount": "抹零金额必须小于应付金额",
    "Rounding amount must be 0 when payable amount is 0": "实收金额+抹零金额应当等于应付金额",
    "Rounding amount cannot be less than 0": "抹零金额不能小于0",
    "Receive amount cannot be less than 0": "收款金额不能小于0",
    "Receive amount cannot be 0 when payable amount is 0": "实收金额+抹零金额应当等于应付金额",
}

ERPOrderMsg = {
    "The order has not been approved and cannot be reversed": "订单未审核，无法反审核",
    "The order has been canceled and cannot be approved": "订单已取消，无法审核",
    "The order has been approved and cannot be approved again": "订单已审核，无法重复审核",
    "Related order has been approved, cannot reverse this order": "关联的ERP订单已审核，无法反审核销售订单",
    "Order has been canceled, cannot be paid": "订单已取消，不能付款",
    "Order has been canceled, cannot cancel pay": "订单已取消，不能取消付款",
    "Order payment status is not pending, cannot proceed": "订单支付状态不是待付款，无法操作",
    "Order payment status is not paid, cannot proceed": "订单支付状态不是付款状态，无法操作",
    "Order has been canceled, cannot cancel again": "订单已取消，不能取消",
    "Order has been shipped, cannot be canceled": "订单已发货，不能取消",
    "Order already approved, no need to approve again": "订单已审核，无需重复操作",
    "Canceled order cannot be approved": "已取消的订单不能审核",
    "Canceled order cannot be unapproved": "已取消的订单不能取消审核",
    "Order not approved, no need to unapprove": "订单未审核，无需取消审核",
    "Approved order cannot be deleted": "已审核的订单不能删除",
    "Approved order cannot be TID verified": "已审核的订单不能进行TID校验",
    "Canceled order cannot be TID verified": "已取消的订单不能进行TID校验",
    "TIDs are required": "TID不能为空",
    "TID verification failed": "TID校验失败",
    "Verification quantity does not match order quantity": "校验数量与订单数量不匹配",
    "Order has been settled, cannot be unapproved": "订单已结算，无法取消审核",
    "Insufficient inventory, cannot be approved": "库存不足，无法审核",
    "Discount percentage must be between 0 and 100": "折扣百分比必须在0到100之间",
    "Invalid discount value": "无效折扣值",
    # "Receive amount must be 0 or equal to the payable amount": "实收金额必须为0或等于应付金额",
    "Receive amount must be 0 or equal to the payable amount": "实收金额必须等于应付金额",
    "Invalid receive amount value": "无效实收金额值",
    "The order has been settled and cannot be reversed": "订单已结算，无法反审核",
    "Order has been canceled, cannot be edited": "订单已取消，无法编辑",
    "Order has been settled, cannot be edited": "订单已结算，无法编辑",
    "Order has been settled, cannot be canceled": "订单已结算，无法取消",
    "Order has been approved, cannot be edited": "订单已审核，无法编辑",
    "Order has paid, cannot be edited": "订单已付款，无法编辑",
    "Order has shipped, cannot be edited": "订单已发货，无法编辑",
    "Main sales order has other sub orders, cannot unapprove": "主销售订单存在子订单，无法取消审核",
    "Order details not found": "订单明细不存在",
    "Main sales order not found": "主销售订单不存在",
    "Main sales order TID info not found": "主销售订单TID信息不存在",
    "Main sales order not approved yet": "主销售订单未审核",
    "Order canceled, cannot reprint labels": "订单已取消，无法补打标签",
    "Order approved, cannot reprint labels": "订单已审核，无法补打标签",
    "Order settled, cannot reprint labels": "订单已结算，无法补打标签",
    "Order type is not label, cannot reprint labels": "订单类型不是流转退货订单，无法补打标签",
    "Reprint quantity cannot more than return quantity": "补打标签数量不能大于商品退货数量",
    "Reprint quantity cannot more than can reprint quantity": "补打标签数量不能大于商品可补打数量",
    "Order has already been inbounded, cannot be unapproved": "订单已入库，无法反审核",
    "Order has already been inbounded, cannot be canceled": "订单已入库，无法取消",
    "Order has already been inbounded, cannot be deleted": "订单已入库，无法删除",
    "Please verify the RFID tag at least once": "请先进行至少一次RFID标签校验",
    "Please select the new label product return to the warehouse": "请选择新标签商品退货入库仓库",
    "Approved order cannot be canceled": "已审核的订单无法取消",
}


ERPCustomerSettleMsg = {
    "Settlement amount cannot be greater than the total amount of orders": "结算金额不能大于订单总金额",
    "Settlement amount cannot be less than 0": "结算金额不能小于0",
    "Settlement amount must be 0 when the total amount of orders is 0": "结算金额必须为0或等于订单总金额",
}

#
MSG = {
    "invalid params": "参数错误",
    "System is busy, please try again later": "系统繁忙，请稍后重试",
    "missing params": "缺少必要参数",
    "verificode is incorrect": "验证码不正确",
    "verification code sent within 60s": "60s内已经发送过验证码，请稍后再试",
    "user and company required": "用户和公司是必填项",
    "invalid user or company": "公司或用户无效",
    "User does not exist": "用户不存在",
    "User does not exist, please register": "用户不存在, 请前往注册",
    "user not match company": "用户与公司不匹配",
    "user with no company": "用户尚未绑定公司,请联系管理员绑定公司",
    "invalid username or password": "用户名或密码无效",
    "User does not exist or password is incorrect": "用户不存在或密码不正确",
    "User does not exist or verificode is incorrect": "用户不存在或验证码不正确",
    "Token is invalid or expired": "令牌无效或已过期",
    "data not found": "数据不存在",
    "no permission to operate": "您没有权限操作该资源",
    "The path product_id is inconsistent with the body product_id": "路径的产品id和消息体的产品id不一致",
    "company of product is inconsistent with the body company": "产品所属公司与消息体中的不一致",
    "category format error": "分类格式错误",
    "category invalid": "分类条目无效",
    "category_list must be list": "分类数据必须是数组",
    "mobile of the user_type existed": "该手机号已有该用户类型",
    "the role of company existed": "该角色名已存在",
    "binding user count more than 0, can't delete": "关联员工数量大于0，无法删除",
    "group does not exist": "角色不存在",
    "the user has no permission to add this group": "用户没有权限添加该角色",
    "username existed": "登录用户名已存在",
    "founder existed": "创建者已经存在",
    "no need to operate on this resource": "无需操作该资源",
    "can not change supplier source data": "供应商录入的数据不允许变更供应商",
    "product main image not found": "商品主图不存在",
    "product main image url not valid": "商品主图的url无效",
    "spec_code existed": "商品编码已存在",
    "No valid spec_code was obtained for more than 2 minutes": "获取有效商品编码超时",
    "live_author invalid": "无效的主播id",
    "the product is already in the plan": "该商品已在该货盘计划中",
    "category elements should be int list": "商品类目应该是类目id列表",
    "Not granted editing permissions": "未获得编辑权限",
    "plan is editing by other user": "未获得编辑权限",
    "user not bind distributor": "分销商用户没有绑定分销商",
    "live_date_end should be a future date": "直播结束时间必须是一个未来的时间",
    "this state can't edit": "当前状态不可编辑",
    "plan which editing state can't be locked": "编辑中的计划不可锁定，需要先结束编辑",
    "invalid user, not bound company": "用户尚未绑定公司",
    "invalid user, not bound distributor": "用户尚未绑定分销商",
    "distributor manager existed": "分销商管理者已存在",
    "distributor not bind live author": "分销商商家没有绑定主播",
    "The pallet table is being modified and adding products is not supported": "货盘修改中，不允许加入商品",
    "adding products is not supported in this state": "当前货盘计划状态下，不允许加入商品",
    "this product cannot add into plan": "该商品状态不允许加入货盘表",
    "this product already in other plans": "该商品已在其他货盘计划中",
    "Invalid user type for this user.": "该用户此用户类型无效",
    "unknown user type": "未知用户类型",
    "user_type not ready": "该用户类型未就绪，请联系管理员",
    "password required when a totally new user be added": "全新的用户加入时，需要设置密码",
    "the group name existed in system groups": "该角色名字已被占用，请更换",
    "Field not permit to change": "无权限编辑该字段",
    "Administrator accounts cannot perform this operation and can only be transferred to others": "管理员账户不可执行此操作，请联系运维人员进行转让处理",
    "The expected value type of product id is number, please check the number type": "商品id期望的值类型是数字，请检查编号类型",
    "This field must consist of at most 3 uppercase letters.": "该字段必须是大写英文字母，不超过3个",
    "user_type not match": "用户类型不匹配",
    "data existed": "数据已存在",
    "handcard not found": "手卡不存在",
    "The start selling price is not equal to the previous end selling price": "开始卖价不等于上一个结束卖价, 请检查后输入",
    "The effective time must be in the future": "生效时间必须是未来的时间",
    "Only published announcements can be withdrawn": "只能撤回已发布的公告",
    "Announcements that have been published cannot be published again": "已发布的公告不可以再次被直接发布",
    "required: skus, company_id": "缺少SKU信息或供应商信息",
    "data parse error": "数据类型错误",
    "missing estimated_sales": "缺少预估销量",
    **OrderMSG,
    **UserMsg,
    **ProductMsg,
    **CouponMsg,
    **DeptMsg,
    **PermissionMsg,
    **ERPPurchaseMsg,
    **CompanyMsg,
    **CommonMsg,
    **ERPProductMsg,
    **ERPWarehouseMsg,
    **ERPCompanySupplierMsg,
    **ERPClientMsg,
    **InboundOrderMsg,
    **ERPStockTakeMsg,
    **ERPSKUInventoryMsg,
    **ERPOrderMsg,
    **ERPSalesOrderMsg,
    **ERPCustomerSettleMsg,
}
