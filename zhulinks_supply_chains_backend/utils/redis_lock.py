# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-11-24 18:19:16
# @Last Modified by:   <PERSON>
# @Last Modified time: 2023-12-22 16:09:31
import redis
from django.core.cache import caches
from django_redis import get_redis_connection
import time


def read_lock(key):
    """
    读取分布式锁
    """
    lock_key = f"lock:{key}"
    redis_cache = caches["redis_cache"]
    lock_acquired = redis_cache.get(lock_key)
    return lock_acquired


def setnx_lock(key, value, timeout=60):
    """
    设置分布式锁
    """
    lock_key = f"lock:{key}"
    expiration_time = timeout + 1  # 防止锁在执行期间过期
    redis_cache = caches["redis_cache"]
    # 使用 cache.add 实现原子性的 setnx
    # 已经有值返回具体值，设置成功返回True
    lock_acquired = redis_cache.get(lock_key) or redis_cache.add(lock_key, value, expiration_time)
    return lock_acquired


def release_lock(key, value):
    """
    释放分布式锁
    """
    lock_key = f"lock:{key}"
    redis_cache = caches["redis_cache"]
    lock_acquired = redis_cache.get(lock_key)
    if not lock_acquired:
        return True
    if lock_acquired == value:
        # 释放锁
        redis_cache.delete(lock_key)
        return True
    return False


def gen_redis_conn(redis_cache_name: str = "redis_cache") -> redis.client.Redis:
    return get_redis_connection(redis_cache_name)


def gen_redis_cache(redis_cache_name: str = "redis_cache"):
    return caches[redis_cache_name]


redis_cache = gen_redis_cache()
redis_conn = gen_redis_conn()
