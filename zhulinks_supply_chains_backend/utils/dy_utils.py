# -*- coding: utf-8 -*-
import base64
import hmac
from hashlib import sha256, md5

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

from common import logger


def aes_encrypt(plain_text: str, app_secret: str) -> str:
    # Remove hyphens from the app_secret and convert it to bytes
    app_secret_bytes = app_secret.replace("-", "").encode("utf-8")

    # Convert the plain text to bytes
    plain_text_bytes = plain_text.encode("utf-8")

    # Create an AES cipher block
    block = algorithms.AES(app_secret_bytes)
    block_size = block.block_size // 8  # Block size in bytes

    # Pad the plain text to match the AES block size
    padder = padding.PKCS7(block.block_size).padder()
    padded_data = padder.update(plain_text_bytes) + padder.finalize()

    # Encrypt using CBC mode with the first `block_size` bytes of the key as the IV
    cipher = Cipher(block, modes.CBC(app_secret_bytes[:block_size]), backend=default_backend())
    encryptor = cipher.encryptor()
    encrypted_msg = encryptor.update(padded_data) + encryptor.finalize()

    # Encode the encrypted message in base64 for safe transmission
    return base64.b64encode(encrypted_msg).decode("utf-8")


def aes_decrypt(msg_secret: str, app_secret: str) -> str | None:
    # 将app_secret中的"-"去掉
    app_secret_bytes = app_secret.replace("-", "").encode("utf-8")

    # 解码base64编码的加密消息
    encrypted_msg = base64.b64decode(msg_secret)

    try:
        # 执行AES解密
        decrypted_msg = do_aes_decrypt(encrypted_msg, app_secret_bytes)
        print(f"解密后: {decrypted_msg.decode('utf-8')}")
        return decrypted_msg.decode("utf-8")
    except Exception as e:
        print(f"解密失败: {e}")
        return


def do_aes_decrypt(encrypted_msg: bytes, key: bytes) -> bytes:
    # 创建AES密码块
    block = algorithms.AES(key)
    block_size = block.block_size // 8  # 获取块大小（以字节为单位）

    # 使用CBC模式进行解密
    cipher = Cipher(block, modes.CBC(key[:block_size]), backend=default_backend())
    decryptor = cipher.decryptor()

    # 解密数据
    orig_data = decryptor.update(encrypted_msg) + decryptor.finalize()

    # 去除填充数据
    orig_data = pkcs5_unpadding(orig_data)

    return orig_data


def pkcs5_unpadding(orig_data: bytes) -> bytes:
    length = len(orig_data)
    unfilled_num = orig_data[length - 1]
    return orig_data[: length - unfilled_num]


def sign_with_sha256(app_id, app_secret, post_body_str, sign):
    need_decrypt_str = f"{app_id}{post_body_str}{app_secret}"
    return hmac.new(app_secret.encode(), need_decrypt_str.encode(), sha256).hexdigest() == sign


def sign_with_md5(app_id, app_secret, post_body_str, sign):
    need_decrypt_str = f"{app_id}{post_body_str}{app_secret}"
    return md5(need_decrypt_str.encode()).hexdigest() == sign


def validate_dy_request_sign(app_id, app_secret, post_body_str, sign, sign_method):
    if sign_method not in ["MD5", "hmac-sha-256"]:
        return False

    if sign_method == "MD5":
        ret = sign_with_md5(app_id, app_secret, post_body_str, sign)
    else:
        ret = sign_with_sha256(app_id, app_secret, post_body_str, sign)

    if not ret:
        logger.warning(f"验签失败: {app_id},{post_body_str},{sign}, {sign_method}")

    return ret


if __name__ == "__main__":
    app_secret = "1234567890abcdef"
    msg = "8888"

    encrypted_msg = aes_encrypt(msg, app_secret)
    print(f"加密后的消息: {encrypted_msg}")

    decrypted_msg = aes_decrypt(encrypted_msg, app_secret)
    print(f"解密后的消息: {decrypted_msg}")
