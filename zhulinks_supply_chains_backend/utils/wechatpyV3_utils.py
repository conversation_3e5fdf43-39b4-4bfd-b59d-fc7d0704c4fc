# -*- coding: utf-8 -*-
import json
import random
import string
import time
from base64 import b64encode, b64decode

import httpx
from Cryptodome.Hash import SHA256
from Cryptodome.PublicKey import RSA
from Cryptodome.Signature import pkcs1_15
from cryptography.exceptions import InvalidSignature
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric.padding import PKCS1v15
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives.hashes import SHA256 as CrySHA256
from cryptography.x509 import load_pem_x509_certificate
from django.conf import settings


# 生成订单号


def random_str(length=8):
    return "".join(random.choice(string.ascii_uppercase + string.digits) for _ in range(length))


def build_headers(nonce_str, timestamp, sign):
    authorization = "WECHATPAY2-SHA256-RSA2048 " + ",".join(
        [
            f'mchid="{settings.WECHATPAY_MCH_ID}"',
            f'nonce_str="{nonce_str}"',
            f'signature="{sign}"',
            f'timestamp="{timestamp}"',
            f'serial_no="{settings.WECHATPAY_CERT_SERIAL_NO}"',
        ]
    )
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": authorization,
    }
    return headers


def get_sign_v3(sign_str, cert_str: str | None = settings.WECHATPAY_APICLIENT_KEY):
    rsa_key = RSA.importKey(cert_str)
    signer = pkcs1_15.new(rsa_key)
    digest = SHA256.new(sign_str.encode("utf8"))
    sign = b64encode(signer.sign(digest)).decode("utf-8")
    return sign


def decrypt(nonce, ciphertext, associated_data):
    key = settings.WECHATPAY_API_KEY
    key_bytes = str.encode(key)
    nonce_bytes = str.encode(nonce)
    ad_bytes = str.encode(associated_data)
    data = b64decode(ciphertext)
    aesgcm = AESGCM(key_bytes)
    return aesgcm.decrypt(nonce_bytes, data, ad_bytes)


def validate_wechat_sign(timestamp, nonce, body: dict, signature):
    """
    校验签名
    :param timestamp: headers内的timestamp
    :param nonce: headers内的nonce
    :param body: request的内容，dictL诶行
    :param signature: headers内的signature
    :return:
    """

    if not isinstance(body, dict):
        raise ValueError("body参数必须为dict类型")
    body_str = json.dumps(body, ensure_ascii=False, separators=(",", ":"))
    sign_str = "%s\n%s\n%s\n" % (timestamp, nonce, body_str)
    certificate = load_pem_x509_certificate(settings.WECHATPAY_VALIDATE_SIGN_CERT.encode("utf-8"), backend=default_backend())
    public_key = certificate.public_key()
    try:
        decode_signature = b64decode(signature)
        public_key.verify(decode_signature, sign_str.encode(), PKCS1v15(), CrySHA256())
    except InvalidSignature as e:
        return False
    except Exception as e:
        return False
    return True


def get_certificates():
    """
    获取平台证书
    """
    url = "https://api.mch.weixin.qq.com/v3/certificates"
    data_json_str = ""
    nonce_str = random_str(32)
    timestamp = str(int(time.time()))
    sign_str = f"GET\n{'/v3/certificates'}\n{timestamp}\n{nonce_str}\n{data_json_str}\n"
    sign = get_sign_v3(sign_str)
    headers = build_headers(nonce_str, timestamp, sign)
    response = httpx.get(url, headers=headers)
    return response.json()


def get_wechat_transaction_info(out_trade_no: str):
    nonce_str = random_str(32)
    timestamp = str(int(time.time()))
    sign_str = f"GET\n/v3/pay/transactions/out-trade-no/{out_trade_no}?mchid={settings.WECHATPAY_MCH_ID}\n{timestamp}\n{nonce_str}\n\n"
    sign = get_sign_v3(sign_str)

    headers = build_headers(nonce_str, timestamp, sign)

    response = httpx.get(
        f"https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/{out_trade_no}?mchid={settings.WECHATPAY_MCH_ID}",
        headers=headers,
    )

    return response


def pay_refund_request_generator(
    transaction_id,
    refund_out_trade_no,
    origin_order_fee,
    refund_fee,
    *args,
    **kwargs,
):
    """
    微信退款
    https://pay.weixin.qq.com/docs/merchant/apis/jsapi-payment/create.html
    :param refund_fee: 退款金额,分
    :param origin_order_fee: 原订单金额,分
    :param refund_out_trade_no: 退款线上订单号
    :param transaction_id: 流水号
    post_data = {
        "out_trade_no": order_id,
        "out_refund_no": time_str,
        # "transaction_id": "4200002231202407269233682844",
        "amount": {
            "total": 1,
            "refund": 1,
            "currency": "CNY",
        },
    }
    :return:
    """
    post_data = {
        "transaction_id": transaction_id,
        "out_refund_no": refund_out_trade_no,
        "notify_url": settings.WECHATPAY_V3_NOTIFY_URL,
        "amount": {
            "total": origin_order_fee,
            "refund": refund_fee,
            "currency": "CNY",
        },
    }
    data_json_str = json.dumps(post_data)
    nonce_str = random_str(32)
    timestamp = str(int(time.time()))
    sign_str = f"POST\n{'/v3/refund/domestic/refunds'}\n{timestamp}\n{nonce_str}\n{data_json_str}\n"
    sign = get_sign_v3(sign_str)
    headers = build_headers(nonce_str, timestamp, sign)

    req_url = "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds"
    response = httpx.post(req_url, headers=headers, data=data_json_str)
    return response


def check_refund_status(out_refund_no):
    """
        查询退款
        :param out_refund_no:
        :return:
        response:
        {
        "amount": {
            "currency": "CNY",
            "discount_refund": 0,
            "from": [],
            "payer_refund": 1,
            "payer_total": 1,
            "refund": 1,
            "refund_fee": 0,
            "settlement_refund": 1,
            "settlement_total": 1,
            "total": 1
        },
        "channel": "ORIGINAL",
        "create_time": "2024-07-27T17:53:43+08:00",
        "funds_account": "UNAVAILABLE",
        "out_refund_no": "**************",
        "out_trade_no": "20240726113117774598",
        "promotion_detail": [],
        "refund_id": "50303200232024072750886342973",
        "status": "SUCCESS",
        "success_time": "2024-07-27T17:53:47+08:00",
        "transaction_id": "4200002296202407265330141359",
        "user_received_account": "支付用户零钱"
    }
    """
    uri = "/v3/refund/domestic/refunds"
    nonce_str = random_str(32)
    timestamp = str(int(time.time()))
    sign_str = f"GET\n{uri}/{out_refund_no}\n{timestamp}\n{nonce_str}\n\n"
    sign = get_sign_v3(sign_str)

    headers = build_headers(nonce_str, timestamp, sign)

    response = httpx.get(
        f"https://api.mch.weixin.qq.com{uri}/{out_refund_no}",
        headers=headers,
    )

    return response
