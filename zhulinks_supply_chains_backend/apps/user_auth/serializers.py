# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON> <PERSON>
# @Date:   2022-06-16 15:25:31
# @Last Modified by:   <PERSON><PERSON> <PERSON>
# @Last Modified time: 2023-03-18 03:37:57
from rest_framework import serializers

from user_auth.models import (
    User,
    UserDailyQuota,
    UserNotification,
    UserRedeemCodeRecord,
    UserCreditsChangeRecord,
    UserBuyMembershipRecord,
)


class UserRedeemCodeRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserRedeemCodeRecord
        fields = [
            "code",
            "benefit_data",
            "redeem_time",
        ]


class UserDailyQuotaSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserDailyQuota
        exclude = ("id", "user", "update_time")


class UserInfoSerializer(serializers.ModelSerializer):
    wxoap_binded = serializers.SerializerMethodField()
    membership_level = serializers.SerializerMethodField()
    membership_type = serializers.SerializerMethodField()
    membership_quota = serializers.SerializerMethodField()
    membership_quota_next_reset_datetime = serializers.SerializerMethodField()
    credits = serializers.SerializerMethodField()

    def get_wxoap_binded(self, obj: User):
        return bool(obj.wx_oap_open_id)

    def get_membership_level(self, obj: User):
        if obj.is_subaccount:
            return obj.parent_account.membership_level
        return obj.membership_level

    def get_membership_type(self, obj: User):
        if obj.is_subaccount:
            return obj.parent_account.membership_type
        return obj.membership_type

    def get_membership_quota(self, obj: User):
        if obj.is_subaccount:
            return obj.parent_account.membership_quota
        return obj.membership_quota

    def get_membership_quota_next_reset_datetime(self, obj: User):
        if obj.is_subaccount:
            dt = obj.parent_account.membership_quota_next_reset_datetime
        else:
            dt = obj.membership_quota_next_reset_datetime

        if dt is not None:
            return int(dt.timestamp() * 1000)
        else:
            return None

    def get_credits(self, obj: User):
        if obj.is_subaccount:
            return obj.parent_account.credits
        return obj.credits

    class Meta:
        model = User
        fields = [
            "mobile",
            "country_code",
            "email",
            "membership_level",
            "membership_type",
            "membership_quota",
            "membership_quota_next_reset_datetime",
            "credits",
            "email_verified",
            "mobile_verified",
            "wxoap_binded",
            "is_subaccount",
            "date_joined",
        ]


class UserCreditsChangeRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserCreditsChangeRecord
        fields = [
            "action",
            "credits_changed",
            "credits_after",
            "create_time",
        ]


class UserBuyMembershipRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserBuyMembershipRecord
        fields = [
            "membership_type",
            "days",
            "create_time",
            "expire_time",
        ]


class UserNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserNotification
        fields = [
            "nid",
            "category",
            "title",
            "read",
            "create_time",
        ]
