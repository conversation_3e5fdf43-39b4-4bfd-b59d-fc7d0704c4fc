'''
Author: <PERSON><PERSON><PERSON>
Date: 2023-05-23 21:35:33
LastEditors: <PERSON><PERSON>i
LastEditTime: 2023-05-26 11:55:10
'''
# -*- coding: utf-8 -*-
# @Author: ander
# @Date:   2019-09-29 23:50:50
# @Last Modified by:   <PERSON><PERSON> <PERSON>
# @Last Modified time: 2023-04-04 14:36:19
from django.urls import path

# from . import views

# urlpatterns = [
#     path("", views.user_info, name="user_info"),
#     path("invitations", views.user_invitations, name="user_invitations"),
#     path("redeem-code/use", views.user_redeem_code_use, name="user_redeem_code_use"),
#     path("redeem-code/records", views.user_redeem_code_records, name="user_redeem_code_records"),
#     path("credits-records", views.user_credits_records, name="user_credits_records"),
#     path("membership-records", views.user_membership_records, name="user_membership_records"),
#     path("notifications/all", views.user_notifications_all, name="user_notifications_all"),
#     path("notifications/unread", views.user_notifications_unread, name="user_notifications_unread"),
#     path("notifications/detail", views.user_notifications_detail, name="user_notifications_detail"),
#     path("sub-accounts/list", views.user_subaccounts_list, name="user_subaccounts_list"),
#     path("sub-accounts/detail/<str:action>", views.user_subaccounts_detail, name="user_subaccounts_detail"),
# ]
