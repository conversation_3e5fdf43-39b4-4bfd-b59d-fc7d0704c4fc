# # -*- coding: utf-8 -*-
# # @Author: <PERSON><PERSON>
# # @Date:   2022-03-31 14:09:49
# # @Last Modified by:   <PERSON><PERSON>
# # @Last Modified time: 2023-04-04 14:36:04
# from django.db.models import Q
# from django.conf import settings
# from django.http import JsonResponse
# from django.core.paginator import Paginator
# from django.contrib.auth import get_user_model
# from django.utils.timezone import now, zoneinfo
# from django.contrib.auth.backends import ModelBackend

# from rest_framework.permissions import IsAuthenticated
# from rest_framework.decorators import api_view, permission_classes

# from user_auth.models import (
#     User,
#     UserNotification,
#     UserRedeemCodeRecord,
#     UserBuyMembershipRecord,
#     UserCreditsChangeRecord,
# )
# from user_auth.serializers import (
#     UserInfoSerializer,
#     UserNotificationSerializer,
#     UserRedeemCodeRecordSerializer,
#     UserCreditsChangeRecordSerializer,
#     UserBuyMembershipRecordSerializer,
# )
# from user_auth.utils import (
#     create_membership,
#     get_parent_user,
#     get_user_daily_quota,
#     get_user_register_referral,
#     handle_user_credits_change,
#     reset_user_membership_quota,
# )


# class EmailorUsernameModelBackend(ModelBackend):
#     def authenticate(self, request, username=None, password=None, **kwargs):
#         UserModel = get_user_model()
#         try:
#             user = UserModel.objects.get(Q(username__iexact=username) | Q(email__iexact=username))
#         except UserModel.DoesNotExist:
#             return None
#         else:
#             if user.check_password(password):
#                 return user
#         return None


# class MobileorUsernameModelBackend(ModelBackend):
#     def authenticate(self, request, username=None, password=None, **kwargs):
#         UserModel = get_user_model()
#         try:
#             user = UserModel.objects.get(Q(username__iexact=username) | Q(mobile__iexact=username))
#         except UserModel.DoesNotExist:
#             return None
#         else:
#             if user.check_password(password):
#                 return user
#         return None


# @api_view(["GET", "POST"])
# @permission_classes([IsAuthenticated])
# def user_info(request):
#     serializer = UserInfoSerializer(request.user)
#     data = serializer.data
#     parent_user = get_parent_user(request.user)
#     user_buy_membership_records_qs = UserBuyMembershipRecord.objects.filter(user=parent_user)
#     if user_buy_membership_records_qs.exists():
#         dt_now = now().astimezone(zoneinfo.ZoneInfo("Asia/Shanghai"))
#         latest_membership_record = user_buy_membership_records_qs.latest("expire_time")
#         if parent_user.membership_type == "NM":
#             data.update(
#                 {
#                     "membership": {
#                         "has_record": False,
#                     }
#                 }
#             )
#         elif latest_membership_record.expire_time < dt_now:
#             parent_user.membership_type = "NM"
#             parent_user.membership_quota = {}
#             parent_user.save()
#             data.update(
#                 {
#                     "membership": {
#                         "has_record": False,
#                     }
#                 }
#             )
#         else:
#             data.update(
#                 {
#                     "membership": {
#                         "has_record": True,
#                         **UserBuyMembershipRecordSerializer(latest_membership_record).data,
#                     }
#                 }
#             )
#             if dt_now > parent_user.membership_quota_next_reset_datetime:
#                 reset_user_membership_quota(parent_user)
#     else:
#         data.update(
#             {
#                 "membership": {
#                     "has_record": False,
#                 }
#             }
#         )
#     if request.user.is_staff:
#         data.update({"is_staff": True})
#     user_daily_quota = get_user_daily_quota(parent_user, serialize=True)
#     data.update({"user_daily_quota": user_daily_quota})
#     data.update({"no_password": len(request.user.password) == 0})
#     response = {"status": 200, "msg": "", "data": data}
#     return JsonResponse(response, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_credits_records(request):
#     page_num = request.data.get("page", 1)
#     page_size = min(request.data.get("page_size", 10), 100)
#     records_qs = UserCreditsChangeRecord.objects.filter(user=request.user).order_by("-create_time")
#     paginator = Paginator(records_qs, page_size)
#     records = paginator.get_page(page_num).object_list.select_related("user")
#     serializer = UserCreditsChangeRecordSerializer(records, many=True)
#     response = {
#         "status": 200,
#         "msg": "",
#         "data": {
#             "records": serializer.data,
#             "total": records_qs.count(),
#             "page_size": page_size,
#             "page_count": paginator.num_pages,
#         },
#     }
#     return JsonResponse(response, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_membership_records(request):
#     records = UserBuyMembershipRecord.objects.filter(user=request.user).order_by("-create_time")
#     serializer = UserBuyMembershipRecordSerializer(records, many=True)
#     data = serializer.data
#     response = {"status": 200, "msg": "", "data": data}
#     return JsonResponse(response, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_notifications_all(request):
#     page_num = request.data.get("page", 1)
#     page_size = min(request.data.get("page_size", 10), 100)
#     category = request.data.get("category")
#     notifications_qs = UserNotification.objects.filter(user=request.user).order_by("-create_time")
#     if category is not None:
#         notifications_qs = notifications_qs.filter(category=category)
#     paginator = Paginator(notifications_qs, page_size)
#     notifications = paginator.get_page(page_num).object_list.select_related("user")
#     serializer = UserNotificationSerializer(notifications, many=True)
#     response = {
#         "status": 200,
#         "msg": "",
#         "data": {
#             "notifications": serializer.data,
#             "total": notifications_qs.count(),
#             "page_size": page_size,
#             "page_count": paginator.num_pages,
#         },
#     }
#     return JsonResponse(response, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_notifications_unread(request):
#     unread_notifications_qs = UserNotification.objects.filter(user=request.user, read=False)
#     response = {"status": 200, "msg": "", "data": {"has_unread": unread_notifications_qs.exists()}}
#     return JsonResponse(response, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_notifications_detail(request):
#     nid = request.data.get("nid")
#     if nid is None:
#         return JsonResponse({"status": 500, "msg": "need notification id", "data": {}}, safe=False)

#     notification_qs = UserNotification.objects.filter(user=request.user, nid=nid)
#     if not notification_qs.exists():
#         return JsonResponse({"status": 404, "msg": "notification not exists", "data": {}}, safe=False)
#     notification = notification_qs.first()
#     notification.read = True
#     notification.save()

#     response = {
#         "status": 200,
#         "msg": "",
#         "data": {
#             "title": notification.title,
#             "content": notification.content,
#             "create_time": notification.create_time,
#         },
#     }
#     return JsonResponse(response, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_invitations(request):
#     page_num = request.data.get("page", 1)
#     page_size = min(request.data.get("page_size", 10), 100)
#     user_register_referral = get_user_register_referral(request.user)
#     invitees = user_register_referral.invitees.all().order_by("-date_joined").values("mobile", "date_joined")
#     paginator = Paginator(invitees, page_size)
#     records = paginator.get_page(page_num).object_list
#     invitees_list = [
#         {
#             "mobile": "*******" + invitee["mobile"][-4:],
#             "register_time": int(invitee["date_joined"].timestamp() * 1000),
#         }
#         for invitee in records
#     ]
#     response = {
#         "status": 200,
#         "msg": "",
#         "data": {
#             "code": user_register_referral.code,
#             "invitees": invitees_list,
#             "total": invitees.count(),
#             "page_size": page_size,
#             "page_count": paginator.num_pages,
#         },
#     }
#     return JsonResponse(response, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_redeem_code_use(request):
#     code = request.data.get("code")
#     if code is None:
#         return JsonResponse({"status": 500, "msg": "need code", "data": {}}, safe=False)
#     redeem_code_record_qs = UserRedeemCodeRecord.objects.filter(code=code, expire_time__gt=now())
#     if not redeem_code_record_qs.exists():
#         return JsonResponse({"status": 404, "msg": "code not exists", "data": {}}, safe=False)
#     available_redeem_code_record_qs = redeem_code_record_qs.filter(user=None)
#     if not available_redeem_code_record_qs.exists():
#         return JsonResponse({"status": 507, "msg": "no available redeem codes", "data": {}}, safe=False)
#     user_used_redeem_code_record_qs = redeem_code_record_qs.filter(user=request.user)
#     if user_used_redeem_code_record_qs.exists():
#         return JsonResponse({"status": 409, "msg": "you have used this code", "data": {}}, safe=False)
#     available_redeem_code_record = available_redeem_code_record_qs.first()
#     available_redeem_code_record.user = request.user
#     available_redeem_code_record.status = "US"
#     available_redeem_code_record.redeem_time = now()
#     available_redeem_code_record.save()

#     benefit = available_redeem_code_record.benefit_data
#     if benefit["type"] == "add_membership":
#         create_membership(
#             request.user,
#             benefit["membership_type"],
#             benefit["membership_length"],
#             "RE",
#         )
#     elif benefit["type"] == "add_credits":
#         handle_user_credits_change(request.user, "RC", benefit["credits"])

#     return JsonResponse({"status": 200, "msg": "", "data": benefit}, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_redeem_code_records(request):
#     page_num = request.data.get("page", 1)
#     page_size = min(request.data.get("page_size", 10), 100)
#     records_qs = UserRedeemCodeRecord.objects.filter(user=request.user).order_by("-redeem_time")
#     paginator = Paginator(records_qs, page_size)
#     records = paginator.get_page(page_num).object_list.select_related("user")
#     serializer = UserRedeemCodeRecordSerializer(records, many=True)
#     response = {
#         "status": 200,
#         "msg": "",
#         "data": {
#             "records": serializer.data,
#             "total": records_qs.count(),
#             "page_size": page_size,
#             "page_count": paginator.num_pages,
#         },
#     }
#     return JsonResponse(response, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_apply_membership_get(request):
#     pass


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_apply_membership_create(request):
#     pass


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_subaccounts_list(request):
#     subaccounts_qs = User.objects.filter(parent_account=request.user)
#     subaccounts_data = UserInfoSerializer(subaccounts_qs, many=True).data
#     return JsonResponse({"status": 200, "msg": "", "data": {"subaccounts": subaccounts_data}}, safe=False)


# @api_view(["POST"])
# @permission_classes([IsAuthenticated])
# def user_subaccounts_detail(request, action):
#     if action != "create":
#         mobile = request.data.get("mobile")
#         if mobile is None:
#             return JsonResponse({"status": 500, "msg": "wrong mobile", "data": {}}, safe=False)
#         subaccount_qs = User.objects.filter(mobile=mobile, parent_account=request.user)
#         if not subaccount_qs.exists():
#             return JsonResponse({"status": 404, "msg": "subaccount not exists", "data": {}}, safe=False)
#         subaccount = subaccount_qs.first()

#     if action == "get":
#         subaccount_data = UserInfoSerializer(subaccount).data
#         return JsonResponse(
#             {
#                 "status": 200,
#                 "msg": "",
#                 "data": subaccount_data,
#             },
#             safe=False,
#         )
#     elif action == "create":
#         password = request.data.get("password", "")
#         if len(password) < 6:
#             return JsonResponse({"status": 422, "msg": "password too short", "data": {}}, safe=False)
#         if request.user.is_subaccount:
#             return JsonResponse({"status": 403, "msg": "not allowed", "data": {}}, safe=False)
#         user_subaccounts_quota = settings.SUBACCOUNTS_QUOTA[request.user.membership_type]
#         user_current_subaccounts_qs = User.objects.filter(parent_account=request.user)
#         user_current_subaccounts_count = user_current_subaccounts_qs.count()
#         if user_current_subaccounts_count >= user_subaccounts_quota:
#             return JsonResponse({"status": 402, "msg": "not enough quota", "data": {}}, safe=False)
#         parent_mobile = request.user.mobile
#         subaccounts_mobiles = user_current_subaccounts_qs.values_list("mobile", flat=True)
#         index = 1
#         valid_subaccount_mobile = f"{parent_mobile}-{index}"
#         while valid_subaccount_mobile in subaccounts_mobiles:
#             index += 1
#             valid_subaccount_mobile = f"{parent_mobile}-{index}"
#         user = User.objects.create(
#             username=valid_subaccount_mobile,
#             mobile=valid_subaccount_mobile,
#             mobile_verified=True,
#             country_code=request.user.country_code,
#             language=request.user.language,
#             two_letter_region_code=request.user.two_letter_region_code,
#             credits=0,
#             register_from="SA",
#             is_subaccount=True,
#             parent_account=request.user,
#         )
#         user.set_password(password)
#         user.save()
#         return JsonResponse({"status": 200, "msg": "", "data": {}}, safe=False)
#     elif action == "update":
#         password = request.data.get("password", "")
#         if len(password) < 6:
#             return JsonResponse({"status": 422, "msg": "password too short", "data": {}}, safe=False)
#         subaccount.set_password(password)
#         subaccount.save()
#         return JsonResponse({"status": 200, "msg": "", "data": {}}, safe=False)
#     elif action == "delete":
#         subaccount.delete()
#         return JsonResponse({"status": 200, "msg": "", "data": {}}, safe=False)
