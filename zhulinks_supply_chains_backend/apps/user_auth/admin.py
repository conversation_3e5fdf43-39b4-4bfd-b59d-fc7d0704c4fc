# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON> <PERSON>
# @Date:   2022-03-31 14:09:49
# @Last Modified by:   <PERSON><PERSON> <PERSON>
# @Last Modified time: 2023-04-04 15:05:08
import csv

from django.contrib import admin
from django.http import HttpResponse
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.forms import UserChangeForm

from user_auth.models import (
    User,
    UserInformationForStaff,
    RegisterReferral,
    UserRedeemCodeRecord,
    UserCreditsChangeRecord,
    UserQuotaChangeRecord,
    UserNotification,
    LoggedInUser,
    UserVerification,
    UserBuyMembershipRecord,
    UserDailyQuota,
    UserDailyQuotaRecord,
)


class CsvMixin:
    def export_as_csv(self, request, queryset):
        meta = self.model._meta
        field_names = [field.name for field in meta.fields]

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename={}.csv".format(meta)
        writer = csv.writer(response)

        writer.writerow(field_names)
        for obj in queryset:
            writer.writerow([getattr(obj, field) for field in field_names])

        return response

    export_as_csv.short_description = "导出CSV"


class MyUserChangeForm(UserChangeForm):
    class Meta(UserChangeForm.Meta):
        model = User


@admin.register(User)
class MyUserAdmin(UserAdmin, CsvMixin):
    form = MyUserChangeForm
    list_display = (
        "username",
        "mobile",
        "date_joined",
        "membership_type",
        "register_from",
        "credits",
        "inviter",
        "wechat_union_id",
    )
    actions = ["export_as_csv"]

    fieldsets = UserAdmin.fieldsets + (
        (
            "基本信息",
            {
                "fields": (
                    "nickname",
                    "mobile",
                    "email_verified",
                )
            },
        ),
        ("语言地区", {"fields": ("language", "two_letter_region_code")}),
        ("积分系统", {"fields": ("credits", "register_referral")}),
        (
            "会员",
            {
                "fields": (
                    "membership_level",
                    "membership_type",
                    "membership_quota",
                    "membership_quota_next_reset_datetime",
                )
            },
        ),
        (
            "微信",
            {
                "fields": (
                    "wechat_id",
                    "wechat_union_id",
                    "wechat_mini_program_open_id",
                    "wx_oap_open_id",
                )
            },
        ),
        ("其他", {"fields": ("inviter", "is_subaccount")}),
    )

    ordering = ("-date_joined",)
    list_filter = ("register_from", "membership_type")


@admin.register(UserInformationForStaff)
class UserInformationForStaffAdmin(admin.ModelAdmin):
    list_display = ("user", "brief")
    search_fields = ["user__mobile"]


@admin.register(RegisterReferral)
class RegisterReferralAdmin(admin.ModelAdmin):
    list_display = ("owner", "code")
    search_fields = ["owner__mobile", "code"]


@admin.register(UserRedeemCodeRecord)
class UserRedeemCodeRecordAdmin(admin.ModelAdmin):
    list_display = ("code", "user", "create_time", "redeem_time", "expire_time")
    search_fields = ["user__mobile", "code"]


@admin.register(UserCreditsChangeRecord)
class UserCreditsChangeRecordAdmin(admin.ModelAdmin):
    list_display = ("user", "action", "credits_changed", "credits_after", "create_time")
    search_fields = ["user__mobile"]
    list_filter = ("action",)


@admin.register(UserNotification)
class UserNotificationAdmin(admin.ModelAdmin):
    list_display = ("user", "category", "title", "read", "create_time")
    search_fields = ["user__mobile", "title"]
    list_filter = ("category", "read")


@admin.register(LoggedInUser)
class LoggedInUserAdmin(admin.ModelAdmin):
    list_display = ("user", "last_login_time")
    search_fields = ["user__mobile"]


@admin.register(UserVerification)
class UserVerificationAdmin(admin.ModelAdmin):
    list_display = ("user", "verify_field", "is_verified", "create_time", "verify_time")


@admin.register(UserBuyMembershipRecord)
class UserBuyMembershipRecordAdmin(admin.ModelAdmin):
    list_display = ("user", "membership_type", "days", "create_time", "expire_time", "create_from")
    search_fields = ["user__mobile"]
    list_filter = ("membership_type", "create_from")


@admin.register(UserQuotaChangeRecord)
class UserQuotaChangeRecordAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "action",
        "resource",
        "quota_changed",
        "quota_after",
        "create_time",
    )
    search_fields = ["user__mobile"]
    list_filter = ("resource", "action")


@admin.register(UserDailyQuota)
class UserDailyQuotaAdmin(admin.ModelAdmin):
    list_display = ("user", "update_time")


@admin.register(UserDailyQuotaRecord)
class UserDailyQuotaRecordAdmin(admin.ModelAdmin):
    list_display = ("user", "create_time")
