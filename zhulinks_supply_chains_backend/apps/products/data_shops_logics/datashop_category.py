# -*- coding: utf-8 -*-

from common.decorators import timing_decorator
from companies.models import DataShop
from products.data_shops_logics import execute_product_request
from products.data_shops_logics.datashop_brands import init_brands
from products.models import DataCategory, DataShopCategoryRelate
from utils.doudian.api.shop_getShopCategory.ShopGetShopCategoryRequest import ShopGetShopCategoryRequest


def bulk_insert_or_update_data_shop_category(data_shop_pk: int, category_list: list, inited: bool = False):
    """
    批量插入或者更新抖店数据分类
    :param data_shop_pk: 数据店铺主键
    :param category_list:
    "category_list": [
      {
        "id": "20000",
        "is_leaf": "false",
        "level": "1",
        "name": "类目名称",
        "parent_id": "0"
      }
    ]
    :return:
    """

    try:
        datashop = DataShop.objects.get(pk=data_shop_pk)
    except DataShop.DoesNotExist:
        raise ValueError(f"店铺主键`{data_shop_pk}`不存在")

    category_id_list = [c["id"] for c in category_list if "id" in c]
    parent_id_list = [c["parent_id"] for c in category_list if "parent_id" in c and c["parent_id"] not in [0, "0", None]]

    categories = DataCategory.objects.filter(category_id__in=category_id_list)
    # 父级类目
    parent_categories = DataCategory.objects.filter(category_id__in=parent_id_list)

    exist_category_map = {category.category_id: category for category in categories}
    exist_parent_category_map = {parent_category.category_id: parent_category for parent_category in parent_categories}

    need_create_objs = []
    need_update_objs = []

    for _category in category_list:
        if "id" not in _category:
            continue
        category_id = str(_category["id"])

        if category_id not in exist_category_map:
            need_create_objs.append(
                DataCategory(
                    category_id=category_id,
                    name=_category["name"],
                    level=_category["level"],
                    shop_parent_id=_category["parent_id"],
                    is_leaf=_category["is_leaf"],
                    parent=exist_parent_category_map.get(str(_category["parent_id"]), None),
                )
            )
        else:
            _need_update_obj = exist_category_map[category_id]
            _need_update_obj.name = _category["name"]
            _need_update_obj.level = _category["level"]
            _need_update_obj.shop_parent_id = _category["parent_id"]
            _need_update_obj.is_leaf = _category["is_leaf"]
            _need_update_obj.parent = exist_parent_category_map.get(str(_category["parent_id"]), None)
            need_update_objs.append(_need_update_obj)
    created_rows = updated_rows = 0
    if need_create_objs:
        created_rows = len(DataCategory.objects.bulk_create(need_create_objs, batch_size=1000))

    if need_update_objs:
        updated_rows = DataCategory.objects.bulk_update(
            need_update_objs,
            fields=[
                "name",
                "level",
                "shop_parent_id",
                "is_leaf",
                "parent",
                "inited",
            ],
            batch_size=1000,
        )
    # 删除旧数据
    DataShopCategoryRelate.objects.filter(datashop_id=datashop.pk, datacategory__category_id__in=category_id_list).delete()

    new_categories = DataCategory.objects.filter(category_id__in=category_id_list)
    DataShopCategoryRelate.objects.bulk_create(
        [
            DataShopCategoryRelate(
                datashop=datashop,
                datacategory=category,
                sub_inited=inited,
            )
            for category in new_categories
        ]
    )

    print(f">>> 店铺`{data_shop_pk}`创建了: {created_rows}, 更新了: {updated_rows}")


def fetch_categories(shop_pk: int, category_id: int = 0):
    request = ShopGetShopCategoryRequest()
    params = request.getParams()
    params.cid = category_id
    response = execute_product_request(shop_pk, request)
    return response.data


def process_categories(data_shop_pk, categories_response: list, inited: bool = False):
    if not categories_response:
        return

    bulk_insert_or_update_data_shop_category(data_shop_pk, categories_response, inited)

    for category in categories_response:
        is_leaf = category["is_leaf"]
        parent_id = category["id"]
        if not is_leaf:
            sub_categories = fetch_categories(data_shop_pk, parent_id)
            process_categories(data_shop_pk, sub_categories)


@timing_decorator(debug=True)
def recursive_category(data_shop_pk: int, inited: bool = False):
    # 递归创建分类
    categories_response = fetch_categories(data_shop_pk, 0)
    process_categories(data_shop_pk, categories_response, inited)


def init_first_level_category(data_shop_pk: int):
    # 初始化第一级分类
    categories_response = fetch_categories(data_shop_pk, 0)
    if not categories_response:
        return
    bulk_insert_or_update_data_shop_category(data_shop_pk, categories_response)
