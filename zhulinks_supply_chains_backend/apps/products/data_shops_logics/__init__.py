# -*- coding: utf-8 -*-
from typing import Any

from common.basics.exceptions import APIViewException
from companies.models import dy_product_application, dy_order_application, DataApplicationShops
from products import logger
from utils.doudian.clients import DyTokenClient
from utils.doudian.core.AccessToken import AccessToken
from utils.doudian.sessions.redisstorage import RedisStorage
from utils.redis_lock import gen_redis_conn


def dy_client(application) -> DyTokenClient:
    conn = gen_redis_conn()
    storage = RedisStorage(conn)
    ak = application.app_key
    a_sec = application.app_secret
    client = DyTokenClient(ak, a_sec, storage)
    return client


def get_data_shop_access_token(app_type: str, data_shop_pk: int, need_verify_is_auth=True) -> tuple[AccessToken, Any]:
    if app_type == "product":
        try:
            application = dy_product_application()
            relate_info = DataApplicationShops.objects.get(application=application, data_shop_id=data_shop_pk)
        except DataApplicationShops.DoesNotExist:
            raise APIViewException(err_message=f"该店铺未关联抖店商品应用")
    elif app_type == "order":
        application = dy_order_application()
        try:
            relate_info = DataApplicationShops.objects.get(application=application, data_shop_id=data_shop_pk)
        except DataApplicationShops.DoesNotExist:
            raise APIViewException(err_message=f"该店铺未关联抖店订单应用")
    else:
        raise APIViewException(err_message="未知店铺,请联系管理员")

    if not relate_info.is_authorized:
        raise APIViewException(err_message=f"该店铺尚未授权,请联系管理员")

    client = dy_client(application)

    if relate_info.authorized_type == 1:
        if not relate_info.auth_code:
            raise APIViewException(err_message=f"缺少授权code")
        ac_token = client.get_token_by_code(relate_info.auth_code)
    elif relate_info.authorized_type == 2:
        ac_token = client.get_token_by_shop_id(relate_info.data_shop.shop_id)
    elif relate_info.authorized_type == 3:
        if not relate_info.auth_code:
            raise APIViewException(err_message=f"缺少授权code")
        ac_token = client.get_token_by_shop_id_and_code(relate_info.data_shop.shop_id, relate_info.auth_code)
    else:
        raise APIViewException(err_message="错误抖店店铺,请联系管理员")

    return ac_token, client.get_config()


def execute_product_request(shop_pk: int, sdk_request):
    accessToken, config = get_data_shop_access_token("product", shop_pk)
    sdk_request.config = config
    response = sdk_request.execute(accessToken)
    if response.code != 10000:
        logger.warning(response.__dict__)

    return response


def execute_order_request(shop_pk: int, sdk_request):
    for i in range(3):
        accessToken, config = get_data_shop_access_token("order", shop_pk)
        sdk_request.config = config
        response = sdk_request.execute(accessToken)
        # 售后单系统错误重试
        if response.code == 20000:
            continue

        if response.code != 10000:
            logger.warning(response.__dict__)

        return response
    else:
        raise ValueError(f"execute_order_request重试错误: {shop_pk},{sdk_request.params.__dict__()}")
