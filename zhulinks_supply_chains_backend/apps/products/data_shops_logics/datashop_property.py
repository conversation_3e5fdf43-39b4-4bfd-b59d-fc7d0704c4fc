# -*- coding: utf-8 -*-
import time

from products.data_shops_logics import execute_product_request
from products.models import DataCateProperties
from utils.doudian.api.product_getCatePropertyV2.ProductGetCatePropertyV2Request import ProductGetCatePropertyV2Request


def fetch_property_list(shop_pk, category_leaf_id: int = 0):
    request = ProductGetCatePropertyV2Request()
    param = request.getParams()
    param.category_leaf_id = category_leaf_id
    response = execute_product_request(shop_pk, request)
    return response.data


def init_properties(shop_pk, data_category_pk, data_category_id: int | str):
    """
    初始化分类属性
    todo: 先物理删除(逻辑删除),重建属性
    :param shop_pk: 店铺主键
    :param data_category_pk: 分类主键
    :param data_category_id: 平台分类id
    :return:
    """
    start = time.time()
    property_response = fetch_property_list(shop_pk, data_category_id)
    if not property_response:
        return

    property_data_list = property_response["data"]
    property_id_list = [p["property_id"] for p in property_data_list]
    properties_qs = DataCateProperties.objects.filter(category_id=data_category_pk, property_id__in=property_id_list)

    properties_map = {_property.property_id: _property for _property in properties_qs}

    need_create_objs = []
    need_update_objs = []

    for property_data in property_data_list:
        property_id = str(property_data["property_id"])

        if property_id not in properties_map:
            need_create_objs.append(
                DataCateProperties(
                    category_id=data_category_pk,
                    diy_type=property_data["diy_type"],
                    has_sub_property=property_data["has_sub_property"],
                    important_type=property_data["important_type"],
                    measure_templates=property_data["measure_templates"],
                    property_id=property_id,
                    property_name=property_data["property_name"],
                    property_type=property_data["property_type"],
                    relation_id=property_data["relation_id"],
                    required=int(property_data["required"]),
                    sequence=int(property_data["sequence"]),
                    status=int(property_data["status"]),
                    value_type=property_data["type"],
                    options=property_data["options"],
                )
            )
        else:
            _tmp_obj = properties_map.get(property_id)
            _tmp_obj.diy_type = property_data["diy_type"]
            _tmp_obj.has_sub_property = property_data["has_sub_property"]
            _tmp_obj.important_type = property_data["important_type"]
            _tmp_obj.measure_templates = property_data["measure_templates"]
            _tmp_obj.property_name = property_data["property_name"]
            _tmp_obj.property_type = property_data["property_type"]
            _tmp_obj.relation_id = property_data["relation_id"]
            _tmp_obj.required = int(property_data["required"])
            _tmp_obj.sequence = int(property_data["sequence"])
            _tmp_obj.status = int(property_data["status"])
            _tmp_obj.value_type = property_data["type"]
            _tmp_obj.options = property_data["options"]

            need_update_objs.append(_tmp_obj)

    created_rows = len(DataCateProperties.objects.bulk_create(need_create_objs))
    update_rows = DataCateProperties.objects.bulk_update(
        need_update_objs,
        fields=[
            "diy_type",
            "has_sub_property",
            "important_type",
            "measure_templates",
            "property_name",
            "property_type",
            "relation_id",
            "required",
            "sequence",
            "status",
            "value_type",
            "options",
        ],
    )
    print(f">>>分类属性`{data_category_id}`: 创建{created_rows}条, 修改:{update_rows}条, cost: {time.time() - start}")
