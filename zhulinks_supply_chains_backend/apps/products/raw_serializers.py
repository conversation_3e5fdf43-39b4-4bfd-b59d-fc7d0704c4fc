# -*- coding: utf-8 -*-


class RawSKUSerializer:
    def __init__(self, instance, many, context):
        self._instance = instance
        self._fields = [
            "id",
            "sku_id",
            "physical_inventory",
            "can_use_inventory",
            "safety_inventory",
            "warehouse_inventory",
            "spec_code",
            "cost_price",
            "retail_price",
            "weight",
            "sales",
            "image",
        ]

    @property
    def data(self):
        return [{field: getattr(_ins, field, None) for field in self._fields} for _ins in self._instance]


class DBModeRawSKUSerializer:
    def __init__(self, instance, many, context):
        self._instance = instance
        self._fields = [
            "id",
            "sku_id",
            "physical_inventory",
            "can_use_inventory",
            "safety_inventory",
            "warehouse_inventory",
            "spec_code",
            "cost_price",
            "retail_price",
            "weight",
            "sales",
            "image",
        ]

        self._private_field = ["safety_inventory", "warehouse_inventory"]
        self._map_field = {"cost_price": "distributor_market_price"}

    @property
    def data(self):
        return [{field: getattr(_ins, self._map_field.get(field, field), None) if field not in self._private_field else None for field in self._fields} for _ins in self._instance]
