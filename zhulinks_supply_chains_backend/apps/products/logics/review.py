# -*- coding: utf-8 -*-
from datetime import datetime

from rest_framework.request import Request

from common.basics.exceptions import DataNotFoundException
from common.formats import DATETIME_FORMAT
from products.models import Product
from products.serializers import ProductReviewInfoSerializer


def get_product_review_info(
    request: Request,
    product_id: int,
    current_user_type: str,
):

    try:
        if current_user_type == "OP":
            product = Product.get_operator_products(request).get(product_id=product_id)
        else:
            product = Product.get_distributor_products(request).get(product_id=product_id, is_deleted=False)
    except Product.DoesNotExist:
        raise DataNotFoundException

    if current_user_type == "OP":
        reviews = product.productreview_set.all()
        re_data = ProductReviewInfoSerializer(instance=reviews, many=True).data
        process_level_order = {
            "BASIC_REVIEW": 1,
            "PRICE_REVIEW": 2,
            "QA_REVIEW": 3,
        }
        sorted_data = sorted(
            re_data,
            key=lambda x: (
                process_level_order.get(x["process_level"]),
                -datetime.strptime(x["create_date"], DATETIME_FORMAT).timestamp(),
            ),
        )
        return sorted_data
    else:
        reviews = product.productreview_set.filter(process_level="QA_REVIEW").order_by("-create_date")
        re_data = ProductReviewInfoSerializer(instance=reviews, many=True).data
        return re_data
