# -*- coding: utf-8 -*-
import copy
import decimal
import json
import traceback
import uuid
from datetime import datetime, timedelta
from hashlib import md5
from typing import Union

from common.basics.exceptions import APIViewException
from common.basics.views import set_log_params
from common.models import DownloadTasks
from common.utils import ProductBulkExtraTags, diff_models, get_current_user_type
from companies.logic import get_combine_product_default_company
from companies.models import Company, Distributor
from django.core.cache import cache
from django.db import IntegrityError, transaction
from django.db.models import Case, Count, F, Max, OuterRef, Prefetch, Q, Subquery, Sum, Value, When
from django.db.models.functions import Coalesce
from django.http import FileResponse
from django.utils import timezone
from orders.models import OriginalOrder
from products import logger
from products.bulk_query import (
    batch_calc_prod_trend,
    bulk_query_items_mark,
    bulk_query_labels,
    bulk_query_latest_history_price,
    bulk_query_product_category_ranks,
    bulk_query_product_refund_rate,
    bulk_query_skus_specs_detail,
    get_sku_download_specs_option_list,
)
from products.filtersets import PlanMapListFilterSet, PlanProfitMarginFilterSet, PlanV2FilterSet
from products.models import (
    CostItemConfig,
    CostSubItem,
    EstimatedAcceptanceRateRules,
    HistoryPrice,
    Product,
    ProductAttrList,
    ProductAttrOption,
    ProductAttrValues,
    ProductCategoryList,
    ProductGift,
    ProductLabels,
    ProductLabelsRelate,
    ProductLinkDistributor,
    ProductReview,
    ProductReviewExtra,
    ProductSelectionItem,
    ProductSelectionItemSKU,
    ProductSelectionPlan,
    ProductSelectionPlanCategory,
    ProductSelectionPlanCompany,
    ProductSelectionPlanRecord,
    ProductSelectionPlanRecordType,
    SpecsKey,
    SpecsValue,
    StockKeepingUnit,
    SubProduct,
    SubStockKeepingUnit,
    bulk_query_product_hosting_state,
    generate_combine_spec_code,
    get_default_combine_product_specs_key,
    get_default_combine_product_specs_value,
)
from products.models_v2 import ProductConfig
from products.raw_serializers import DBModeRawSKUSerializer, RawSKUSerializer
from products.serializers import (
    CombineProductUpdateSer,
    CombineProductValidateSer,
    CombineSKUValidateSer,
    CombineSubSKUValidateSer,
    CostSubItemDetailSerializer,
    DBDistributorModePlanListSer,
    EstimatedAcceptanceRateRulesDetailSerializer,
    MyProductCreateSer,
    OperateStockKeepingUnitProductDetailSerializer,
    OPPlanListSer,
    ProductAttrOptionCreateOrUpdateSer,
    ProductAttrOptionInfoSer,
    ProductAttrOptionSerializer,
    ProductCategoryListSerializer,
    ProductCopySer,
    ProductSerializer,
    SelectionMapItemSer,
    SkuCopyInfoSer,
    StockKeepingUnitSerializer,
)
from products.tasks import (
    add_images_to_qdrant,
    async_move_plan_item_to_new_plan_invoker,
    async_selection_plan_download_task,
    copy_oss_images,
    flush_user_read_selection_plan_status,
    send_selection_plan_listener_task,
    sync_combine_product_to_jst,
    sync_product_to_JST,
    w_plan_record,
)
from rest_framework import serializers
from rest_framework.request import Request
from users.models import User, update_cache_real_name_with_user_id
from users.tasks import ceate_notifications
from utils.download_tmp import DBPlanProfitMariginActualTmpl, DBPlanProfitMariginTmpl, OPPlanProfitMariginActualTmpl, OPPlanProfitMariginTmpl, get_excel_async
from utils.http_handle import FieldsError, IResponse, custom_django_filter, custom_filter, get_range_query
from utils.redis_lock import read_lock, setnx_lock

"""
运营商、分销商共用logic
"""


def get_category_name_by_id(category_id: int | str) -> dict:
    cache_key = f"category_{category_id}"
    data = cache.get(cache_key)
    if data:
        return data

    cate_qs = ProductCategoryList.objects.filter(id=category_id).first()
    if cate_qs:
        data = ProductCategoryListSerializer(instance=cate_qs).data
        cache.set(cache_key, data, timeout=60 * 60)
        return data

    cache.set(cache_key, "", timeout=60)
    return {"id": 0, "name": ""}


def get_category_name_by_id_list(category_list: list):
    """
    获取商品分类列表名字
    """
    assert isinstance(category_list, list), ValueError("category_list must be list")
    re_data = []

    for category_id in category_list:
        cache_key = f"category_{category_id}"
        data = cache.get(cache_key)
        if data is None:
            cate_qs = ProductCategoryList.objects.filter(id=category_id).first()
            if cate_qs:
                data = ProductCategoryListSerializer(instance=cate_qs).data
                re_data.append(data)
                cache.set(cache_key, data, timeout=60 * 60)
        else:
            re_data.append(data)
    return re_data


def attr_download_info_handler(download_data: dict, product: Product):
    """商品参数下载统一处理"""
    # 商品参数下载
    product_params_field = ["款式", "镶嵌", "产地"]

    attr_options_qs = ProductAttrOption.objects.filter(product=product, attr__name__in=product_params_field)
    attr_options_data = ProductAttrOptionInfoSer(instance=attr_options_qs, many=True).data
    attr_option_map = {attr_option.get("name"): attr_option.get("value") for attr_option in attr_options_data}

    for field in product_params_field:
        download_data[field] = attr_option_map.get(field, "") or ""


def get_cs_selection_plan_items(request, plan_id, product_ser, sku_ser):
    """
    客服货盘计划的商品列表
    """
    try:
        current_user = request.user
        current_user_type = request.auth.get("user_type")

        re_data = {}
        raw_params = request.query_params.copy()
        # 前端参数转换
        and_Q = None
        if raw_params.get("name"):
            raw_params["product__name"] = raw_params.pop("name")[0]

        if raw_params.get("product_deleted"):
            raw_params["product__is_deleted"] = raw_params.pop("product_deleted")[0]

        if raw_params.get("product_id"):
            raw_params["product__product_id"] = raw_params.pop("product_id")[0]
        if raw_params.get("code"):
            tmp_code = raw_params.pop("code")[0]
            if str(tmp_code).startswith("J"):
                tmp_code = tmp_code[1:]

            if current_user_type == "DB":
                if not and_Q:
                    and_Q = Q()
                and_Q.add(Q(**{"product__code__icontains": tmp_code}), Q.OR)
                and_Q.add(Q(**{"sub_product__code__icontains": tmp_code}), Q.OR)
            else:
                raw_params["product__code"] = tmp_code

        if raw_params.get("category"):
            raw_params["product__category"] = raw_params.pop("category")[0]
        if raw_params.get("physical_inventory"):
            raw_params["product__physical_inventory__range"] = raw_params.pop("physical_inventory")[0]
        if raw_params.get("history_price"):
            history_price = json.loads(raw_params.pop("history_price")[0])
            if history_price:
                and_Q = get_range_query(
                    "product__min_history_price",
                    "product__max_history_price",
                    history_price,
                )
        if raw_params.get("orders"):
            orders = json.loads(raw_params.pop("orders")[0])
            orders = [f"product__{i}" for i in orders]
            raw_params["orders"] = json.dumps(orders)
        else:
            raw_params["orders"] = json.dumps(["order"])

        if raw_params.get("doudian_id"):
            raw_params["product__doudian_id"] = raw_params.pop("doudian_id")[0]

        try:
            query_condition = {"plan_id": plan_id}

            if current_user_type == "DB":
                query_condition["distributor_id"] = current_user.distributor.distributor_id
            selection_plan = ProductSelectionPlan.objects.get(**query_condition)
        except ProductSelectionPlan.DoesNotExist:
            raise APIViewException(code=400, err_message="data not found")

        filters = {
            "is_deleted": False,
            "selection_plan_id": plan_id,
        }

        hybrid_fields = [
            "product__name",
            "product__product_id",
            "product__code",
            "product__stockkeepingunit__link_code",
            "product__stockkeepingunit__spec_code",
        ]

        if current_user_type == "DB":
            hybrid_fields.append("sub_product__code")

            if raw_params.get("hybrid_search"):
                _tmp_hybrid_search = raw_params.pop("hybrid_search")[0]
                if str(_tmp_hybrid_search).startswith("J"):
                    raw_params["hybrid_search"] = str(_tmp_hybrid_search)[1:]

        _, _, item_qs = custom_filter(
            raw_params,
            ProductSelectionItem,
            array_fields=["product__category"],
            like_fields=[
                "product__name",
                "product__code",
                "sub_product__code",
                "product__product_id",
            ],
            hybrid_fields=hybrid_fields,
            query_Q=and_Q,
            **filters,
        )
        re_data["plan_id"] = selection_plan.plan_id
        re_data["name"] = selection_plan.name
        re_data["live_date_start"] = selection_plan.live_date_start
        re_data["live_date_end"] = selection_plan.live_date_end
        re_data["state"] = selection_plan.state
        re_data["count"] = 0
        # 上播率
        live_groupby_counts = (
            selection_plan.productselectionitem_set.filter(is_deleted=False)
            .values("has_live")
            .annotate(calc_count=Count("id"))
            .values(
                "has_live",
                "calc_count",
            )
        )
        re_data["live_rate"] = 0
        item_live_total_counts = 0
        live_counts = 0
        for i in live_groupby_counts:
            item_live_total_counts += i["calc_count"]

            if i["has_live"]:
                live_counts += i["calc_count"]

        if item_live_total_counts:
            re_data["live_rate"] = round(live_counts / item_live_total_counts, 4)

        if not item_qs:
            return re_data

        # 获取所有plan分类id
        plan_category_ids = {item.plan_category_id for item in item_qs}
        # item的所有商品id,批量查询product
        item_product_ids = {item.product_id for item in item_qs}
        # plan category 批量查询
        plan_category_qs = ProductSelectionPlanCategory.objects.filter(id__in=list(plan_category_ids)).select_related("category").order_by("order")

        # company_id对应的plan_category
        company_category_map = {}
        for plan_category in plan_category_qs:
            category_plan_company_id = plan_category.plan_company_id
            if category_plan_company_id not in company_category_map:
                company_category_map[category_plan_company_id] = [plan_category]
            else:
                company_category_map[category_plan_company_id].append(plan_category)

        # 排序
        for k, v in company_category_map.items():
            company_category_map[k] = sorted(v, key=lambda x: getattr(x, "order"))

        # category对应的item
        category_item_map = {}

        for item in item_qs:
            item_category_id = item.plan_category_id
            if item_category_id not in category_item_map:
                category_item_map[item_category_id] = [item]
            else:
                category_item_map[item_category_id].append(item)

        # 排序
        for k, v in category_item_map.items():
            category_item_map[k] = sorted(v, key=lambda x: getattr(x, "order"))

        # 商品map
        products = Product.objects.filter(product_id__in=item_product_ids)

        product_map = {product.product_id: product for product in products}
        product_ids_map = {product.id: product.product_id for product in products}

        # 批量查询sub_product
        sub_products_map = {}
        if current_user_type == "DB":
            sub_product_ids = [i.sub_product_id for i in item_qs if i.sub_product_id]
            sub_products = SubProduct.objects.filter(
                product_id__in=sub_product_ids,
                owner=request.user.distributor,
                is_deleted=False,
            )
            sub_products_map = {sub_product.parent_product_id: sub_product for sub_product in sub_products}

        # 批量查询sku信息
        skus = StockKeepingUnit.objects.prefetch_related("specs_value", "specs_value__spec_key").filter(product_id__in=product_ids_map.keys(), become_history=False)
        sku_map = {}
        sku_id_set = set()

        for sku in skus:
            sku_id_set.add(sku.id)
            if sku.product_id not in sku_map:
                sku_map[sku.product_id] = [sku]
                continue

            sku_map[sku.product_id].append(sku)

        # 批量查询history
        group_by_history_price_qs = (
            HistoryPrice.objects.values("sku_id")
            .annotate(max_id=Max("id"))
            .filter(sku_id__in=sku_id_set)
            .values(
                "max_id",
                "sku_id",
            )
        )
        history_price_id_list = [history_price.get("max_id") for history_price in group_by_history_price_qs]
        history_price_qs = HistoryPrice.objects.filter(id__in=history_price_id_list).only("sku_id", "history_price")
        history_price_map = {history_price.sku_id: history_price.history_price for history_price in history_price_qs}
        # 获取所有公司的id
        plan_companies_ids = {plan_category.plan_company_id for plan_category in plan_category_qs}
        plan_companies = ProductSelectionPlanCompany.objects.filter(id__in=list(plan_companies_ids)).select_related("company").order_by("order")
        total_count = 0
        tree_structured_data = []

        # 批量查询category
        category_ids = set()
        for p in products:
            for c in p.category:
                category_ids.add(c)

        category_list = ProductCategoryList.objects.filter(id__in=list(category_ids)).only("id", "name")
        category_map = {category.id: {"id": category.id, "name": category.name} for category in category_list}

        # 批量查询趋势
        # 查询所有的趋势
        trend_map = batch_calc_prod_trend(products)

        # 批量查询label
        product_labels_dict = bulk_query_labels(item_product_ids)

        for plan_company in plan_companies:
            company_product_count = 0
            company_data = {
                "plan_company_id": plan_company.id,
                "company_id": plan_company.company.company_id,
                "company_name": plan_company.company.name,
                "order": plan_company.order,
                "count": 0,
                "data": [],
            }

            for plan_category in company_category_map.get(plan_company.id):
                category_product_count = 0
                category_data = {
                    "plan_category_id": plan_category.id,
                    "category_id": plan_category.category.id,
                    "category_name": plan_category.category.name,
                    "order": plan_category.order,
                    "count": 0,
                    "data": [],
                }

                for item in category_item_map.get(plan_category.id):
                    # 检查近7日是否有订单
                    product_instance = product_map.get(item.product_id)
                    if not product_instance:
                        continue

                    _data = product_ser(
                        instance=product_instance,
                        context={
                            "request": request,
                            "sub_products_map": sub_products_map,
                        },
                    ).data
                    _category = _data.get("category")

                    #
                    _data["product_deleted"] = item.product_deleted

                    if _category:
                        # 数据库保存的string类型数据，需要改成int获取map数据
                        _data["category"] = [category_map.get(int(_category_id)) for _category_id in _category]

                    _data["remark"] = item.remark
                    _data["order"] = item.order
                    _data["plan_product_id"] = item.id

                    # 确认状态
                    _data["product_confirm_state"] = item.product_confirm_state

                    # 商品库存和价格趋势
                    _data["inventory_trend"] = trend_map.get(f"{product_instance.product_id}_physical_inventory", "")
                    _data["cost_price_trend"] = trend_map.get(f"{product_instance.product_id}_cost_price")

                    # 客服数据
                    _data["link_cs_id"] = item.link_cs_id
                    if item.link_cs:
                        _data["link_cs_name"] = item.link_cs.name
                    else:
                        _data["link_cs_name"] = ""
                    _data["link_cs_remark"] = item.link_cs_remark
                    _data["has_live"] = item.has_live
                    # 标签
                    _data["labels"] = product_labels_dict.get(item.product_id)

                    # skus 数据
                    sku_list = []
                    skus_qs = sku_map.get(product_instance.id, [])

                    for sku_qs in skus_qs:
                        skus_data = sku_ser(instance=sku_qs, context={"request": request}).data
                        # 获取最新历史价
                        skus_data["latest_history_price"] = history_price_map.get(sku_qs.id, None)
                        sku_list.append(skus_data)

                    _data["skus"] = sku_list
                    company_product_count += 1
                    category_product_count += 1
                    total_count += 1
                    category_data["data"].append(_data)
                category_data["count"] = category_product_count
                # 添加分类数据到公司数据中
                company_data["data"].append(category_data)
            company_data["count"] = company_product_count
            # 添加公司数据到树结构中
            tree_structured_data.append(company_data)

        # 上播率
        re_data["data"] = tree_structured_data
        re_data["count"] = total_count
        return re_data
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        raise APIViewException(code=500, err_message=str(e))


def download_cs_selection_plan_product_list_fc(request, plan_id, download_items_ser, download_tmpl):
    """
    客服货盘计划-商品列表下载
    """
    current_user = request.user
    current_user_type = request.auth.get("user_type")
    if current_user_type == "DB" and not current_user.distributor:
        raise APIViewException(code=400, message="user not bind distributor")
    raw_params = request.query_params.copy()
    # 前端参数转换
    if raw_params.get("name"):
        raw_params["name__icontains"] = raw_params.pop("name")[0]
    if raw_params.get("category"):
        raw_params["category"] = raw_params.pop("category")[0]
    if raw_params.get("physical_inventory"):
        raw_params["physical_inventory__range"] = raw_params.pop("physical_inventory")[0]
    if raw_params.get("cost_price"):
        raw_params["__cost_price__range"] = raw_params.pop("cost_price")[0]
    if raw_params.get("history_price"):
        raw_params["__history_price__range"] = raw_params.pop("history_price")[0]

    condition = {
        "plan_id": plan_id,
    }
    if current_user_type == "DB":
        condition["distributor_id"] = current_user.distributor.distributor_id
    try:
        selection_plan = ProductSelectionPlan.objects.get(**condition)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")

    create_user_type = raw_params.get("create_user_type")

    item_query_condition = {
        "selection_plan_id": plan_id,
        "is_deleted": False,
    }
    if create_user_type:
        raw_params.pop("create_user_type")
        item_query_condition["create_user_type"] = create_user_type

    item_qs = ProductSelectionItem.objects.filter(**item_query_condition).values(
        "product",
        "sub_product_id",
        "remark",
        "link_cs__name",
        "link_cs_remark",
        "has_live",
    )
    data_dict = {}
    for item in item_qs:
        data_dict[item["product"]] = {
            "remark": item.get("remark"),
            "link_cs__name": item.get("link_cs__name"),
            "link_cs_remark": item.get("link_cs_remark"),
            "has_live": item.get("has_live"),
        }
    product_ids = data_dict.keys()
    # 按照id列表指定的顺序
    custom_product_id_sort = Case(*[When(product_id=product_id, then=idx) for idx, product_id in enumerate(product_ids)])
    filters = dict(is_deleted=False, product_id__in=product_ids)

    # 获取商品
    page_products, re_data, plan_products_qs = custom_filter(
        raw_params,
        Product,
        array_fields=["category"],
        like_fields=["name", "product_id", "code"],
        hybrid_fields=[
            "name",
            "product_id",
            "code__icontains",
            "stockkeepingunit__link_code",
            "stockkeepingunit__spec_code",
        ],
        force_orders=False,
        **filters,
    )
    re_data = []
    num = 0
    plan_products_qs = plan_products_qs.order_by(custom_product_id_sort)

    # 批量查询sub_product
    sub_products_map = {}
    if current_user_type == "DB":
        sub_product_ids = [i["sub_product_id"] for i in item_qs if i["sub_product_id"]]
        sub_products = SubProduct.objects.filter(
            product_id__in=sub_product_ids,
            owner=request.user.distributor,
            is_deleted=False,
        )
        sub_products_map = {sub_product.parent_product_id: sub_product for sub_product in sub_products}

    for product in plan_products_qs:
        re_product = {
            "货盘ID": selection_plan.plan_id,
            "货盘名称": selection_plan.name,
        }

        product_data = download_items_ser(
            instance=product,
            context={"request": request, "sub_products_map": sub_products_map},
        ).data
        # template data
        for k, v in download_tmpl.items():
            value = product_data.get(k)
            re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value
        # 款式
        attr_download_info_handler(re_product, product)

        # 近7日是否播过
        live_past_7_days = False
        last_7_day = timezone.now() - timezone.timedelta(days=7)
        if product.raworder_set.filter(order_date__gte=last_7_day).exists():
            live_past_7_days = True

        re_product["备注"] = ""
        re_product["是否已播"] = "否"
        if data_dict.get(product.product_id):
            re_product["备注"] = data_dict.get(product.product_id)["remark"]
            re_product["是否已播"] = "是" if data_dict.get(product.product_id)["has_live"] else "否"

        re_product["近7日是否播过"] = "是" if live_past_7_days else "否"

        # category
        category = product_data.get("category")
        category_id_and_name_list = get_category_name_by_id_list(category_list=category)
        name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目"}
        for i in range(len(category_id_and_name_list)):
            re_product[f"{name_key[i]}ID"] = category_id_and_name_list[i].get("id")
            re_product[f"{name_key[i]}名称"] = category_id_and_name_list[i].get("name")
            if i == 3:
                break
        # sku
        skus_qs = StockKeepingUnit.objects.filter(product=product, become_history=False)
        for sku_qs in skus_qs:
            num += 1
            re_sku = {
                "序号": num,
            }
            re_sku.update(re_product)
            re_sku["规格ID"] = sku_qs.sku_id
            # spec
            spec_option_list = get_sku_download_specs_option_list(sku_qs)
            re_sku["规格"] = ";".join(spec_option_list)
            re_sku["建议售价"] = sku_qs.retail_price

            # 链接编码
            re_sku["链接编码"] = sku_qs.link_code
            # 销量
            re_sku["销量"] = sku_qs.sales
            # 需要权限
            # if current_user.has_perm("products.view_cost_price_StockKeepingUnit"):
            #     re_sku["成本价"] = sku_qs.cost_price
            # else:
            #     re_sku["成本价"] = "***"
            re_sku["现货库存"] = sku_qs.physical_inventory
            re_sku["7天补货库存"] = sku_qs.safety_inventory

            # history_price = None
            # history_price_author_id = None
            # history_price_author_name = None
            # latest = HistoryPrice.objects.filter(sku_id=sku_qs.id).order_by("-create_date").first()
            # if latest:
            #     history_price = latest.history_price
            #     if latest.author:
            #         history_price_author_id = latest.author.author_id
            #         history_price_author_name = latest.author.name
            # re_sku["历史价"] = history_price
            # re_sku["历史价主播id"] = history_price_author_id
            # re_sku["历史价主播名"] = history_price_author_name

            re_sku["抖店ID"] = product.doudian_id or ""
            re_sku["客服接待等级"] = ""
            if data_dict.get(product.product_id):
                re_sku["客服接待等级"] = data_dict.get(product.product_id)["link_cs__name"]

            re_sku["客服接待备注"] = ""
            if data_dict.get(product.product_id):
                re_sku["客服接待备注"] = data_dict.get(product.product_id)["link_cs_remark"]

            re_data.append(re_sku)

    filename = f"客服货盘计划：{selection_plan.name}-{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
    byte_buffer = get_excel_async("商品", re_data, image_columns=[6])
    if byte_buffer:
        byte_buffer.seek(0)
    return FileResponse(byte_buffer, filename=filename, as_attachment=True)


def draft_create_product(
    request: Request,
    product_state: int = 4,
    data_source: str = Union["OP", "DB"],
):
    """
    草稿箱商品保存和发布
    :param request: drf request object
    :param product_state: 商品状态. 保存为草稿箱商品的为4; 草稿箱发布的 运营商的是1,分销商的是0
    :param data_source: 运营商或者分销商用户
    :return:
    """

    def _create_product(data):
        if data.get("spec_lists"):
            # 剔除垃圾数据
            new_spec_lists = []
            spec_lists = data.pop("spec_lists")
            for i in spec_lists:
                new_spec_lists.append(
                    {
                        "source_id": i.get("source_id"),
                        "name": i.get("name"),
                        "values": i.get("values"),
                    }
                )
            data["spec_lists"] = new_spec_lists

        # 校验分类
        category = data.get("category")
        all_numbers = all(isinstance(e, int) for e in category)
        if not all_numbers:
            raise ValueError("category elements should be int list")

        data["state"] = product_state
        # 数据来源
        data["data_source"] = data_source

        remark = "运营商上传的商品" if data_source == "OP" else "分销商上传的商品"

        # 如果商品状态为1时, 审核状态为True; 否则都为False
        review_data = {
            "physical_inventory_exact": product_state == 1,
            "quality_qualified": product_state == 1,
            "price_reasonable": product_state == 1,
            "remark": remark,
        }

        # data["state_reason"] = review_data
        product = ProductSerializer(data=data, context={"request": request})
        if product.is_valid():
            instance = product.save()
            # 创建商品审核数据
            review_data["create_user"] = data.get("create_user")
            review_data["update_user"] = data.get("update_user")
            review_data["product_id"] = instance.id
            product_review = ProductReview.objects.create(**review_data)
            return instance
        raise FieldsError(product.errors)

    def _create_sku_and_spec_options(product, skus_data):
        re_data = []
        qdrant_images = []
        for sku_data in skus_data:
            log_data = None
            re_sku = None
            sku_data["product"] = product.id
            if sku_data.get("specs"):
                # 剔除垃圾数据
                new_specs = []
                specs = sku_data.pop("specs")
                for i in specs:
                    new_specs.append(
                        {
                            "name": i.get("name"),
                            "value": i.get("value"),
                        }
                    )
                sku_data["specs"] = new_specs
            if not sku_data.get("spec_code"):
                sku_data["spec_code"] = None

            if sku_data.get("image"):
                qdrant_images.append(sku_data["image"])
            # create sku
            sku = StockKeepingUnitSerializer(data=sku_data, context={"request": request})
            if not sku.is_valid():
                raise FieldsError(sku.errors)
            sku_instance = sku.save()
            re_sku = StockKeepingUnitSerializer(instance=sku_instance, context={"request": request}).data
            re_sku.pop("product")
            re_data.append(re_sku)
        return re_data, qdrant_images

    def _create_product_attr_options(product, attr_options_data):
        new_options = []
        for attr_option_data in attr_options_data:
            # 剔除没值的数据
            if attr_option_data.get("value"):
                attr_id = attr_option_data.pop("attr_id")
                attr_option_data["product"] = product.id
                attr_option_data["attr"] = attr_id
                new_options.append(attr_option_data)
        attr_options = ProductAttrOptionSerializer(data=new_options, many=True)
        if not attr_options.is_valid():
            raise FieldsError(attr_options.errors)
        instances = attr_options.save()
        return instances

    try:
        raw_data = request.data
        current_user = request.user

        raw_data["create_user"] = current_user.user_id
        raw_data["update_user"] = current_user.user_id

        assert raw_data.get("skus") and raw_data.get("attr_options") and raw_data.get("company_id"), "required: skus, attr_options, company_id"

        skus_data = raw_data.pop("skus")
        attr_options_data = raw_data.pop("attr_options")
        company_id = raw_data.pop("company_id")
        try:
            company = Company.objects.get(company_id=company_id)
        except Company.DoesNotExist:
            raise APIViewException(err_message="company_id invalid")

        raw_data["company"] = company.id
        if raw_data.get("address_id"):
            raw_data["address"] = raw_data.pop("address_id")

        try:
            with transaction.atomic():
                # product
                product_instance = _create_product(raw_data)
                # sku && spec options
                sku_data, qdrant_images = _create_sku_and_spec_options(product_instance, skus_data)
                # product attr options
                attr_options_instance = _create_product_attr_options(product_instance, attr_options_data)
        except ValueError as e:
            logger.error(str(e))
            raise APIViewException(err_message=str(e))
        except IntegrityError as e:
            logger.error(str(e))
            raise APIViewException(code=500, err_message=str(e))

        new_product = Product.objects.get(pk=product_instance.id)
        # 初次上传没有历史价，如果有零售价，则将之转为历史价
        # history_data = []
        # for item in sku_data:
        #     if item.get("retail_price"):
        #         data = {
        #             "sku_id": item["sku_id"],
        #             "history_price": item["retail_price"],
        #         }
        #         history_data.append(data)
        # if history_data:
        #     insert_history_price(product_id=product_instance.product_id, raw_data=history_data)

        # 草稿箱的商品不上传图片至qdrant
        if new_product.state != 4:
            images = copy.deepcopy(new_product.main_images)
            if new_product.detail_images:
                images.extend(copy.deepcopy(new_product.detail_images))
            if qdrant_images:
                images.extend(qdrant_images)
            add_images_to_qdrant.delay(new_product.id, list(set(images)))

        resp_data = ProductSerializer(instance=new_product, context={"request": request}).data
        category = resp_data.get("category")
        category_list = get_category_name_by_id_list(category)
        resp_data["category"] = category_list

        resp_data.pop("company")
        resp_data["skus"] = sku_data
        attr_options = ProductAttrOptionSerializer(instance=attr_options_instance, many=True).data
        for attr_option in attr_options:
            attr_option["attr_id"] = attr_option.pop("attr")
            attr_option.pop("product")
        resp_data["attr_options"] = attr_options

        return resp_data
    except IntegrityError as e:
        logger.error(str(e))
        raise APIViewException(code=500, err_message=str(e))
    except Exception as e:
        logger.error(f"{e}, {traceback.format_exc()}")
        raise APIViewException(code=500, err_message=str(e))


def map_item_move_fc(request: Request):
    """
    排品地图商品移动
    :param request:
    :return:
    """

    def _plan_state_checker(plan_id: int):
        """
        检查货盘状态
        """
        try:
            filter_condition = {"plan_id": plan_id}
            if request.user.user_type == "DB":
                if not request.user.distributor:
                    raise ValueError("user not bind distributor")
                filter_condition["distributor__id"] = request.user.distributor_id

            selection_plan = ProductSelectionPlan.objects.get(**filter_condition)
        except ProductSelectionPlan.DoesNotExist:
            raise APIViewException(err_message="data not found")
        # 锁定状态能修改排品顺序
        if selection_plan.state == 0:
            raise APIViewException(err_message="this state can't edit")

    raw_data = request.data

    plan_product_id_list = []
    for data in raw_data:
        plan_product_id = data.get("plan_product_id")
        map_order = data.get("map_order")

        if map_order is None or not plan_product_id:
            raise ValueError("invalid params")

        plan_product_id_list.append(plan_product_id)

    try:
        item = ProductSelectionItem.objects.get(pk=plan_product_id_list[0], is_deleted=False, product__is_deleted=False)
    except ProductSelectionItem.DoesNotExist:
        raise APIViewException(err_message="商品不存在,请刷新页面后重试")

    # 查计划
    _plan_id = item.selection_plan_id
    # 先查货盘计划状态
    _plan_state_checker(_plan_id)
    # 排品模式权限跟货盘编辑权限区分
    plan_lock_key = f"plan_map:{_plan_id}"
    user_id = read_lock(plan_lock_key)
    if request.user.user_id != user_id:
        raise APIViewException(err_message="Not granted editing permissions")

    items = ProductSelectionItem.objects.filter(pk__in=plan_product_id_list, is_deleted=False, product__is_deleted=False)
    if items.count() != len(plan_product_id_list):
        raise APIViewException(err_message="移动商品中有商品已被删除, 请刷新数据后再进行排序")

    need_update_objs = []
    items_map = {item.pk: item for item in items}
    for data in raw_data:
        plan_product_id = data.get("plan_product_id")
        map_order = data.get("map_order")
        if plan_product_id not in items_map:
            raise APIViewException(err_message="商品不存在,请刷新页面后重试")

        items_map[plan_product_id].map_order = map_order
        need_update_objs.append(items_map[plan_product_id])

    ProductSelectionItem.objects.bulk_update(need_update_objs, fields=["map_order"], batch_size=100)
    return IResponse()


def selection_plan_prods_map_fc(request: Request, plan_id: int):
    """
    排品地图商品列表
    :param request:
    :param plan_id:
    :return:
    """
    try:
        re_data = {}
        current_user = request.user

        raw_params = request.query_params.copy()

        current_user_type = request.auth.get("user_type")

        if raw_params.get("orders"):
            orders = json.loads(raw_params.pop("orders")[0])
            orders = [f"product__{i}" for i in orders]
            # 排品地图
            request.query_params.update({"orders": json.dumps(orders)})
        else:
            request.query_params.update({"orders": json.dumps(["map_order"])})

        try:
            query_condition = {"plan_id": plan_id}

            if current_user_type == "DB":
                query_condition["distributor__id"] = current_user.distributor_id

            selection_plan = ProductSelectionPlan.objects.get(**query_condition)
        except ProductSelectionPlan.DoesNotExist:
            return IResponse(code=400, message="data not found")

        # 添加货盘监听
        if current_user.real_name != "飞书跑数据":
            send_selection_plan_listener_task(request, "货盘-排品列表", selection_plan)

        filters = {
            "selection_plan_id": plan_id,
            "is_deleted": False,
            "product__is_deleted": False,
        }
        target = ProductSelectionItem.objects.filter(**filters)

        if current_user_type == "DB":
            cannot_see_product_id_list = (
                ProductConfig.objects.exclude(
                    visible_distributor=[],
                )
                .exclude(visible_distributor__contains=selection_plan.distributor_id)
                .values_list("product_id", flat=True)
            )
            target = target.exclude(product_id__in=cannot_see_product_id_list)

        _, _, item_qs = custom_django_filter(
            request,
            target,
            PlanMapListFilterSet,
            need_serialize=False,
        )

        # 补充计划信息
        re_data["name"] = selection_plan.name
        re_data["live_date_start"] = selection_plan.live_date_start
        re_data["live_date_end"] = selection_plan.live_date_end
        re_data["plan_id"] = selection_plan.plan_id
        re_data["state"] = selection_plan.state

        item_qs = item_qs.select_related("product", "product__company")
        # 上播率计算
        item_live_counts = (
            ProductSelectionItem.objects.values("has_live")
            .annotate(calc_count=Count("id"))
            .filter(selection_plan_id=selection_plan.plan_id, is_deleted=False)
            .values(
                "has_live",
                "calc_count",
            )
        )
        total_count = 0
        live_count = 0
        for i in item_live_counts:
            total_count += i["calc_count"]
            if i["has_live"]:
                live_count += i["calc_count"]
        re_data["plan_inventory_warning_count"] = 0
        re_data["wait_review_count"] = 0
        re_data["live_rate"] = 0

        if total_count:
            re_data["live_rate"] = round(live_count / total_count, 4)

        # 排品地图
        ser_data = SelectionMapItemSer(instance=item_qs, many=True, context={"request": request}).data
        prod_counts = len(ser_data)

        re_data["data"] = ser_data
        re_data["count"] = prod_counts

        plan_inventory_warning_count = 0
        wait_review_count = 0

        for _d in ser_data:
            if _d["can_use_inventory"] < 0:
                plan_inventory_warning_count += 1

            if _d["product_state"] in [4]:
                wait_review_count += 1

        re_data["plan_inventory_warning_count"] = plan_inventory_warning_count
        re_data["wait_review_count"] = wait_review_count
        return IResponse(data=re_data)
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=500, message=str(e))


def common_add_product_to_plan_fc(
    user_id: int,
    plan_id: int,
    product_id: int,
    selection_plan: ProductSelectionPlan,
    post_sku_data: list,
    create_user_type="OP",
    **kwargs,
):
    """
    加入货盘计划
    :param user_id: 当前用户id
    :param plan_id: 货盘计划id
    :param product_id: 商品product_id
    :param selection_plan: 货盘计划object
    :param post_sku_data: sku列表信息
    :param create_user_type: 创建用户的端
    :param kwargs:
    :return:
    """
    if not isinstance(post_sku_data, list):
        raise ValueError("提交信息错误")
    if not post_sku_data:
        raise ValueError("sku信息不能为空")

    # 分销模式
    is_distributor_mode = kwargs.get("distributor_mode") or selection_plan.distributor.distributor_mode == 2

    # 确定是否已经在计划中
    item_qs = ProductSelectionItem.objects.filter(selection_plan_id=plan_id, product_id=product_id, is_deleted=False)
    if item_qs.exists():
        raise ValueError("商品已在货盘中")

    try:
        product = Product.objects.get(product_id=product_id, is_deleted=False)
    except Product.DoesNotExist:
        raise ValueError("数据不存在")
    # 判断商品是否在上架状态
    if product.state != 1:
        raise ValueError("该商品尚未上架")
    # 判断sku data
    skus = product.stockkeepingunit_set.filter(become_history=False).only("sku_id", "physical_inventory", "plan_use_inventory")

    inventory_skus_map = {
        sku.sku_id: {
            "physical_inventory": sku.physical_inventory or 0,
            "plan_use_inventory": sku.plan_use_inventory or 0,
        }
        for sku in skus
    }
    sub_skus_map = {}
    # 组合商品需要判断
    if product.is_combine:
        sub_skus = product.substockkeepingunit_set.filter(become_history=False, parent_sku_id__in=inventory_skus_map.keys())
        for sub_sku in sub_skus:
            sub_parent_sku_id = sub_sku.parent_sku_id
            if sub_parent_sku_id not in sub_skus_map:
                sub_skus_map[sub_parent_sku_id] = [sub_sku]
            else:
                sub_skus_map[sub_parent_sku_id].append(sub_sku)

    for sku_data in post_sku_data:
        sku_id = sku_data.get("sku_id")
        physical_inventory = sku_data.get("physical_inventory", 0) or 0

        if not str(sku_id).isdigit():
            raise ValueError(f"非法SKU_ID:{sku_id}")

        int_sku_id = int(sku_id)

        if not int_sku_id or int_sku_id not in inventory_skus_map:
            raise ValueError("错误SKU信息,请刷新页面后重试")

        sku_inventory_obj = inventory_skus_map.get(int_sku_id)

        # 去限制，预估销量联动
        # can_use_inventory = sku_inventory_obj.get("physical_inventory") - sku_inventory_obj.get("plan_use_inventory")

        # if can_use_inventory < 0:
        #     raise ValueError(f"规格{sku_id}可用库存为:{can_use_inventory}, 请联系专员维护")
        #
        # if physical_inventory > can_use_inventory:
        #     raise ValueError(f"规格{sku_id}数量:{physical_inventory}. 已超过可用库存: {can_use_inventory}")

        if product.is_combine:
            if int_sku_id not in sub_skus_map:
                raise ValueError("错误组合商品SKU信息,请刷新后重试")

            sub_skus = sub_skus_map[int_sku_id]
            for sub_sku in sub_skus:
                act_need_inventory = physical_inventory * sub_sku.num

                relate_sku = sub_sku.relate_sku
                sub_can_use_inventory = (relate_sku.physical_inventory or 0) - (relate_sku.plan_use_inventory or 0)
                if sub_can_use_inventory < 0:
                    raise ValueError(f"组合商品{sku_id}可用库存为:{sub_can_use_inventory}, 请联系专员维护")

                if act_need_inventory > sub_can_use_inventory:
                    # raise ValueError(f"{sku_id}数量:{physical_inventory}. 已超过可用库存: {can_use_inventory}")
                    raise ValueError(f"组合商品{sku_id}库存不足")

    # 判断是否在其他进行中的计划 2024.03.30天意确认放开限制
    # items = ProductSelectionItem.objects.filter(
    #     product_id=product_id,
    #     selection_plan__state=1,
    #     is_deleted=False,
    # ).values_list("selection_plan__name", flat=True)
    # # 复制时过滤当前货盘
    # if kwargs.get("exclude_id"):
    #     items = items.exclude(selection_plan_id=kwargs.get("exclude_id"))
    #
    # if items:
    #     raise ValueError(f"该商品已被货盘计划【{'、'.join(items)}】选中")

    try:
        with transaction.atomic():
            # 公司
            product_company = product.company

            # 校验商品分类,商品分类小于3级需要更新商品,公司下的二级分类
            category_list = product.category
            if len(category_list) < 3:
                raise ValueError(f"商品:{product}分类小于三级，请更新商品")

            # 创建公司
            plan_company = ProductSelectionPlanCompany.objects.filter(selection_plan_id=plan_id, company_id=product_company.company_id).first()
            if not plan_company:
                # 获取公司最大排序并创建新的计划公司
                max_company_order = ProductSelectionPlanCompany.objects.filter(selection_plan_id=plan_id, is_deleted=False).aggregate(Max("order"))["order__max"] or 0

                plan_company = ProductSelectionPlanCompany.objects.create(selection_plan_id=plan_id, company_id=product_company.company_id, order=max_company_order + 1)

            # 二级类目创建关联
            sec_category_id = category_list[1]
            plan_category = plan_company.productselectionplancategory_set.all().filter(category_id=sec_category_id).first()
            if not plan_category:
                # 获取二级分类最大排序并创建新的分类
                max_category_order = ProductSelectionPlanCategory.objects.filter(plan_company=plan_company, is_deleted=False).aggregate(c_order=Max("order"))["c_order"] or 0
                plan_category = ProductSelectionPlanCategory.objects.create(plan_company=plan_company, category_id=sec_category_id, order=max_category_order + 1)

            # 商品排序
            item_max_order_dict = ProductSelectionItem.objects.filter(plan_category=plan_category, is_deleted=False).aggregate(item_order=Max("order"))
            map_item_max_order_dict = ProductSelectionItem.objects.filter(selection_plan_id=plan_id, is_deleted=False).aggregate(max_item_order=Max("map_order"))
            # 最大的 item order和排品order
            max_item_order = item_max_order_dict["item_order"] or 0
            max_map_item_order = map_item_max_order_dict["max_item_order"] or 0

            item_data = {
                "selection_plan_id": plan_id,
                "plan_category_id": plan_category.id,
                "product_id": product_id,
                "create_user": user_id,
                "update_user": user_id,
                "order": max_item_order + 1,
                "map_order": max_map_item_order + 1,
                "create_user_type": create_user_type,
                "remark": kwargs.get("item_remark", ""),
                "selection_reason": kwargs.get("selection_reason", ""),
            }

            # 增加sub_product_id
            # 如果是分销模式，用的是
            if is_distributor_mode:
                sub_product = SubProduct.objects.filter(parent_product__product_id=product_id, owner__letters="FX", is_deleted=False)
                if not sub_product.exists():
                    raise ValueError("商品尚未加到分销市场或已删除")
                sub_product = sub_product[0]
            else:
                # 不存在就新增副本商品
                try:
                    sub_product = SubProduct.objects.get(parent_product__product_id=product_id, owner=selection_plan.distributor)
                except SubProduct.DoesNotExist:
                    post_data = product.__dict__
                    post_data["parent_product"] = product.pk
                    post_data["owner"] = selection_plan.distributor_id
                    post_data["create_user"] = user_id
                    ser = MyProductCreateSer(data=post_data)
                    if ser.is_valid():
                        sub_product = ser.save()

                        # 创建关联分销商货号数据
                        ProductLinkDistributor(
                            product=product,
                            distributor_id=selection_plan.distributor_id,
                            code=sub_product.code,
                            create_user=user_id,
                        ).save()
                    else:
                        logger.warning(f"异步加入货盘失败,{ser.errors}")
                        sub_product = None

            if sub_product:
                item_data["sub_product_id"] = sub_product.product_id
            # 货盘商品
            item = ProductSelectionItem.objects.create(**item_data)

            # 库存锁
            ts_point = transaction.savepoint()

            try_times = 3

            for sku_data in post_sku_data:
                for i in range(try_times):
                    sku_id = sku_data.get("sku_id")
                    physical_inventory = sku_data.get("physical_inventory", 0) or 0

                    if not str(sku_id).isdigit():
                        transaction.savepoint_rollback(ts_point)
                        raise ValueError(f"非法SKU:{sku_id}")

                    try:
                        _sku = StockKeepingUnit.objects.get(sku_id=sku_id, become_history=False)
                    except StockKeepingUnit.DoesNotExist:
                        transaction.savepoint_rollback(ts_point)
                        raise ValueError("SKU不存在")
                    # 去限制，预估销量联动
                    # if (physical_inventory > _sku.can_use_inventory) or (physical_inventory > (_sku.physical_inventory - _sku.plan_use_inventory)):
                    #     transaction.savepoint_rollback(ts_point)
                    #     logger.info(f"加货盘库存不足 >> {sku_id},{product_id}")
                    #     raise ValueError(f"库存不足")

                    # 判断组合商品库存
                    if product.is_combine:
                        sub_skus = _sku.parent_sub_skus.filter(become_history=False)
                        for sub_sku in sub_skus:
                            relate_sku = sub_sku.relate_sku
                            act_need_inventory = sub_sku.num * physical_inventory

                            if (act_need_inventory > (relate_sku.can_use_inventory or 0)) or (
                                act_need_inventory > ((relate_sku.physical_inventory or 0) - (relate_sku.plan_use_inventory or 0))
                            ):
                                transaction.savepoint_rollback(ts_point)
                                logger.info(f"组合商品加货盘库存不足 >> {sku_id},{product_id},{relate_sku}")
                                raise ValueError("组合商品库存不足")

                            # 更新关联sku的占用库存
                            relate_sku.plan_use_inventory = F("plan_use_inventory") + act_need_inventory
                            relate_sku.save(update_fields=["plan_use_inventory"])

                    # 原sku库存
                    origin_sku_plan_use_inventory = _sku.plan_use_inventory
                    new_plan_use_inventory = origin_sku_plan_use_inventory + physical_inventory
                    can_use_inventory = _sku.physical_inventory - new_plan_use_inventory

                    # 不判断可用库存
                    sku_update_rows = StockKeepingUnit.objects.filter(
                        sku_id=sku_id,
                        become_history=False,
                        # can_use_inventory__gte=physical_inventory,
                    ).update(
                        plan_use_inventory=new_plan_use_inventory,
                        can_use_inventory=can_use_inventory,
                    )

                    if sku_update_rows == 0:
                        if i == try_times - 1:
                            transaction.savepoint_rollback(ts_point)
                            raise ValueError("加入货盘失败,请重试")

                        continue

                    # 创建item sku, 预估销量联动
                    ProductSelectionItemSKU.objects.create(
                        selection_plan=selection_plan,
                        sku=_sku,
                        item=item,
                        physical_inventory=physical_inventory,
                        remark=sku_data.get("remark", "") or "",
                        # estimated_sales=sku_data.get("estimated_sales", 0) or 0,
                        estimated_sales=physical_inventory,
                        create_user=user_id,
                    )
                    break

            transaction.savepoint_commit(ts_point)
            # 写入操作明细日志
            w_plan_record(
                plan_id=plan_id,
                record_type=ProductSelectionPlanRecordType.ADD_PROD,
                content="",
                create_user=user_id,
                product_id=product_id,
                remark=kwargs.get("remark"),
            )
            # 创建消息通知
            notifications_data = {
                "product_id": product_id,
                "company_id": product_company.company_id,
                "notify_type": "plan",
                "operation_type": "added_plan",
                "content": f"商品成功加入直播日期【{selection_plan.live_date_start}】的货盘",
            }
            ceate_notifications.delay(notifications_data, user_type_list=["SP"])

            return item
    except IntegrityError as e:
        logger.error(str(e))
        raise ValueError(str(e))


def selection_plan_item_move_fc(
    request,
    old_plan_id: int,
    new_plan_id: int,
    item_id: int,
    remark: str,
    post_sku_data: list,
):
    current_user = request.user

    def plan_state_checker(plan_id):
        try:
            filter_condition = {"plan_id": plan_id}
            if get_current_user_type(request) == "DB":
                filter_condition["distributor__id"] = current_user.distributor_id

            selection_plan = ProductSelectionPlan.objects.get(**filter_condition)
            if selection_plan.state in [0, 2]:
                raise ValueError(f"当前货盘:{selection_plan}{selection_plan.get_state_display()},不可编辑")
                # raise ValueError("this state can't edit")
            return selection_plan
        except ProductSelectionPlan.DoesNotExist:
            raise ValueError("data not found")

    try:
        old_plan = plan_state_checker(old_plan_id)
        new_plan = plan_state_checker(new_plan_id)

        try:
            item = ProductSelectionItem.objects.get(
                pk=item_id,
                selection_plan_id=old_plan_id,
                is_deleted=False,
            )
        except ProductSelectionItem.DoesNotExist:
            raise ValueError("货盘商品不存在")

        if item.has_live:
            raise ValueError("商品已播,无法移动")

        if item.product.is_deleted:
            raise ValueError("商品已删除,无法移动")

        setnx_lock(old_plan_id, current_user.user_id)
        # 其他人正在编辑中
        user_id = read_lock(old_plan_id)
        if current_user.user_id != user_id:
            raise ValueError("Not granted editing permissions")

        new_plan_user_id = read_lock(new_plan_id)
        if new_plan_user_id and new_plan_user_id != user_id:
            real_name = update_cache_real_name_with_user_id(new_plan_user_id) or new_plan_user_id
            raise ValueError(f'{real_name}正在编辑计划"{new_plan.name}",无法进行商品移动')

        log_content = f"{old_plan}货盘商品{item.product_id}移动到新货盘{new_plan}"

        with transaction.atomic():
            # 更新sku占用库存
            item_skus = item.productselectionitemsku_set.filter(become_history=False)

            for item_sku in item_skus:
                _sku = item_sku.sku
                _sku.plan_use_inventory = _sku.plan_use_inventory - item_sku.physical_inventory
                _sku.save(update_fields=["plan_use_inventory", "can_use_inventory"])

            common_add_product_to_plan_fc(
                current_user.user_id,
                new_plan_id,
                item.product_id,
                new_plan,
                post_sku_data=post_sku_data,
                create_user_type=request.auth.get("user_type"),
                remark=remark,
            )

            # 物理删除数据
            item.delete()

        # 写入操作明细日志
        w_plan_record(
            plan_id=old_plan_id,
            record_type=ProductSelectionPlanRecordType.DEL_PROD,
            content=log_content,
            create_user=user_id,
            product_id=item.product_id,
        )
        return IResponse()
    except ValueError as e:
        return IResponse(code=400, message=str(e))
    except Exception as e:
        logger.error(f">>{e}-{traceback.format_exc()}")
        return IResponse(code=500, message="移动失败,请联系管理员")


def product_copy_fc(request: Request, product_id: int):
    """
    商品复制共同logic
    :param request:
    :param product_id: 旧商品的商品id
    :return:
    """
    return IResponse(code=400, message="暂时停止商品复制功能")
    current_user = request.user
    current_user_type = request.auth.get("user_type")

    def image_url_recreate(raw_image_path: str):
        """
        重新生成image_url
        img_url = "https://zhulinks.oss-cn-guangzhou.aliyuncs.com/user-upload/prod/images/productMgmt/imgs/64e2d15c475d0bcda032dded4eeed700_1704425040812.png?a=123&b=23"
        :param raw_image_path:
        :return:
        """
        try:
            if not raw_image_path or not isinstance(raw_image_path, str):
                return ""
            image_path_split = raw_image_path.split("?")
            if not image_path_split:
                return ""

            image_path = image_path_split[0]

            others = ""
            if len(image_path_split) > 1:
                others = "?" + "?".join(image_path_split[1:])

            last_slash = str(image_path).rindex("/")

            image_name = image_path[last_slash + 1 :]

            new_image_name = uuid.uuid4().hex + "." + image_name.split(".")[-1]

            raw_dir_path = image_path[: last_slash + 1]

            return raw_dir_path + new_image_name + others
        except Exception as e:
            logger.warning(f"解析图片地址{raw_image_path}失败,{e}{traceback.format_exc()}")
            return ""

    def _copy_product(data):
        if data.get("spec_lists"):
            # 剔除垃圾数据
            spec_lists = data.pop("spec_lists")
            data["spec_lists"] = [
                {
                    "source_id": spec.get("source_id"),
                    "name": spec.get("name"),
                    "values": spec.get("values"),
                }
                for spec in spec_lists
            ]

        # 校验分类
        try:
            # category转成int列表
            data["category"] = [int(i) for i in data.get("category")]
        except ValueError:
            raise ValueError("category elements should be int list")
        # review_remark
        # 复制修改原名称
        data["name"] = data["name"] + "_副本1"

        # 重新赋值main_images/ detail_images/ main_video
        raw_prod_main_images = data.get("main_images")
        raw_prod_detail_images = data.get("detail_images")
        raw_main_video = data.get("main_video")

        new_main_images = {raw_img: image_url_recreate(raw_img) for raw_img in raw_prod_main_images if raw_img and image_url_recreate(raw_img)}
        # new_detail_images = {raw_img: image_url_recreate(raw_img) for raw_img in raw_prod_detail_images if raw_img and image_url_recreate(raw_img)}

        new_main_video = image_url_recreate(raw_main_video)
        new_main_video_dict = {raw_main_video: new_main_video} if new_main_video else {}

        data["main_images"] = new_main_images.values()
        # data["detail_images"] = new_detail_images.values()
        data["main_video"] = new_main_video

        # 重新赋值create_user
        data["create_user"] = current_user.user_id
        # 需要oss复制的商品列表
        need_oss_copy_images = [new_main_images, new_main_video_dict]

        # 如果是供应商复制的，修改供应商
        if current_user_type == "SP":
            data["company"] = current_user.company_id

        # 分销商复制的，修改owner属性为当前用户
        if current_user_type == "DB":
            data["owner"] = current_user.distributor.distributor_id

        # 数据来源修改
        data["data_source"] = current_user_type
        data["state"] = 0

        # 旧商品的specs属性
        specs_keys = data.pop("specs")
        specs_value_ids = data.pop("specs_value")
        product_ser = ProductCopySer(data=data, context={"request": request})
        if not product_ser.is_valid():
            raise FieldsError(product_ser.errors)

        instance = product_ser.save()

        # 复制商品的specs和specs_value
        for specs_key in specs_keys:
            instance.specs.add(specs_key)

        for specs_value_id in specs_value_ids:
            instance.specs_value.add(specs_value_id)

        return instance, need_oss_copy_images

    def _copy_sku_and_spec_options(product, skus_data):
        re_data = []
        qdrant_images = []
        need_oss_copy_images = []
        for sku_data in skus_data:
            sku_data["product"] = product.id

            sku_specs_keys = sku_data.pop("specs_name_keys")
            sku_specs_values = sku_data.pop("specs_value_keys")

            if sku_data.get("specs"):
                # 剔除垃圾数据
                specs = sku_data.pop("specs")
                sku_data["specs"] = [
                    {
                        "name": spec.get("name"),
                        "value": spec.get("value"),
                    }
                    for spec in specs
                ]

            sku_data["spec_code"] = None

            if sku_data.get("image"):
                # 重新赋值sku image
                raw_sku_image = sku_data["image"]
                new_image_path = image_url_recreate(sku_data)
                sku_data["image"] = new_image_path
                # 复制oss image
                need_oss_copy_images.append({raw_sku_image: new_image_path})
                # 上传图片到qdrant
                qdrant_images.append(sku_data["image"])

            sku_data["company_code"] = product.company.code
            # create sku
            sku_ser = SkuCopyInfoSer(data=sku_data)
            if not sku_ser.is_valid():
                raise FieldsError(sku_ser.errors)
            sku_instance = sku_ser.save()

            # 添加sku specs
            for sku_specs_key in sku_specs_keys:
                sku_instance.specs_name.add(sku_specs_key)
            for sku_specs_value in sku_specs_values:
                sku_instance.specs_value.add(sku_specs_value)

        return re_data, qdrant_images, need_oss_copy_images

    def _copy_product_attr_options(new_prod, attr_option_data):
        new_options = [
            {
                "product": new_prod.id,
                "attr": attr_option.get("attr"),
                "name": attr_option.get("name"),
                "value": attr_option.get("value"),
                "attr_value": attr_option.get("attr_value"),
            }
            for attr_option in attr_option_data
            if attr_option.get("value")
        ]
        attr_options_ser = ProductAttrOptionCreateOrUpdateSer(data=new_options, many=True)
        if not attr_options_ser.is_valid():
            raise FieldsError(attr_options_ser.errors)
        attr_options_instances = attr_options_ser.save()
        return attr_options_instances

    try:
        try:
            old_product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            raise ValueError("data not found")
        # fetch origin product info
        old_product_info = ProductCopySer(instance=old_product, many=False, context={"request": request})
        raw_data = old_product_info.data

        # 组建参数
        sku_data = raw_data.pop("skus")
        attr_options = raw_data.pop("attr_options")

        # 保存记录
        with transaction.atomic():
            # create product
            new_product, need_oss_copy_prod_images = _copy_product(raw_data)
            # create skus
            sku_data, qdrant_images, need_oss_copy_sku_images = _copy_sku_and_spec_options(new_product, sku_data)
            # create attr options
            attr_options_instance = _copy_product_attr_options(new_product, attr_options)

        # 上传图片到qdrant
        qdrant_image_list = list({*new_product.main_images, *qdrant_images})
        if qdrant_image_list:
            add_images_to_qdrant.delay(new_product.id, qdrant_image_list)

        # 复制商品图片
        for prod_image_dict in need_oss_copy_prod_images:
            for raw_image, new_image in prod_image_dict.items():
                copy_oss_images.delay(raw_image, new_image)

        # 复制sku图片
        for sku_image_dict in need_oss_copy_sku_images:
            for raw_image, new_image in sku_image_dict.items():
                copy_oss_images.delay(raw_image, new_image)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=new_product.product_id,
                model=Product,
                describe=f"复制商品.商品:{new_product}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        # 不返回复制的商品内容
        return IResponse(data={"id": new_product.product_id})
    except (ValueError, FieldsError) as e:
        logger.warning(f">>> copy product error{e},{traceback.format_exc()}")
        return IResponse(code=400, message=str(e))
    except (IntegrityError, Exception) as e:
        logger.warning(f">>>> copy product internal error: {e},{traceback.format_exc()}")
        return IResponse(code=500, message="internal server error")


def selection_plan_item_confirm_fc(request: Request, item_id: int):
    """
    货盘质检

    不需要修改状态即可确认
    """

    product_confirm_state = request.data.get("product_confirm_state", None)
    if product_confirm_state not in (0, 1):
        return IResponse(code=400, message="invalid state")

    qa_data = request.data.get("qa_data", None)

    try:
        filter_condition = {"id": item_id}

        current_user_type = request.auth.get("user_type") or request.user.user_type

        if current_user_type == "DB":
            filter_condition["selection_plan__distributor__id"] = request.user.distributor_id

        item = ProductSelectionItem.objects.get(**filter_condition)
    except ProductSelectionItem.DoesNotExist:
        return IResponse(code=400, message="data not found")

    if item.selection_plan.state == 0:
        return IResponse(code=400, message="货盘已结束，无法确认")
    if product_confirm_state == item.product_confirm_state:
        return IResponse(code=400, message="状态相同，无需重新确认")
    product = item.product
    if product.is_deleted:
        return IResponse(code=400, message="商品已删除,无法移动商品")
    if product.state != 1:
        return IResponse(code=400, message="商品未上架,无法确认")

    if not qa_data:
        item.update_user = request.user.user_id
        item.product_confirm_state = product_confirm_state
        item.save()
    else:
        is_pass = qa_data.get("is_pass")
        if is_pass not in [True, False]:
            raise APIViewException(err_message="审核结果错误")

        total_num = qa_data.get("total_num")
        pass_num = qa_data.get("pass_num")
        qa_content = qa_data.get("qa_content")

        if total_num is None or pass_num is None:
            raise APIViewException(err_message="质检数量或合格数量不能为空")
        if total_num <= 0:
            raise APIViewException(err_message="质检数量必须大于0")
        if pass_num < 0:
            raise APIViewException(err_message="合格数量不能小于0")
        if pass_num > total_num:
            raise APIViewException(err_message="合格数量不能大于质检数量")

        process = product.productreviewprocess_set.filter(become_history=False).last()
        if not process:
            raise APIViewException(err_message="商品审核流程错误,请联系管理员")

        create_data = {
            "process": process,
            "product": product,
            "process_level": "QA_REVIEW",
            "physical_inventory_exact": True,
            "quality_qualified": True if is_pass else False,
            "price_reasonable": True,
            "remark": (qa_data.get("remark", "") or "") + f"（货盘质检: 货盘计划:{item.selection_plan.name}）",
            "recommended_price": qa_data.get("recommended_price"),
            "review_cost_price": qa_data.get("review_cost_price"),
            "selection_plan": item.selection_plan,
            "review_times": 1,
            "create_user": request.user.user_id,
        }

        last_qa_review = ProductReview.objects.filter(product=product, process_level="QA_REVIEW").only("review_times").last()
        if last_qa_review:
            create_data["review_times"] = last_qa_review.review_times + 1

        with transaction.atomic():
            item.update_user = request.user.user_id
            item.product_confirm_state = product_confirm_state
            item.save()

            instance = ProductReview.objects.create(**create_data)
            # 添加额外信息
            ProductReviewExtra.objects.create(
                review=instance,
                total_num=total_num,
                pass_num=pass_num,
                qa_content=qa_content,
            )

    if product_confirm_state == 0:
        record_type = ProductSelectionPlanRecordType.CANCEL_PROD_CONFIRM
    else:
        record_type = ProductSelectionPlanRecordType.PROD_CONFIRM
    # 写入操作明细日志
    w_plan_record(
        plan_id=item.selection_plan_id,
        record_type=record_type,
        content=f"商品ID:{item.product_id}",
        create_user=request.user.user_id,
        product_id=item.product_id,
    )

    return IResponse()


def post_product_link_distributor_fc(request: Request, product_id):
    """
    商品关联分销商货号

    暂时不做身份校验
    :param request:
    :param product_id:
    :return:
    """

    distributor_id = request.data.get("distributor_id")
    code = request.data.get("code")
    linked_product_id = request.data.get("linked_product_id")

    if not all([distributor_id, code, linked_product_id]):
        return IResponse(code=400, message="invalid params")

    if product_id == linked_product_id:
        return IResponse(code=400, message="相同商品不允许关联商品货号")

    if len(code) > 10:
        return IResponse(code=400, message="货号参数不能超过10位")

    try:
        product = Product.objects.get(product_id=product_id, is_deleted=False)
    except Product.DoesNotExist:
        return IResponse(code=400, message="data not found")

    try:
        linked_product = Product.objects.get(product_id=linked_product_id, code=code, is_deleted=False)
    except Product.DoesNotExist:
        return IResponse(code=400, message="被关联商品不存在,请刷新页面后重试")

    if linked_product.origin_product:
        return IResponse(
            code=400,
            message=f"该关联商品已有原商品:{linked_product.origin_product_id},无法进行绑定",
        )

    try:
        distributor = Distributor.objects.get(distributor_id=distributor_id)
    except Distributor.DoesNotExist:
        return IResponse(code=400, message="分销商不存在")

    if distributor.state == 0:
        return IResponse(code=400, message="分销商待审核状态，请联系管理员进行审核")

    if distributor.state == 2:
        return IResponse(code=400, message="分销商审核不通过，请联系管理员进行核实")

    if ProductLinkDistributor.objects.filter(
        product=product,
        distributor=distributor,
        is_deleted=False,
    ).exists():
        return IResponse(code=400, message="该商品已关联该分销商货号，请刷新列表")

    with transaction.atomic(savepoint=False):
        ProductLinkDistributor(
            product=product,
            distributor=distributor,
            code=code,
            linked_product_id=linked_product_id,
            create_user=request.user.user_id,
        ).save()
        # 修改商品的原商品数据
        linked_product.origin_product = product
        linked_product.update_user = request.user.user_id
        linked_product.save()

    return IResponse()


def plan_tree_data(
    request: Request,
    plan_id: int,
    product_list_ser: serializers.SerializerMetaclass,
    sku_ser: serializers.SerializerMetaclass,
):
    current_user_type = get_current_user_type(request)

    raw_params = request.query_params.copy()
    # 前端参数转换
    and_Q = None
    if raw_params.get("name"):
        raw_params["product__name"] = raw_params.pop("name")[0]

    if raw_params.get("company_id"):
        raw_params["product__company__company_id"] = raw_params.pop("company_id")[0]

    if raw_params.get("product_deleted"):
        raw_params["product__is_deleted"] = raw_params.pop("product_deleted")[0]

    if raw_params.get("product_id"):
        raw_params["product__product_id"] = raw_params.pop("product_id")[0]
    # 副本商品查询
    if raw_params.get("sub_product_id"):
        raw_params["sub_product_id"] = raw_params.pop("sub_product_id")[0]

    if raw_params.get("code"):
        tmp_code = raw_params.pop("code")[0]
        if tmp_code[-1] == "J":
            tmp_code = tmp_code[:-1]

        if not and_Q:
            and_Q = Q()
        and_Q.add(Q(**{"product__code__icontains": tmp_code}), Q.OR)
        and_Q.add(Q(**{"sub_product__code__icontains": tmp_code}), Q.OR)

    if raw_params.get("category"):
        raw_params["product__category"] = raw_params.pop("category")[0]
    if raw_params.get("physical_inventory"):
        raw_params["product__physical_inventory__range"] = raw_params.pop("physical_inventory")[0]
    if raw_params.get("history_price"):
        history_price = json.loads(raw_params.pop("history_price")[0])
        if history_price:
            and_Q = get_range_query(
                "product__min_history_price",
                "product__max_history_price",
                history_price,
            )

    if raw_params.get("orders"):
        orders = json.loads(raw_params.pop("orders")[0])
        new_orders = []
        for i in orders:
            if "create_date" in i:
                new_orders.append(i)
            else:
                new_orders.append(f"product__{i}")
        raw_params["orders"] = json.dumps(new_orders)
    else:
        raw_params["orders"] = json.dumps(["order"])

    try:
        condition = {"plan_id": plan_id}
        if get_current_user_type(request) == "DB":
            condition["distributor"] = request.user.distributor

        selection_plan = ProductSelectionPlan.objects.get(**condition)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")

    # 添加货盘监听
    send_selection_plan_listener_task(request, "货盘-商品列表", selection_plan)

    filters = {
        "selection_plan_id": plan_id,
        "is_deleted": False,
    }

    hybrid_fields = [
        "product__name",
        "product__product_id",
        "product__code",
        "product__stockkeepingunit__link_code",
        "product__stockkeepingunit__spec_code",
        "product__productlinkdistributor__code",
    ]

    hybrid_fields.append("sub_product__code")

    if raw_params.get("hybrid_search"):
        _tmp_hybrid_search = raw_params.get("hybrid_search")
        if str(_tmp_hybrid_search).startswith("J"):
            raw_params["hybrid_search"] = str(_tmp_hybrid_search)[1:]
        else:
            raw_params["hybrid_search"] = _tmp_hybrid_search

    _page_model_objects, tmp_re_data, item_qs = custom_filter(
        raw_params,
        ProductSelectionItem,
        array_fields=["product__category"],
        like_fields=[
            "product__name",
            "product__code",
            "sub_product__code",
            "product__product_id",
        ],
        hybrid_fields=hybrid_fields,
        query_Q=and_Q,
        **filters,
    )

    re_data = {
        "plan_id": selection_plan.plan_id,
        "name": selection_plan.name,
        "live_date_start": selection_plan.live_date_start,
        "live_date_end": selection_plan.live_date_end,
        "state": selection_plan.state,
        "plan_inventory_warning_count": 0,
        "wait_review_count": 0,
        "count": 0,
    }

    # 上播率
    item_counts = selection_plan.productselectionitem_set.filter(is_deleted=False).count()
    if item_counts == 0:
        re_data["live_rate"] = 0
    else:
        read_counts = selection_plan.productselectionitem_set.filter(is_deleted=False, has_live=True).count()
        re_data["live_rate"] = round(read_counts / item_counts, 4)

    if not item_qs:
        return IResponse(data=re_data)

    # 获取所有plan分类id
    plan_category_ids = {item.plan_category_id for item in item_qs}
    # item的所有商品id,批量查询product
    item_product_ids = {item.product_id for item in item_qs}
    # plan category 批量查询
    plan_category_qs = ProductSelectionPlanCategory.objects.filter(id__in=list(plan_category_ids)).select_related("category").order_by("order")

    # company_id对应的plan_category
    company_category_map = {}
    for plan_category in plan_category_qs:
        category_plan_company_id = plan_category.plan_company_id
        if category_plan_company_id not in company_category_map:
            company_category_map[category_plan_company_id] = [plan_category]
        else:
            company_category_map[category_plan_company_id].append(plan_category)

    # 排序
    for k, v in company_category_map.items():
        company_category_map[k] = sorted(v, key=lambda x: getattr(x, "order"))

    # category对应的item
    category_item_map = {}

    for item in item_qs:
        item_category_id = item.plan_category_id
        if item_category_id not in category_item_map:
            category_item_map[item_category_id] = [item]
        else:
            category_item_map[item_category_id].append(item)

    # 排序
    for k, v in category_item_map.items():
        category_item_map[k] = sorted(v, key=lambda x: getattr(x, "order"))

    # 商品map
    products = Product.objects.filter(product_id__in=item_product_ids)

    product_map = {product.product_id: product for product in products}
    product_ids_for_sku = {product.id for product in products}

    # 批量查询额外查询
    bulk_product_extra_tags = ProductBulkExtraTags(products)

    # 查询sub_product信息
    sub_product_map = {}
    if get_current_user_type(request) == "DB":
        sub_product_ids = {item.sub_product_id for item in item_qs}
        sub_products = SubProduct.objects.filter(product_id__in=sub_product_ids, owner=selection_plan.distributor)
        sub_product_map = {sub_product.parent_product_id: sub_product for sub_product in sub_products}

    # 批量查询sku信息
    skus = StockKeepingUnit.objects.filter(product_id__in=product_ids_for_sku, become_history=False)
    # 批量查询sku的规格信息
    sku_specs = bulk_query_skus_specs_detail(skus)
    sku_map = {}
    sku_id_set = set()

    for sku in skus:
        sku_id_set.add(sku.id)
        if sku.product_id not in sku_map:
            sku_map[sku.product_id] = [sku]
            continue

        sku_map[sku.product_id].append(sku)

    # 批量查询history
    group_by_history_price_qs = (
        HistoryPrice.objects.values("sku_id")
        .annotate(max_id=Max("id"))
        .filter(sku_id__in=sku_id_set)
        .values(
            "max_id",
            "sku_id",
        )
    )
    history_price_id_list = [history_price.get("max_id") for history_price in group_by_history_price_qs]
    history_price_qs = HistoryPrice.objects.filter(id__in=history_price_id_list).only("sku_id", "history_price")
    history_price_map = {history_price.sku_id: history_price.history_price for history_price in history_price_qs}

    # 批量查询item skus
    item_skus = ProductSelectionItemSKU.objects.filter(
        sku__in=skus,
        become_history=False,
        item__in=item_qs,
    ).only("sku_id", "physical_inventory")
    item_skus_map = {item_sku.sku_id: item_sku.physical_inventory for item_sku in item_skus}

    # 获取所有公司的id
    plan_companies_ids = {plan_category.plan_company_id for plan_category in plan_category_qs}
    plan_companies = ProductSelectionPlanCompany.objects.filter(id__in=list(plan_companies_ids)).select_related("company").order_by("order")
    total_count = 0
    tree_structured_data = []

    # 批量查询category
    category_ids = set()
    for p in products:
        for c in p.category:
            category_ids.add(c)

    category_list = ProductCategoryList.objects.filter(id__in=list(category_ids)).only("id", "name")
    category_map = {category.id: {"id": category.id, "name": category.name} for category in category_list}

    # 批量查询趋势
    # 查询所有的趋势
    trend_map = batch_calc_prod_trend(products)

    # 查询状态
    # 查询托管状态
    hosting_product_map = bulk_query_product_hosting_state(item_product_ids)
    # 查询标签
    product_labels_dict = bulk_query_labels(item_product_ids)

    # 告警商品数量
    plan_inventory_warning_count = 0
    # 待审核数量
    wait_review_count = 0

    for plan_company in plan_companies:
        company_product_count = 0
        company_data = {
            "plan_company_id": plan_company.id,
            "company_id": plan_company.company.company_id,
            "company_name": plan_company.company.name,
            "order": plan_company.order,
            "count": 0,
            "data": [],
        }

        for plan_category in company_category_map.get(plan_company.id):
            category_product_count = 0
            category_data = {
                "plan_category_id": plan_category.id,
                "category_id": plan_category.category.id,
                "category_name": plan_category.category.name,
                "order": plan_category.order,
                "count": 0,
                "data": [],
            }

            for item in category_item_map.get(plan_category.id):
                # 检查近7日是否有订单
                product_instance = product_map.get(item.product_id)
                if not product_instance:
                    continue

                # 统计货盘货盘不足
                if product_instance.can_use_inventory < 0:
                    plan_inventory_warning_count += 1

                # 待审核的数量
                if product_instance.state in [4]:
                    wait_review_count += 1

                _data = product_list_ser(
                    instance=product_instance,
                    context={
                        "request": request,
                        "sub_products_map": sub_product_map,
                        "bulk_product_extra_tags": bulk_product_extra_tags,
                    },
                ).data
                _category = _data.get("category")
                if _category:
                    # 数据库保存的string类型数据，需要改成int获取map数据
                    _data["category"] = [category_map.get(int(_category_id)) for _category_id in _category]

                _data["has_live"] = item.has_live
                # 确认状态
                _data["product_confirm_state"] = item.product_confirm_state
                # 商品库存和价格趋势

                _data["inventory_trend"] = trend_map.get(f"{product_instance.product_id}_physical_inventory", "")
                _data["cost_price_trend"] = trend_map.get(f"{product_instance.product_id}_cost_price")

                _data["remark"] = item.remark
                _data["order"] = item.order
                _data["plan_product_id"] = item.id

                # item占用的库存
                _data["plan_use_physical_inventory"] = item.physical_inventory

                # skus 数据
                sku_list = []
                skus_qs = sku_map.get(product_instance.id, [])

                for sku_qs in skus_qs:
                    skus_data = sku_ser(
                        instance=sku_qs,
                        context={"request": request, "sku_specs": sku_specs},
                    ).data
                    # 获取最新历史价
                    skus_data["latest_history_price"] = history_price_map.get(sku_qs.id, None)
                    # sku占用库存
                    skus_data["plan_use_physical_inventory"] = item_skus_map.get(sku_qs.sku_id) or 0

                    sku_list.append(skus_data)

                _data["skus"] = sku_list

                # 添加状态
                _data["is_new"] = product_instance.is_new
                _data["hosting_state"] = hosting_product_map.get(item.product_id)

                # 添加标签
                _data["labels"] = product_labels_dict.get(item.product_id) or []
                # 商品状态
                _data["product_state"] = product_instance.state

                company_product_count += 1
                category_product_count += 1
                total_count += 1
                category_data["data"].append(_data)
            category_data["count"] = category_product_count
            # 添加分类数据到公司数据中
            company_data["data"].append(category_data)
            company_data["count"] = company_product_count
        # 添加公司数据到树结构中
        tree_structured_data.append(company_data)
    re_data["data"] = tree_structured_data
    re_data["count"] = total_count
    re_data["plan_inventory_warning_count"] = plan_inventory_warning_count
    re_data["wait_review_count"] = wait_review_count
    return IResponse(code=200, data=re_data)


def update_sku_inventory_fc(request: Request, sku_id):
    """
    更新sku现货库存和7天库存
    :param request:
    :param sku_id:
    :return:
    """
    physical_inventory = request.data.get("physical_inventory")
    safety_inventory = request.data.get("safety_inventory")

    if physical_inventory is None or safety_inventory is None:
        return IResponse(code=400, message="invalid params")

    sku_filter_condition = {
        "sku_id": sku_id,
        "become_history": False,
    }

    try:
        sku = StockKeepingUnit.objects.get(**sku_filter_condition)
    except StockKeepingUnit.DoesNotExist:
        return IResponse(code=400, data="data not found")

    sku.physical_inventory = physical_inventory
    sku.safety_inventory = safety_inventory
    sku.__update_user__ = request.user.user_id
    sku.save()
    return IResponse()


def new_selection_plan_download_fc(
    request: Request,
    plan_id: int,
):
    raw_params = request.query_params.copy() or {}
    fields = raw_params.get("fields", "")
    if fields not in ["spu", "sku", "all", ""]:
        return IResponse(code=400, message="请选择导出方式")

    current_user = request.user
    current_user_type = get_current_user_type(request)

    # 前端参数映射
    param_mappings = {
        "name": "name__icontains",
        "company_id": "company__company_id",
        "category": "category",
        "physical_inventory": "physical_inventory__range",
        "cost_price": "__cost_price__range",
        "history_price": "__history_price__range",
    }
    # 转换参数
    for key, new_key in param_mappings.items():
        if key in raw_params:
            raw_params[new_key] = raw_params.pop(key)[0]

    plan_query_condition = {"plan_id": plan_id}
    if current_user_type == "DB":
        plan_query_condition["distributor_id"] = current_user.distributor.distributor_id

    try:
        plan = ProductSelectionPlan.objects.only("name").get(**plan_query_condition)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")
    display_mode = raw_params.get("display_mode", None)

    if fields == "spu":
        filename = f"货盘计划：{plan.name}(商品)-{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
    elif fields == "sku":
        filename = f"货盘计划：{plan.name}(规格)-{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
    elif fields == "all":
        filename = f"货盘计划：{plan.name}(明细)-{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
    else:
        filename = f"货盘计划：{plan.name}-{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"

    #  开启异步任务
    start_time = datetime.now()
    obj_save_fields = dict(
        filename=filename,
        download_type="selection_plan",
        download_platform=current_user_type,
        source_id=plan_id,
        download_page="货盘表" if display_mode != "map" else "排品表",
        post_data=raw_params,
        absolute_url=request.build_absolute_uri(),
        create_user=current_user.user_id,
        create_date=timezone.make_aware(start_time),
        expired_time=timezone.make_aware(start_time + timedelta(days=6, minutes=45)),
    )
    # 记录分销商
    if current_user_type == "DB":
        obj_save_fields["distributor_id"] = current_user.distributor.distributor_id

    task = DownloadTasks.objects.create(**obj_save_fields)
    async_selection_plan_download_task.delay(task.id)
    return IResponse(code=201, message="后台正在处理下载任务", data={"task_id": task.id})


def record_download_fc(
    request: Request,
    plan_id: int,
):
    ProductSelectionPlanRecord.objects.filter(selection_plan_id=plan_id)


def clean_specs(specs_list, skus_data, request):
    """
    清理specs数据
    todo: 增加request具体判断 current_user的规格值和规格名
    :param request:
    :param specs_list: spu的商品数据
    :param skus_data: sku的数据
    :return:
    """
    # 校验specs_list
    if not isinstance(specs_list, list):
        raise APIViewException(err_message="非法规格")

    if skus_data is None:
        skus_data = []

    spu_spec_key_ids = set()
    spu_spec_value_ids = set()

    # value更改map, old_id: {"new_value_id": 123}
    spec_value_change_map = {}

    for specs in specs_list:
        key_id = specs.get("source_id")
        key_name = specs.get("name")
        values = specs.get("values")

        if not isinstance(values, list):
            raise APIViewException(err_message="非法规格值")

        # 校验name
        if not SpecsKey.objects.filter(id=key_id, name=key_name).exists():
            raise APIViewException(err_message=f"规格名称:[{key_name}]不存在,请刷新页面后重试")

        spu_spec_key_ids.add(key_id)

        for val in values:
            if not isinstance(val, dict):
                raise APIViewException(err_message="规格值类型错误")

            value_id = val.get("value_id")
            text = val.get("text")

            # key_id可能为新数据, 要新建value
            if not SpecsValue.objects.filter(id=value_id, value=text, spec_key_id=key_id).exists():
                current_user_type = get_current_user_type(request)
                tmp_params = {
                    "data_source": current_user_type,
                    "value": text,
                    "spec_key_id": key_id,
                    "defaults": {
                        "create_user": request.user.user_id,
                    },
                }
                if current_user_type == "DB":
                    tmp_params["distributor_id"] = request.user.distributor.distributor_id
                elif current_user_type == "SP":
                    tmp_params["supplier_id"] = request.user.company.company_id

                val_instance, created = SpecsValue.objects.get_or_create(**tmp_params)
                # 重新赋值
                _old_value_id, value_id = value_id, val_instance.id
                val["value_id"] = value_id
                val["old_value_id"] = _old_value_id
                spec_value_change_map[_old_value_id] = value_id

            spu_spec_value_ids.add(value_id)

    # 判断sku的specs是否与spu信息对应
    sku_spec_key_ids = set()
    sku_spec_value_ids = set()
    for sku_data in skus_data:
        specs = sku_data.get("specs", [])
        for spec in specs:
            key_id = spec.get("name_id")
            key_name = spec.get("name")

            value_id = spec.get("value_id")
            value = spec.get("value")

            if not SpecsKey.objects.filter(id=key_id, name=key_name).exists():
                raise APIViewException(err_message=f"SKU规格名称:[{key_name}]不存在,请刷新页面后重试")

            if not SpecsValue.objects.filter(id=value_id, value=value, spec_key_id=key_id).exists():
                if not spec_value_change_map.get(value_id):
                    raise APIViewException(err_message=f"SKU规格值:{value}不存在,请刷新页面后重试")

                new_value_id = spec_value_change_map.get(value_id)
                # 修改
                spec["value_id"] = new_value_id
                value_id = new_value_id

            if key_id:
                sku_spec_key_ids.add(key_id)

            if value_id:
                sku_spec_value_ids.add(value_id)

        # 处理好顺序
        # 重新排列第二个列表
        reordered_list_2 = []
        for item_1 in specs_list:
            source_id = item_1["source_id"]
            for item_2 in specs:
                if item_2["name_id"] == source_id:
                    reordered_list_2.append(item_2)
                    break
        sku_data["specs"] = reordered_list_2

    if spu_spec_key_ids.difference(sku_spec_key_ids):
        raise APIViewException(err_message="商品规格名与SKU信息不符合,请刷新页面后重试")

    if spu_spec_value_ids.difference(sku_spec_value_ids):
        raise APIViewException(err_message="商品规格值与SKU信息不符合,请刷新页面后重试")


def clean_create_labels(labels: list):
    """
    新增时清理标签
    :param labels:
    :return:
    """
    for label in labels:
        if not label or not isinstance(label, int):
            raise APIViewException(err_message="标签类型错误,请刷新后重试")

    if len(labels) > 6:
        raise APIViewException(err_message="最多允许6个标签")

    labels_count = ProductLabels.objects.filter(pk__in=labels, is_deleted=False, is_normal=True).count()
    if labels_count != len(labels):
        raise APIViewException(err_message="标签信息错误,请刷新后重试")

    return labels


def clean_update_labels(labels: list, product: Product):
    """
    编辑清理标签
    :param product:
    :param labels:
    :return:
    """
    for label in labels:
        if not label or not isinstance(label, int):
            raise APIViewException(err_message="标签类型错误,请刷新后重试")

    if len(labels) > 6:
        raise APIViewException(err_message="最多允许6个标签")

    relates = ProductLabelsRelate.objects.filter(product=product, become_history=False).only("label_id", "label__is_normal")
    origin_labels_id = []
    # 防止前端传来原来的label_id
    for relate in relates:
        if not relate.label.is_normal and relate.label_id in labels:
            labels.remove(relate.label_id)

        if relate.label.is_normal:
            origin_labels_id.append(relate.label_id)

    labels_count = ProductLabels.objects.filter(pk__in=labels, is_deleted=False, is_normal=True).count()
    if labels_count != len(labels):
        raise APIViewException(err_message="标签信息错误,请刷新后重试")

    return origin_labels_id, labels


def validate_category(category_list: list):
    """共用校验category id列表的方法"""
    if category_list:
        all_numbers = all(isinstance(e, (int, str)) for e in category_list)
        if not all_numbers:
            raise APIViewException(err_message="分类列表id必须为整数类型")

        if len(category_list) != 3:
            raise APIViewException(err_message="分类必须为3级分类，请检查")

        if ProductCategoryList.objects.filter(id__in=category_list).count() != len(category_list):
            raise APIViewException(err_message="错误分类ID")


def sku_specs_add_handler(sku_instance: StockKeepingUnit, specs: dict):
    """
    添加sku specs key和value的关系
    :param sku_instance:
    :param specs:
    :return:
    """
    for spec in specs:
        sku_instance.specs_name.add(spec["name_id"])
        sku_instance.specs_value.add(spec["value_id"])


def product_specs_update_handler(product_instance: Product, specs_list: list):
    product_instance.specs.clear()
    product_instance.specs_value.clear()

    for specs in specs_list:
        product_instance.specs.add(specs.get("source_id"))

        for val in specs["values"]:
            product_instance.specs_value.add(val["value_id"])


def sku_specs_update_handler(sku_instance: StockKeepingUnit, specs: dict):
    """
    sku更新specs共用方法, 商品编辑的时候对比sku对象和新post过来的数据，判断新增还是移除


    不全部清除数据，方便以后日志记录
    :param sku_instance:
    :param specs:
    :return:
    """
    sku_instance.specs_name.clear()
    sku_instance.specs_value.clear()

    for spec in specs:
        sku_instance.specs_name.add(spec["name_id"])
        sku_instance.specs_value.add(spec["value_id"])


def product_list_params_handler(raw_params: dict, exclude_fields: list = None):
    if exclude_fields is None:
        exclude_fields = []
    # 去除数据
    for exclude_field in exclude_fields:
        if raw_params.get(exclude_field):
            raw_params.pop(exclude_field)

    if raw_params.get("spec_code"):
        raw_params["stockkeepingunit__spec_code"] = raw_params.pop("spec_code")[0]
    if raw_params.get("company_id"):
        raw_params["company__company_id"] = raw_params.pop("company_id")[0]
    if raw_params.get("physical_inventory"):
        raw_params["physical_inventory__range"] = raw_params.pop("physical_inventory")[0]
    if raw_params.get("create_date"):
        raw_params["create_date__range"] = raw_params.pop("create_date")[0]
    if raw_params.get("update_date"):
        raw_params["update_date__range"] = raw_params.pop("update_date")[0]
    if raw_params.get("style"):
        raw_params["productattroption__attr__name"] = "款式"
        raw_params["productattroption__attr_value__name"] = raw_params.pop("style")[0]
    if raw_params.get("cost_price"):
        raw_params["__cost_price__range"] = raw_params.pop("cost_price")[0]
    if raw_params.get("distributor_market_price"):
        raw_params["__distributor_market_price__range"] = raw_params.pop("distributor_market_price")[0]
    if raw_params.get("retail_price"):
        raw_params["__retail_price__range"] = raw_params.pop("retail_price")[0]

    # 历史卖价
    if raw_params.get("history_price"):
        raw_params["__history_price__range"] = raw_params.pop("history_price")[0]

    if raw_params.get("brand"):
        raw_params["brand_id"] = raw_params.pop("brand")[0]

    if raw_params.get("spec_name"):
        raw_params["specs__name"] = raw_params.pop("spec_name")[0]
    if raw_params.get("spec_value"):
        raw_params["specs_value__value"] = raw_params.pop("spec_value")[0]
    # 新增筛选
    if raw_params.get("live_date"):
        raw_params["live_date__range"] = raw_params.pop("live_date")[0]
    if raw_params.get("distributor_id"):
        raw_params["owner_id"] = raw_params.pop("distributor_id")[0]

    # if raw_params.get("is_centralized_purchasing"):
    code = raw_params.get("code")
    if code and code[-1] == "J":
        raw_params["code"] = code[:-1]

    if raw_params.get("labels_relate"):
        label_id = raw_params.pop("labels_relate")[0]
        product_ids = list(set(ProductLabelsRelate.objects.filter(label_id=label_id, become_history=False).values_list("product_id", flat=True)))
        # 如果没有product_ids 会默认去除该参数
        # 0 列表会查询none
        if not product_ids:
            product_ids = [0]
        raw_params["product_id__in"] = json.dumps(product_ids)


def common_update_product_attr_options(product: Product, attr_options_data: list) -> (list, list):
    """
    三个端的更新attr option操作都一样, 公共方法
    :param product:
    :param attr_options_data:
    :return:
    """

    re_data = []
    change_content = []
    for attr_option_data in attr_options_data:
        # 剔除没值的数据
        attr_value = attr_option_data.get("value")
        if not attr_value:
            continue

        attr_id = attr_option_data.get("attr_id")

        if not ProductAttrList.objects.filter(id=attr_id, is_deleted=False).exists():
            raise APIViewException(err_message="商品属性不存在,请刷新页面后重试")

        try:
            attr_value_object = ProductAttrValues.objects.only("id").get(attr_id=attr_id, name=attr_value, is_deleted=False)
        except ProductAttrValues.DoesNotExist:
            raise APIViewException(err_message="商品属性值不存在,请属性页面后重试")

        new_create = False
        raw_object = None

        attr_option_data["product"] = product.id
        attr_option_data["attr"] = attr_id
        attr_option_data["attr_value"] = attr_value_object.id

        attr_option_id = attr_option_data.get("id")

        if attr_option_id:
            attr_option_qs = ProductAttrOption.objects.filter(id=attr_option_id, product=product).first()
            if not attr_option_qs:
                raise ValueError(f"attr option {attr_option_id} not found in product({product.product_id})")

            raw_object = copy.deepcopy(attr_option_qs)
            attr_option = ProductAttrOptionCreateOrUpdateSer(instance=attr_option_qs, data=attr_option_data)
        else:
            attr_option = ProductAttrOptionCreateOrUpdateSer(data=attr_option_data)
            new_create = True

        if not attr_option.is_valid():
            raise FieldsError(attr_option.errors)

        attr_option_instance = attr_option.save()

        if new_create:
            change_content.append(f"新增属性:{attr_option_instance}")
        else:
            _change_content = diff_models(raw_object, attr_option_instance, private_field=["name", "value"])
            if _change_content:
                change_content.append(f'更新属性"{attr_option_instance}":{_change_content}')

        re_attr_option = ProductAttrOptionSerializer(instance=attr_option_instance).data
        re_attr_option["attr_id"] = re_attr_option.pop("attr")
        re_attr_option.pop("product")
        re_data.append(re_attr_option)

    # 删除旧的attr_option
    old_attr_option_ids = [i["attr_id"] for i in re_data]
    need_delete_objs = product.productattroption_set.exclude(attr_id__in=old_attr_option_ids)
    for need_delete_obj in need_delete_objs:
        change_content.append(f"删除属性:{need_delete_obj}")
    need_delete_objs.delete()
    return re_data, change_content


def common_update_gift(product: Product, gifts: dict, company_id: int):
    """
    公共更新赠品

    Args:
        product (Product): _description_
        gifts (dict): _description_
        user_type (str): _description_
        company_id (_type_, optional): _description_. Defaults to None.
    """
    if not gifts:
        return

    assert gifts.get("has_gift") is not None, "has_gift required"
    has_gift = gifts.pop("has_gift")
    gift_products = gifts.pop("gift_products", [])
    assert len(gift_products) < 6, "length of gift_products should less than 6"
    # product.refresh_from_db()

    if not has_gift:
        # 删除所有的赠品
        raw_prodcut_qs = ProductGift.objects.filter(is_deleted=False, product=product)
        if raw_prodcut_qs:
            raw_prodcut_qs.update(is_deleted=True)
        product.has_gift = has_gift
    else:
        gift_type = gifts.get("gift_type")
        assert gift_type and gift_type in ("PO", "AC"), "gift_type should be PO or AC"

        if gift_type == "PO":
            raw_prodcut_qs = ProductGift.objects.filter(is_deleted=False, product=product, gift_product__company_id=company_id)
            if not gift_products:
                # 删除所有赠品
                raw_prodcut_qs.update(is_deleted=True)
            else:
                # 新旧处理：加入新增、删除未在表单数据
                raw_gift_prodcut_ids = raw_prodcut_qs.values_list("gift_product_id", flat=True)
                new_products = set(gift_products) - set(raw_gift_prodcut_ids)
                need_deleted = set(raw_gift_prodcut_ids) - set(gift_products)
                # 新增
                data_list = []
                for product_id in new_products:
                    g_product = Product.objects.filter(product_id=product_id, is_deleted=False, company_id=company_id).first()
                    if not g_product:
                        # 赠品不存在
                        raise ValueError(f"gift product({product_id}) not found")
                    data = {"product_id": product.product_id, "gift_type": "PO", "gift_product_id": product_id}
                    data_list.append(ProductGift(**data))
                instances = ProductGift.objects.bulk_create(data_list)
                # 删除需要删除的
                if need_deleted:
                    need_deleted_qs = ProductGift.objects.filter(is_deleted=False, product=product, gift_product_id__in=need_deleted)
                    if need_deleted_qs:
                        need_deleted_qs.update(is_deleted=True)

            # 删除配件类型的
            another_type = ProductGift.objects.filter(is_deleted=False, product=product, gift_type="AC")
            if another_type:
                another_type.update(is_deleted=True)
        else:
            gifts["product_id"] = product.product_id
            raw_prodcut_qs = ProductGift.objects.filter(is_deleted=False, product=product, gift_type=gift_type)
            if raw_prodcut_qs:
                raw_prodcut_qs.update(**gifts)
            else:
                assert gifts.get("name") and gifts.get("material") and gifts.get("image"), "name, material and image required"
                ProductGift.objects.create(**gifts)
            # 删除商品类型的
            another_type = ProductGift.objects.filter(is_deleted=False, product=product, gift_type="PO")
            if another_type:
                another_type.update(is_deleted=True)
        product.has_gift = has_gift
        product.gift_type = gift_type
    # product.save()


def op_db_update_sku_and_spec_options(product: Product, skus_data: list, request: Request, company_code: str):
    """
    运营商和分销商共用更新sku逻辑
    :param product:
    :param skus_data:
    :param request:
    :return:
    """
    origins_qs_list = list(StockKeepingUnit.objects.filter(product=product, become_history=False))

    re_data = []
    add_images = []
    del_images = []
    change_content = []
    # 重置审核状态
    # 0不需要审核
    # 1重新审核
    # 2核价审核
    reset_preview_state = 0

    # 规格排序
    sku_order = 1
    for sku_data in skus_data:
        if sku_data.get("link_code"):
            sku_data["spec_code"] = sku_data["link_code"]
        # 额外处理specs
        specs = sku_data.get("specs") or []

        # 添加商品id
        sku_data["product"] = product.id
        sku_data["company_code"] = company_code
        # 添加商品排序
        sku_data["order"] = sku_order
        # sku
        sku_id = sku_data.get("sku_id")

        qdrant_image = None
        new_create = False
        raw_object = None

        if not sku_id:
            # new sku: include add/sub specs or change specs
            qdrant_image = sku_data.get("image")
            sku = StockKeepingUnitSerializer(data=sku_data, context={"request": request})

            if float(sku_data.get("physical_inventory") or 0.0) != 0.0:
                if product.product_confirm_state:
                    raise ValueError("直播库存锁定,无法新增现货库存")

            if float(sku_data.get("safety_inventory") or 0.0) != 0.0:
                if product.product_confirm_state:
                    raise ValueError("直播库存锁定,无法新增7天库存")
            new_create = True
            # 重新审核
            reset_preview_state = 1
        else:
            sku_qs = StockKeepingUnit.objects.filter(sku_id=sku_id, product=product, become_history=False).first()
            if not sku_qs:
                raise ValueError(f"sku {sku_id} not found in product({product.product_id})")
            if sku_data.get("image") and sku_data["image"] != sku_qs.image:
                qdrant_image = sku_data["image"]
            if not sku_data.get("image") and sku_qs.image:
                del_images.append(sku_qs.image)
            # if sku which the form excluded, will set become_history=True
            for index, origin in enumerate(origins_qs_list):
                if origin.sku_id == sku_qs.sku_id:
                    origins_qs_list.pop(index)
                    break

            # 更新了成本价，需要审核
            # decimal类型,前端传的可能是int类型
            # if not reset_preview_state:
            #     for field in ("cost_price",):
            #         if sku_data.get(field) and float(sku_data.get(field)) != float(getattr(sku_qs, field)):
            #             if product.state in [1, 5]:
            #                 reset_preview_state = 2

            # 新旧对比object
            sku_data.pop("cost_price", None)
            raw_object = copy.deepcopy(sku_qs)
            sku = StockKeepingUnitSerializer(
                instance=sku_qs,
                data=sku_data,
                partial=True,
                context={"request": request},
            )

            # 判断现货库存是否修改
            try:
                raw_physical_inventory = int(sku_qs.physical_inventory or 0)
            except Exception as e:
                logger.warning(f"parse physical_inventory error {e}")
                raw_physical_inventory = 0

            new_physical_inventory = int(sku_data.get("physical_inventory") or 0)

            if raw_physical_inventory != new_physical_inventory:
                if product.product_confirm_state:
                    raise ValueError("直播库存锁定,无法编辑现货库存")

            try:
                raw_safety_inventory = int(sku_qs.safety_inventory or 0)
            except Exception as e:
                logger.warning(f"parse safety_inventory error {e}")
                raw_safety_inventory = 0

            new_safety_inventory = int(sku_data.get("safety_inventory") or 0)
            if raw_safety_inventory != new_safety_inventory:
                if product.product_confirm_state:
                    raise ValueError("直播库存锁定,无法编辑7天库存")

        if not sku.is_valid():
            raise FieldsError(sku.errors)
        sku_instance = sku.save()
        sku_order += 1

        log_sku_name = "".join([str(i["value"]) for i in sku_instance.specs]) + f"({sku_instance.sku_id})"
        if new_create:
            # 处理specs,添加sku规格信息
            sku_specs_add_handler(sku_instance, specs)

            # 添加日志
            change_content.append(f"新增SKU:{log_sku_name}")
        else:
            # 处理specs, 判断是否需要新增或者移除
            sku_specs_update_handler(sku_instance, specs)

            # 添加日志
            _change_content = diff_models(
                raw_object,
                sku_instance,
                private_field=[
                    "specs",
                    "can_use_inventory",
                ],
            )
            if _change_content:
                change_content.append(f'更新SKU"{log_sku_name}":{_change_content}')

        # 图片向量
        if qdrant_image:
            add_images.append(qdrant_image)
        re_sku = OperateStockKeepingUnitProductDetailSerializer(instance=sku_instance, context={"request": request}).data
        # 最新历史价
        history_price_data = []
        author_create_date = (
            HistoryPrice.objects.filter(sku_id=sku_instance.id)
            .values("author_id")
            .annotate(
                effective_author=Coalesce("author_id", Value("")),
                max_create_date=Max("create_date"),
            )
            .values("effective_author", "max_create_date")
        )

        for i in author_create_date:
            filters = {
                "author_id": (i["effective_author"] if i.get("effective_author") else None),
                "create_date": i.get("max_create_date"),
                "sku_id": sku_qs.id,
            }
            history_price = HistoryPrice.objects.filter(**filters).first()
            if history_price:
                history_price_data.append(
                    {
                        "history_price": history_price.history_price,
                        "history_price_author_id": (history_price.author.author_id if history_price.author else None),
                        "history_price_author_name": (history_price.author.name if history_price.author else None),
                    }
                )
            else:
                logger.warning(f"history_price not found: sku:{sku_qs.sku_id, i.get('effective_author'), i.get('max_create_date')}")
        re_sku["history_price_data"] = history_price_data
        re_data.append(re_sku)
    if origins_qs_list:
        for origin in origins_qs_list:
            origin.__dict__.update(become_history=True)
            origin.save()

            # todo:添加through,修改is_deleted状态

            change_content.append(f"删除SKU:{origin.sku_id}")
    return re_data, add_images, del_images, change_content, reset_preview_state


def common_create_product_attr_options(product: Product, attr_options_data: list):
    """
    公共创建商品attr option逻辑
    :param product: 新建的商品对象
    :param attr_options_data: attroptions 列表
    :return:
    """
    new_options = []

    for attr_option_data in attr_options_data:
        # 剔除没值的数据
        attr_value = attr_option_data.get("value")
        if not attr_value:
            continue

        attr_id = attr_option_data.get("attr_id")

        if not attr_id:
            raise APIViewException(err_message="非法商品参数")

        if not ProductAttrList.objects.filter(id=attr_id, is_deleted=False).exists():
            raise APIViewException(err_message="商品属性不存在,请刷新页面后重试")

        try:
            attr_value_object = ProductAttrValues.objects.only("id").get(attr_id=attr_id, name=attr_value, is_deleted=False)
        except ProductAttrValues.DoesNotExist:
            raise APIViewException(err_message="商品属性值不存在,请属性页面后重试")

        attr_option_data["product"] = product.id
        attr_option_data["attr"] = attr_id
        attr_option_data["attr_value"] = attr_value_object.id
        new_options.append(attr_option_data)

    if not new_options:
        return

    attr_options = ProductAttrOptionCreateOrUpdateSer(data=new_options, many=True)
    if not attr_options.is_valid():
        raise FieldsError(attr_options.errors)
    instances = attr_options.save()
    return instances


def common_create_sku_and_spec_options(product: Product, skus_data: list, request: Request, company_code: str):
    """
    公共创建sku
    :param product:
    :param skus_data:
    :param request:
    :return:
    """
    re_data = []
    qdrant_images = []
    sku_order = 1
    for sku_data in skus_data:
        # 已清理的spec信息
        specs = sku_data.get("specs") or []

        sku_data["product"] = product.id
        sku_data["company_code"] = company_code
        sku_data["spec_code"] = sku_data.get("spec_code", None)
        sku_data["order"] = sku_order
        if sku_data.get("image"):
            qdrant_images.append(sku_data["image"])

        # 创建sku
        sku_ser = StockKeepingUnitSerializer(data=sku_data, context={"request": request})
        if not sku_ser.is_valid():
            raise FieldsError(sku_ser.errors)

        sku_instance = sku_ser.save()
        sku_order += 1
        # 自营供应商新增商品必须填写建议售价
        if product.company.is_self_support and not sku_instance.retail_price:
            raise APIViewException(err_message="自营供应商新增商品必须填写建议售价")

        # 添加sku规格信息
        sku_specs_add_handler(sku_instance, specs)

        re_sku = StockKeepingUnitSerializer(instance=sku_instance, context={"request": request}).data
        re_sku.pop("product")

        re_data.append(re_sku)
    return re_data, qdrant_images


def dd_common_create_sku_and_spec_options(product: Product, skus_data: list, request: Request, company_code: str):
    """
    抖店专用公共创建sku
    :param product:
    :param skus_data:
    :param request:
    :return:
    """
    re_data = []
    qdrant_images = []
    sku_order = 1
    for sku_data in skus_data:
        # 已清理的spec信息
        specs = sku_data.get("specs") or []

        sku_data["product"] = product.id
        sku_data["company_code"] = company_code
        sku_data["spec_code"] = sku_data.get("spec_code", None)
        sku_data["order"] = sku_order
        if sku_data.get("image"):
            qdrant_images.append(sku_data["image"])

        # 创建sku
        sku_ser = StockKeepingUnitSerializer(data=sku_data, context={"request": request})
        if not sku_ser.is_valid():
            raise FieldsError(sku_ser.errors)

        sku_instance = sku_ser.save()
        sku_order += 1
        # 自营供应商新增商品必须填写建议售价
        if product.company.is_self_support and not sku_instance.retail_price:
            raise APIViewException(err_message="自营供应商新增商品必须填写建议售价")

        # 添加sku规格信息
        sku_specs_add_handler(sku_instance, specs)

        re_sku = StockKeepingUnitSerializer(instance=sku_instance, context={"request": request}).data
        re_sku.pop("product")

        re_data.append({sku_data.get("dy_sku_id"): re_sku})
    return re_data, qdrant_images


def common_create_gift(product: Product, gifts: dict, company_id: int):
    """
    公共创建赠品
    Args:
        product (Product): _description_
        gift (dict): {}
        user_type (str): SP/OP
        company_id (int): company.id
    """
    if not gifts:
        return None, None
    assert gifts.get("has_gift") is not None, "has_gift required"
    has_gift = gifts.pop("has_gift")
    if not has_gift:
        return None, None
    gift_products = gifts.pop("gift_products", [])
    assert len(gift_products) <= 5, "length of gift_products should no more than 5"
    gift_type = gifts.get("gift_type")
    assert gift_type and gift_type in ("PO", "AC"), "gift_type should be PO or AC"

    if gift_type == "PO":
        assert gift_products, "gift_products required"
        data_list = []
        for product_id in gift_products:
            # 赠品供应商和主商品供应商必须一致
            g_product = Product.objects.filter(product_id=product_id, is_deleted=False, company_id=company_id).first()
            if not g_product:
                # 赠品不存在
                raise ValueError(f"gift product({product_id}) not found")
            data = {"product_id": product.product_id, "gift_type": "PO", "gift_product_id": product_id}
            data_list.append(ProductGift(**data))
        instances = ProductGift.objects.bulk_create(data_list)
    else:
        assert gifts.get("name") and gifts.get("material") and gifts.get("image"), "name, material and image required"
        gifts["product_id"] = product.product_id
        instances = ProductGift.objects.create(**gifts)
    product.refresh_from_db()
    product.has_gift = has_gift
    product.gift_type = gift_type
    product.save()
    return gift_type, instances


def common_create_product(
    data: dict,
    specs_list: list,
    request: Request,
    prod_create_ser: serializers.SerializerMetaclass,
) -> Product:
    """
    公共创建商品函数
    :param data:
    :param specs_list:
    :param request:
    :param prod_create_ser:
    :return:
    """
    # 校验分类
    category = data.get("category")
    validate_category(category)

    # 校验labels
    labels = data.pop("labels", [])
    cleaned_labels = clean_create_labels(labels)

    current_user_type = get_current_user_type(request)
    # 运营商创建商品自动通过审核
    if current_user_type == "OP":
        # 商品审核
        # data["state"] = 1
        # 数据来源
        data["data_source"] = "OP"

    # 保留数据到specs_list
    data["spec_lists"] = specs_list

    product_ser = prod_create_ser(data=data, context={"request": request})
    if not product_ser.is_valid():
        raise FieldsError(product_ser.errors)

    prod_instance = product_ser.save()
    # 添加spec key关联
    for specs in specs_list:
        prod_instance.specs.add(specs.get("source_id"))
    # 添加spec val关联
    for specs in specs_list:
        for val in specs["values"]:
            prod_instance.specs_value.add(val["value_id"])

    # 添加labels_relate
    if get_current_user_type(request) != "SP":
        for label_pk in cleaned_labels:
            prod_instance.labels_relate.add(label_pk)

    pic_score = "0.0"
    if prod_instance.main_images:
        image_url = prod_instance.main_images[0]
        # 保存商品的相似度
        image_key = md5(str(image_url).encode()).hexdigest()
        cache_key = f"pic_score:{image_key}"
        # 保存1小时
        val = cache.get(cache_key)
        if val:
            pic_score = val

    prod_instance.pic_score = pic_score
    prod_instance.save(update_fields=["pic_score"], need_update_date=False)
    return prod_instance


def common_query_item_skus_inventory(request: Request, plan_id: int, product_id: int):
    """
    查询item skus库存信息
    :param request:
    :param plan_id:
    :param product_id:
    :return:
    """
    item_sku_condition = dict(
        selection_plan_id=plan_id,
        item__product__product_id=product_id,
        become_history=False,
        item__is_deleted=False,
    )

    if get_current_user_type(request) == "DB":
        item_sku_condition["selection_plan__distributor"] = request.user.distributor

    item_skus = ProductSelectionItemSKU.objects.prefetch_related("item", "sku").filter(**item_sku_condition)

    re_data = {}
    items = []

    for item_sku in item_skus:
        sku = item_sku.sku

        val_list = sku.specs_value.values_list("value", flat=True)
        items.append(
            {
                "sku_id": item_sku.sku_id,
                "name": "/".join(val_list) or "默认规格",
                "physical_inventory": item_sku.physical_inventory,
                "can_use_inventory": (sku.can_use_inventory or 0) + (item_sku.physical_inventory or 0),
                "remark": item_sku.remark or "",
            }
        )

        re_data["selection_reason"] = item_sku.item.selection_reason

    re_data["items"] = items
    return re_data


def common_update_plan_item_skus_fc(request: Request, plan_id: int, product_id: int):
    """
    item sku更新
    :param request:
    :param plan_id:
    :param product_id:
    :return:
    """
    post_sku_data = request.data.get("sku_data")
    selection_reason = request.data.get("selection_reason", "") or ""

    if not isinstance(post_sku_data, list):
        return IResponse(code=400, message="提交信息错误")

    if not post_sku_data:
        return IResponse(code=400, message="sku信息不能为空")

    try:
        item_condition = dict(selection_plan_id=plan_id, product_id=product_id, is_deleted=False)

        if get_current_user_type(request) == "DB":
            item_condition["selection_plan__distributor"] = request.user.distributor

        item = ProductSelectionItem.objects.get(**item_condition)
    except ProductSelectionItem.DoesNotExist:
        return IResponse(code=400, message="货盘商品不存在或不可编辑")

    try:
        product = Product.objects.get(product_id=product_id, is_deleted=False)
    except Product.DoesNotExist:
        return IResponse(code=400, message="商品不存在")

    # 先查货盘计划状态
    current_user = request.user

    try:
        selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(message="货盘计划不存在")

    if selection_plan.state in [0, 2]:
        return IResponse(message="this state can't edit")

    user_id = read_lock(plan_id)
    if current_user.user_id != user_id:
        return IResponse(code=400, message="Not granted editing permissions")

    # 判断sku data
    skus = product.stockkeepingunit_set.filter(become_history=False).only(
        "sku_id",
        "physical_inventory",
        "plan_use_inventory",
        "can_use_inventory",
    )
    inventory_skus_map = {
        sku.sku_id: {
            "physical_inventory": sku.physical_inventory or 0,
            "plan_use_inventory": sku.plan_use_inventory or 0,
            "can_use_inventory": sku.can_use_inventory or 0,
        }
        for sku in skus
    }
    # 原来的sku_data信息
    origin_item_skus = item.productselectionitemsku_set.filter(become_history=False).only("sku_id", "physical_inventory")
    origin_item_skus_map = {
        origin_item_sku.sku_id: {
            "object": origin_item_sku,
            "physical_inventory": origin_item_sku.physical_inventory or 0,
        }
        for origin_item_sku in origin_item_skus
    }

    # 组合商品
    sub_skus_map = {}
    if product.is_combine:
        sub_skus = product.substockkeepingunit_set.filter(become_history=False, parent_sku_id__in=inventory_skus_map.keys())
        for sub_sku in sub_skus:
            sub_parent_sku_id = sub_sku.parent_sku_id
            if sub_parent_sku_id not in sub_skus_map:
                sub_skus_map[sub_parent_sku_id] = [sub_sku]
            else:
                sub_skus_map[sub_parent_sku_id].append(sub_sku)

    # 首次判断, 清理提交数据
    for sku_data in post_sku_data:
        sku_id = sku_data.get("sku_id")
        physical_inventory = sku_data.get("physical_inventory", 0) or 0

        if not str(sku_id).isdigit():
            return IResponse(code=400, message=f"非法SKU_ID:{sku_id}")

        sku_id = int(sku_id)
        if not sku_id or not inventory_skus_map.get(sku_id) or not origin_item_skus_map.get(sku_id):
            return IResponse(code=400, message="错误SKU信息,请刷新页面后重试")

        sku_inventory_obj = inventory_skus_map.get(int(sku_id))
        exist_item_sku_inventory = origin_item_skus_map.get(sku_id)["physical_inventory"]

        # 实际的可用库存
        act_can_use_inventory = sku_inventory_obj.get("can_use_inventory") + exist_item_sku_inventory

        if act_can_use_inventory < 0:
            return IResponse(
                code=400,
                message=f"{sku_id}可用库存为:{act_can_use_inventory}, 请联系专员维护",
            )

        if physical_inventory > act_can_use_inventory:
            return IResponse(
                code=400,
                message=f"{sku_id}数量:{physical_inventory}. 已超过可用库存: {act_can_use_inventory}",
            )

        if product.is_combine:
            if sku_id not in sub_skus_map:
                raise ValueError("错误组合商品SKU信息,请刷新后重试")

            sub_skus = sub_skus_map[sku_id]
            for sub_sku in sub_skus:
                act_need_inventory = physical_inventory * sub_sku.num
                relate_sku = sub_sku.relate_sku

                # 实际能用的库存 = sku可用库存 + 旧(占用)的可用库存
                act_sub_can_use_inventory = relate_sku.can_use_inventory + sub_sku.num * exist_item_sku_inventory

                if act_sub_can_use_inventory < 0:
                    raise ValueError(f"组合商品{sku_id}可用库存为:{act_sub_can_use_inventory}, 请联系专员维护")

                if act_need_inventory > act_sub_can_use_inventory:
                    # raise ValueError(f"{sku_id}数量:{physical_inventory}. 已超过可用库存: {can_use_inventory}")
                    raise ValueError(f"组合商品{sku_id}库存不足")

    with transaction.atomic():
        # 更新理由
        # item.selection_reason = selection_reason
        item.update_user = current_user.user_id
        item.save(update_fields=["update_user"])

        # 库存锁
        ts_point = transaction.savepoint()

        try_times = 3
        log_data_list = []
        for sku_data in post_sku_data:
            for i in range(try_times):
                sku_id = sku_data.get("sku_id")
                physical_inventory = sku_data.get("physical_inventory", 0) or 0
                remark = sku_data.get("remark", "") or ""

                if not str(sku_id).isdigit():
                    transaction.savepoint_rollback(ts_point)
                    return IResponse(code=400, message=f"非法SKU_ID:{sku_id}")

                try:
                    sku_id = int(sku_id)
                    _sku = StockKeepingUnit.objects.get(sku_id=sku_id, become_history=False)
                except StockKeepingUnit.DoesNotExist:
                    transaction.savepoint_rollback(ts_point)
                    return IResponse(code=400, message=f"SKU_ID:{sku_id}不存在")

                origin_item_sku_inventory = origin_item_skus_map.get(sku_id)["physical_inventory"]

                # 实际库存 = sku当前可用加上原有库存
                act_can_use_inventory = _sku.can_use_inventory + origin_item_sku_inventory
                # 实际计算库存 = 物理库存 - 占用库存 + 原item_sku库存
                act_cal_can_use_inventory = _sku.physical_inventory - _sku.plan_use_inventory + origin_item_sku_inventory

                if (physical_inventory > act_can_use_inventory) or (physical_inventory > act_cal_can_use_inventory):
                    transaction.savepoint_rollback(ts_point)
                    logger.warning(f"加货盘库存不足 >> {sku_id},{product_id}")
                    return IResponse(code=400, message="库存不足")

                # 判断组合商品库存
                if product.is_combine:
                    sub_skus = _sku.parent_sub_skus.filter(become_history=False)
                    for sub_sku in sub_skus:
                        relate_sku = sub_sku.relate_sku
                        # 实际需要的库存 = 关联的数量 * 占用库存
                        act_need_inventory = sub_sku.num * physical_inventory

                        # 实际可用库存
                        ac_relate_sub_can_use_inventory = (relate_sku.can_use_inventory or 0) + (origin_item_sku_inventory or 0) * sub_sku.num

                        if act_need_inventory > ac_relate_sub_can_use_inventory:
                            transaction.savepoint_rollback(ts_point)
                            logger.warning(f"组合商品加货盘库存不足 >> {sku_id},{product_id},{relate_sku},{act_need_inventory},{ac_relate_sub_can_use_inventory}")
                            raise ValueError("组合商品库存不足")

                        # 更新关联sku的占用库存
                        relate_sku.plan_use_inventory = F("plan_use_inventory") - (origin_item_sku_inventory or 0) * sub_sku.num + act_need_inventory
                        relate_sku.save(update_fields=["plan_use_inventory"])

                # 原sku库存
                origin_sku_plan_use_inventory = _sku.plan_use_inventory
                new_plan_use_inventory = origin_sku_plan_use_inventory - origin_item_sku_inventory + physical_inventory
                can_use_inventory = _sku.physical_inventory - new_plan_use_inventory

                # 实际需要查询的可用库存
                filter_can_use_inventory = physical_inventory - origin_item_sku_inventory

                sku_update_rows = StockKeepingUnit.objects.filter(
                    sku_id=sku_id,
                    become_history=False,
                    can_use_inventory__gte=filter_can_use_inventory,
                ).update(
                    plan_use_inventory=new_plan_use_inventory,
                    can_use_inventory=can_use_inventory,
                )

                if sku_update_rows == 0:
                    if i == try_times - 1:
                        transaction.savepoint_rollback(ts_point)
                        return IResponse(code=400, message="修改货盘库存失败,请重试")

                    continue

                origin_item_sku = origin_item_skus_map.get(sku_id)["object"]

                # 更新库存
                update_physical_inventory = physical_inventory or 0
                # 库存跟预估销量一起联动
                origin_item_sku.physical_inventory = update_physical_inventory
                origin_item_sku.estimated_sales = update_physical_inventory
                origin_item_sku.remark = remark
                setattr(origin_item_sku, "skip_history_when_saving", False)
                origin_item_sku.save(update_fields=["physical_inventory", "estimated_sales", "remark"])

                log_data_list.append(f"SKU:{sku_id}修改预购销量:{origin_item_sku_inventory}为{physical_inventory}")
                break

        transaction.savepoint_commit(ts_point)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(plan_id),
                model=ProductSelectionPlan,
                describe=f'货盘计划"{plan_id}"修改了商品"{product_id}"库存:{"、".join(log_data_list)}',
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        # 插入操作明细日志
        w_plan_record(
            plan_id=plan_id,
            record_type=ProductSelectionPlanRecordType.MOD_SKU_INVENTORY,
            content=f"编辑了:{product_id}SKU库存",
            create_user=current_user.user_id,
            product_id=product_id,
        )

    return IResponse()


def bulk_delete_selection_products_fc(request: Request, plan_id: int):
    """
    批量删除货盘计划商品
    :param request:
    :param plan_id:
    :return:
    """
    raw_data = request.data
    current_user = request.user
    product_id_list = raw_data.get("product_id_list")
    delete_reason = raw_data.get("delete_reason", "") or ""
    if not product_id_list:
        return IResponse(code=400, message="商品id不能为空")
    if not isinstance(product_id_list, list):
        return IResponse(code=400, message="invalid params")

    product_id_list = list(set(product_id_list))

    try:
        query_conditions = {"plan_id": plan_id}
        if get_current_user_type(request) == "DB":
            query_conditions["distributor"] = current_user.distributor
        selection_plan = ProductSelectionPlan.objects.get(**query_conditions)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")
    if selection_plan.state in [0, 2]:
        return IResponse(code=400, message="this state can't edit")

    user_id = read_lock(plan_id)
    if current_user.user_id != user_id:
        return IResponse(code=400, message="Not granted editing permissions")

    items = ProductSelectionItem.objects.filter(selection_plan=plan_id, product_id__in=product_id_list, is_deleted=False)
    if items.count() != len(product_id_list):
        return IResponse(code=400, message="数据错误,请刷新页面后重试")

    with transaction.atomic():
        # 释放sku库存
        for item in items:
            item_skus = item.productselectionitemsku_set.filter(become_history=False)
            for item_sku in item_skus:
                if item_sku.physical_inventory and item_sku.physical_inventory > 0:
                    sku = StockKeepingUnit.objects.select_for_update().get(sku_id=item_sku.sku_id)
                    # 减去sku的占用库存
                    sku.plan_use_inventory -= item_sku.physical_inventory or 0
                    sku.save(update_fields=["plan_use_inventory", "can_use_inventory"])

                    # 组合商品释放占用库存
                    if sku.product.is_combine:
                        sub_skus = sku.parent_sub_skus.filter(become_history=False)
                        for sub_sku in sub_skus:
                            relate_sku = StockKeepingUnit.objects.select_for_update().get(sku_id=sub_sku.relate_sku_id)
                            relate_sku.plan_use_inventory = F("plan_use_inventory") - (item_sku.physical_inventory * sub_sku.num or 0)
                            relate_sku.save(update_fields=["plan_use_inventory"])

            item.is_deleted = True
            item.delete_reason = delete_reason
            item.update_user = current_user.user_id
            # 释放锁定状态
            item.product_confirm_state = 0
            item.save()

            # 插入操作明细日志
            w_plan_record(
                plan_id=plan_id,
                record_type=ProductSelectionPlanRecordType.DEL_PROD,
                content="",
                create_user=current_user.user_id,
                product_id=item.product_id,
            )

    # 日志记录
    try:
        set_log_params(
            request,
            resource_id=str(plan_id),
            model=ProductSelectionPlan,
            describe=f'货盘计划"{selection_plan}"批量移除商品',
            operate_content=f"商品ID列表,{','.join([str(i) for i in product_id_list])}",
            is_success_input=True,
        )
    except Exception as e:
        logger.warning(f"set_log_params catch error: {e}")
        pass
    return IResponse()


def selection_plan_prods_map_for_feishu(request: Request, plan_id: int):
    """
    排品地图商品列表
    :param request:
    :param plan_id:
    :return:
    """
    try:
        re_data = {}
        current_user = request.user

        # current_user_type = get_current_user_type(request)

        raw_params = request.query_params.copy()
        # 前端参数转换
        and_Q = None
        if raw_params.get("name"):
            raw_params["product__name"] = raw_params.pop("name")[0]

        if raw_params.get("company_id"):
            raw_params["product__company__company_id"] = raw_params.pop("company_id")[0]

        if raw_params.get("product_id"):
            raw_params["product__product_id"] = raw_params.pop("product_id")[0]
        if raw_params.get("code"):
            tmp_code = raw_params.pop("code")[0]
            if str(tmp_code).startswith("J"):
                tmp_code = tmp_code[1:]

            # if current_user_type == "DB":
            #     if not and_Q:
            #         and_Q = Q()
            #     and_Q.add(Q(**{"product__code__icontains": tmp_code}), Q.OR)
            #     and_Q.add(Q(**{"sub_product__code__icontains": tmp_code}), Q.OR)
            # else:
            #     raw_params["product__code"] = tmp_code

        # 支持多级分类查询
        if raw_params.get("category"):
            raw_params["product__category__contains"] = raw_params.pop("category")[0]

        if raw_params.get("physical_inventory"):
            raw_params["product__physical_inventory__range"] = raw_params.pop("physical_inventory")[0]
        if raw_params.get("history_price"):
            history_price = json.loads(raw_params.pop("history_price")[0])
            if history_price:
                and_Q = get_range_query(
                    "product__min_history_price",
                    "product__max_history_price",
                    history_price,
                )

        if raw_params.get("orders"):
            orders = json.loads(raw_params.pop("orders")[0])
            orders = [f"product__{i}" for i in orders]
            # 排品地图
            raw_params["orders"] = json.dumps(orders)
        else:
            raw_params["orders"] = json.dumps(["map_order"])

        try:
            query_condition = {"plan_id": plan_id}

            selection_plan = ProductSelectionPlan.objects.get(**query_condition)
        except ProductSelectionPlan.DoesNotExist:
            return IResponse(code=400, message="data not found")

        filters = {
            "selection_plan_id": plan_id,
            "is_deleted": False,
            "product__is_deleted": False,
        }

        hybrid_fields = [
            "product__name",
            "product__product_id",
            "product__code",
            "product__stockkeepingunit__link_code",
            "product__stockkeepingunit__spec_code",
        ]

        # if current_user_type == "DB":
        #     hybrid_fields.append("sub_product__code")
        #
        #     if raw_params.get("hybrid_search"):
        #         _tmp_hybrid_search = raw_params.pop("hybrid_search")[0]
        #         if str(_tmp_hybrid_search).startswith("J"):
        #             raw_params["hybrid_search"] = str(_tmp_hybrid_search)[1:]

        _, _, item_qs = custom_filter(
            raw_params,
            ProductSelectionItem,
            array_fields=["product__category__contains"],
            like_fields=[
                "product__name",
                "product__code",
                "sub_product_code",
                "product__product_id",
            ],
            hybrid_fields=hybrid_fields,
            query_Q=and_Q,
            **filters,
        )

        # 补充计划信息
        re_data["name"] = selection_plan.name
        re_data["live_date_start"] = selection_plan.live_date_start
        re_data["live_date_end"] = selection_plan.live_date_end
        re_data["plan_id"] = selection_plan.plan_id
        re_data["state"] = selection_plan.state

        item_qs = item_qs.select_related("product", "product__company")
        # 上播率计算
        item_live_counts = (
            ProductSelectionItem.objects.values("has_live")
            .annotate(calc_count=Count("id"))
            .filter(selection_plan_id=selection_plan.plan_id, is_deleted=False)
            .values(
                "has_live",
                "calc_count",
            )
        )
        total_count = 0
        live_count = 0
        for i in item_live_counts:
            total_count += i["calc_count"]
            if i["has_live"]:
                live_count += i["calc_count"]
        re_data["plan_inventory_warning_count"] = 0
        re_data["wait_review_count"] = 0
        re_data["live_rate"] = 0

        if total_count:
            re_data["live_rate"] = round(live_count / total_count, 4)

        request.user.distributor = selection_plan.distributor
        # 排品地图
        ser_data = SelectionMapItemSer(instance=item_qs, many=True, context={"request": request, "for_feishu": True}).data
        prod_counts = len(ser_data)

        re_data["data"] = ser_data
        re_data["count"] = prod_counts

        plan_inventory_warning_count = 0
        wait_review_count = 0

        need_flush_read_objs = []

        for _d in ser_data:
            item_pk = _d.pop("id")
            item_not_read = _d.get("not_read")

            if _d["can_use_inventory"] < 0:
                plan_inventory_warning_count += 1

            if _d["product_state"] in [4]:
                wait_review_count += 1

            if item_not_read:
                need_flush_read_objs.append(current_user.user_id)

        re_data["plan_inventory_warning_count"] = plan_inventory_warning_count
        re_data["wait_review_count"] = wait_review_count

        if need_flush_read_objs:
            flush_user_read_selection_plan_status.delay(need_flush_read_objs, current_user.user_id)
        return IResponse(data=re_data)
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=500, message=str(e))


def v2_plan_tree_list(
    request: Request,
    plan_id: int,
):
    params = request.query_params.copy()

    current_user_type = get_current_user_type(request)
    current_user = request.user

    try:
        condition = {"plan_id": plan_id}
        if current_user_type == "DB":
            condition["distributor"] = current_user.distributor

        plan = ProductSelectionPlan.objects.get(**condition)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")
    # 添加货盘监听
    if current_user.real_name != "飞书跑数据":
        send_selection_plan_listener_task(request, "货盘-商品列表", plan)

    items = ProductSelectionItem.objects.prefetch_related(
        "product",
        "product__handcard",
        "plan_category",
        "plan_category__category",
        "plan_category__plan_company",
        "plan_category__plan_company__company",
        "product__stockkeepingunit_set",
    ).filter(is_deleted=False, selection_plan_id=plan_id)

    # 货盘排除特定分销商可见商品
    if current_user_type == "DB":
        cannot_see_product_id_list = (
            ProductConfig.objects.exclude(
                visible_distributor=[],
            )
            .exclude(visible_distributor__contains=current_user.distributor.distributor_id)
            .values_list("product_id", flat=True)
        )
        items = items.exclude(product_id__in=cannot_see_product_id_list)

    order_map = {
        "min_cost_price": "product__min_cost_price",
        "-min_cost_price": "-product__min_cost_price",
        "physical_inventory": "product__physical_inventory",
        "-physical_inventory": "-product__physical_inventory",
    }
    _, _, items = custom_django_filter(
        request,
        items,
        PlanV2FilterSet,
        iserializer=None,
        need_serialize=False,
        need_paginator=False,
        order_map=order_map,
    )

    re_data = {
        "plan_id": plan.plan_id,
        "name": plan.name,
        "live_date_start": plan.live_date_start,
        "live_date_end": plan.live_date_end,
        "state": plan.state,
        "plan_inventory_warning_count": 0,
        "wait_review_count": 0,
        "count": 0,
    }

    item_live_counts = (
        items.values("has_live")
        .annotate(count=Count("has_live"))
        .values(
            "has_live",
            "count",
        )
    )

    total_count = 0
    live_count = 0
    for i in item_live_counts:
        total_count += i["count"]
        if i["has_live"]:
            live_count += i["count"]
    re_data["plan_inventory_warning_count"] = 0
    re_data["wait_review_count"] = 0
    re_data["live_rate"] = 0
    if total_count:
        re_data["live_rate"] = round(live_count / total_count, 4)

    # 查询sub_product信息
    sub_product_map = {}
    current_user_type = get_current_user_type(request)
    if current_user_type == "DB":
        sub_product_ids = {item.sub_product_id for item in items}
        sub_products = SubProduct.objects.filter(product_id__in=sub_product_ids, owner=plan.distributor)
        sub_product_map = {sub_product.parent_product_id: sub_product for sub_product in sub_products}

    # 批量查询额外查询
    products = [item.product for item in items]

    bulk_product_extra_tags = ProductBulkExtraTags(products)
    ser_data = OPPlanListSer(instance=products, many=True, context={"sub_products_map": sub_product_map, "current_user_type": current_user_type}).data
    product_map = {d["product_id"]: d for d in ser_data}

    # 查询标签
    item_product_ids = [item.product_id for item in items]
    product_labels_dict = bulk_query_labels(item_product_ids)

    # 批量查询category
    category_ids = set()
    for p in products:
        for c in p.category:
            category_ids.add(c)

    category_list = ProductCategoryList.objects.filter(id__in=list(category_ids)).only("id", "name")
    category_map = {category.id: {"id": category.id, "name": category.name} for category in category_list}
    # 查询所有的趋势
    trend_map = batch_calc_prod_trend(products)
    # 批量查询sku信息
    product_pk_list = {product.id for product in products}
    skus = StockKeepingUnit.objects.filter(product_id__in=product_pk_list, become_history=False)

    # skus = [sku for item in items for sku in item.product.stockkeepingunit_set.all() if not sku.become_history]

    # 批量查询history
    sku_primary_key_list = [sku.id for sku in skus]
    max_ids_4_sku = HistoryPrice.objects.filter(sku_id__in=sku_primary_key_list).values("sku_id").annotate(max_id=Max("id")).values("sku_id", "max_id")
    hp_ids = [obj["max_id"] for obj in max_ids_4_sku]
    history_price_objs = HistoryPrice.objects.filter(id__in=hp_ids).only("sku_id", "history_price")
    history_price_map = {history_price.sku_id: history_price.history_price for history_price in history_price_objs}

    # 批量查询sku的规格信息
    sku_specs = bulk_query_skus_specs_detail(skus)
    sku_map = {}
    sku_id_set = set()

    for sku in skus:
        sku_id_set.add(sku.id)
        if sku.product_id not in sku_map:
            sku_map[sku.product_id] = [sku]
            continue

        sku_map[sku.product_id].append(sku)

    item_skus = ProductSelectionItemSKU.objects.filter(
        sku__in=skus,
        become_history=False,
        item__in=items,
    ).only("sku_id", "physical_inventory", "estimated_sales")
    item_skus_map = {
        item_sku.sku_id: {
            "physical_inventory": item_sku.physical_inventory,
            "estimated_sales": item_sku.estimated_sales,
        }
        for item_sku in item_skus
    }

    # 查询托管状态
    hosting_product_map = bulk_query_product_hosting_state(item_product_ids)

    # 批量查询rank排行榜
    rank_map = bulk_query_product_category_ranks(list(product_pk_list))

    temp_data = {}

    if items:
        # 获取不能看的字段
        sku_cannot_view_fields = product_cannot_view_fields = current_user.get_user_cannot_view_fields(current_user_type, getattr(request, "client_code", "PC"))

    # 批量查询退费率
    has_permission, refund_rate_map = bulk_query_product_refund_rate(current_user, current_user_type, item_product_ids)

    # 标签数据
    items_mark_map = bulk_query_items_mark(items)
    # 更新用户阅读状态的id
    need_update_read_list = []
    for item in items:
        _company = item.plan_category.plan_company
        _category = item.plan_category
        # 公司信息
        _plan_company_id = _company.id
        _company_data = {
            "plan_company_id": _plan_company_id,
            "company_id": _company.company.company_id,
            "company_name": _company.company.name,
            "order": _company.order,
            "count": 0,
            "not_read": False,
            "data": {},
        }
        # 分类信息
        _plan_category_id = _category.id
        _category_data = {
            "plan_category_id": _plan_category_id,
            "category_id": _category.category_id,
            "category_name": _category.category.name,
            "order": _category.order,
            "count": 0,
            "not_read": False,
            "data": {},
        }

        # 商品信息
        _product = item.product
        _plan_product_id = item.id

        _product_data = product_map.get(_product.product_id)
        _category = _product_data.get("category")
        if _category:
            # 数据库保存的string类型数据，需要改成int获取map数据
            _product_data["category"] = [category_map.get(int(_category_id)) for _category_id in _category]
        # 成本价控制
        for f in product_cannot_view_fields:
            if _product_data.get(f):
                _product_data[f] = "***"

        _product_data["refund_rate"] = "***" if not has_permission else (refund_rate_map.get(item.product_id, None) or None)
        _product_data["extra_tags"] = bulk_product_extra_tags.get(_product.id)
        _product_data["has_live"] = item.has_live
        _product_data["product_confirm_state"] = item.product_confirm_state
        _product_data["remark"] = item.remark
        _product_data["selection_reason"] = item.selection_reason
        _product_data["order"] = item.order
        _product_data["plan_product_id"] = item.id
        _product_data["plan_use_physical_inventory"] = item.physical_inventory

        _product_data["inventory_trend"] = trend_map.get(f"{_product.product_id}_physical_inventory", "")
        _product_data["cost_price_trend"] = trend_map.get(f"{_product.product_id}_cost_price")

        # 排行榜信息
        _product_data["rank"] = rank_map.get(_product.id)

        # 添加skus数据
        skus_qs = sku_map.get(_product.id, [])
        skus_data = RawSKUSerializer(instance=skus_qs, many=True, context={"sku_specs": sku_specs, "request": request}).data
        for sku_data in skus_data:
            obj_pk = sku_data.pop("id")
            sku_data["specs"] = sku_specs.get(obj_pk) or []
            # 成本价控制
            for f in sku_cannot_view_fields:
                if sku_data.get(f):
                    sku_data[f] = "***"

            # 分销商拼接spec_code
            if current_user_type == "DB":
                if not request.user.distributor.letters:
                    raise APIViewException(err_message="缺少分销商代码")
                sku_data["spec_code"] = sku_data["spec_code"] + request.user.distributor.letters

            # 获取最新历史价
            sku_data["latest_history_price"] = history_price_map.get(obj_pk, None)
            # sku占用库存
            sku_data["plan_use_physical_inventory"] = (item_skus_map.get(sku_data["sku_id"])["physical_inventory"] or 0) if sku_data["sku_id"] in item_skus_map else 0
            sku_data["estimated_sales"] = (item_skus_map.get(sku_data["sku_id"])["estimated_sales"] or 0) if sku_data["sku_id"] in item_skus_map else 0

        _product_data["skus"] = skus_data

        _product_data["hosting_state"] = hosting_product_map.get(item.product_id)
        # 添加标签
        _product_data["labels"] = product_labels_dict.get(item.product_id) or []

        # 添加标记数据
        _product_data["mark"] = items_mark_map.get(_plan_product_id, []) or []

        # 是否已读
        not_read_status = current_user.user_id not in (item.read_users or [])
        _product_data["not_read"] = not_read_status
        if not_read_status:
            need_update_read_list.append(_plan_product_id)

        if _plan_company_id not in temp_data:
            temp_data[_plan_company_id] = _company_data
            temp_data[_plan_company_id]["data"][_plan_category_id] = _category_data
            temp_data[_plan_company_id]["data"][_plan_category_id]["data"][_plan_product_id] = _product_data
        else:
            if _plan_category_id not in temp_data[_plan_company_id]["data"]:
                temp_data[_plan_company_id]["data"][_plan_category_id] = _category_data

            temp_data[_plan_company_id]["data"][_plan_category_id]["data"][_plan_product_id] = _product_data

    order_fields = []
    if params.get("orders"):
        order_fields = json.loads(params.pop("orders")[0])

    # 排序
    data_list = sorted(temp_data.values(), key=lambda x: x["order"])

    for _company_data in data_list:
        _company_data["data"] = sorted(_company_data["data"].values(), key=lambda x: x["order"])

        for category_data in _company_data["data"]:
            if order_fields:
                _field_name = order_fields[0]
                if _field_name[0] == "-":
                    order_func = lambda x: -float(x[_field_name[1:]])
                else:
                    order_func = lambda x: float(x[_field_name])
            else:
                order_func = lambda x: x["order"]

            category_data["data"] = sorted(category_data["data"].values(), key=order_func)

    # 计算count
    total_count = 0
    plan_inventory_warning_count = 0
    wait_review_count = 0
    for _company_data in data_list:
        category_datas = _company_data["data"]
        company_count = 0
        for category_data in category_datas:
            company_count += len(category_data["data"])
            category_data["count"] = len(category_data["data"])
            for prod in category_data["data"]:
                if prod["can_use_inventory"] < 0:
                    plan_inventory_warning_count += 1

                if prod["product_state"] in [4]:
                    wait_review_count += 1

                # 如果有一个没读，把分类、供应商都置为未读
                product_not_read = prod["not_read"]
                if product_not_read:
                    category_data["not_read"] = True
                    _company_data["not_read"] = True
        _company_data["count"] = company_count
        total_count += company_count
    re_data["count"] = total_count
    re_data["plan_inventory_warning_count"] = plan_inventory_warning_count
    re_data["wait_review_count"] = wait_review_count
    re_data["data"] = data_list
    # 添加到已读
    if need_update_read_list:
        flush_user_read_selection_plan_status.delay(need_update_read_list, current_user.user_id)
    return IResponse(data=re_data)


def plan_profit_margin_download_data(request, plan_id):
    try:
        params = request.query_params.copy()
        # 是否为实际货盘利润
        actual = params.get("actual", "0")
        assert actual in ["0", "1", "true", "false", "True", "False"], "actual should be bool value"
        actual = False if actual in ["0", "false", "False"] else True

        current_user_type = get_current_user_type(request)
        PlanProfitMariginTmpl = OPPlanProfitMariginTmpl
        if current_user_type == "DB":
            if actual:
                PlanProfitMariginTmpl = DBPlanProfitMariginActualTmpl
            else:
                PlanProfitMariginTmpl = DBPlanProfitMariginTmpl
        else:
            if actual:
                PlanProfitMariginTmpl = OPPlanProfitMariginActualTmpl

        new_data_list = []
        raw_data, plan_name = plan_profit_margin(request, plan_id)
        for i in raw_data.get("data", []):
            i["sum_safety_inventory_estimated_total_profit"] = raw_data.get("sum_safety_inventory_estimated_total_profit")
            i["sum_physical_inventory_estimated_total_profit"] = raw_data.get("sum_physical_inventory_estimated_total_profit")
            i["sum_plan_physical_inventory_estimated_total_profit"] = raw_data.get("sum_plan_physical_inventory_estimated_total_profit")
            i["sum_physical_inventory_estimated_total_gross_profit"] = raw_data.get("sum_physical_inventory_estimated_total_gross_profit")
            i["sum_plan_physical_inventory_estimated_total_gross_profit"] = raw_data.get("sum_plan_physical_inventory_estimated_total_gross_profit")
            i["sum_actual_sales_total_profit"] = raw_data.get("sum_actual_sales_total_profit")
            i["sum_actual_sales_total_gross_profit"] = raw_data.get("sum_actual_sales_total_gross_profit")
            i["sum_estimated_acceptance_total_profit"] = raw_data.get("sum_estimated_acceptance_total_profit")
            i["sum_estimated_acceptance_total_gross_profit"] = raw_data.get("sum_estimated_acceptance_total_gross_profit")
            new_data_list.append(i)

        cn_data_list = []
        for data in new_data_list:
            cn_data = {}
            for k, cn_name in PlanProfitMariginTmpl.items():
                cn_data[cn_name] = data.get(k)
                if k in ["boundary_profit_rate", "gross_profit_rate"]:
                    cn_data[cn_name] = "{:.2%}".format(data.get(k)) if data.get(k) else data.get(k)
                else:
                    cn_data[cn_name] = data.get(k)
                if cn_name == "供应商名称":
                    # 此处插入数据，保证位置
                    specs = data.get("specs") if data.get("specs") else []
                    specs_list = []
                    for i in specs:
                        specs_list.append(f"{i['name']}:{i['value']}")
                    cn_data["规格"] = ",".join(specs_list) if specs_list else ""

                    category = data.get("category", [])
                    idx = 1
                    for item in category:
                        cn_data[f"{idx}级类目ID"] = item["id"]
                        cn_data[f"{idx}级类目"] = item["name"]
                        idx += 1
                    start_selling_price = data.get("start_selling_price") if data.get("start_selling_price") else ""
                    end_selling_price = data.get("end_selling_price") if data.get("end_selling_price") else ""
                    if not start_selling_price and not end_selling_price:
                        cn_data["建议售价区间"] = ""
                    else:
                        cn_data["建议售价区间"] = f"{start_selling_price}-{end_selling_price}"

                    estimated_acceptance_start_selling_price = data.get("estimated_acceptance_start_selling_price") if data.get("estimated_acceptance_start_selling_price") else ""
                    estimated_acceptance_end_selling_price = data.get("estimated_acceptance_end_selling_price") if data.get("estimated_acceptance_end_selling_price") else ""
                    if not estimated_acceptance_start_selling_price and not estimated_acceptance_end_selling_price:
                        cn_data["预估签收售价区间"] = ""
                    else:
                        cn_data["预估签收售价区间"] = f"{estimated_acceptance_start_selling_price}-{estimated_acceptance_end_selling_price}"

            cn_data_list.append(cn_data)
        last = {}
        if cn_data_list:
            for k, v in cn_data_list[-1].items():
                PlanProfitMariginTmpl_reverse = {v: k for k, v in PlanProfitMariginTmpl.items()}
                last[k] = ""
                if PlanProfitMariginTmpl_reverse.get(k):
                    if f"sum_{PlanProfitMariginTmpl_reverse[k]}" in list(raw_data.keys()):
                        last[k] = raw_data.get(f"sum_{PlanProfitMariginTmpl_reverse[k]}")
        last["商品ID"] = f"合计（{len(cn_data_list)} SKU）"
        cn_data_list.append(last)

        return cn_data_list, plan_name
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        raise e


def plan_profit_margin(request, plan_id, show_sub_code=True):
    try:
        current_user_type = get_current_user_type(request)
        current_user = request.user
        distributor = None
        plan_filters = {"plan_id": plan_id}
        plan_item_sku_filters = {
            "selection_plan_id": plan_id,
            "item__is_deleted": False,
            "become_history": False,
        }
        if current_user_type == "DB":
            distributor = current_user.distributor
            plan_filters["distributor_id"] = distributor.distributor_id
            plan_item_sku_filters["selection_plan__distributor_id"] = distributor.distributor_id

        params = request.query_params.copy()
        # 是否为实际货盘利润
        actual = params.get("actual", "0")
        assert actual in ["0", "1", "true", "false", "True", "False"], "actual should be bool value"
        actual = False if actual in ["0", "false", "False"] else True

        plan_has_ended = False
        # 获取成本项配置
        cost_sub_query = Prefetch("cost_item_config__cost_sub_items", queryset=CostSubItem.objects.all())
        estimated_acceptance_rate_query = Prefetch("cost_item_config__estimated_acceptance_rate_rules", queryset=EstimatedAcceptanceRateRules.objects.all())
        plan = ProductSelectionPlan.objects.filter(**plan_filters).prefetch_related(cost_sub_query, estimated_acceptance_rate_query).first()
        if not plan:
            return None, None

        plan_name = plan.name

        if plan.state == 0:
            # 货盘已经结束, 获取货盘结束时间对应的成本项
            plan_has_ended = True
            cost_item_config = plan.cost_item_config
        else:
            # 获取当前生效中的成本项
            cost_sub_query = Prefetch("cost_sub_items", queryset=CostSubItem.objects.all())
            estimated_acceptance_rate_query = Prefetch("estimated_acceptance_rate_rules", queryset=EstimatedAcceptanceRateRules.objects.all())
            cost_item_config = CostItemConfig.objects.filter(status="IE").prefetch_related(cost_sub_query, estimated_acceptance_rate_query).first()
        cost_sub_items_data = []
        price_fetching_rules = []
        estimated_acceptance_rate_rules_data = []
        if cost_item_config:
            price_fetching_rules = cost_item_config.price_fetching_rules
            cost_sub_items_data = CostSubItemDetailSerializer(instance=cost_item_config.cost_sub_items, many=True).data
            estimated_acceptance_rate_rules_data = EstimatedAcceptanceRateRulesDetailSerializer(instance=cost_item_config.estimated_acceptance_rate_rules, many=True).data

        # 货盘预估全部放出，货盘利润大于0的放出来
        if actual:
            plan_item_sku_filters["actual_sales__gt"] = 0

        skus_qs = ProductSelectionItemSKU.objects.filter(**plan_item_sku_filters)

        order_map = {
            "cost_price": "sku__cost_price",
            "-cost_price": "-sku__cost_price",
            "physical_inventory": "sku__physical_inventory",
            "-physical_inventory": "-sku__physical_inventory",
        }

        _, _, skus_qs = custom_django_filter(
            request,
            skus_qs,
            PlanProfitMarginFilterSet,
            iserializer=None,
            need_serialize=False,
            need_paginator=False,
            order_map=order_map,
        )

        latest_prices = (
            HistoryPrice.objects.filter(product__product_id=OuterRef("item__product_id"), sku_id=OuterRef("sku__id")).order_by("-create_date").values("history_price")[:1]
        )
        related_query = Prefetch("item__product", queryset=Product.objects.all())
        company_query = Prefetch("item__product__company", queryset=Company.objects.all())

        if current_user_type == "DB":
            latest_prices = HistoryPrice.objects.filter(author=distributor.live_author.author_id, sku_id=OuterRef("sku__id")).order_by("-create_date").values("history_price")[:1]
            related_subpro_query = Prefetch("item__sub_product", queryset=SubProduct.objects.all())
            skus_qs = skus_qs.annotate(latest_history_price=Subquery(latest_prices)).prefetch_related(related_query, company_query, related_subpro_query).select_related("sku")
        else:
            skus_qs = (
                skus_qs.annotate(latest_history_price=Subquery(latest_prices))
                .prefetch_related(
                    related_query,
                    company_query,
                )
                .select_related("sku")
            )

        # 查询sku规格信息
        skus = [obj.sku for obj in skus_qs]
        specs_map = bulk_query_skus_specs_detail(skus)

        data = []
        for sku in skus_qs:
            _product = sku.item.product
            if current_user_type == "DB" and sku.item.sub_product:
                _product = sku.item.sub_product
            # _product = sku.item.sub_product if current_user_type == "DB" else sku.item.product
            info = {
                "product_id": sku.item.product.product_id,
                "sub_product_id": sku.item.sub_product.product_id if sku.item.sub_product else None,
                "name": _product.name,
                "main_images": _product.main_images,
                "code": sku.item.product.code,
                "sub_code": sku.item.sub_product.code if sku.item.sub_product and show_sub_code else None,
                "specs": specs_map.get(sku.sku.id),
                "company_id": sku.item.product.company.company_id,
                "company_name": sku.item.product.company.name,
                "category": get_category_name_by_id_list(sku.item.product.category),
                "sku_id": sku.sku_id,
                "cost_price": sku.sku.cost_price,
                "retail_price": sku.sku.retail_price,
                "history_price": sku.latest_history_price,
                "physical_inventory": sku.sku.physical_inventory,
                "plan_physical_inventory": sku.physical_inventory,
                "safety_inventory": sku.sku.safety_inventory,
                "actual_sales": sku.actual_sales,
                "actual_price": sku.actual_price,
                "estimated_sales": sku.estimated_sales,
            }

            breakeven_price_rate = cost_item_config.breakeven_price_rate if cost_item_config else 1
            cal_info = calculate_cost(info, cost_sub_items_data, estimated_acceptance_rate_rules_data, price_fetching_rules, breakeven_price_rate, actual=actual)
            info.update(**cal_info)

            data.append(info)

        def safe_sum(values):
            return sum(x if x is not None else decimal.Decimal(0.00) for x in values)

        # 计算总和
        if not actual:
            fields_to_sum = [
                "safety_inventory_estimated_total_profit",
                "physical_inventory_estimated_total_profit",
                "plan_physical_inventory_estimated_total_profit",
                "physical_inventory_estimated_total_gross_profit",
                "plan_physical_inventory_estimated_total_gross_profit",
            ]
        else:
            fields_to_sum = [
                "actual_sales",
                "actual_sales_total_profit",
                "actual_sales_total_gross_profit",
                "estimated_acceptance_total_profit",
                "estimated_acceptance_total_gross_profit",
            ]
        fields_to_sum.extend(
            [
                "cost_price",
                "retail_price",
                "history_price",
                "physical_inventory",
                "plan_physical_inventory",
                "safety_inventory",
                "estimated_sales",
                "fixed_cost",
                "breakeven_price",
                "boundary_profit",
                "gross_profit",
                "estimated_sales_estimated_total_profit",
                "estimated_sales_estimated_gross_profit",
            ]
        )

        re_results = {f"sum_{field}": safe_sum(item.get(field) for item in data) for field in fields_to_sum}
        re_results["data"] = data
        re_results["plan_has_ended"] = plan_has_ended
        return re_results, plan_name
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        raise e


def calculate_actaul_saels_and_mark_has_live(plan_id, live_date_start, live_date_end, distributor_id):
    """
    计算实际销量和标记是否已播
    """
    try:
        item_sku_qs = ProductSelectionItemSKU.objects.filter(
            selection_plan_id=plan_id,
            item__is_deleted=False,
            become_history=False,
        ).values("sku", "item")
        sku_item_dict = {i["sku"]: i["item"] for i in item_sku_qs}

        live_date_start = timezone.make_aware(datetime.combine(live_date_start, datetime.min.time()), timezone.get_current_timezone()).strftime("%Y-%m-%d %H:%M:%S")
        live_date_end = timezone.make_aware(datetime.combine(live_date_end, datetime.max.time()), timezone.get_current_timezone()).strftime("%Y-%m-%d %H:%M:%S")

        # 子查询获取每个SKU的最新售价
        goods_price_subquery = (
            OriginalOrder.objects.filter(order_time__range=(live_date_start, live_date_end), distributor_id=distributor_id, sku__sku_id=OuterRef("sku__sku_id"))
            .order_by("-order_time")
            .values("goods_price")[:1]
        )
        orders = OriginalOrder.objects.filter(order_time__range=(live_date_start, live_date_end), distributor_id=distributor_id, sku__sku_id__in=sku_item_dict.keys())
        # 根据SKU分组,计算总销量,并获取最新售价
        # [{'sku__sku_id': 163841541, 'total_sales': 1, 'latest_price': Decimal('10.00')}]
        sku_sales = orders.values("sku__sku_id").annotate(total_sales=Sum("item_num"), latest_price=Subquery(goods_price_subquery))

        for i in sku_sales:
            item_sku = ProductSelectionItemSKU.objects.filter(
                sku_id=i["sku__sku_id"],
                item_id=sku_item_dict[i["sku__sku_id"]],
                item__is_deleted=False,
                become_history=False,
            ).first()
            if not item_sku:
                continue
            item_sku.actual_sales = i["total_sales"]
            item_sku.actual_price = i["latest_price"]
            item_sku.save(update_fields=["actual_sales", "actual_price"])
            item = item_sku.item
            item.has_live = True
            item.save(update_fields=["has_live"])
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")


def calculate_cost(sku_info, cost_sub_items_data, estimated_acceptance_rate_rules_data, price_fetching_rules, breakeven_price_rate, actual=False):
    """
    计算成本项

    Args:
        sku_info (_type_): _description_
        cost_sub_items_data (_type_): _description_
        estimated_acceptance_rate_rules_data (_type_): _description_
        price_fetching_rules (_type_): _description_
        breakeven_price_rate (_type_): _description_
        actual (bool, optional): 是否为实际货盘利润. Defaults to False.

    Raises:
        e: _description_

    Returns:
        _type_: _description_
    """

    try:
        re_data = {
            "fixed_cost": None,
            "breakeven_price": None,
            "boundary_profit": None,
            "boundary_profit_rate": None,
            "remark": None,
            "reference_price": None,
            "gross_profit": None,
            "gross_profit_rate": None,
        }

        if not cost_sub_items_data:
            logger.info(f"cost_sub_items_data: {cost_sub_items_data}, {type(cost_sub_items_data)}")
            return re_data
        cost_price = sku_info.get("cost_price", 0)
        retail_price = sku_info.get("retail_price", 0)
        history_price = sku_info.get("history_price", 0)
        # 实际销量
        actual_sales = sku_info.get("actual_sales", 0)
        actual_price = sku_info.get("actual_price", 0)

        target_cost_sub_item = {}
        target_estimated_acceptance_rate = {}
        # 获取卖价参考值
        reference_price = retail_price
        # 实际货盘利润且有实际销量，取对应订单实际卖价
        if actual and actual_sales > 0:
            if not actual_price:
                logger.error(f"{sku_info.get('sku_id')}:actual_sales > 0 but not actual_price({actual_price})")
                return re_data
            else:
                reference_price = actual_price
                re_data["history_price"] = actual_price
        else:
            if not reference_price and "FS" in price_fetching_rules:
                reference_price = history_price
            if not reference_price and "BP" in price_fetching_rules:
                # 取第一项的盈亏平衡价
                first_cost_sub_item = cost_sub_items_data[0]
                _fixed_cost = decimal.Decimal(first_cost_sub_item.get("total_cost", 0))
                _variable_cost_rate_total = decimal.Decimal(first_cost_sub_item.get("variable_cost_rate_total", 0))
                _breakeven_price = (cost_price + _fixed_cost) / (1 - _variable_cost_rate_total)
                reference_price = _breakeven_price / breakeven_price_rate

        # 没有目标价格
        logger.info(f"reference_price: {reference_price}, {type(reference_price)}")
        if not reference_price:
            return re_data
        for i in cost_sub_items_data:
            logger.info(f"get target_cost_sub_item: {decimal.Decimal(i['start_selling_price'])}-{reference_price}-{decimal.Decimal(i['end_selling_price'])}")
            if decimal.Decimal(i["start_selling_price"]) < reference_price <= decimal.Decimal(i["end_selling_price"]):
                target_cost_sub_item = i
        for j in estimated_acceptance_rate_rules_data:
            logger.info(f"get target_estimated_acceptance_rate: {decimal.Decimal(j['start_selling_price'])}-{reference_price}-{decimal.Decimal(j['end_selling_price'])}")
            if decimal.Decimal(j["start_selling_price"]) < reference_price <= decimal.Decimal(j["end_selling_price"]):
                target_estimated_acceptance_rate = j

        # 没有目标配置
        if not target_cost_sub_item:
            logger.info(f"target_cost_sub_item: {target_cost_sub_item}, {type(target_cost_sub_item)}")
            return re_data
        # 毛利润
        gross_profit = reference_price - cost_price
        fixed_cost = decimal.Decimal(target_cost_sub_item.get("total_cost", 0))
        variable_cost_rate_total = decimal.Decimal(target_cost_sub_item.get("variable_cost_rate_total", 0))
        # 边界利润
        boundary_profit = reference_price * (1 - variable_cost_rate_total) - (cost_price + fixed_cost)
        safety_inventory = sku_info["safety_inventory"] if sku_info.get("safety_inventory") else 0
        physical_inventory = sku_info["physical_inventory"] if sku_info.get("physical_inventory") else 0
        plan_physical_inventory = sku_info["plan_physical_inventory"] if sku_info.get("plan_physical_inventory") else 0
        # 该商品sku预估最后签收
        estimated_acceptance_rate = decimal.Decimal(target_estimated_acceptance_rate.get("estimated_acceptance_rate", 0))
        estimated_acceptance_count = actual_sales * estimated_acceptance_rate
        re_data["start_selling_price"] = target_cost_sub_item["start_selling_price"]
        re_data["end_selling_price"] = target_cost_sub_item["end_selling_price"]
        re_data["fixed_cost"] = round(fixed_cost, 2)
        re_data["breakeven_price"] = round((cost_price + fixed_cost) / (1 - variable_cost_rate_total), 2)
        re_data["boundary_profit"] = round(boundary_profit, 2)
        if re_data["boundary_profit"] < 0:
            re_data["remark"] = f"建议售价为：盈亏平衡价{re_data['breakeven_price']}"

        if not retail_price and not history_price:
            remark_value = re_data["breakeven_price"] / round(breakeven_price_rate, 2)
            re_data["remark"] = f"建议售价为：盈亏平衡价{re_data['breakeven_price']}/{round(breakeven_price_rate, 2)}={round(remark_value, 2)}"
        re_data["boundary_profit_rate"] = round(boundary_profit / reference_price, 2)
        re_data["gross_profit"] = round(gross_profit, 2)
        # 毛利率
        re_data["gross_profit_rate"] = round(gross_profit / reference_price, 2)
        re_data["reference_price"] = round(reference_price, 2)
        # 预估销量总利润
        re_data["estimated_sales_estimated_total_profit"] = round(sku_info["estimated_sales"] * boundary_profit, 2)
        # 预估销量毛利润
        re_data["estimated_sales_estimated_gross_profit"] = round(sku_info["estimated_sales"] * gross_profit, 2)

        if not actual:
            # 预估
            re_data["safety_inventory_estimated_total_profit"] = round(safety_inventory * boundary_profit, 2)
            re_data["physical_inventory_estimated_total_profit"] = round(physical_inventory * boundary_profit, 2)
            re_data["plan_physical_inventory_estimated_total_profit"] = round(plan_physical_inventory * boundary_profit, 2)
            re_data["physical_inventory_estimated_total_gross_profit"] = round(physical_inventory * gross_profit, 2)
            re_data["plan_physical_inventory_estimated_total_gross_profit"] = round(plan_physical_inventory * gross_profit, 2)
        else:
            # 实际销量
            re_data["actual_sales"] = actual_sales
            re_data["actual_sales_total_profit"] = round(actual_sales * boundary_profit, 2)
            re_data["actual_sales_total_gross_profit"] = round(actual_sales * gross_profit, 2)
            re_data["estimated_acceptance_rate"] = round(estimated_acceptance_rate, 2)
            re_data["estimated_acceptance_start_selling_price"] = round(decimal.Decimal(target_estimated_acceptance_rate.get("start_selling_price", 0)), 2)
            re_data["estimated_acceptance_end_selling_price"] = round(decimal.Decimal(target_estimated_acceptance_rate.get("end_selling_price", 0)), 2)
            re_data["estimated_acceptance_total_profit"] = round(estimated_acceptance_count * boundary_profit, 2)
            re_data["estimated_acceptance_total_gross_profit"] = round(estimated_acceptance_count * gross_profit, 2)

        handle_signed_zero = {k: decimal.Decimal(0.00) for k, v in re_data.items() if isinstance(v, decimal.Decimal) and v.is_zero() and v.is_signed()}
        re_data.update(**handle_signed_zero)

        return re_data
    except Exception as e:
        logger.error(f"calculate_cost err: {str(e)}--{traceback.format_exc()}")
        raise e


def sync_to_sub_products(raw_product, new_product):
    """
    更新所有副本商品
    :param raw_product:
    :param new_product:
    :return:
    """
    sub_update_fields = ["name", "main_images", "detail_images", "remark", "size"]
    # 更新副本商品
    updated_fields = {f: getattr(new_product, f) for f in sub_update_fields}
    updated_fields["update_user"] = new_product.update_user
    updated_fields["update_date"] = timezone.now()
    updated_rows = SubProduct.objects.filter(is_deleted=False, parent_product=new_product, owner__product_info_sync=True).update(**updated_fields)
    print(f"更新条数:{updated_rows}")


def price_fill_data(start_date_str: str, end_date_str: str, origin_data: list, min_price_fill_val, price_name: str = "cost_price"):
    # 定义日期区间
    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

    # 将日期字符串转换为datetime对象的辅助函数
    def parse_date(date_str):
        return datetime.strptime(date_str, "%Y-%m-%d")

    # 填充数据的函数
    def _fill_data(start_date, end_date, original_data, min_price_fill_val=0):
        filled_data = []
        current_date = start_date
        prev_price = None  # 用于保存前一个日期的成本价格
        for data in original_data:
            while current_date < parse_date(data["date"]):
                if prev_price is not None:
                    filled_data.append({"date": current_date.strftime("%Y-%m-%d"), price_name: prev_price})
                else:
                    filled_data.append({"date": current_date.strftime("%Y-%m-%d"), price_name: min_price_fill_val})
                current_date += timedelta(days=1)
            filled_data.append(data)
            current_date += timedelta(days=1)
            prev_price = data[price_name]
        while current_date <= end_date:
            if prev_price is not None:
                filled_data.append({"date": current_date.strftime("%Y-%m-%d"), price_name: prev_price})
            else:
                filled_data.append({"date": current_date.strftime("%Y-%m-%d"), price_name: min_price_fill_val})
            current_date += timedelta(days=1)
        return filled_data

    # 填充数据
    filled_data = _fill_data(start_date, end_date, origin_data, min_price_fill_val)
    return filled_data


def bulk_move_items_to_new_plan(
    request,
    current_user,
):
    def validate_plan_product_pks(_plan_product_pks):
        if not isinstance(plan_product_pks, list):
            raise APIViewException(err_message="货盘商品ID格式错误,请刷新后重试")

        if not plan_product_pks:
            raise APIViewException(err_message="货盘商品ID不能为空")

    def get_plan(_plan_id, distributor, _current_user_type):
        try:
            if _current_user_type == "DB":
                return ProductSelectionPlan.objects.get(plan_id=plan_id, distributor=distributor)
            return ProductSelectionPlan.objects.get(plan_id=plan_id)
        except ProductSelectionPlan.DoesNotExist:
            raise APIViewException(err_message=f"{plan_id}货盘不存在")

    def check_plan_editability(_plan):
        if _plan.state in [0, 2]:
            raise ValueError(f"当前货盘:{_plan}{_plan.get_state_display()},不可编辑")

    def validate_plan_items(_plan, _plan_product_pks):
        item_count = _plan.productselectionitem_set.filter(
            pk__in=_plan_product_pks,
            is_deleted=False,
            product__is_deleted=False,
            product__can_use_inventory__gte=0,
        ).count()
        if item_count != len(_plan_product_pks):
            raise APIViewException(err_message="移动商品中存在库存不足或已删除的数据，无法移动")

    def set_lock(_plan_id, _user_id):
        setnx_lock(_plan_id, _user_id, 60 * 5)
        lock_user_id = read_lock(_plan_id)
        if _user_id != lock_user_id:
            raise APIViewException(err_message="Not granted editing permissions")

    plan_id = request.data.get("plan_id")
    new_plan_id = request.data.get("new_plan_id")
    plan_product_pks = request.data.get("plan_product_ids")
    remark = request.data.get("remark") or ""
    current_user_type = get_current_user_type(request)

    validate_plan_product_pks(plan_product_pks)

    cur_distributor = current_user.distributor

    old_plan = get_plan(plan_id, cur_distributor, current_user_type)
    new_plan = get_plan(new_plan_id, cur_distributor, current_user_type)

    check_plan_editability(old_plan)
    check_plan_editability(new_plan)

    validate_plan_items(old_plan, plan_product_pks)

    set_lock(plan_id, current_user.user_id)
    set_lock(new_plan_id, current_user.user_id)

    async_move_plan_item_to_new_plan_invoker.delay(
        plan_id,
        str(old_plan),
        plan_product_pks,
        new_plan_id,
        str(new_plan),
        current_user.user_id,
        current_user_type,
        remark,
    )

    # 日志记录
    try:
        set_log_params(
            request,
            resource_id=new_plan_id,
            model=ProductSelectionPlan,
            describe=f"批量移动货盘:【{old_plan}】商品到新货盘:【{new_plan}】",
            operate_content="批量移动商品, 货盘ID: " + "、".join(plan_product_pks),
            is_success_input=True,
        )
    except Exception as e:
        logger.warning(f"set_log_params catch error: {e}")
        pass


def bulk_add_to_my_product(product_id_list, current_user):
    for product_id in product_id_list:
        try:
            product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            continue

        exist_sub_product = SubProduct.objects.filter(parent_product=product, is_deleted=False, owner=current_user.distributor).first()
        if exist_sub_product:
            # 触发信号
            exist_sub_product.save()
            continue

        post_data = product.__dict__
        post_data["parent_product"] = product.pk
        post_data["owner"] = current_user.distributor.distributor_id
        post_data["create_user"] = current_user.user_id
        ser = MyProductCreateSer(data=post_data)
        if not ser.is_valid():
            continue

        my_product = ser.save()
        # 创建关联分销商货号数据
        ProductLinkDistributor(
            product=product,
            distributor=current_user.distributor,
            code=my_product.code,
            create_user=current_user.user_id,
        ).save()

        # 判断是否有
        ProductSelectionItem.objects.filter(
            product_id=product_id,
            selection_plan__distributor=current_user.distributor,
            selection_plan__state__in=[1, 2],
        ).update(
            sub_product=my_product,
        )


def create_combine_product(post_data: dict, current_user: User, current_user_type: str) -> Product:
    # 规格数据
    skus_data = post_data.pop("skus", [])
    if not skus_data:
        raise APIViewException(err_message="缺少规格信息")

    if not isinstance(skus_data, list):
        raise APIViewException(err_message="规格信息格式错误")

    for sku_data in skus_data:
        sub_skus = sku_data.get("sub_skus")
        if not sub_skus:
            raise APIViewException(err_message="规格内商品不能为空")

        main_product_counts = 0
        for sub_sku in sub_skus:
            if sub_sku.get("is_main") is True:
                main_product_counts += 1

        if main_product_counts < 1:
            raise APIViewException(err_message="组合商品规格内必须有一个主商品")
        if main_product_counts > 1:
            raise APIViewException(err_message="组合商品规格内不能超过一个主商品")

    current_user_id = current_user.user_id
    post_data["create_user"] = current_user_id
    post_data["data_source"] = current_user_type
    post_data["is_combine"] = True
    post_data["company"] = get_combine_product_default_company().pk

    product_create_ser = CombineProductValidateSer(data=post_data)
    if not product_create_ser.is_valid():
        raise FieldsError(error_value=product_create_ser.errors)
    need_add_skus = []
    need_add_sub_skus = []

    sku_cost_price_list = []
    sku_retail_price_list = []

    with transaction.atomic():
        product_instance = Product(**product_create_ser.validated_data)
        product_instance.skip_history_when_saving = True
        product_instance.state = 1
        product_instance.save()

        spec_key = get_default_combine_product_specs_key()
        product_physical_inventory = 0
        product_specs = [
            {
                "name": spec_key.name,
                "values": [],
                "source_id": spec_key.id,
            }
        ]

        for sku_data in skus_data:
            sub_skus = sku_data.pop("sub_skus", [])

            sku_validate_ser = CombineSKUValidateSer(data=sku_data)
            if not sku_validate_ser.is_valid():
                raise FieldsError(error_value=sku_validate_ser.errors)

            spec_val = get_default_combine_product_specs_value(spec_key, sku_validate_ser.validated_data.get("name"))

            product_specs[0]["values"].append(
                {
                    "text": spec_val.value,
                    "value_id": spec_val.id,
                }
            )

            sku_specs = [
                {
                    "name": spec_key.name,
                    "value": spec_val.value,
                    "name_id": spec_key.id,
                    "value_id": spec_val.id,
                }
            ]

            new_combine_product_sku = StockKeepingUnit(
                product=product_instance,
                specs=sku_specs,
                retail_price=sku_validate_ser.validated_data.get("price"),
                image=sku_validate_ser.validated_data.get("image"),
                spec_code=generate_combine_spec_code(),
            )

            sku_cost_price = 0

            sku_physical_inventory_list = []
            for sub_sku in sub_skus:
                sub_sku_ser = CombineSubSKUValidateSer(data=sub_sku)
                if not sub_sku_ser.is_valid():
                    raise FieldsError(error_value=sub_sku_ser.errors)

                sku_id = sub_sku_ser.validated_data.get("sku_id")
                num = sub_sku_ser.validated_data.get("num")
                is_main = sub_sku_ser.validated_data.get("is_main")

                try:
                    sku = StockKeepingUnit.objects.get(sku_id=sku_id, become_history=False)
                except StockKeepingUnit.DoesNotExist:
                    raise APIViewException(err_message="SKU`{sku_id}`不存在或已成为历史,请刷新后重试")

                if sku.can_use_inventory <= 0:
                    raise APIViewException(err_message=f"规格{sku_id}可用库存小于等于0,无法添加到组合商品")

                if sku.can_use_inventory < num:
                    raise APIViewException(err_message=f"规格{sku_id}可用库存小于添加数量{num}")

                if sku.product.state != 1:
                    raise APIViewException(err_message=f"商品规格:{sku.sku_id}未上架")

                if sku.product.is_combine:
                    raise APIViewException(err_message=f"规格:{sku.sku_id}商品为组合商品,不能加到新组合商品")

                # sku现货库存
                sku_physical_inventory_list.append(sku.physical_inventory // num)

                # 组合成本价
                sku_cost_price += decimal.Decimal(sku.cost_price) * num

                need_add_sub_skus.append(
                    SubStockKeepingUnit(
                        product=product_instance,
                        parent_sku_id=new_combine_product_sku.sku_id,
                        relate_sku=sku,
                        num=num,
                        is_main=is_main,
                        create_user=current_user_id,
                    ),
                )
            # 子sku的成本价合集
            new_combine_product_sku.cost_price = sku_cost_price
            new_combine_product_sku.physical_inventory = min(sku_physical_inventory_list) if sku_physical_inventory_list else 0
            new_combine_product_sku.can_use_inventory = new_combine_product_sku.physical_inventory

            need_add_skus.append(new_combine_product_sku)

            if new_combine_product_sku.cost_price:
                sku_cost_price_list.append(new_combine_product_sku.cost_price)

            if new_combine_product_sku.retail_price:
                sku_retail_price_list.append(new_combine_product_sku.retail_price)

            #
            product_physical_inventory += new_combine_product_sku.physical_inventory

        StockKeepingUnit.objects.bulk_create(need_add_skus)
        SubStockKeepingUnit.objects.bulk_create(need_add_sub_skus)

        # 添加sku规格
        skus = StockKeepingUnit.objects.filter(sku_id__in=[i.sku_id for i in need_add_skus])
        for sku in skus:
            for specs in sku.specs:
                sku.specs_name.add(specs["name_id"])
                sku.specs_value.add(specs["value_id"])

        product_instance.min_cost_price = min(sku_cost_price_list or [0])
        product_instance.max_cost_price = max(sku_cost_price_list or [0])
        product_instance.min_retail_price = min(sku_retail_price_list or [0])
        product_instance.max_retail_price = max(sku_retail_price_list or [0])
        product_instance.spec_lists = product_specs
        product_instance.physical_inventory = product_physical_inventory
        product_instance.can_use_inventory = product_physical_inventory
        product_instance.save(
            update_fields=[
                "min_cost_price",
                "max_cost_price",
                "min_retail_price",
                "max_retail_price",
                "spec_lists",
                "physical_inventory",
                "can_use_inventory",
            ]
        )
        # 规格
        product_instance.specs.add(product_specs[0].get("source_id"))
        for v in product_specs[0]["values"]:
            product_instance.specs_value.add(v["value_id"])

        return product_instance


def update_combine_product(post_data: dict, product_obj: Product, current_user: User) -> Product:
    def _validate_sku(sku_id):
        try:
            sku = StockKeepingUnit.objects.get(sku_id=sku_id, become_history=False)
        except StockKeepingUnit.DoesNotExist:
            raise APIViewException(err_message=f"SKU `{sku_id}` 不存在或已成为历史, 请刷新后重试")

        if sku.can_use_inventory <= 0:
            raise APIViewException(err_message=f"规格 {sku_id} 可用库存小于等于 0, 无法添加到组合商品")
        if sku.product.state != 1:
            raise APIViewException(err_message=f"商品规格: {sku.sku_id} 未上架")
        if sku.product.is_combine:
            raise APIViewException(err_message=f"规格: {sku.sku_id} 商品为组合商品, 不能加到新组合商品")

        return sku

    def _create_new_combine_sku(product_instance, sku_specs, sku_validate_ser, sub_skus, current_user, exist_sub_sku_id_list, need_add_sub_skus):
        combine_sku = StockKeepingUnit(
            product=product_instance,
            specs=sku_specs,
            retail_price=sku_validate_ser.validated_data.get("price"),
            image=sku_validate_ser.validated_data.get("image"),
            spec_code=generate_combine_spec_code(),
        )
        sku_cost_price = 0
        sku_physical_inventory_list = []

        for sub_sku in sub_skus:
            sub_sku_ser = CombineSubSKUValidateSer(data=sub_sku)
            if not sub_sku_ser.is_valid():
                raise FieldsError(error_value=sub_sku_ser.errors)

            sku_id = sub_sku_ser.validated_data.get("sku_id")
            num = sub_sku_ser.validated_data.get("num")
            is_main = sub_sku_ser.validated_data.get("is_main")

            sku = _validate_sku(sku_id)
            sku_physical_inventory_list.append(sku.physical_inventory // num)
            sku_cost_price += sku.cost_price * num

            sub_sku = SubStockKeepingUnit(
                product=product_instance,
                parent_sku_id=combine_sku.sku_id,
                relate_sku=sku,
                num=num,
                is_main=is_main,
                create_user=current_user.user_id,
            )
            need_add_sub_skus.append(sub_sku)
            exist_sub_sku_id_list.append(sub_sku.sub_sku_id)

        return combine_sku, sku_cost_price, sku_physical_inventory_list, need_add_sub_skus

    def _update_existing_combine_sku(product_obj, origin_sku_id, sku_specs, sku_validate_ser, sub_skus, current_user, exist_sub_sku_id_list, need_add_sub_skus):
        try:
            combine_sku = product_obj.stockkeepingunit_set.get(sku_id=origin_sku_id, become_history=False)
        except StockKeepingUnit.DoesNotExist:
            raise APIViewException(err_message=f"SKU_ID:{origin_sku_id}不存在,请刷新页面后尝试")
        combine_sku.specs = sku_specs
        combine_sku.retail_price = sku_validate_ser.validated_data.get("price")
        combine_sku.image = sku_validate_ser.validated_data.get("image")

        sku_cost_price = 0
        sku_physical_inventory_list = []

        for sub_sku in sub_skus:
            sub_sku_ser = CombineSubSKUValidateSer(data=sub_sku)
            if not sub_sku_ser.is_valid():
                raise FieldsError(error_value=sub_sku_ser.errors)

            sku_id = sub_sku_ser.validated_data.get("sku_id")
            num = sub_sku_ser.validated_data.get("num")
            is_main = sub_sku_ser.validated_data.get("is_main")

            origin_sku = combine_sku.parent_sub_skus.filter(relate_sku_id=sku_id, become_history=False).first()
            if not origin_sku or (origin_sku and num > origin_sku.num):
                sku = _validate_sku(sku_id)
            else:
                sku = origin_sku.relate_sku
            sku_physical_inventory_list.append(sku.physical_inventory // num)
            sku_cost_price += decimal.Decimal(sku.cost_price) * num

            try:
                old_sub_sku = combine_sku.parent_sub_skus.get(relate_sku_id=sku_id)
                old_sub_sku.num = num
                old_sub_sku.is_main = is_main
                old_sub_sku.become_history = False
                old_sub_sku.save()
                exist_sub_sku_id_list.append(old_sub_sku.sub_sku_id)
            except SubStockKeepingUnit.DoesNotExist:
                sub_sku = SubStockKeepingUnit(
                    product=product_obj,
                    parent_sku_id=combine_sku.sku_id,
                    relate_sku=sku,
                    num=num,
                    is_main=is_main,
                    create_user=current_user.user_id,
                )
                need_add_sub_skus.append(sub_sku)
                exist_sub_sku_id_list.append(sub_sku.sub_sku_id)

        combine_sku.save()
        return combine_sku, sku_cost_price, sku_physical_inventory_list, need_add_sub_skus

    skus_data = post_data.pop("skus", [])
    if not skus_data:
        raise APIViewException(err_message="缺少规格信息")
    if not isinstance(skus_data, list):
        raise APIViewException(err_message="规格信息格式错误")
    for sku_data in skus_data:
        sub_skus = sku_data.get("sub_skus")
        if not sub_skus:
            raise APIViewException(err_message="规格内商品不能为空")
        # 校验是否有主商品
        main_product_counts = 0
        for sub_sku in sub_skus:
            if sub_sku.get("is_main") is True:
                main_product_counts += 1

        if main_product_counts < 1:
            raise APIViewException(err_message="组合商品规格内必须有一个主商品")
        if main_product_counts > 1:
            raise APIViewException(err_message="组合商品规格内不能超过一个主商品")

    post_data["update_user"] = current_user.user_id

    update_ser = CombineProductUpdateSer(instance=product_obj, data=post_data)
    if not update_ser.is_valid():
        raise FieldsError(update_ser.errors)

    need_add_skus = []
    need_add_sub_skus = []
    sku_cost_price_list = []
    sku_retail_price_list = []
    product_physical_inventory = 0
    exist_sku_id_list = []
    exist_sub_sku_id_list = []

    spec_key = get_default_combine_product_specs_key()
    product_specs = [
        {
            "name": spec_key.name,
            "values": [],
            "source_id": spec_key.id,
        }
    ]

    with transaction.atomic():
        product_instance = update_ser.save(no_history=True)

        for sku_data in skus_data:
            sub_skus = sku_data.pop("sub_skus", [])
            sku_validate_ser = CombineSKUValidateSer(data=sku_data)
            if not sku_validate_ser.is_valid():
                raise FieldsError(error_value=sku_validate_ser.errors)

            origin_sku_id = sku_validate_ser.validated_data.get("sku_id")
            spec_val = get_default_combine_product_specs_value(spec_key, sku_validate_ser.validated_data.get("name"))
            product_specs[0]["values"].append(
                {
                    "text": spec_val.value,
                    "value_id": spec_val.id,
                }
            )
            sku_specs = [
                {
                    "name": spec_key.name,
                    "value": spec_val.value,
                    "name_id": spec_key.id,
                    "value_id": spec_val.id,
                }
            ]

            if not origin_sku_id:
                combine_sku, sku_cost_price, sku_physical_inventory_list, need_add_sub_skus = _create_new_combine_sku(
                    product_instance,
                    sku_specs,
                    sku_validate_ser,
                    sub_skus,
                    current_user,
                    exist_sub_sku_id_list,
                    need_add_sub_skus,
                )
                need_add_skus.append(combine_sku)
            else:
                combine_sku, sku_cost_price, sku_physical_inventory_list, need_add_sub_skus = _update_existing_combine_sku(
                    product_obj,
                    origin_sku_id,
                    sku_specs,
                    sku_validate_ser,
                    sub_skus,
                    current_user,
                    exist_sub_sku_id_list,
                    need_add_sub_skus,
                )

                combine_sku.specs_name.clear()
                combine_sku.specs_value.clear()

                combine_sku.specs_name.add(sku_specs[0]["name_id"])
                combine_sku.specs_value.add(sku_specs[0]["value_id"])

            combine_sku.cost_price = sku_cost_price
            combine_sku.physical_inventory = min(sku_physical_inventory_list) if sku_physical_inventory_list else 0
            combine_sku.can_use_inventory = combine_sku.physical_inventory

            if combine_sku.cost_price:
                sku_cost_price_list.append(combine_sku.cost_price)
            if combine_sku.retail_price:
                sku_retail_price_list.append(combine_sku.retail_price)

            product_physical_inventory += combine_sku.physical_inventory
            exist_sku_id_list.append(combine_sku.sku_id)

        sku_add_rows = len(StockKeepingUnit.objects.bulk_create(need_add_skus))
        sub_sku_add_rows = len(SubStockKeepingUnit.objects.bulk_create(need_add_sub_skus))

        # 更新该组合商品的sku信息
        combine_skus = product_obj.stockkeepingunit_set.filter(become_history=False).exclude(sku_id__in=exist_sku_id_list).all()
        for combine_sku in combine_skus:
            combine_sku.become_history = True
            combine_sku.save()

        sub_skus = product_obj.substockkeepingunit_set.filter(become_history=False).exclude(sub_sku_id__in=exist_sub_sku_id_list).all()
        for sub_sku in sub_skus:
            sub_sku.become_history = True
            sub_sku.save()

        skus = StockKeepingUnit.objects.filter(sku_id__in=[i.sku_id for i in need_add_skus])
        for sku in skus:
            for specs in sku.specs:
                sku.specs_name.add(specs["name_id"])
                sku.specs_value.add(specs["value_id"])

        product_instance.min_cost_price = min(sku_cost_price_list or [0])
        product_instance.max_cost_price = max(sku_cost_price_list or [0])
        product_instance.min_retail_price = min(sku_retail_price_list or [0])
        product_instance.max_retail_price = max(sku_retail_price_list or [0])
        product_instance.spec_lists = product_specs
        product_instance.physical_inventory = product_physical_inventory
        product_instance.can_use_inventory = product_physical_inventory
        product_instance.save(
            update_fields=[
                "min_cost_price",
                "max_cost_price",
                "min_retail_price",
                "max_retail_price",
                "spec_lists",
                "physical_inventory",
                "can_use_inventory",
            ]
        )

        # 清空
        product_instance.specs.clear()
        product_instance.specs_value.clear()
        product_instance.specs.add(product_specs[0].get("source_id"))
        for v in product_specs[0]["values"]:
            product_instance.specs_value.add(v["value_id"])
    # 有新增规格，先同步到聚水潭
    if sku_add_rows > 0 or sub_sku_add_rows > 0:
        sync_product_to_JST.delay(product_instance.product_id)

    sync_to_sub_products(None, product_obj)

    # 同步聚水潭
    sync_combine_product_to_jst.delay(product_instance.product_id, product_name=product_instance.name)

    return product_instance


def v2_distributor_mode_plan_tree_list(
    request: Request,
    plan_id: int,
):
    """
    分销模式的货盘计划
    :param request:
    :param plan_id:
    :return:
    """
    params = request.query_params.copy()

    current_user = request.user

    try:
        plan = ProductSelectionPlan.objects.get(plan_id=plan_id, distributor_id=current_user.distributor.distributor_id)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")

    items = ProductSelectionItem.objects.prefetch_related(
        "product",
        "product__handcard",
        "plan_category",
        "plan_category__category",
        "plan_category__plan_company",
        "plan_category__plan_company__company",
        "product__stockkeepingunit_set",
    ).filter(is_deleted=False, selection_plan_id=plan_id)
    order_map = {
        "min_cost_price": "product__min_cost_price",
        "-min_cost_price": "-product__min_cost_price",
        "physical_inventory": "product__physical_inventory",
        "-physical_inventory": "-product__physical_inventory",
    }
    _, _, items = custom_django_filter(
        request,
        items,
        PlanV2FilterSet,
        iserializer=None,
        need_serialize=False,
        need_paginator=False,
        order_map=order_map,
    )

    re_data = {
        "plan_id": plan.plan_id,
        "name": plan.name,
        "live_date_start": plan.live_date_start,
        "live_date_end": plan.live_date_end,
        "state": plan.state,
        "plan_inventory_warning_count": 0,
        "wait_review_count": 0,
        "count": 0,
    }

    item_live_counts = (
        items.values("has_live")
        .annotate(count=Count("has_live"))
        .values(
            "has_live",
            "count",
        )
    )

    total_count = 0
    live_count = 0
    for i in item_live_counts:
        total_count += i["count"]
        if i["has_live"]:
            live_count += i["count"]
    re_data["plan_inventory_warning_count"] = 0
    re_data["wait_review_count"] = 0
    re_data["live_rate"] = 0
    if total_count:
        re_data["live_rate"] = round(live_count / total_count, 4)

    # 查询sub_product信息，分销模式显示的是FX
    current_user_type = get_current_user_type(request)
    sub_product_ids = {item.sub_product_id for item in items}
    # 移除分销市场、删除的品也显示出来
    sub_products = SubProduct.origin_objects.filter(product_id__in=sub_product_ids, owner__letters="FX")
    sub_product_map = {sub_product.parent_product_id: sub_product for sub_product in sub_products}

    # 批量查询额外查询
    products = [item.product for item in items]

    bulk_product_extra_tags = ProductBulkExtraTags(products)
    ser_data = DBDistributorModePlanListSer(
        instance=products,
        many=True,
        context={
            "sub_products_map": sub_product_map,
            "current_user_type": current_user_type,
        },
    ).data
    product_map = {d["product_id"]: d for d in ser_data}

    # 查询标签
    item_product_ids = [item.product_id for item in items]
    product_labels_dict = bulk_query_labels(item_product_ids, fronted_display=True)

    # 批量查询category
    category_ids = set()
    for p in products:
        for c in p.category:
            category_ids.add(c)

    category_list = ProductCategoryList.objects.filter(id__in=list(category_ids)).only("id", "name")
    category_map = {category.id: {"id": category.id, "name": category.name} for category in category_list}
    # 查询所有的趋势
    trend_map = batch_calc_prod_trend(products)
    # 批量查询sku信息
    product_pk_list = {product.id for product in products}
    skus = StockKeepingUnit.objects.filter(product_id__in=product_pk_list, become_history=False)

    # 批量查询history
    history_price_map = bulk_query_latest_history_price(skus)

    # 批量查询sku的规格信息
    sku_specs = bulk_query_skus_specs_detail(skus)
    sku_map = {}
    sku_id_set = set()

    for sku in skus:
        sku_id_set.add(sku.id)
        if sku.product_id not in sku_map:
            sku_map[sku.product_id] = [sku]
            continue

        sku_map[sku.product_id].append(sku)

    item_skus = ProductSelectionItemSKU.objects.filter(
        sku__in=skus,
        become_history=False,
        item__in=items,
    ).only("sku_id", "physical_inventory")
    item_skus_map = {item_sku.sku_id: item_sku.physical_inventory for item_sku in item_skus}

    # 查询托管状态
    hosting_product_map = bulk_query_product_hosting_state(item_product_ids)

    # 批量查询rank排行榜
    rank_map = bulk_query_product_category_ranks(list(product_pk_list))

    temp_data = {}

    product_cannot_view_fields, sku_cannot_view_fields = [], []
    if items:
        sku_cannot_view_fields = product_cannot_view_fields = current_user.get_user_cannot_view_fields(current_user_type, getattr(request, "client_code", "PC"))

    # 批量查询退费率
    # has_permission, refund_rate_map = bulk_query_product_refund_rate(current_user, current_user_type, item_product_ids)

    for item in items:
        _company = item.plan_category.plan_company
        _category = item.plan_category
        # 公司信息
        _plan_company_id = _company.id

        display_company_id = 8888888888
        _plan_company_id = display_company_id
        display_company_name = "珠凌甄选"

        _company_data = {
            "plan_company_id": _plan_company_id,
            "company_id": display_company_id,
            "company_name": display_company_name,
            "order": _company.order,
            "count": 0,
            "data": {},
        }
        # 分类信息
        _plan_category_id = _category.id
        _category_data = {
            "plan_category_id": _plan_category_id,
            "category_id": _category.category_id,
            "category_name": _category.category.name,
            "order": _category.order,
            "count": 0,
            "data": {},
        }

        # 商品信息
        _product = item.product
        _plan_product_id = item.id

        _product_data = product_map.get(_product.product_id)
        _category = _product_data.get("category")
        if _category:
            # 数据库保存的string类型数据，需要改成int获取map数据
            _product_data["category"] = [category_map.get(int(_category_id)) for _category_id in _category]
        # 成本价控制
        # for f in product_cannot_view_fields:
        #     if _product_data.get(f):
        #         _product_data[f] = "***"

        # 不显示退费率, 返回null适配前端
        _product_data["refund_rate"] = None
        _product_data["extra_tags"] = bulk_product_extra_tags.get(_product.id)
        _product_data["has_live"] = item.has_live
        _product_data["product_confirm_state"] = item.product_confirm_state
        _product_data["remark"] = item.remark
        _product_data["order"] = item.order
        _product_data["plan_product_id"] = item.id
        _product_data["plan_use_physical_inventory"] = item.physical_inventory

        _product_data["inventory_trend"] = trend_map.get(f"{_product.product_id}_physical_inventory", "")
        _product_data["cost_price_trend"] = trend_map.get(f"{_product.product_id}_cost_price")

        # 排行榜信息
        _product_data["rank"] = rank_map.get(_product.id)

        # 添加skus数据
        skus_qs = sku_map.get(_product.id, [])
        skus_data = DBModeRawSKUSerializer(instance=skus_qs, many=True, context={"sku_specs": sku_specs, "request": request}).data
        for sku_data in skus_data:
            obj_pk = sku_data.pop("id")
            sku_data["specs"] = sku_specs.get(obj_pk) or []
            # 成本价控制
            # for f in sku_cannot_view_fields:
            #     if sku_data.get(f):
            #         sku_data[f] = "***"

            # 分销商拼接spec_code
            if current_user_type == "DB":
                if not request.user.distributor.letters:
                    raise APIViewException(err_message="缺少分销商代码")
                sku_data["spec_code"] = sku_data["spec_code"] + "FX"

            # 获取最新历史价
            sku_data["latest_history_price"] = history_price_map.get(obj_pk, None)
            # sku占用库存
            sku_data["plan_use_physical_inventory"] = item_skus_map.get(sku_data["sku_id"]) or 0

        _product_data["skus"] = skus_data

        _product_data["hosting_state"] = hosting_product_map.get(item.product_id)
        # 添加标签
        _product_data["labels"] = product_labels_dict.get(item.product_id) or []

        if _plan_company_id not in temp_data:
            temp_data[_plan_company_id] = _company_data
            temp_data[_plan_company_id]["data"][_plan_category_id] = _category_data
            temp_data[_plan_company_id]["data"][_plan_category_id]["data"][_plan_product_id] = _product_data
        else:
            if _plan_category_id not in temp_data[_plan_company_id]["data"]:
                temp_data[_plan_company_id]["data"][_plan_category_id] = _category_data

            temp_data[_plan_company_id]["data"][_plan_category_id]["data"][_plan_product_id] = _product_data

    order_fields = []
    if params.get("orders"):
        order_fields = json.loads(params.pop("orders")[0])

    # 排序
    data_list = sorted(temp_data.values(), key=lambda x: x["order"])

    for _company_data in data_list:
        _company_data["data"] = sorted(_company_data["data"].values(), key=lambda x: x["order"])

        for category_data in _company_data["data"]:
            if order_fields:
                _field_name = order_fields[0]
                if _field_name[0] == "-":
                    order_func = lambda x: -float(x[_field_name[1:]])
                else:
                    order_func = lambda x: float(x[_field_name])
            else:
                order_func = lambda x: x["order"]

            category_data["data"] = sorted(category_data["data"].values(), key=order_func)

    # 计算count
    total_count = 0
    plan_inventory_warning_count = 0
    wait_review_count = 0

    for _company_data in data_list:
        category_datas = _company_data["data"]
        company_count = 0
        for category_data in category_datas:
            company_count += len(category_data["data"])
            category_data["count"] = len(category_data["data"])
            for prod in category_data["data"]:
                if prod["can_use_inventory"] < 0:
                    plan_inventory_warning_count += 1

                if prod["product_state"] in [4]:
                    wait_review_count += 1

        _company_data["count"] = company_count
        total_count += company_count
    re_data["count"] = total_count
    re_data["plan_inventory_warning_count"] = plan_inventory_warning_count
    re_data["wait_review_count"] = wait_review_count
    re_data["data"] = data_list
    return IResponse(data=re_data)
