# -*- coding: utf-8 -*-

from django.contrib.postgres.aggregates import StringAgg
from django.db.models import Su<PERSON>, Char<PERSON>ield, Prefetch
from django.db.models.functions import Cast
from rest_framework.request import Request

from common.basics.exceptions import APIViewException
from companies.models import Distributor
from products.filtersets import ProductDBSalesCountFilterSet
from products.models import Product, ProductDBSalesCount
from products.serializers import ProductDBSalesCountDetailSer
from users.models import User
from utils.http_handle import custom_django_filter


def get_product_db_sales_detail(
    request: Request,
    current_user: User,
    current_user_type: str,
    product_id: int,
    calc_date: str = None,
):
    try:
        if current_user_type == "OP":
            product = Product.get_operator_products(request).only("pk").get(product_id=product_id)
        else:
            product = Product.get_distributor_products(request).only("pk").get(product_id=product_id)
    except Product.DoesNotExist:
        raise APIViewException(err_message="data not found")
    condition = {"product_id": product.pk}
    if calc_date:
        distributor_info = Prefetch("distributor", Distributor.objects.only("id", "distributor_id", "name"))
        condition["calc_date"] = calc_date
        db_sales_qs = ProductDBSalesCount.objects.prefetch_related(distributor_info).filter(**condition).order_by("-calc_date")
        re_data, _, _ = custom_django_filter(
            request,
            db_sales_qs,
            iserializer=ProductDBSalesCountDetailSer,
            need_serialize=True,
            force_order=False,
        )

        return re_data

    db_sales_qs = (
        ProductDBSalesCount.objects.filter(**condition)
        .values("calc_date")
        .annotate(
            total_sales=Sum("sales"),
            total_sales_amount=Sum("sales_amount"),
            distributor_ids=StringAgg(Cast("distributor_id", output_field=CharField()), "|"),
        )
        .values(
            "calc_date",
            "total_sales",
            "total_sales_amount",
            "distributor_ids",
        )
        .order_by("-calc_date")
    )

    re_data, page_objs, _ = custom_django_filter(
        request,
        db_sales_qs,
        ProductDBSalesCountFilterSet,
        force_order=False,
        need_serialize=False,
    )

    db_id_set = {x for i in page_objs for x in i["distributor_ids"].split("|")}

    distributors = Distributor.objects.filter(pk__in=db_id_set).only("pk", "distributor_id", "name")
    distributors_map = {
        str(db.pk): {
            "distributor_id": db.distributor_id,
            "name": db.name,
        }
        for db in distributors
    }
    ret = [
        {
            "calc_date": page_obj["calc_date"],
            "total_sales": page_obj["total_sales"],
            "total_sales_amount": page_obj["total_sales_amount"],
            "distributor_ids": [distributors_map.get(db_id) for db_id in page_obj["distributor_ids"].split("|")],
        }
        for page_obj in page_objs
    ]
    re_data["data"] = ret
    return re_data
