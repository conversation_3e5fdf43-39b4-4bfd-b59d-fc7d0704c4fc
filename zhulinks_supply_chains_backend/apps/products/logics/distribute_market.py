# -*- coding: utf-8 -*-
def process_price_with_rule(price):
    # 将价格转换为浮点数
    price = float(price)

    # 获取价格的整数部分和小数部分
    integer_part = int(price)
    decimal_part = price - integer_part

    # 有小数点进1
    if 0 < decimal_part < 1:
        integer_part += 1
        decimal_part = 0.0

    # 规则a：如果个位数是1，则变为2
    if integer_part % 10 == 1:
        integer_part += 1
    # 重新组合整数部分和小数部分
    new_price = integer_part + decimal_part

    # 返回处理后的价格，保留两位小数
    return round(new_price, 2)
