from django.conf import settings
from django.utils.timezone import now
from products import logger
from products.models import Product
from users.models import UserMiniProgramSubscribe
from utils.wechat_client import generate_wechat_client


def dail_new_products_subscribe(mini_program: str):
    """
    发送每日上新商品订阅消息
    """
    # 获取订阅用户
    subscribe_users = UserMiniProgramSubscribe.objects.filter(platform=2, is_subscribe=True)
    if not subscribe_users.exists():
        logger.info("没有需要发送订阅消息的用户")
        return

    # 获取小程序的配置信息
    config = get_mini_program_config(mini_program)
    if not config:
        logger.error(f"小程序 {mini_program} 配置缺失，请检查设置")
        return

    app_id, app_secret, template_id = config

    # 初始化微信客户端
    mall_client = generate_wechat_client(app_id, app_secret)

    # 获取每日新品数量
    current_date = now().date()
    product_count = Product.objects.filter(create_date__date=current_date).count()
    data = {
        "thing1": {"value": "每日新品"},
        "time5": {"value": str(current_date)},
        "number4": {"value": product_count},
    }

    # 批量发送订阅消息
    successful_user_ids = []
    for user in subscribe_users:
        try:
            send_result = send_subscribe_message(mall_client, user, template_id, data)
            if send_result:
                successful_user_ids.append(user.id)
        except Exception as e:
            logger.error(f"用户 {user.id} 小程序订阅消息发送异常：{str(e)}")

    # 更新成功发送的用户状态
    if successful_user_ids:
        UserMiniProgramSubscribe.objects.filter(id__in=successful_user_ids).update(is_subscribe_send=True, is_subscribe=False)
        logger.info(f"成功发送订阅消息给 {len(successful_user_ids)} 个用户")


def get_mini_program_config(mini_program: str):
    """
    获取小程序配置信息
    """
    app_id = settings.MINI_PROGRAM_SHOP_APP_ID_MAP.get(mini_program)
    app_secret = settings.MINI_PROGRAM_SETTINGS_MAP.get(app_id)
    template_id = settings.MINI_PROGRAM_TEMPLATE_ID_MAP.get(mini_program)

    if not app_id or not app_secret or not template_id:
        return None
    return app_id, app_secret, template_id


def send_subscribe_message(client, user, template_id, data):
    """
    发送订阅消息
    """
    try:
        resp = client.wxa.send_subscribe_message(
            user_id=user.platform_uni_id,
            template_id=template_id,
            data=data,
            page="distributorPackage/todayNewProduct/index",
        )
        if resp.get("errcode") == 0:
            logger.info(f"成功发送订阅消息给用户 {user.id}")
            return True
        else:
            logger.error(f"用户 {user.id} 订阅消息发送失败：{resp.get('errmsg')}")
            return False
    except Exception as e:
        logger.error(f"用户 {user.id} 订阅消息发送异常：{str(e)}")
        return False
