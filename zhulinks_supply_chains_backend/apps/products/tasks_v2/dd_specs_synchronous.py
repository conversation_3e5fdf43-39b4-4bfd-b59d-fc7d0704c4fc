from products.models import SpecsKey, SpecsValue
from products.models_v2 import DouDianSK<PERSON>


def dd_specs_synchronous(data_shop_pk: int):
    if data_shop_pk:
        dd_data_all = DouDianSKU.objects.filter(shop_id=data_shop_pk).values(
            "spec_detail_name1",
            "spec_detail_name2",
            "spec_detail_name3",
        )
    else:
        dd_data_all = DouDianSKU.objects.values(
            "spec_detail_name1",
            "spec_detail_name2",
            "spec_detail_name3",
        )

    # 获取或创建规格主键
    specs_keys = {
        "spec_detail_name1": SpecsKey.objects.get_or_create(name="外部规格1", data_source="OP")[0],
        "spec_detail_name2": SpecsKey.objects.get_or_create(name="外部规格2", data_source="OP")[0],
        "spec_detail_name3": SpecsKey.objects.get_or_create(name="外部规格3", data_source="OP")[0],
    }

    # 获取所有 SpecsValue 的现有值
    existing_values = {specs_key.id: set(SpecsValue.objects.filter(spec_key_id=specs_key.id).values_list("value", flat=True)) for specs_key in specs_keys.values()}

    # 存储批量插入的 SpecsValue 对象
    new_values = []

    for dd_data in dd_data_all:
        for spec_name, specs_key in specs_keys.items():
            value = dd_data.get(spec_name)
            if value and value not in existing_values[specs_key.id]:
                # 添加新的 SpecsValue 到新值列表
                new_values.append(SpecsValue(spec_key_id=specs_key.id, value=value))
                existing_values[specs_key.id].add(value)  # 更新已存在值的集合

    # 批量插入新值
    if new_values:
        SpecsValue.objects.bulk_create(new_values)
