import time
from datetime import datetime
from django.conf import settings
from products.models import Product
from utils.feishu import FeiShuDocx
from products import logger


def feishu_daily_new_product():
    """
    飞书每日上新同步到多维表格
    """
    # 获取配置
    feishu_daily_new_product_table_id = getattr(settings, "FEISHU_DAILY_NEW_PRODUCT_TABLE_ID", None)
    feishu_daily_new_product_table_token = getattr(settings, "FEISHU_DAILY_NEW_PRODUCT_TABLE_TOKEN", None)
    if not feishu_daily_new_product_table_id or not feishu_daily_new_product_table_token:
        logger.error("飞书每日上新缺少配置信息")
        return

    # 初始化 Feishu 客户端
    feishu_client = FeiShuDocx(
        table_id=feishu_daily_new_product_table_id,
        table_app_token=feishu_daily_new_product_table_token,
    )

    # 获取当日日期与时间戳
    date = datetime.now().strftime("%Y-%m-%d")
    timestamp_ms = convert_to_timestamp(date)

    # 查询当日创建的商品
    products = Product.objects.filter(create_date__date=date)
    if not products.exists():
        logger.info("未找到当日新增商品，无需同步")
        return

    # 构造记录
    records = {"records": []}
    for product in products:
        try:
            img_url = product.main_images[0] if product.main_images else None
            file_token = feishu_client.table_update_img(img_url) if img_url else None

            record = create_product_record(product, file_token, timestamp_ms)
            records["records"].append({"fields": record})
        except Exception as e:
            logger.error(f"处理商品 {product.product_id} 时发生错误：{str(e)}")

    # 插入到飞书多维表格
    if records["records"]:
        try:
            feishu_client.table_record_insert_dict(records)
            feishu_client.send_daily_new_product()
            logger.info(f"成功同步 {len(records['records'])} 条记录到飞书多维表格")
        except Exception as e:
            logger.error(f"同步记录到飞书多维表格失败：{str(e)}")


def convert_to_timestamp(date_str: str) -> int:
    """
    将日期字符串转换为毫秒级时间戳
    """
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        return int(time.mktime(date_obj.timetuple())) * 1000
    except ValueError as e:
        logger.error(f"日期格式错误：{date_str}，错误信息：{str(e)}")
        return 0


def create_product_record(product, file_token, timestamp_ms) -> dict:
    """
    创建单个商品的记录
    """
    return {
        "商品ID": str(product.product_id),
        "图片": file_token,
        "商品名称": product.name,
        "推广价": str(product.min_cost_price) if product.min_cost_price else "",
        "建议售价": str(product.max_retail_price) if product.max_retail_price else "",
        "货号": product.code,
        "现货库存": str(product.physical_inventory),
        "7天补货库存": str(product.safety_inventory),
        "尺寸": product.size,
        "供应商": product.company.name if product.company else "未知供应商",
        "状态": product.get_state_display(),
        "日期": timestamp_ms,
    }
