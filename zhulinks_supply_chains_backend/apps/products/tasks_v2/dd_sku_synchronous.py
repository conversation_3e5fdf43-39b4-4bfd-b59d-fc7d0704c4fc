import re

from django.db import transaction

from common.decorators import timing_decorator
from companies.models import DataShop
from products.data_shops_logics import get_data_shop_access_token
from products.models_v2 import DouDianProduct, DDCustomsReportInfo, DouDianSKU
from products.tasks_v2.dd_products_synchronous import covert_cent_to_decimal
from utils.doudian.api.sku_detail.SkuDetailRequest import SkuDetailRequest


def configure_request(sku_id=None):
    sdk_request = SkuDetailRequest()
    param = sdk_request.getParams()
    param.sku_id = sku_id
    return sdk_request


def execute_product_request(shop_pk: int, sdk_request):
    accessToken, config = get_data_shop_access_token("product", shop_pk)
    sdk_request.config = config
    response = sdk_request.execute(accessToken)
    if response.code != 10000:
        print(response.__dict__)
    return response


@timing_decorator(debug=True)
def dd_sku_synchronous(data_shop_pk: int):
    try:
        shop_data = DataShop.objects.get(pk=data_shop_pk)
    except DataShop.DoesNotExist:
        raise ValueError(f"{data_shop_pk} not found.")

    batch_size = 10
    offset = 0

    while True:
        # 获取未同步商品的列表
        products = DouDianProduct.objects.filter(
            shop_id=shop_data.shop_id,
        ).order_by(
            "id"
        )[offset : offset + batch_size]
        # print(f"products:{products}")
        if not products:
            break

        to_create_sku = []
        to_create_customs = []
        products_to_update = []
        # pattern = r"^(J\d+)([A-Za-z]{1,3})$"
        # 获取sku_id列表
        existing_sku_ids = set(DouDianSKU.objects.values_list("sku_id", flat=True))

        for product in products:
            spec_prices = product.spec_prices
            if spec_prices:
                if not isinstance(spec_prices, list):
                    continue
                # 从spec_prices中获取sku_id
                for spec_price in spec_prices:
                    sku_id = spec_price.get("sku_id")
                    if not sku_id:
                        sku_id = spec_price.get("id")
                    # 如果sku_id不为空且不在已存在的sku_id列表中
                    if sku_id and sku_id not in existing_sku_ids:
                        # 创建sku请求
                        sdk_request = configure_request(sku_id=sku_id)
                        response = execute_product_request(data_shop_pk, sdk_request)
                        items = response.data
                        items["price"]= covert_cent_to_decimal(items["price"])
                        items["settlement_price"]= covert_cent_to_decimal(items["settlement_price"])
                        customs_report_info = items.pop("customs_report_info")
                        items["sku_id"] = items.pop("id")
                        items["shop_id"] = shop_data.shop_id

                        # match = re.match(pattern, items["code"])
                        #
                        # if match:
                        #     code = match.group(1)
                        #     items["code"] = code

                        # 如果items中有customs_report_info字段 且code不为空 则创建customs_report_info
                        if customs_report_info and customs_report_info.get("code") is not None:
                            customs_report_info_instance = DDCustomsReportInfo(**customs_report_info)
                            to_create_customs.append(customs_report_info_instance)
                            items["customs_report_info"] = customs_report_info_instance
                        # 创建sku
                        to_create_sku.append(DouDianSKU(**items))
                        existing_sku_ids.add(sku_id)
            # 设置商品为已同步sku
            product.is_sku_synchronous = True
            products_to_update.append(product)

        with transaction.atomic():
            if to_create_customs:
                DDCustomsReportInfo.objects.bulk_create(to_create_customs)
            if to_create_sku:
                DouDianSKU.objects.bulk_create(to_create_sku)
            DouDianProduct.objects.bulk_update(products_to_update, ["is_sku_synchronous"])
        offset += batch_size
