# -*- coding: utf-8 -*-
import datetime
import decimal
import hashlib
import json
import time
import traceback
import uuid
from decimal import ROUND_HALF_UP

from common.basics.exceptions import APIViewException
from common.basics.fields import <PERSON>SelectField
from common.basics.models import BaseModel
from common.models import CustomerService, LiveAuthor, get_product_expire_config
from common.utils import ProductExtraTags
from companies.models import Brand, Company, DataShop, Distributor
from django.contrib.postgres.fields import ArrayField
from django.contrib.postgres.indexes import GinIndex
from django.core.cache import cache
from django.db import models, transaction
from django.db.models import Case, Max, Q, QuerySet, Sum, Value, When
from django.db.models.query import RawQuerySet
from django.db.utils import IntegrityError
from django.utils import timezone
from django.utils.crypto import get_random_string
from django_cte import CTEManager
from products import logger
from products.model_managers import (
    CustomHistoricalRecords,
    ProductHistoricalManager,
    ProductHistoricalQuerySet,
    ProductSaveSkipHistoryManager,
    SKUHistoricalManager,
    SKUSaveSkipHistoryManger,
)
from users.models import User
from users.tasks import ceate_notifications
from utils.common import get_random, get_random_number_str
from utils.redis_lock import redis_cache

from .logics.distribute_market import process_price_with_rule
from .sources import PRODUCT_NO_NEED_TO_CREATE_HISTORY_FIELDS, SKU_NO_NEED_TO_CREATE_HISTORY_FIELDS
from .sql import FetchAllCategoryChildren, FetchAllCategoryParents


def default_attr_search_key():
    start = int(time.time())

    while True:
        search_key = get_random_string(length=6)
        if (int(time.time()) - start) > 120:
            raise ValueError("cannot generate unique search key in 20s.")
        if not ProductAttrList.objects.filter(search_key=search_key, is_deleted=False).exists():
            return search_key


def get_category_by_id_list(category_list: list):
    """
    获取商品分类列表名字
    """
    assert isinstance(category_list, list), ValueError("category_list must be list")
    re_data = []

    for category_id in category_list:
        cache_key = f"category_{category_id}"
        data = cache.get(cache_key)

        if data:
            re_data.append(data)
        else:
            try:
                cate_qs = ProductCategoryList.objects.get(id=category_id)
                data = {"id": cate_qs.id, "name": cate_qs.name}
                re_data.append(data)
                cache.set(cache_key, data, timeout=60 * 60)
            except ProductCategoryList.DoesNotExist:
                pass
    return re_data


def get_category_name_by_id(category_list):
    """
    获取商品分类列表名字
    """
    assert isinstance(category_list, list), ValueError("category_list must be list")
    re_data = []

    for category_id in category_list:
        cache_key = f"category_{category_id}"
        data = cache.get(cache_key)
        if data is None:
            cate_qs = ProductCategoryList.objects.filter(id=category_id).first()
            if cate_qs:
                data = {"id": cate_qs.pk, "name": cate_qs.name}
                re_data.append(data)
                cache.set(cache_key, data, timeout=60 * 60)
        else:
            re_data.append(data)
    return re_data


class ProductAttrList(models.Model):
    """
    商品属性列表，供选择
    """

    name = models.CharField("属性名称", max_length=8)

    # 弃用values，
    values = ArrayField(
        models.CharField(
            "属性值",
            max_length=16,
            blank=False,
            null=False,
        ),
        size=500,
        blank=True,
        null=True,
        verbose_name="属性值列表",
    )
    attr_type = models.CharField(
        verbose_name="属性类型",
        choices=(
            ("select", "下拉框"),
            ("input", "输入框"),
        ),
        default="select",
        max_length=12,
    )
    searchable = models.BooleanField("可搜索的", default=True)
    spec_usable = models.BooleanField("可用于规格", default=True)
    customizable = models.BooleanField("可自定义的", default=True)
    not_null = models.BooleanField("必填项", default=True)
    display_frontend = models.BooleanField("可前端显示", default=True)
    enable = models.BooleanField(verbose_name="是否启用", default=True)
    multiple = models.BooleanField(verbose_name="是否支持多选", default=False)
    # 前端搜索的字段,动态配置
    search_key = models.CharField(verbose_name="搜索字段", max_length=16, default=default_attr_search_key)
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_user = models.CharField(verbose_name="最后更新者", default="", null=True, blank=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品属性列表"
        verbose_name_plural = verbose_name
        # constraints = (
        #     models.UniqueConstraint(
        #         fields=["name", "is_deleted"],
        #         name="unique_name_is_deleted_is_false",
        #     ),
        # )

    def __str__(self):
        return self.name + f"({self.id})"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._origin_name = self.name

    @property
    def can_delete(self):
        return not self.categoryattrmembership_set.exists()


class ProductAttrValues(BaseModel):
    attr = models.ForeignKey(
        ProductAttrList,
        verbose_name="所属属性",
        db_constraint=False,
        on_delete=models.CASCADE,
    )
    name = models.CharField(verbose_name="值名称", max_length=32, default="")
    order = models.PositiveSmallIntegerField("排序", help_text="数字越大排越后", default=0)

    class Meta:
        verbose_name = "属性值映射表"
        verbose_name_plural = verbose_name
        ordering = ("order",)

        constraints = (
            models.UniqueConstraint(
                fields=(
                    "attr_id",
                    "name",
                    "is_deleted",
                ),
                name="unique_attr_name_is_not_deleted",
                condition=models.Q(is_deleted=False),
            ),
        )

    def __str__(self):
        return f"{self.name}({self.pk})"


class CategoryManager(models.Manager):
    def fetch_all_children(self, id_val: int, include_self: bool = True) -> RawQuerySet:
        """
        查询所有子级数据,
        :param id_val: primary_key id值
        :param include_self: 是否包含自身数据
        :return:RawQuerySet
        需要转换成QuerySet,可以先查询出id值，然后QuerySet.filter
        """
        params = [id_val]
        raw_sql = FetchAllCategoryChildren
        if not include_self:
            raw_sql += " WHERE NOT id = %s"
            params.append(id_val)

        return self.raw(raw_sql, [id_val])

    def fetch_all_parents(self, id_val: int, include_self: bool = True) -> RawQuerySet:
        """
        查询所有父级数据,
        :param id_val: primary_key id值
        :param include_self: 是否包含自身数据
        :return:RawQuerySet
        需要转换成QuerySet,可以先查询出id值，然后QuerySet.filter
        """
        params = [id_val]
        raw_sql = FetchAllCategoryParents
        if not include_self:
            raw_sql += " WHERE NOT id = %s"
            params.append(id_val)

        return self.raw(raw_sql, params)


class ProductCategoryList(models.Model):
    """
    商品类别, 三级分类
    """

    id = models.BigAutoField("类别id", primary_key=True)
    name = models.CharField("类别名称", max_length=28)
    parent = models.ForeignKey("self", on_delete=models.CASCADE, related_name="subs", null=True, blank=True, verbose_name="父级类别")
    level = models.PositiveSmallIntegerField(verbose_name="层级", default=1)
    attrs = models.ManyToManyField(ProductAttrList, through="CategoryAttrMembership")
    JST_id = models.CharField("聚水潭id", max_length=16, null=True, blank=True)
    order = models.PositiveSmallIntegerField(verbose_name="排序", help_text="数字越大排越前", default=0)
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_user = models.CharField(verbose_name="最后更新人", default="", null=True, blank=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    is_distributor_display = models.BooleanField(verbose_name="分销模式是否显示", default=False)
    objects = CategoryManager()

    class Meta:
        verbose_name = "商品类别列表"
        verbose_name_plural = verbose_name
        ordering = (
            "-order",
            "id",
        )

    def __str__(self):
        return f"{self.name}({self.id})"


def is_complete_category(c_id_list: list):
    """
    判断类别列表是否为一个完整的链路
    :param c_id_list:
    :return:
    """
    all_numbers = all(isinstance(e, (int, str)) for e in c_id_list)
    if not all_numbers:
        raise APIViewException(err_message="分类列表id必须为整数类型")
    categories = ProductCategoryList.objects.filter(id__in=c_id_list, is_deleted=False).values("id", "parent_id")
    if not categories:
        raise APIViewException(err_message="错误分类ID")

    def build_hierarchy(_categories):
        # 构建id到parent_id的映射
        id_to_parent = {cat["id"]: cat["parent_id"] for cat in _categories}
        return id_to_parent

    def is_complete_chain(id_to_parent, id_list):
        for i in range(len(id_list) - 1):
            # 当前ID的下一个ID是否是其子节点
            if id_to_parent.get(id_list[i + 1]) != id_list[i]:
                return False
        return True

    id_to_parent_list = build_hierarchy(categories)
    return is_complete_chain(id_to_parent_list, c_id_list)


class CategoryAttrMembership(models.Model):
    """
    商品分类与属性关系
    """

    category = models.ForeignKey(ProductCategoryList, on_delete=models.CASCADE)
    attr = models.ForeignKey(ProductAttrList, on_delete=models.CASCADE)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品类别与属性绑定关系"
        verbose_name_plural = verbose_name


class ProductUnit(models.Model):
    """
    商品单位
    """

    name = models.CharField("单位名", max_length=8, blank=False, null=False)

    class Meta:
        verbose_name = "商品单位"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


class ProductAddress(models.Model):
    name = models.CharField("地区名称", max_length=200, null=False, blank=False, default="", unique=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "所属地区"
        verbose_name_plural = verbose_name

    def __str__(self) -> str:
        return self.name


class SpecsKey(BaseModel):
    """
    商品规格表
    """

    name = models.CharField(verbose_name="规格名描述", max_length=128, default="", null=True, blank=True)
    # 是否在下拉列表显示, 关联着attr_list的spec_usable
    display = models.BooleanField("是否显示", default=True)
    # 数据来源
    data_source = models.CharField(
        verbose_name="数据来源",
        choices=(
            ("OP", "运营商"),
            ("BASIC", "基础"),
            ("DB", "分销商"),
            ("SP", "供应商"),
            ("CB", "组合商品"),
        ),
        max_length=6,
        default="BASIC",
    )
    # 用于同步属性数据。有attr_id默认是基础属性
    attr = models.ForeignKey(
        ProductAttrList,
        on_delete=models.SET_NULL,
        db_constraint=False,
        verbose_name="关联属性名",
        null=True,
        blank=True,
    )
    distributor = models.ForeignKey(
        Distributor,
        on_delete=models.CASCADE,
        to_field="distributor_id",
        verbose_name="所属分销商",
        db_constraint=False,
        null=True,
        blank=True,
    )
    supplier = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        to_field="company_id",
        verbose_name="所属供应商",
        db_constraint=False,
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = "规格名管理"
        verbose_name_plural = verbose_name
        constraints = [
            # models.UniqueConstraint(
            #     fields=("name",),
            #     condition=models.Q(
            #         distributor__isnull=True,
            #         supplier__isnull=True,
            #         data_source="BASIC",
            #     ),
            #     name="unique_name_with_basic",
            # ),
            models.UniqueConstraint(
                fields=(
                    "name",
                    "distributor",
                ),
                name="unique_name_with_db_owner",
            ),
            models.UniqueConstraint(
                fields=(
                    "name",
                    "supplier",
                ),
                name="unique_name_with_supplier_owner",
            ),
        ]

    def __str__(self):
        return f"{self.name}({self.pk})"


def get_default_combine_product_specs_key() -> SpecsKey:
    obj, _ = SpecsKey.objects.get_or_create(
        name="规格",
        data_source="CB",
        defaults={"display": False},
    )
    return obj


class SpecsValue(BaseModel):
    """
    商品规格值表
    """

    spec_key = models.ForeignKey(
        SpecsKey,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="所属规格名",
        null=True,
        blank=True,
    )
    value = models.CharField(verbose_name="规格值描述", max_length=128)
    # 是否在下拉列表显示, 关联着attr_list的spec_usable
    display = models.BooleanField("是否显示", default=True)

    attr_value = models.ForeignKey(
        ProductAttrValues,
        on_delete=models.SET_NULL,
        db_constraint=False,
        verbose_name="关联属性值",
        null=True,
        blank=True,
    )

    # 数据来源
    data_source = models.CharField(
        verbose_name="数据来源",
        choices=(
            ("OP", "运营商"),
            ("BASIC", "基础"),
            ("DB", "分销商"),
            ("SP", "供应商"),
            ("CB", "组合商品"),
        ),
        max_length=6,
        default="BASIC",
    )
    distributor = models.ForeignKey(
        Distributor,
        on_delete=models.CASCADE,
        to_field="distributor_id",
        verbose_name="所属分销商",
        db_constraint=False,
        null=True,
        blank=True,
    )
    supplier = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        to_field="company_id",
        verbose_name="所属供应商",
        db_constraint=False,
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = "规格值管理"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.value + f"({self.id})"


def get_default_combine_product_specs_value(key: SpecsKey, value_text: str) -> SpecsValue:
    spec_value, _ = SpecsValue.objects.get_or_create(
        spec_key=key,
        value=value_text,
        data_source="CB",
    )
    return spec_value


class ProductLabelTypes(models.Model):
    name = models.CharField(verbose_name="标签类型", max_length=12, unique=True)
    order = models.PositiveSmallIntegerField(verbose_name="排序,数字越小排越前", default=0)

    class Meta:
        verbose_name = "标签类型管理"
        verbose_name_plural = verbose_name
        ordering = ("order",)

    def __str__(self):
        return f"{self.name}({self.id})"


class ProductLabels(BaseModel):
    name = models.CharField(verbose_name="标签名称", max_length=32)
    l_type = models.ForeignKey(ProductLabelTypes, on_delete=models.CASCADE, db_constraint=False, verbose_name="标签类型", null=True, blank=True)
    order = models.PositiveSmallIntegerField(verbose_name="排序,数字越大排越前", default=0)
    # 全部商品隐藏
    display = models.BooleanField(verbose_name="是否显示", default=True)
    # 后台自定义的不允许修改，列表返回为True的数据
    can_edit = models.BooleanField(verbose_name="是否能修改", default=True)
    # 后端打的标签为特殊标签, is_normal为False, can_edit=False. 特殊标签不允许修改
    is_normal = models.BooleanField(verbose_name="是否为普通标签", default=True)
    # 区分分销商标签，便于后续更新
    distributor_letters = models.CharField("分销商代号字母", max_length=12, blank=True, null=True, db_index=True)
    # 显示补充，例如: name=女性, display_extra_text=占比, product_label_relate=40%， 则标签名称应该为：女性占比40%
    display_extra_text = models.CharField(verbose_name="显示补充", max_length=16, help_text="标签名称+该补充+绑定到商品的值", null=True, blank=True, default="")
    parent = models.ForeignKey("self", on_delete=models.CASCADE, db_constraint=False, null=True, blank=True, verbose_name="父标签")
    # 前端是否显示
    fronted_display = models.BooleanField("前端是否显示", default=True)

    class Meta:
        verbose_name = "标签管理"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.name}({self.pk})"

    @property
    def link_product_count(self):
        """
        关联商品数量
        :return:
        """
        return self.productlabelsrelate_set.filter(product__is_deleted=False, become_history=False).count()


class ProductLabelsRule(BaseModel):
    label = models.OneToOneField(ProductLabels, on_delete=models.CASCADE, verbose_name="关联的标签", db_constraint=False)
    value = models.CharField(verbose_name="阈值", max_length=12, null=True, blank=True, default="")
    rule = models.JSONField(verbose_name="规则", null=True, blank=True)
    remark = models.TextField(verbose_name="备注", null=True, blank=True)

    class Meta:
        verbose_name = "标签规则"
        verbose_name_plural = verbose_name


class ProductHistoricalWithExtraFields(models.Model):
    """
    商品版本记录额外数据
    """

    category_data = models.JSONField("分类数据", null=True, blank=True, default=dict)
    company_data = models.JSONField("公司数据", null=True, blank=True, default=dict)
    attr_options_data = models.JSONField("商品参数数据", null=True, blank=True, default=list)
    labels_data = models.JSONField("标签数据", null=True, blank=True, default=dict)
    gifts_data = models.JSONField("赠品数据", null=True, blank=True, default=dict)

    class Meta:
        abstract = True


class Product(models.Model):
    """
    商品 SPU
    """

    PRODUCT_TYPE_CHOICES = (
        ("FN", "成品"),
        ("SP", "样品"),
    )
    CROSS_BORDER_TYPE_CHOICES = (
        ("CN", "中国大陆地区"),
        ("HK", "中国香港"),
    )
    PRODUCT_STATE_CHOICES = (
        (0, "待审核"),
        (1, "上架"),
        (2, "审核不通过"),
        (3, "下架"),
        (4, "待核价"),
        (5, "待质检"),
    )

    DATA_SOURCE_CHOICES = (
        ("SP", "供应商录入"),
        ("OP", "运营商录入"),
        ("OP_JST", "运营商-聚水潭录入"),
        ("OP_KP", "运营商-快批录入"),
        ("DB", "分销商录入"),
        ("DB_MINI", "分销商-小程序录入"),
    )

    HANDLE_TYPE_CHOICES = (
        ("M", "主商品"),
        ("S", "子商品"),
    )

    GIFT_TYPE_CHOICES = (
        ("AC", "配件"),
        ("PO", "商品"),
    )

    origin_product = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        to_field="product_id",
        blank=True,
        null=True,
        verbose_name="原商品链接",
        default=None,
    )
    product_id = models.BigIntegerField("商品id", unique=True, blank=False, null=False, default=get_random)
    product_type = models.CharField("商品类型", choices=PRODUCT_TYPE_CHOICES, max_length=2, blank=True, null=True, default="FN")
    is_centralized_purchasing = models.BooleanField("是否集采", default=False)
    name = models.CharField("商品名称", max_length=80, blank=False, null=False)
    category = ArrayField(models.CharField("商品分类id", max_length=32, blank=True, null=True), size=3, blank=False, null=False, verbose_name="分类列表")
    code = models.CharField("货号", max_length=20, blank=True, null=True)
    size = models.CharField("尺寸", max_length=56, blank=True, null=True, default=None)
    brand = models.ForeignKey(Brand, on_delete=models.SET_NULL, blank=True, null=True, verbose_name="品牌")
    # 组合商品没有供应商
    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name="供应商")
    sales = models.BigIntegerField("商品销量", default=0)
    # 运营商 -> 所有编辑不会修改状态
    # 分销商 -> 所有编辑不会修改状态
    # 供应商 -> 修改成本价、建议售价, 重置state为待审核
    state = models.SmallIntegerField("商品状态", choices=PRODUCT_STATE_CHOICES, default=0)
    state_reason = models.JSONField("状态描述", blank=True, null=True)
    # 上下架理由
    listing_delisting_reason = models.TextField("上下架理由", blank=True, null=True, default="")
    # 上下架备注
    state_remark = models.TextField("状态备注", blank=True, null=True, default="")
    is_deleted = models.BooleanField("是否删除", default=False, db_index=True)
    cross_border_attr = models.CharField("跨境属性", choices=CROSS_BORDER_TYPE_CHOICES, max_length=2, default="CN")
    unit = models.ForeignKey(ProductUnit, on_delete=models.SET_NULL, blank=True, null=True, verbose_name="商品单位")
    # tags = ArrayField(models.CharField("商品标签", max_length=20, blank=True, null=True), size=20, blank=True, null=True, verbose_name="标签列表")
    tags = ArrayField(models.CharField("商品标签", max_length=500, blank=True, null=True), size=20, blank=True, null=True, verbose_name="标签列表")
    remark = models.TextField("商品备注", blank=True, null=True)
    seller_remark = models.TextField("商家备注", blank=True, null=True)
    main_images = ArrayField(models.CharField("商品主图", max_length=300, default=None), size=15, blank=False, null=False, verbose_name="主图列表")
    spec_lists = ArrayField(models.JSONField("商品规格对象", default=dict), size=3, blank=True, null=True, default=list, verbose_name="商品规格列表")
    main_video = models.CharField("商品主视频", max_length=300, blank=True, null=True)
    detail_images_backup = ArrayField(models.CharField("图文详情备份", max_length=300, default=None), size=20, blank=True, null=True, default=list, verbose_name="图文详情列表")
    detail_images = models.TextField("图文详情", blank=True, null=True)
    physical_inventory = models.BigIntegerField("现货库存", default=0, blank=True, null=True)
    safety_inventory = models.BigIntegerField("7天补货库存", default=0, blank=True, null=True)
    # 可用库存
    plan_use_inventory = models.BigIntegerField(verbose_name="货盘占用库存", default=0, blank=True, null=True)
    can_use_inventory = models.BigIntegerField(verbose_name="可用库存", default=0, blank=True, null=True)

    # merchant_inventory = models.BigIntegerField("商家库存", default=0, blank=True, null=True)
    has_link_code = models.BooleanField("已设置链接编码", default=False)
    min_cost_price = models.DecimalField("最低成本价", max_digits=10, decimal_places=2, blank=True, null=True)
    max_cost_price = models.DecimalField("最高成本价", max_digits=10, decimal_places=2, blank=True, null=True)
    min_retail_price = models.DecimalField("最低建议售价", max_digits=10, decimal_places=2, blank=True, null=True)
    max_retail_price = models.DecimalField("最高建议售价", max_digits=10, decimal_places=2, blank=True, null=True)
    # 分销商价格
    min_distributor_market_price = models.DecimalField("最低分销商市场价格", max_digits=10, decimal_places=2, blank=True, null=True)
    max_distributor_market_price = models.DecimalField("最低分销商市场价格", max_digits=10, decimal_places=2, blank=True, null=True)

    # V2.9.7版本新增分销推广价
    min_distributor_promotion_price = models.DecimalField("最低分销推广价", max_digits=10, decimal_places=2, blank=True, null=True)
    max_distributor_promotion_price = models.DecimalField("最低分销推广价", max_digits=10, decimal_places=2, blank=True, null=True)

    min_history_price = models.DecimalField("最低历史价", max_digits=10, decimal_places=2, blank=True, null=True)
    max_history_price = models.DecimalField("最高历史价", max_digits=10, decimal_places=2, blank=True, null=True)
    inventory_warning = models.BooleanField("库存告警", default=False)
    data_source = models.CharField("数据来源", choices=DATA_SOURCE_CHOICES, max_length=10, default="SP")
    has_DouDian_link = models.BooleanField("完成抖店链接", default=False)
    confirm_DouDian_link_user = models.CharField("确认完成抖店链接者", max_length=32, blank=True, null=True)
    live_date = models.DateTimeField("直播日期", blank=True, null=True)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    # 2023.12.05 brand字段改为address 去除 brand
    address = models.ForeignKey(ProductAddress, verbose_name="商品地区", on_delete=models.SET_NULL, db_constraint=False, null=True, blank=True)
    owner = models.ForeignKey(Distributor, verbose_name="商品所属分销商", to_field="distributor_id", on_delete=models.CASCADE, db_constraint=False, null=True, blank=True)

    doudian_id = models.CharField("抖店id", max_length=128, null=True, blank=True, default="")
    has_live = models.BooleanField(verbose_name="是否已播", default=False)
    cert_images = ArrayField(
        base_field=models.CharField(verbose_name="图片地址", max_length=300, default=None),
        size=5,
        null=True,
        blank=True,
        default=list,
        verbose_name="商品资质证书地址列表",
    )
    price_review_date = models.DateTimeField(verbose_name="最新价格审核时间", null=True, blank=True)
    # 2.28 从array_field修改为many_to_many
    specs = models.ManyToManyField(SpecsKey, verbose_name="规格名")
    specs_value = models.ManyToManyField(SpecsValue, verbose_name="规格值")
    # 添加新品标签
    is_new = models.BooleanField(verbose_name="是否为新品", default=False)
    expire_date = models.DateTimeField(verbose_name="新品过期时间", null=True, blank=True)
    # objects = ProductManager()
    handle_type = models.CharField("处理类型", choices=HANDLE_TYPE_CHOICES, max_length=1, null=True, blank=True)
    has_new_code = models.BooleanField(verbose_name="是否有新式编码", default=False)
    # 商品额外标签，例如: 爆款
    labels_relate = models.ManyToManyField(ProductLabels, through="ProductLabelsRelate", verbose_name="额外标签", blank=True)
    gift_type = models.CharField("赠品类型", choices=GIFT_TYPE_CHOICES, max_length=2, null=True, blank=True)
    has_gift = models.BooleanField("是否有赠品", default=False)
    is_combine = models.BooleanField("是否为组合商品", default=False)
    # 视频切片滑动
    slicing_url = models.CharField(verbose_name="切片url，需要组装num", default="", null=True, blank=True)
    slicing_num = models.IntegerField(verbose_name="切片数量", default=0, null=True, blank=True)
    video_3D = models.CharField(verbose_name="3e切片原视频", default="", null=True, blank=True)
    # 主图相似度
    pic_score = models.CharField("主图相似度", null=True, blank=True, max_length=16, default="0.0")
    # 是否在分销商市场
    is_in_distributor_market = models.BooleanField("是否在分销商市场", default=False)

    # 版本记录
    history = CustomHistoricalRecords(bases=[ProductHistoricalWithExtraFields])
    objects = ProductHistoricalManager()  # 使用自定义的 Manager
    skip_history_price_objects = ProductSaveSkipHistoryManager()  # 不保存历史记录 Manager

    class Meta:
        verbose_name = "商品"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(
                name="company_index",
                fields=["company"],
                include=["is_deleted"],
            ),
            models.Index(
                name="code_index",
                fields=["code"],
            ),
            models.Index(
                name="remark_index",
                fields=["remark"],
            ),
        ]
        permissions = (
            ("view_min_cost_price_Product", "查看商品最小推广价"),
            ("change_min_cost_price_Product", "修改商品最小推广价"),
            ("view_max_cost_price_Product", "查看商品最大推广价"),
            ("change_max_cost_price_Product", "修改商品最大推广价"),
            ("show_max_cost_price_Product", "最大推广价、供应商不显示"),
            ("show_min_cost_price_Product", "最小推广价、供应商不显示"),
        )

    def __str__(self):
        return f"{self.name}({self.product_id})"

    def generate_code(self):
        unique = False
        while not unique:
            code = get_random_number_str(6)
            unique = not self._meta.model.objects.filter(code=code).exists()
        return code

    def save(self, need_update_date=True, *args, **kwargs):
        try:
            if len(self.category) < 3:
                raise APIViewException(err_message="系统暂不支持少于三级分类的商品创建")

            with transaction.atomic():
                update_fields = kwargs.get("update_fields", [])
                need_to_create_history = True
                # 单独或仅修改过期时间和is_new状态，不创建版本
                if update_fields:
                    if len(set(update_fields) - PRODUCT_NO_NEED_TO_CREATE_HISTORY_FIELDS) == 0:
                        need_to_create_history = False

                # 原逻辑追加 update_date
                # 不需要update_date, 设置need_update_date=False
                if need_update_date and kwargs.get("update_fields") and "update_date" not in kwargs["update_fields"]:
                    self.update_date = timezone.now()
                    list(kwargs["update_fields"]).append("update_date")

                if self.pk:
                    # 修改,不允许修改
                    # 暂时开放修改
                    if not self.code:
                        original = self._meta.model.objects.get(pk=self.pk)
                        self.code = original.code
                else:
                    # 新增
                    # 暂时开放传入
                    if not self.code:
                        self.code = self.generate_code()
                try:
                    # 新创建的数据不保存product变动
                    # 1. __update_user__由两个地方传递, orders.tasks和 sku save serializer
                    # 2. 从sku post save 信号传过来的__update_sku_id__才进行记录,不记录product自身的修改
                    if getattr(self, "__update_user__", None) and getattr(self, "__update_sku_id__", None):
                        old_prod = self._meta.model.objects.get(pk=self.pk, is_deleted=False)
                        if old_prod.physical_inventory != self.physical_inventory:
                            ProductModHistory(
                                product_id=self.product_id,
                                sku_id=getattr(self, "__update_sku_id__", None),
                                mod_field_name="physical_inventory",
                                old_value=old_prod.physical_inventory,
                                new_value=self.physical_inventory,
                                create_user=getattr(self, "__update_user__", self.update_user),
                            ).save()

                        if old_prod.safety_inventory != self.safety_inventory:
                            ProductModHistory(
                                product_id=self.product_id,
                                sku_id=getattr(self, "__update_sku_id__", None),
                                mod_field_name="safety_inventory",
                                old_value=old_prod.safety_inventory,
                                new_value=self.safety_inventory,
                                create_user=getattr(self, "__update_user__", self.update_user),
                            ).save()
                        if old_prod.sales != self.sales:
                            ProductModHistory(
                                product_id=self.product_id,
                                sku_id=getattr(self, "__update_sku_id__", None),
                                mod_field_name="sales",
                                old_value=old_prod.sales,
                                new_value=self.sales,
                                create_user=getattr(self, "__update_user__", self.update_user),
                            ).save()

                except Exception as e:
                    logger.warning(f"保存mod history 记录失败:{e},{traceback.format_exc()}")
                    pass
                try:
                    # 判断新品标签
                    if not self.pk:
                        # 如果是新品
                        self.is_new = True
                        start_date = self.create_date or datetime.datetime.now()
                        self.expire_date = timezone.make_aware(start_date + datetime.timedelta(days=int(get_product_expire_config().value)))
                    else:
                        if self.create_date and not self.expire_date:
                            self.expire_date = timezone.make_aware(self.create_date + datetime.timedelta(days=int(get_product_expire_config().value)))
                            if self.expire_date > self.create_date:
                                self.is_new = True
                except Exception as e:
                    logger.warning(f"product is_new save fetch error:{e}.{traceback.format_exc()}")
                if not need_to_create_history:
                    setattr(self, "skip_history_when_saving", True)
                super().save(*args, **kwargs)
            #######
            # 逻辑移动到products.signals [product_pre_save_handler/product_post_save_handler],防止循环引用
            #######
            # 如果公司或分类修改，需要更新货盘计划的公司分类
            # if not self._state.adding and (self.company.id != self.__original_company_id or str(self.category[1]) != str(self.__original_category[1]) or self.is_deleted):
            #     update_company_category.delay(self.product_id, self.is_deleted)
            # 更新存储的初始状态
            # self.__original_company_id = self.company.id
            # self.__original_category = self.category

        except IntegrityError as e:
            # todo: 如果版本变化，异常信息可能改变，后期考虑优化
            if e.args[0] and e.args[0].startswith('duplicate key value violates unique constraint "products_product_product_id'):
                self.product_id = get_random()
                self.save(*args, **kwargs)
            else:
                raise e
        except Exception as e:
            logger.error(f"{str(e)}")
            raise e

    def get_or_create_latest_history(self):
        latest_history = self.history.first()
        if latest_history:
            return latest_history
        else:
            self.save()
            return self.history.first()

    @property
    def product_confirm_state(self):
        # 商品确认状态
        # 货盘完成后商品可以编辑现货库存/7天库存
        if ProductConfirmStateModelView.objects.filter(product_id=self.product_id).exists():
            return 1
        return 0

        if self.productselectionitem_set.filter(
            selection_plan__state__in=[1, 2],
            product_confirm_state=1,
            is_deleted=False,
        ).exists():
            return 1
        return 0

    @property
    def hosting_state(self):
        hosting_product = self.hostingproducts_set.filter(
            state=1,
            is_deleted=False,
        ).last()
        if not hosting_product:
            return None
        return hosting_product.state

    def get_category_data(self):
        """商品分类ID列表转成带name的列表"""
        return get_category_name_by_id(self.category)

    @property
    def can_edit(self):
        """
        是否能修改
        :return:
        """
        return self.state in (1, 2, 3)

    @property
    def extra_tags(self):
        """
        统一的额外标签
        :return:
        """
        return ProductExtraTags(self).to_dict()

    @property
    def labels_list_info(self) -> list:
        # 获取labels信息，按照中间件的添加顺序，倒序排序
        # 列表数据，倒序
        label_relates = ProductLabelsRelate.objects.select_related("label").filter(product=self, become_history=False)
        return [
            {
                "id": label_relate.label_id,
                "name": label_relate.show_name,
                "can_edit": label_relate.label.can_edit,
            }
            for label_relate in label_relates
        ]

    @property
    def labels_detail_info(self) -> dict:
        # 处理成obj, 分成两部分
        # 系统不能编辑，普通标签能编辑

        label_relates = ProductLabelsRelate.objects.select_related("label").filter(product=self, become_history=False)
        re_data = {
            "normal": [],
            "system": [],
        }
        for label_relate in label_relates:
            if label_relate.label.is_normal:
                re_data["normal"].append(
                    {
                        "id": label_relate.label_id,
                        "name": label_relate.show_name,
                        "can_edit": label_relate.label.can_edit,
                    }
                )
            else:
                re_data["system"].append(
                    {
                        "id": label_relate.label_id,
                        "name": label_relate.show_name,
                        "can_edit": label_relate.label.can_edit,
                        "act_value": label_relate.act_value,
                        "distributor_letters": label_relate.distributor_letters,
                    }
                )

        re_data["system"] = re_data["system"]

        return re_data

    @property
    def state_text(self):
        return self.get_state_display()

    @property
    def unit_text(self):
        return self.unit.name

    @property
    def rank(self):
        rank_history = (
            ProductCategorySalesRankHistory.objects.filter(
                calc_date=datetime.datetime.now(),
                product_id=self.pk,
                rank__lte=20,
            )
            .only(
                "product_id",
                "category_id",
                "rank",
            )
            .first()
        )
        if not rank_history:
            return None

        category_id = rank_history.category_id
        category = ProductCategoryList.objects.filter(id=category_id).first()

        return {
            "category_id": category_id,
            "category_name": category.name if category else "",
            "rank": rank_history.rank,
        }

    @classmethod
    def get_distributor_products(cls, request, need_annotate_warehouse_inventory=False) -> ProductHistoricalQuerySet:
        """
        特定分销商只能看到与其关联的供应商的商品
        :param request:
        :param need_annotate_warehouse_inventory:
        :return:
        """
        from products.models_v2.product_config import ProductConfig

        distributor_id = request.user.distributor.distributor_id
        # 获取不可见的公司 ID 列表
        invisible_company_ids = list(Company.objects.exclude(visible_distributors=[]).exclude(visible_distributors__contains=distributor_id).values_list("pk", flat=True))

        cannot_see_product_id_list = list(
            ProductConfig.objects.exclude(
                visible_distributor=[],
            )
            .exclude(visible_distributor__contains=distributor_id)
            .values_list("product_id", flat=True)
        )

        # 构建查询
        query = Product.objects.exclude(product_id__in=cannot_see_product_id_list).exclude(company_id__in=invisible_company_ids).exclude(is_deleted=True)

        # 如果需要注解仓库库存
        if need_annotate_warehouse_inventory:
            query = query.annotate(
                warehouse_inventory=Sum(
                    "stockkeepingunit__warehouse_inventory",
                    filter=Q(stockkeepingunit__become_history=False),
                )
            )

        return query

    @classmethod
    def get_operator_products(cls, request, is_deleted: bool | None = False, need_annotate_warehouse_inventory=False) -> ProductHistoricalQuerySet:
        """
        指定用户只能看到哪些供应商的商品
        :param need_annotate_warehouse_inventory: 是否需要显示仓库库存
        :param is_deleted:
        :param request:
        :return:
        """
        visible_supplier = request.user.visible_supplier
        query = Product.objects.all()

        if visible_supplier:
            query = query.filter(company__company_id__in=visible_supplier)

        if is_deleted is not None:
            query = query.filter(is_deleted=is_deleted)

        if need_annotate_warehouse_inventory:
            query = query.annotate(
                warehouse_inventory=Sum(
                    "stockkeepingunit__warehouse_inventory",
                    filter=Q(stockkeepingunit__become_history=False),
                )
            )

        return query

    @property
    def state_desc(self):
        product_state = self.state
        if product_state == 4:
            latest_record = self.skucostpricereviewrecord_set.only("state").last()
            if latest_record and latest_record.state == 0:
                return "改价中"
        elif product_state == 2:
            last_review = self.productreview_set.filter(process__become_history=False).last()
            if last_review:
                if not last_review.verified_pass:
                    if last_review.process_level == "BASIC_REVIEW":
                        return "基础审核不通过"
                    elif last_review.process_level == "PRICE_REVIEW":
                        return "核价不通过"
                    elif last_review.process_level == "QA_REVIEW":
                        return "质检不通过"
                else:
                    latest_record = self.skucostpricereviewrecord_set.only("state").last()
                    if latest_record and latest_record.state == 2:
                        return "改价不通过"
        return self.get_state_display()

    @property
    def warehouse_inventory(self):
        # 如果查询时已经 annotate，直接返回
        if hasattr(self, "_warehouse_inventory"):
            return self._warehouse_inventory
        # 否则重新计算
        return self.stockkeepingunit_set.filter(become_history=False).aggregate(Sum("warehouse_inventory"))["warehouse_inventory__sum"]

    @warehouse_inventory.setter
    def warehouse_inventory(self, value):
        # 用于存储 annotate 的结果
        self._warehouse_inventory = value

    @property
    def distribute_show_can_use_inventory(self):
        """
        分销模式显示的可用库存, 方便统一修改
        :return:
        """
        return self.can_use_inventory


class ProductLabelsRelate(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, db_constraint=False, to_field="product_id", verbose_name="商品")
    label = models.ForeignKey(ProductLabels, on_delete=models.CASCADE, db_constraint=False, verbose_name="标签")
    act_value = models.CharField(verbose_name="实际的值", max_length=64, null=True, blank=True)
    value = models.CharField(verbose_name="显示值描述", max_length=64, null=True, blank=True)
    become_history = models.BooleanField(verbose_name="是否成为历史", default=False)
    remark = models.TextField(verbose_name="备注信息", null=True, blank=True)
    label_date = models.DateTimeField(verbose_name="标签时间", null=True, blank=True)
    label_week = models.PositiveSmallIntegerField(verbose_name="本年第几周", null=True, blank=True)
    distributor_letters = models.CharField("分销商代号字母", max_length=12, blank=True, null=True, db_index=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True, blank=True, null=True)
    update_date = models.DateTimeField("更新时间", auto_now=True, blank=True, null=True)

    class Meta:
        verbose_name = "商品标签关联关系"
        verbose_name_plural = verbose_name
        ordering = ("label__is_normal", "-id")

    @property
    def show_name(self):
        return f"{self.label.name}{self.label.display_extra_text or ''}{self.value or ''}"


class SKUHistoricalWithExtraFields(models.Model):
    """
    SKU版本记录额外数据
    """

    # product_version = models.BigIntegerField("对应商品版本号", null=True, blank=True)
    specs_data = models.JSONField("规格数据", null=True, blank=True, default=dict)

    class Meta:
        abstract = True


def generate_combine_spec_code():
    attempts = 0
    max_attempts = 30
    while attempts < max_attempts:
        attempts += 1
        spec_code = f"Z{get_random_number_str(6)}"
        if not StockKeepingUnit.objects.filter(spec_code=spec_code).exists():
            return spec_code
    raise ValueError("系统生成商编失败")


class StockKeepingUnit(models.Model):
    sku_id = models.BigIntegerField("sku id", unique=True, blank=False, null=False, default=get_random, db_index=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, blank=False, null=False, verbose_name="商品")
    physical_inventory = models.BigIntegerField("现货库存", default=0)
    safety_inventory = models.BigIntegerField("7天补货库存", default=0, blank=True, null=True)
    warehouse_inventory = models.BigIntegerField("仓库库存", default=0, blank=True, null=True)
    # spec_code = models.CharField("规格编码", max_length=30, blank=True, null=True)
    external_sku_id = models.CharField("外部sku id", unique=True, max_length=30, blank=True, null=True)
    plan_use_inventory = models.BigIntegerField(verbose_name="货盘占用库存", default=0, blank=True, null=True)
    can_use_inventory = models.BigIntegerField(verbose_name="可用库存", default=0, blank=True, null=True)
    spec_code = models.CharField("商品编码", max_length=30, blank=True, null=True, db_index=True)
    link_code = models.CharField("链接编码", max_length=15, blank=True, null=True)
    cost_price = models.DecimalField("成本价", max_digits=10, decimal_places=2)
    retail_price = models.DecimalField("建议售价", max_digits=10, decimal_places=2, blank=True, null=True)
    # retail_price = models.DecimalField("零售价", max_digits=10, decimal_places=2, blank=True, null=True)
    history_price = models.DecimalField("历史价", max_digits=10, decimal_places=2, blank=True, null=True)
    distributor_market_price = models.DecimalField("分销商市场价格", max_digits=10, decimal_places=2, blank=True, null=True)
    weight = models.FloatField("重量", blank=True, null=True)
    sales = models.BigIntegerField("SKU销量", default=0)
    image = models.CharField("规格主图", max_length=300, blank=True, null=True)
    specs = ArrayField(models.JSONField("规格键值对", default=dict), size=3, blank=True, null=True, default=list, verbose_name="规格列表")
    become_history = models.BooleanField("已经变成历史数据", blank=True, null=True, default=False)
    create_date = models.DateTimeField("创建时间", auto_now_add=True, blank=True, null=True)
    update_date = models.DateTimeField("更新时间", auto_now=True, blank=True, null=True)
    # 不使用，货盘针对使用
    has_live = models.BooleanField(verbose_name="是否已播", default=False)
    # 2.28 从array_field修改为many_to_many
    specs_name = models.ManyToManyField(SpecsKey, verbose_name="规格名")
    specs_value = models.ManyToManyField(SpecsValue, verbose_name="规格值")
    order = models.IntegerField(verbose_name="规格排序", default=0, blank=True, null=True)

    # v2.9.7新增分销推广价
    distributor_promotion_price = models.DecimalField("分销推广价格", max_digits=10, decimal_places=2, blank=True, null=True)

    # 版本记录
    history = CustomHistoricalRecords(bases=[SKUHistoricalWithExtraFields])
    objects = SKUHistoricalManager()  # 使用自定义的 Manager
    skip_history_price_objects = SKUSaveSkipHistoryManger()  # 不保留historyManger

    class Meta:
        verbose_name = "商品sku"
        verbose_name_plural = verbose_name
        ordering = ("order", "id")
        indexes = [
            GinIndex(
                fields=["specs"],
                condition=models.Q(become_history=False),
                name="specs_gin_index",
            ),
        ]
        permissions = (
            ("view_cost_price_StockKeepingUnit", "查看sku推广价"),
            ("change_cost_price_StockKeepingUnit", "修改sku推广价"),
            ("show_cost_price_StockKeepingUnit", "推广价、供应商不显示"),
        )
        constraints = [
            models.UniqueConstraint(
                fields=["spec_code"],
                condition=models.Q(become_history=False),
                name="unique_spec_code_when_become_history_is_false",
            ),
        ]

    def __str__(self):
        if self.specs:
            return "-".join([i["value"] for i in self.specs]) + f"({self.sku_id})"
        return str(self.sku_id)

    def generate_spec_code(self, company_code):
        unique = False
        while not unique:
            spec_code = f"J{company_code}" + get_random_number_str(6)
            unique = not self._meta.model.objects.filter(spec_code=spec_code).exists()
        return spec_code

    def get_or_create_latest_history(self):
        latest_history = self.history.first()
        if latest_history:
            return latest_history
        else:
            self.save()
            return self.history.first()

    def save(self, *args, **kwargs):
        self.can_use_inventory = (self.physical_inventory or 0) - (self.plan_use_inventory or 0)

        update_fields = kwargs.get("update_fields", [])
        need_to_create_history = True
        # 单独或仅修改现货库存、销量等不创建历史记录
        if update_fields:
            if len(set(update_fields) - SKU_NO_NEED_TO_CREATE_HISTORY_FIELDS) == 0:
                need_to_create_history = False

        if update_fields and "can_use_inventory" not in update_fields:
            update_fields.append("can_use_inventory")

        try:
            with transaction.atomic():
                if self.pk:
                    # 修改,不允许修改
                    # 暂时开放修改
                    if not self.spec_code:
                        if self.product.is_combine:
                            self.spec_code = generate_combine_spec_code()
                        else:
                            original = self._meta.model.objects.get(pk=self.pk)
                            self.spec_code = original.spec_code
                else:
                    # 新增
                    # 暂时开放传入
                    if not self.spec_code:
                        if self.product.is_combine:
                            self.spec_code = generate_combine_spec_code()
                        else:
                            assert "company_code" in kwargs, "company_code required"
                            self.spec_code = self.generate_spec_code(kwargs.pop("company_code"))

                try:
                    raw_cost_price = self._meta.model.objects.get(pk=self.pk).cost_price
                    raw_sales = self._meta.model.objects.get(pk=self.pk).sales
                    if raw_cost_price != self.cost_price:
                        ProductModHistory(
                            product_id=self.product.product_id,
                            sku_id=self.sku_id,
                            mod_field_name="cost_price",
                            old_value=raw_cost_price,
                            new_value=self.cost_price,
                            create_user=getattr(self, "__update_user__", self.product.update_user),
                        ).save()

                        sku_spec_text = f"{self.sku_id}"
                        notify_trend = ""
                        try:
                            if not self.specs:
                                sku_spec_text = "默认规格"
                            else:
                                sku_spec_text = ",".join([str(spec.get("value")) for spec in self.specs if spec.get("value")])

                            # 趋势
                            if raw_cost_price > self.cost_price:
                                notify_trend = "down"
                            elif raw_cost_price < self.cost_price:
                                notify_trend = "up"
                            else:
                                notify_trend = ""
                        except Exception as e:
                            logger.warning(f"解析规格名称失败,{e},{self.specs}")
                            pass

                        # 创建价格变动通知
                        notifications_data = {
                            "product_id": self.product.product_id,
                            "company_id": self.product.company.company_id,
                            "notify_type": "price",
                            "operation_type": "price_changed",
                            "content": f"{self.product.code}【{sku_spec_text}】的推广价从{raw_cost_price}变为{self.cost_price}",
                            "trend": notify_trend,
                        }
                        ceate_notifications.delay(notifications_data, ["SP", "OP", "DB"])
                    if raw_sales != self.sales:
                        ProductModHistory(
                            product_id=self.product.product_id,
                            sku_id=self.sku_id,
                            mod_field_name="sales",
                            old_value=raw_sales,
                            new_value=self.sales,
                            create_user=getattr(self, "__update_user__", self.product.update_user),
                        ).save()

                except Exception:
                    pass
                if not need_to_create_history:
                    setattr(self, "skip_history_when_saving", True)
                super().save(*args, **kwargs)
        except IntegrityError as e:
            # todo: 如果版本变化，异常信息可能改变，后期考虑优化
            if e.args[0] and e.args[0].startswith('duplicate key value violates unique constraint "products_stockkeepingunit_sku_id'):
                self.sku_id = get_random()
                self.save(*args, **kwargs)
            else:
                raise e
        except Exception as e:
            logger.error(f"{str(e)}")
            raise e

    @property
    def distribute_show_physical_inventory(self):
        """
        分销模式显示的库存, 方便统一修改
        :return:
        """
        return self.physical_inventory

    @property
    def distribute_show_can_use_inventory(self):
        """
        分销模式显示的可用库存, 方便统一修改
        :return:
        """
        return self.can_use_inventory
        return

    # def get_pre_sale_distributor_promotion_price(self, price_configs, need_post_amount: bool = False):
    #     # 计算预售分销价格
    #     _calc_price = 0
    #     for price_config in price_configs:
    #         if self.cost_price > price_config.min_cost_price:
    #             _calc_price = self.cost_price * decimal.Decimal(price_config.markup_percentage / 100)
    #
    #     price = decimal.Decimal(_calc_price).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
    #     # 榜单，没在分销市场的加3块邮费
    #     if need_post_amount:
    #         price += decimal.Decimal("3.00")
    #
    #     rule_price = process_price_with_rule(price)
    #     return rule_price

    def get_pre_sale_distributor_market_price(self, price_configs, need_post_amount: bool = False):
        # 计算预售分销价格
        # _calc_price = 0
        # for price_config in price_configs:
        #     if self.cost_price > price_config.min_cost_price:
        #         _calc_price = self.cost_price * decimal.Decimal(price_config.markup_percentage / 100)

        _calc_price = 0
        for price_config in price_configs:
            if not self.distributor_promotion_price:
                _calc_price = self.cost_price * decimal.Decimal(price_config.markup_percentage / 100)
            else:
                if self.distributor_promotion_price > price_config.min_cost_price:
                    _calc_price = self.distributor_promotion_price * decimal.Decimal(price_config.markup_percentage / 100)

        price = decimal.Decimal(_calc_price).quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
        # 榜单，没在分销市场的加3块邮费
        if need_post_amount:
            price += decimal.Decimal("3.00")

        rule_price = process_price_with_rule(price)
        return rule_price


def generate_sub_sku_id():
    attempts = 0
    max_attempts = 30
    while attempts < max_attempts:
        attempts += 1
        sub_sku_id = get_random()
        if not SubStockKeepingUnit.objects.filter(sub_sku_id=sub_sku_id).exists():
            return sub_sku_id
    raise ValueError("系统生成子SKUID失败")


class SubStockKeepingUnit(models.Model):
    sub_sku_id = models.BigIntegerField("子SKU_ID", unique=True, blank=False, null=False, default=generate_sub_sku_id, db_index=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, to_field="product_id", db_constraint=False, verbose_name="所属商品")
    parent_sku = models.ForeignKey(
        StockKeepingUnit,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="父SKU",
        related_name="parent_sub_skus",
        to_field="sku_id",
    )
    relate_sku = models.ForeignKey(
        StockKeepingUnit,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联SKU",
        related_name="related_sub_skus",
        to_field="sku_id",
    )
    num = models.IntegerField("数量", default=0)
    become_history = models.BooleanField("是否成为历史", default=False)
    is_main = models.BooleanField("是否为主商品", null=True, blank=True, default=False)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "组合商品子SKU"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=["parent_sku", "relate_sku"],
                condition=models.Q(become_history=False),
                name="unique_parent_sku_and_relate_sku_with_become_history",
            ),
        ]


class ProductModHistory(models.Model):
    """
    商品/SKU变动历史表
    主要是记录人为修改的数据 create_user为system的代表系统拉取的数据
    """

    product = models.ForeignKey(Product, on_delete=models.CASCADE, db_constraint=False, to_field="product_id", null=True, blank=True)
    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE, db_constraint=False, to_field="sku_id", null=True, blank=True)
    mod_field_name = models.CharField(verbose_name="变动的字段", max_length=64, null=False, blank=False, default="", db_index=True)
    old_value = models.CharField(verbose_name="旧数据", max_length=128, null=True, blank=True, default="")
    new_value = models.CharField(verbose_name="新数据", max_length=128, null=True, blank=True, default="")
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True, db_index=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True, db_index=True)

    class Meta:
        verbose_name = "商品/SKU变动历史表"
        verbose_name_plural = verbose_name


class SysProductModHistory(models.Model):
    """
    系统商品变动历史表
    主要记录聚水谭拉取的数据
    """

    product = models.ForeignKey(Product, on_delete=models.CASCADE, db_constraint=False, to_field="product_id", null=True, blank=True)
    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE, db_constraint=False, to_field="sku_id", null=True, blank=True)
    mod_field_name = models.CharField(verbose_name="变动的字段", max_length=64, null=False, blank=False, default="")
    old_value = models.CharField(verbose_name="旧数据", max_length=128, null=True, blank=True, default="")
    new_value = models.CharField(verbose_name="新数据", max_length=128, null=True, blank=True, default="")
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "系统商品/SKU变动历史表"
        verbose_name_plural = verbose_name


def calc_product_inventory_trend(product: Product):
    """计算商品的现货库存趋势"""
    new_mod_history = product.productmodhistory_set.filter(mod_field_name="physical_inventory").exclude(create_user="system").order_by("-create_date").first()
    if not new_mod_history:
        return ""
    now_prod_physical_inventory = product.physical_inventory
    change_value = int(new_mod_history.old_value)

    if change_value > now_prod_physical_inventory:
        return "down"
    elif change_value == now_prod_physical_inventory:
        return ""
    return "up"


def calc_product_cost_price_trend(product: Product):
    """
    计算价格的趋势
    比较最新的sku信息即可
    """
    new_mod_history = product.productmodhistory_set.filter(mod_field_name="cost_price").exclude(create_user="system").order_by("-create_date").first()
    if not new_mod_history:
        return ""

    if not new_mod_history.sku:
        return ""

    now_sku_cos_price = new_mod_history.sku.cost_price
    change_value = decimal.Decimal(new_mod_history.old_value).quantize(decimal.Decimal("0.00"))

    if change_value > now_sku_cos_price:
        return "down"
    elif change_value == now_sku_cos_price:
        return ""
    return "up"


def calculate_history_price(product_id):
    """
    计算历史价
    """
    try:
        product = Product.objects.get(product_id=product_id, is_deleted=False)
    except Product.DoesNotExist:
        logger.error(f"product: {product_id} not found")
        return

    sku_qs = product.stockkeepingunit_set.filter(become_history=False).values("id")
    sku_ids = [sku["id"] for sku in sku_qs]

    latest_prices = HistoryPrice.objects.filter(sku_id__in=sku_ids).values("sku_id").annotate(latest_create_date=Max("create_date"))
    min_history_price = None
    max_history_price = None
    for price in latest_prices:
        latest_price = HistoryPrice.objects.filter(sku_id=price["sku_id"], create_date=price["latest_create_date"]).first()
        if latest_price and latest_price.history_price:
            if not min_history_price:
                min_history_price = latest_price.history_price
            if not max_history_price:
                max_history_price = latest_price.history_price
            if latest_price.history_price < min_history_price:
                min_history_price = latest_price.history_price
            if latest_price.history_price > max_history_price:
                max_history_price = latest_price.history_price
    product.min_history_price = min_history_price
    product.max_history_price = max_history_price
    update_fields = [
        "min_history_price",
        "max_history_price",
    ]
    # 计算动态历史价不创建历史记录
    product.skip_history_when_saving = True
    product.save(update_fields=update_fields)


def process_id_generator():
    process_id = uuid.uuid4().hex

    while 1:
        if not ProductReviewProcess.objects.filter(process_id=process_id).exists():
            return process_id
        process_id = uuid.uuid4().hex


class ProductReviewProcess(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, blank=False, null=False, verbose_name="商品")
    # 同个id属于同一个流程
    process_id = models.CharField(verbose_name="审核流程ID", unique=True, editable=False, default=process_id_generator, max_length=32)
    become_history = models.BooleanField(verbose_name="是否为历史数据", default=False)

    class Meta:
        verbose_name = "商品审核流程"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=["product"],
                condition=models.Q(become_history=False),
                name="unique_product_process",
            ),
        ]

    def __str__(self):
        return self.process_id


class ProductReview(models.Model):
    process = models.ForeignKey(
        ProductReviewProcess,
        on_delete=models.CASCADE,
        to_field="process_id",
        db_constraint=False,
        null=True,
        blank=True,
        verbose_name="审核流程ID",
    )

    PROCESS_LEVEL_CHOICES = (
        ("BASIC_REVIEW", "基础信息审核"),
        ("PRICE_REVIEW", "核价信息审核"),
        ("QA_REVIEW", "质检信息审核"),
    )

    product = models.ForeignKey(Product, on_delete=models.CASCADE, blank=False, null=False, verbose_name="商品")
    process_level = models.CharField(
        verbose_name="审核层级",
        choices=PROCESS_LEVEL_CHOICES,
        max_length=32,
        default="BASIC_REVIEW",
    )
    physical_inventory_exact = models.BooleanField("现货库存准确", default=False)
    quality_qualified = models.BooleanField("品质合格", default=False)
    price_reasonable = models.BooleanField("价格合理", default=False)
    recommended_price = models.DecimalField("建议售价", max_digits=10, decimal_places=2, blank=True, null=True)
    review_cost_price = models.DecimalField("核价成本价", max_digits=10, decimal_places=2, blank=True, null=True)
    remark = models.TextField("备注", blank=True, null=True)
    review_times = models.PositiveSmallIntegerField(verbose_name="第几次审核", default=1)
    become_history = models.BooleanField(verbose_name="是否为历史数据", default=False)
    selection_plan = models.ForeignKey(
        "ProductSelectionPlan",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="plan_id",
        null=True,
        blank=True,
        verbose_name="关联的货盘",
    )
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品审核"
        verbose_name_plural = verbose_name

    @property
    def verified_pass(self):
        return all([self.physical_inventory_exact, self.quality_qualified, self.price_reasonable])


class QAReviewQuestionType(models.Model):
    _cache_key = "qa_q_list:{}"
    name = models.CharField("质检问题类型名称", max_length=32, db_index=True, unique=True)
    short_desc = models.CharField("简单描述", max_length=12, null=True, blank=True, default="")
    enable = models.BooleanField("是否启用", default=True)

    class Meta:
        verbose_name = "质检问题类型"
        verbose_name_plural = verbose_name

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        super().save(force_insert, force_update, using, update_fields)

        if self.pk:
            cache.delete(self._cache_key.format(self.pk))

    def __str__(self):
        return f"{self.name}"

    def get_questions_list_with_cache(self):
        val = cache.get(self._cache_key.format(self.pk))
        if val:
            return val

        questions = self.qareviewquestion_set.all()
        re_data = [{"id": question.id, "name": question.name} for question in questions]
        cache.set(self._cache_key.format(self.pk), re_data, 60 * 60 * 2)
        return re_data


class QAReviewQuestion(models.Model):
    name = models.CharField("问题名称", max_length=200)
    q_type = models.ForeignKey(QAReviewQuestionType, on_delete=models.CASCADE, db_constraint=False, verbose_name="关联类型")

    class Meta:
        verbose_name = "质检问题"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.name}"

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        super().save(force_insert, force_update, using, update_fields)

        if self.pk:
            cache.delete(QAReviewQuestionType._cache_key.format(self.q_type_id))


class ProductReviewExtra(models.Model):
    review = models.OneToOneField(ProductReview, on_delete=models.CASCADE, db_constraint=False, verbose_name="所关联的审核")
    total_num = models.IntegerField("质检总数", default=0)
    pass_num = models.IntegerField("合格数量", default=0)
    # [
    #     {
    #         "type_id": 1,
    #         "type_name": "主石品质",
    #         "is_fine": true,
    #         "question_id": null,
    #         "question_name": null,
    #         "question_remark": null
    #     },
    #     {
    #         "type_id": 2,
    #         "type_name": "工艺质量",
    #         "is_fine": false,
    #         "question_id": 1,
    #         "question_name": "抛光问题",
    #         "question_remark": ""
    #     }
    # ]
    qa_content = models.JSONField("质检内容", null=True, blank=True, default=dict)

    class Meta:
        verbose_name = "质检审核额外信息"
        verbose_name_plural = verbose_name


class HistoryPrice(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, blank=False, null=False, verbose_name="商品")
    sub_product = models.ForeignKey(
        "SubProduct",
        on_delete=models.CASCADE,
        db_constraint=False,
        blank=True,
        null=True,
        verbose_name="我的商品",
        to_field="product_id",
    )
    raw_product_id = models.BigIntegerField("原始商品id", blank=True, null=True)
    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE, blank=False, null=True, default=None, verbose_name="sku")
    history_price = models.DecimalField("历史价", max_digits=10, decimal_places=2, blank=True, null=True)
    author = models.ForeignKey(LiveAuthor, on_delete=models.SET_NULL, to_field="author_id", blank=True, null=True, verbose_name="主播")
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    # 去掉 auto_now_add=True 和 auto_now=True
    create_date = models.DateTimeField("创建时间", blank=True, null=True)
    update_date = models.DateTimeField("更新时间", blank=True, null=True)
    objects = CTEManager()

    class Meta:
        verbose_name = "历史价格表"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=["sku", "create_date", "author"],
                name="product_create_date_author_index",
            ),
        ]

    def __str__(self):
        return f"HistoryPrice({self.product_id}-{self.sku_id})"


class ProductAttrOption(models.Model):
    """
    商品属性，有商品关系, 已被商品选中
    """

    attr = models.ForeignKey(ProductAttrList, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    name = models.CharField(max_length=8, verbose_name="选择的属性名")
    value = models.CharField("选择的属性值", max_length=8)
    attr_value = models.ForeignKey(
        ProductAttrValues,
        on_delete=models.CASCADE,
        verbose_name="属性值",
        db_constraint=False,
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = "商品属性"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=[
                    "product",
                    "attr",
                    "attr_value",
                ],
                name="unique_product_attr_value",
            ),
        ]

    def __str__(self):
        return f"{self.name}({self.attr_id})"


class ProductSpecList(models.Model):
    """
    商品规格列表，创建商品时创建，供编辑商品时选择
    如果是从商品属性中同步过来的，则有source_id
    0629: 同ProductSpecOption，此表不再使用，直接在产品中设置字段
    """

    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    source_id = models.IntegerField("属性源id", blank=True, null=True)
    name = models.CharField("规格名", max_length=8, blank=False, null=False)
    values = ArrayField(models.CharField("规格值", max_length=8, blank=False, null=False), size=50, blank=False, null=False)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品规格列表"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=["product", "name"],
                name="unique_product_speclist_name",
            ),
        ]

    def __str__(self):
        return self.name


class ProductSpecOption(models.Model):
    """
    商品规格，有商品关系，已被商品sku选中
    0629: 由于前端更新时无法保存id传回，故此表不再使用，直接在sku设置字段
    """

    spec = models.ForeignKey(ProductSpecList, on_delete=models.CASCADE)
    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE)
    name = models.CharField("选择的规格名", max_length=8, blank=False, null=False)
    value = models.CharField("选择的规格值", max_length=8)

    class Meta:
        verbose_name = "商品规格"
        verbose_name_plural = verbose_name


def sub_product_id_generator():
    tmp_product_id = get_random()
    while 1:
        if not SubProduct.objects.filter(product_id=tmp_product_id).exists():
            return tmp_product_id
        tmp_product_id = get_random()


def sub_product_code_generator(letters: str, product_pk_list: list) -> dict:
    """
    返回商品主键和对应的code
    :param letters:
    :param product_pk_list:
    :return:
    """
    re_data = {}
    if not product_pk_list:
        return re_data

    current_code_list = []

    for product_pk in product_pk_list:
        for _ in range(120):
            code = letters + get_random_number_str(4)
            if code in current_code_list:
                continue

            if SubProduct.objects.filter(code=code).exists():
                continue

            if Product.objects.filter(code=code).exists():
                continue

            current_code_list.append(code)
            re_data[product_pk] = code
            break
        else:
            raise APIViewException(err_message="系统生成货号失败")

    return re_data


class SubProduct(BaseModel):
    """
    商品副本
    注意：商品副本的object会过滤掉is_deleted的数据， 如果需要全部筛选用origin_objects
    注意：商品副本的object会过滤掉is_deleted的数据， 如果需要全部筛选用origin_objects
    注意：商品副本的object会过滤掉is_deleted的数据， 如果需要全部筛选用origin_objects
    """

    _log_private_field = ["code_change_times"]

    PRODUCT_LISTED_CHOICES = (
        (0, "待上架"),
        (1, "已上架"),
        (2, "已下架"),
    )
    parent_product = models.ForeignKey(Product, on_delete=models.CASCADE, blank=False, null=False, verbose_name="父商品")
    product_id = models.BigIntegerField("商品id", unique=True, blank=False, null=False, default=sub_product_id_generator)
    # 分销商特有的数据
    code = models.CharField("货号", max_length=20, blank=True, null=True)
    # 只推一次货号到聚水潭
    inited_code = models.CharField("初始化货号", max_length=20, blank=True, null=True)
    name = models.CharField("商品名称", max_length=80, blank=False, null=False)
    main_images = ArrayField(models.CharField("商品主图", max_length=300, default=None), size=15, blank=False, null=False, verbose_name="主图列表")
    detail_images = models.TextField("图文详情", blank=True, null=True)
    remark = models.TextField("商品备注", blank=True, null=True)
    size = models.CharField("尺寸", max_length=56, blank=True, null=True, default=None)
    # 默认0, 暂时逻辑是只能修改一次，扩展多次
    code_change_times = models.PositiveSmallIntegerField(verbose_name="货号修改次数", default=0)
    # 分销商特有的数据
    alias_code = models.CharField("分销商别称货号", max_length=20, blank=True, null=True)
    alias_code_history = models.JSONField("别称货号历史数据", null=True, blank=True, default=dict)
    #
    owner = models.ForeignKey(Distributor, on_delete=models.CASCADE, to_field="distributor_id", verbose_name="副本所属分销商")
    is_listed_DouDian = models.SmallIntegerField("上架抖店状态", choices=PRODUCT_LISTED_CHOICES, default=0)
    listed_DouDian_return = models.CharField("上架抖店的返回值", max_length=56, blank=True, null=True)
    # 是否在分销商市场
    is_in_distributor_market = models.BooleanField("是否在分销商市场", default=False)

    def _code_generator_(self):
        if not self.owner:
            return
        if not self.owner.letters:
            raise ValueError("distributor with no special letter code")

        unique = False
        length = 5
        while not unique:
            code = self.owner.letters + get_random_number_str(length)
            # 新增查原商品表
            unique = (not self._meta.model.objects.filter(code=code).exists()) and (not Product.objects.filter(code=code).exists())
        return code

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        if self.pk:
            # 修改,不允许修改
            # 暂时开放
            if not self.code:
                original = self._meta.model.objects.get(pk=self.pk)
                self.code = original.code
        else:
            # 新增
            # 暂时开放
            if not self.code:
                self.code = self._code_generator_()
        if not self.inited_code:
            self.inited_code = self.code
        return super().save(force_insert, force_update, using, update_fields)

    class Meta:
        verbose_name = "商品副本"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=["parent_product", "owner"],
                condition=models.Q(is_deleted=False),
                name="unique_owner_subproduct_with_is_deleted",
            ),
        ]

        permissions = (
            ("view_min_cost_price_SubProduct", "查看商品副本最小推广价"),
            ("change_min_cost_price_SubProduct", "修改商品副本最小推广价"),
            ("view_max_cost_price_SubProduct", "查看商品副本最大推广价"),
            ("change_max_cost_price_SubProduct", "修改商品副本最大推广价"),
        )

    def __str__(self):
        return self.name + f"({self.product_id})"

    @classmethod
    def get_distributor_sub_products(cls, request, *args, **kwargs) -> QuerySet:
        # 特定供应商可见
        from products.models_v2.product_config import ProductConfig

        db_id = request.user.distributor.distributor_id
        cls_qs = SubProduct.objects.all()
        if kwargs.get("prefetch_fields"):
            cls_qs = cls_qs.prefetch_related(*kwargs["prefetch_fields"])

        if kwargs.get("is_distributor_mode"):
            target = cls_qs.filter(
                distributormarketsubproductrelate__owner_id=db_id,
                owner__letters="FX",
                parent_product__is_deleted=False,
                is_in_distributor_market=True,
                parent_product__is_in_distributor_market=True,
            )
        else:
            target = cls_qs.filter(
                owner_id=db_id,
                is_deleted=False,
                parent_product__is_deleted=False,
            )

        # 不能看的商品id列表
        cannot_see_product_id_list = list(
            ProductConfig.objects.exclude(
                visible_distributor=[],
            )
            .exclude(visible_distributor__contains=db_id)
            .values_list("product_id", flat=True)
        )
        target = target.exclude(parent_product__product_id__in=cannot_see_product_id_list)

        target = target.filter(
            Q(parent_product__company__visible_distributors=[])
            | Q(parent_product__company__visible_distributors__contains=db_id)
            | Q(parent_product__company__visible_distributors__contains=str(db_id)),
        )

        return target


class DistributorMarketSubProductRelate(models.Model):
    sub_product = models.ForeignKey(SubProduct, on_delete=models.CASCADE, blank=False, null=False, verbose_name="关联市场分销子商品")
    owner = models.ForeignKey(Distributor, on_delete=models.CASCADE, to_field="distributor_id", verbose_name="副本所属分销商")
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_user = models.CharField(verbose_name="最后更新人", default="", null=True, blank=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "分销市场关联子商品"
        verbose_name_plural = verbose_name


class ProductSelectionPlan(models.Model):
    """
    选品计划
    """

    PLAN_STATE_CHOICES = (
        (0, "已结束"),
        (1, "进行中"),
        (2, "已锁定"),
    )
    plan_id = models.BigIntegerField("计划id", unique=True, blank=False, null=False, default=get_random)
    name = models.CharField("计划名称", max_length=80, blank=False, null=False)
    # live_author = models.ForeignKey(LiveAuthor, on_delete=models.SET_NULL, blank=False, null=True, default=None, verbose_name="主播")
    distributor = models.ForeignKey(Distributor, on_delete=models.SET_NULL, to_field="distributor_id", blank=False, null=True, default=None, verbose_name="分销商")
    cost_item_config = models.ForeignKey("CostItemConfig", on_delete=models.SET_NULL, blank=True, null=True, default=None, verbose_name="成本项")
    remark = models.TextField("备注", blank=True, null=True)
    live_date_start = models.DateField("直播开始日期", db_index=True)
    live_date_end = models.DateField("直播结束日期", db_index=True)
    state = models.SmallIntegerField("状态", choices=PLAN_STATE_CHOICES, default=1)
    spu_count = models.IntegerField("spu数量", null=True, blank=True, default=0)
    total_sales_amount = models.DecimalField("总销售额", max_digits=10, decimal_places=2, null=True, blank=True, default=0)
    total_order_count = models.IntegerField("订单数量", null=True, blank=True, default=0)
    total_profit = models.DecimalField("总利润", max_digits=10, decimal_places=2, null=True, blank=True, default=0)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        # verbose_name = "选品计划"
        verbose_name = "货盘计划"
        verbose_name_plural = verbose_name
        ordering = ("-live_date_start",)

    def save(self, *args, **kwargs):
        try:
            with transaction.atomic():
                super().save(*args, **kwargs)
        except IntegrityError as e:
            # todo: 如果版本变化，异常信息可能改变，后期考虑优化
            if e.args[0] and e.args[0].startswith('duplicate key value violates unique constraint "products_productselectionplan_plan_id'):
                self.plan_id = get_random()
                self.save(*args, **kwargs)
            else:
                raise e
        except Exception as e:
            logger.error(f"{str(e)}")
            raise e

    def __str__(self):
        return f"{self.name}({self.plan_id})"


class ProductSelectionPlanCompany(models.Model):
    selection_plan = models.ForeignKey(ProductSelectionPlan, on_delete=models.CASCADE, blank=False, null=False, to_field="plan_id", verbose_name="所属计划")
    company = models.ForeignKey(Company, on_delete=models.CASCADE, blank=False, null=False, to_field="company_id", verbose_name="公司")
    order = models.IntegerField("排序", blank=True, default=0)
    is_deleted = models.BooleanField("是否删除", default=False)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "货盘计划公司"
        verbose_name_plural = verbose_name

        constraints = [
            models.UniqueConstraint(
                fields=["selection_plan", "company"],
                condition=models.Q(is_deleted=False),
                name="unique_plan_company_when_is_deleted_is_false",
            ),
        ]

    def __str__(self):
        return f"PlanCompany({self.selection_plan}-{self.company.name})"


class ProductSelectionPlanCategory(models.Model):
    plan_company = models.ForeignKey(ProductSelectionPlanCompany, on_delete=models.CASCADE, blank=False, null=False, verbose_name="所属货盘计划公司")
    category = models.ForeignKey(ProductCategoryList, on_delete=models.CASCADE, blank=False, null=False, verbose_name="所属分类")
    order = models.IntegerField("排序", blank=True, default=0)
    is_deleted = models.BooleanField("是否删除", default=False)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "货盘计划分类"
        verbose_name_plural = verbose_name

        constraints = [
            models.UniqueConstraint(
                fields=["plan_company", "category"],
                condition=models.Q(is_deleted=False),
                name="plan_company_category_when_is_deleted_is_false",
            ),
        ]

    def __str__(self):
        return f"SelectionPlanCategroy({self.plan_company.company.name}-{self.category.name})"


class ProductSelectionItem(models.Model):
    CREATE_USER_TYPE_CHOICES = (
        ("SP", "供应商"),
        ("OP", "运营商"),
        ("DB", "分销商"),
    )

    selection_plan = models.ForeignKey(ProductSelectionPlan, on_delete=models.CASCADE, blank=False, null=False, to_field="plan_id", verbose_name="所属计划")
    plan_category = models.ForeignKey(ProductSelectionPlanCategory, on_delete=models.CASCADE, blank=True, null=True, verbose_name="所属货盘计划分类")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, blank=False, null=False, to_field="product_id", verbose_name="商品")
    product_version_id = models.BigIntegerField("货盘商品快照ID", null=True, blank=True, default=None)
    product_history_info = models.JSONField("商品历史快照特殊信息", null=True, blank=True, default=dict)

    # 商品副本
    sub_product = models.ForeignKey(
        SubProduct,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        to_field="product_id",
        verbose_name="分销商我的商品",
    )

    remark = models.TextField("备注", blank=True, null=True)
    order = models.IntegerField("排序", blank=True, default=0)
    map_order = models.IntegerField("排品地图模式排序", blank=True, default=0)
    link_cs = models.ForeignKey(
        CustomerService,
        verbose_name="关联的客服",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        default=None,
        db_constraint=False,
    )
    link_cs_remark = models.TextField("关联客户备注", null=True, blank=True, default="")
    is_deleted = models.BooleanField("是否删除", default=False)
    delete_reason = models.CharField("删除理由", blank=True, null=True, default="")
    selection_reason = models.CharField("选品理由", blank=True, null=True, default="")
    has_live = models.BooleanField(verbose_name="是否已播", default=False)
    create_user_type = models.CharField("用户类型", choices=CREATE_USER_TYPE_CHOICES, max_length=2, default="DB")
    # 商品确认状态
    product_confirm_state = models.PositiveSmallIntegerField(
        verbose_name="商品确认状态",
        choices=(
            (0, "未确认"),
            (1, "已确认"),
        ),
        default=0,
    )
    physical_inventory = models.IntegerField(default=0, verbose_name="货盘SPU现货库存")
    mark = models.JSONField("货盘标记列表", null=True, blank=True, default=list)
    read_users = models.JSONField("查看过的用户user_id列表", null=True, blank=True, default=list)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    objects = CTEManager()

    class Meta:
        verbose_name = "货盘计划数据条目"
        verbose_name_plural = verbose_name

        constraints = [
            models.UniqueConstraint(
                fields=["plan_category", "product"],
                condition=models.Q(is_deleted=False),
                name="plan_category_prodcut_when_is_deleted_is_false",
            ),
        ]

    def __str__(self):
        return f"SelectionItem({self.selection_plan}-{self.product})"

    @property
    def product_deleted(self):
        validate_list = []
        if self.product:
            validate_list.append(self.product.is_deleted)
        if self.sub_product:
            validate_list.append(self.sub_product.is_deleted)
        return all(validate_list)


class ProductSelectionItemSKU(models.Model):
    selection_plan = models.ForeignKey(
        ProductSelectionPlan,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="plan_id",
        verbose_name="所属计划",
    )
    sku = models.ForeignKey(
        StockKeepingUnit,
        to_field="sku_id",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联sku信息",
    )
    item = models.ForeignKey(
        ProductSelectionItem,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="所属货盘item",
    )
    become_history = models.BooleanField(default=False, verbose_name="是否成为历史")
    physical_inventory = models.IntegerField(default=0, verbose_name="货盘SKU现货库存")
    actual_sales = models.BigIntegerField("实际销量", blank=True, null=True, default=0)
    actual_price = models.DecimalField("实际订单商品价格", max_digits=10, decimal_places=2, blank=True, null=True)
    remark = models.TextField(verbose_name="备注信息", default="", null=True, blank=True)
    sku_version_id = models.BigIntegerField("sku快照ID", null=True, blank=True, default=None)
    # 记录快照历史表不存在的数据
    # {
    #     "cost_price": 123,
    #     "retail_price": 123,
    #     "physical_inventory": 123,
    #     "warehouse_inventory": 123,
    #     "safety_inventory": 888,
    #     "product_attrs": [{'id': 51506, 'name': '款式', 'value': '手镯', 'attr_id': 2, 'value_id': 17}]
    # }
    sku_history_info = models.JSONField("规格特殊历史数据", null=True, blank=True, default=dict)
    order_count = models.IntegerField("订单数量", blank=True, null=True, default=0)
    estimated_sales = models.IntegerField("预估销量", blank=True, null=True, default=0)
    sales_amount = models.DecimalField("销售额", max_digits=20, decimal_places=2, blank=True, null=True)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    objects = CTEManager()

    class Meta:
        verbose_name = "计划关联的SKU信息"
        verbose_name_plural = verbose_name

    # @property
    # def history_cost_price(self):
    #     if not self.sku_history_info:
    #         return self.sku.cost_price
    #     elif "cost_price" not in self.sku_history_info:
    #         return self.sku.cost_price
    #     else:
    #         return self.sku_history_info.get("cost_price", 0) or 0


class ProductSelectionPlanRecordType(models.TextChoices):
    ADD_PROD = "add_product", "增加了商品"
    DEL_PROD = "delete_product", "删除了商品"
    RESTORE_PROD = "restore_product", "还原了商品"
    ADD_REMARK = "add_remark", "增加了备注"
    MOD_REMARK = "modify_remark", "更新备注"
    ADD_PLAN = "add_plan", "增加了计划"
    MOD_PLAN = "modify_plan", "编辑了计划"
    DEL_PLAN = "delete_plan", "删除了计划"
    LOCK_PLAN = "lock_plan", "锁定计划"
    RELEASE_PLAN = "release_plan", "解锁计划"
    MOD_ORDER = "modify_order", "调整顺序"
    MOD_CS_LINK = "modify_cs_link", "更新了关联客服"
    PROD_CONFIRM = "prod_confirm", "商品确认"
    CANCEL_PROD_CONFIRM = "cancel_prod_confirm", "取消商品确认"
    MOD_SKU_INVENTORY = "mod_sku_inventory", "编辑SKU库存"


class ProductSelectionPlanRecord(models.Model):
    """商品货盘表操作明细"""

    selection_plan = models.ForeignKey(
        ProductSelectionPlan,
        on_delete=models.CASCADE,
        blank=False,
        null=False,
        to_field="plan_id",
        verbose_name="所属计划",
        db_constraint=False,
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        to_field="product_id",
        verbose_name="商品",
        db_constraint=False,
    )

    record_type = models.CharField("操作类型", max_length=30, choices=ProductSelectionPlanRecordType.choices, null=True, blank=True, db_index=True)
    content = models.TextField("操作内容", default="")
    remark = models.TextField("备注", default="")
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True, db_index=True)

    class Meta:
        verbose_name = "货盘表操作明细"
        verbose_name_plural = verbose_name


class ProductAdvicePrice(models.Model):
    advice_price = models.DecimalField("建议价格", max_digits=10, decimal_places=2, blank=True, null=True)
    remark = models.TextField("备注信息", default="")
    create_date = models.DateTimeField(verbose_name="创建日期", auto_now_add=True)
    update_date = models.DateTimeField(verbose_name="更新日期", auto_now_add=True)

    class Meta:
        verbose_name = "商品建议价格"
        verbose_name_plural = verbose_name


class ProductReviewPriceStateChoice(models.IntegerChoices):
    PASS = 1, "通过"
    Fail = 2, "不通过"


class ProductReviewPrice(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, to_field="product_id", verbose_name="商品", db_constraint=False)
    state = models.PositiveSmallIntegerField(verbose_name="核价状态", choices=ProductReviewPriceStateChoice.choices)
    review_price = models.DecimalField("审核的价格", max_digits=10, decimal_places=2, blank=True, null=True)

    remark = models.TextField(verbose_name="备注信息", default="")
    create_user = models.BigIntegerField(verbose_name="审核用户ID")
    create_date = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_user = models.BigIntegerField(verbose_name="更新用户ID", null=True, blank=True, default=0)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "核价信息"
        verbose_name_plural = verbose_name


class LiveNeeds(models.Model):
    needs_id = models.BigIntegerField(verbose_name="直播需求ID", null=False, blank=False, default=get_random)
    theme = models.CharField(verbose_name="直播主题", null=False, blank=False, max_length=100)
    live_date = models.DateField(verbose_name="直播日期", null=False, blank=False)
    live_category = ArrayField(
        base_field=ArrayField(
            base_field=models.BigIntegerField(
                verbose_name="分类ID",
            ),
            size=12,
            default=None,
            verbose_name="多级分类id列表",
        ),
        size=64,
        null=True,
        blank=True,
        default=list,
        verbose_name="分类列表",
    )

    product_images = ArrayField(
        models.CharField(
            "参考商品图片url",
            max_length=300,
            default=None,
        ),
        size=20,
        blank=False,
        null=False,
        verbose_name="参考商品图片列表",
    )
    remark = models.TextField(verbose_name="备注说明", null=True, blank=True, default="")
    state = models.PositiveSmallIntegerField(
        verbose_name="需求确认状态",
        choices=(
            (0, "未确认"),
            (1, "已确认"),
        ),
        default=0,
    )
    distributor = models.ForeignKey(Distributor, on_delete=models.CASCADE, db_constraint=False, to_field="distributor_id", null=True, blank=True)
    create_user = models.CharField(verbose_name="创建人", null=True, blank=True, default="")
    update_user = models.CharField(verbose_name="更新人", null=True, blank=True, default="")
    confirm_user = models.CharField(verbose_name="确认人", null=True, blank=True, default="")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    confirm_date = models.DateTimeField("确认时间", null=True, blank=True)

    class Meta:
        verbose_name = "直播需求"
        verbose_name_plural = verbose_name

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        if not self.pk:
            end_time = time.time() + 120
            while 1:
                if time.time() > end_time:
                    raise ValueError("Fail generate needs_id in 2 minutes")
                if not self._meta.model.objects.filter(needs_id=self.needs_id).exists():
                    break
                self.needs_id = get_random()

        return super().save(force_insert, force_update, using, update_fields)

    def __str__(self):
        return f"{self.theme}({self.pk})"


class ProductLinkDistributor(models.Model):
    product = models.ForeignKey(
        Product,
        to_field="product_id",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联商品",
    )
    distributor = models.ForeignKey(
        Distributor,
        to_field="distributor_id",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="商品关联的分销商",
    )
    linked_product_id = models.BigIntegerField(
        verbose_name="被关联商品product_id",
        null=True,
    )
    is_sub_linked = models.BooleanField(verbose_name="是否关联的副本id", default=False)
    code = models.CharField(verbose_name="货号", max_length=20, blank=True, null=True)
    # 预留拖动
    order = models.PositiveSmallIntegerField(verbose_name="显示顺序(越大显示越前)", default=0)
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品关联分销商货号"
        verbose_name_plural = verbose_name

        # constraints = [
        #     models.UniqueConstraint(
        #         fields=["product", "distributor"],
        #         condition=models.Q(is_deleted=False),
        #         name="unique_product_distributor_is_deleted_is_true",
        #     ),
        # ]


class PlanItemDeleteReasonChoices(models.Model):
    name = models.CharField(verbose_name="删除文本", max_length=128)
    enable = models.BooleanField(verbose_name="是否启用", default=False)
    order = models.PositiveSmallIntegerField(verbose_name="排序,数字越小排越前", default=0)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "货盘商品删除理由标签"
        verbose_name_plural = verbose_name
        ordering = ("order",)


class HostingProducts(BaseModel):
    hosting_id = models.IntegerField(verbose_name="托管ID", unique=True)
    product = models.ForeignKey(
        Product,
        to_field="product_id",
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联商品",
    )
    live_start_date = models.DateTimeField(verbose_name="直播开始时间", null=True, blank=True)
    live_end_date = models.DateTimeField(verbose_name="直播结束时间", null=True, blank=True)
    state = models.PositiveSmallIntegerField(
        verbose_name="托管状态",
        choices=(
            (1, "托管中"),
            (2, "托管过期"),
            (3, "取消托管"),
        ),
        default=1,
    )
    expire_date = models.DateTimeField(
        verbose_name="过期时间",
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = "供应商托管商品列表"
        verbose_name_plural = verbose_name

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        if not self.pk or not self.hosting_id:
            _hosting_id = get_random()
            while True:
                if not HostingProducts.objects.filter(hosting_id=_hosting_id).exists():
                    break
                _hosting_id = get_random()
            self.hosting_id = _hosting_id

        super().save(force_insert, force_update, using, update_fields)


def bulk_query_product_hosting_state(product_ids: list | tuple | set) -> dict:
    """批量查询托管商品的状态"""
    hosting_products = (
        HostingProducts.objects.filter(
            product_id__in=product_ids,
            state=1,
            is_deleted=False,
            product__is_deleted=False,
        )
        .only("product_id", "state")
        .order_by("id")
    )

    return {hosting_product.product_id: hosting_product.state for hosting_product in hosting_products}


class ProductRecord(models.Model):
    product_id = models.BigIntegerField("主商品id", unique=True, blank=False, null=False)
    old_code = models.CharField("原货号", max_length=20, blank=True, null=True, db_index=True)
    new_code = models.CharField("新货号", max_length=20, blank=True, null=True, db_index=True)

    class Meta:
        verbose_name = "商品记录表"
        verbose_name_plural = verbose_name


class SkuRecord(models.Model):
    product_record = models.ForeignKey(ProductRecord, on_delete=models.CASCADE, blank=False, null=False, to_field="product_id", verbose_name="商品记录")
    sku_id = models.BigIntegerField("sku id", blank=True, null=True)
    old_spec_code = models.CharField("原商品编码", max_length=30, blank=True, null=True, db_index=True)
    new_spec_code = models.CharField("原商品编码", max_length=30, blank=True, null=True, db_index=True)

    class Meta:
        verbose_name = "SKU记录表"
        verbose_name_plural = verbose_name


class SubProductRecord(models.Model):
    old_parent_product_id = models.BigIntegerField("旧父商品id", blank=False, null=False)
    new_parent_product_id = models.BigIntegerField("新父商品id", blank=False, null=False)
    product_id = models.BigIntegerField("商品副本id", unique=True, blank=False, null=False)
    old_code = models.CharField("原货号", max_length=20, blank=True, null=True, db_index=True)
    new_code = models.CharField("新货号", max_length=20, blank=True, null=True, db_index=True)

    class Meta:
        verbose_name = "副本商品记录表"
        verbose_name_plural = verbose_name


class SubSkuRecord(models.Model):
    product_record = models.ForeignKey(SubProductRecord, on_delete=models.CASCADE, blank=False, null=False, to_field="product_id", verbose_name="副本商品记录表")
    sku_id = models.BigIntegerField("sku id", blank=True, null=True)
    old_spec_code = models.CharField("原商品编码", max_length=30, blank=True, null=True, db_index=True)
    new_spec_code = models.CharField("新商品编码", max_length=30, blank=True, null=True, db_index=True)

    class Meta:
        verbose_name = "副本SKU记录表"
        verbose_name_plural = verbose_name


class ProductCodeA2VRecord(models.Model):
    product_id = models.BigIntegerField("主商品id", unique=True, blank=False, null=False)
    old_code = models.CharField("原货号", max_length=20, blank=True, null=True, db_index=True)
    new_code = models.CharField("新货号", max_length=20, blank=True, null=True, db_index=True)

    class Meta:
        verbose_name = "商品货号A2V记录表"
        verbose_name_plural = verbose_name


class FailedUploadProductJST(models.Model):
    product_id = models.BigIntegerField("主商品id", blank=False, null=False)
    sub_product_id = models.BigIntegerField("副本id", blank=True, null=True)
    reason = models.TextField("失败原因")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    is_combine = models.BooleanField("是否为组合商品", default=False)

    class Meta:
        verbose_name = "上传聚水潭失败记录"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=["product_id", "sub_product_id"],
                name="unique_product_and_sub",
            ),
        ]


class ProductDyLabels(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, db_constraint=False, null=True, blank=True, verbose_name="商品")
    dy_product_id = models.CharField(verbose_name="抖店商品id", max_length=255, blank=True, null=True, default="")
    raw_info = models.JSONField(verbose_name="抖店用户画像原内容", null=True, blank=True, default=dict)
    info = models.JSONField(verbose_name="抖店用户画像处理后内容", null=True, blank=True, default=dict)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    update_user = models.CharField(verbose_name="最后更新者", default="", null=True, blank=True)

    class Meta:
        verbose_name = "抖店用户画像数据"
        verbose_name_plural = verbose_name

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        self.info = self.info_handler()

        super().save(force_insert, force_update, using, update_fields)

    def info_handler(self):
        try:
            re_data = []
            for i in self.raw_info:
                name = i["chart_name"]
                val = i["chart_lines"][0]["trends"]
                re_data.append({"name": name, "val": val})
            return re_data
        except Exception as e:
            print(e)
            logger.warning(f"处理画像内容错误:{e}")

            return None


class JSTShopProduct(models.Model):
    task_lock_name = "jst:shop_product_task"

    shop_name = models.CharField(verbose_name="店铺名称", max_length=255, blank=True, null=True, default="")
    dy_product_id = models.CharField(verbose_name="抖店商品id", max_length=255, blank=True, null=True, default="")
    dy_sku_id = models.CharField(verbose_name="抖店sku_id", max_length=255, blank=True, null=True, default="", unique=True, db_index=True)

    # 款式编码 erp_com_id
    code = models.CharField(verbose_name="款式编码(货号)", max_length=255, blank=True, null=True, default="")
    sku_spec_code = models.CharField(verbose_name="SKU商编", max_length=255, blank=True, null=True, default="")
    raw_sku_id = models.CharField(verbose_name="系统旧sku_id", max_length=255, blank=True, null=True, default="")
    #
    jst_product_name = models.CharField(verbose_name="聚水潭商品名称", max_length=255, blank=True, null=True, default="")
    jst_sku_name = models.CharField(verbose_name="聚水潭商品名称", max_length=255, blank=True, null=True, default="")
    dy_create_date = models.DateTimeField(verbose_name="抖店创建时间", null=True, blank=True)
    dy_update_date = models.DateTimeField(verbose_name="抖店更新时间", null=True, blank=True)
    jst_create_date = models.DateTimeField(verbose_name="聚水潭创建时间", null=True, blank=True)
    type_id = models.CharField(verbose_name="类目id", max_length=255, blank=True, null=True, default="")
    dy_price = models.DecimalField(verbose_name="官方售价", max_digits=10, decimal_places=4, blank=True, null=True)
    sale_price = models.CharField(verbose_name="售价", max_length=255, blank=True, null=True, default="")
    cost_price = models.CharField(verbose_name="成本价", max_length=255, blank=True, null=True, default="")
    dy_url = models.CharField(verbose_name="抖店链接", max_length=255, blank=True, null=True, default="")
    dy_pic = models.TextField(verbose_name="抖店首图", blank=True, default="")
    pic = models.TextField(verbose_name="商品首图", blank=True, default="")
    hash_code = models.CharField(verbose_name="唯一值", max_length=64, null=True, blank=True, default="")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    update_user = models.CharField(verbose_name="最后更新者", default="", null=True, blank=True)

    class Meta:
        verbose_name = "聚水潭店铺商品表"
        verbose_name_plural = verbose_name

    @classmethod
    def update_fields(cls):
        can_update_fields = [field.name for field in cls._meta.get_fields() if field.name not in ["dy_sku_id", "create_user", "create_date", "id", "pk"]]
        return can_update_fields


def shop_product_map_handler(post_data: dict) -> dict:
    """
    处理成跟models里面的字段一致
    :param post_data:
    :return:
    """
    re_data = {
        "shop_name": post_data.get("shop_name"),
        "dy_product_id": post_data.get("dy_com_id"),
        "dy_sku_id": post_data.get("dy_sku_id"),
        "raw_sku_id": post_data.get("raw_sku_id"),
        "code": post_data.get("erp_com_id"),
        "sku_spec_code": post_data.get("erp_sku_id"),
        "jst_product_name": post_data.get("com_name"),
        "jst_sku_name": post_data.get("sku_name"),
        "dy_create_date": post_data.get("dy_com_create_time"),
        "dy_update_date": post_data.get("dy_com_update_time"),
        "jst_create_date": post_data.get("com_create_time"),
        "type_id": post_data.get("type_id"),
        "dy_price": post_data.get("dy_price"),
        "sale_price": post_data.get("erp_sale_price"),
        "cost_price": post_data.get("erp_cost_price"),
        "dy_url": post_data.get("com_url"),
        "dy_pic": post_data.get("dy_pic"),
        "pic": post_data.get("erp_pic"),
    }
    hash_code = hashlib.md5(json.dumps(re_data, ensure_ascii=False).encode()).hexdigest()
    re_data["hash_code"] = hash_code
    re_data["create_time"] = post_data["create_time"]
    return re_data


class ProductSalesCount(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="商品", db_constraint=False)
    sales = models.IntegerField(verbose_name="销量", default=0, db_index=True)
    calc_date = models.DateField(verbose_name="日期", db_index=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    objects = CTEManager()

    class Meta:
        verbose_name = "商品每日销量统计"
        verbose_name_plural = verbose_name
        constraints = (
            models.UniqueConstraint(
                fields=["product", "calc_date"],
                name="unique_product_sales_date",
            ),
        )


class ProductAfterSalesCount(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="商品", db_constraint=False)
    count = models.IntegerField(verbose_name="退货量", default=0)
    calc_date = models.DateField(verbose_name="日期")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品每日售后数量统计"
        verbose_name_plural = verbose_name
        constraints = (
            models.UniqueConstraint(
                fields=["product", "calc_date"],
                name="unique_product_as_sales_date",
            ),
        )


class ProductDBSalesCount(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="商品", db_constraint=False)
    sales = models.IntegerField(verbose_name="销量", default=0)
    distributor = models.ForeignKey(
        Distributor,
        on_delete=models.CASCADE,
        verbose_name="关联分销商",
    )
    calc_date = models.DateField(verbose_name="日期")
    sales_amount = models.DecimalField("销售额", max_digits=20, decimal_places=2, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品分销商销量统计"
        verbose_name_plural = verbose_name

        constraints = (
            models.UniqueConstraint(
                fields=["product", "distributor", "calc_date"],
                name="unique_product_db_sales_date",
            ),
        )


class SKUSalesCount(models.Model):
    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE, db_constraint=False, verbose_name="商品sku")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="商品", db_constraint=False, default=None)
    sales = models.IntegerField(verbose_name="销量", default=0)
    calc_date = models.DateField(verbose_name="日期")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品SKU销量统计"
        verbose_name_plural = verbose_name
        constraints = (
            models.UniqueConstraint(
                fields=["sku", "product", "calc_date"],
                name="unique_sku_product_sales_date",
            ),
        )


class HandCard(models.Model):
    AS_SERVICE_CHOICES = (
        ("SR", "七天无理由退换货"),
        ("FI", "运费险"),
    )
    SHIP_TIME_CHOICES = (
        ("SG", "现货"),
        ("PS", "预售"),
    )
    product = models.OneToOneField(Product, on_delete=models.CASCADE, to_field="product_id", blank=False, null=False, verbose_name="商品")
    recommendations = models.CharField("推荐语", max_length=25, blank=True, null=True)
    core_selling_point = models.CharField("商品核心卖点", max_length=250, blank=False, null=False)
    remark = models.CharField("其他注意事项", max_length=250, blank=True, null=True)
    video = models.CharField("介绍视频", max_length=300, blank=True, null=True)
    aftersales_service = MultiSelectField("售后服务", choices=AS_SERVICE_CHOICES, max_length=5, blank=False, null=False)
    ship_time = MultiSelectField("发货时间", choices=SHIP_TIME_CHOICES, max_length=5, blank=False, null=False)
    spot_goods_time = models.CharField("现货具体时间", max_length=30, blank=True, null=True)
    pre_sale_time = models.CharField("预售具体时间", max_length=30, blank=True, null=True)
    logistics_company = models.CharField("物流公司", max_length=30, blank=False, null=False)
    shipping_list = models.CharField("发货清单", max_length=30, blank=False, null=False)
    qualification_information = models.CharField("资质信息", max_length=30, blank=True, null=True)

    class Meta:
        verbose_name = "手卡"
        verbose_name_plural = verbose_name


class ProductGift(models.Model):
    GIFT_TYPE_CHOICES = (
        ("AC", "配件"),
        ("PO", "商品"),
    )
    product = models.ForeignKey(Product, on_delete=models.CASCADE, to_field="product_id", blank=False, null=False, verbose_name="商品")
    gift_type = models.CharField("赠品类型", choices=GIFT_TYPE_CHOICES, max_length=2, blank=False, null=False)
    name = models.CharField("名称", max_length=15, blank=True, null=True)
    material = models.CharField("材质", max_length=15, blank=True, null=True)
    image = models.CharField("图片", max_length=300, blank=True, null=True)
    gift_product = models.ForeignKey(Product, on_delete=models.CASCADE, to_field="product_id", blank=True, null=True, related_name="gift_product", verbose_name="赠品商品")
    count = models.IntegerField("数量", default=1)
    is_deleted = models.BooleanField("是否删除", default=False)

    class Meta:
        verbose_name = "赠品"
        verbose_name_plural = verbose_name

        constraints = (
            models.UniqueConstraint(
                fields=["product", "gift_product"],
                condition=models.Q(is_deleted=False),
                name="product_gift_product_when_is_deleted_is_false",
            ),
        )


class ProductReviewedCostPrice(models.Model):
    """通过核价审核记录表,每次通过审核记录一次"""

    product = models.ForeignKey(Product, on_delete=models.CASCADE, db_constraint=False, to_field="product_id", null=True, blank=True)
    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE, db_constraint=False, to_field="sku_id", null=True, blank=True)
    old_cost_price = models.DecimalField("旧成本价", max_digits=10, decimal_places=2, null=True)
    cost_price = models.DecimalField("成本价", max_digits=10, decimal_places=2, null=True)
    create_user = models.CharField("审核的用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", null=True, blank=True)

    class Meta:
        verbose_name = "商品SKU审核成本价"
        verbose_name_plural = verbose_name


class CostItemConfigManager(models.Manager):
    def get_queryset(self):
        now = timezone.now()
        max_effective_time = super().get_queryset().filter(effective_time__lte=now, is_deleted=False).aggregate(Max("effective_time"))["effective_time__max"]

        return (
            super()
            .get_queryset()
            .filter(is_deleted=False)
            .annotate(
                status=Case(
                    When(effective_time__gt=now, then=Value("NE")),  # 未生效
                    When(effective_time=max_effective_time, then=Value("IE")),  # 生效中
                    default=Value("ED"),  # 已结束
                    output_field=models.CharField(),
                )
            )
            .order_by("-id")
        )


class CostItemConfig(BaseModel):
    STATUS_CHOICES = (
        ("NE", "未生效"),
        ("IE", "生效中"),
        ("ED", "已结束"),
    )
    PRICE_FETCHING_RULES_CHOICES = (
        ("FS", "无历史卖价, 取建议售价"),
        ("BP", "无建议售价和历史卖价时，取盈亏平衡价"),
    )
    name = models.CharField("配置名称", max_length=255)
    remark = models.TextField("备注", blank=True)
    effective_time = models.DateTimeField("生效时间")
    effective_immediately = models.BooleanField("立即生效", default=False)
    # status = models.CharField("状态", choices=STATUS_CHOICES, max_length=2, default="NE")
    price_fetching_rules = MultiSelectField("价格取数规则", choices=PRICE_FETCHING_RULES_CHOICES, max_length=5, blank=False, null=False)
    breakeven_price_rate = models.DecimalField("盈亏平衡价率", max_digits=10, decimal_places=2)
    objects = CostItemConfigManager()

    class Meta:
        verbose_name = "成本项配置"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=["effective_time"],
                name="unique_effective_time",
                condition=models.Q(is_deleted__exact=False),
            ),
        ]

    def __str__(self):
        return self.name


class CostSubItem(BaseModel):
    cost_item_config = models.ForeignKey(CostItemConfig, on_delete=models.CASCADE, related_name="cost_sub_items", verbose_name="成本项配置")
    start_selling_price = models.DecimalField("开始卖价", max_digits=10, decimal_places=2)
    end_selling_price = models.DecimalField("结束卖价", max_digits=10, decimal_places=2)
    logistics_fee = models.DecimalField("物流费", max_digits=10, decimal_places=2)
    quality_inspection_fee = models.DecimalField("质检费", max_digits=10, decimal_places=2)
    base_inspection_service_fee = models.DecimalField("基地质检服务费", max_digits=10, decimal_places=2)
    inspection_packing_fee = models.DecimalField("质检打包费", max_digits=10, decimal_places=2)
    reinspection_fee = models.DecimalField("复检费", max_digits=10, decimal_places=2)
    warehousing_operation_fee = models.DecimalField("仓储操作费", max_digits=10, decimal_places=2)
    outsourcing_material_fee = models.DecimalField("外包材料费", max_digits=10, decimal_places=2)
    freight_insurance = models.DecimalField("运费险", max_digits=10, decimal_places=2)
    packaging_box_fee = models.DecimalField("包装盒费", max_digits=10, decimal_places=2)
    management_cost = models.DecimalField("管理成本", max_digits=10, decimal_places=2)
    return_markup_fee = models.DecimalField("退货加乘费", max_digits=10, decimal_places=2)
    total_cost = models.DecimalField("合计费用", max_digits=10, decimal_places=2, blank=True, null=True)
    platform_service_fee_percentage = models.DecimalField("平台服务费", max_digits=5, decimal_places=2)
    return_fee_percentage = models.DecimalField("退货费用", max_digits=5, decimal_places=2)
    gift = models.DecimalField("赠品（链子等）", max_digits=10, decimal_places=2)
    vat_percentage = models.DecimalField("增值税", max_digits=5, decimal_places=2)
    variable_cost_rate_total = models.DecimalField("变动费用率合计", max_digits=5, decimal_places=2, blank=True, null=True)

    class Meta:
        verbose_name = "成本项配置子项"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.cost_item_config.name}({self.start_selling_price}-{self.end_selling_price})"

    def save(self, *args, **kwargs):
        # 计算合计费用和变动费用率合计
        self.total_cost = (
            self.logistics_fee
            + self.quality_inspection_fee
            + self.base_inspection_service_fee
            + self.inspection_packing_fee
            + self.reinspection_fee
            + self.warehousing_operation_fee
            + self.outsourcing_material_fee
            + self.freight_insurance
            + self.packaging_box_fee
            + self.management_cost
        ) * (1 + self.return_markup_fee)
        self.variable_cost_rate_total = self.platform_service_fee_percentage + self.return_fee_percentage + self.gift + self.vat_percentage
        super().save(*args, **kwargs)


class EstimatedAcceptanceRateRules(BaseModel):
    cost_item_config = models.ForeignKey(CostItemConfig, on_delete=models.CASCADE, related_name="estimated_acceptance_rate_rules", verbose_name="成本项配置")
    start_selling_price = models.DecimalField("开始卖价", max_digits=10, decimal_places=2)
    end_selling_price = models.DecimalField("结束卖价", max_digits=10, decimal_places=2)
    estimated_acceptance_rate = models.DecimalField("预估签收率", max_digits=10, decimal_places=2)

    class Meta:
        verbose_name = "预估签收率规则"
        verbose_name_plural = verbose_name


class DataCategory(models.Model):
    data_shop = models.ManyToManyField(DataShop, through="DataShopCategoryRelate", verbose_name="关联店铺")
    category_id = models.CharField(verbose_name="店铺分类ID", max_length=32)
    name = models.CharField(verbose_name="分类名称", max_length=32)
    level = models.CharField(verbose_name="分类层级", max_length=6)
    shop_parent_id = models.CharField(verbose_name="店铺分类ID", max_length=32)
    parent = models.ForeignKey("self", on_delete=models.CASCADE, db_constraint=False, verbose_name="父级类目", null=True, blank=True)
    is_leaf = models.BooleanField(verbose_name="是否为叶子类目", default=False)
    enable = models.BooleanField(verbose_name="是否启用", default=True)
    auth_required = models.BooleanField(verbose_name="是否要求品牌有授权", default=False)
    inited = models.BooleanField(verbose_name="是否已经初始化下级分类", default=False)
    config = models.JSONField(verbose_name="分类配置", null=True, default=dict)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "数据店铺分类"
        verbose_name_plural = verbose_name


class DataShopCategoryRelate(models.Model):
    datashop = models.ForeignKey(DataShop, on_delete=models.CASCADE, verbose_name="关联店铺")
    datacategory = models.ForeignKey(DataCategory, on_delete=models.CASCADE, verbose_name="关联分类")
    sub_inited = models.BooleanField(verbose_name="下级初始化", default=False)
    brand_inited = models.BooleanField("品牌初始化", default=False)
    property_inited = models.BooleanField("属性初始化", default=False)

    class Meta:
        verbose_name = "数据店铺分类关联"
        verbose_name_plural = verbose_name


class DataBrand(models.Model):
    category = models.ManyToManyField(DataCategory, through="DataCategoryBrand", verbose_name="所属店铺分类")
    brand_id = models.CharField(verbose_name="品牌ID", max_length=32, db_index=True)
    name_cn = models.CharField(verbose_name="品牌中文名称", max_length=32)
    name_en = models.CharField(verbose_name="品牌英文名称", max_length=32, null=True, blank=True, default="")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "店铺分类品牌"
        verbose_name_plural = verbose_name


class DataCategoryBrand(models.Model):
    brand = models.ForeignKey(DataBrand, on_delete=models.CASCADE, verbose_name="品牌")
    data_category = models.ForeignKey(DataCategory, on_delete=models.CASCADE, verbose_name="分类")
    is_authed = models.BooleanField(verbose_name="是否授权", default=False)

    class Meta:
        verbose_name = "店铺分类品牌关联"
        verbose_name_plural = verbose_name


class DataCateProperties(models.Model):
    category = models.ForeignKey(DataCategory, on_delete=models.CASCADE, db_constraint=False, verbose_name="所属店铺分类")
    # 1支持自定义 2不支持自定义
    diy_type = models.CharField(verbose_name="是否支持自定义", max_length=3, default="0", null=True, blank=True)
    important_type = models.SmallIntegerField(verbose_name="是否为重要属性", default=0, null=True, blank=True)
    measure_templates = ArrayField(base_field=models.JSONField(verbose_name="模版属性"), verbose_name="度量", null=True, blank=True, default=list)
    multi_select_max = models.SmallIntegerField(verbose_name="最大多选数", default=0)
    options = ArrayField(base_field=models.JSONField(verbose_name="属性可选值"), verbose_name="属性可选值列表", null=True, blank=True, default=list)
    property_id = models.CharField(verbose_name="属性ID", max_length=64, db_index=True)
    property_name = models.CharField(verbose_name="属性名称", max_length=100)
    # 属性类型，0 绑定属性 1关键属性 2售卖属性 3 商品属性
    property_type = models.CharField(verbose_name="属性类型", max_length=10)
    relation_id = models.CharField(verbose_name="关系ID", max_length=100)
    required = models.PositiveSmallIntegerField(verbose_name="是否选填", default=0)
    sequence = models.IntegerField(verbose_name="属性顺序", default=0)
    # 属性状态 0有效 1无效
    status = models.SmallIntegerField(verbose_name="属性状态", default=0)
    # 输入text、单选select、多选multi_select、时间戳timestamp、时间段timerange
    value_type = models.CharField(verbose_name="值类型", max_length=32)
    has_sub_property = models.BooleanField(verbose_name="是否有下级级联属性", default=False)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "店铺分类属性"
        verbose_name_plural = verbose_name
        constraints = (
            models.UniqueConstraint(
                fields=["category", "property_id"],
                name="unique_cate_property_id_idx",
            ),
        )


class DataShopMaterialFolder(models.Model):
    data_shop = models.ForeignKey(DataShop, on_delete=models.CASCADE, db_constraint=False, verbose_name="所属店铺")
    name = models.CharField(verbose_name="文件夹名称", max_length=32)
    folder_id = models.CharField(verbose_name="文件夹ID", max_length=255, unique=True, db_index=True)
    parent_folder_id = models.CharField(verbose_name="父文件夹ID", max_length=255, default="0", null=True, blank=True)
    folder_type = models.SmallIntegerField(
        verbose_name="文件夹类型",
        choices=(
            (0, "用户自建"),
            (1, "系统文件夹"),
        ),
        default=0,
    )

    # create_date = models.DateTimeField("创建时间", auto_now_add=True)
    # update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "店铺素材文件夹"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.name}({self.folder_id})"


def uuid_generator():
    return uuid.uuid4().hex


class DataShopUploadTasks(models.Model):
    data_shop = models.ForeignKey(DataShop, on_delete=models.CASCADE, db_constraint=False, verbose_name="所属店铺")
    TASK_TYPE_CHOICES = (
        ("ADD", "新增"),
        ("EDIT", "编辑"),
    )
    task_type = models.CharField("任务类型", choices=TASK_TYPE_CHOICES, default="ADD", max_length=12)
    task_id = models.CharField(verbose_name="任务ID", unique=True, max_length=255, default=uuid_generator)
    material_pass = models.BooleanField(verbose_name="素材是否全部通过", default=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, db_constraint=False, verbose_name="关联商品")
    sub_product = models.ForeignKey(SubProduct, on_delete=models.CASCADE, db_constraint=False, to_field="product_id", verbose_name="关联副商品")
    # 用户输入的商品名称
    product_name = models.CharField(verbose_name="商品名称", max_length=200)
    raw_category = models.JSONField("原商品分类列表", null=True, blank=True, default=dict)
    # 版本优化使用
    product_version = models.CharField(verbose_name="商品版本", max_length=10, default="1")
    sub_product_version = models.CharField(verbose_name="副本商品版本", max_length=10, default="1")
    cate_leaf_id = models.CharField(verbose_name="叶子分类ID", max_length=32)
    # 分类id列表,保留json，逻辑处理成列表返回
    category_id_list = models.JSONField(verbose_name="分类三级ID", default=dict)
    # 596120136默认是无品牌
    brand_id = models.CharField(verbose_name="品牌ID", max_length=128, default="596120136")
    presell_type = models.CharField(verbose_name="发货模式", max_length=12, default="0")
    delivery_delay_day = models.CharField(verbose_name="承诺发货时间", max_length=12, default="0")
    pre_sell_config = models.CharField(verbose_name="预售模式", max_length=128, default="2")
    reduce_type = models.CharField(verbose_name="减库存方式", max_length=128, default="1")
    freight_id = models.CharField(verbose_name="包邮方式", max_length=128, default="0")
    properties = models.JSONField(verbose_name="属性配置")
    extra = models.JSONField(verbose_name="额外配置", null=True, blank=True, default=dict)
    remark = models.TextField(verbose_name="备注信息", default="")
    post_data = models.JSONField(verbose_name="请求的数据", default=dict)
    rule = models.JSONField(verbose_name="发布规则", default=dict, null=True, blank=True)
    state = models.PositiveSmallIntegerField(
        verbose_name="状态",
        choices=(
            (0, "等待图片审核"),
            (1, "图片审核失败"),
            (2, "等待商品上传"),
            (4, "商品上传成功"),
            (5, "商品上传失败"),
            (6, "取消上传"),
            (7, "系统取消上传(已存在)"),
        ),
        default=0,
    )
    error_code = models.CharField(verbose_name="失败Code", max_length=255, null=True, blank=True, default="")
    error_reason = models.TextField(verbose_name="失败原因", max_length=255, null=True, blank=True, default="")
    final_date = models.DateTimeField("完成时间", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    cancel_user = models.CharField(verbose_name="取消人", default="", null=True, blank=True)
    objects = CTEManager()

    class Meta:
        verbose_name = "上传任务"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.task_id}"


class DataShopMaterial(models.Model):
    data_shop = models.ForeignKey(DataShop, on_delete=models.CASCADE, db_constraint=False, verbose_name="所属店铺")
    folder = models.ForeignKey(DataShopMaterialFolder, on_delete=models.CASCADE, db_constraint=False, to_field="folder_id", verbose_name="所在文件夹")
    material_type = models.SmallIntegerField(
        verbose_name="素材类型",
        choices=(
            (1, "图片"),
            (2, "视频"),
        ),
        default=1,
    )
    request_id = models.CharField(verbose_name="请求ID", unique=True, max_length=255, default=uuid_generator)
    task = models.ForeignKey(
        DataShopUploadTasks,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="task_id",
        verbose_name="所属任务",
    )
    # 素材状态，0-待下载；1-有效；4-回收站中；6-已删除； -1 未知
    material_state = models.SmallIntegerField(verbose_name="素材状态", default=-1)
    # 1-待审核 2-审核中 3-审核通过 4-审核拒绝
    audit_state = models.SmallIntegerField(
        verbose_name="审核状态",
        choices=(
            (1, "待审核"),
            (2, "审核中"),
            (3, "审核通过"),
            (4, "审核拒绝"),
        ),
        default=1,
    )
    byte_url = models.CharField(verbose_name="素材url", max_length=255, null=True, blank=True, default="")
    is_new = models.BooleanField(verbose_name="是否为新文件", default=False)
    material_id = models.CharField(verbose_name="素材ID", max_length=255, db_index=True, null=True, blank=True)
    name = models.CharField(verbose_name="素材名称", max_length=255)
    origin_url = models.CharField(verbose_name="素材url", max_length=255, null=True, blank=True, default="")
    size = models.IntegerField(verbose_name="文件大小(KB)", default=0)
    photo_info = models.JSONField(verbose_name="图片信息", null=True, blank=True, default=dict)
    video_info = models.JSONField(verbose_name="视频信息", null=True, blank=True, default=dict)
    create_time = models.DateTimeField(verbose_name="平台创建时间", null=True, blank=True)
    update_time = models.DateTimeField(verbose_name="平台更新时间", null=True, blank=True)
    delete_time = models.DateTimeField(verbose_name="平台删除时间", null=True, blank=True)
    error_code = models.CharField(verbose_name="失败Code", max_length=255, null=True, blank=True, default="")
    error_reason = models.TextField(verbose_name="失败原因", max_length=255, null=True, blank=True, default="")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "素材中心"
        verbose_name_plural = verbose_name


class DataShopProductMap(models.Model):
    task = models.ForeignKey(DataShopUploadTasks, on_delete=models.CASCADE, db_constraint=False, to_field="task_id", verbose_name="所属任务")
    # 商品映射
    data_shop = models.ForeignKey(DataShop, on_delete=models.CASCADE, db_constraint=False, verbose_name="所属店铺")
    platform_create_time = models.DateTimeField(verbose_name="平台创建时间", null=True, blank=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, to_field="product_id", db_constraint=False, verbose_name="所属商品")
    sub_product = models.ForeignKey(SubProduct, on_delete=models.CASCADE, to_field="product_id", db_constraint=False, verbose_name="所属副商品")
    outer_product_id = models.CharField(verbose_name="系统商品ID", max_length=255, null=True, blank=True, default="")
    platform_product_id = models.CharField(verbose_name="抖店商品ID", max_length=255, null=True, blank=True, default="")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "抖店店铺商品映射"
        verbose_name_plural = verbose_name


class DataShopSKU(models.Model):
    data_map = models.ForeignKey(DataShopProductMap, on_delete=models.CASCADE, db_constraint=False, verbose_name="所属映射")
    dy_spec_code = models.CharField(verbose_name="抖店商品编号", max_length=255)
    spec_code = models.CharField(verbose_name="处理后的商编", max_length=255, null=True, blank=True, default="")
    db_letters = models.CharField(verbose_name="分销商代码", max_length=255, null=True, blank=True, default="")
    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE, to_field="sku_id", db_constraint=False, verbose_name="关联SKU")
    platform_sku_id = models.CharField(verbose_name="抖店SKU_ID", max_length=255)
    outer_sku_id = models.CharField(verbose_name="系统SKU", max_length=255, null=True, blank=True, default="")
    spec_detail_id1 = models.CharField(verbose_name="子规格ID", max_length=255, null=True, blank=True, default="")
    spec_detail_id2 = models.CharField(verbose_name="子规格ID", max_length=255, null=True, blank=True, default="")
    spec_detail_id3 = models.CharField(verbose_name="子规格ID", max_length=255, null=True, blank=True, default="")
    spec_detail_ids = ArrayField(base_field=models.CharField(verbose_name="子规格ID", max_length=255, null=True, blank=True, default=""), verbose_name="子规格ID列表", default=list)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "抖店店铺商品SKU"
        verbose_name_plural = verbose_name


class DataShopProductDetail(models.Model):
    relate_product = models.ForeignKey(Product, on_delete=models.CASCADE, to_field="product_id", db_constraint=False, verbose_name="所属商品")
    sub_product = models.ForeignKey(SubProduct, on_delete=models.CASCADE, to_field="product_id", db_constraint=False, verbose_name="所属副商品")
    data_shop = models.ForeignKey(DataShop, on_delete=models.CASCADE, db_constraint=False, verbose_name="所属店铺")

    #
    account_template_id = models.CharField("多账号充值账号模板", max_length=50, null=True, blank=True, default="")
    after_sale_service = models.CharField("售后服务", null=True, blank=True, default="", max_length=255)
    after_sale_service_v2 = models.JSONField("售后服务新结构", null=True, blank=True, default=dict)
    appoint_delivery_day = models.IntegerField("可预约发货天数", null=True, blank=True, default=0)
    # brand_id = models.CharField(max_length=50, null=True, blank=True, default="")
    # car_vin_code = models.CharField(max_length=50, null=True, blank=True, default="")
    category_detail = models.JSONField("类目详情", null=True, blank=True, default=dict)
    cdf_category = models.CharField(max_length=50, blank=True, null=True, default="")
    check_status = models.IntegerField("海南免税，海关限购分类编码，仅海淘商品有值返回", null=True, blank=True, default=0)
    create_time = models.DateTimeField("商品创建时间", null=True, blank=True, default=None)
    delay_rule = models.JSONField("特殊日期延迟发货规则", null=True, blank=True, default=dict)
    # 现货模式的发货天数；阶梯模式现货部分的发货天数，9999=当日发、1=次日发
    delivery_delay_day = models.IntegerField("现货模式的发货天数", null=True, blank=True, default=0)
    # 承诺发货时间，单位是天; 不传则默认为2天，当presell_type为0或2均只支持传入2；当presell_type为1时支持可选值为: 2、3、5、7、10、15；
    delivery_method = models.IntegerField("承诺发货时间", null=True, blank=True, default=0)
    description = models.TextField("商品详情", null=True, blank=True, default="")
    # 售卖价 单位分 后台转回decimal
    discount_price = models.DecimalField("售卖价", max_digits=10, decimal_places=2, null=True, blank=True, default=0.0)
    # 草稿状态；0-无草稿,1-未提审,2-待审核,3-审核通过,4-审核未通过。详见：https://op.jinritemai.com/docs/question-docs/92/2070
    draft_status = models.IntegerField("草稿状态", null=True, blank=True, default=0)
    # 额外信息，如资质
    extra = models.TextField("额外信息", null=True, blank=True, default="")
    # format_update_time = models.DateTimeField(null=True, blank=True, default=None)
    # 运费模版id
    freight_id = models.CharField("运费模版ID", max_length=50, null=True, blank=True, default="")
    img = models.CharField("头图，主图第一张", null=True, blank=True, default="", max_length=255)
    is_auto_charge = models.BooleanField("是否是自动充值商品", null=True, blank=True, default=False)
    is_c2b_switch_on = models.BooleanField("是否支持c2b小程序", null=True, blank=True, default=False)
    # is_create = models.CharField(max_length=50, null=True, blank=True, default="")
    is_sub_product = models.BooleanField("是否是组合商品的子商品", null=True, blank=True, default=False)
    limit_per_buyer = models.IntegerField("单用户累计限购件数", null=True, blank=True, default=0)
    # 跨境物流信息（仅海淘商品返回）
    logistics_info = models.JSONField("跨境物流信息", null=True, blank=True, default=dict)
    long_pic_url = models.TextField("长图url", null=True, blank=True, default="")
    main_image_three_to_four = models.TextField("商品主图3:4；最多支持5张图片；宽高比例为3:4", null=True, blank=True, default="")
    # main_pic_3_4 = ArrayField(null=True, blank=True, default=dict)
    main_product_id = models.CharField("主商品ID", max_length=255, null=True, blank=True, default="")
    # 划线价 单位分
    market_price = models.DecimalField("划线价", max_digits=10, decimal_places=2, null=True, blank=True, default=0.0)
    material_video_id = models.CharField("主图视频ID", max_length=255, null=True, blank=True, default="")
    maximum_per_order = models.IntegerField("单用户下单限购件数", null=True, blank=True, default=0)
    # 支持c2b定制时的小程序id（特定c2b定制商家使用）
    micro_app_id = models.CharField("支持c2b定制时的小程序id", max_length=50, null=True, blank=True, default="")
    # 用户每次下单至少购买的件数
    minimum_per_order = models.IntegerField("用户每次下单至少购买的件数", null=True, blank=True, default=0)
    mobile = models.CharField(max_length=255, null=True, blank=True, default="")
    name = models.CharField("商品名称", max_length=255, null=True, blank=True, default="")
    name_prefix = models.CharField("系统推荐的标题前缀", max_length=255, null=True, blank=True, default="")
    name_suffix = models.CharField("标题后缀", max_length=255, null=True, blank=True, default="")
    need_recharge_mode = models.BooleanField("生活娱乐充值模式", null=True, blank=True, default=False)
    open_user_id = models.CharField(max_length=50, blank=True, null=True, default="")
    # 【即将废弃】外部商家编码，商家自定义字段。推荐使用，outer_product_id字段
    out_product_id = models.CharField("外部商家编码", max_length=255, null=True, blank=True, default="")
    outer_product_id = models.CharField("外部商家编码", max_length=50, null=True, blank=True, default="")
    pay_type = models.IntegerField(null=True, blank=True, default=0)
    pic = models.JSONField(verbose_name="商品主图；最多支持5张图片", null=True, blank=True, default=dict)
    # 提取方式新字段，推荐使用。"0": 普通商品-使用物流发货, "1": 虚拟商品-无需物流与电子交易凭证, "2": 虚拟商品-使用电子交易凭证, "3": 虚拟商品-充值直连
    pickup_method = models.IntegerField("提取方式", null=True, blank=True, default=0)
    # 卡券信息
    poi_resource = models.JSONField("卡券信息", null=True, blank=True, default=dict)
    # 发货模式：presell_type = 0 现货；presell_type = 2 阶梯；presell_type = 1 && presell_config_level = 0 全款预售；presell_type = 1 && presell_config_level = 1 sku预售；presell_type = 1 && presell_config_level = 2 现货+预售；presell_type = 1 && presell_config_level = 3 新预售
    presell_config_level = models.IntegerField("发货模式", null=True, blank=True, default=0)
    # 现货模式的发货天数；阶梯模式现货部分的发货天数，9999=当日发、1=次日发
    presell_delay = models.IntegerField("现货模式的发货天数", null=True, blank=True, default=0)
    # 预售发货方式配置 0-预售结束后xx天发货; 1-支付完成后xx天发货
    presell_delivery_type = models.IntegerField("预售发货方式配置", null=True, blank=True, default=0)
    # 预售结束时间
    presell_end_time = models.DateTimeField("预售结束时间", null=True, blank=True, default=None)
    # 预售类型，1-全款预售，0-非预售，2-阶梯库存
    presell_type = models.IntegerField("预售类型", null=True, blank=True, default=0)
    # 商品价格是否含税
    price_has_tax = models.BooleanField("商品价格是否含税", null=True, blank=True, default=False)
    # 商品审核结果
    product_audit_info = models.JSONField("商品审核结果", null=True, blank=True, default=dict)
    # product_format = models.JSONField(null=True, blank=True, default=dict)
    # 类目属性 返回是string
    product_format_new = models.JSONField("类目属性", null=True, blank=True, default=dict)
    # 商品ID，抖店系统生成，店铺下唯一；长度19位。
    product_id = models.CharField(max_length=255, null=True, blank=True, default="")
    # 商品类型，0-普通，3-虚拟，6玉石闪购，7云闪购
    product_type = models.IntegerField(null=True, blank=True, default=0)
    # 前置质检相关（特定二手商家、特定二手类目使用）
    quality_inspection_info = models.JSONField("前置质检相关", null=True, blank=True, default=dict)
    quality_list = models.JSONField("资质信息", null=True, blank=True, default=list)
    # recommend_remark = models.CharField(max_length=255, null=True, blank=True, default="")
    # 库存扣减方式，1-拍下减库存 2-付款减库存
    reduce_type = models.IntegerField("库存扣减方式", null=True, blank=True, default=0)
    # 参考价，返回单位分. 需转换
    reference_price = models.DecimalField("参考价", max_digits=10, decimal_places=2, null=True, blank=True, default=0.0)
    reference_price_certificate = models.JSONField("参考价凭证", null=True, blank=True, default=dict)
    # 销售渠道类型，包括纯电商（onlineOnly）、专柜同款（sameAsOffline），云零售商家（https://fxg.jinritemai.com/ffa/merchant-growth/cloud-retail）可以设置
    sale_channel_type = models.CharField("销售渠道类型", max_length=255, null=True, blank=True, default="")
    sale_limit_id = models.CharField("限售模板ID", max_length=255, null=True, blank=True, default="")
    # 售卖方式;0:全渠道手售卖,1:仅指定直播间售卖
    sell_channel = models.JSONField("售卖方式", null=True, blank=True, default=dict)
    sell_num = models.IntegerField("商品销量", null=True, blank=True, default=0)
    shop_category = models.JSONField("shop_category", null=True, blank=True, default=dict)
    short_product_name = models.CharField("导购短标题", max_length=255, null=True, blank=True, default="")
    size_info_template_id = models.CharField("尺码模板ID", max_length=255, null=True, blank=True, default="")
    spec_id = models.CharField("商品规格", max_length=255, null=True, blank=True, default="")
    spec_pics = models.JSONField("规格图片列表", null=True, blank=True, default=dict)
    spec_prices = models.JSONField("商品sku详情", null=True, blank=True, default=dict)
    specs = models.JSONField("规格信息", null=True, blank=True, default=dict)
    # spu_id 大于0代表有挂载spu
    spu_id = models.CharField("spu_id", max_length=255, null=True, blank=True, default="")
    standard_brand_id = models.CharField("品牌库BrandID", max_length=255, null=True, blank=True, default="")
    # 审核通过后上架售卖时间配置：0-立即上架售卖 1-放入仓库
    start_sale_type = models.IntegerField("审核通过后上架售卖时间配置", null=True, blank=True, default=0)
    # 商品在店铺中状态: 0-在线；1-下线；2-删除
    status = models.IntegerField("商品在店铺中状态", null=True, blank=True, default=0)
    # 门店ID
    store_id = models.CharField("门店ID", max_length=50, null=True, blank=True, default="")
    # 单位价格信息
    unit_price_info = models.JSONField("单位价格信息", null=True, blank=True, default=dict)
    update_time = models.DateTimeField("商品更新时间", null=True, blank=True, default=None)
    use_brand_name = models.BooleanField("是否勾选使用品牌名", null=True, blank=True, default=False)
    # 重量单位，0-kg, 1-g
    weight_unit = models.IntegerField("重量单位", null=True, blank=True, default=0)
    # 重量数值
    weight_value = models.FloatField("重量数值", null=True, blank=True, default=0)
    white_back_ground_pic_url = models.CharField("白底图url", null=True, blank=True, default="")

    class Meta:
        verbose_name = "抖店商品详情"
        verbose_name = verbose_name

    def __str__(self):
        return self.name


class DyProductList(models.Model):
    """抖店商品列表"""

    name_suffix = models.CharField("标题后缀", max_length=255, null=True, blank=True, default="")
    name_prefix = models.CharField("标题前缀", max_length=255, null=True, blank=True, default="")
    title_limit = models.JSONField("标题长度限制", null=True, blank=True, default=dict)
    sell_num = models.IntegerField("销量", null=True, blank=True, default=0)
    can_not_combine_reason = models.CharField("不可搭配的原因", max_length=255, null=True, blank=True, default="")
    shop_category = models.JSONField("店铺装修分类", null=True, blank=True, default=dict)
    product_id = models.BigIntegerField("商品ID,店铺下唯一")
    # https://op.jinritemai.com/docs/question-docs/92/2070
    status = models.IntegerField("商品在店铺中状态", default=0)
    # 商品审核状态: 1-未提交；2-待审核；3-审核通过；4-审核未通过；5-封禁；7-审核通过待上架；详见商品状态机：https://op.jinritemai.com/docs/question-docs/92/2070
    check_status = models.IntegerField("商品审核状态", default=0)
    img = models.CharField("抖店商品图片", max_length=255, null=True, blank=True, default="")
    name = models.CharField("抖店商品标题", max_length=255, null=True, blank=True, default="")
    code = models.CharField("根据标题提取的货号", max_length=12, null=True, blank=True, default="")
    # 商品类型；0-普通；1-新客商品；3-虚拟；6-玉石闪购；7-云闪购 ；127-其他类型；
    product_type = models.IntegerField("商品类型", null=True, blank=True, default=0)
    spec_id = models.BigIntegerField("商品规格")
    create_time = models.DateTimeField("抖店商品创建时间", null=True, blank=True)
    update_time = models.DateTimeField("抖店商品更新时间", null=True, blank=True)
    description = models.TextField("商品详情", null=True, blank=True, default="")
    extra = models.JSONField("额外信息", null=True, blank=True, default=dict)
    category_detail = models.JSONField("类目详情", null=True, blank=True, default=dict)
    # 即将废弃
    out_product_id = models.CharField("外部商家编码", null=True, blank=True, max_length=255, default="")
    outer_product_id = models.CharField("外部商家编码", null=True, blank=True, max_length=255, default="")

    # 售卖价 单位分 后台转回decimal
    discount_price = models.DecimalField("售卖价", max_digits=10, decimal_places=2, null=True, blank=True, default=0.0)
    # 划线价 单位分
    market_price = models.DecimalField("划线价", max_digits=10, decimal_places=2, null=True, blank=True, default=0.0)
    pay_type = models.IntegerField(null=True, blank=True, default=0)
    mobile = models.CharField(max_length=255, null=True, blank=True, default="")
    recommend_remark = models.CharField(max_length=255, null=True, blank=True, default="")
    is_package_product = models.BooleanField("是否为组合商品", default=False)
    package_product_list = models.JSONField("商品关联的组合主商品ID", null=True, blank=True, default=dict)
    sub_product_list = models.JSONField("商品关联的组合子商品ID", null=True, blank=True, default=dict)
    channel_main_product = models.JSONField("小时达子品绑定的主品信息", null=True, blank=True, default=dict)
    cursor_id = models.CharField("查询列表的cursor_id(首次为空)", null=True, blank=True, max_length=200, default="")
    spec_prices = models.JSONField("规格SKU信息", null=True, blank=True)
    # 关联系统数据
    data_shop = models.ForeignKey(
        DataShop,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="shop_id",
        null=True,
        blank=True,
        verbose_name="关联店铺",
    )
    relate_product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="product_id",
        null=True,
        blank=True,
        verbose_name="关联商品",
    )

    relate_sub_product = models.ForeignKey(
        SubProduct,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="product_id",
        null=True,
        blank=True,
        verbose_name="关联子商品",
    )
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "抖店商品列表"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=["data_shop", "product_id"],
                name="unique_data_shop_product_id_idx",
            ),
        ]


class DyProductRawData(models.Model):
    # 关联系统数据
    data_shop = models.ForeignKey(
        DataShop,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="shop_id",
        null=True,
        blank=True,
        verbose_name="关联店铺",
    )
    product_id = models.BigIntegerField("商品ID,店铺下唯一")
    raw_data = models.JSONField("元数据")

    class Meta:
        verbose_name = "抖店商品列表元数据"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                fields=["data_shop", "product_id"],
                name="unique_raw_data_shop_product_id_idx",
            ),
        ]


class SKUInventoryReviewRecord(models.Model):
    ACTION_TYPE_CHOICES = (
        ("increase", "增加"),
        ("decrease", "减少"),
    )

    COMPANY_ROLE_CHOICES = (
        ("SP", "供应商"),
        ("OP", "运营商"),
    )

    sku = models.ForeignKey(
        StockKeepingUnit,
        on_delete=models.CASCADE,
        to_field="sku_id",
        db_constraint=False,
        verbose_name="关联SKU",
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        to_field="product_id",
        db_constraint=False,
        verbose_name="所属商品",
    )
    # 由那一端申请的
    user_type = models.CharField("申请来源", choices=COMPANY_ROLE_CHOICES, max_length=12, default="SP")
    # 提交申请的库存
    apply_inventory = models.IntegerField("申请时候SKU的库存", default=0)
    # 审核记录的实际库存
    before_change_inventory = models.IntegerField("修改前库存", default=0)
    change_inventory = models.IntegerField("修改的库存", default=0)
    after_change_inventory = models.IntegerField("修改后库存", default=0)
    apply_safety_inventory = models.IntegerField("申请时候SKU的7天现货库存", default=0)
    after_safety_inventory = models.IntegerField("修改后7天现货库存", default=0)
    action_type = models.CharField("操作类型", choices=ACTION_TYPE_CHOICES, max_length=12, default="increase")
    create_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="user_id",
        verbose_name="创建者",
        related_name="create_user",
    )
    cancel_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="user_id",
        verbose_name="取消者",
        related_name="cancel_user",
    )
    review_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="user_id",
        verbose_name="审核人",
        related_name="review_user",
    )
    review_remark = models.TextField("审核备注信息", null=True, blank=True, default="")
    review_date = models.DateTimeField("审核时间", null=True, blank=True, default=None)
    state = models.PositiveSmallIntegerField(
        "审核状态",
        choices=(
            (0, "待审核"),
            (1, "通过"),
            (2, "未通过"),
        ),
        default=0,
    )
    remark = models.TextField("备注信息", null=True, blank=True, default="")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "库存修改审核记录"
        verbose_name_plural = verbose_name


class ProductStateReason(models.Model):
    name = models.CharField("理由名称", max_length=128)
    reason_type = models.PositiveSmallIntegerField(
        "理由类型",
        choices=(
            (1, "上架"),
            (2, "下架"),
            (3, "通用"),
        ),
        default=3,
    )
    enable = models.BooleanField("是否启用", default=True)

    class Meta:
        verbose_name = "上下架理由枚举管理"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.get_reason_type_display()}理由-{self.name}({self.pk})"


class ProductStateReviewRecord(models.Model):
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        to_field="product_id",
        db_constraint=False,
        verbose_name="所属商品",
    )
    review_type = models.PositiveSmallIntegerField(
        "上下架类型",
        choices=(
            (1, "上架"),
            (2, "下架"),
        ),
    )
    reason = models.ForeignKey(ProductStateReason, on_delete=models.PROTECT, db_constraint=False, verbose_name="上下架理由")
    remark = models.TextField("备注信息", null=True, blank=True, default="")
    review_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="user_id",
        verbose_name="审核人",
        related_name="review_user_set",
    )
    state = models.PositiveSmallIntegerField(
        "审核状态",
        choices=(
            (0, "待审核"),
            (1, "通过"),
            (2, "未通过"),
        ),
        default=0,
    )
    review_remark = models.TextField("审核备注信息", null=True, blank=True, default="")
    review_date = models.DateTimeField("审核时间", null=True, blank=True, default=None)
    #
    create_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="user_id",
        verbose_name="创建者",
        related_name="create_user_set",
    )
    cancel_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="user_id",
        verbose_name="取消者",
        related_name="cancel_user_set",
    )
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "上下架审核记录"
        verbose_name_plural = verbose_name


class ProductConfirmStateModelView(models.Model):
    product_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = "product_selection_confirm_state_view"


class ProductRefundRecord(models.Model):
    ## 退货
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="product_id",
        verbose_name="关联商品",
    )
    sku = models.ForeignKey(
        StockKeepingUnit,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="sku_id",
        verbose_name="关联sku",
    )
    spec_code = models.CharField(verbose_name="商品编码", max_length=128, db_index=True)
    record_date = models.DateField("退费日期", db_index=True)
    order_num = models.IntegerField("下单件数", default=0)
    refund_num = models.IntegerField("退单件数", default=0)
    shop_name = models.CharField("所属店铺名称", max_length=128)
    hash_value = models.CharField("唯一值", max_length=64, unique=True)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "退费统计记录表"
        verbose_name_plural = verbose_name


class ProductComments(models.Model):
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="product_id",
        verbose_name="关联商品",
    )
    sku = models.ForeignKey(
        StockKeepingUnit,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="sku_id",
        verbose_name="关联sku",
        null=True,
        blank=True,
    )
    spec_code = models.CharField(verbose_name="商品编码", max_length=128, db_index=True, null=True, blank=True)
    c_type = models.CharField(
        "评论类型",
        choices=(
            ("QA", "质检"),
            ("AS", "退费"),
        ),
        default="QA",
    )
    # 批次名称:
    #   质检 - 空值默认以评论为唯一键更新
    #   质检 - 货盘名称和评论为唯一键更新
    #   退费 - 批次名称为：日期-所属直播间 + 评论作为唯一 更新
    batch_name = models.CharField("批次名称", null=True, blank=True, default="", max_length=128)
    display = models.BooleanField("是否显示", default=True)
    display_range = MultiSelectField(
        "可见范围",
        choices=(
            ("OP", "运营商"),
            ("DB", "分销商"),
            ("SP", "供应商"),
        ),
        max_length=32,
        null=True,
        blank=True,
        default="",
    )
    comment = models.TextField("原因评论", default="")
    total_num = models.IntegerField("质检数量", default=0)
    unqualified_num = models.IntegerField("不合格数量", default=0)
    hash_value = models.CharField("唯一值", max_length=64, unique=True)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "商品问题表"
        verbose_name_plural = verbose_name


class TempProduct(models.Model):
    DATASOURCE_CHOICES = (
        ("DD", "抖店"),
        ("JST", "聚水潭"),
    )

    STATE_CHOICES = (
        (0, "待确认"),
        (1, "已合并"),
        (2, "已认领"),
    )
    uni_id = models.CharField("唯一id", max_length=64, unique=True, db_index=True)
    data_source = models.CharField("数据来源", max_length=12, choices=DATASOURCE_CHOICES, default="DD")
    product_id = models.CharField("抖店商品ID", max_length=255, db_index=True)
    state = models.PositiveSmallIntegerField("临时商品状态", choices=STATE_CHOICES, default=0, db_index=True)
    category = models.JSONField("抖店分类id列表", null=True, blank=True)
    name = models.CharField("商品名称", max_length=255)
    pic = models.CharField("抖店商品图片", max_length=300, blank=True, null=True)
    sku_id = models.CharField("原系统商品skuId", max_length=255, db_index=True)
    raw_spec_code = models.CharField("抖店原商品编码", max_length=33, blank=True, null=True)
    parsed_spec_code = models.CharField("商品编码", max_length=33, blank=True, null=True)
    sku_specs = models.JSONField("原系统规格信息", max_length=255, blank=True, null=True, default=dict)
    price = models.DecimalField("商品价格", max_digits=10, decimal_places=2, blank=True, null=True)
    shop_id = models.CharField("店铺ID", max_length=100, blank=True, null=True)
    shop_name = models.CharField("下单商户名称", max_length=255, blank=True, null=True)
    author_id = models.CharField("直播主播id（达人）", max_length=100, blank=True, null=True)
    author_name = models.CharField("直播主播名称", max_length=255, blank=True, null=True)
    distributor = models.ForeignKey(Distributor, on_delete=models.SET_NULL, blank=True, null=True, to_field="distributor_id", verbose_name="分销商")
    order_date = models.DateField("订单日期")
    remark = models.TextField("备注信息", null=True, blank=True, default="")
    create_date = models.DateTimeField("创建时间", auto_now_add=True, blank=True, null=True)
    update_date = models.DateTimeField("更新时间", auto_now=True, blank=True, null=True)

    class Meta:
        verbose_name = "临时商品"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"{self.raw_spec_code}({self.product_id})"


class TempProductDetail(models.Model):
    relate_product = models.ForeignKey(
        TempProduct,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联的临时商品",
        to_field="uni_id",
    )
    ex_order_id = models.CharField("外部订单号", max_length=100, blank=True, null=True, db_index=True)

    class Meta:
        verbose_name = "临时商品关联订单号"
        verbose_name_plural = verbose_name
        unique_together = (
            (
                "relate_product",
                "ex_order_id",
            ),
        )


class TempProductMap(models.Model):
    MAP_TYPE_CHOICES = (
        ("claim", "认领"),
        ("merge", "合并"),
    )
    map_type = models.CharField("关联类型", choices=MAP_TYPE_CHOICES)
    ex_product_id = models.CharField("外部商品ID", max_length=255, db_index=True)
    ex_sku_id = models.CharField("外部规格ID", max_length=255, db_index=True)
    sub_product = models.ForeignKey(SubProduct, on_delete=models.CASCADE, db_constraint=False, to_field="product_id", verbose_name="系统副本商品")
    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE, db_constraint=False, to_field="sku_id", verbose_name="系统规格ID")
    distributor = models.ForeignKey(Distributor, on_delete=models.CASCADE, db_constraint=False, to_field="distributor_id", verbose_name="所属分销商")
    spec_code = models.CharField("上传到抖店的商编", max_length=128, blank=True, null=True)
    uploaded = models.BooleanField("是否已经成功上传", default=False)
    shop_id = models.CharField("店铺id", max_length=64)
    errors = models.TextField("错误信息", null=True, blank=True)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "临时商品与系统商品映射"
        verbose_name_plural = verbose_name


class JSTShopSKUMap(models.Model):
    sku_id = models.CharField("珠凌商品SKU_ID", max_length=100, db_index=True)
    sku_spec_code = models.CharField("珠凌商品原商编", max_length=100, db_index=True)
    jst_sku_id = models.CharField("聚水潭商品编码", max_length=255, null=True, blank=True)
    jst_i_id = models.CharField("聚水潭款式编码", max_length=255, null=True, blank=True)
    jst_sku_code = models.CharField("国标码", max_length=255, null=True, blank=True)
    jst_shop_i_id = models.CharField("聚水潭店铺商品款式", max_length=255, null=True, blank=True)
    jst_shop_sku_id = models.CharField("聚水潭店铺商品编码", max_length=255, null=True, blank=True)
    jst_original_sku_id = models.CharField("原始商品编码", max_length=255, null=True, blank=True)
    jst_name = models.CharField("商品名称", max_length=255, null=True, blank=True)
    jst_shop_properties_value = models.CharField("店铺颜色规格", max_length=255, null=True, blank=True)
    # 商品标识(线上商品的特殊属性，比如零售，阶梯)，可更新，可为空字符串
    uploaded = models.BooleanField("是否上传成功", default=False)
    error = models.TextField("错误信息", null=True, blank=True, default="")
    jst_sku_sign = models.CharField("商品标识", max_length=255, null=True, blank=True)
    jst_shop_id = models.CharField("店铺ID", max_length=255, db_index=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "聚水潭店铺商品资料"
        verbose_name_plural = verbose_name


class SKUCostPriceReviewRecord(models.Model):
    ACTION_TYPE_CHOICES = (
        ("rise", "涨"),
        ("fall", "降"),
    )

    sku = models.ForeignKey(
        StockKeepingUnit,
        on_delete=models.CASCADE,
        to_field="sku_id",
        db_constraint=False,
        verbose_name="关联SKU",
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        to_field="product_id",
        db_constraint=False,
        verbose_name="所属商品",
    )
    batch_id = models.CharField("批量id", max_length=150, null=True, blank=True, default="")
    # 提交申请的推广价
    apply_cost_price = models.DecimalField("申请时候SKU的推广价", max_digits=10, decimal_places=2)
    apply_cost_price_max = models.DecimalField("申请时候SKU的推广价批量时的最大值", max_digits=10, decimal_places=2, default=0)
    # 审核记录的推广价
    after_change_cost_price = models.DecimalField("修改后的价格", max_digits=10, decimal_places=2)
    apply_remark = models.CharField("请求备注", max_length=150, null=True, blank=True, default="")
    apply_image = models.CharField("申请凭证", max_length=200, null=True, blank=True, default="")
    action_type = models.CharField("操作类型", choices=ACTION_TYPE_CHOICES, max_length=12, default="increase")
    create_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="user_id",
        verbose_name="创建者",
        related_name="cost_price_change_create_user",
    )
    cancel_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="user_id",
        verbose_name="取消者",
        related_name="cost_price_change_cancel_user",
    )
    review_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="user_id",
        verbose_name="审核人",
        related_name="cost_price_change_review_user",
    )
    review_remark = models.TextField("审核备注信息", null=True, blank=True, default="")
    review_date = models.DateTimeField("审核时间", null=True, blank=True, default=None)
    state = models.PositiveSmallIntegerField(
        "审核状态",
        choices=(
            (0, "待审核"),
            (1, "通过"),
            (2, "未通过"),
            (3, "取消"),  # 与三级审核冲突，进行取消操作
        ),
        default=0,
    )
    remark = models.TextField("备注信息", null=True, blank=True, default="")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "推广价修改审核记录"
        verbose_name_plural = verbose_name


class BatchSkuCostPriceRelate(models.Model):
    batch_id = models.CharField("批量id", max_length=150, null=True, blank=True, default="")
    sku = models.ForeignKey(
        StockKeepingUnit,
        on_delete=models.CASCADE,
        to_field="sku_id",
        db_constraint=False,
        verbose_name="关联SKU",
    )


class SKUDistributorSalesCount(models.Model):
    ## 暂不使用，需要统计到第二天4点的订单数据, 直接在item_sku记录数据
    # 任务写在了orders.models

    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE, db_constraint=False, verbose_name="关联sku")
    distributor = models.ForeignKey(Distributor, on_delete=models.CASCADE, db_constraint=False, verbose_name="关联分销商")
    sales_count = models.IntegerField("销量", default=0)
    calc_date = models.DateField(verbose_name="订单日期", db_index=True)
    order_count = models.IntegerField("订单数量", default=0)
    sales_amount = models.DecimalField("订单总金额", max_digits=20, decimal_places=2, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "SKU分销商每日销量"
        verbose_name_plural = verbose_name
        constraints = (
            models.UniqueConstraint(
                fields=["sku", "calc_date", "distributor"],
                name="unique_sku_db_sales_date_calc",
            ),
        )


class FrontProductCategoryList(models.Model):
    """
    商品类别, 三级分类
    """

    id = models.BigAutoField("类别id", primary_key=True)
    name = models.CharField("类别名称", max_length=28)
    parent = models.ForeignKey("self", on_delete=models.CASCADE, related_name="subs", null=True, blank=True, verbose_name="父级类别")
    level = models.PositiveSmallIntegerField(verbose_name="层级", default=1)
    is_big_image = models.BooleanField(verbose_name="是否大图", default=False)
    image_url = models.CharField(verbose_name="图片地址", max_length=200, default="")
    order = models.PositiveSmallIntegerField(verbose_name="排序", help_text="数字越大排越前", default=0)
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_user = models.CharField(verbose_name="最后更新人", default="", null=True, blank=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)
    back_category = ArrayField(
        ArrayField(models.CharField("商品分类id", max_length=32, blank=True, null=True, default=""), size=3, blank=False, null=False, verbose_name="分类列表", default=list),
        size=30,
        blank=False,
        null=False,
        verbose_name="多个关联分类",
        default=list,
    )
    style = ArrayField(models.CharField("款式", max_length=20, default=None), size=30, blank=True, null=True, default=list, verbose_name="款式")

    class Meta:
        verbose_name = "前台商品类别"
        verbose_name_plural = verbose_name
        ordering = (
            "-order",
            "id",
        )

    def __str__(self):
        return f"{self.name}({self.id})"


class GoodProductNote(models.Model):
    """
    好物笔记
    """

    note_id = models.BigIntegerField("笔记id", unique=True, blank=False, null=False, default=get_random)
    images = ArrayField(models.CharField("好物分享图片", max_length=300, default=None), size=10, blank=False, null=False, verbose_name="好物分享图片列表")
    video = ArrayField(models.CharField("好物分享视频", max_length=300, blank=True, null=True), size=10, blank=False, null=False, verbose_name="好物分享视频列表")
    content = models.TextField("分享内容", blank=True, null=True)
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        to_field="product_id",
        db_constraint=False,
        verbose_name="所属商品",
    )
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "好物种草笔记"
        verbose_name_plural = verbose_name
        ordering = (
            "-create_date",
            "id",
        )

    def __str__(self):
        return f"{self.note_id}({self.content})"


class GoodProductNoteComment(models.Model):
    """
    好物笔记评论表
    """

    comment_id = models.BigIntegerField("评论id", unique=True, blank=False, null=False, default=get_random)
    content = models.TextField("评论内容", blank=True, null=True)
    note = models.ForeignKey(
        GoodProductNote,
        on_delete=models.CASCADE,
        to_field="note_id",
        db_constraint=False,
        verbose_name="所属好物笔记",
    )
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "好物种草笔记评论"
        verbose_name_plural = verbose_name
        ordering = (
            "-create_date",
            "id",
        )

    def __str__(self):
        return f"{self.comment_id}({self.content})"


class GoodProductNoteGiveLike(models.Model):
    """
    好物分享点赞表
    """

    GIVE_LIKE_TYPE_CHOICES = (
        ("PT", "商品"),
        ("NT", "笔记"),
        ("CT", "评论"),
    )

    related_id = models.BigIntegerField("关联id", blank=False, null=False)
    give_like_type = models.CharField("点赞类型", choices=GIVE_LIKE_TYPE_CHOICES, max_length=10, blank=True, null=True, default="PT")
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "好物种草以及评论点赞"
        verbose_name_plural = verbose_name
        ordering = (
            "-create_date",
            "id",
        )


class ProductSalesRankHistory(models.Model):
    # 往前推30天计算的销量
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="商品", db_constraint=False)
    sales = models.IntegerField(verbose_name="销量", default=0, db_index=True)
    rank = models.IntegerField(verbose_name="排位")
    calc_date = models.DateField(verbose_name="统计日期", db_index=True)

    class Meta:
        verbose_name = "销量统计历史排行榜"
        verbose_name_plural = verbose_name
        constraints = (
            models.UniqueConstraint(
                fields=["product", "calc_date"],
                name="unique_product_sales_history_date",
            ),
        )


class ProductCategorySalesRankHistory(models.Model):
    # 往前推30天计算的分类销量
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="商品", db_constraint=False)
    category_id = models.IntegerField(verbose_name="分类ID", db_index=True)
    sales = models.IntegerField(verbose_name="销量", default=0, db_index=True)
    rank = models.IntegerField(verbose_name="排位")
    calc_date = models.DateField(verbose_name="统计日期", db_index=True)

    class Meta:
        verbose_name = "分类销量统计历史排行榜"
        verbose_name_plural = verbose_name
        constraints = (
            models.UniqueConstraint(
                fields=["product", "category_id", "calc_date"],
                name="unique_product_category_sales_history_date",
            ),
        )


class ProductRefundRateHistory(models.Model):
    product_id = models.CharField("商品id", null=False, blank=False, db_index=True, max_length=64)
    rate = models.FloatField("退费率", default=0)
    calc_date = models.DateField("统计的日期")
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        unique_together = ("product_id", "calc_date")
        verbose_name = "商品退费率"
        verbose_name_plural = verbose_name
        permissions = (
            (
                "view_rate_ProductRefundRateHistory",
                "查看商品退费率",
            ),
        )


class UserWishList(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, db_constraint=False, to_field="product_id", verbose_name="关联商品")
    user = models.ForeignKey(User, on_delete=models.CASCADE, db_constraint=False, to_field="user_id", verbose_name="关联用户")
    wish_type = models.PositiveSmallIntegerField(
        "数据来源",
        choices=(
            (1, "臻品-用户想要"),
            (2, "分销-用户预购"),
        ),
        default=1,
    )
    number = models.IntegerField("预购数量", default=1)
    distributor = models.ForeignKey(
        Distributor,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="distributor_id",
        null=True,
        blank=True,
        default=None,
        verbose_name="关联分销商",
    )
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "用户想要商品"
        verbose_name_plural = verbose_name


class DistributorMarketProductPriceConfig(BaseModel):
    min_cost_price = models.DecimalField("最小推广价", max_digits=10, decimal_places=2, default=0)
    max_cost_price = models.DecimalField("最大推广价", max_digits=10, decimal_places=2, default=0)
    min_purchase_num = models.PositiveIntegerField("最小起购数量", default=0)
    max_purchase_num = models.PositiveIntegerField("最大起购数量", default=0)
    markup_percentage = models.PositiveIntegerField("加价百分比", default=0)

    class Meta:
        verbose_name = "分销市场价格配置"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"[{self.min_cost_price}~{self.max_cost_price}](ID:{self.pk})"


class SelectionPlanLabelMark(models.Model):
    name = models.CharField("标签名称", max_length=12, unique=True)
    mark_type = models.PositiveSmallIntegerField("标记类型", choices=((1, "流量标记"), (2, "业务标记")), default=1)
    order = models.PositiveSmallIntegerField("排序", default=0, help_text="从小到大排序")
    display = models.BooleanField("是否显示", default=True)

    class Meta:
        verbose_name = "货盘商品标记配置项"
        verbose_name_plural = verbose_name
        ordering = ("order",)

    def __str__(self):
        return self.name

    @staticmethod
    def get_max_connection_map():
        max_connection_map = {1: 1, 2: 2}
        mark_max_select_key = "mark_max_selections"
        val = redis_cache.get(mark_max_select_key)
        if val:
            max_connection_map = val
        return max_connection_map


class SelectionPlanLabelMarkRelate(models.Model):
    mark = models.ForeignKey(
        SelectionPlanLabelMark,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="关联标记",
    )
    distributor = models.ForeignKey(
        Distributor,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="distributor_id",
        verbose_name="关联分销商",
    )

    class Meta:
        verbose_name = "货盘标记分销商关联"
        verbose_name_plural = verbose_name


class SimilarProductRelate(models.Model):
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="product_id",
        verbose_name="珠凌商品",
    )

    external_product_id = models.CharField(verbose_name="外部商品ID", null=False, blank=False, max_length=128)
    is_deleted = models.BooleanField(verbose_name="是否已删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_user = models.CharField(verbose_name="最后更新者", default="", null=True, blank=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "相似品"
        verbose_name_plural = verbose_name


class DyProductMap(models.Model):
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="product_id",
        verbose_name="主商品",
    )
    sub_product = models.ForeignKey(
        SubProduct,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="product_id",
        verbose_name="副本商品",
        null=True,
        blank=True,
    )
    sku = models.ForeignKey(StockKeepingUnit, on_delete=models.CASCADE, db_constraint=False, verbose_name="关联sku")
    dy_sku_id = models.CharField("抖店SKU_ID", max_length=128, db_index=True)
    shop_id = models.CharField("抖店SKU_ID", max_length=128, db_index=True)
    dy_product_id = models.CharField("抖店商品ID", max_length=128, db_index=True)

    class Meta:
        verbose_name = "珠凌-抖店商品映射"
        verbose_name_plural = verbose_name
        # 所有字段整表唯一
        unique_together = (
            (
                "product",
                "sub_product",
                "sku",
                "dy_sku_id",
                "shop_id",
                "dy_product_id",
            ),
        )


class ProductsProductLabelsAll(models.Model):
    id = models.BigAutoField(primary_key=True)
    product_id = models.CharField(max_length=255, blank=True, null=True, db_comment="商品id")
    product_name = models.CharField(max_length=255, blank=True, null=True, db_comment="商品名称")
    i_id = models.CharField(max_length=255, blank=True, null=True, db_comment="商品货号")
    cost_price = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="成本价")
    company_name = models.CharField(max_length=255, blank=True, null=True, db_comment="供应商名称")
    size = models.CharField(max_length=255, blank=True, null=True, db_comment="商品尺寸")
    sys_category = models.CharField(max_length=255, blank=True, null=True, db_comment="系统商品分类")
    category = models.CharField(max_length=255, blank=True, null=True, db_comment="商品分类")
    company_addr = models.CharField(max_length=255, blank=True, null=True, db_comment="供应商品地址")
    style = models.CharField(max_length=255, blank=True, null=True, db_comment="款式")
    place = models.CharField(max_length=255, blank=True, null=True, db_comment="商品产地")
    beset = models.CharField(max_length=255, blank=True, null=True, db_comment="镶嵌材料")
    is_beset = models.CharField(max_length=255, blank=True, null=True, db_comment="是否镶嵌")
    avg_price_7 = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="近7天平均售价")
    avg_price_30 = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="近30天平均售价")
    avg_price_60 = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="近60天平均售价")
    avg_price_90 = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="近90天平均售价")
    avg_price_180 = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="近180天平均售价")
    avg_price_365 = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="近365天平均售价")
    sum_paid_qty_7 = models.BigIntegerField(blank=True, null=True, db_comment="近7天商品累计销量")
    sum_paid_qty_30 = models.BigIntegerField(blank=True, null=True, db_comment="近30天商品累计销量")
    sum_paid_qty_60 = models.BigIntegerField(blank=True, null=True, db_comment="近60天商品累计销量")
    sum_paid_qty_90 = models.BigIntegerField(blank=True, null=True, db_comment="近90天商品累计销量")
    sum_paid_qty_180 = models.BigIntegerField(blank=True, null=True, db_comment="近180天商品累计销量")
    sum_paid_qty_365 = models.BigIntegerField(blank=True, null=True, db_comment="近365天商品累计销量")
    sum_paid_amt_7 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近7天累计支付金额")
    sum_paid_amt_30 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近30天累计支付金额")
    sum_paid_amt_60 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近60天累计支付金额")
    sum_paid_amt_90 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近90天累计支付金额")
    sum_paid_amt_180 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近180天累计支付金额")
    sum_paid_amt_365 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近365天累计支付金额")
    max_online_users = models.BigIntegerField(blank=True, null=True, db_comment="最近一场在线人数")
    cnt_openid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场下单人数")
    cnt_pay_openid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场支付人数")
    cnt_end_openid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场签收人数")
    cnt_refund_openid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场退款人数")
    cnt_bef_send_refund_openid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场发货前退款人数")
    cnt_af_send_refund_openid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场发货后退款人数")
    cnt_af_end_refund_openid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场签收后退货人数")
    cnt_qt_refund_openid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场品退人数")
    cnt_click_openid = models.SmallIntegerField(blank=True, null=True, db_comment="最近一场商品点击人数")
    cnt_value_click_rate = models.SmallIntegerField(blank=True, null=True, db_comment="最近一场商品曝光-点击率（人数）")
    cnt_click_pay_rate = models.SmallIntegerField(blank=True, null=True, db_comment="最近一场商品点击-成交转化率（人数）")
    sum_qty = models.BigIntegerField(blank=True, null=True, db_comment="最近一场已下单商品数量")
    sum_pay_qty = models.BigIntegerField(blank=True, null=True, db_comment="最近一场已支付商品数量")
    sum_send_qty = models.BigIntegerField(blank=True, null=True, db_comment="最近一场已发货商品数量")
    sum_end_qty = models.BigIntegerField(blank=True, null=True, db_comment="最近一场已签收商品数量")
    sum_bef_send_refund_qty = models.BigIntegerField(blank=True, null=True, db_comment="最近一场发货前退货商品数量")
    sum_af_send_refund_qty = models.BigIntegerField(blank=True, null=True, db_comment="最近一场发货后退货商品数量")
    sum_af_end_refund_qty = models.BigIntegerField(blank=True, null=True, db_comment="最近一场签收后退货商品数量")
    sum_qt_refund_qty = models.BigIntegerField(blank=True, null=True, db_comment="最近一场品退商品数量")
    sum_introduce = models.BigIntegerField(blank=True, null=True, db_comment="最近一场讲解时长")
    cnt_introduce = models.BigIntegerField(blank=True, null=True, db_comment="最近一场讲解次数")
    sale_100_time = models.CharField(max_length=255, blank=True, null=True, db_comment="最近一场100单耗时")
    sale_300_time = models.CharField(max_length=255, blank=True, null=True, db_comment="最近一场300单耗时")
    sale_500_time = models.CharField(max_length=255, blank=True, null=True, db_comment="最近一场500单耗时")
    sale_1000_time = models.CharField(max_length=255, blank=True, null=True, db_comment="最近一场1000单耗时")
    sale_2000_time = models.CharField(max_length=255, blank=True, null=True, db_comment="最近一场2000单耗时")
    cnt_soid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场已创建订单数量")
    cnt_pay_soid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场已支付订单数量")
    cnt_send_soid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场已发货订单数量")
    cnt_end_soid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场已签收订单数量")
    cnt_qt_refund_soid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场品退订单数量")
    cnt_ps_soid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场预售订单数")
    cnt_bef_send_refund_soid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场发货前退款订单数")
    cnt_af_send_refund_soid = models.BigIntegerField(blank=True, null=True, db_comment="最近一场发货后退款订单数")
    rate_bef_send_refund_soid = models.FloatField(blank=True, null=True, db_comment="最近一场发货前订单退款率")
    rate_af_send_refund_soid = models.FloatField(blank=True, null=True, db_comment="最近一场发货后订单退款率")
    price = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="最近一场售价")
    profit = models.FloatField(blank=True, null=True, db_comment="最近一场毛利")
    avg_paid_openid = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="最近一场客单价")
    live_cost_price = models.TextField(blank=True, null=True, db_comment="成本价")
    price_rate = models.FloatField(blank=True, null=True, db_comment="定价比")
    sum_paid_amount = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="支付金额")
    sum_paid_end_amount = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="签收金额")
    sum_paid_refund_amount = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="退货金额")
    tet_amount = models.SmallIntegerField(blank=True, null=True, db_comment="千次曝光成交金额")
    sum_bef_send_refund_amount = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="发货前退款金额")
    sum_af_send_refund_amount = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="发货后退款金额")
    sum_af_end_refund_amount = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="签收后退款金额")
    max_state_sum_qty = models.TextField(blank=True, null=True, db_comment="购买商品数量最多的省份")
    max_state_sum_pay_qty = models.TextField(blank=True, null=True, db_comment="支付商品数量最多的省份")
    max_state_sum_end_qty = models.TextField(blank=True, null=True, db_comment="签收商品数量最多的省份")
    max_state_sum_refund_qty = models.TextField(blank=True, null=True, db_comment="退货商品数量最多的省份")
    max_state_sum_qt_refund_qty = models.TextField(blank=True, null=True, db_comment="品退商品数量最多的省份")
    max_state_cnt_openid = models.TextField(blank=True, null=True, db_comment="购买人数最多的省份")
    max_state_cnt_pay_openid = models.TextField(blank=True, null=True, db_comment="支付人数最多的省份")
    max_state_cnt_end_openid = models.TextField(blank=True, null=True, db_comment="签收人数最多的省份")
    max_state_cnt_refund_openid = models.TextField(blank=True, null=True, db_comment="退货人数最多的省份")
    max_state_cnt_qt_refund_openid = models.TextField(blank=True, null=True, db_comment="品退人数最多的省份")
    max_city_sum_qty = models.TextField(blank=True, null=True, db_comment="购买商品数量最多的城市")
    max_city_sum_pay_qty = models.TextField(blank=True, null=True, db_comment="支付商品数量最多的城市")
    max_city_sum_end_qty = models.TextField(blank=True, null=True, db_comment="签收商品数量最多的城市")
    max_city_sum_refund_qty = models.TextField(blank=True, null=True, db_comment="退货商品数量最多的城市")
    max_city_sum_qt_refund_qty = models.TextField(blank=True, null=True, db_comment="品退商品数量最多的城市")
    max_city_cnt_openid = models.TextField(blank=True, null=True, db_comment="购买人数最多的城市")
    max_city_cnt_pay_openid = models.TextField(blank=True, null=True, db_comment="支付人数最多的城市")
    max_city_cnt_end_openid = models.TextField(blank=True, null=True, db_comment="签收人数最多的城市")
    max_city_cnt_refund_openid = models.TextField(blank=True, null=True, db_comment="退货人数最多的城市")
    max_city_cnt_qt_refund_openid = models.TextField(blank=True, null=True, db_comment="品退人数最多的城市")
    avg_max_online_users_month = models.FloatField(blank=True, null=True, db_comment="月内场均在线人数")
    avg_cnt_openid_month = models.FloatField(blank=True, null=True, db_comment="月内场均下单人数")
    avg_cnt_pay_openid_month = models.FloatField(blank=True, null=True, db_comment="月内场均支付人数")
    avg_cnt_end_openid_month = models.FloatField(blank=True, null=True, db_comment="月内场均签收人数")
    avg_cnt_refund_openid_month = models.FloatField(blank=True, null=True, db_comment="月内场均退款人数")
    avg_cnt_bef_send_refund_openid_month = models.FloatField(blank=True, null=True, db_comment="月内场均发货前退款人数")
    avg_cnt_af_send_refund_openid_month = models.FloatField(blank=True, null=True, db_comment="月内场均发货后退款人数")
    avg_cnt_af_end_refund_openid_month = models.FloatField(blank=True, null=True, db_comment="月内场均签收后退货人数")
    avg_cnt_qt_refund_openid_month = models.FloatField(blank=True, null=True, db_comment="月内场均品退人数")
    avg_cnt_click_openid_month = models.FloatField(blank=True, null=True, db_comment="月内场均商品点击人数")
    avg_cnt_value_click_rate_month = models.FloatField(blank=True, null=True, db_comment="月内场均商品曝光-点击率（人数）")
    avg_cnt_click_pay_rate_month = models.FloatField(blank=True, null=True, db_comment="月内场均商品点击-成交转化率（人数）")
    avg_sum_qty_month = models.FloatField(blank=True, null=True, db_comment="月内场均已下单商品数量")
    avg_sum_pay_qty_month = models.FloatField(blank=True, null=True, db_comment="月内场均已支付商品数量")
    avg_sum_send_qty_month = models.FloatField(blank=True, null=True, db_comment="月内场均已发货商品数量")
    avg_sum_end_qty_month = models.FloatField(blank=True, null=True, db_comment="月内场均已签收商品数量")
    avg_sum_bef_send_refund_qty_month = models.FloatField(blank=True, null=True, db_comment="月内场均发货前退货商品数量")
    avg_sum_af_send_refund_qty_month = models.FloatField(blank=True, null=True, db_comment="月内场均发货后退货商品数量")
    avg_sum_af_end_refund_qty_month = models.FloatField(blank=True, null=True, db_comment="月内场均签收后退货商品数量")
    avg_sum_qt_refund_qty_month = models.FloatField(blank=True, null=True, db_comment="月内场均品退商品数量")
    avg_sum_introduce_month = models.FloatField(blank=True, null=True, db_comment="月内场均讲解时长")
    avg_cnt_introduce_month = models.FloatField(blank=True, null=True, db_comment="月内场均讲解次数")
    min_sale_100_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场100单最小耗时")
    min_sale_300_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场300单最小耗时")
    min_sale_500_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场500单最小耗时")
    min_sale_1000_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场1000单最小耗时")
    min_sale_2000_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场2000单最小耗时")
    max_sale_100_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场100单最大耗时")
    max_sale_300_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场300单最大耗时")
    max_sale_500_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场500单最大耗时")
    max_sale_1000_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场1000单最大耗时")
    max_sale_2000_time_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场2000单最大耗时")
    avg_cnt_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均已创建订单数量")
    avg_cnt_pay_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均已支付订单数量")
    avg_cnt_send_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均已发货订单数量")
    avg_cnt_end_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均已签收订单数量")
    avg_cnt_qt_refund_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均品退订单数量")
    avg_cnt_ps_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均预售订单数")
    avg_cnt_bef_send_refund_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均发货前退款订单数")
    avg_cnt_af_send_refund_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均发货后退款订单数")
    avg_rate_bef_send_refund_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均发货前订单退款率")
    avg_rate_af_send_refund_soid_month = models.FloatField(blank=True, null=True, db_comment="月内场均发货后订单退款率")
    min_price_month = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="月内单场最小售价")
    min_profit_month = models.FloatField(blank=True, null=True, db_comment="月内单场最小毛利")
    min_cost_price_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场最小成本价")
    min_price_rate_month = models.FloatField(blank=True, null=True, db_comment="月内单场最小定价比")
    min_avg_paid_openid_month = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="月内单场最小客单价")
    max_price_month = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="月内单场最大售价")
    max_profit_month = models.FloatField(blank=True, null=True, db_comment="月内单场最大毛利")
    max_cost_price_month = models.CharField(max_length=255, blank=True, null=True, db_comment="月内单场最大成本价")
    max_price_rate_month = models.FloatField(blank=True, null=True, db_comment="月内单场最大定价比")
    max_avg_paid_openid_month = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="月内单场最大客单价")
    avg_sum_paid_amount_month = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="月内场均支付金额")
    avg_sum_paid_end_amount_month = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="月内场均签收金额")
    avg_sum_paid_refund_amount_month = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="月内场均退货金额")
    avg_tet_amount_month = models.FloatField(blank=True, null=True, db_comment="月内场均千次曝光成交金额")
    avg_sum_bef_send_refund_amount_month = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="月内场均发货前退款金额")
    avg_sum_af_send_refund_amount_month = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="月内场均发货后退款金额")
    avg_sum_af_end_refund_amount_month = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="月内场均签收后退款金额")
    total_max_online_users_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计在线人数")
    total_cnt_openid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计下单人数")
    total_cnt_pay_openid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计支付人数")
    total_cnt_refund_openid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计退款人数")
    total_cnt_bef_send_refund_openid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计发货前退款人数")
    total_cnt_af_send_refund_openid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计发货后退款人数")
    total_cnt_af_end_refund_openid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计签收后退货人数")
    total_cnt_qt_refund_openid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计品退人数")
    total_cnt_click_openid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计商品点击人数")
    total_cnt_value_click_rate_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计商品曝光-点击率（人数）")
    total_cnt_click_pay_rate_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计商品点击-成交转化率（人数）")
    total_sum_qty_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计已下单商品数量")
    total_sum_pay_qty_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计已支付商品数量")
    total_sum_send_qty_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计已发货商品数量")
    total_sum_end_qty_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计已签收商品数量")
    total_sum_bef_send_refund_qty_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计发货前退货商品数量")
    total_sum_af_send_refund_qty_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计发货后退货商品数量")
    total_sum_af_end_refund_qty_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计签收后退货商品数量")
    total_sum_qt_refund_qty_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计品退商品数量")
    total_sum_introduce_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计讲解时长")
    total_cnt_introduce_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计讲解次数")
    total_cnt_soid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计已创建订单数量")
    total_cnt_pay_soid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计已支付订单数量")
    total_cnt_send_soid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计已发货订单数量")
    total_cnt_end_soid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计已签收订单数量")
    total_cnt_qt_refund_soid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计品退订单数量")
    total_cnt_ps_soid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计预售订单数")
    total_cnt_bef_send_refund_soid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计发货前退款订单数")
    total_cnt_af_send_refund_soid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计发货后退款订单数")
    total_sum_paid_amount_month = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="月内累计支付金额")
    total_sum_paid_end_amount_month = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="月内累计签收金额")
    total_sum_paid_refund_amount_month = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="月内累计退货金额")
    total_tet_amount_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计千次曝光成交金额")
    total_sum_bef_send_refund_amount_month = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="月内累计发货前退款金额")
    total_sum_af_send_refund_amount_month = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="月内累计发货后退款金额")
    total_sum_af_end_refund_amount_month = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="月内累计签收后退款金额")
    avg_max_online_users_history = models.FloatField(blank=True, null=True, db_comment="历史场均在线人数")
    avg_cnt_openid_history = models.FloatField(blank=True, null=True, db_comment="历史场均下单人数")
    avg_cnt_pay_openid_history = models.FloatField(blank=True, null=True, db_comment="历史场均支付人数")
    avg_cnt_end_openid_history = models.FloatField(blank=True, null=True, db_comment="历史场均签收人数")
    avg_cnt_refund_openid_history = models.FloatField(blank=True, null=True, db_comment="历史场均退款人数")
    avg_cnt_bef_send_refund_openid_history = models.FloatField(blank=True, null=True, db_comment="历史场均发货前退款人数")
    avg_cnt_af_send_refund_openid_history = models.FloatField(blank=True, null=True, db_comment="历史场均发货后退款人数")
    avg_cnt_af_end_refund_openid_history = models.FloatField(blank=True, null=True, db_comment="历史场均签收后退货人数")
    avg_cnt_qt_refund_openid_history = models.FloatField(blank=True, null=True, db_comment="历史场均品退人数")
    avg_cnt_click_openid_history = models.FloatField(blank=True, null=True, db_comment="历史场均商品点击人数")
    avg_cnt_value_click_rate_history = models.FloatField(blank=True, null=True, db_comment="历史场均商品曝光-点击率（人数）")
    avg_cnt_click_pay_rate_history = models.FloatField(blank=True, null=True, db_comment="历史场均商品点击-成交转化率（人数）")
    avg_sum_qty_history = models.FloatField(blank=True, null=True, db_comment="历史场均已下单商品数量")
    avg_sum_pay_qty_history = models.FloatField(blank=True, null=True, db_comment="历史场均已支付商品数量")
    avg_sum_send_qty_history = models.FloatField(blank=True, null=True, db_comment="历史场均已发货商品数量")
    avg_sum_end_qty_history = models.FloatField(blank=True, null=True, db_comment="历史场均已签收商品数量")
    avg_sum_bef_send_refund_qty_history = models.FloatField(blank=True, null=True, db_comment="历史场均发货前退货商品数量")
    avg_sum_af_send_refund_qty_history = models.FloatField(blank=True, null=True, db_comment="历史场均发货后退货商品数量")
    avg_sum_af_end_refund_qty_history = models.FloatField(blank=True, null=True, db_comment="历史场均签收后退货商品数量")
    avg_sum_qt_refund_qty_history = models.FloatField(blank=True, null=True, db_comment="历史场均品退商品数量")
    avg_sum_introduce_history = models.FloatField(blank=True, null=True, db_comment="历史场均讲解时长")
    avg_cnt_introduce_history = models.FloatField(blank=True, null=True, db_comment="历史场均讲解次数")
    min_sale_100_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场100单最小耗时")
    min_sale_300_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场300单最小耗时")
    min_sale_500_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场500单最小耗时")
    min_sale_1000_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场1000单最小耗时")
    min_sale_2000_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场2000单最小耗时")
    max_sale_100_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场100单最大耗时")
    max_sale_300_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场300单最大耗时")
    max_sale_500_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场500单最大耗时")
    max_sale_1000_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场1000单最大耗时")
    max_sale_2000_time_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场2000单最大耗时")
    avg_cnt_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均已创建订单数量")
    avg_cnt_pay_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均已支付订单数量")
    avg_cnt_send_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均已发货订单数量")
    avg_cnt_end_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均已签收订单数量")
    avg_cnt_qt_refund_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均品退订单数量")
    avg_cnt_ps_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均预售订单数")
    avg_cnt_bef_send_refund_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均发货前退款订单数")
    avg_cnt_af_send_refund_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均发货后退款订单数")
    avg_rate_bef_send_refund_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均发货前订单退款率")
    avg_rate_af_send_refund_soid_history = models.FloatField(blank=True, null=True, db_comment="历史场均发货后订单退款率")
    min_price_history = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="历史单场最小售价")
    min_profit_history = models.FloatField(blank=True, null=True, db_comment="历史单场最小毛利")
    min_cost_price_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场最小成本价")
    min_price_rate_history = models.FloatField(blank=True, null=True, db_comment="历史单场最小定价比")
    min_avg_paid_openid_history = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="历史单场最小客单价")
    max_price_history = models.DecimalField(max_digits=18, decimal_places=2, blank=True, null=True, db_comment="历史单场最大售价")
    max_profit_history = models.FloatField(blank=True, null=True, db_comment="历史单场最大毛利")
    max_cost_price_history = models.CharField(max_length=255, blank=True, null=True, db_comment="历史单场最大成本价")
    max_price_rate_history = models.FloatField(blank=True, null=True, db_comment="历史单场最大定价比")
    max_avg_paid_openid_history = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="历史单场最大客单价")
    avg_sum_paid_amount_history = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="历史场均支付金额")
    avg_sum_paid_end_amount_history = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="历史场均签收金额")
    avg_sum_paid_refund_amount_history = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="历史场均退货金额")
    avg_tet_amount_history = models.FloatField(blank=True, null=True, db_comment="历史场均千次曝光成交金额")
    avg_sum_bef_send_refund_amount_history = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="历史场均发货前退款金额")
    avg_sum_af_send_refund_amount_history = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="历史场均发货后退款金额")
    avg_sum_af_end_refund_amount_history = models.DecimalField(max_digits=38, decimal_places=4, blank=True, null=True, db_comment="历史场均签收后退款金额")
    total_max_online_users_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计在线人数")
    total_cnt_openid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计下单人数")
    total_cnt_pay_openid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计支付人数")
    total_cnt_end_openid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计签收人数")
    total_cnt_refund_openid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计退款人数")
    total_cnt_bef_send_refund_openid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计发货前退款人数")
    total_cnt_af_send_refund_openid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计发货后退款人数")
    total_cnt_af_end_refund_openid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计签收后退货人数")
    total_cnt_qt_refund_openid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计品退人数")
    total_cnt_click_openid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计商品点击人数")
    total_cnt_value_click_rate_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计商品曝光-点击率（人数）")
    total_cnt_click_pay_rate_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计商品点击-成交转化率（人数）")
    total_sum_qty_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计已下单商品数量")
    total_sum_pay_qty_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计已支付商品数量")
    total_sum_send_qty_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计已发货商品数量")
    total_sum_end_qty_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计已签收商品数量")
    total_sum_bef_send_refund_qty_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计发货前退货商品数量")
    total_sum_af_send_refund_qty_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计发货后退货商品数量")
    total_sum_af_end_refund_qty_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计签收后退货商品数量")
    total_sum_qt_refund_qty_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计品退商品数量")
    total_sum_introduce_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计讲解时长")
    total_cnt_introduce_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计讲解次数")
    total_cnt_soid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计已创建订单数量")
    total_cnt_pay_soid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计已支付订单数量")
    total_cnt_send_soid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计已发货订单数量")
    total_cnt_end_soid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计已签收订单数量")
    total_cnt_qt_refund_soid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计品退订单数量")
    total_cnt_ps_soid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计预售订单数")
    total_cnt_bef_send_refund_soid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计发货前退款订单数")
    total_rate_af_send_refund_soid_history = models.FloatField(blank=True, null=True, db_comment="历史累计发货后订单退款率")
    total_sum_paid_amount_history = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="历史累计支付金额")
    total_sum_paid_end_amount_history = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="历史累计签收金额")
    total_sum_paid_refund_amount_history = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="历史累计退货金额")
    total_tet_amount_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计千次曝光成交金额")
    total_sum_bef_send_refund_amount_history = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="历史累计发货前退款金额")
    total_sum_af_send_refund_amount_history = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="历史累计发货后退款金额")
    total_sum_af_end_refund_amount_history = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="历史累计签收后退款金额")
    total_cnt_end_openid_month = models.BigIntegerField(blank=True, null=True, db_comment="月内累计签收人数")
    sum_done_amt_7 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近7天累计签收金额")
    sum_done_amt_30 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近30天累计签收金额")
    sum_done_amt_60 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近60天累计签收金额")
    sum_done_amt_90 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近90天累计签收金额")
    sum_done_amt_180 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近180天累计签收金额")
    sum_done_amt_365 = models.DecimalField(max_digits=38, decimal_places=2, blank=True, null=True, db_comment="近365天累计签收金额")
    sum_done_qty_7 = models.BigIntegerField(blank=True, null=True, db_comment="近7天商品累计成交量")
    sum_done_qty_30 = models.BigIntegerField(blank=True, null=True, db_comment="近30天商品累计成交量")
    sum_done_qty_60 = models.BigIntegerField(blank=True, null=True, db_comment="近60天商品累计成交量")
    sum_done_qty_90 = models.BigIntegerField(blank=True, null=True, db_comment="近90天商品累计成交量")
    sum_done_qty_180 = models.BigIntegerField(blank=True, null=True, db_comment="近180天商品累计成交量")
    sum_done_qty_365 = models.BigIntegerField(blank=True, null=True, db_comment="近365天商品累计成交量")
    total_cnt_af_send_refund_soid_history = models.BigIntegerField(blank=True, null=True, db_comment="历史累计发货后退款订单数")
    is_feast_hot = models.CharField(max_length=65533, blank=True, null=True, db_comment="节日热销")
    feast_labels = models.CharField(max_length=65533, blank=True, null=True, db_comment="节日标签")
    other_labels = models.CharField(max_length=65533, blank=True, null=True, db_comment="其他标签")

    class Meta:
        managed = False
        db_table = "products_product_labels_all"
        unique_together = (("product_id", "i_id"),)
        db_table_comment = "商品销售指标宽表"
