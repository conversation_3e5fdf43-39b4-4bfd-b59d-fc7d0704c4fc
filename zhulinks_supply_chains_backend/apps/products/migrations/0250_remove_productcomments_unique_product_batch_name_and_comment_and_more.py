# Generated by Django 5.0.6 on 2024-06-28 06:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "products",
            "0249_remove_productrefundrecord_unique_product_refund_with_date_and_shop_name_and_more",
        ),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="productcomments",
            name="unique_product_batch_name_and_comment",
        ),
        migrations.AddField(
            model_name="productcomments",
            name="sku",
            field=models.ForeignKey(
                db_constraint=False,
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.stockkeepingunit",
                to_field="sku_id",
                verbose_name="关联sku",
            ),
            preserve_default=False,
        ),
        migrations.AddConstraint(
            model_name="productcomments",
            constraint=models.UniqueConstraint(
                fields=("sku", "batch_name", "comment"),
                name="unique_sku_batch_name_and_comment",
            ),
        ),
    ]
