# Generated by Django 4.2.11 on 2024-03-25 09:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0130_merge_20240321_1647"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product_id",
                    models.BigIntegerField(unique=True, verbose_name="主商品id"),
                ),
                (
                    "old_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=20,
                        null=True,
                        verbose_name="原货号",
                    ),
                ),
                (
                    "new_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=20,
                        null=True,
                        verbose_name="新货号",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品记录表",
                "verbose_name_plural": "商品记录表",
            },
        ),
        migrations.CreateModel(
            name="SubProductRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "old_parent_product_id",
                    models.BigIntegerField(verbose_name="旧父商品id"),
                ),
                (
                    "new_parent_product_id",
                    models.BigIntegerField(verbose_name="新父商品id"),
                ),
                (
                    "product_id",
                    models.BigIntegerField(unique=True, verbose_name="商品副本id"),
                ),
                (
                    "old_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=20,
                        null=True,
                        verbose_name="原货号",
                    ),
                ),
                (
                    "new_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=20,
                        null=True,
                        verbose_name="新货号",
                    ),
                ),
            ],
            options={
                "verbose_name": "副本商品记录表",
                "verbose_name_plural": "副本商品记录表",
            },
        ),
        migrations.AlterModelOptions(
            name="hostingproducts",
            options={
                "verbose_name": "供应商托管商品列表",
                "verbose_name_plural": "供应商托管商品列表",
            },
        ),
        migrations.CreateModel(
            name="SubSkuRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "old_spec_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=30,
                        null=True,
                        verbose_name="原商品编码",
                    ),
                ),
                (
                    "new_spec_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=30,
                        null=True,
                        verbose_name="原商品编码",
                    ),
                ),
                (
                    "product_record",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.subproductrecord",
                        to_field="product_id",
                        verbose_name="副本商品记录表",
                    ),
                ),
            ],
            options={
                "verbose_name": "副本SKU记录表",
                "verbose_name_plural": "副本SKU记录表",
            },
        ),
        migrations.CreateModel(
            name="SkuRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "old_spec_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=30,
                        null=True,
                        verbose_name="原商品编码",
                    ),
                ),
                (
                    "new_spec_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=30,
                        null=True,
                        verbose_name="原商品编码",
                    ),
                ),
                (
                    "product_record",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.productrecord",
                        to_field="product_id",
                        verbose_name="商品记录",
                    ),
                ),
            ],
            options={
                "verbose_name": "SKU记录表",
                "verbose_name_plural": "SKU记录表",
            },
        ),
    ]
