# Generated by Django 5.0.6 on 2024-06-28 06:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "products",
            "0248_remove_productrefundrecord_unique_product_refund_with_date_and_room_and_more",
        ),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="productrefundrecord",
            name="unique_product_refund_with_date_and_shop_name",
        ),
        migrations.AddField(
            model_name="productrefundrecord",
            name="sku",
            field=models.ForeignKey(
                db_constraint=False,
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.stockkeepingunit",
                to_field="sku_id",
                verbose_name="关联sku",
            ),
            preserve_default=False,
        ),
        migrations.AddConstraint(
            model_name="productrefundrecord",
            constraint=models.UniqueConstraint(
                fields=("sku", "record_date", "shop_name"),
                name="unique_sku_with_date_and_shop_name",
            ),
        ),
    ]
