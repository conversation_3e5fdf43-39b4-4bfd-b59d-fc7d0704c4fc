# Generated by Django 4.2.11 on 2024-04-03 07:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0145_product_can_use_inventory_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="productselectionplanrecord",
            name="record_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("add_product", "增加了商品"),
                    ("delete_product", "删除了商品"),
                    ("restore_product", "还原了商品"),
                    ("add_remark", "增加了备注"),
                    ("modify_remark", "更新备注"),
                    ("add_plan", "增加了计划"),
                    ("modify_plan", "编辑了计划"),
                    ("delete_plan", "删除了计划"),
                    ("lock_plan", "锁定计划"),
                    ("release_plan", "解锁计划"),
                    ("modify_order", "调整顺序"),
                    ("modify_cs_link", "更新了关联客服"),
                    ("prod_confirm", "商品确认"),
                    ("cancel_prod_confirm", "取消商品确认"),
                    ("mod_sku_inventory", "编辑SKU库存"),
                ],
                db_index=True,
                max_length=30,
                null=True,
                verbose_name="操作类型",
            ),
        ),
    ]
