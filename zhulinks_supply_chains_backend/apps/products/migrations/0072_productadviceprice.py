# -*- coding: utf-8 -*-
# @Author: <PERSON>
# @Date:   2023-12-07 17:20:39
# @Last Modified by:   <PERSON>
# @Last Modified time: 2023-12-07 17:24:22
# Generated by Django 4.2.7 on 2023-12-07 06:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0071_alter_productaddress_options_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductAdvicePrice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "advice_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="建议价格",
                    ),
                ),
                ("remark", models.TextField(default="", verbose_name="备注信息")),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建日期"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="更新日期"),
                ),
            ],
            options={
                "verbose_name": "商品建议价格",
                "verbose_name_plural": "商品建议价格",
            },
        ),
        migrations.CreateModel(
            name="ProductReviewPrice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "state",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "通过"), (2, "不通过")], verbose_name="核价状态"
                    ),
                ),
                (
                    "review_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="审核的价格",
                    ),
                ),
                ("remark", models.TextField(default="", verbose_name="备注信息")),
                ("create_user", models.BigIntegerField(verbose_name="审核用户ID")),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.BigIntegerField(
                        blank=True, default=0, null=True, verbose_name="更新用户ID"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        to_field="product_id",
                        verbose_name="商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "核价信息",
                "verbose_name_plural": "核价信息",
            },
        ),
    ]
