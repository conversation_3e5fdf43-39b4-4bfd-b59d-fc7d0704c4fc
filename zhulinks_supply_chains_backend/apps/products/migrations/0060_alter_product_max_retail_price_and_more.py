# Generated by Django 4.2.1 on 2023-11-21 02:38

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0059_productselectionitem_create_date_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="product",
            name="max_retail_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="最高建议售价",
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="min_retail_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="最低建议售价",
            ),
        ),
        migrations.AlterField(
            model_name="productattrlist",
            name="values",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=16, verbose_name="属性值"), size=500
            ),
        ),
        migrations.AlterField(
            model_name="stockkeepingunit",
            name="retail_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="建议售价",
            ),
        ),
    ]
