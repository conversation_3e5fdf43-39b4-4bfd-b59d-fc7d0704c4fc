# Generated by Django 5.0.6 on 2024-05-23 07:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0198_datashopproductmap_datashopsku_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="datashopuploadtasks",
            name="state",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, "等待图片审核"),
                    (1, "图片审核失败"),
                    (2, "等待商品上传"),
                    (4, "商品上传成功"),
                    (5, "商品上传失败"),
                    (6, "取消上传"),
                    (7, "系统取消上传(已存在)"),
                ],
                default=0,
                verbose_name="状态",
            ),
        ),
    ]
