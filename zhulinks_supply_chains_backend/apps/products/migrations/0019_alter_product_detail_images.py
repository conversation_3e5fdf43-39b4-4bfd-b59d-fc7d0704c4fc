# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-07-17 16:51:23
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-07-21 15:23:04
# Generated by Django 4.2.1 on 2023-07-17 08:51

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0018_stockkeepingunit_create_date_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="product",
            name="detail_images",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(default=None, max_length=300, verbose_name="图文详情"),
                blank=True,
                default=list,
                null=True,
                size=20,
                verbose_name="图文详情列表",
            ),
        ),
    ]
