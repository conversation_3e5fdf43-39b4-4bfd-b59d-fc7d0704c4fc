# Generated by Django 5.0.8 on 2024-11-18 05:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0066_company_grade"),
        ("products", "0321_productselectionplan_spu_count_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="userwishlist",
            name="distributor",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="companies.distributor",
                to_field="distributor_id",
                verbose_name="关联分销商",
            ),
        ),
        migrations.AddField(
            model_name="userwishlist",
            name="number",
            field=models.IntegerField(default=1, verbose_name="预购数量"),
        ),
        migrations.AddField(
            model_name="userwishlist",
            name="wish_type",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "臻品-用户想要"), (2, "分销-用户预购")],
                default=1,
                verbose_name="数据来源",
            ),
        ),
    ]
