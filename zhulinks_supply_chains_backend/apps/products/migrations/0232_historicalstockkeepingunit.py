# Generated by Django 5.0.6 on 2024-06-20 05:28

import django.contrib.postgres.fields
import django.db.models.deletion
import simple_history.models
import utils.common
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0231_merge_20240619_1803"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalStockKeepingUnit",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "sku_id",
                    models.BigIntegerField(
                        db_index=True,
                        default=utils.common.get_random,
                        verbose_name="sku id",
                    ),
                ),
                ("physical_inventory", models.BigIntegerField(verbose_name="现货库存")),
                (
                    "safety_inventory",
                    models.BigIntegerField(
                        blank=True, default=0, null=True, verbose_name="7天补货库存"
                    ),
                ),
                (
                    "warehouse_inventory",
                    models.BigIntegerField(
                        blank=True, default=0, null=True, verbose_name="仓库库存"
                    ),
                ),
                (
                    "external_sku_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=30,
                        null=True,
                        verbose_name="外部sku id",
                    ),
                ),
                (
                    "plan_use_inventory",
                    models.BigIntegerField(
                        blank=True, default=0, null=True, verbose_name="货盘占用库存"
                    ),
                ),
                (
                    "can_use_inventory",
                    models.BigIntegerField(
                        blank=True, default=0, null=True, verbose_name="可用库存"
                    ),
                ),
                (
                    "spec_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=30,
                        null=True,
                        verbose_name="商品编码",
                    ),
                ),
                (
                    "link_code",
                    models.CharField(
                        blank=True, max_length=15, null=True, verbose_name="链接编码"
                    ),
                ),
                (
                    "cost_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="成本价"
                    ),
                ),
                (
                    "retail_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="建议售价",
                    ),
                ),
                (
                    "history_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="历史价",
                    ),
                ),
                (
                    "weight",
                    models.FloatField(blank=True, null=True, verbose_name="重量"),
                ),
                ("sales", models.BigIntegerField(default=0, verbose_name="SKU销量")),
                (
                    "image",
                    models.CharField(
                        blank=True, max_length=300, null=True, verbose_name="规格主图"
                    ),
                ),
                (
                    "specs",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.JSONField(
                            default=dict, verbose_name="规格键值对"
                        ),
                        blank=True,
                        default=list,
                        null=True,
                        size=3,
                        verbose_name="规格列表",
                    ),
                ),
                (
                    "become_history",
                    models.BooleanField(
                        blank=True,
                        default=False,
                        null=True,
                        verbose_name="已经变成历史数据",
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "has_live",
                    models.BooleanField(default=False, verbose_name="是否已播"),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="products.product",
                        verbose_name="商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical 商品sku",
                "verbose_name_plural": "historical 商品sku",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
