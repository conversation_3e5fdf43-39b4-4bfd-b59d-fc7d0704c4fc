# Generated by Django 5.0.6 on 2024-06-28 05:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0246_productcomments_unique_product_batch_name_and_comment"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="productrefundrecord",
            name="unique_product_refund_with_date_and_room",
        ),
        migrations.RenameField(
            model_name="productrefundrecord",
            old_name="refund_date",
            new_name="record_date",
        ),
        migrations.AddConstraint(
            model_name="productrefundrecord",
            constraint=models.UniqueConstraint(
                fields=("product", "record_date", "live_room"),
                name="unique_product_refund_with_date_and_room",
            ),
        ),
    ]
