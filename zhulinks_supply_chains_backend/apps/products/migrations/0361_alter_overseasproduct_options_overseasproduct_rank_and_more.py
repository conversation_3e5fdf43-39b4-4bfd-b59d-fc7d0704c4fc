# Generated by Django 5.0.8 on 2024-12-11 07:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0360_overseascountry_overseasplatform_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="overseasproduct",
            options={
                "ordering": ("rank",),
                "verbose_name": "海外榜单商品",
                "verbose_name_plural": "海外榜单商品",
            },
        ),
        migrations.AddField(
            model_name="overseasproduct",
            name="rank",
            field=models.IntegerField(default=0, verbose_name="排名(小到大排序)"),
        ),
        migrations.AlterField(
            model_name="overseasproduct",
            name="img",
            field=models.JSONField(
                blank=True, default=list, null=True, verbose_name="商品图片列表"
            ),
        ),
    ]
