# Generated by Django 4.2.11 on 2024-05-11 02:56

from django.db import migrations, models
import django.db.models.deletion
import multiselectfield.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0176_merge_20240510_1425"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductGift",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "gift_type",
                    multiselectfield.db.fields.MultiSelectField(
                        choices=[("AC", "配件"), ("PO", "商品")],
                        max_length=2,
                        verbose_name="赠品类型",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=15, null=True, verbose_name="名称"
                    ),
                ),
                (
                    "material",
                    models.CharField(
                        blank=True, max_length=15, null=True, verbose_name="材质"
                    ),
                ),
                (
                    "image",
                    models.CharField(
                        blank=True, max_length=300, null=True, verbose_name="图片"
                    ),
                ),
                ("count", models.IntegerField(default=1, verbose_name="数量")),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "gift_product",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="gift_product",
                        to="products.product",
                        to_field="product_id",
                        verbose_name="赠品商品",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        to_field="product_id",
                        verbose_name="商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "赠品",
                "verbose_name_plural": "赠品",
            },
        ),
        migrations.AddConstraint(
            model_name="productgift",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("product", "gift_product"),
                name="product_gift_product_when_is_deleted_is_false",
            ),
        ),
    ]
