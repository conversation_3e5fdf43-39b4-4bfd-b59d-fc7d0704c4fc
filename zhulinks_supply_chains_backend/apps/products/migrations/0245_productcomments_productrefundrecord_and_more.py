# Generated by Django 5.0.6 on 2024-06-28 05:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0244_merge_20240627_1811"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductComments",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "c_type",
                    models.CharField(
                        choices=[("QA", "质检"), ("AS", "退费")],
                        default="QA",
                        verbose_name="评论类型",
                    ),
                ),
                (
                    "batch_name",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=128,
                        null=True,
                        verbose_name="批次名称",
                    ),
                ),
                ("display", models.BooleanField(default=True, verbose_name="是否显示")),
                ("comment", models.TextField(default="", verbose_name="原因评论")),
                ("total_num", models.IntegerField(default=0, verbose_name="质检数量")),
                (
                    "unqualified_num",
                    models.IntegerField(default=0, verbose_name="不合格数量"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="创建用户"
                    ),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="更新用户"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "商品评论表",
                "verbose_name_plural": "商品评论表",
            },
        ),
        migrations.CreateModel(
            name="ProductRefundRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("refund_date", models.DateField(verbose_name="退费日期")),
                ("order_num", models.IntegerField(default=0, verbose_name="下单件数")),
                ("refund_num", models.IntegerField(default=0, verbose_name="退单件数")),
                (
                    "live_room",
                    models.CharField(max_length=128, verbose_name="所属直播间"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="创建用户"
                    ),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="更新用户"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "退费统计记录表",
                "verbose_name_plural": "退费统计记录表",
            },
        ),
        migrations.AddConstraint(
            model_name="substockkeepingunit",
            constraint=models.UniqueConstraint(
                condition=models.Q(("become_history", False)),
                fields=("parent_sku", "relate_sku"),
                name="unique_parent_sku_and_relate_sku_with_become_history",
            ),
        ),
        migrations.AddField(
            model_name="productcomments",
            name="product",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.product",
                to_field="product_id",
                verbose_name="关联商品",
            ),
        ),
        migrations.AddField(
            model_name="productrefundrecord",
            name="product",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.product",
                to_field="product_id",
                verbose_name="关联商品",
            ),
        ),
        migrations.AddConstraint(
            model_name="productrefundrecord",
            constraint=models.UniqueConstraint(
                fields=("product", "refund_date", "live_room"),
                name="unique_product_refund_with_date_and_room",
            ),
        ),
    ]
