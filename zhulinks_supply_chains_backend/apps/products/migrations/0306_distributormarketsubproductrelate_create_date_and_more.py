# Generated by Django 5.0.8 on 2024-09-27 02:24

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0305_alter_productattrvalues_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="distributormarketsubproductrelate",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="distributormarketsubproductrelate",
            name="create_user",
            field=models.CharField(
                blank=True, default="", null=True, verbose_name="创建者"
            ),
        ),
        migrations.AddField(
            model_name="distributormarketsubproductrelate",
            name="is_deleted",
            field=models.BooleanField(default=False, verbose_name="是否删除"),
        ),
        migrations.AddField(
            model_name="distributormarketsubproductrelate",
            name="update_date",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
        migrations.AddField(
            model_name="distributormarketsubproductrelate",
            name="update_user",
            field=models.CharField(
                blank=True, default="", null=True, verbose_name="最后更新人"
            ),
        ),
    ]
