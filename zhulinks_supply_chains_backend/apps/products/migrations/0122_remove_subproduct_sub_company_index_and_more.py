# Generated by Django 4.2.7 on 2024-03-08 09:37

from django.db import migrations, models
import products.models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0121_merge_20240308_1109"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="subproduct",
            name="sub_company_index",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="address",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="attr_options",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="brand",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="category",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="company",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="confirm_DouDian_link_user",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="cross_border_attr",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="data_source",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="doudian_id",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="has_DouDian_link",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="has_link_code",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="has_live",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="history_prices",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="inventory_warning",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="live_date",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="main_video",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="max_cost_price",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="max_history_price",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="max_retail_price",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="min_cost_price",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="min_history_price",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="min_retail_price",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="physical_inventory",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="product_review",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="product_type",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="safety_inventory",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="sales",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="size",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="skus",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="spec_lists",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="state",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="state_reason",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="tags",
        ),
        migrations.RemoveField(
            model_name="subproduct",
            name="unit",
        ),
        migrations.AlterField(
            model_name="subproduct",
            name="create_user",
            field=models.CharField(
                blank=True, default="", null=True, verbose_name="创建者"
            ),
        ),
        migrations.AlterField(
            model_name="subproduct",
            name="detail_images",
            field=models.TextField(blank=True, null=True, verbose_name="图文详情"),
        ),
        migrations.AlterField(
            model_name="subproduct",
            name="product_id",
            field=models.BigIntegerField(
                default=products.models.sub_product_id_generator,
                unique=True,
                verbose_name="商品id",
            ),
        ),
        migrations.AlterField(
            model_name="subproduct",
            name="update_user",
            field=models.CharField(
                blank=True, default="", null=True, verbose_name="最后更新人"
            ),
        ),
        migrations.AddConstraint(
            model_name="subproduct",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_deleted", False)),
                fields=("parent_product", "owner"),
                name="unique_owner_subproduct_with_is_deleted",
            ),
        ),
    ]
