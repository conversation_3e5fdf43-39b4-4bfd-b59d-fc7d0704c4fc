# Generated by Django 5.0.6 on 2024-07-01 03:29

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "products",
            "0252_remove_productrefundrecord_unique_sku_with_date_and_shop_name_and_more",
        ),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="productcomments",
            name="unique_sku_spec_code_batch_name_and_comment",
        ),
        migrations.RemoveConstraint(
            model_name="productrefundrecord",
            name="unique_sku_spec_code_with_date_and_shop_name",
        ),
        migrations.AddField(
            model_name="productcomments",
            name="hash_value",
            field=models.CharField(
                default=django.utils.timezone.now,
                max_length=64,
                unique=True,
                verbose_name="唯一值",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="productrefundrecord",
            name="hash_value",
            field=models.Char<PERSON>ield(
                default=django.utils.timezone.now,
                max_length=64,
                unique=True,
                verbose_name="唯一值",
            ),
            preserve_default=False,
        ),
    ]
