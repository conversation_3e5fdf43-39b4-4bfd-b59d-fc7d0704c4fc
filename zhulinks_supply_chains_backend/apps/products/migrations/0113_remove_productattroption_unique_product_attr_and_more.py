# Generated by Django 4.2.7 on 2024-02-27 05:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0112_productattrvalues_and_more"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="productattroption",
            name="unique_product_attr",
        ),
        migrations.AddField(
            model_name="productattroption",
            name="attr_value",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.productattrvalues",
                verbose_name="关联属性的值",
            ),
        ),
        migrations.AddConstraint(
            model_name="productattroption",
            constraint=models.UniqueConstraint(
                fields=("product", "attr", "attr_value"),
                name="unique_product_attr_value",
            ),
        ),
    ]
