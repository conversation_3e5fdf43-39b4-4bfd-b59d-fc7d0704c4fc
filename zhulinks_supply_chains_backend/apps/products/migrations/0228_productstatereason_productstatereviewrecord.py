# Generated by Django 5.0.6 on 2024-06-19 07:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0227_skuinventoryreviewrecord_apply_inventory"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductStateReason",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=128, verbose_name="理由名称")),
                (
                    "reason_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "上架"), (2, "下架"), (3, "通用")],
                        default=3,
                        verbose_name="理由类型",
                    ),
                ),
                ("enable", models.BooleanField(default=True, verbose_name="是否启用")),
            ],
            options={
                "verbose_name": "上下架理由枚举管理",
                "verbose_name_plural": "上下架理由枚举管理",
            },
        ),
        migrations.CreateModel(
            name="ProductStateReviewRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "review_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "上架"), (2, "下架")], verbose_name="上下架类型"
                    ),
                ),
                (
                    "remark",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="备注信息"
                    ),
                ),
                (
                    "review_date",
                    models.DateTimeField(
                        blank=True, default=None, null=True, verbose_name="审核时间"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "cancel_user",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="cancel_user_set",
                        to=settings.AUTH_USER_MODEL,
                        to_field="user_id",
                        verbose_name="取消者",
                    ),
                ),
                (
                    "create_user",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="create_user_set",
                        to=settings.AUTH_USER_MODEL,
                        to_field="user_id",
                        verbose_name="创建者",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        to_field="product_id",
                        verbose_name="所属商品",
                    ),
                ),
                (
                    "reason",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="products.productstatereason",
                        verbose_name="上下架理由",
                    ),
                ),
                (
                    "review_user",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review_user_set",
                        to=settings.AUTH_USER_MODEL,
                        to_field="user_id",
                        verbose_name="审核人",
                    ),
                ),
            ],
            options={
                "verbose_name": "上下架审核记录",
                "verbose_name_plural": "上下架审核记录",
            },
        ),
    ]
