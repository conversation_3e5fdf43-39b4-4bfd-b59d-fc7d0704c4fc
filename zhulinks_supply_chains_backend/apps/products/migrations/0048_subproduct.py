# Generated by Django 4.2.1 on 2023-11-03 09:08

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import utils.common


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("companies", "0022_bank_alter_shop_options"),
        ("products", "0047_remove_stockkeepingunit_spec_code_index_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SubProduct",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product_id",
                    models.BigIntegerField(
                        default=utils.common.get_random,
                        unique=True,
                        verbose_name="商品id",
                    ),
                ),
                (
                    "product_type",
                    models.CharField(
                        blank=True,
                        choices=[("FN", "成品"), ("SF", "半成品")],
                        default="FN",
                        max_length=2,
                        null=True,
                        verbose_name="商品类型",
                    ),
                ),
                ("name", models.CharField(max_length=80, verbose_name="商品名称")),
                (
                    "category",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            blank=True, max_length=32, null=True, verbose_name="商品分类id"
                        ),
                        size=3,
                        verbose_name="分类列表",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="货号"
                    ),
                ),
                (
                    "size",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=56,
                        null=True,
                        verbose_name="尺寸",
                    ),
                ),
                ("sales", models.BigIntegerField(default=0, verbose_name="商品销量")),
                (
                    "state",
                    models.SmallIntegerField(
                        choices=[(0, "待审核"), (1, "上架"), (2, "审核不通过"), (3, "下架")],
                        default=0,
                        verbose_name="商品状态",
                    ),
                ),
                (
                    "state_reason",
                    models.JSONField(blank=True, null=True, verbose_name="状态描述"),
                ),
                ("is_deleted", models.BooleanField(default=False, verbose_name="是否删除")),
                (
                    "cross_border_attr",
                    models.CharField(
                        choices=[("CN", "中国大陆地区"), ("HK", "中国香港")],
                        default="CN",
                        max_length=2,
                        verbose_name="跨境属性",
                    ),
                ),
                (
                    "tags",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            blank=True, max_length=20, null=True, verbose_name="商品标签"
                        ),
                        blank=True,
                        null=True,
                        size=20,
                        verbose_name="标签列表",
                    ),
                ),
                (
                    "main_images",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            default=None, max_length=300, verbose_name="商品主图"
                        ),
                        size=5,
                        verbose_name="主图列表",
                    ),
                ),
                (
                    "spec_lists",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.JSONField(
                            default=dict, verbose_name="商品规格对象"
                        ),
                        blank=True,
                        default=list,
                        null=True,
                        size=3,
                        verbose_name="商品规格列表",
                    ),
                ),
                (
                    "main_video",
                    models.CharField(
                        blank=True, max_length=300, null=True, verbose_name="商品主视频"
                    ),
                ),
                (
                    "detail_images",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            default=None, max_length=300, verbose_name="图文详情"
                        ),
                        blank=True,
                        default=list,
                        null=True,
                        size=20,
                        verbose_name="图文详情列表",
                    ),
                ),
                (
                    "physical_inventory",
                    models.BigIntegerField(
                        blank=True, default=0, null=True, verbose_name="现货库存"
                    ),
                ),
                (
                    "safety_inventory",
                    models.BigIntegerField(
                        blank=True, default=0, null=True, verbose_name="7天补货库存"
                    ),
                ),
                (
                    "has_link_code",
                    models.BooleanField(default=False, verbose_name="已设置链接编码"),
                ),
                (
                    "min_cost_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="最低成本价",
                    ),
                ),
                (
                    "max_cost_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="最高成本价",
                    ),
                ),
                (
                    "min_retail_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="最低零售价",
                    ),
                ),
                (
                    "max_retail_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="最高零售价",
                    ),
                ),
                (
                    "min_history_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="最低历史价",
                    ),
                ),
                (
                    "max_history_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="最高历史价",
                    ),
                ),
                (
                    "inventory_warning",
                    models.BooleanField(default=False, verbose_name="库存告警"),
                ),
                (
                    "data_source",
                    models.CharField(
                        choices=[
                            ("SP", "供应商录入"),
                            ("OP", "运营商录入"),
                            ("OP_JST", "运营商-聚水潭录入"),
                            ("OP_KP", "运营商-快批录入"),
                        ],
                        default="SP",
                        max_length=6,
                        verbose_name="数据来源",
                    ),
                ),
                (
                    "has_DouDian_link",
                    models.BooleanField(default=False, verbose_name="完成抖店链接"),
                ),
                (
                    "confirm_DouDian_link_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="确认完成抖店链接者"
                    ),
                ),
                (
                    "is_listed_DouDian",
                    models.BooleanField(default=False, verbose_name="是否上架抖店"),
                ),
                (
                    "listed_DouDian_return",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="上架抖店的返回值"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="创建用户"
                    ),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="更新用户"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "brand",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="companies.brand",
                        verbose_name="品牌",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        verbose_name="供应商",
                    ),
                ),
                (
                    "history_prices",
                    models.ManyToManyField(
                        to="products.historyprice", verbose_name="商品历史价"
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="副本创建者",
                    ),
                ),
                (
                    "parent_product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        verbose_name="父商品",
                    ),
                ),
                (
                    "product_review",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="products.productreview",
                        verbose_name="商品审核",
                    ),
                ),
                (
                    "skus",
                    models.ManyToManyField(
                        to="products.stockkeepingunit", verbose_name="商品skus"
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="products.productunit",
                        verbose_name="商品单位",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品",
                "verbose_name_plural": "商品",
                "permissions": (
                    (
                        "view_min_cost_price_SubProduct",
                        "Can read subprodcut min_cost_price",
                    ),
                    (
                        "change_min_cost_price_SubProduct",
                        "Can change subproduct min_cost_price",
                    ),
                    (
                        "view_max_cost_price_SubProduct",
                        "Can read subprodcut max_cost_price",
                    ),
                    (
                        "change_max_cost_price_SubProduct",
                        "Can change subproduct max_cost_price",
                    ),
                ),
                "indexes": [
                    models.Index(
                        fields=["company"],
                        include=("is_deleted",),
                        name="sub_company_index",
                    )
                ],
            },
        ),
    ]
