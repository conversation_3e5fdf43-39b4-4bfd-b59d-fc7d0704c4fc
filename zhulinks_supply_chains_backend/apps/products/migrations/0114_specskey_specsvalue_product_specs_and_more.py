# Generated by Django 4.2.7 on 2024-02-28 10:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("companies", "0032_stall_is_deleted"),
        ("products", "0113_remove_productattroption_unique_product_attr_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SpecsKey",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False, verbose_name="是否删除")),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.Char<PERSON>ield(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=128,
                        null=True,
                        verbose_name="规格名描述",
                    ),
                ),
                ("display", models.BooleanField(default=True, verbose_name="是否显示")),
                (
                    "attr",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="products.productattrlist",
                        verbose_name="关联属性名",
                    ),
                ),
                (
                    "distributor",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.distributor",
                        to_field="distributor_id",
                        verbose_name="所属分销商",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        to_field="company_id",
                        verbose_name="所属供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "规格名管理",
                "verbose_name_plural": "规格名管理",
            },
        ),
        migrations.CreateModel(
            name="SpecsValue",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False, verbose_name="是否删除")),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("value", models.CharField(max_length=128, verbose_name="规格值描述")),
                (
                    "attr_value",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="products.productattrvalues",
                        verbose_name="关联属性值",
                    ),
                ),
                (
                    "spec_key",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.specskey",
                        verbose_name="所属规格名",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="product",
            name="specs",
            field=models.ManyToManyField(
                to="products.specskey", verbose_name="商品关联属性名"
            ),
        ),
        migrations.AddField(
            model_name="product",
            name="specs_value",
            field=models.ManyToManyField(
                to="products.specsvalue", verbose_name="商品关联属性值"
            ),
        ),
        migrations.AddField(
            model_name="stockkeepingunit",
            name="specs_name",
            field=models.ManyToManyField(
                to="products.specskey", verbose_name="商品关联属性名"
            ),
        ),
        migrations.AddField(
            model_name="stockkeepingunit",
            name="specs_value",
            field=models.ManyToManyField(
                to="products.specsvalue", verbose_name="商品关联属性值"
            ),
        ),
        migrations.AddConstraint(
            model_name="specskey",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("distributor__isnull", True), ("supplier__isnull", True)
                ),
                fields=("name",),
                name="unique_name_with_no_owner",
            ),
        ),
        migrations.AddConstraint(
            model_name="specskey",
            constraint=models.UniqueConstraint(
                fields=("name", "distributor"), name="unique_name_with_db_owner"
            ),
        ),
        migrations.AddConstraint(
            model_name="specskey",
            constraint=models.UniqueConstraint(
                fields=("name", "supplier"), name="unique_name_with_supplier_owner"
            ),
        ),
    ]
