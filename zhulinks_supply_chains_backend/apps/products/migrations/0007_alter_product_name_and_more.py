# Generated by Django 4.2.1 on 2023-06-19 12:18

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0006_alter_stockkeepingunit_cost_price_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="product",
            name="name",
            field=models.Char<PERSON>ield(max_length=80, verbose_name="商品名称"),
        ),
        migrations.AlterField(
            model_name="stockkeepingunit",
            name="safety_inventory",
            field=models.BigIntegerField(
                blank=True, default=0, null=True, verbose_name="安全库存"
            ),
        ),
        migrations.AlterField(
            model_name="stockkeepingunit",
            name="spec_code",
            field=models.CharField(
                blank=True, max_length=30, null=True, verbose_name="规格编码"
            ),
        ),
        migrations.AlterField(
            model_name="stockkeepingunit",
            name="weight",
            field=models.FloatField(blank=True, null=True, verbose_name="重量"),
        ),
    ]
