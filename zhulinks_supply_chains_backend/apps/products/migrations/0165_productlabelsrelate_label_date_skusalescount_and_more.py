# Generated by Django 4.2.11 on 2024-04-24 10:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0164_merge_20240424_1053"),
    ]

    operations = [
        migrations.AddField(
            model_name="productlabelsrelate",
            name="label_date",
            field=models.DateTimeField(blank=True, null=True, verbose_name="标签时间"),
        ),
        migrations.CreateModel(
            name="SKUSalesCount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("sales", models.IntegerField(default=0, verbose_name="销量")),
                ("calc_date", models.DateField(verbose_name="日期")),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "sku",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.stockkeepingunit",
                        verbose_name="商品sku",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品SKU销量统计",
                "verbose_name_plural": "商品SKU销量统计",
            },
        ),
        migrations.CreateModel(
            name="ProductSalesCount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("sales", models.IntegerField(default=0, verbose_name="销量")),
                ("calc_date", models.DateField(verbose_name="日期")),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        verbose_name="商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品销量统计",
                "verbose_name_plural": "商品销量统计",
            },
        ),
    ]
