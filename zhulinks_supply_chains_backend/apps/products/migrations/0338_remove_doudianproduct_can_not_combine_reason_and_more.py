# Generated by Django 5.1.2 on 2024-11-07 07:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0337_doudianproduct_can_not_combine_reason_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="doudianproduct",
            name="can_not_combine_reason",
        ),
        migrations.RemoveField(
            model_name="doudianproduct",
            name="cos_ratio",
        ),
        migrations.RemoveField(
            model_name="doudianproduct",
            name="is_package_product",
        ),
        migrations.RemoveField(
            model_name="doudianproduct",
            name="pay_type",
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="check_status",
            field=models.IntegerField(blank=True, null=True, verbose_name="审核状态"),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="delivery_method",
            field=models.IntegerField(
                blank=True, null=True, verbose_name="承诺发货时间（天）"
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="draft_status",
            field=models.IntegerField(blank=True, null=True, verbose_name="草稿状态"),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="is_auto_charge",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="是否是自动充值商品"
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="is_charity_product",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="是否公益商品"
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="is_evaluate_opened",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="测评模块是否打开"
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="market_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=30,
                null=True,
                verbose_name="划线价",
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="need_recharge_mode",
            field=models.BooleanField(
                blank=True,
                default=False,
                null=True,
                verbose_name="是否是生活娱乐充值模式",
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="new_step_product",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="新现货+预售商品标识"
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="outer_product_id",
            field=models.CharField(
                blank=True, max_length=255, null=True, verbose_name="外部商家编码"
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="presell_type",
            field=models.IntegerField(blank=True, null=True, verbose_name="预售类型"),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="product_id_str",
            field=models.CharField(max_length=255, verbose_name="商品ID（字符串类型）"),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="shop_id",
            field=models.BigIntegerField(default=1, verbose_name="店铺ID"),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="spec_id",
            field=models.BigIntegerField(
                blank=True, null=True, verbose_name="商品规格ID"
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="standard_brand_id",
            field=models.IntegerField(
                blank=True, null=True, verbose_name="品牌库brand id"
            ),
        ),
        migrations.AlterField(
            model_name="doudianproduct",
            name="use_brand_name",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="是否勾选使用品牌名"
            ),
        ),
    ]
