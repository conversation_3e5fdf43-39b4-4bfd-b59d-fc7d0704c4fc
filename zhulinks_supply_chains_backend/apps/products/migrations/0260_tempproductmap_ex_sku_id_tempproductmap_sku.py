# Generated by Django 5.0.6 on 2024-07-18 07:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0259_alter_tempproductmap_map_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="tempproductmap",
            name="ex_sku_id",
            field=models.CharField(
                db_index=True, default=None, max_length=255, verbose_name="外部规格ID"
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="tempproductmap",
            name="sku",
            field=models.ForeignKey(
                db_constraint=False,
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.stockkeepingunit",
                to_field="sku_id",
                verbose_name="系统规格ID",
            ),
            preserve_default=False,
        ),
    ]
