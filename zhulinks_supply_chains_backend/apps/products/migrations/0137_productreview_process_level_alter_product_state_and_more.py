# Generated by Django 4.2.11 on 2024-03-27 08:04

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0136_product_has_new_code"),
    ]

    operations = [
        migrations.AddField(
            model_name="productreview",
            name="process_level",
            field=models.CharField(
                choices=[
                    ("BASIC_REVIEW", "基础信息审核"),
                    ("PRICE_REVIEW", "核价信息审核"),
                    ("QA_REVIEW", "质检信息审核"),
                ],
                default="BASIC_REVIEW",
                max_length=32,
                verbose_name="审核层级",
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="state",
            field=models.SmallIntegerField(
                choices=[
                    (0, "待审核"),
                    (1, "上架"),
                    (2, "审核不通过"),
                    (3, "下架"),
                    (4, "待核价"),
                    (5, "待质检"),
                ],
                default=0,
                verbose_name="商品状态",
            ),
        ),
        migrations.CreateModel(
            name="ProductReviewProcess",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "process_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        unique=True,
                        verbose_name="审核流程ID",
                    ),
                ),
                (
                    "become_history",
                    models.BooleanField(default=False, verbose_name="是否为历史数据"),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        verbose_name="商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品审核流程",
                "verbose_name_plural": "商品审核流程",
            },
        ),
        migrations.AddField(
            model_name="productreview",
            name="process",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.productreviewprocess",
                to_field="process_id",
                verbose_name="审核流程ID",
            ),
        ),
        migrations.AddConstraint(
            model_name="productreviewprocess",
            constraint=models.UniqueConstraint(
                condition=models.Q(("become_history", False)),
                fields=("product",),
                name="unique_product_process",
            ),
        ),
    ]
