# Generated by Django 5.1.2 on 2024-11-07 07:27

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0335_remove_doudianproduct_can_not_combine_reason_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="doudianproduct",
            name="create_time",
            field=models.DateTimeField(
                default=django.utils.timezone.now, verbose_name="商品创建时间"
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="doudianproduct",
            name="update_time",
            field=models.DateTimeField(
                default=django.utils.timezone.now, verbose_name="商品更新时间"
            ),
            preserve_default=False,
        ),
    ]
