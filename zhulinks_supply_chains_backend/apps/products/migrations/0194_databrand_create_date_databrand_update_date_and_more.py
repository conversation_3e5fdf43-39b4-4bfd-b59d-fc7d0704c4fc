# Generated by Django 5.0.6 on 2024-05-23 02:14

import django.db.models.deletion
import django.utils.timezone
import products.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0044_alter_datashop_shop_id"),
        ("products", "0193_datashopmaterialfolder"),
    ]

    operations = [
        migrations.AddField(
            model_name="databrand",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="databrand",
            name="update_date",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
        migrations.AddField(
            model_name="datacategory",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="datacategory",
            name="update_date",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
        migrations.AddField(
            model_name="datacateproperties",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="datacateproperties",
            name="update_date",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="datashopmaterialfolder",
            name="folder_id",
            field=models.CharField(
                db_index=True, max_length=255, unique=True, verbose_name="文件夹ID"
            ),
        ),
        migrations.CreateModel(
            name="DataShopUploadTasks",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "task_id",
                    models.CharField(
                        default=products.models.uuid_generator,
                        max_length=255,
                        unique=True,
                        verbose_name="任务ID",
                    ),
                ),
                (
                    "material_pass",
                    models.BooleanField(default=False, verbose_name="素材是否全部通过"),
                ),
                (
                    "product_version",
                    models.CharField(
                        default="1", max_length=10, verbose_name="商品版本"
                    ),
                ),
                (
                    "sub_product_version",
                    models.CharField(
                        default="1", max_length=10, verbose_name="副本商品版本"
                    ),
                ),
                (
                    "cate_leaf_id",
                    models.CharField(max_length=32, verbose_name="叶子分类ID"),
                ),
                (
                    "brand_id",
                    models.CharField(
                        default="596120136", max_length=128, verbose_name="品牌ID"
                    ),
                ),
                (
                    "pre_sell_config",
                    models.CharField(
                        default="2", max_length=128, verbose_name="预售模式"
                    ),
                ),
                (
                    "reduce_type",
                    models.CharField(
                        default="1", max_length=128, verbose_name="减库存方式"
                    ),
                ),
                (
                    "freight_id",
                    models.CharField(
                        default="0", max_length=128, verbose_name="包邮方式"
                    ),
                ),
                ("properties", models.JSONField(verbose_name="属性配置")),
                (
                    "extra",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="额外配置"
                    ),
                ),
                ("remark", models.TextField(default="", verbose_name="备注信息")),
                (
                    "post_data",
                    models.JSONField(default=dict, verbose_name="请求的数据"),
                ),
                (
                    "state",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "等待图片审核"),
                            (1, "图片审核失败"),
                            (2, "等待商品上传"),
                            (4, "商品上传成功"),
                            (5, "商品上传失败"),
                            (6, "取消上传"),
                        ],
                        default=0,
                        verbose_name="状态",
                    ),
                ),
                (
                    "error_code",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="失败Code",
                    ),
                ),
                (
                    "error_reason",
                    models.TextField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="失败原因",
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "cancel_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="取消人"
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        verbose_name="关联商品",
                    ),
                ),
                (
                    "sub_product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.subproduct",
                        to_field="product_id",
                        verbose_name="关联副商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "上传任务",
                "verbose_name_plural": "上传任务",
            },
        ),
        migrations.CreateModel(
            name="DataShopMaterial",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "material_type",
                    models.SmallIntegerField(
                        choices=[(1, "图片"), (2, "视频")],
                        default=1,
                        verbose_name="素材类型",
                    ),
                ),
                (
                    "request_id",
                    models.CharField(
                        default=products.models.uuid_generator,
                        max_length=255,
                        unique=True,
                        verbose_name="请求ID",
                    ),
                ),
                (
                    "material_state",
                    models.SmallIntegerField(default=-1, verbose_name="素材状态"),
                ),
                (
                    "audit_state",
                    models.SmallIntegerField(
                        choices=[
                            (1, "待审核"),
                            (2, "审核中"),
                            (3, "审核通过"),
                            (4, "审核拒绝"),
                        ],
                        default=1,
                        verbose_name="审核状态",
                    ),
                ),
                (
                    "byte_url",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="素材url",
                    ),
                ),
                (
                    "is_new",
                    models.BooleanField(default=False, verbose_name="是否为新文件"),
                ),
                (
                    "material_id",
                    models.CharField(
                        db_index=True,
                        max_length=255,
                        unique=True,
                        verbose_name="素材ID",
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="素材名称")),
                (
                    "origin_url",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="素材url",
                    ),
                ),
                (
                    "size",
                    models.PositiveSmallIntegerField(
                        default=0, verbose_name="文件大小(KB)"
                    ),
                ),
                (
                    "photo_info",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="图片信息"
                    ),
                ),
                (
                    "video_info",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="视频信息"
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="平台创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="平台更新时间"
                    ),
                ),
                (
                    "delete_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="平台删除时间"
                    ),
                ),
                (
                    "error_code",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="失败Code",
                    ),
                ),
                (
                    "error_reason",
                    models.TextField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="失败原因",
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "data_shop",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.datashop",
                        verbose_name="所属店铺",
                    ),
                ),
                (
                    "folder",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.datashopmaterialfolder",
                        to_field="folder_id",
                        verbose_name="所在文件夹",
                    ),
                ),
                (
                    "task",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.datashopuploadtasks",
                        to_field="task_id",
                        verbose_name="所属任务",
                    ),
                ),
            ],
            options={
                "verbose_name": "素材中心",
                "verbose_name_plural": "素材中心",
            },
        ),
    ]
