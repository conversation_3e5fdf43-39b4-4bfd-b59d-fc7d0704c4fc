# Generated by Django 4.2.7 on 2024-03-07 03:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0111_alter_productcategorylist_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="product",
            name="origin_product",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.product",
                to_field="product_id",
                verbose_name="原商品链接",
            ),
        ),
        migrations.AddField(
            model_name="productlinkdistributor",
            name="linked_product_id",
            field=models.BigIntegerField(null=True, verbose_name="被关联商品product_id"),
        ),
    ]
