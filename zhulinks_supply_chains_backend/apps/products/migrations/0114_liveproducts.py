# Generated by Django 4.2.7 on 2024-03-20 06:22

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        (
            "products",
            "0113_remove_productlinkdistributor_unique_product_distributor_is_deleted_is_true",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="LiveProducts",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False, verbose_name="是否删除")),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("live_start_date", models.DateTimeField(verbose_name="创建时间")),
                ("live_end_date", models.DateTimeField(verbose_name="创建时间")),
                (
                    "product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        to_field="product_id",
                        verbose_name="关联商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "供应商直播商品列表",
                "verbose_name_plural": "供应商直播商品列表",
            },
        ),
    ]
