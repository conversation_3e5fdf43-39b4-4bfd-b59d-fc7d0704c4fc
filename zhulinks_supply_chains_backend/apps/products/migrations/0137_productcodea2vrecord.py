# Generated by Django 4.2.11 on 2024-03-27 03:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0136_product_has_new_code"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductCodeA2VRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product_id",
                    models.BigIntegerField(unique=True, verbose_name="主商品id"),
                ),
                (
                    "old_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=20,
                        null=True,
                        verbose_name="原货号",
                    ),
                ),
                (
                    "new_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=20,
                        null=True,
                        verbose_name="新货号",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品货号A2V记录表",
                "verbose_name_plural": "商品货号A2V记录表",
            },
        ),
    ]
