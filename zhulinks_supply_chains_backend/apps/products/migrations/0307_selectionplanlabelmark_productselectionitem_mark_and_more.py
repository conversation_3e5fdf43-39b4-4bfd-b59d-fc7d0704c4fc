# Generated by Django 5.0.8 on 2024-10-08 08:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0306_distributormarketsubproductrelate_create_date_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SelectionPlanLabelMark",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=12, unique=True, verbose_name="标签名称"
                    ),
                ),
                (
                    "order",
                    models.PositiveSmallIntegerField(
                        default=0, help_text="从小到大排序", verbose_name="排序"
                    ),
                ),
                ("display", models.BooleanField(default=True, verbose_name="是否显示")),
            ],
            options={
                "verbose_name": "货盘商品标记配置项",
                "verbose_name_plural": "货盘商品标记配置项",
                "ordering": ("order",),
            },
        ),
        migrations.AddField(
            model_name="productselectionitem",
            name="mark",
            field=models.JSONField(
                blank=True, default=list, null=True, verbose_name="货盘标记列表"
            ),
        ),
        migrations.AddField(
            model_name="productselectionitem",
            name="read_users",
            field=models.JSONField(
                blank=True,
                default=list,
                null=True,
                verbose_name="查看过的用户user_id列表",
            ),
        ),
    ]
