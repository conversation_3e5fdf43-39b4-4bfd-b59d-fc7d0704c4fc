# Generated by Django 4.2.7 on 2024-01-23 03:28

from django.db import migrations, models
from scripts.insert_delete_reasons import exec_insert


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0098_productcategorylist_create_user_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="PlanItemDeleteReasonChoices",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=128, verbose_name="删除文本")),
                ("enable", models.BooleanField(default=False, verbose_name="是否启用")),
                (
                    "order",
                    models.PositiveSmallIntegerField(default=0, verbose_name="排序,数字越小排越前"),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "货盘商品删除理由标签",
                "verbose_name_plural": "货盘商品删除理由标签",
                "ordering": ("order",),
            },
        ),
        migrations.RunPython(exec_insert),
    ]
