# Generated by Django 5.0.8 on 2024-08-14 09:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0274_historicalstockkeepingunit_order_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="productmodhistory",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True, db_index=True, verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="productmodhistory",
            name="create_user",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=32,
                null=True,
                verbose_name="创建用户",
            ),
        ),
        migrations.AlterField(
            model_name="productmodhistory",
            name="mod_field_name",
            field=models.CharField(
                db_index=True, default="", max_length=64, verbose_name="变动的字段"
            ),
        ),
    ]
