# Generated by Django 5.0.6 on 2024-08-28 02:48

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0285_productcategorysalesrankhistory_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="productcategorysalesrankhistory",
            options={
                "verbose_name": "分类销量统计历史排行榜",
                "verbose_name_plural": "分类销量统计历史排行榜",
            },
        ),
        migrations.AlterModelOptions(
            name="productsalesrankhistory",
            options={"verbose_name": "销量统计历史排行榜", "verbose_name_plural": "销量统计历史排行榜"},
        ),
        migrations.AddField(
            model_name="frontproductcategorylist",
            name="is_big_image",
            field=models.BooleanField(default=False, verbose_name="是否大图"),
        ),
    ]
