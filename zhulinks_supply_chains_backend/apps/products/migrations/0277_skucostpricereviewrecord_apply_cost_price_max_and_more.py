# Generated by Django 5.0.6 on 2024-08-20 03:46

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0276_skucostpricereviewrecord_batch_id_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="skucostpricereviewrecord",
            name="apply_cost_price_max",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                max_digits=10,
                verbose_name="申请时候SKU的推广价批量时的最大值",
            ),
        ),
    ]
