# Generated by Django 5.0.6 on 2024-06-28 10:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "products",
            "0250_remove_productcomments_unique_product_batch_name_and_comment_and_more",
        ),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="productcomments",
            name="unique_sku_batch_name_and_comment",
        ),
        migrations.AddField(
            model_name="productcomments",
            name="spec_code",
            field=models.CharField(
                db_index=True, default=1, max_length=128, verbose_name="商品编码"
            ),
            preserve_default=False,
        ),
        migrations.AddConstraint(
            model_name="productcomments",
            constraint=models.UniqueConstraint(
                fields=("sku", "spec_code", "batch_name", "comment"),
                name="unique_sku_spec_code_batch_name_and_comment",
            ),
        ),
    ]
