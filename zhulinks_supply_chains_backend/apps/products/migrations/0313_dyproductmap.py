# Generated by Django 5.0.8 on 2024-10-15 12:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0312_alter_similarproductrelate_unique_together_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="DyProductMap",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "dy_sku_id",
                    models.CharField(
                        db_index=True, max_length=128, verbose_name="抖店SKU_ID"
                    ),
                ),
                (
                    "shop_id",
                    models.CharField(
                        db_index=True, max_length=128, verbose_name="抖店SKU_ID"
                    ),
                ),
                (
                    "dy_product_id",
                    models.CharField(
                        db_index=True, max_length=128, verbose_name="抖店商品ID"
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        to_field="product_id",
                        verbose_name="主商品",
                    ),
                ),
                (
                    "sku",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.stockkeepingunit",
                        verbose_name="关联sku",
                    ),
                ),
                (
                    "sub_product",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.subproduct",
                        to_field="product_id",
                        verbose_name="副本商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "珠凌-抖店商品映射",
                "verbose_name_plural": "珠凌-抖店商品映射",
                "unique_together": {
                    (
                        "product",
                        "sub_product",
                        "sku",
                        "dy_sku_id",
                        "shop_id",
                        "dy_product_id",
                    )
                },
            },
        ),
    ]
