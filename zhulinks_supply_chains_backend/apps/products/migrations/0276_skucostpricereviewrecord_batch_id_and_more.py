# Generated by Django 5.0.6 on 2024-08-19 07:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0275_alter_productmodhistory_create_date_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="skucostpricereviewrecord",
            name="batch_id",
            field=models.CharField(
                blank=True, default="", max_length=150, null=True, verbose_name="批量id"
            ),
        ),
        migrations.CreateModel(
            name="BatchSkuCostPriceRelate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "batch_id",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=150,
                        null=True,
                        verbose_name="批量id",
                    ),
                ),
                (
                    "sku",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.stockkeepingunit",
                        to_field="sku_id",
                        verbose_name="关联SKU",
                    ),
                ),
            ],
        ),
    ]
