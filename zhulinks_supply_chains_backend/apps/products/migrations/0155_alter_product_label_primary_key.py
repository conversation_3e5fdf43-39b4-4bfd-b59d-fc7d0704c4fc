# -*- coding: utf-8 -*-
# Generated by afei 4.2.11 on 2024-04-18 17:26
from django.db import migrations
from django.db import connection


def alter_id_seq(*args, **kwargs):
    from products.models import ProductLabels

    label = ProductLabels.objects.order_by("-id").only("id").first()
    max_id = 10000
    if not label or label.id < max_id:
        with connection.cursor() as cursor:
            cursor.execute("""SELECT pg_catalog.setval('"public"."products_productlabels_id_seq"', %s, true);""", [max_id])
        print(f"----刷新max_id:{max_id}----")


class Migration(migrations.Migration):
    """
    从10000开始自增labels_id
    """

    dependencies = [
        ("products", "0154_remove_product_labels_and_more"),
    ]

    operations = [migrations.RunPython(alter_id_seq)]
