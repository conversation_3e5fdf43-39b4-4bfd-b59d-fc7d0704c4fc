# Generated by Django 5.0.8 on 2024-08-26 02:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0282_alter_stockkeepingunit_options"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductSalesRankHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("product_pk", models.IntegerField(verbose_name="商品主键")),
                (
                    "sales",
                    models.IntegerField(db_index=True, default=0, verbose_name="销量"),
                ),
                ("rank", models.IntegerField(verbose_name="排位")),
                ("calc_date", models.DateField(db_index=True, verbose_name="统计日期")),
            ],
            options={
                "verbose_name": "历史排行榜",
                "verbose_name_plural": "历史排行榜",
            },
        ),
        migrations.Alter<PERSON>ield(
            model_name="productsalescount",
            name="calc_date",
            field=models.DateField(db_index=True, verbose_name="日期"),
        ),
        migrations.Alter<PERSON>ield(
            model_name="productsalescount",
            name="sales",
            field=models.IntegerField(db_index=True, default=0, verbose_name="销量"),
        ),
    ]
