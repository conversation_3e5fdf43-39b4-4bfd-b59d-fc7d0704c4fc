# Generated by Django 5.0.8 on 2024-12-09 13:54

import django.db.models.deletion
import products.models_v2.overseas_product
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0358_productdistributepricesettings_become_history"),
    ]

    operations = [
        migrations.CreateModel(
            name="OverseasProductMaterial",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=128, unique=True, verbose_name="材质名称"
                    ),
                ),
            ],
            options={
                "verbose_name": "海外榜单商品材质",
                "verbose_name_plural": "海外榜单商品材质",
            },
        ),
        migrations.CreateModel(
            name="OverseasProductStyle",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=128, unique=True, verbose_name="款式名称"
                    ),
                ),
            ],
            options={
                "verbose_name": "海外榜单商品款式",
                "verbose_name_plural": "海外榜单商品款式",
            },
        ),
        migrations.CreateModel(
            name="OverseasProduct",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product_id",
                    models.BigIntegerField(
                        default=products.models_v2.overseas_product.external_product_id_generator,
                        unique=True,
                        verbose_name="商品id",
                    ),
                ),
                (
                    "platform_name",
                    models.CharField(max_length=128, verbose_name="平台名称"),
                ),
                (
                    "platform_product_id",
                    models.CharField(max_length=128, verbose_name="平台商品id"),
                ),
                (
                    "currency",
                    models.CharField(
                        default="USD", max_length=3, verbose_name="基础货币"
                    ),
                ),
                (
                    "sales_price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="售价"
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="商品名称")),
                (
                    "brand",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=32,
                        null=True,
                        verbose_name="商品品牌",
                    ),
                ),
                (
                    "product_link",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=300,
                        null=True,
                        verbose_name="商品链接",
                    ),
                ),
                (
                    "img",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=300,
                        null=True,
                        verbose_name="商品图片",
                    ),
                ),
                ("month_sales", models.IntegerField(default=0, verbose_name="月销量")),
                ("sales", models.IntegerField(default=0, verbose_name="总销量")),
                (
                    "comment_count",
                    models.IntegerField(default=0, verbose_name="评论数"),
                ),
                (
                    "positive_comment_rate",
                    models.DecimalField(
                        decimal_places=2, default=0, max_digits=6, verbose_name="好评率"
                    ),
                ),
                (
                    "publish_date",
                    models.DateTimeField(
                        blank=True, default=None, null=True, verbose_name="上架时间"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "material",
                    models.ForeignKey(
                        blank=True,
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.overseasproductmaterial",
                        verbose_name="关联材质",
                    ),
                ),
                (
                    "style",
                    models.ForeignKey(
                        blank=True,
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.overseasproductstyle",
                        verbose_name="关联款式",
                    ),
                ),
            ],
            options={
                "verbose_name": "海外榜单商品",
                "verbose_name_plural": "海外榜单商品",
                "unique_together": {("platform_name", "platform_product_id")},
            },
        ),
    ]
