# Generated by Django 5.0.6 on 2024-07-01 06:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "products",
            "0253_remove_productcomments_unique_sku_spec_code_batch_name_and_comment_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="ProductAfterSalesCount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("count", models.IntegerField(default=0, verbose_name="退货量")),
                ("calc_date", models.DateField(verbose_name="日期")),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        verbose_name="商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品每日售后数量统计",
                "verbose_name_plural": "商品每日售后数量统计",
            },
        ),
        migrations.AddConstraint(
            model_name="productaftersalescount",
            constraint=models.UniqueConstraint(
                fields=("product", "calc_date"), name="unique_product_as_sales_date"
            ),
        ),
    ]
