# Generated by Django 5.0.3 on 2024-06-18 10:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0228_historicalproduct"),
    ]

    operations = [
        migrations.<PERSON>ameField(
            model_name="historicalproduct",
            old_name="attr_options",
            new_name="attr_options_data",
        ),
        migrations.RenameField(
            model_name="historicalproduct",
            old_name="gifts",
            new_name="gifts_data",
        ),
        migrations.RenameField(
            model_name="historicalproduct",
            old_name="labels",
            new_name="labels_data",
        ),
        migrations.AddField(
            model_name="historicalproduct",
            name="category_data",
            field=models.JSONField(
                blank=True, default=dict, null=True, verbose_name="分类数据"
            ),
        ),
        migrations.AddField(
            model_name="historicalproduct",
            name="company_data",
            field=models.JSONField(
                blank=True, default=dict, null=True, verbose_name="公司数据"
            ),
        ),
    ]
