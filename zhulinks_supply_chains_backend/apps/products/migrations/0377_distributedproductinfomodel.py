# Generated by Django 5.1.2 on 2025-02-13 07:22

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0376_historicalproduct_max_distributor_promotion_price_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="DistributedProductInfoModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="是否删除"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="最后更新人"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product_name",
                    models.CharField(max_length=80, verbose_name="商品名称"),
                ),
                (
                    "main_images",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            default=None, max_length=300, verbose_name="商品主图"
                        ),
                        size=15,
                        verbose_name="主图列表",
                    ),
                ),
                (
                    "parent_product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        verbose_name="父商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "分销市场商品副本信息",
                "verbose_name_plural": "分销市场商品副本信息",
            },
        ),
    ]
