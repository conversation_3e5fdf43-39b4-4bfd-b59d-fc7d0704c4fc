# Generated by Django 5.1.2 on 2024-11-07 09:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0339_doudianproduct_can_not_combine_reason_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="EXProductsAssociationModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_by",
                    models.Char<PERSON>ield(
                        blank=True, max_length=32, null=True, verbose_name="创建用户"
                    ),
                ),
                (
                    "updated_by",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="更新用户"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "ex_product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="external_associations",
                        to="products.doudiansku",
                        to_field="sku_id",
                        verbose_name="外部商品ID",
                    ),
                ),
                (
                    "inner_product",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="internal_associations",
                        to="products.stockkeepingunit",
                        to_field="sku_id",
                        verbose_name="内部商品ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "外部商品与内部商品关联",
                "verbose_name_plural": "外部商品与内部商品关联",
            },
        ),
    ]
