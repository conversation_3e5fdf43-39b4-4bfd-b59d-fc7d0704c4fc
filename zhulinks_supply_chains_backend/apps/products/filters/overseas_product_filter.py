# -*- coding: utf-8 -*-
from common.basic import BaseDateFilterSet, RangeFilter
from products.models_v2.overseas_product import OverseasProduct
import django_filters


class OverseasProductListFilterSet(BaseDateFilterSet):
    sales_price = RangeFilter(field_name="sales_price")
    relate_platform_id = django_filters.NumberFilter(field_name="relate_platform_id")

    class Meta:
        model = OverseasProduct
        fields = (
            "relate_platform_id",
            "sales_price",
        )
