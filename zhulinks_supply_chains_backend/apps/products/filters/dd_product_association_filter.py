import django_filters
from django.core.exceptions import ValidationError

from companies.models import DataShop
from products.models_v2 import DouDianProduct, DouDianSKU, EXProductsAssociationModel


class DouDianProductFilter(django_filters.rest_framework.FilterSet):
    name = django_filters.CharFilter(field_name="name", lookup_expr="icontains")
    shop_name = django_filters.CharFilter(method="filter_shop_name")
    external_product_code = django_filters.CharFilter(method="filter_external_product_code")
    zhulinks_product_id = django_filters.CharFilter(method="filter_zhulinks_product_id")
    format_update_time = django_filters.DateFromToRangeFilter(field_name="format_update_time")
    association_status = django_filters.CharFilter(method="filter_association_status")
    product_id = django_filters.NumberFilter(field_name="product_id",error_messages={"invalid": "请输入有效的数字 ID。"})

    class Meta:
        model = DouDianProduct
        fields = ["product_id", "name", "shop_name", "external_product_code", "zhulinks_product_id", "format_update_time", "association_status"]

    def filter_shop_name(self, queryset, name, value):
        matching_shop_ids = DataShop.objects.filter(name__icontains=value).values_list("id", flat=True)
        matching_shop_ids = [int(shop_id) for shop_id in matching_shop_ids]
        return queryset.filter(shop_id__in=matching_shop_ids)

    def filter_external_product_code(self, queryset, name, value):
        if not value.isdigit():
            raise ValidationError(f"请输入有效的数字 ID.")
        matching_ddsku_ids = DouDianSKU.objects.filter(sku_id=value).values_list("product_id", flat=True)
        return queryset.filter(product_id__in=matching_ddsku_ids)

    def filter_zhulinks_product_id(self, queryset, name, value):
        if not value.isdigit():
            raise ValidationError(f"请输入有效的数字 ID.")
        ex_product_id = set(EXProductsAssociationModel.objects.filter(inner_product__product__product_id=value).values_list("ex_product__product_id", flat=True))
        return queryset.filter(product_id__in=ex_product_id)

    def filter_association_status(self, queryset, name, value):
        ex_product_id = set(EXProductsAssociationModel.objects.values_list("ex_product_spu__product_id", flat=True))
        if value == "已关联":
            return queryset.filter(product_id__in=ex_product_id)
        elif value == "未关联":
            return queryset.exclude(product_id__in=ex_product_id)
        else:
            return queryset
