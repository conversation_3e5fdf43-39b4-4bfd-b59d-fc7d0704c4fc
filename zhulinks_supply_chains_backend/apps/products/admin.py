# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-06-09 10:36:14
# @Last Modified by:   <PERSON> <PERSON><PERSON><PERSON>
# @Last Modified time: 2023-12-20 17:19:03
from django.contrib import admin
from django.contrib.admin import RelatedOnlyFieldListFilter
from django.utils.translation import gettext_lazy as _
from import_export import resources, fields
from import_export.admin import ImportExportModelAdmin
from import_export.widgets import ForeignKeyWidget

from common.models import DownloadTasks
from utils.admin_optimization import NoCountPaginator
from .models import (
    Product,
    StockKeepingUnit,
    ProductAttrList,
    ProductCategoryList,
    CategoryAttrMembership,
    ProductAttrOption,
    ProductUnit,
    SubProduct,
    ProductSelectionPlan,
    ProductSelectionItem,
    ProductAddress,
    ProductAdvicePrice,
    ProductSelectionPlanRecord,
    ProductModHistory,
    LiveNeeds,
    SysProductModHistory,
    PlanItemDeleteReasonChoices,
    SpecsKey,
    SpecsV<PERSON>ue,
    HostingProducts,
    ProductRecord,
    SkuRecord,
    SubProductRecord,
    SubSkuRecord,
    ProductCodeA2VRecord,
    ProductLinkDistributor,
    FailedUploadProductJST,
    ProductLabelTypes,
    ProductLabels,
    ProductLabelsRelate,
    ProductLabelsRule,
    JSTShopProduct,
    ProductDyLabels,
    ProductSalesCount,
    ProductDBSalesCount,
    HandCard,
    ProductGift,
    ProductReviewedCostPrice,
    HistoryPrice,
    ProductSelectionPlanCategory,
    ProductReview,
    CostItemConfig,
    CostSubItem,
    EstimatedAcceptanceRateRules,
    ProductSelectionItemSKU,
    DataShopUploadTasks,
    DataShopProductMap,
    ProductStateReason,
    ProductRefundRecord,
    ProductComments,
    ProductAfterSalesCount,
    TempProduct,
    TempProductDetail,
    TempProductMap,
    QAReviewQuestionType,
    QAReviewQuestion,
    SubStockKeepingUnit,
    GoodProductNote,
    GoodProductNoteComment,
    SKUSalesCount,
    ProductReviewProcess,
    SKUCostPriceReviewRecord,
    ProductStateReviewRecord,
    SKUInventoryReviewRecord,
    ProductRefundRateHistory,
    UserWishList,
    DistributorMarketSubProductRelate,
    ProductAttrValues,
    SelectionPlanLabelMark,
    SelectionPlanLabelMarkRelate,
    SimilarProductRelate,
)
from .models_v2 import ProductConfig, DistributedProductInfoModel
from .models_v2.inventory import DistributorWarehouseInventory
from .models_v2.overseas_product import OverseasProduct


# Register your models here.
@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    raw_id_fields = (
        "origin_product",
        "company",
        "address",
        "owner",
    )
    list_display = (
        "product_id",
        "product_type",
        "name",
        "category",
        "code",
        "brand",
        "company",
        "sales",
        "state",
        # "state_reason",
        "cross_border_attr",
        "unit",
        "tags",
        "create_date",
        "update_date",
    )

    list_filter = (
        # "name",
        # "category",
        ("company", RelatedOnlyFieldListFilter),
        "brand",
        "cross_border_attr",
    )

    search_fields = (
        "name",
        "product_id",
        "company__name",
        "brand__name",
        "doudian_id",
        "code",
    )
    readonly_fields = (
        "product_id",
        "create_date",
        "update_date",
    )
    exclude = (
        "specs",
        "specs_value",
        "spec_lists",
        "address",
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("unit", "company")


@admin.register(StockKeepingUnit)
class StockKeepingUnitAdmin(admin.ModelAdmin):
    list_display = (
        "product",
        "sku_id",
        "physical_inventory",
        "safety_inventory",
        "external_sku_id",
        "spec_code",
        "cost_price",
        "retail_price",
        "weight",
        "sales",
    )
    # list_filter = ("product",)
    search_fields = (
        "sku_id",
        "spec_code",
        "product__name",
        "product__product_id",
        "external_sku_id",
    )
    readonly_fields = (
        "product",
        "sku_id",
        "specs",
    )
    exclude = (
        "specs_name",
        "specs_value",
    )


@admin.register(ProductAttrList)
class ProductAttrListAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "attr_type",
        # "values",
        "searchable",
        "spec_usable",
        "customizable",
        "not_null",
        "display_frontend",
        "search_key",
        "enable",
        "is_deleted",
        "create_date",
        "update_date",
    )
    list_filter = (
        "name",
        "searchable",
        "spec_usable",
        "customizable",
        "not_null",
        "display_frontend",
        "attr_type",
        "enable",
    )
    search_fields = ("name",)
    readonly_fields = ("create_date", "update_date")


@admin.register(ProductCategoryList)
class ProductCategoryListAdmin(admin.ModelAdmin):
    # raw_id_fields = ("parent",)

    list_display = (
        "id",
        "name",
        "level",
        "order",
        "parent",
        "JST_id",
    )
    search_fields = (
        "id",
        "name",
    )
    list_filter = ("level",)
    autocomplete_fields = ("parent",)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("parent")


@admin.register(CategoryAttrMembership)
class CategoryAttrMembershipAdmin(admin.ModelAdmin):
    list_display = (
        "category",
        "attr",
        "create_date",
        "update_date",
    )
    list_filter = ("category", "attr")
    search_fields = ("category__id", "category__name", "attr__name")
    readonly_fields = ("create_date", "update_date")


@admin.register(ProductAttrOption)
class ProductAttrOptionAdmin(admin.ModelAdmin):
    show_full_result_count = False
    paginator = NoCountPaginator
    list_per_page = 50

    list_select_related = ("product",)

    list_filter = (
        "attr_id",
        "attr__name",
    )
    search_fields = (
        "attr__id",
        "attr__name",
        "product__id",
        "product__product_id",
        "product__name",
    )
    readonly_fields = (
        "attr",
        "product",
    )


@admin.register(ProductUnit)
class ProductUnitAdmin(admin.ModelAdmin):
    list_display = ("name",)
    list_filter = ("name",)
    search_fields = ("name",)


@admin.register(SubProduct)
class SubProductAdmin(admin.ModelAdmin):
    raw_id_fields = ("parent_product",)
    list_display = (
        "name",
        "product_id",
        "parent_product",
        "create_date",
        "update_date",
    )
    list_filter = ("owner",)
    search_fields = (
        "name",
        "product_id",
        "parent_product__product_id",
        "code",
        "alias_code",
        "parent_product__code",
        "parent_product__company__name",
        "owner__name",
    )
    ordering = ("-create_date",)


@admin.register(ProductSelectionPlan)
class ProductSelectionPlanAdmin(admin.ModelAdmin):
    list_display = (
        "plan_id",
        "name",
        "distributor",
        "live_date_start",
        "live_date_end",
        "state",
        "create_user",
        "create_date",
        "update_user",
        "update_date",
    )
    list_filter = ("state",)
    search_fields = (
        "plan_id",
        "name",
        "distributor__name",
    )

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related("distributor")


@admin.register(ProductSelectionPlanCategory)
class ProductSelectionPlanCategoryAdmin(admin.ModelAdmin):
    search_fields = ("category_id",)


@admin.register(ProductSelectionItem)
class ProductSelectionItemAdmin(admin.ModelAdmin):
    show_full_result_count = False
    # paginator = NoCountPaginator
    list_per_page = 20

    list_select_related = ("selection_plan", "product")

    list_display = (
        "selection_plan",
        "product",
        "order",
        "map_order",
        "remark",
        "is_deleted",
        "create_user",
        "create_date",
        "update_user",
        "update_date",
    )
    list_filter = (
        "selection_plan",
        "is_deleted",
    )
    search_fields = (
        "selection_plan__plan_id",
        "selection_plan__name",
        "product__name",
    )
    exclude = ("plan_category",)
    autocomplete_fields = ("selection_plan", "sub_product", "product")


@admin.register(ProductAddress)
class AddressAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "create_date",
        "update_date",
    )
    search_fields = ("name",)


@admin.register(ProductAdvicePrice)
class ProductAdvicePriceAdmin(admin.ModelAdmin):
    list_display = (
        "advice_price",
        "remark",
        "create_date",
        "update_date",
    )


@admin.register(ProductSelectionPlanRecord)
class ProductSelectionPlanRecordAdmin(admin.ModelAdmin):
    show_full_result_count = False
    paginator = NoCountPaginator
    list_per_page = 50

    list_select_related = ("product",)

    list_display = (
        "id",
        "selection_plan",
        "product",
        "record_type",
        # "content",
        "remark",
        "create_user",
        "create_date",
    )
    search_fields = (
        "selection_plan__plan_id",
        "product__product_id",
        "product__name",
    )

    list_filter = ("record_type",)
    autocomplete_fields = (
        "selection_plan",
        "product",
    )
    # fields = (
    #     "record_type",
    #     "content",
    #     "remark",
    # )


@admin.register(ProductModHistory)
class ProductModHistoryAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "product",
        "sku",
        "mod_field_name",
        "old_value",
        "new_value",
        "create_user",
        "create_date",
    )
    search_fields = (
        "product__product_id",
        "product__name",
        "sku__sku_id",
        "create_user",
        "mod_field_name",
    )
    autocomplete_fields = ("product", "sku")
    list_filter = ("mod_field_name",)


@admin.register(SysProductModHistory)
class SysProductModHistoryAdmin(admin.ModelAdmin):
    show_full_result_count = False
    paginator = NoCountPaginator
    list_per_page = 50

    list_select_related = ("product", "sku")

    list_display = (
        "id",
        "product",
        "sku",
        "mod_field_name",
        "old_value",
        "new_value",
        "create_user",
        "create_date",
    )
    search_fields = (
        "product__product_id",
        "product__name",
        "sku__sku_id",
        "create_user",
        "mod_field_name",
    )

    fields = (
        "mod_field_name",
        "old_value",
        "new_value",
    )


@admin.register(LiveNeeds)
class LiveNeedsAdmin(admin.ModelAdmin):
    list_display = (
        "needs_id",
        "theme",
        "live_date",
        "remark",
        "state",
        "distributor",
        "create_user",
        "update_user",
        "confirm_user",
        "create_date",
        "update_date",
        "confirm_date",
    )

    search_fields = (
        "theme",
        "needs_id",
        "remark",
        "distributor__name",
        "distributor__distributor_id",
        "distributor__distributor_id",
        "create_user",
        "confirm_user",
    )


@admin.register(PlanItemDeleteReasonChoices)
class PlanItemDeleteChoicesAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "enable", "order")
    search_fields = ("name",)


@admin.register(SpecsKey)
class SpecsKeyAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "display",
        "data_source",
        "create_user",
    )
    search_fields = (
        "name",
        "id",
        "data_source",
    )
    list_filter = ("data_source",)


@admin.register(SpecsValue)
class SpecsValueAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "value",
        "own_key",
        "data_source",
    )

    def own_key(self, obj):
        return obj.spec_key.name + f"({obj.spec_key_id})"

    own_key.short_description = "所属规格名"

    search_fields = (
        "value",
        "spec_key__name",
    )


@admin.register(HostingProducts)
class HostingProductsAdmin(admin.ModelAdmin):
    raw_id_fields = ("product",)
    list_display = (
        "hosting_id",
        "product",
        "live_start_date",
        "live_end_date",
        "state",
        "expire_date",
    )

    search_fields = (
        "hosting_id",
        "product__name",
        "product_id",
    )


@admin.register(ProductRecord)
class ProductRecordAdmin(ImportExportModelAdmin):
    list_display = (
        "product_id",
        "old_code",
        "new_code",
    )
    search_fields = (
        "product_id",
        "old_code",
        "new_code",
    )
    ordering = ("product_id",)


class SkuRecordResource(resources.ModelResource):
    product_id = fields.Field(column_name="product_id", attribute="product_record", widget=ForeignKeyWidget(ProductRecord, "product_id"))
    old_code = fields.Field(column_name="old_code", attribute="product_record", widget=ForeignKeyWidget(ProductRecord, "old_code"))
    new_code = fields.Field(column_name="new_code", attribute="product_record", widget=ForeignKeyWidget(ProductRecord, "new_code"))

    class Meta:
        model = SkuRecord
        fields = (
            "sku_id",
            "old_spec_code",
            "new_spec_code",
            "product_id",
            "old_code",
            "new_code",
        )
        export_order = (
            "product_id",
            "old_code",
            "new_code",
            "sku_id",
            "old_spec_code",
            "new_spec_code",
        )

    def dehydrate_product_id(self, sku_record):
        return sku_record.product_record.product_id

    def dehydrate_old_code(self, sku_record):
        return sku_record.product_record.old_code

    def dehydrate_new_code(self, sku_record):
        return sku_record.product_record.new_code


@admin.register(SkuRecord)
class SkuRecordAdmin(ImportExportModelAdmin):
    resource_class = SkuRecordResource
    list_display = (
        "product_record",
        "sku_id",
        "old_spec_code",
        "new_spec_code",
    )
    search_fields = (
        "product_record__product_id",
        "sku_id",
        "old_spec_code",
        "new_spec_code",
    )
    raw_id_fields = ("product_record",)


@admin.register(SubProductRecord)
class SubProductRecordAdmin(ImportExportModelAdmin):
    list_display = (
        "product_id",
        "old_parent_product_id",
        "new_parent_product_id",
        "old_code",
        "new_code",
    )
    search_fields = (
        "product_id",
        "old_parent_product_id",
        "new_parent_product_id",
        "old_code",
        "new_code",
    )
    ordering = ("product_id",)


class SubSkuRecordResource(resources.ModelResource):
    product_id = fields.Field(column_name="product_id", attribute="product_record", widget=ForeignKeyWidget(SubProductRecord, "product_id"))
    old_code = fields.Field(column_name="old_code", attribute="product_record", widget=ForeignKeyWidget(SubProductRecord, "old_code"))
    new_code = fields.Field(column_name="new_code", attribute="product_record", widget=ForeignKeyWidget(SubProductRecord, "new_code"))

    class Meta:
        model = SubSkuRecord
        fields = ("sku_id", "old_spec_code", "new_spec_code", "product_id", "old_code", "new_code")
        export_order = ("product_id", "old_code", "new_code", "sku_id", "old_spec_code", "new_spec_code")

    def dehydrate_product_id(self, sub_sku_record):
        return sub_sku_record.product_record.product_id

    def dehydrate_old_code(self, sub_sku_record):
        return sub_sku_record.product_record.old_code

    def dehydrate_new_code(self, sub_sku_record):
        return sub_sku_record.product_record.new_code


@admin.register(SubSkuRecord)
class SubSkuRecordAdmin(ImportExportModelAdmin):
    resource_class = SubSkuRecordResource
    list_display = (
        "product_record",
        "sku_id",
        "old_spec_code",
        "new_spec_code",
    )
    search_fields = (
        "product_record__product_id",
        "sku_id",
        "old_spec_code",
        "new_spec_code",
    )
    raw_id_fields = ("product_record",)


@admin.register(ProductCodeA2VRecord)
class ProductCodeA2VRecordAdmin(ImportExportModelAdmin):
    list_display = (
        "product_id",
        "old_code",
        "new_code",
    )
    list_filter = (
        "old_code",
        "new_code",
    )
    search_fields = (
        "product_id",
        "old_code",
        "new_code",
    )
    ordering = ("product_id",)


@admin.register(ProductLinkDistributor)
class ProductLinkDistributorAdmin(admin.ModelAdmin):
    raw_id_fields = (
        "product",
        "distributor",
    )
    list_display = (
        "id",
        "product",
        "distributor",
        "linked_product_id",
        "is_sub_linked",
        "code",
        "order",
        "is_deleted",
        "create_user",
        "update_user",
        "create_date",
        "update_date",
    )
    list_filter = (
        "is_deleted",
        "is_sub_linked",
        "create_date",
        "update_date",
    )
    search_fields = (
        "code",
        "product__product_id",
        "distributor__distributor_id",
    )
    ordering = ("-order",)
    readonly_fields = (
        "create_date",
        "update_date",
    )


@admin.register(FailedUploadProductJST)
class FailedUploadProductJSTAdmin(admin.ModelAdmin):
    list_display = ("product_id", "sub_product_id", "reason", "create_date")
    search_fields = ("product_id", "sub_product_id")
    readonly_fields = ("create_date",)


@admin.register(ProductLabelTypes)
class ProductLabelTypesAdmin(admin.ModelAdmin):
    list_display = ("name", "order")
    search_fields = ("name",)


@admin.register(ProductLabels)
class ProductLabelsAdmin(admin.ModelAdmin):
    list_display = ("name", "l_type", "order", "display", "can_edit")
    search_fields = ("name",)


@admin.register(ProductLabelsRule)
class ProductLabelsRuleAdmin(admin.ModelAdmin):
    list_display = (
        "label",
        "value",
        "rule",
        "remark",
    )


@admin.register(ProductLabelsRelate)
class ProductLabelsRelateAdmin(admin.ModelAdmin):
    list_per_page = 20
    list_display = (
        "product",
        "label",
        "act_value",
        "value",
        "become_history",
        "label_date",
        "label_week",
        "distributor_letters",
    )
    search_fields = ("product__product_id", "product__name")
    list_filter = ("label", "label_date")
    date_hierarchy = "label_date"
    autocomplete_fields = ("product",)


@admin.register(JSTShopProduct)
class JSTShopProductAdmin(admin.ModelAdmin):
    list_display = [field.name for field in JSTShopProduct._meta.get_fields() if field.name not in ["dy_url", "dy_pic", "pic"]]
    search_fields = (
        "shop_name",
        "dy_product_id",
        "dy_sku_id",
        "code",
        "sku_spec_code",
        "raw_sku_id",
    )


@admin.register(ProductDyLabels)
class ProductDyLabelsAdmin(admin.ModelAdmin):
    list_display = ("product", "dy_product_id", "create_date", "update_date")
    autocomplete_fields = ("product",)
    search_fields = (
        "product__name",
        "product__product_id",
        "dy_product_id",
    )


@admin.register(ProductSalesCount)
class ProductSalesCountAdmin(admin.ModelAdmin):
    list_display = ("product", "calc_date", "sales")
    search_fields = (
        "product__product_id",
        "product__code",
        "product__name",
    )
    list_filter = ("calc_date",)
    autocomplete_fields = ("product",)


@admin.register(ProductDBSalesCount)
class ModelNameAdmin(admin.ModelAdmin):
    list_display = (
        "product",
        "sales",
        "sales_amount",
        "distributor",
        "calc_date",
        "create_date",
        "update_date",
    )

    search_fields = (
        "product__product_id",
        "product__code",
    )
    list_filter = ("distributor",)

    autocomplete_fields = ("product", "distributor")


@admin.register(HandCard)
class HandCardAdmin(admin.ModelAdmin):
    raw_id_fields = ("product",)

    list_display = (
        "product",
        "recommendations",
        "core_selling_point",
        "logistics_company",
        "shipping_list",
    )
    list_filter = (
        "aftersales_service",
        "ship_time",
        "logistics_company",
    )
    search_fields = (
        "product__name",
        "recommendations",
        "core_selling_point",
    )
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "product",
                    "recommendations",
                    "core_selling_point",
                    "remark",
                    "video",
                )
            },
        ),
        (
            "Advanced options",
            {
                "classes": ("collapse",),
                "fields": (
                    "aftersales_service",
                    "ship_time",
                    "spot_goods_time",
                    "pre_sale_time",
                    "logistics_company",
                    "shipping_list",
                    "qualification_information",
                ),
            },
        ),
    )


@admin.register(ProductGift)
class ProductGiftAdmin(admin.ModelAdmin):
    raw_id_fields = (
        "product",
        "gift_product",
    )
    list_display = (
        "product",
        "gift_type",
        "name",
        "material",
        "image",
        "gift_product",
        "count",
        "is_deleted",
    )
    list_filter = ("gift_type", "is_deleted")
    search_fields = ("name", "material")
    list_editable = ("count", "is_deleted")
    # raw_id_fields = ("product", "gift_product")
    autocomplete_fields = ["product", "gift_product"]
    actions = ["mark_as_deleted"]

    # def mark_as_deleted(self, request, queryset):
    #     queryset.update(is_deleted=True)

    # mark_as_deleted.short_description = "标记为删除选中的赠品"

    # def get_queryset(self, request):
    #     # 重写此方法以包括被标记删除的赠品
    #     qs = super().get_queryset(request)
    #     return qs.filter(is_deleted=False)

    fieldsets = (
        (None, {"fields": ("product", "gift_type")}),
        (
            "赠品详情",
            {
                "fields": ("name", "material", "image", "gift_product", "count"),
                "classes": ("collapse",),
            },
        ),
        (
            "高级设置",
            {
                "fields": ("is_deleted",),
                "classes": ("collapse",),
            },
        ),
    )


@admin.register(ProductReviewedCostPrice)
class ProductReviewedCostPriceAdmin(admin.ModelAdmin):
    list_display = ("id", "product", "sku", "cost_price", "create_user", "create_date")
    search_fields = (
        "product__product_id",
        "product__name",
        "product__code",
        "sku__sku_id",
        "sku__id",
    )
    autocomplete_fields = ("product", "sku")


@admin.register(HistoryPrice)
class HistoryPriceAdmin(admin.ModelAdmin):
    list_display = ("id", "product", "sub_product", "raw_product_id", "sku", "history_price", "author", "create_date")
    search_fields = (
        "product__id",
        "product__product_id",
        "product__name",
        "product__code",
        "sku__sku_id",
        "sku__id",
        "sub_product__product_id",
        "sub_product__name",
    )
    autocomplete_fields = ("product", "sku", "sub_product")
    list_per_page = 20


@admin.register(ProductReview)
class ProductReviewAdmin(admin.ModelAdmin):
    show_full_result_count = False
    list_display = (
        "id",
        "process_id",
        "product",
        "process_level",
        "physical_inventory_exact",
        "quality_qualified",
        "price_reasonable",
        "recommended_price",
        "review_cost_price",
        "remark",
        "review_times",
        "become_history",
        "create_date",
    )

    list_filter = ("process_level",)
    autocomplete_fields = ("product", "selection_plan")
    raw_id_fields = ("process",)
    search_fields = ("product__product_id",)


@admin.register(ProductReviewProcess)
class ProductReviewProcessAdmin(admin.ModelAdmin):
    show_full_result_count = False
    list_display = (
        "product",
        "process_id",
        "become_history",
    )
    autocomplete_fields = ("product",)

    search_fields = ("product__product_id", "process_id")


class CurrentStatusFilter(admin.SimpleListFilter):
    title = _("status")
    parameter_name = "status"

    def lookups(self, request, model_admin):
        # 提供过滤选项，这里你可以根据实际需求自定义
        return (
            ("NE", _("未生效")),
            ("IE", _("生效中")),
            ("ED", _("已结束")),
        )

    def queryset(self, request, queryset):
        if self.value() == "NE":
            return queryset.filter(status="NE")
        if self.value() == "IE":
            return queryset.filter(status="IE")
        if self.value() == "ED":
            return queryset.filter(status="ED")


@admin.register(CostItemConfig)
class CostItemConfigAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "get_status",
        "effective_time",
        "price_fetching_rules_display",
    )
    list_filter = (
        "effective_time",
        CurrentStatusFilter,
    )
    search_fields = ("name",)
    fields = (
        "name",
        "remark",
        "effective_time",
        "price_fetching_rules",
        "breakeven_price_rate",
        "is_deleted",
    )
    readonly_fields = ("effective_time",)

    def get_status(self, obj):
        # 这个方法返回注解的字段值
        return obj.status

    get_status.short_description = "状态"

    def price_fetching_rules_display(self, obj):
        return ", ".join(dict(obj.PRICE_FETCHING_RULES_CHOICES)[rule] for rule in obj.price_fetching_rules)

    price_fetching_rules_display.short_description = "价格取数规则"


class CostSubItemInline(admin.TabularInline):
    model = CostSubItem
    extra = 1
    fields = (
        "start_selling_price",
        "end_selling_price",
        "total_cost",
        "variable_cost_rate_total",
    )


@admin.register(CostSubItem)
class CostSubItemAdmin(admin.ModelAdmin):
    raw_id_fields = ("cost_item_config",)
    list_display = (
        "id",
        "cost_item_config",
        "start_selling_price",
        "end_selling_price",
        "total_cost",
        "variable_cost_rate_total",
    )
    list_filter = ("cost_item_config",)
    search_fields = ("cost_item_config__name",)


@admin.register(EstimatedAcceptanceRateRules)
class EstimatedAcceptanceRateRulesAdmin(admin.ModelAdmin):
    raw_id_fields = ("cost_item_config",)
    list_display = (
        "id",
        "cost_item_config",
        "start_selling_price",
        "end_selling_price",
        "estimated_acceptance_rate",
    )
    list_filter = ("cost_item_config",)
    search_fields = ("cost_item_config__name",)


@admin.register(ProductSelectionItemSKU)
class ProductSelectionItemSKUAdmin(admin.ModelAdmin):
    raw_id_fields = ("selection_plan", "sku", "item")
    list_display = ("selection_plan", "sku", "item", "become_history", "physical_inventory", "actual_sales", "actual_price", "create_user", "update_date")
    # list_filter = ("become_history", "selection_plan", "sku", "item")
    search_fields = (
        "sku__sku_id",
        "item__product__name",
        "selection_plan__name",
        "selection_plan__plan_id",
    )

    date_hierarchy = "update_date"
    ordering = ("-update_date",)
    readonly_fields = ("create_date", "update_date")

    # fieldsets = (
    #     (None, {"fields": ("selection_plan", "sku", "item")}),
    #     ("Inventory and Sales", {"fields": ("physical_inventory", "actual_sales", "actual_price")}),
    #     ("Status", {"fields": ("become_history",)}),
    #     ("Audit Info", {"fields": ("remark", "create_user", "create_date", "update_user", "update_date")}),
    # )


@admin.register(DataShopUploadTasks)
class DataShopUploadTasksAdmin(admin.ModelAdmin):
    list_display = [
        field.name
        for field in DataShopUploadTasks._meta.fields
        if field.name
        not in [
            "material_pass",
            "raw_category",
            "properties",
            "extra",
            "post_data",
            "rule",
        ]
    ]

    autocomplete_fields = (
        "product",
        "sub_product",
    )


@admin.register(DataShopProductMap)
class DataShopProductMapAdmin(admin.ModelAdmin):
    list_display = (
        "task",
        "data_shop",
        "platform_create_time",
        "product",
        "sub_product",
        "outer_product_id",
        "platform_product_id",
        "create_date",
        "update_date",
    )
    autocomplete_fields = ("data_shop", "product", "sub_product")


@admin.register(ProductStateReason)
class ProductStateReasonAdmin(admin.ModelAdmin):
    search_fields = ("name",)
    list_display = ("id", "name", "reason_type", "enable")
    list_filter = ("reason_type",)


@admin.register(ProductRefundRecord)
class ProductRefundRecordAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "product",
        "sku",
        "record_date",
        "order_num",
        "refund_num",
        "shop_name",
        "create_date",
        "update_date",
    )
    search_fields = (
        "product__product_id",
        "product__name",
        "sku__sku_id",
        "product__code",
        "shop_name",
    )

    autocomplete_fields = (
        "product",
        "sku",
    )


def mark_as_not_display(model_admin, request, queryset):
    updated_count = queryset.update(display=False)
    model_admin.message_user(request, f"{updated_count} items marked as not display")


mark_as_not_display.short_description = "批量设置不可见"


def mark_as_display(model_admin, request, queryset):
    updated_count = queryset.update(display=True)
    model_admin.message_user(request, f"{updated_count} items marked as display")


mark_as_display.short_description = "批量设置可见"


def mark_as_op_display(model_admin, request, queryset):
    updated_count = queryset.update(display_range=["OP"])
    model_admin.message_user(request, f"{updated_count} items marked only display for OP")


mark_as_op_display.short_description = "只允许运营商看"


def mark_as_db_display(model_admin, request, queryset):
    updated_count = queryset.update(display_range=["DB"])
    model_admin.message_user(request, f"{updated_count} items marked only display for DB")


mark_as_db_display.short_description = "只允许分销商看"


def mark_as_sp_display(model_admin, request, queryset):
    updated_count = queryset.update(display_range=["SP"])
    model_admin.message_user(request, f"{updated_count} items marked only display for SP")


mark_as_sp_display.short_description = "只允许供应商看"


def mark_as_op_and_db_display(model_admin, request, queryset):
    updated_count = queryset.update(display_range=["OP", "DB"])
    model_admin.message_user(request, f"{updated_count} items marked only display for OP&DB")


mark_as_op_and_db_display.short_description = "只允许运营商、分销商看"


def mark_as_op_and_sp_display(model_admin, request, queryset):
    updated_count = queryset.update(display_range=["OP", "SP"])
    model_admin.message_user(request, f"{updated_count} items marked only display for OP&SP")


mark_as_op_and_sp_display.short_description = "只允许运营商、供应商看"


def mark_as_db_and_sp_display(model_admin, request, queryset):
    updated_count = queryset.update(display_range=["DB", "SP"])
    model_admin.message_user(request, f"{updated_count} items marked only display for DB&SP")


mark_as_db_and_sp_display.short_description = "只允许分销商、供应商看"


def mark_as_all_display(model_admin, request, queryset):
    updated_count = queryset.update(display_range=["OP", "DB", "SP"])
    model_admin.message_user(request, f"{updated_count} items marked only display for DB&SP")


mark_as_all_display.short_description = "三端可查看"


class DisplayRangeMultiSelectFieldFilter(admin.SimpleListFilter):
    title = "可见范围"
    parameter_name = "display_range"

    def lookups(self, request, model_admin):
        return ProductComments._meta.get_field("display_range").choices

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(display_range__contains=self.value())
        return queryset


@admin.register(ProductComments)
class ProductCommentsAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "product",
        "sku",
        "c_type",
        "batch_name",
        "display",
        "display_range",
        "create_date",
        "update_date",
    )

    search_fields = (
        "product__product_id",
        "product__name",
        "sku__sku_id",
        "product__code",
        "batch_name",
    )

    list_filter = ("c_type", "display", DisplayRangeMultiSelectFieldFilter)

    autocomplete_fields = ("product", "sku")

    actions = [
        mark_as_not_display,
        mark_as_display,
        mark_as_op_display,
        mark_as_db_display,
        mark_as_sp_display,
        mark_as_op_and_db_display,
        mark_as_op_and_sp_display,
        mark_as_db_and_sp_display,
        mark_as_all_display,
    ]


@admin.register(ProductAfterSalesCount)
class ProductAfterSalesCountAdmin(admin.ModelAdmin):
    list_display = (
        "product",
        "count",
        "calc_date",
        "create_date",
    )
    search_fields = (
        "product__product_id",
        "product__name",
    )
    autocomplete_fields = ("product",)


@admin.register(TempProduct)
class TempProductAdmin(admin.ModelAdmin):
    list_display = [
        field.name
        for field in TempProduct._meta.fields
        if field.name
        not in [
            "sku_specs",
            "category",
        ]
    ]
    list_filter = ("shop_id",)
    date_hierarchy = "order_date"
    search_fields = (
        "product_id",
        "name",
        "sku_id",
        "raw_spec_code",
    )


@admin.register(TempProductDetail)
class TempProductDetailAdmin(admin.ModelAdmin):
    list_display = ("id", "relate_product", "ex_order_id")
    search_fields = ("ex_order_id",)
    autocomplete_fields = ("relate_product",)


@admin.register(TempProductMap)
class TempProductMapAdmin(admin.ModelAdmin):
    list_display = [field.name for field in TempProductMap._meta.fields]
    search_fields = (
        "ex_order_id",
        "ex_sku_id",
        "sku__spec_code",
        "sku__sku_id",
    )
    autocomplete_fields = ("sub_product", "distributor", "sku")


@admin.register(QAReviewQuestionType)
class QAReviewQuestionTypeAdmin(admin.ModelAdmin):
    show_full_result_count = False
    list_display = ("id", "name", "short_desc", "enable")
    search_fields = ("name", "short_desc")


@admin.register(QAReviewQuestion)
class QAReviewQuestionAdmin(admin.ModelAdmin):
    show_full_result_count = False
    list_display = ("id", "name", "q_type")
    autocomplete_fields = ("q_type",)
    search_fields = ("name",)
    list_filter = ("q_type",)


@admin.register(SKUCostPriceReviewRecord)
class SKUCostPriceReviewRecordAdmin(admin.ModelAdmin):
    show_full_result_count = False
    list_display = ("sku", "product", "apply_cost_price", "after_change_cost_price", "action_type")
    autocomplete_fields = ("sku", "product")
    search_fields = ("sku__sku_id", "product__name", "product__product_id", "sku__spec_code")


@admin.register(SubStockKeepingUnit)
class SubStockKeepingUnitAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "sub_sku_id",
        "product",
        "parent_sku",
        "relate_sku",
        "num",
        "become_history",
        "is_main",
    )
    autocomplete_fields = (
        "product",
        "parent_sku",
        "relate_sku",
    )

    search_fields = (
        "product__id",
        "product__name",
        "product__code",
    )


@admin.register(DownloadTasks)
class DownloadTasksAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "status",
        "filename",
        "download_type",
        "download_platform",
        "source_id",
        "download_page",
        "celery_id",
        # "download_url",
        "distributor",
        "company",
        "create_user",
        "update_user",
        "expired_time",
        "start_time",
        "finish_time",
        "create_date",
        "update_date",
    )
    list_filter = ("download_type", "download_platform", "status")
    search_fields = ("create_user", "celery_id")


@admin.register(GoodProductNote)
class GoodProductNoteAdmin(admin.ModelAdmin):
    show_full_result_count = False
    list_display = ("note_id", "content", "product", "is_deleted")
    autocomplete_fields = ("product",)
    search_fields = ("product__name", "product__product_id", "content")


@admin.register(GoodProductNoteComment)
class GoodProductNoteCommentAdmin(admin.ModelAdmin):
    show_full_result_count = False
    list_display = ("comment_id", "content", "note", "is_deleted")
    autocomplete_fields = ("note",)
    search_fields = ("comment_id", "note__note_id", "content")


@admin.register(ProductStateReviewRecord)
class ProductStateReviewRecordAdmin(admin.ModelAdmin):
    list_display = [field.name for field in ProductStateReviewRecord._meta.fields]
    autocomplete_fields = ("product", "review_user", "create_user", "cancel_user")
    search_fields = ("product__product_id", "product__name", "product__code", "create_user__real_name", "review_user__real_name")
    list_filter = ("review_type", "state")


@admin.register(SKUInventoryReviewRecord)
class SKUInventoryReviewRecordAdmin(admin.ModelAdmin):
    list_display = [field.name for field in SKUInventoryReviewRecord._meta.fields]
    autocomplete_fields = ("sku", "product", "create_user", "cancel_user", "review_user")
    search_fields = ("product__product_id", "product__name", "product__code", "sku__sku_id", "sku__spec_code", "create_user__real_name", "review_user__real_name")
    list_filter = (
        "state",
        "user_type",
        "action_type",
    )


@admin.register(SKUSalesCount)
class SKUSalesCountAdmin(admin.ModelAdmin):
    list_display = ["sku", "product", "sales", "calc_date"]
    autocomplete_fields = ("sku", "product")
    search_fields = ("product__product_id", "product__name", "sku__sku_id", "sku__spec_code")


@admin.register(ProductRefundRateHistory)
class ProductRefundRateHistoryAdmin(admin.ModelAdmin):
    list_display = (
        "pk",
        "product_id",
        "rate",
        "calc_date",
        "create_user",
        "create_date",
    )
    search_fields = ("product_id",)
    list_filter = ("calc_date", "create_date")


@admin.register(UserWishList)
class UserWishListAdmin(admin.ModelAdmin):
    list_display = (
        "product",
        "user",
        "wish_type",
        "distributor",
        "create_date",
        "update_date",
    )
    autocomplete_fields = ("product", "user", "distributor")
    search_fields = (
        "product__product_id",
        "product__name",
        "product__code",
        "user__username",
        "user__real_name",
        "user__mobile",
    )
    list_filter = ("wish_type", "create_date")

    date_hierarchy = "create_date"


@admin.register(DistributorMarketSubProductRelate)
class DistributorMarketSubProductRelateAdmin(admin.ModelAdmin):
    list_display = ("sub_product", "owner")
    autocomplete_fields = ("sub_product", "owner")
    search_fields = (
        "sub_product__product_id",
        "sub_product__name",
        "sub_product__code",
        "sub_product__parent_product__product_id",
        "sub_product__parent_product__name",
        "sub_product__parent_product__code",
    )


@admin.register(ProductAttrValues)
class ProductAttrValuesAdmin(admin.ModelAdmin):
    list_display = ("pk", "attr", "name", "order")
    list_filter = ("attr",)
    autocomplete_fields = ("attr",)
    search_fields = ("name",)


class SelectionPlanLabelMarkRelateInLine(admin.TabularInline):
    model = SelectionPlanLabelMarkRelate
    extra = 1

    autocomplete_fields = ("mark", "distributor")


@admin.register(SelectionPlanLabelMark)
class SelectionPlanLabelMarkAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "mark_type", "order", "display")
    search_fields = ("name",)
    list_filter = ("display", "mark_type")
    inlines = [SelectionPlanLabelMarkRelateInLine]


@admin.register(SimilarProductRelate)
class SimilarProductRelateAdmin(admin.ModelAdmin):
    list_display = ("id", "product", "external_product_id", "is_deleted", "create_user", "create_date")

    search_fields = (
        "product__name",
        "product__product_id",
        "external_product_id",
    )

    list_filter = ("is_deleted",)

    autocomplete_fields = ("product",)


@admin.register(OverseasProduct)
class OverseasProductAdmin(admin.ModelAdmin):
    list_display = [f.name for f in OverseasProduct._meta.fields]

    search_fields = ("product_id", "platform_product_id", "name")

    list_filter = ("relate_platform_id",)


@admin.register(DistributorWarehouseInventory)
class DistributorWarehouseInventoryAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "sku_id",
        "distributor_id",
        "warehouse_inventory",
        "qty",
        "order_lock_qty",
        "create_date",
        "update_date",
    )
    raw_id_fields = ("sku", "distributor")
    search_fields = (
        "sku__sku_id",
        "sku__spec_code",
        "sku__product__product_id",
        "sku__product__name",
    )
    list_filter = ("update_date",)


@admin.register(ProductConfig)
class ProductConfigAdmin(admin.ModelAdmin):
    list_display = ("id", "product", "disable_display_in_rank")
    search_fields = ("product__product_id", "product__name")
    autocomplete_fields = ("product",)


@admin.register(DistributedProductInfoModel)
class DistributedProductInfoModelAdmin(admin.ModelAdmin):
    list_display = ("parent_product", "product_name", "main_images")
    autocomplete_fields = ("parent_product",)
    search_fields = ("parent_product__product_id", "parent_product__name")
