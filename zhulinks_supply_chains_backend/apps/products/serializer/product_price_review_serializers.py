from rest_framework import serializers

from products.models import SKUCostPriceReviewRecord


class ProductPriceReviewSerializer(serializers.ModelSerializer):
    review_user = serializers.SerializerMethodField()
    state_display = serializers.ReadOnlyField(source="get_state_display")
    apply_cost_price = serializers.SerializerMethodField()

    class Meta:
        model = SKUCostPriceReviewRecord
        fields = [
            "id",
            "create_date",
            "review_user",
            "state_display",
            "state",
            "review_remark",
            "apply_cost_price",
            "after_change_cost_price",
            "apply_image",
            "remark",
            "product",
            "action_type",
            "review_remark",
            "apply_remark"
        ]
        read_only_fields = ("id",)

    @staticmethod
    def get_review_user(obj):
        if obj.review_user:
            return obj.review_user.real_name
        return None

    @staticmethod
    def get_apply_cost_price(obj):
        apply_cost_price = obj.apply_cost_price
        apply_cost_price_max = obj.apply_cost_price_max
        if apply_cost_price and apply_cost_price_max:
            if apply_cost_price != apply_cost_price_max:
                return f"{apply_cost_price} - {apply_cost_price_max}"
            return apply_cost_price
        return apply_cost_price or apply_cost_price_max or None
