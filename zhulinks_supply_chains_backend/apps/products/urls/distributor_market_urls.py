# -*- coding: utf-8 -*-
from django.urls import path

from products.views.distributor_market_views.operator_views import (
    OPDistributorMarketProductView,
    OPDistributorMarketProductManagerView,
    OPDistributorMarketProductPriceView,
    OPDistributorMarketProductPriceConfigView,
)

urlpatterns = [
    # 分销市场 - 商品列表
    path("distributor_market_products", OPDistributorMarketProductView.as_view(), name="product.distributor_market_products"),
    # 分销市场 - 商品管理 (加入分销商市场，移除)
    path("distributor_market_products/manager", OPDistributorMarketProductManagerView.as_view(), name="product.distributor_market_products.manager"),
    # 分销市场 - 价格更新
    path("distributor_market_products/price", OPDistributorMarketProductPriceView.as_view(), name="product.distributor_market_products.price"),
    # 分销市场 - 价格配置
    path("distributor_market_products/price_config", OPDistributorMarketProductPriceConfigView.as_view(), name="product.distributor_market_products.price_config"),
]
