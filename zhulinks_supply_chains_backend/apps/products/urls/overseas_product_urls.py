# -*- coding: utf-8 -*-
from django.urls import path, include
from products.views.overseas_product_views import (
    OverseasProductRankView,
    OverseasProductPlatformEnumView,
    OverseasProductDetailView,
)


urlpatterns = [
    # 海外榜单 - 平台枚举
    path("overseas_products/platform_enum", OverseasProductPlatformEnumView.as_view(), name="product.overseas_products.platform_enum"),
    # 海外商品榜单
    path("overseas_products/rank", OverseasProductRankView.as_view(), name="product.overseas_products.rank"),
    # 海外商品详情
    path("overseas_products/<int:product_id>", OverseasProductDetailView.as_view(), name="product.overseas_products.detail"),
]
