# -*- coding: utf-8 -*-
from django.urls import path

from products.views.operator_product_views import *

urlpatterns = [
    # 货盘计划列表
    path("op_product/selection_plan", OPProductSelectionPlanView.as_view(), name="op.product.selection_plan"),
    # 货盘利润
    path("op_product/selection_plan/profit_margin/<int:plan_id>", OPProductSelectionPlanProfitMarginView.as_view(), name="op.product.selection_plan.profit_margin"),
    # 修改预估销量
    path("op_product/selection_plan/modify_estimated_sales/<int:plan_id>", OPProductSelectionPlanProfitMarginView.as_view(), name="op.product.selection_plan.modify_estimated_sales"),
    # 货盘利润下载
    path("op_product/selection_plan/profit_margin/download/<int:plan_id>", OPProductSelectionPlanProfitMarginDownloadView.as_view(), name="op.product.selection_plan.profit_margin.download"),
    # 货盘复制
    path("op_product/selection_plan/<int:plan_id>/copy", op_copy_selection_plan_view, name="op.product.selection_plan.copy"),
    # 单品加入货盘计划
    path("op_product/selection_plan/<int:plan_id>/<int:product_id>", OPProductSelectionPlanManagerView.as_view(), name="op.product.selection_plan_manager"),
    # 编辑item的库存
    path("op_product/selection_plan/<int:plan_id>/inventory/<int:product_id>", OPProductSelectionPlanSKUInventoryManager.as_view(), name="op.product.selection_plan_inventory_manager"),
    # 供应商货表盘回收站列表
    path("op_product/selection_plan/recycle/<int:selection_plan_id>", OPSelectionPlanItemRecycleListView.as_view(), name="op.product.selection_plan_recycle"),
    #
    path("op_product/selection_plan/recycle/download/<int:selection_plan_id>", OPSelectionPlanItemRecycleListDownloadView.as_view(), name="op.product.selection_plan_recycle_download"),
    # 还原货盘商品
    path("op_product/selection_plan/recycle/<int:item_id>/restore", OPSelectionPlanItemRecycleDetailView.as_view(), name="op.product.selection_plan_recycle_item"),
    ###
    path("op_product/selection_plan/<int:plan_id>/bulk", OPProductPlanBulkView.as_view(), name="op.product.to_plan_bulk"),
    # 货盘商品列表
    path("op_product/selection_plan/<int:plan_id>/list", OPProductSelectionPlanManagerView.as_view(), name="op.product.selection_plan_manager.list"),
    # 商品地图
    path("op_product/selection_plan/<int:plan_id>/map_list", OPSelectionPlanProdsMapView.as_view(), name="op.product.selection_plan_manager.map_list"),
    #
    path("op_product/selection_plan/<int:plan_id>/map_list_for_feishu", OPSelectionPlanProdsMapForFeishuView.as_view(), name="op.product.selection_plan_manager.map_list"),
    # 客服货盘计划
    path("op_product/cs_selection_plan/<int:plan_id>/list", OPCSProductSelectionPlanItemView.as_view(), name="op.product.cs_selection_plan.list"),
    path("op_product/cs_selection_plan/<int:plan_id>/<int:product_id>/link_cs", OPCSProductSelectionPlanItemLinkView.as_view(), name="op.product.cs_selection_plan.link_cs"),
    path("op_product/cs_selection_plan/<int:plan_id>/download", OPCSProductSelectionPlanItemDownloadView.as_view(), name="op.product.cs_selection_plan_manager.download"),
    # 货盘商品导出
    path("op_product/selection_plan/<int:plan_id>/download", OPSelectionPlanProductDownloadView.as_view(), name="op.product.selection_plan_manager.download"),
    # 货盘状态查询
    path("op_product/selection_plan/<int:plan_id>/check", op_check_product_in_plan_view, name="op.product.check_product_in_plan_view"),
    # 货盘详情
    path("op_product/selection_plan/<int:plan_id>", OPProductSelectionPlanDetailView.as_view(), name="op.product.selection_plan.detail"),
    # 移动货盘商品到其他货盘
    path("op_product/selection_plan/<int:plan_id>/move/<int:plan_product_id>", OPSelectionItemMoveView.as_view(), name="op.product.selection_plan.item_move"),
    # 批量移动商品到其他货盘
    path("op_product/selection_plan/bulk_move", OPSelectionItemBulkMoveView.as_view(), name="op.product.selection_plan.bulk_move"),
    # 货盘商品确认
    path("op_product/selection_plan/prod_confirm/<int:plan_product_id>", OPSelectionItemConfirmView.as_view(), name="op.product.selection_plan.prod_confirm"),
    # 移动公司
    path("op_product/selection_plan/move/company", OPPlanCompanyDetailView.as_view(), name="op.product.selection_plan.move.company"),
    path("op_product/selection_plan/move/category", OPPlanCategoryDetailView.as_view(), name="op.product.selection_plan.move.category"),
    path("op_product/selection_plan/move/product", OPPlanProductDetailView.as_view(), name="op.product.selection_plan.move.product"),
    # 地图排品
    path("op_product/selection_plan/move/map_product", OPSelectionMapItemMoveView.as_view(), name="op.product.selection_plan.move.map_product"),
    # 货盘商品标签
    path("op_product/selection_plan/mark", OPSelectionPlanMarkView.as_view(), name="op.product.selection_plan.mark"),
    # 供应商 货盘表操作记录
    path("op_product/selection_plan/records/<int:plan_id>", SelectionPlanRecordsView.as_view(), name="op.product.selection_plan.records"),
    path("op_product/selection_plan/records/counts/<int:plan_id>", OPSelectionPlanRecordsCountsView.as_view(), name="op.product.selection_plan.record_counts"),
    # 运营商和分销商共用
    path("selection_plan/<int:plan_id>/edit_status", ProductSelectionPlanEditStatusView.as_view(), name="product.selection_plan.edit_status"),
]
