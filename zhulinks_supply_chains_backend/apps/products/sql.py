# -*- coding: utf-8 -*-

# ----分类列表递归查询叶节点的数据----
FetchAllCategoryParents = """WITH RECURSIVE Path AS (SELECT *
                        FROM products_productcategorylist t
                        WHERE t.id = %s
                          and not t.is_deleted
                        UNION ALL
                        SELECT t1.*
                        FROM products_productcategorylist t1
                                 JOIN Path p ON t1.id = p.parent_id
                            and not t1.is_deleted)
SELECT *
FROM Path"""

# ----分类列表递归查询子节点的数据----
FetchAllCategoryChildren = """WITH RECURSIVE Path AS (SELECT *
                        FROM products_productcategorylist t
                        WHERE t.id = %s
                          and not t.is_deleted
                        UNION ALL
                        SELECT t1.*
                        FROM products_productcategorylist t1
                                 JOIN Path p ON t1.parent_id = p.id
                            and not t1.is_deleted)
SELECT *
FROM Path"""

# 查询抖店三级分类
FetchDyCateParentsSQL = """WITH RECURSIVE PATH AS (
	SELECT
		* 
	FROM
		products_datacategory T 
	WHERE
		T.category_id = %s UNION ALL
	SELECT
		t1.* 
	FROM
		products_datacategory t1
		JOIN PATH P ON t1.category_id = P.shop_parent_id 
	) SELECT
	* 
FROM
PATH"""


# ----- 创建商品标签宽表 -----
CreateProductLabelsAllSQL = """DROP TABLE IF EXISTS products_product_labels_all;

CREATE TABLE IF NOT EXISTS products_product_labels_all
(
    id                                       bigint generated by default as identity
        primary key,
    product_id                               VARCHAR(255)     NULL,
    product_name                             VARCHAR(255)     NULL,
    i_id                                     VARCHAR(255)     NULL,
    cost_price                               DECIMAL(18, 2)   NULL,
    company_name                             VARCHAR(255)     NULL,
    size                                     VARCHAR(255)     NULL,
    sys_category                             VARCHAR(255)     NULL,
    category                                 VARCHAR(255)     NULL,
    company_addr                             VARCHAR(255)     NULL,
    style                                    VARCHAR(255)     NULL,
    place                                    VARCHAR(255)     NULL,
    beset                                    VARCHAR(255)     NULL,
    is_beset                                 VARCHAR(255)     NULL,
    avg_price_7                              DECIMAL(18, 2)   NULL,
    avg_price_30                             DECIMAL(18, 2)   NULL,
    avg_price_60                             DECIMAL(18, 2)   NULL,
    avg_price_90                             DECIMAL(18, 2)   NULL,
    avg_price_180                            DECIMAL(18, 2)   NULL,
    avg_price_365                            DECIMAL(18, 2)   NULL,
    sum_paid_qty_7                           BIGINT           NULL,
    sum_paid_qty_30                          BIGINT           NULL,
    sum_paid_qty_60                          BIGINT           NULL,
    sum_paid_qty_90                          BIGINT           NULL,
    sum_paid_qty_180                         BIGINT           NULL,
    sum_paid_qty_365                         BIGINT           NULL,
    sum_paid_amt_7                           DECIMAL(38, 2)   NULL,
    sum_paid_amt_30                          DECIMAL(38, 2)   NULL,
    sum_paid_amt_60                          DECIMAL(38, 2)   NULL,
    sum_paid_amt_90                          DECIMAL(38, 2)   NULL,
    sum_paid_amt_180                         DECIMAL(38, 2)   NULL,
    sum_paid_amt_365                         DECIMAL(38, 2)   NULL,
    max_online_users                         BIGINT           NULL,
    cnt_openid                               BIGINT           NULL,
    cnt_pay_openid                           BIGINT           NULL,
    cnt_end_openid                           BIGINT           NULL,
    cnt_refund_openid                        BIGINT           NULL,
    cnt_bef_send_refund_openid               BIGINT           NULL,
    cnt_af_send_refund_openid                BIGINT           NULL,
    cnt_af_end_refund_openid                 BIGINT           NULL,
    cnt_qt_refund_openid                     BIGINT           NULL,
    cnt_click_openid                         SMALLINT         NULL,
    cnt_value_click_rate                     SMALLINT         NULL,
    cnt_click_pay_rate                       SMALLINT         NULL,
    sum_qty                                  BIGINT           NULL,
    sum_pay_qty                              BIGINT           NULL,
    sum_send_qty                             BIGINT           NULL,
    sum_end_qty                              BIGINT           NULL,
    sum_bef_send_refund_qty                  BIGINT           NULL,
    sum_af_send_refund_qty                   BIGINT           NULL,
    sum_af_end_refund_qty                    BIGINT           NULL,
    sum_qt_refund_qty                        BIGINT           NULL,
    sum_introduce                            BIGINT           NULL,
    cnt_introduce                            BIGINT           NULL,
    sale_100_time                            VARCHAR(255)     NULL,
    sale_300_time                            VARCHAR(255)     NULL,
    sale_500_time                            VARCHAR(255)     NULL,
    sale_1000_time                           VARCHAR(255)     NULL,
    sale_2000_time                           VARCHAR(255)     NULL,
    cnt_soid                                 BIGINT           NULL,
    cnt_pay_soid                             BIGINT           NULL,
    cnt_send_soid                            BIGINT           NULL,
    cnt_end_soid                             BIGINT           NULL,
    cnt_qt_refund_soid                       BIGINT           NULL,
    cnt_ps_soid                              BIGINT           NULL,
    cnt_bef_send_refund_soid                 BIGINT           NULL,
    cnt_af_send_refund_soid                  BIGINT           NULL,
    rate_bef_send_refund_soid                DOUBLE PRECISION NULL,
    rate_af_send_refund_soid                 DOUBLE PRECISION NULL,
    price                                    DECIMAL(18, 2)   NULL,
    profit                                   DOUBLE PRECISION NULL,
    avg_paid_openid                          DECIMAL(38, 4)   NULL,
    live_cost_price                          TEXT             NULL,
    price_rate                               DOUBLE PRECISION NULL,
    sum_paid_amount                          DECIMAL(38, 2)   NULL,
    sum_paid_end_amount                      DECIMAL(38, 2)   NULL,
    sum_paid_refund_amount                   DECIMAL(38, 2)   NULL,
    tet_amount                               SMALLINT         NULL,
    sum_bef_send_refund_amount               DECIMAL(38, 2)   NULL,
    sum_af_send_refund_amount                DECIMAL(38, 2)   NULL,
    sum_af_end_refund_amount                 DECIMAL(38, 2)   NULL,
    max_state_sum_qty                        TEXT             NULL,
    max_state_sum_pay_qty                    TEXT             NULL,
    max_state_sum_end_qty                    TEXT             NULL,
    max_state_sum_refund_qty                 TEXT             NULL,
    max_state_sum_qt_refund_qty              TEXT             NULL,
    max_state_cnt_openid                     TEXT             NULL,
    max_state_cnt_pay_openid                 TEXT             NULL,
    max_state_cnt_end_openid                 TEXT             NULL,
    max_state_cnt_refund_openid              TEXT             NULL,
    max_state_cnt_qt_refund_openid           TEXT             NULL,
    max_city_sum_qty                         TEXT             NULL,
    max_city_sum_pay_qty                     TEXT             NULL,
    max_city_sum_end_qty                     TEXT             NULL,
    max_city_sum_refund_qty                  TEXT             NULL,
    max_city_sum_qt_refund_qty               TEXT             NULL,
    max_city_cnt_openid                      TEXT             NULL,
    max_city_cnt_pay_openid                  TEXT             NULL,
    max_city_cnt_end_openid                  TEXT             NULL,
    max_city_cnt_refund_openid               TEXT             NULL,
    max_city_cnt_qt_refund_openid            TEXT             NULL,
    avg_max_online_users_month               DOUBLE PRECISION NULL,
    avg_cnt_openid_month                     DOUBLE PRECISION NULL,
    avg_cnt_pay_openid_month                 DOUBLE PRECISION NULL,
    avg_cnt_end_openid_month                 DOUBLE PRECISION NULL,
    avg_cnt_refund_openid_month              DOUBLE PRECISION NULL,
    avg_cnt_bef_send_refund_openid_month     DOUBLE PRECISION NULL,
    avg_cnt_af_send_refund_openid_month      DOUBLE PRECISION NULL,
    avg_cnt_af_end_refund_openid_month       DOUBLE PRECISION NULL,
    avg_cnt_qt_refund_openid_month           DOUBLE PRECISION NULL,
    avg_cnt_click_openid_month               DOUBLE PRECISION NULL,
    avg_cnt_value_click_rate_month           DOUBLE PRECISION NULL,
    avg_cnt_click_pay_rate_month             DOUBLE PRECISION NULL,
    avg_sum_qty_month                        DOUBLE PRECISION NULL,
    avg_sum_pay_qty_month                    DOUBLE PRECISION NULL,
    avg_sum_send_qty_month                   DOUBLE PRECISION NULL,
    avg_sum_end_qty_month                    DOUBLE PRECISION NULL,
    avg_sum_bef_send_refund_qty_month        DOUBLE PRECISION NULL,
    avg_sum_af_send_refund_qty_month         DOUBLE PRECISION NULL,
    avg_sum_af_end_refund_qty_month          DOUBLE PRECISION NULL,
    avg_sum_qt_refund_qty_month              DOUBLE PRECISION NULL,
    avg_sum_introduce_month                  DOUBLE PRECISION NULL,
    avg_cnt_introduce_month                  DOUBLE PRECISION NULL,
    min_sale_100_time_month                  VARCHAR(255)     NULL,
    min_sale_300_time_month                  VARCHAR(255)     NULL,
    min_sale_500_time_month                  VARCHAR(255)     NULL,
    min_sale_1000_time_month                 VARCHAR(255)     NULL,
    min_sale_2000_time_month                 VARCHAR(255)     NULL,
    max_sale_100_time_month                  VARCHAR(255)     NULL,
    max_sale_300_time_month                  VARCHAR(255)     NULL,
    max_sale_500_time_month                  VARCHAR(255)     NULL,
    max_sale_1000_time_month                 VARCHAR(255)     NULL,
    max_sale_2000_time_month                 VARCHAR(255)     NULL,
    avg_cnt_soid_month                       DOUBLE PRECISION NULL,
    avg_cnt_pay_soid_month                   DOUBLE PRECISION NULL,
    avg_cnt_send_soid_month                  DOUBLE PRECISION NULL,
    avg_cnt_end_soid_month                   DOUBLE PRECISION NULL,
    avg_cnt_qt_refund_soid_month             DOUBLE PRECISION NULL,
    avg_cnt_ps_soid_month                    DOUBLE PRECISION NULL,
    avg_cnt_bef_send_refund_soid_month       DOUBLE PRECISION NULL,
    avg_cnt_af_send_refund_soid_month        DOUBLE PRECISION NULL,
    avg_rate_bef_send_refund_soid_month      DOUBLE PRECISION NULL,
    avg_rate_af_send_refund_soid_month       DOUBLE PRECISION NULL,
    min_price_month                          DECIMAL(18, 2)   NULL,
    min_profit_month                         DOUBLE PRECISION NULL,
    min_cost_price_month                     VARCHAR(255)     NULL,
    min_price_rate_month                     DOUBLE PRECISION NULL,
    min_avg_paid_openid_month                DECIMAL(38, 4)   NULL,
    max_price_month                          DECIMAL(18, 2)   NULL,
    max_profit_month                         DOUBLE PRECISION NULL,
    max_cost_price_month                     VARCHAR(255)     NULL,
    max_price_rate_month                     DOUBLE PRECISION NULL,
    max_avg_paid_openid_month                DECIMAL(38, 4)   NULL,
    avg_sum_paid_amount_month                DECIMAL(38, 4)   NULL,
    avg_sum_paid_end_amount_month            DECIMAL(38, 4)   NULL,
    avg_sum_paid_refund_amount_month         DECIMAL(38, 4)   NULL,
    avg_tet_amount_month                     DOUBLE PRECISION NULL,
    avg_sum_bef_send_refund_amount_month     DECIMAL(38, 4)   NULL,
    avg_sum_af_send_refund_amount_month      DECIMAL(38, 4)   NULL,
    avg_sum_af_end_refund_amount_month       DECIMAL(38, 4)   NULL,
    total_max_online_users_month             BIGINT           NULL,
    total_cnt_openid_month                   BIGINT           NULL,
    total_cnt_pay_openid_month               BIGINT           NULL,
    total_cnt_refund_openid_month            BIGINT           NULL,
    total_cnt_bef_send_refund_openid_month   BIGINT           NULL,
    total_cnt_af_send_refund_openid_month    BIGINT           NULL,
    total_cnt_af_end_refund_openid_month     BIGINT           NULL,
    total_cnt_qt_refund_openid_month         BIGINT           NULL,
    total_cnt_click_openid_month             BIGINT           NULL,
    total_cnt_value_click_rate_month         BIGINT           NULL,
    total_cnt_click_pay_rate_month           BIGINT           NULL,
    total_sum_qty_month                      BIGINT           NULL,
    total_sum_pay_qty_month                  BIGINT           NULL,
    total_sum_send_qty_month                 BIGINT           NULL,
    total_sum_end_qty_month                  BIGINT           NULL,
    total_sum_bef_send_refund_qty_month      BIGINT           NULL,
    total_sum_af_send_refund_qty_month       BIGINT           NULL,
    total_sum_af_end_refund_qty_month        BIGINT           NULL,
    total_sum_qt_refund_qty_month            BIGINT           NULL,
    total_sum_introduce_month                BIGINT           NULL,
    total_cnt_introduce_month                BIGINT           NULL,
    total_cnt_soid_month                     BIGINT           NULL,
    total_cnt_pay_soid_month                 BIGINT           NULL,
    total_cnt_send_soid_month                BIGINT           NULL,
    total_cnt_end_soid_month                 BIGINT           NULL,
    total_cnt_qt_refund_soid_month           BIGINT           NULL,
    total_cnt_ps_soid_month                  BIGINT           NULL,
    total_cnt_bef_send_refund_soid_month     BIGINT           NULL,
    total_cnt_af_send_refund_soid_month      BIGINT           NULL,
    total_sum_paid_amount_month              DECIMAL(38, 2)   NULL,
    total_sum_paid_end_amount_month          DECIMAL(38, 2)   NULL,
    total_sum_paid_refund_amount_month       DECIMAL(38, 2)   NULL,
    total_tet_amount_month                   BIGINT           NULL,
    total_sum_bef_send_refund_amount_month   DECIMAL(38, 2)   NULL,
    total_sum_af_send_refund_amount_month    DECIMAL(38, 2)   NULL,
    total_sum_af_end_refund_amount_month     DECIMAL(38, 2)   NULL,
    avg_max_online_users_history             DOUBLE PRECISION NULL,
    avg_cnt_openid_history                   DOUBLE PRECISION NULL,
    avg_cnt_pay_openid_history               DOUBLE PRECISION NULL,
    avg_cnt_end_openid_history               DOUBLE PRECISION NULL,
    avg_cnt_refund_openid_history            DOUBLE PRECISION NULL,
    avg_cnt_bef_send_refund_openid_history   DOUBLE PRECISION NULL,
    avg_cnt_af_send_refund_openid_history    DOUBLE PRECISION NULL,
    avg_cnt_af_end_refund_openid_history     DOUBLE PRECISION NULL,
    avg_cnt_qt_refund_openid_history         DOUBLE PRECISION NULL,
    avg_cnt_click_openid_history             DOUBLE PRECISION NULL,
    avg_cnt_value_click_rate_history         DOUBLE PRECISION NULL,
    avg_cnt_click_pay_rate_history           DOUBLE PRECISION NULL,
    avg_sum_qty_history                      DOUBLE PRECISION NULL,
    avg_sum_pay_qty_history                  DOUBLE PRECISION NULL,
    avg_sum_send_qty_history                 DOUBLE PRECISION NULL,
    avg_sum_end_qty_history                  DOUBLE PRECISION NULL,
    avg_sum_bef_send_refund_qty_history      DOUBLE PRECISION NULL,
    avg_sum_af_send_refund_qty_history       DOUBLE PRECISION NULL,
    avg_sum_af_end_refund_qty_history        DOUBLE PRECISION NULL,
    avg_sum_qt_refund_qty_history            DOUBLE PRECISION NULL,
    avg_sum_introduce_history                DOUBLE PRECISION NULL,
    avg_cnt_introduce_history                DOUBLE PRECISION NULL,
    min_sale_100_time_history                VARCHAR(255)     NULL,
    min_sale_300_time_history                VARCHAR(255)     NULL,
    min_sale_500_time_history                VARCHAR(255)     NULL,
    min_sale_1000_time_history               VARCHAR(255)     NULL,
    min_sale_2000_time_history               VARCHAR(255)     NULL,
    max_sale_100_time_history                VARCHAR(255)     NULL,
    max_sale_300_time_history                VARCHAR(255)     NULL,
    max_sale_500_time_history                VARCHAR(255)     NULL,
    max_sale_1000_time_history               VARCHAR(255)     NULL,
    max_sale_2000_time_history               VARCHAR(255)     NULL,
    avg_cnt_soid_history                     DOUBLE PRECISION NULL,
    avg_cnt_pay_soid_history                 DOUBLE PRECISION NULL,
    avg_cnt_send_soid_history                DOUBLE PRECISION NULL,
    avg_cnt_end_soid_history                 DOUBLE PRECISION NULL,
    avg_cnt_qt_refund_soid_history           DOUBLE PRECISION NULL,
    avg_cnt_ps_soid_history                  DOUBLE PRECISION NULL,
    avg_cnt_bef_send_refund_soid_history     DOUBLE PRECISION NULL,
    avg_cnt_af_send_refund_soid_history      DOUBLE PRECISION NULL,
    avg_rate_bef_send_refund_soid_history    DOUBLE PRECISION NULL,
    avg_rate_af_send_refund_soid_history     DOUBLE PRECISION NULL,
    min_price_history                        DECIMAL(18, 2)   NULL,
    min_profit_history                       DOUBLE PRECISION NULL,
    min_cost_price_history                   VARCHAR(255)     NULL,
    min_price_rate_history                   DOUBLE PRECISION NULL,
    min_avg_paid_openid_history              DECIMAL(38, 4)   NULL,
    max_price_history                        DECIMAL(18, 2)   NULL,
    max_profit_history                       DOUBLE PRECISION NULL,
    max_cost_price_history                   VARCHAR(255)     NULL,
    max_price_rate_history                   DOUBLE PRECISION NULL,
    max_avg_paid_openid_history              DECIMAL(38, 4)   NULL,
    avg_sum_paid_amount_history              DECIMAL(38, 4)   NULL,
    avg_sum_paid_end_amount_history          DECIMAL(38, 4)   NULL,
    avg_sum_paid_refund_amount_history       DECIMAL(38, 4)   NULL,
    avg_tet_amount_history                   DOUBLE PRECISION NULL,
    avg_sum_bef_send_refund_amount_history   DECIMAL(38, 4)   NULL,
    avg_sum_af_send_refund_amount_history    DECIMAL(38, 4)   NULL,
    avg_sum_af_end_refund_amount_history     DECIMAL(38, 4)   NULL,
    total_max_online_users_history           BIGINT           NULL,
    total_cnt_openid_history                 BIGINT           NULL,
    total_cnt_pay_openid_history             BIGINT           NULL,
    total_cnt_end_openid_history             BIGINT           NULL,
    total_cnt_refund_openid_history          BIGINT           NULL,
    total_cnt_bef_send_refund_openid_history BIGINT           NULL,
    total_cnt_af_send_refund_openid_history  BIGINT           NULL,
    total_cnt_af_end_refund_openid_history   BIGINT           NULL,
    total_cnt_qt_refund_openid_history       BIGINT           NULL,
    total_cnt_click_openid_history           BIGINT           NULL,
    total_cnt_value_click_rate_history       BIGINT           NULL,
    total_cnt_click_pay_rate_history         BIGINT           NULL,
    total_sum_qty_history                    BIGINT           NULL,
    total_sum_pay_qty_history                BIGINT           NULL,
    total_sum_send_qty_history               BIGINT           NULL,
    total_sum_end_qty_history                BIGINT           NULL,
    total_sum_bef_send_refund_qty_history    BIGINT           NULL,
    total_sum_af_send_refund_qty_history     BIGINT           NULL,
    total_sum_af_end_refund_qty_history      BIGINT           NULL,
    total_sum_qt_refund_qty_history          BIGINT           NULL,
    total_sum_introduce_history              BIGINT           NULL,
    total_cnt_introduce_history              BIGINT           NULL,
    total_cnt_soid_history                   BIGINT           NULL,
    total_cnt_pay_soid_history               BIGINT           NULL,
    total_cnt_send_soid_history              BIGINT           NULL,
    total_cnt_end_soid_history               BIGINT           NULL,
    total_cnt_qt_refund_soid_history         BIGINT           NULL,
    total_cnt_ps_soid_history                BIGINT           NULL,
    total_cnt_bef_send_refund_soid_history   BIGINT           NULL,
    total_rate_af_send_refund_soid_history   DOUBLE PRECISION NULL,
    total_sum_paid_amount_history            DECIMAL(38, 2)   NULL,
    total_sum_paid_end_amount_history        DECIMAL(38, 2)   NULL,
    total_sum_paid_refund_amount_history     DECIMAL(38, 2)   NULL,
    total_tet_amount_history                 BIGINT           NULL,
    total_sum_bef_send_refund_amount_history DECIMAL(38, 2)   NULL,
    total_sum_af_send_refund_amount_history  DECIMAL(38, 2)   NULL,
    total_sum_af_end_refund_amount_history   DECIMAL(38, 2)   NULL,
    constraint products_product_labels_all_pid_pname_i_id_uniq
        unique (product_id, product_name, i_id)
);
COMMENT ON TABLE products_product_labels_all IS '商品销售指标宽表';
COMMENT ON COLUMN products_product_labels_all.product_id IS '商品id';
COMMENT ON COLUMN products_product_labels_all.product_name IS '商品名称';
COMMENT ON COLUMN products_product_labels_all.i_id IS '商品货号';
COMMENT ON COLUMN products_product_labels_all.cost_price IS '成本价';
COMMENT ON COLUMN products_product_labels_all.company_name IS '供应商名称';
COMMENT ON COLUMN products_product_labels_all.size IS '商品尺寸';
COMMENT ON COLUMN products_product_labels_all.sys_category IS '系统商品分类';
COMMENT ON COLUMN products_product_labels_all.category IS '商品分类';
COMMENT ON COLUMN products_product_labels_all.company_addr IS '供应商品地址';
COMMENT ON COLUMN products_product_labels_all.style IS '款式';
COMMENT ON COLUMN products_product_labels_all.place IS '商品产地';
COMMENT ON COLUMN products_product_labels_all.beset IS '镶嵌材料';
COMMENT ON COLUMN products_product_labels_all.is_beset IS '是否镶嵌';
COMMENT ON COLUMN products_product_labels_all.avg_price_7 IS '近7天平均售价';
COMMENT ON COLUMN products_product_labels_all.avg_price_30 IS '近30天平均售价';
COMMENT ON COLUMN products_product_labels_all.avg_price_60 IS '近60天平均售价';
COMMENT ON COLUMN products_product_labels_all.avg_price_90 IS '近90天平均售价';
COMMENT ON COLUMN products_product_labels_all.avg_price_180 IS '近180天平均售价';
COMMENT ON COLUMN products_product_labels_all.avg_price_365 IS '近365天平均售价';
COMMENT ON COLUMN products_product_labels_all.sum_paid_qty_7 IS '近7天商品累计销量';
COMMENT ON COLUMN products_product_labels_all.sum_paid_qty_30 IS '近30天商品累计销量';
COMMENT ON COLUMN products_product_labels_all.sum_paid_qty_60 IS '近60天商品累计销量';
COMMENT ON COLUMN products_product_labels_all.sum_paid_qty_90 IS '近90天商品累计销量';
COMMENT ON COLUMN products_product_labels_all.sum_paid_qty_180 IS '近180天商品累计销量';
COMMENT ON COLUMN products_product_labels_all.sum_paid_qty_365 IS '近365天商品累计销量';
COMMENT ON COLUMN products_product_labels_all.sum_paid_amt_7 IS '近7天累计支付金额';
COMMENT ON COLUMN products_product_labels_all.sum_paid_amt_30 IS '近30天累计支付金额';
COMMENT ON COLUMN products_product_labels_all.sum_paid_amt_60 IS '近60天累计支付金额';
COMMENT ON COLUMN products_product_labels_all.sum_paid_amt_90 IS '近90天累计支付金额';
COMMENT ON COLUMN products_product_labels_all.sum_paid_amt_180 IS '近180天累计支付金额';
COMMENT ON COLUMN products_product_labels_all.sum_paid_amt_365 IS '近365天累计支付金额';
COMMENT ON COLUMN products_product_labels_all.max_online_users IS '最近一场在线人数';
COMMENT ON COLUMN products_product_labels_all.cnt_openid IS '最近一场下单人数';
COMMENT ON COLUMN products_product_labels_all.cnt_pay_openid IS '最近一场支付人数';
COMMENT ON COLUMN products_product_labels_all.cnt_end_openid IS '最近一场签收人数';
COMMENT ON COLUMN products_product_labels_all.cnt_refund_openid IS '最近一场退款人数';
COMMENT ON COLUMN products_product_labels_all.cnt_bef_send_refund_openid IS '最近一场发货前退款人数';
COMMENT ON COLUMN products_product_labels_all.cnt_af_send_refund_openid IS '最近一场发货后退款人数';
COMMENT ON COLUMN products_product_labels_all.cnt_af_end_refund_openid IS '最近一场签收后退货人数';
COMMENT ON COLUMN products_product_labels_all.cnt_qt_refund_openid IS '最近一场品退人数';
COMMENT ON COLUMN products_product_labels_all.cnt_click_openid IS '最近一场商品点击人数';
COMMENT ON COLUMN products_product_labels_all.cnt_value_click_rate IS '最近一场商品曝光-点击率（人数）';
COMMENT ON COLUMN products_product_labels_all.cnt_click_pay_rate IS '最近一场商品点击-成交转化率（人数）';
COMMENT ON COLUMN products_product_labels_all.sum_qty IS '最近一场已下单商品数量';
COMMENT ON COLUMN products_product_labels_all.sum_pay_qty IS '最近一场已支付商品数量';
COMMENT ON COLUMN products_product_labels_all.sum_send_qty IS '最近一场已发货商品数量';
COMMENT ON COLUMN products_product_labels_all.sum_end_qty IS '最近一场已签收商品数量';
COMMENT ON COLUMN products_product_labels_all.sum_bef_send_refund_qty IS '最近一场发货前退货商品数量';
COMMENT ON COLUMN products_product_labels_all.sum_af_send_refund_qty IS '最近一场发货后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.sum_af_end_refund_qty IS '最近一场签收后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.sum_qt_refund_qty IS '最近一场品退商品数量';
COMMENT ON COLUMN products_product_labels_all.sum_introduce IS '最近一场讲解时长';
COMMENT ON COLUMN products_product_labels_all.cnt_introduce IS '最近一场讲解次数';
COMMENT ON COLUMN products_product_labels_all.sale_100_time IS '最近一场100单耗时';
COMMENT ON COLUMN products_product_labels_all.sale_300_time IS '最近一场300单耗时';
COMMENT ON COLUMN products_product_labels_all.sale_500_time IS '最近一场500单耗时';
COMMENT ON COLUMN products_product_labels_all.sale_1000_time IS '最近一场1000单耗时';
COMMENT ON COLUMN products_product_labels_all.sale_2000_time IS '最近一场2000单耗时';
COMMENT ON COLUMN products_product_labels_all.cnt_soid IS '最近一场已创建订单数量';
COMMENT ON COLUMN products_product_labels_all.cnt_pay_soid IS '最近一场已支付订单数量';
COMMENT ON COLUMN products_product_labels_all.cnt_send_soid IS '最近一场已发货订单数量';
COMMENT ON COLUMN products_product_labels_all.cnt_end_soid IS '最近一场已签收订单数量';
COMMENT ON COLUMN products_product_labels_all.cnt_qt_refund_soid IS '最近一场品退订单数量';
COMMENT ON COLUMN products_product_labels_all.cnt_ps_soid IS '最近一场预售订单数';
COMMENT ON COLUMN products_product_labels_all.cnt_bef_send_refund_soid IS '最近一场发货前退款订单数';
COMMENT ON COLUMN products_product_labels_all.cnt_af_send_refund_soid IS '最近一场发货后退款订单数';
COMMENT ON COLUMN products_product_labels_all.rate_bef_send_refund_soid IS '最近一场发货前订单退款率';
COMMENT ON COLUMN products_product_labels_all.rate_af_send_refund_soid IS '最近一场发货后订单退款率';
COMMENT ON COLUMN products_product_labels_all.price IS '最近一场售价';
COMMENT ON COLUMN products_product_labels_all.profit IS '最近一场毛利';
COMMENT ON COLUMN products_product_labels_all.avg_paid_openid IS '最近一场客单价';
COMMENT ON COLUMN products_product_labels_all.live_cost_price IS '成本价';
COMMENT ON COLUMN products_product_labels_all.price_rate IS '定价比';
COMMENT ON COLUMN products_product_labels_all.sum_paid_amount IS '支付金额';
COMMENT ON COLUMN products_product_labels_all.sum_paid_end_amount IS '签收金额';
COMMENT ON COLUMN products_product_labels_all.sum_paid_refund_amount IS '退货金额';
COMMENT ON COLUMN products_product_labels_all.tet_amount IS '千次曝光成交金额';
COMMENT ON COLUMN products_product_labels_all.sum_bef_send_refund_amount IS '发货前退款金额';
COMMENT ON COLUMN products_product_labels_all.sum_af_send_refund_amount IS '发货后退款金额';
COMMENT ON COLUMN products_product_labels_all.sum_af_end_refund_amount IS '签收后退款金额';
COMMENT ON COLUMN products_product_labels_all.max_state_sum_qty IS '购买商品数量最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_state_sum_pay_qty IS '支付商品数量最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_state_sum_end_qty IS '签收商品数量最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_state_sum_refund_qty IS '退货商品数量最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_state_sum_qt_refund_qty IS '品退商品数量最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_state_cnt_openid IS '购买人数最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_state_cnt_pay_openid IS '支付人数最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_state_cnt_end_openid IS '签收人数最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_state_cnt_refund_openid IS '退货人数最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_state_cnt_qt_refund_openid IS '品退人数最多的省份';
COMMENT ON COLUMN products_product_labels_all.max_city_sum_qty IS '购买商品数量最多的城市';
COMMENT ON COLUMN products_product_labels_all.max_city_sum_pay_qty IS '支付商品数量最多的城市';
COMMENT ON COLUMN products_product_labels_all.max_city_sum_end_qty IS '签收商品数量最多的城市';
COMMENT ON COLUMN products_product_labels_all.max_city_sum_refund_qty IS '退货商品数量最多的城市';
COMMENT ON COLUMN products_product_labels_all.max_city_sum_qt_refund_qty IS '品退商品数量最多的城市';
COMMENT ON COLUMN products_product_labels_all.max_city_cnt_openid IS '购买人数最多的城市';
COMMENT ON COLUMN products_product_labels_all.max_city_cnt_pay_openid IS '支付人数最多的城市';
COMMENT ON COLUMN products_product_labels_all.max_city_cnt_end_openid IS '签收人数最多的城市';
COMMENT ON COLUMN products_product_labels_all.max_city_cnt_refund_openid IS '退货人数最多的城市';
COMMENT ON COLUMN products_product_labels_all.max_city_cnt_qt_refund_openid IS '品退人数最多的城市';
COMMENT ON COLUMN products_product_labels_all.avg_max_online_users_month IS '月内场均在线人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_openid_month IS '月内场均下单人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_pay_openid_month IS '月内场均支付人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_end_openid_month IS '月内场均签收人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_refund_openid_month IS '月内场均退款人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_bef_send_refund_openid_month IS '月内场均发货前退款人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_af_send_refund_openid_month IS '月内场均发货后退款人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_af_end_refund_openid_month IS '月内场均签收后退货人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_qt_refund_openid_month IS '月内场均品退人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_click_openid_month IS '月内场均商品点击人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_value_click_rate_month IS '月内场均商品曝光-点击率（人数）';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_click_pay_rate_month IS '月内场均商品点击-成交转化率（人数）';
COMMENT ON COLUMN products_product_labels_all.avg_sum_qty_month IS '月内场均已下单商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_pay_qty_month IS '月内场均已支付商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_send_qty_month IS '月内场均已发货商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_end_qty_month IS '月内场均已签收商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_bef_send_refund_qty_month IS '月内场均发货前退货商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_af_send_refund_qty_month IS '月内场均发货后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_af_end_refund_qty_month IS '月内场均签收后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_qt_refund_qty_month IS '月内场均品退商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_introduce_month IS '月内场均讲解时长';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_introduce_month IS '月内场均讲解次数';
COMMENT ON COLUMN products_product_labels_all.min_sale_100_time_month IS '月内单场100单最小耗时';
COMMENT ON COLUMN products_product_labels_all.min_sale_300_time_month IS '月内单场300单最小耗时';
COMMENT ON COLUMN products_product_labels_all.min_sale_500_time_month IS '月内单场500单最小耗时';
COMMENT ON COLUMN products_product_labels_all.min_sale_1000_time_month IS '月内单场1000单最小耗时';
COMMENT ON COLUMN products_product_labels_all.min_sale_2000_time_month IS '月内单场2000单最小耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_100_time_month IS '月内单场100单最大耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_300_time_month IS '月内单场300单最大耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_500_time_month IS '月内单场500单最大耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_1000_time_month IS '月内单场1000单最大耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_2000_time_month IS '月内单场2000单最大耗时';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_soid_month IS '月内场均已创建订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_pay_soid_month IS '月内场均已支付订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_send_soid_month IS '月内场均已发货订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_end_soid_month IS '月内场均已签收订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_qt_refund_soid_month IS '月内场均品退订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_ps_soid_month IS '月内场均预售订单数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_bef_send_refund_soid_month IS '月内场均发货前退款订单数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_af_send_refund_soid_month IS '月内场均发货后退款订单数';
COMMENT ON COLUMN products_product_labels_all.avg_rate_bef_send_refund_soid_month IS '月内场均发货前订单退款率';
COMMENT ON COLUMN products_product_labels_all.avg_rate_af_send_refund_soid_month IS '月内场均发货后订单退款率';
COMMENT ON COLUMN products_product_labels_all.min_price_month IS '月内单场最小售价';
COMMENT ON COLUMN products_product_labels_all.min_profit_month IS '月内单场最小毛利';
COMMENT ON COLUMN products_product_labels_all.min_cost_price_month IS '月内单场最小成本价';
COMMENT ON COLUMN products_product_labels_all.min_price_rate_month IS '月内单场最小定价比';
COMMENT ON COLUMN products_product_labels_all.min_avg_paid_openid_month IS '月内单场最小客单价';
COMMENT ON COLUMN products_product_labels_all.max_price_month IS '月内单场最大售价';
COMMENT ON COLUMN products_product_labels_all.max_profit_month IS '月内单场最大毛利';
COMMENT ON COLUMN products_product_labels_all.max_cost_price_month IS '月内单场最大成本价';
COMMENT ON COLUMN products_product_labels_all.max_price_rate_month IS '月内单场最大定价比';
COMMENT ON COLUMN products_product_labels_all.max_avg_paid_openid_month IS '月内单场最大客单价';
COMMENT ON COLUMN products_product_labels_all.avg_sum_paid_amount_month IS '月内场均支付金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_paid_end_amount_month IS '月内场均签收金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_paid_refund_amount_month IS '月内场均退货金额';
COMMENT ON COLUMN products_product_labels_all.avg_tet_amount_month IS '月内场均千次曝光成交金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_bef_send_refund_amount_month IS '月内场均发货前退款金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_af_send_refund_amount_month IS '月内场均发货后退款金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_af_end_refund_amount_month IS '月内场均签收后退款金额';
COMMENT ON COLUMN products_product_labels_all.total_max_online_users_month IS '月内累计在线人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_openid_month IS '月内累计下单人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_pay_openid_month IS '月内累计支付人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_refund_openid_month IS '月内累计退款人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_bef_send_refund_openid_month IS '月内累计发货前退款人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_af_send_refund_openid_month IS '月内累计发货后退款人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_af_end_refund_openid_month IS '月内累计签收后退货人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_qt_refund_openid_month IS '月内累计品退人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_click_openid_month IS '月内累计商品点击人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_value_click_rate_month IS '月内累计商品曝光-点击率（人数）';
COMMENT ON COLUMN products_product_labels_all.total_cnt_click_pay_rate_month IS '月内累计商品点击-成交转化率（人数）';
COMMENT ON COLUMN products_product_labels_all.total_sum_qty_month IS '月内累计已下单商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_pay_qty_month IS '月内累计已支付商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_send_qty_month IS '月内累计已发货商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_end_qty_month IS '月内累计已签收商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_bef_send_refund_qty_month IS '月内累计发货前退货商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_af_send_refund_qty_month IS '月内累计发货后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_af_end_refund_qty_month IS '月内累计签收后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_qt_refund_qty_month IS '月内累计品退商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_introduce_month IS '月内累计讲解时长';
COMMENT ON COLUMN products_product_labels_all.total_cnt_introduce_month IS '月内累计讲解次数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_soid_month IS '月内累计已创建订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_pay_soid_month IS '月内累计已支付订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_send_soid_month IS '月内累计已发货订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_end_soid_month IS '月内累计已签收订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_qt_refund_soid_month IS '月内累计品退订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_ps_soid_month IS '月内累计预售订单数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_bef_send_refund_soid_month IS '月内累计发货前退款订单数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_af_send_refund_soid_month IS '月内累计发货后退款订单数';
COMMENT ON COLUMN products_product_labels_all.total_sum_paid_amount_month IS '月内累计支付金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_paid_end_amount_month IS '月内累计签收金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_paid_refund_amount_month IS '月内累计退货金额';
COMMENT ON COLUMN products_product_labels_all.total_tet_amount_month IS '月内累计千次曝光成交金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_bef_send_refund_amount_month IS '月内累计发货前退款金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_af_send_refund_amount_month IS '月内累计发货后退款金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_af_end_refund_amount_month IS '月内累计签收后退款金额';
COMMENT ON COLUMN products_product_labels_all.avg_max_online_users_history IS '历史场均在线人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_openid_history IS '历史场均下单人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_pay_openid_history IS '历史场均支付人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_end_openid_history IS '历史场均签收人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_refund_openid_history IS '历史场均退款人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_bef_send_refund_openid_history IS '历史场均发货前退款人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_af_send_refund_openid_history IS '历史场均发货后退款人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_af_end_refund_openid_history IS '历史场均签收后退货人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_qt_refund_openid_history IS '历史场均品退人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_click_openid_history IS '历史场均商品点击人数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_value_click_rate_history IS '历史场均商品曝光-点击率（人数）';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_click_pay_rate_history IS '历史场均商品点击-成交转化率（人数）';
COMMENT ON COLUMN products_product_labels_all.avg_sum_qty_history IS '历史场均已下单商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_pay_qty_history IS '历史场均已支付商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_send_qty_history IS '历史场均已发货商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_end_qty_history IS '历史场均已签收商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_bef_send_refund_qty_history IS '历史场均发货前退货商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_af_send_refund_qty_history IS '历史场均发货后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_af_end_refund_qty_history IS '历史场均签收后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_qt_refund_qty_history IS '历史场均品退商品数量';
COMMENT ON COLUMN products_product_labels_all.avg_sum_introduce_history IS '历史场均讲解时长';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_introduce_history IS '历史场均讲解次数';
COMMENT ON COLUMN products_product_labels_all.min_sale_100_time_history IS '历史单场100单最小耗时';
COMMENT ON COLUMN products_product_labels_all.min_sale_300_time_history IS '历史单场300单最小耗时';
COMMENT ON COLUMN products_product_labels_all.min_sale_500_time_history IS '历史单场500单最小耗时';
COMMENT ON COLUMN products_product_labels_all.min_sale_1000_time_history IS '历史单场1000单最小耗时';
COMMENT ON COLUMN products_product_labels_all.min_sale_2000_time_history IS '历史单场2000单最小耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_100_time_history IS '历史单场100单最大耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_300_time_history IS '历史单场300单最大耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_500_time_history IS '历史单场500单最大耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_1000_time_history IS '历史单场1000单最大耗时';
COMMENT ON COLUMN products_product_labels_all.max_sale_2000_time_history IS '历史单场2000单最大耗时';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_soid_history IS '历史场均已创建订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_pay_soid_history IS '历史场均已支付订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_send_soid_history IS '历史场均已发货订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_end_soid_history IS '历史场均已签收订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_qt_refund_soid_history IS '历史场均品退订单数量';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_ps_soid_history IS '历史场均预售订单数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_bef_send_refund_soid_history IS '历史场均发货前退款订单数';
COMMENT ON COLUMN products_product_labels_all.avg_cnt_af_send_refund_soid_history IS '历史场均发货后退款订单数';
COMMENT ON COLUMN products_product_labels_all.avg_rate_bef_send_refund_soid_history IS '历史场均发货前订单退款率';
COMMENT ON COLUMN products_product_labels_all.avg_rate_af_send_refund_soid_history IS '历史场均发货后订单退款率';
COMMENT ON COLUMN products_product_labels_all.min_price_history IS '历史单场最小售价';
COMMENT ON COLUMN products_product_labels_all.min_profit_history IS '历史单场最小毛利';
COMMENT ON COLUMN products_product_labels_all.min_cost_price_history IS '历史单场最小成本价';
COMMENT ON COLUMN products_product_labels_all.min_price_rate_history IS '历史单场最小定价比';
COMMENT ON COLUMN products_product_labels_all.min_avg_paid_openid_history IS '历史单场最小客单价';
COMMENT ON COLUMN products_product_labels_all.max_price_history IS '历史单场最大售价';
COMMENT ON COLUMN products_product_labels_all.max_profit_history IS '历史单场最大毛利';
COMMENT ON COLUMN products_product_labels_all.max_cost_price_history IS '历史单场最大成本价';
COMMENT ON COLUMN products_product_labels_all.max_price_rate_history IS '历史单场最大定价比';
COMMENT ON COLUMN products_product_labels_all.max_avg_paid_openid_history IS '历史单场最大客单价';
COMMENT ON COLUMN products_product_labels_all.avg_sum_paid_amount_history IS '历史场均支付金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_paid_end_amount_history IS '历史场均签收金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_paid_refund_amount_history IS '历史场均退货金额';
COMMENT ON COLUMN products_product_labels_all.avg_tet_amount_history IS '历史场均千次曝光成交金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_bef_send_refund_amount_history IS '历史场均发货前退款金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_af_send_refund_amount_history IS '历史场均发货后退款金额';
COMMENT ON COLUMN products_product_labels_all.avg_sum_af_end_refund_amount_history IS '历史场均签收后退款金额';
COMMENT ON COLUMN products_product_labels_all.total_max_online_users_history IS '历史累计在线人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_openid_history IS '历史累计下单人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_pay_openid_history IS '历史累计支付人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_end_openid_history IS '历史累计签收人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_refund_openid_history IS '历史累计退款人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_bef_send_refund_openid_history IS '历史累计发货前退款人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_af_send_refund_openid_history IS '历史累计发货后退款人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_af_end_refund_openid_history IS '历史累计签收后退货人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_qt_refund_openid_history IS '历史累计品退人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_click_openid_history IS '历史累计商品点击人数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_value_click_rate_history IS '历史累计商品曝光-点击率（人数）';
COMMENT ON COLUMN products_product_labels_all.total_cnt_click_pay_rate_history IS '历史累计商品点击-成交转化率（人数）';
COMMENT ON COLUMN products_product_labels_all.total_sum_qty_history IS '历史累计已下单商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_pay_qty_history IS '历史累计已支付商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_send_qty_history IS '历史累计已发货商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_end_qty_history IS '历史累计已签收商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_bef_send_refund_qty_history IS '历史累计发货前退货商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_af_send_refund_qty_history IS '历史累计发货后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_af_end_refund_qty_history IS '历史累计签收后退货商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_qt_refund_qty_history IS '历史累计品退商品数量';
COMMENT ON COLUMN products_product_labels_all.total_sum_introduce_history IS '历史累计讲解时长';
COMMENT ON COLUMN products_product_labels_all.total_cnt_introduce_history IS '历史累计讲解次数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_soid_history IS '历史累计已创建订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_pay_soid_history IS '历史累计已支付订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_send_soid_history IS '历史累计已发货订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_end_soid_history IS '历史累计已签收订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_qt_refund_soid_history IS '历史累计品退订单数量';
COMMENT ON COLUMN products_product_labels_all.total_cnt_ps_soid_history IS '历史累计预售订单数';
COMMENT ON COLUMN products_product_labels_all.total_cnt_bef_send_refund_soid_history IS '历史累计发货前退款订单数';
COMMENT ON COLUMN products_product_labels_all.total_rate_af_send_refund_soid_history IS '历史累计发货后订单退款率';
COMMENT ON COLUMN products_product_labels_all.total_sum_paid_amount_history IS '历史累计支付金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_paid_end_amount_history IS '历史累计签收金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_paid_refund_amount_history IS '历史累计退货金额';
COMMENT ON COLUMN products_product_labels_all.total_tet_amount_history IS '历史累计千次曝光成交金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_bef_send_refund_amount_history IS '历史累计发货前退款金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_af_send_refund_amount_history IS '历史累计发货后退款金额';
COMMENT ON COLUMN products_product_labels_all.total_sum_af_end_refund_amount_history IS '历史累计签收后退款金额'
"""


# ----- 10-16增加新字段 -----
ProductLabelsAllAddColumn1016SQL = """ALTER TABLE PUBLIC.products_product_labels_all ADD "total_cnt_end_openid_month" BIGINT NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_amt_7" DECIMAL ( 38, 2 ) NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_amt_30" DECIMAL ( 38, 2 ) NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_amt_60" DECIMAL ( 38, 2 ) NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_amt_90" DECIMAL ( 38, 2 ) NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_amt_180" DECIMAL ( 38, 2 ) NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_amt_365" DECIMAL ( 38, 2 ) NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_qty_7" BIGINT NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_qty_30" BIGINT NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_qty_60" BIGINT NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_qty_90" BIGINT NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_qty_180" BIGINT NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "sum_done_qty_365" BIGINT NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "total_cnt_af_send_refund_soid_history" BIGINT NULL;
COMMENT ON COLUMN products_product_labels_all."total_cnt_end_openid_month" IS '月内累计签收人数';
COMMENT ON COLUMN products_product_labels_all."sum_done_amt_7" IS '近7天累计签收金额';
COMMENT ON COLUMN products_product_labels_all."sum_done_amt_30" IS '近30天累计签收金额';
COMMENT ON COLUMN products_product_labels_all."sum_done_amt_60" IS '近60天累计签收金额';
COMMENT ON COLUMN products_product_labels_all."sum_done_amt_90" IS '近90天累计签收金额';
COMMENT ON COLUMN products_product_labels_all."sum_done_amt_180" IS '近180天累计签收金额';
COMMENT ON COLUMN products_product_labels_all."sum_done_amt_365" IS '近365天累计签收金额';
COMMENT ON COLUMN products_product_labels_all."sum_done_qty_7" IS '近7天商品累计成交量';
COMMENT ON COLUMN products_product_labels_all."sum_done_qty_30" IS '近30天商品累计成交量';
COMMENT ON COLUMN products_product_labels_all."sum_done_qty_60" IS '近60天商品累计成交量';
COMMENT ON COLUMN products_product_labels_all."sum_done_qty_90" IS '近90天商品累计成交量';
COMMENT ON COLUMN products_product_labels_all."sum_done_qty_180" IS '近180天商品累计成交量';
COMMENT ON COLUMN products_product_labels_all."sum_done_qty_365" IS '近365天商品累计成交量';
COMMENT ON COLUMN products_product_labels_all."total_cnt_af_send_refund_soid_history" IS '历史累计发货后退款订单数';"""


# ----- 10-16 17:42增加新字段 -----
ProductLabelsAllAddColumn10161742SQL = """ALTER TABLE PUBLIC.products_product_labels_all ADD "is_feast_hot" varchar(65533) NULL;
ALTER TABLE PUBLIC.products_product_labels_all ADD "feast_labels" varchar(65533) NULL;
COMMENT ON COLUMN products_product_labels_all."is_feast_hot" IS '节日热销';
COMMENT ON COLUMN products_product_labels_all."feast_labels" IS '节日标签';"""


# ----- 10-21更改unique key sql -----
ProductLabelsAllChangeUniqueKey1021SQL = """alter table public.products_product_labels_all
    drop constraint products_product_labels_all_pid_pname_i_id_uniq;

alter table public.products_product_labels_all
    add constraint products_product_labels_all_pid_i_id_uniq
        unique (product_id, i_id);"""


# ----- 1022 增加新字段 -----
ProductLabelsAllAddOtherLabelsSQL = """ALTER TABLE PUBLIC.products_product_labels_all ADD "other_labels" varchar(65533) NULL;
COMMENT ON COLUMN products_product_labels_all."other_labels" IS '其他标签';"""