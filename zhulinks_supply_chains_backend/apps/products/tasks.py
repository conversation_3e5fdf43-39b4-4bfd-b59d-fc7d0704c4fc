# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-09-14 16:55:01
# @Last Modified by:   <PERSON><PERSON> <PERSON>
# @Last Modified time: 2024-08-07 13:18:16
import copy
import json
import os
import random
import re
import time
import traceback
import uuid
import warnings
from copy import deepcopy
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from urllib.parse import unquote

import httpx
import oss2
from celery import shared_task
from common.formats import DATE_FORMAT
from common.models import DownloadTasks, LiveAuthor, get_download_max_tasks_count_config, get_feishu_robots_config, get_feishu_table_config, get_product_expire_config
from common.utils import convert_datatime_val_2_aware_time, get_current_user_type, parse_spec_code
from common.utils_v2.processing_translation import convert_ser_data, json_content_translation_replacement
from companies.caches import get_distributor_info_by_letters_v2
from django.apps import apps
from django.conf import settings
from django.core.paginator import Paginator
from django.db import IntegrityError, connection, transaction
from django.db.models import Case, Count, F, Max, Min, Prefetch, Q, QuerySet, Sum, When
from django.http import HttpRequest
from django.utils import timezone
from products import logger
from products.bulk_query import (
    bulk_query_confirm_state_log,
    bulk_query_labels,
    bulk_query_latest_history_price_with_auth_info,
    bulk_query_latest_history_price_with_auth_info_by_product_pk_list,
    bulk_query_skus_specs_detail,
    bulk_query_skus_specs_detail_with_sku_pk_list,
)
from products.data_shops_logics.common import handle_add_product_task_response, handle_edit_product_task_response
from products.data_shops_logics.datashop_product import update_dy_sku_code
from products.data_shops_logics.datashop_products_utils import edit_product, search_material, upload_product
from products.models import (
    CostItemConfig,
    DataShop,
    FailedUploadProductJST,
    JSTShopSKUMap,
    Product,
    ProductAttrOption,
    ProductCategoryList,
    ProductLinkDistributor,
    ProductReview,
    ProductSelectionItem,
    ProductSelectionItemSKU,
    ProductSelectionPlan,
    ProductSelectionPlanCategory,
    ProductSelectionPlanCompany,
    ProductSelectionPlanRecordType,
    SelectionPlanLabelMark,
    StockKeepingUnit,
    SubProduct,
    TempProductMap,
)
from products.models_v2 import EXProductsAssociationModel
from products.models_v2.inventory import DistributorWarehouseInventory
from products.product_info_version import bulk_create_or_get_latest_history
from products.serializers import DBOperateSelectionPlanProductListDownloadSerializer, MyProductCreateSer, OperateSelectionPlanProductListDownloadSerializer
from products.tasks_v2.daily_new_product_subscribe import dail_new_products_subscribe
from products.tasks_v2.dd_products_detail_synchronous import dd_products_detail_synchronous
from products.tasks_v2.dd_products_synchronous import dd_products_synchronous
from products.tasks_v2.dd_sku_association_synchronous import dd_sku_association_synchronous
from products.tasks_v2.dd_sku_synchronous import dd_sku_synchronous
from products.tasks_v2.dd_specs_synchronous import dd_specs_synchronous
from products.tasks_v2.feishu_daily_new_product import feishu_daily_new_product
from products.tasks_v2.product_region_stats_command import product_region_stats_command
from rest_framework.request import Request
from users.models import User, UserDataSource, UserDistributorRelationShip, UserExtraAuth
from utils.aliyun_functions import generate_sts_credential, retry_fc
from utils.aliyun_oss import copy_file, object_is_exists, upload_file_with_bytes
from utils.caches import get_user_real_name_by_user_id
from utils.download_tmp import (
    DBMode2SelectionPlanDownloadAllProductTmpl,
    DBOperatorSelectionPlanDownloadAllProductTmpl,
    DBOperatorSelectionPlanDownloadProductTmpl,
    OperatorSelectionPlanDownloadAllProductTmpl,
    OperatorSelectionPlanDownloadProductTmpl,
    get_excel_async,
)
from utils.en_pptx import generate_en_pptx
from utils.feishu import FeiShuDocx
from utils.http_handle import custom_filter
from utils.jst import JSTAPIClient, jst_client, upload_supplier_to_JST
from utils.oss_v2 import upload_object_to_oss_with_bytes
from utils.pptx import generate_pptx
from utils.redis_lock import gen_redis_cache, gen_redis_conn, read_lock, redis_cache, redis_conn, release_lock
from utils.vector_database import add_image_data, create_collection, delete_data

from zhulinks_supply_chains_backend.celery import app

# 获取或创建collection
database_id = settings.QDRANT_DATABASE_ID
embedding_type = "image"
create_collection(database_id, embedding_type)


# create_collection(database_id, embedding_type, size=768)


@shared_task
def print_env_vars():
    env_vars = dict(os.environ)
    print(print_env_vars.request.id)
    return env_vars


@app.task(queue="long_running_tasks")
def flush_product_selection_plan_to_end(plan_id: int = None):
    """
    将直播日期已过的选品计划置为结束状态
    任务设置执行时间为早上8点钟
    SKU:
        现货库存 -
    """
    try:
        today = timezone.now().date()
        plan_qs = ProductSelectionPlan.objects.filter(live_date_end__lt=today).exclude(state=0)
        if plan_id:
            plan_qs = plan_qs.filter(plan_id=plan_id)
        # 释放product和sku的库存

        plan_id_list = []

        with transaction.atomic():
            for plan in plan_qs:
                plan_id_list.append(plan.plan_id)

                item_rows_updated = 0
                item_sku_rows_updated = 0

                item_skus = plan.productselectionitemsku_set.prefetch_related("sku", "item").filter(become_history=False, item__is_deleted=False)

                # 记录最新的sku历史版本id
                sku_qs = [item_sku.sku for item_sku in item_skus]
                sku_histories, product_histories = bulk_create_or_get_latest_history(sku_qs)

                need_update_item_objs = []
                need_update_item_sku_objs = []

                for item_sku in item_skus:
                    sku = item_sku.sku
                    item = item_sku.item

                    if item_sku.physical_inventory:
                        # 更新现货库存
                        origin_physical_inventory = sku.physical_inventory
                        origin_plan_use_inventory = sku.plan_use_inventory

                        # 已播扣现货库存，没播只扣占用库存
                        # if item_sku.item.has_live:
                        #     sku.physical_inventory = origin_physical_inventory - item_sku.actual_sales
                        # else:
                        #     sku.physical_inventory = origin_physical_inventory

                        sku.plan_use_inventory = origin_plan_use_inventory - item_sku.physical_inventory
                        sku.save(update_fields=["plan_use_inventory"])
                        print(f">>> 更新sku:{sku},原库存:{origin_physical_inventory}")

                    # 暂时保留该数据
                    sku_history_info = {
                        "cost_price": float(sku.cost_price) if sku.cost_price is not None else None,
                        "retail_price": float(sku.retail_price) if sku.retail_price is not None else None,
                        "physical_inventory": sku.physical_inventory,
                        "warehouse_inventory": sku.warehouse_inventory,
                        "safety_inventory": sku.safety_inventory,
                    }

                    # 需要保留item的商品历史id
                    item.product_version_id = None
                    if sku.product_id in product_histories:
                        product_history_obj = product_histories.get(sku.product_id)
                        item.product_version_id = product_history_obj.history_id
                        need_update_item_objs.append(item)

                        # 保存商品当前的属性,用于货盘统计
                        sku_history_info["product_attrs"] = product_history_obj.attr_options_data

                    # 需要保留item_sku的历史sku_id
                    item_sku.sku_version_id = None
                    if sku.id in sku_histories:
                        item_sku.sku_version_id = sku_histories.get(sku.id).history_id
                    item_sku.sku_history_info = sku_history_info
                    need_update_item_sku_objs.append(item_sku)

                # 更新货盘item
                if need_update_item_objs:
                    item_rows_updated = ProductSelectionItem.objects.bulk_update(need_update_item_objs, fields=["product_version_id"])

                # 更新货盘item_sku
                if need_update_item_sku_objs:
                    item_sku_rows_updated = ProductSelectionItemSKU.objects.bulk_update(need_update_item_sku_objs, fields=["sku_version_id", "sku_history_info"])

                logger.warning(f">>> 更新item:{item_rows_updated}, item_sku:{item_sku_rows_updated}")

                # 如果有修改的需要进行释放
                user_id = read_lock(plan.plan_id)
                if user_id:
                    release_lock(plan.plan_id, user_id)

            print(f">>> 更新货盘:{plan_qs}")
            # 获取此时有效的成本价计划
            cost_item_config = CostItemConfig.objects.filter(status="IE").first()
            if cost_item_config:
                res = plan_qs.update(state=0, cost_item_config=cost_item_config)
            else:
                res = plan_qs.update(state=0)

        # 统计货盘
        for p_id in plan_id_list:
            calculate_ended_plan_data.delay(p_id)

        return f"Success {today}:{res}"
    except Exception as e:
        return f"Failed: {str(e)}"


@app.task(queue="common_tasks")
def delete_product(product_id):
    """
    删除商品
    """
    try:
        del_images = []
        product = Product.objects.filter(product_id=product_id).first()
        if not product:
            return
        if product.main_images:
            del_images.extend(deepcopy(product.main_images))
        if product.detail_images_backup:
            del_images.extend(deepcopy(product.detail_images_backup))

        skus_qs = StockKeepingUnit.objects.filter(product=product)
        for sku in skus_qs:
            if sku.image:
                del_images.append(sku.image)
        # 删除oss图片
        for image in del_images:
            try:
                credential = generate_sts_credential(
                    settings.ALIYUN_OSS_ROLE_ARN_DEL,
                    str(uuid.uuid4()),
                )

                endpoint = "http://oss-cn-guangzhou.aliyuncs.com"
                file_name = image.split("https://zhulinks.oss-cn-guangzhou.aliyuncs.com/")[1]

                buckname = "zhulinks"
                auth = oss2.StsAuth(
                    credential.get("access_key_id"),
                    credential.get("access_key_secret"),
                    credential.get("security_token"),
                )
                bucket = oss2.Bucket(auth, endpoint, buckname)

                try:
                    res = bucket.delete_object(file_name)
                    if res.status != 200:
                        logger.error(f"FileUpload oss delete Err: {res.status}")
                except oss2.exceptions.NoSuchKey:
                    # 处理文件不存在的情况
                    pass
            except Exception as e:
                logger.error(f"{str(e)}")
                continue
        # 删除sku
        if skus_qs:
            skus_qs.delete()

        # 删除attroption
        attr_option_qs = ProductAttrOption.objects.filter(product=product)
        if attr_option_qs:
            attr_option_qs.delete()

        # 删除productreview
        product_review_qs = ProductReview.objects.filter(product=product)
        if product_review_qs:
            product_review_qs.delete()

        # 删除qrant
        res = delete_data(
            database_id=settings.QDRANT_DATABASE_ID,
            object_id=product.id,
            embedding_type="image",
        )

        # 删除product
        product.delete()

        return "Success"
    except Exception as e:
        return f"Failed: {str(e)}"


@app.task(queue="common_tasks")
def delete_images_from_qdrant(object_id, raw_data=None):
    """
    从qdrant数据库中删除图片数据

    Args:
        object_id (_type_): product.id
        raw_data (_type_, optional): image_url. Defaults to None.

    Returns:
        _type_: _description_
    """
    try:
        res = delete_data(
            database_id=database_id,
            object_id=object_id,
            embedding_type="image",
            raw_data=raw_data,
        )
        logger.error(f"res:{res}")
    except Exception as e:
        logger.error(str(e))
        return f"Failed: {str(e)}"


@app.task(queue="common_tasks")
def add_images_to_qdrant(object_id, images):
    """
    将图片加入qdrant数据库:
    object_id: product.id
    images: [url_1, url_2]

    """
    try:
        for image in images:
            add_image_data(
                image_url=image,
                database_id=database_id,
                object_id=object_id,
            )
        return "Success"
    except Exception as e:
        logger.error(str(e))
        return f"Failed: {str(e)}"


@app.task(queue="long_running_tasks")
def flush_product_image_to_qdrant(**filters):
    """
    将所选商品的图片刷入qdrant中
    """
    Product = apps.get_model("products", "Product")
    StockKeepingUnit = apps.get_model("products", "StockKeepingUnit")

    product_qs = Product.objects.filter(**filters).values("id", "main_images", "detail_images").order_by("-id")
    page_size = 50
    paginator = Paginator(product_qs, page_size)
    total_page = paginator.num_pages
    for i in range(total_page):
        page_model_objects = paginator.page(i + 1)
        for product in page_model_objects:
            main_images = product.get("main_images", [])
            # detail_images = product.get("detail_images", [])
            sku_dicts = StockKeepingUnit.objects.filter(product_id=product["id"], become_history=False).values("sku_id", "image")
            sku_images = [j["image"] for j in sku_dicts if j.get("image")]
            images = main_images + sku_images
            images = list(set(images))
            logger.info(f"insert qdrant: {product['id']}--{images}")
            add_images_to_qdrant.delay(product["id"], images=images)
            # add_images_to_qdrant.delay(product["id"], images=images)


@app.task(queue="long_running_tasks")
def flush_sub_product_main_image_to_qdrant():
    SubProduct = apps.get_model("products", "SubProduct")
    sub_products_qs = SubProduct.objects.select_related(
        "parent_product",
    ).filter(
        is_deleted=False,
        parent_product__is_deleted=False,
    )
    paginator = Paginator(sub_products_qs, 1000)  # 分批处理，每批1000条
    for page_num in paginator.page_range:
        print(f"=== page_num {page_num} ===")
        for sub_product in paginator.page(page_num):
            object_id = sub_product.parent_product.id
            sub_images = sub_product.main_images
            main_images = sub_product.parent_product.main_images
            images = set(sub_images) - set(main_images)
            # print(f"insert qdrant: {object_id}--{images}")
            logger.info(f"insert qdrant: {object_id}--{images}")
            add_images_to_qdrant.delay(object_id, images=images)
    print("flush_sub_product_main_image_to_qdrant completed.")


@app.task(queue="common_tasks")
def w_product_selection_plan_record(plan_id, product_id, record_type, content, remark, create_user):
    ProductSelectionPlanRecord = apps.get_model("products", "ProductSelectionPlanRecord")

    record = ProductSelectionPlanRecord(
        selection_plan_id=plan_id,
        product_id=product_id,
        record_type=record_type,
        content=content,
        remark=remark,
        create_user=create_user,
    ).save()


def w_plan_record(
    plan_id: int,
    record_type: str,
    content: str,
    create_user: int | str,
    remark: str = "",
    product_id: int = None,
):
    """
    写入货盘表操作明细

    Args:
        plan_id (int): 货盘表ID
        record_type (str): 操作类型 请参考 models.ProductSelectionPlanRecordType
        content (str): 操作内容 例: 备注从[xxx]修改为[yyy]
        create_user (int | str): 用户user id
        remark (str, optional): 备注信息，默认为空. Defaults to "".
        product_id (int, optional): 商品id, 可能不涉及到货盘表商品的操作. Defaults to None.
    """
    w_product_selection_plan_record.delay(
        plan_id,
        product_id,
        record_type,
        content,
        remark or "",
        create_user,
    )


@app.task(queue="common_tasks")
def update_company_category(product_id, is_deleted):
    """
    更新商品对应的在选品计划中的公司、分类
    """
    ProductSelectionItem = apps.get_model("products", "ProductSelectionItem")
    ProductSelectionPlanCategory = apps.get_model("products", "ProductSelectionPlanCategory")
    ProductSelectionPlanCompany = apps.get_model("products", "ProductSelectionPlanCompany")

    try:
        item_qs = ProductSelectionItem.objects.filter(product_id=product_id, is_deleted=False)
        # if is_deleted:
        #     # 商品标记为删除，直接删除选品计划条目
        #     item_qs.delete()
        #     return "Success"
        for item in item_qs:
            product = item.product
            company = product.company
            category = product.category
            plan_category = item.plan_category
            plan_company = plan_category.plan_company
            # 二级分类被修改
            if str(plan_category.category_id) != str(category[1]):
                # 查看是否存在新分类id的分类数据，如果存在，则绑定到新分类，如不存在，则新建分类
                # 检查旧分类下是否还有商品条目，如无则删除之（无需）
                existed_category = ProductSelectionPlanCategory.objects.filter(
                    plan_company=plan_company,
                    category_id=category[1],
                ).first()
                if existed_category:
                    # 获取分类中最大的商品排序
                    max_order_dict = (
                        ProductSelectionItem.objects.filter(
                            plan_category=existed_category,
                            is_deleted=False,
                        )
                        .values("order")
                        .order_by("-order")
                        .first()
                    )
                    if not max_order_dict:
                        max_order = 0
                    else:
                        max_order = max_order_dict.get("order")

                    item.plan_category = existed_category
                    item.order = max_order + 1
                    item.save(update_fields=["plan_category", "order"])
                else:
                    # 获取公司下二级分类最大排序并创建新的分类
                    max_category_order_dict = (
                        ProductSelectionPlanCategory.objects.filter(
                            plan_company=plan_company,
                        )
                        .values("order")
                        .order_by("-order")
                        .first()
                    )
                    if not max_category_order_dict:
                        max_category_order = 0
                    else:
                        max_category_order = max_category_order_dict.get("order")
                    new_plan_category = ProductSelectionPlanCategory.objects.create(
                        plan_company=plan_company,
                        category_id=category[1],
                        order=max_category_order + 1,
                    )
                    item.plan_category = new_plan_category
                    item.order = 1
                    item.save(update_fields=["plan_category", "order"])
            # 公司被修改
            if plan_company.company.id != company.id:
                # 查看是否存在新公司id的公司，如果存在，则绑定到新公司，如不存在，则新建公司
                # 检查旧公司下是否还有分类，分类下是否还有其他商品，如果均无则删除之(无需)
                existed_company = ProductSelectionPlanCompany.objects.filter(
                    selection_plan_id=item.selection_plan.plan_id,
                    company_id=company.company_id,
                ).first()
                if existed_company:
                    # 查看该公司下是否有可用分类
                    existed_category = ProductSelectionPlanCategory.objects.filter(
                        plan_company=existed_company,
                        category_id=category[1],
                    ).first()
                    if existed_category:
                        # 获取分类中最大商品排序
                        max_order_dict = (
                            ProductSelectionItem.objects.filter(
                                plan_category=existed_category,
                                is_deleted=False,
                            )
                            .values("order")
                            .order_by("-order")
                            .first()
                        )
                        if not max_order_dict:
                            max_order = 0
                        else:
                            max_order = max_order_dict.get("order")

                        item.plan_category = existed_category
                        item.order = max_order + 1
                        item.save(update_fields=["plan_category", "order"])
                    else:
                        # 获取公司下二级分类最大排序并创建新的分类
                        max_category_order_dict = (
                            ProductSelectionPlanCategory.objects.filter(
                                plan_company=existed_company,
                            )
                            .values("order")
                            .order_by("-order")
                            .first()
                        )
                        if not max_category_order_dict:
                            max_category_order = 0
                        else:
                            max_category_order = max_category_order_dict.get("order")
                        new_plan_category = ProductSelectionPlanCategory.objects.create(
                            plan_company=existed_company,
                            category_id=category[1],
                            order=max_category_order + 1,
                        )
                        item.plan_category = new_plan_category
                        item.order = 1
                        item.save(update_fields=["plan_category", "order"])
                else:
                    # 获取公司最大排序并创建新的计划公司
                    max_company_order_dict = (
                        ProductSelectionPlanCompany.objects.filter(
                            selection_plan_id=item.selection_plan.plan_id,
                            is_deleted=False,
                        )
                        .values("order")
                        .order_by("-order")
                        .first()
                    )
                    if not max_company_order_dict:
                        max_company_order = 0
                    else:
                        max_company_order = max_company_order_dict.get("order")
                    new_plan_company = ProductSelectionPlanCompany.objects.create(
                        selection_plan_id=item.selection_plan.plan_id,
                        company_id=company.company_id,
                        order=max_company_order + 1,
                    )
                    new_plan_category = ProductSelectionPlanCategory.objects.create(
                        plan_company=new_plan_company,
                        category_id=category[1],
                        order=1,
                    )
                    item.plan_category = new_plan_category
                    item.order = 1
                    item.save(update_fields=["plan_category", "order"])

        return "Success"

    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        return f"Failed: {product_id}-{str(e)}"


@app.task(queue="common_tasks")
def copy_oss_images(raw_image_path: str, new_image_path: str):
    """
    复制oss对象
    :param raw_image_path:
    :param new_image_path:
    :return:
    """
    try:
        raw_image_path = unquote(raw_image_path.replace("https://zhulinks.oss-cn-guangzhou.aliyuncs.com/", ""))
        new_image_path = unquote(new_image_path.replace("https://zhulinks.oss-cn-guangzhou.aliyuncs.com/", ""))

        data = generate_sts_credential(
            settings.ALIYUN_OSS_ROLE_ARN_ADD,
            str(uuid.uuid4()),
        )
        copy_response = copy_file(data["access_key_id"], data["access_key_secret"], data["security_token"], raw_image_path, new_image_path)
        logger.info(f"copy image result:{copy_response}.")
    except Exception as e:
        logger.warning(f"复制oss文件失败.{e}. RawImage:{raw_image_path},NewImage:{new_image_path}")


@app.task(queue="celery")
def send_product_create_notify(
    product_id,
    product_code,
    product_name,
    supplier_name,
    live_date,
    product_state,
    distributor_name,
    cost_price,
    image_url,
):
    """
    发送商品提报的通知
    :param product_id: 商品id
    :param product_code: 商品货号
    :param product_name: 商品名称
    :param supplier_name: 供应商名称
    :param live_date: 直播日期
    :param product_state: 商品状态
    :param distributor_name: 分销商名称
    :param cost_price: 成本价
    :param image_url: 图片地址
    :return:
    """
    feishu_client = FeiShuDocx()
    response = feishu_client.send_product_create_msg(
        product_id,
        product_code,
        product_name,
        supplier_name,
        live_date,
        product_state,
        distributor_name,
        cost_price,
        image_url,
    )
    logger.info(f">>>>飞书发送新品提报通知，返回结果:{response.status_code}-{response.text}")


@app.task(queue="celery")
def send_sp_product_create_notify(
    product_id,
    product_code,
    product_name,
    supplier_name,
    physical_inventory,
    product_state,
    advice_price,
    cost_price,
    image_url,
):
    """
    发送商品提报的通知
    :param product_id: 商品id
    :param product_code: 商品货号
    :param product_name: 商品名称
    :param supplier_name: 供应商名称
    :param live_date: 直播日期
    :param product_state: 商品状态
    :param distributor_name: 分销商名称
    :param cost_price: 成本价
    :param image_url: 图片地址
    :return:
    """
    feishu_client = FeiShuDocx()
    response = feishu_client.send_sp_product_create_msg(
        product_id,
        product_code,
        product_name,
        supplier_name,
        physical_inventory,
        product_state,
        advice_price,
        cost_price,
        image_url,
    )
    logger.info(f">>>>供应商飞书发送新品提报通知，返回结果:{response.status_code}-{response.text}")


@app.task(queue="subscribe_tasks", ignore_result=True)
def jst_sync_inventory(request_data: dict):
    """
    聚水潭仓库库存数据同步
    https://openweb.jushuitan.com/message-doc?docType=sku&docId=business_sku_syn

    :param request_data:
    {
        "charset": "utf-8",
        "biz": '{"customizeQty2":0.0000,"customizeQty1":0.0000,"iId":"A8496","pickLock":0.0000,"returnQty":55.0000,"coid":12317661,"purchaseQty":0.0000,"inQty":0.0000,"defectiveQty":0.0000,"qty":184.0000,"name":"【云上叙】7  A8496-S925银镶嵌南红戒指-多样性发-8mm（东哥）","customizeQty9":0.0000,"modified":"2024-01-16 11:48:31.533","customizeQty8":0.0000,"customizeQty10":140.0000,"customizeQty7":0.0000,"orderLock":0.0000,"maxQty":0,"customizeQty6":0.0000,"customizeQty5":0.0000,"skuId":"01GZ2312084016","customizeQty4":0.0000,"virtualQty":0.0000,"customizeQty3":0.0000}',
        "app_key": "bf8c53e0eaae4d7dba23cdb4ed8f2a13",
        "action_code": "business_sku_syn",
        "sign": "ceab71b9a6b24887cc400bf1527e2bee",
        "version": "2",
        "timestamp": "1705376976",
    }
    :return:
    """

    biz_data = request_data.get("biz")
    if not biz_data:
        return "FAIL"

    # {'customizeQty2': 0.0, 'customizeQty1': 0.0, 'iId': 'A8496', 'pickLock': 0.0, 'returnQty': 55.0, 'coid': 12317661, 'purchaseQty': 0.0, 'inQty': 0.0, 'defectiveQty': 0.0, 'qty': 184.0, 'name': '【云上叙】7  A8496-S925银镶嵌南红戒指-多样性发-8mm（东哥）', 'customizeQty9': 0.0, 'modified': '2024-01-16 11:48:31.533', 'customizeQty8': 0.0, 'customizeQty10': 140.0, 'customizeQty7': 0.0, 'orderLock': 0.0, 'maxQty': 0, 'customizeQty6': 0.0, 'customizeQty5': 0.0, 'skuId': '01GZ2312084016', 'customizeQty4': 0.0, 'virtualQty': 0.0, 'customizeQty3': 0.0}
    jst_product_data = json.loads(biz_data)

    sku_id = jst_product_data.get("skuId", "")
    qty = int(jst_product_data.get("qty", 0))
    order_lock = int(jst_product_data.get("orderLock", 0))
    raw_spec_code, parsed_spec_code, letters = parse_spec_code(sku_id)

    filter_spec_code = parsed_spec_code or raw_spec_code
    skus = StockKeepingUnit.objects.filter(spec_code=filter_spec_code, become_history=False)
    if not skus.exists():
        return f"FAIL: {sku_id} not match"

    distributor_info = get_distributor_info_by_letters_v2(letters)
    if distributor_info.distributor_pk:
        for sku in skus:
            DistributorWarehouseInventory.objects.update_or_create(
                sku_id=sku.sku_id,
                distributor_id=distributor_info.distributor_id,
                defaults={
                    "warehouse_inventory": qty - order_lock,
                    "qty": qty,
                    "order_lock_qty": order_lock,
                },
            )


@app.task(queue="common_tasks", ignore_result=True)
def jst_sync_inventory_v2(sku_id, qty, order_lock):
    """
    聚水潭仓库库存数据同步
    https://openweb.jushuitan.com/message-doc?docType=sku&docId=business_sku_syn

    :param request_data:
    {
        "charset": "utf-8",
        "biz": '{"customizeQty2":0.0000,"customizeQty1":0.0000,"iId":"A8496","pickLock":0.0000,"returnQty":55.0000,"coid":12317661,"purchaseQty":0.0000,"inQty":0.0000,"defectiveQty":0.0000,"qty":184.0000,"name":"【云上叙】7  A8496-S925银镶嵌南红戒指-多样性发-8mm（东哥）","customizeQty9":0.0000,"modified":"2024-01-16 11:48:31.533","customizeQty8":0.0000,"customizeQty10":140.0000,"customizeQty7":0.0000,"orderLock":0.0000,"maxQty":0,"customizeQty6":0.0000,"customizeQty5":0.0000,"skuId":"01GZ2312084016","customizeQty4":0.0000,"virtualQty":0.0000,"customizeQty3":0.0000}',
        "app_key": "bf8c53e0eaae4d7dba23cdb4ed8f2a13",
        "action_code": "business_sku_syn",
        "sign": "ceab71b9a6b24887cc400bf1527e2bee",
        "version": "2",
        "timestamp": "1705376976",
    }
    :return:
    """

    raw_spec_code, parsed_spec_code, letters = parse_spec_code(sku_id)

    filter_spec_code = parsed_spec_code or raw_spec_code
    skus = StockKeepingUnit.objects.filter(spec_code=filter_spec_code, become_history=False)
    if not skus.exists():
        return f"FAIL: {sku_id} not match"

    distributor_info = get_distributor_info_by_letters_v2(letters)
    if distributor_info.distributor_pk:
        for sku in skus:
            DistributorWarehouseInventory.objects.update_or_create(
                sku_id=sku.sku_id,
                distributor_id=distributor_info.distributor_id,
                defaults={
                    "warehouse_inventory": qty - order_lock,
                    "qty": qty,
                    "order_lock_qty": order_lock,
                },
            )


@shared_task
def jst_init_inventory():
    """初始化商品库存"""
    try:
        jst = JSTAPIClient()
        start_ts = 0
        redis_cache = gen_redis_cache()
        jst_init_key = "jst_init_unique_key"

        while 1:
            jst_response = jst.fetch_inventory(start_ts)

            inventorys = jst_response["data"]["inventorys"]
            if not inventorys:
                redis_cache.delete(jst_init_key)
                break

            inventory_map = {}
            max_ts_list = []
            for inventory_data in inventorys:
                sku_id = inventory_data["sku_id"]
                qty = inventory_data["qty"]
                data_ts = inventory_data["ts"]

                inventory_map[sku_id] = int(qty)
                max_ts_list.append(data_ts)

            max_ts = max(max_ts_list)
            print(f">>>{max_ts}")
            if max_ts == start_ts:
                print(f"complete with max_ts:{max_ts}")
                redis_cache.delete(jst_init_key)
                break
            StockKeepingUnit = apps.get_model("products", "StockKeepingUnit")
            SysProductModHistory = apps.get_model("products", "SysProductModHistory")
            skus = StockKeepingUnit.objects.filter(spec_code__in=inventory_map.keys(), become_history=False)

            # 添加
            mod_history_objs = [
                SysProductModHistory(
                    product=sku.product,
                    sku=sku,
                    mod_field_name="warehouse_inventory",
                    old_value=sku.warehouse_inventory,
                    new_value=inventory_map.get(sku.spec_code),
                    create_user="system",
                    create_date=datetime.now(),
                )
                for sku in skus
                if sku.warehouse_inventory != inventory_map.get(sku.spec_code)
            ]

            with transaction.atomic():
                SysProductModHistory.objects.bulk_create(mod_history_objs)

                # 更新sku
                for sku in skus:
                    sku.warehouse_inventory = inventory_map.get(sku.spec_code)

                StockKeepingUnit.objects.bulk_update(skus, ["warehouse_inventory"])

            start_ts = max_ts
            time.sleep(round(random.uniform(0, 0.2), 2))

    except Exception as e:
        logger.warning(f"初始化库存失败,{e}.{traceback.format_exc()}")


@shared_task
def flush_attr_name(attr_id: int, old_name: str, name: str):
    """
    attr list 更新name后，刷新attr_option和product spec_lists
    :param old_name:
    :param attr_id:
    :param name:
    :return:
    """
    ProductAttrOption = apps.get_model("products", "ProductAttrOption")
    Product = apps.get_model("products", "Product")
    # 更新attr_option
    ProductAttrOption.objects.filter(attr_id=attr_id).update(name=name)

    # todo:更新Product spec_lists
    # spec_lists_field_size = Product.spec_lists.field.size

    # base_q = Q()
    # for i in range(spec_lists_field_size):
    #     base_q.add(Q(**{f"spec_lists__{i}__source_id": attr_id}), Q.OR)


def upload_category_to_JST_system(category: QuerySet):
    """
    上传分类
    """
    # 配置上传数据
    biz = {
        "pv_names": list(category.attrs.all().values_list("id", flat=True)),
        "c_name": category.name,
        "enable": False if category.is_deleted else True,
    }
    if category.parent:
        biz["parent_c_id"] = category.parent.JST_id
    if category.JST_id:
        biz["c_id"] = category.JST_id

    client = JSTAPIClient()
    url = "/open/webapi/itemapi/category/addorupdate"
    res = client.request(url, biz)
    return res.get("c_id")


def upload_category_with_parents(category: QuerySet):
    """
    上传带父级的分类
    """
    # 如果该分类有父级分类，并且父级分类还没有上传，先上传父级分类
    if category.parent and not category.parent.JST_id:
        category.parent.JST_id = upload_category_with_parents(category.parent)
        category.parent.save()

    # 现在上传当前分类
    category.JST_id = upload_category_to_JST_system(category)
    category.save()
    time.sleep(0.6)
    return category.JST_id


@app.task(queue="common_tasks")
def sync_category_to_JST(category_id):
    """
    同步商品分类数据至聚水潭
    """

    print(f"sync_category_to_JST SYNC_TO_JST: {settings.SYNC_TO_JST}/{settings.ENV}")
    if not settings.SYNC_TO_JST:
        return "Failed: SYNC_TO_JST is False"
    try:
        ProductCategoryList = apps.get_model("products", "ProductCategoryList")
        category = ProductCategoryList.objects.filter(id=category_id).first()
        if not category:
            return f"Failed: category {category_id} not existed"
        JST_id = upload_category_with_parents(category)
        return f"Sucess: {JST_id}"
    except Exception as e:
        return f"Failed: {str(e)}"


def _get_skus_specs_detail(obj):
    StockKeepingUnit = apps.get_model("products", "StockKeepingUnit")
    specs_names = StockKeepingUnit.specs_name.through._meta.model.objects.select_related("specskey").filter(stockkeepingunit=obj).order_by("id")

    specs_names_map = {specs_name.specskey_id: specs_name.specskey.name for specs_name in specs_names}

    specs_values_relations = (
        StockKeepingUnit.specs_value.through._meta.model.objects.select_related("specsvalue")
        .filter(
            stockkeepingunit=obj,
            specsvalue__spec_key_id__in=specs_names_map.keys(),
        )
        .order_by("id")
    )
    val_data = []
    for specs_values_relation in specs_values_relations:
        value_id = specs_values_relation.specsvalue_id
        value = specs_values_relation.specsvalue.value
        value_key_id = specs_values_relation.specsvalue.spec_key_id
        val_data.append(
            {
                "name_id": value_key_id,
                "value_id": value_id,
                "value": value,
            }
        )

    re_data = []
    tmp_list = []
    for name_id, name in specs_names_map.items():
        tmp_list.append(
            {
                "name_id": name_id,
                "name": name,
                "values": [
                    {
                        "value_id": val["value_id"],
                        "value": val["value"],
                    }
                    for val in val_data
                    if val["name_id"] == name_id
                ],
            }
        )

    for val in tmp_list:
        _values = val["values"]
        for _val in _values:
            re_data.append(
                {
                    "name_id": val["name_id"],
                    "name": val["name"],
                    "value_id": _val["value_id"],
                    "value": _val["value"],
                }
            )

    return re_data


def query_product_in_JST(code_list: list):
    """
    查询指定货号数据是否在聚水潭
    Args:
        code_list (list): _description_

    Returns:
        {
          code: [spec_code, spec_code]
        }
    """

    re_data = {}
    if not code_list:
        return {}
    biz = {"i_ids": code_list}
    client = JSTAPIClient()
    url = "/open/mall/item/query"
    res = client.request(url, biz)
    datas = res.get("datas", [])
    for data in datas:
        skus = data.get("skus", [])
        code = data["i_id"]
        code_dict = {}
        for u in skus:
            code_dict[u["sku_id"]] = {
                "name": u["name"],
                "i_id": u["i_id"],
            }
        re_data[code] = code_dict
    return re_data


def query_product_in_jst_by_spec_code(spec_code_list: list):
    """
    根据商品查询商品是否在聚水潭
    :param spec_code_list: 商编列表
    :return:
    商编对应的货号
    {
        sku_id:code
    }
    """
    if not spec_code_list:
        return {}
    client = JSTAPIClient()
    result = {}
    # 最大支持20个
    for i in range(0, len(spec_code_list), 20):
        query_spec_code = spec_code_list[i : i + 20]
        ret = client.query_product_with_sku_spec_code_list(query_spec_code)
        datas = ret.get("datas", [])
        for data in datas:
            result[data["sku_id"]] = {
                "name": data["name"],
                "i_id": data["i_id"],
                "brand": data["brand"],
            }
    return result


@app.task(queue="celery")
def sync_product_to_JST(product_id, sub_product_id=None):
    """
    同步商品副本数据至聚水潭
    Args:
        product_id (_type_): 需要同步的主商品
        sub_product_id (_type_, optional): 如有值，只同步指定的商品副本数据，默认None

    Returns:
        _type_: run result
        :param product_id:
        :param sub_product_id:
    """

    print(f"sync_product_to_JST SYNC_TO_JST: {settings.SYNC_TO_JST}/{settings.ENV}")
    if not settings.SYNC_TO_JST:
        return "Failed: SYNC_TO_JST is False"
    try:

        def get_product_type_name(_product):
            for i in _product.PRODUCT_TYPE_CHOICES:
                if i[0] == _product.product_type:
                    if _product.product_type == "SP":
                        return "成品"
                    return i[1]

        def get_category(category_id):
            cate_qs = ProductCategoryList.objects.filter(id=category_id).first()
            return cate_qs

        product = Product.objects.filter(product_id=product_id).first()
        if not product:
            return f"Failed: {product_id} not found"

        product_category = product.category
        if not product_category:
            return f"Failed: {product_id} missing category"

        if not isinstance(product_category, list):
            return "Failed: {} invalid category type. current_type: {}".format(product_id, type("product_category"))

        if len(product_category) < 2:
            return f"Failed {product_id} category is not completed"

        #  供应商没在聚水潭，先上传供应商
        if not product.company.JST_name:
            print(f"handle company: {product.company.name}")
            upload_supplier_to_JST(product.company.company_id)

        # 获取尺寸和款式
        size = product.size if product.size else ""
        attr_value = ProductAttrOption.objects.filter(product=product, name="款式").select_related("attr_value").values("attr_value__name").first()
        style_name = attr_value["attr_value__name"] if attr_value else ""

        # 获取sku数据
        items = []
        skus = product.stockkeepingunit_set.filter(become_history=False)
        for sku in skus:
            spec_code = sku.spec_code
            if not spec_code:
                continue
            item = {
                "sku_id": sku.spec_code,
                "inner_sku_id": sku.sku_id,
            }
            if sku.cost_price:
                item["c_price"] = round(float(sku.cost_price), 2)
            if sku.retail_price:
                item["s_price"] = round(float(sku.retail_price), 2)
            item["item_type"] = get_product_type_name(product)
            item["enabled"] = 0 if product.is_deleted else 1
            item["supplier_name"] = product.company.name
            # 最后一级分类，改成二级分类
            last_category = get_category(product_category[-1])
            second_category = get_category(product_category[1])
            #
            item["c_name"] = last_category.name
            item["brand"] = second_category.name
            item["vc_name"] = style_name
            # 先看分类是否上传过聚水潭，没有则上传
            if not last_category.JST_id:
                upload_category_with_parents(last_category)
            # 颜色及规格
            specs = _get_skus_specs_detail(sku)
            if specs:
                item["properties_value"] = ";".join([f"{i.get('name')}:{i.get('value')}" for i in specs])
            item["stock_disabled"] = False
            if product.unit:
                item["unit"] = product.unit.name
            items.append(item)
        if not items:
            return "Failed: sku of product{product_id} not found"

        result = {}
        client = JSTAPIClient()
        # 查询商品副本, 去除disable_push 禁止推送的副本
        if not sub_product_id:
            sub_product_qs = SubProduct.objects.prefetch_related("owner").filter(parent_product=product, owner__disable_push=False)
        else:
            sub_product_qs = SubProduct.objects.filter(product_id=sub_product_id, owner__disable_push=False)

        if not sub_product_qs:
            return {"SUCCESS": True, "REMARK": "没有副本商品,不需要上传"}

        # code_list = [sub.code for sub in sub_product_qs]
        # 先查询是否已经在聚水潭有数据，如有则不更新名称和图片
        # existed_JST = query_product_in_JST(code_list)
        # logger.info(items)
        spec_code_list = [item["sku_id"] + sub.owner.letters for sub in sub_product_qs for item in items]
        jst_map = query_product_in_jst_by_spec_code(spec_code_list)

        for sub_product in sub_product_qs:
            code = sub_product.code
            #
            original_code = copy.deepcopy(code)

            distributor = sub_product.owner
            letters = distributor.letters
            distributor_name = distributor.name

            if letters in ["XP", "XY", "XZ", "X"] or distributor.push_inited_code:
                inited_code = sub_product.inited_code
                # 用初始化code代替
                if inited_code:
                    code = inited_code
            if not letters:
                result[code] = f"subproduct({sub_product.product_id}) owner's letters not found"
                continue
            sub_items = deepcopy(items)

            shop_sku_items_data = []

            for sub_item in sub_items:
                # 款式编码 - 货号
                sub_item["i_id"] = code
                sub_item["pic"] = sub_product.main_images[0]
                sub_item["pic_big"] = sub_product.main_images[0]

                post_name = code + "-" + sub_product.name + "-" + size if size else code + "-" + sub_product.name

                if distributor.is_self_support or (product.is_in_distributor_market and letters == "FX"):
                    post_name += "（珠凌）"

                sub_item["name"] = post_name
                spec_code = sub_item["sku_id"] + letters
                sub_item["sku_id"] = spec_code
                sub_item["remark"] = sub_product.remark

                if distributor_name:
                    sub_item["labels"] = [distributor_name]

                # spec_code_dict = existed_JST.get(code, {})
                old_obj = jst_map.get(spec_code)
                if old_obj:
                    if distributor.is_self_support or (product.is_in_distributor_market and letters == "FX"):
                        sub_item["name"] = sub_item["name"]
                    else:
                        sub_item["name"] = old_obj["name"]
                    # sub_item["brand"] = old_obj["brand"]
                    sub_item.pop("pic")
                    sub_item.pop("pic_big")

                    jst_code = old_obj["i_id"]
                    if jst_code == original_code:
                        # 相同的货号以聚水潭的为准
                        sub_item["i_id"] = jst_code
                    elif letters in ["XP", "XY", "XZ", "X"] or distributor.push_inited_code:
                        old_code_pattern = re.compile(r".*-\w")
                        matcher = re.match(old_code_pattern, jst_code)
                        if matcher:
                            sub_item["i_id"] = jst_code

                # 自营的需要上传到店铺商品资料
                # 加入到分销市场也需要同步到店铺商品资料
                if distributor.is_self_support or (product.is_in_distributor_market and letters == "FX"):
                    temp_shop_sku_item = dict(
                        sku_id=sub_item["inner_sku_id"],
                        spec_code=sub_item["sku_id"],
                        jst_sku_id=spec_code,
                        jst_i_id=sub_item["i_id"],
                        jst_sku_code=sub_item["inner_sku_id"],
                        jst_shop_i_id=sub_item["i_id"],
                        jst_shop_sku_id=spec_code,
                        jst_original_sku_id=sub_item["sku_id"],
                        jst_name=sub_item["name"],
                        jst_shop_properties_value=sub_item.get("properties_value", "默认规格"),
                        jst_sku_sign="",
                    )

                    shop_sku_items_data.append(temp_shop_sku_item)

                # if spec_code in spec_code_dict:
                #     # 聚水潭中有数据，不更新名字和图片
                #     old_obj = spec_code_dict.get(spec_code)
                #     sub_item["name"] = old_obj["name"]
                #     # 聚水潭的货号
                #     jst_code = old_obj["i_id"]
                #
                #     sub_item.pop("pic")
                #     sub_item.pop("pic_big")
                #     # 聚水潭中有数据, 判断旧的规则
                #     old_code_pattern = re.compile(r".*-\w")
                #     matcher = re.match(old_code_pattern, jst_code)
                #     if matcher:
                #         sub_item["i_id"] = jst_code

            biz = {"items": sub_items}
            logger.warning(f">>>>{biz}")
            url = "/open/jushuitan/itemsku/upload"
            res = client.request(url, biz)
            result[code] = res
            time.sleep(0.6)

            # 同步商品到店铺资料
            if shop_sku_items_data:
                if product.is_in_distributor_market:
                    sync_product_to_jst_shop.delay(settings.DISTRIBUTOR_MODE_SHOP_ID, shop_sku_items_data)
                else:
                    sync_product_to_jst_shop.delay(settings.JST_SELF_OPERATED_SHOP_ID, shop_sku_items_data)

        return {"SUCCESS": True, "REMARK": result}
    except Exception as e:
        obj, created = FailedUploadProductJST.objects.update_or_create(
            product_id=product_id,
            sub_product_id=sub_product_id,
            defaults={"reason": str(e)},
        )
        return {"SUCCESS": False, "REMARK": str(e)}


@app.task(queue="common_tasks")
def sync_combine_product_to_jst(product_id, sub_product_id: int = None, product_name: str = None):
    print(f"sync_combine_product_to_jst current_env: {settings.SYNC_TO_JST}/{settings.ENV}")
    if not settings.SYNC_TO_JST:
        return {"SUCCESS": False, "REMARK": "Failed: SYNC_TO_JST is False"}

    def handle_company(company):
        if company.JST_name:
            print(f"handle company: {company.name}")
            upload_supplier_to_JST(company.company_id)

    def generate_post_data(sub_product, skus, db_letters):
        post_data = {"items": []}
        for sku in skus:
            sub_skus = sku.parent_sub_skus.filter(become_history=False).values("relate_sku__spec_code", "num")

            push_name = sku.specs[0]["value"]

            post_data["items"].append(
                {
                    "i_id": sub_product.code,  # 货号
                    "sku_id": f"{sku.spec_code}{db_letters}",  # 商编
                    "remark": "",
                    "childList": [
                        {
                            "qty": sub_sku["num"],
                            "src_sku_id": f"{sub_sku['relate_sku__spec_code']}{db_letters}",
                            "sale_price": float(sku.retail_price or 0) or 0,
                        }
                        for sub_sku in sub_skus
                    ],
                    "pic": sku.image,
                    "properties_value": push_name,
                    # "name": product_name or sub_product.name,
                    "name": push_name,
                    "short_name": "",
                    "brand": "",
                    "cost_price": float(sku.cost_price or 0),
                }
            )
        return post_data

    try:
        if sub_product_id:
            sub_product = SubProduct.objects.get(product_id=sub_product_id)
            products = [sub_product]
        else:
            product = Product.objects.get(product_id=product_id, is_deleted=False, is_combine=True)
            products = product.subproduct_set.filter(is_deleted=False)

        remark_map = {}
        for sub_product in products:
            handle_company(sub_product.parent_product.company)
            db_letters = sub_product.owner.letters
            skus = sub_product.parent_product.stockkeepingunit_set.filter(become_history=False)
            post_data = generate_post_data(sub_product, skus, db_letters)
            print(post_data)
            jst_client = JSTAPIClient()
            ret = jst_client.combine_sku_bulk_upload(post_data)
            remark_map[sub_product.product_id] = ret
        return {"SUCCESS": True, "REMARK": json.dumps(remark_map, ensure_ascii=False)}
    except SubProduct.DoesNotExist:
        return {"SUCCESS": False, "REMARK": f"Failed: SubProduct {sub_product_id} not found"}
    except Product.DoesNotExist:
        return {"SUCCESS": False, "REMARK": f"Failed: 商品ID {product_id} not found"}
    except Exception as e:
        FailedUploadProductJST.objects.update_or_create(
            product_id=product_id,
            sub_product_id=sub_product_id,
            defaults={"reason": str(e), "is_combine": True},
        )

        print(traceback.format_exc())

        return {"SUCCESS": False, "REMARK": str(e)}


@app.task(queue="long_running_tasks")
def retry_failed_uploads():
    """
    重试上传至聚水潭失败的数据
    """
    FailedUploadProductJST = apps.get_model("products", "FailedUploadProductJST")
    failed_uploads_qs = FailedUploadProductJST.objects.all().order_by("-id")
    paginator = Paginator(failed_uploads_qs, 1000)
    for page_num in paginator.page_range:
        print(f"=== page_num {page_num} ===")
        for upload in paginator.page(page_num):
            if upload.is_combine:
                result = sync_combine_product_to_jst(upload.product_id, upload.sub_product_id)
            else:
                result = sync_product_to_JST(upload.product_id, upload.sub_product_id)
            if isinstance(result, dict) and result.get("SUCCESS"):
                print(f"{upload.product_id}-{upload.sub_product_id} retry upload completed")
                upload.delete()
            else:
                print(f"{upload.product_id}-{upload.sub_product_id} retry upload failed: {result}")
            time.sleep(1)


@app.task(queue="celery")
def selection_plan_listener_task(post_data: dict):
    """
    货盘访问日志记录
    :param post_data:
    :return:
    """
    feishu_client = FeiShuDocx()
    new_post_data = feishu_client.plan_record_decode(post_data)
    resp, is_success = feishu_client.table_record_insert_one(new_post_data)
    logger.info(f"插入飞书多维表格响应:{resp}")
    return is_success


def send_selection_plan_listener_task(request: Request, page_desc: str, selection_plan):
    try:
        current_user_type_map = {
            "OP": "运营商",
            "DB": "分销商",
            "SP": "供应商",
        }
        current_user_type = get_current_user_type(request)
        current_user = request.user
        # 组织参数
        absolute_uri = request._request.build_absolute_uri()
        platform = current_user_type_map.get(current_user_type, "其他")
        now_time = datetime.now()
        access_time = str(now_time)
        access_date = now_time.strftime(DATE_FORMAT)
        post_data = {
            "url": absolute_uri,  # 请求url
            "page_desc": page_desc,  # 请求页面
            "plan_id": str(selection_plan.plan_id),  # 货盘ID
            "plan_name": selection_plan.name,  # 货盘名称
            "platform": platform,  # 操作端
            "user_real_name": current_user.real_name or "",  # 用户真实姓名
            "user_id": str(current_user.user_id),  # 用户id
            "mobile": str(current_user.mobile or ""),  # 用户手机号
            "access_time": access_time,  # 访问时间
            "access_date": access_date,  # 访问日期
        }
        selection_plan_listener_task.delay(post_data)
    except Exception as e:
        logger.warning(f"发送货盘日志监听失败,{e}{traceback.format_exc()}")


@shared_task
def update_hosting_products_state():
    """
    定时更新托管商品状态,30分钟更新一次
    :return:
    """
    HostingProducts = apps.get_model("products", "HostingProducts")
    now = datetime.now()
    updated_rows = HostingProducts.objects.filter(state=1, expire_date__lte=now, is_deleted=False).update(state=2)
    logger.info(f">>>更新托管商品数据 {updated_rows} 条. 时间:{now}")


@app.task(queue="common_tasks")
def update_products_new_state():
    """
    每天更新一次商品的is_new状态
    :return:
    """
    Product = apps.get_model("products", "Product")
    now = datetime.now()

    Product.objects.filter(is_deleted=False, is_new=True, expire_date__lte=now).update(is_new=False)

    Product.objects.filter(
        is_deleted=False,
        is_new=False,
        expire_date__isnull=True,
    ).update(
        expire_date=F("create_date") + timedelta(days=int(get_product_expire_config().value)),
    )

    Product.objects.filter(is_deleted=False, is_new=False, expire_date__lte=now).update(is_new=False)


@app.task(queue="common_tasks")
def upload_sub_product_data():
    SubProduct = apps.get_model("products", "SubProduct")
    sub_products_qs = SubProduct.objects.filter(parent_product__is_deleted=False).order_by("-id")
    paginator = Paginator(sub_products_qs, 1000)
    for page_num in paginator.page_range:
        print(f"=== page_num {page_num} ===")
        for sub_product in paginator.page(page_num):
            print(f"sub_product: {sub_product.product_id}")
            sync_product_to_JST(sub_product.parent_product.product_id, sub_product_id=sub_product.product_id)
            time.sleep(1)
    print("upload product to JST completed")


@app.task(queue="common_tasks")
def inventory_warning_check():
    # 查询库存不足的货盘商品
    ProductSelectionPlan = apps.get_model("products", "ProductSelectionPlan")
    plans = ProductSelectionPlan.objects.filter(state__in=[1, 2])
    now_date_str = datetime.now().strftime("%Y%m%d")

    for plan in plans:
        user_id = plan.create_user
        items = plan.productselectionitem_set.select_related("product").filter(is_deleted=False, product__can_use_inventory__lt=0)
        for item in items:
            cache_key = f"plan_notify:{now_date_str}_{plan.plan_id}_{item.product_id}"
            val = redis_cache.get(cache_key)
            if str(val) == str(user_id):
                continue

            try:
                try:
                    auth = UserExtraAuth.objects.get(user_id=user_id, data_source=UserDataSource.FEISHU)
                except UserExtraAuth.DoesNotExist:
                    cli = FeiShuDocx()
                    user = User.objects.get(user_id=user_id)
                    feishu_response = cli.get_user_id(user.mobile)
                    print(feishu_response)
                    user_data = feishu_response["data"]["user_list"][0]
                    if not user_data.get("user_id"):
                        logger.warning(f">>>无法获取飞书ID,飞书响应:{feishu_response}")
                        continue

                    auth = UserExtraAuth.objects.create(user_id=user, data_source=UserDataSource.FEISHU, extenal_id=user_data.get("user_id"))
            except Exception as e:
                logger.warning(f">>>获取飞书id失败,{e}.{traceback.format_exc()}")
                continue

            product = item.product
            sub_product = item.sub_product if item.sub_product else product

            send_inventory_warning_notify.delay(
                user_id,
                cache_key,
                auth.extenal_id,
                product.main_images[0],
                plan.name,
                plan.plan_id,
                sub_product.name,
                product.product_id,
                sub_product.code,
                item.physical_inventory,
                product.physical_inventory,
                product.plan_use_inventory,
                product.can_use_inventory,
            )
            break


@app.task(queue="celery")
def send_inventory_warning_notify(
    user_id,
    cache_key,
    extenal_id,
    img_url,
    plan_name,
    plan_id,
    product_name,
    product_id,
    code,
    plan_physical_inventory,
    product_physical_inventory,
    product_plan_use_inventory,
    product_can_use_inventory,
):
    try:
        cli = FeiShuDocx()
        plan_info = f"{plan_name}({plan_id})"
        product_info = product_name + f"({product_id})"
        response = cli.feishu_inventory_warning_notify(
            extenal_id,
            img_url,
            plan_info,
            product_info,
            code,
            plan_physical_inventory,
            product_physical_inventory,
            product_plan_use_inventory,
            product_can_use_inventory,
        )
        if response["code"] != 0:
            logger.warning(f">>>发送飞书失败:{response}")
            return "FAIL"
        # 设置redis缓存,24小时
        redis_cache.set(cache_key, str(user_id), 60 * 60 * 24)
        return "SUCCESS"
    except Exception as e:
        logger.warning(f">>>发送飞书失败:{e},{traceback.format_exc()}")
        return "FAIL"


@app.task(queue="long_running_tasks")
def handle_jst_shop_products(handle_data: list, create_user_id: int | str):
    warnings.filterwarnings("ignore", "DateTimeField .* received a naive datetime")
    try:
        JSTShopProduct = apps.get_model("products", "JSTShopProduct")
        post_count = len(handle_data)
        batch_count = 100

        need_create_objects = []
        need_update_objects = []
        start_time = time.time()
        with transaction.atomic():
            for i in range(0, post_count, batch_count):
                batch_data = handle_data[i : i + batch_count]
                # 最新的一条
                _tmp = {}
                for _d in batch_data:
                    dy_sku_id = _d["dy_sku_id"]
                    if dy_sku_id not in _tmp:
                        _tmp[dy_sku_id] = _d
                    else:
                        if _d.get("create_time") > _tmp[dy_sku_id]["create_time"]:
                            _tmp[dy_sku_id] = _d

                batch_data = _tmp.values()
                for data in batch_data:
                    data.pop("create_time")

                dy_sku_ids = [x["dy_sku_id"] for x in batch_data]

                jst_products = JSTShopProduct.objects.filter(dy_sku_id__in=dy_sku_ids)
                jst_products_map = {str(jst_product.dy_sku_id): jst_product for jst_product in jst_products}

                for j in batch_data:
                    if str(j["dy_sku_id"]) in jst_products_map:
                        jst_product = jst_products_map[str(j["dy_sku_id"])]
                        if jst_product.hash_code == j["hash_code"]:
                            continue
                        jst_product.__dict__.update(**j)
                        jst_product.update_user = create_user_id
                        jst_product.update_date = timezone.now()
                        need_update_objects.append(jst_product)
                    else:
                        need_create_objects.append(JSTShopProduct(create_user=create_user_id, **j))
            created_rows = updated_rows = 0
            if need_create_objects:
                created_rows = len(JSTShopProduct.objects.bulk_create(need_create_objects, batch_size=1000, ignore_conflicts=True))
            if need_update_objects:
                updated_rows = JSTShopProduct.objects.bulk_update(need_update_objects, fields=JSTShopProduct.update_fields(), batch_size=1000)

        logger.info(f"------ 聚水潭店铺商品处理完成, 耗时:{time.time() - start_time}, 创建:{created_rows},更新:{updated_rows} ------")
        return True
    except Exception as e:
        logger.warning(f"同步聚水潭店铺商品错误,{e}.{traceback.format_exc()}")
        return False
    finally:
        redis_con = gen_redis_conn()
        redis_con.decr("jst:shop_product_task")


@app.task(queue="common_tasks")
def generate_pptx_async(datas):
    ppt_buffer = generate_pptx(datas)
    # 文件上传至oss
    aliyun_certs = generate_sts_credential(
        settings.ALIYUN_OSS_ROLE_ARN_ADD,
        str(uuid.uuid4()),
    )
    download_url = upload_file_with_bytes(
        aliyun_certs["access_key_id"],
        aliyun_certs["access_key_secret"],
        aliyun_certs["security_token"],
        ppt_buffer,
        f"handcard_{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}{str(uuid.uuid4())[:8]}.pptx",
        base_path="user-upload/searchtemp/images/",
        endpoint="http://oss-cn-guangzhou.aliyuncs.com",
        buckname="zhulinks",
    )
    return download_url


# 定义转换函数
def normalize_data(data):
    if isinstance(data, list):  # 如果是列表，逐个递归处理
        return [normalize_data(item) for item in data]
    elif isinstance(data, dict):  # 如果是字典，递归处理值
        return {key: normalize_data(value) for key, value in data.items()}
    elif isinstance(data, Decimal):  # 如果是 Decimal，转换为 float
        return float(data)
    elif data is None:  # 如果是 None，转换为空字符串
        return ""
    else:  # 其他类型直接返回
        return data


@app.task(queue="common_tasks")
def generate_en_pptx_async(datas):
    # 转换数据格式
    convert_data = convert_ser_data(datas)
    # 进行内容翻译
    translated_content = json_content_translation_replacement(convert_data)

    ppt_buffer = generate_en_pptx(translated_content)

    # 文件上传至oss
    aliyun_certs = generate_sts_credential(
        settings.ALIYUN_OSS_ROLE_ARN_ADD,
        str(uuid.uuid4()),
    )
    download_url = upload_file_with_bytes(
        aliyun_certs["access_key_id"],
        aliyun_certs["access_key_secret"],
        aliyun_certs["security_token"],
        ppt_buffer,
        f"handcard_{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}{str(uuid.uuid4())[:8]}.pptx",
        base_path="user-upload/searchtemp/images/",
        endpoint="http://oss-cn-guangzhou.aliyuncs.com",
        buckname="zhulinks",
    )
    return download_url


# @shared_task
# def generate_pptx_async(datas):
#     ppt_buffer = generate_pptx(datas)
#     # 文件上传至oss
#     aliyun_certs = generate_sts_credential(
#         settings.ALIYUN_OSS_ROLE_ARN_ADD,
#         str(uuid.uuid4()),
#     )
#     objec_file_name = upload_file_with_bytes(
#         aliyun_certs["access_key_id"],
#         aliyun_certs["access_key_secret"],
#         aliyun_certs["security_token"],
#         ppt_buffer,
#         f"hangcard_{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}{str(uuid.uuid4())[:8]}.pptx",
#         base_path="user-upload/searchtemp/images/",
#         endpoint="http://oss-cn-guangzhou.aliyuncs.com",
#         buckname="zhulinks",
#     )
#     download_url = "https://zhulinks.com/v1/products/handcard/stream" + objec_file_name
#     return download_url


@app.task(queue="common_tasks")
def check_and_update_config_status():
    """
    更新成本项配置状态
    """
    CostItemConfig = apps.get_model("products", "CostItemConfig")
    now_utc = timezone.now()
    now = timezone.localtime(now_utc)
    active_config = CostItemConfig.objects.filter(status="IE").first()
    next_config = (
        CostItemConfig.objects.filter(
            effective_time__gte=now,
            status="NE",
        )
        .order_by("effective_time")
        .first()
    )
    if next_config:
        if active_config:
            active_config.status = "ED"
            active_config.save()
        next_config.status = "IE"
        next_config.save()
    if active_config:
        # 生效时间大于当前时间且大于生效任务的生效时间置为已结束
        other_config = CostItemConfig.objects.filter(Q(effective_time__gte=now) & Q(effective_time__gt=active_config.effective_time) & Q(status="NE")).order_by("effective_time")
        if other_config:
            other_config.update(status="ED")
    # 安排下一个任务切换
    # schedule_next_task_change()


# def schedule_next_task_change():
#     CostItemConfig = apps.get_model("products", "CostItemConfig")
#     now_utc = timezone.now()
#     now = timezone.localtime(now_utc)
#     next_inactive_task = CostItemConfig.objects.filter(effective_time__gte=now, status="NE").order_by("effective_time").first()
#     if next_inactive_task:
#         next_effective_time = next_inactive_task.effective_time
#         check_and_update_config_status.delay(eta=next_effective_time)


@app.task(queue="common_tasks")
def scan_dy_upload_beat_tasks():
    DataShopUploadTasks = apps.get_model("products", "DataShopUploadTasks")
    # 扫描还没有上传商品的任务
    tasks = DataShopUploadTasks.objects.filter(state__in=[0, 2])
    for task in tasks:
        if task.task_type == "EDIT":
            last_map = task.sub_product.datashopproductmap_set.filter(data_shop=task.data_shop).last()
            sync_product_to_dy_task.delay(task.task_id, "edit", last_map.task_id)
            continue
        sync_product_to_dy_task.delay(task.task_id)


@app.task(queue="common_tasks")
def sync_product_to_dy_task(task_id: str, sync_type: str = "add", last_map_id: str = None):
    if sync_type == "edit" and not last_map_id:
        return "FAIL: Missing `last_map_id` While sync_type is edit "

    sync_function_map = {
        "add": upload_product,
        "edit": edit_product,
    }

    response_function_map = {
        "add": handle_add_product_task_response,
        "edit": handle_edit_product_task_response,
    }

    if sync_type not in sync_function_map:
        return "FAIL: Invalid SyncType"

    conn = None
    _key = f"upload_tasks:{task_id}"
    try:
        conn = gen_redis_conn()

        ret = conn.setnx(_key, 1)
        if not ret:
            # 正在执行中
            return f"SUCCESS:{task_id} IN PROCESSING"

        DataShopUploadTasks = apps.get_model("products", "DataShopUploadTasks")
        DataShopMaterial = apps.get_model("products", "DataShopMaterial")

        try:
            task = DataShopUploadTasks.objects.get(task_id=task_id)
        except DataShopUploadTasks.DoesNotExist:
            return f"FAIL:{task_id} NOT FOUND"
        # 10分钟过期
        conn.expire(_key, 60 * 10)

        shop_pk = task.data_shop_id
        materials = task.datashopmaterial_set.all()
        materials_state_list = [m.audit_state for m in materials]
        if 4 in materials_state_list:
            task.error_reason = task.error_reason + ",".join([f"REQ:{x.request_id}图片{x.error_reason}" for x in materials])
            task.state = 1
            task.save()
            return "SUCCESS"
        elif all(x == 3 for x in materials_state_list):
            post_data = task.post_data
            # 替换image_url
            _byte_url_list = [j.byte_url for j in materials]
            post_data["pic"] = "|".join(_byte_url_list)
            # 详情列表 description  暂时用主图
            post_data["description"] = "|".join(_byte_url_list)
            resp = sync_function_map[sync_type](shop_pk, post_data)
            # 处理结果
            ret = response_function_map[sync_type](resp, task, post_data, last_map_id)
            return ret

        need_update_objs = []

        for material in materials:
            material_info_list = search_material(shop_pk, material.material_id)["material_info_list"]
            for material_info in material_info_list:
                if material.material_id == material_info["material_id"]:
                    material.byte_url = material_info["byte_url"]
                    material.audit_state = material_info["audit_status"]
                    material.error_reason = material_info["audit_reject_desc"]
                    material.size = material_info["size"]
                    material.create_time = convert_datatime_val_2_aware_time(material_info.get("create_time"))
                    material.update_time = convert_datatime_val_2_aware_time(material_info.get("update_time"))
                    material.delete_time = convert_datatime_val_2_aware_time(material_info.get("delete_time"))
                    material.video_info = material_info.get("videoInfo")
                    material.photo_info = material_info["photoInfo"]
                    material.material_state = material_info["operate_status"]
                    material.update_date = timezone.now()
                    need_update_objs.append(material)

        DataShopMaterial.objects.bulk_update(
            need_update_objs,
            fields=[
                "byte_url",
                "audit_state",
                "error_reason",
                "size",
                "create_time",
                "update_time",
                "delete_time",
                "video_info",
                "photo_info",
                "material_state",
                "update_date",
            ],
        )

        new_materials = task.datashopmaterial_set.order_by("id").all().only("audit_state", "error_reason", "request_id", "byte_url", "origin_url")

        new_materials_state_list = [m.audit_state for m in new_materials]
        if 4 in new_materials_state_list:
            task.error_reason = task.error_reason + ",".join([f"REQ:{x.request_id}图片{x.error_reason}" for x in new_materials])
            task.state = 1
            task.save()
            ret = "SUCCESS"
        elif all(x == 3 for x in new_materials_state_list):
            post_data = task.post_data
            # 替换image_url
            _byte_url_list = [j.byte_url for j in new_materials]
            post_data["pic"] = "|".join(_byte_url_list)
            # 详情列表 description  暂时用主图
            post_data["description"] = "|".join(_byte_url_list)
            resp = sync_function_map[sync_type](shop_pk, post_data)
            # 处理结果
            ret = response_function_map[sync_type](resp, task, post_data, last_map_id)

        return ret
    except Exception as e:
        logger.warning(traceback.format_exc())
        return f"FAIL: UNKNOWN EXCEPTION: {e}"
    finally:
        if conn:
            conn.delete(_key)


@app.task(queue="common_tasks")
def subscribe_material_result_message(data: str):
    try:
        sync_function_map = {
            "add": upload_product,
            "edit": edit_product,
        }

        response_function_map = {
            "add": handle_add_product_task_response,
            "edit": handle_edit_product_task_response,
        }

        DataShopMaterial = apps.get_model("products", "DataShopMaterial")

        # task_cache_key = f"dy_material_ret:{task_id}"
        dict_data = json.loads(data)
        audit_state = dict_data["audit_status"]
        audit_state_text = dict_data["audit_status_desc"]
        material_id = dict_data["material_id"]
        material_type_map = {"photo": 1, "video": 2}
        material = DataShopMaterial.objects.filter(material_id=material_id, audit_state__in=[0, 1]).last()

        if not material:
            logger.warning(f">>>{material_id}找不到")
            return

        material.audit_state = audit_state
        material.error_reason = audit_state_text
        material.byte_url = dict_data["byte_url"]
        material.create_time = dict_data["create_time"]
        material.delete_time = dict_data["delete_time"] or None
        material.material_type = material_type_map.get(dict_data["material_type"], 1)
        material.name = dict_data["name"]
        material.material_state = dict_data["operate_status"]
        material.photo_info = dict_data["photo_info"]
        material.size = dict_data["size"]
        material.update_time = dict_data["update_time"]
        material.video_info = dict_data["video_info"]
        material.save()

        # 设置redis状态
        r = gen_redis_conn()
        task_id = material.task_id
        task_cache_key = f"dy_material_ret:{task_id}"
        task_status_map = {3: "pass", 4: "fail"}
        r.hset(task_cache_key, material_id, task_status_map[audit_state])
        if r.ttl(task_cache_key) == -2:
            logger.warning(f">>>>{task_id}-{material_id}任务已超时")
            return

        all_pass = True
        materials = r.hgetall(task_cache_key)
        for material_id, status in materials.items():
            if status.decode("utf-8") == "fail":
                # print("有素材失败，任务无法完成")
                task = material.task
                material_objs = DataShopMaterial.objects.filter(task_id=task_id).only("request_id", "error_reason")
                task.error_reason = task.error_reason + ",".join([f"REQ:{x.request_id}图片{x.error_reason}" for x in material_objs])
                task.state = 1
                task.save(update_fields=["error_reason", "state"])
                # 删除key
                r.delete(task_cache_key)
                return

            if status.decode("utf-8") == "pending":
                all_pass = False

        if all_pass:
            print("所有素材都通过，执行上传商品操作")
            task = material.task
            post_data = task.post_data
            material_objs = DataShopMaterial.objects.filter(task_id=task_id)
            # 替换image_url
            _byte_url_list = [j.byte_url for j in material_objs]
            post_data["pic"] = "|".join(_byte_url_list)
            # 详情列表 description  暂时用主图
            post_data["description"] = "|".join(_byte_url_list)
            resp = sync_function_map[task.task_type.lower()](task.data_shop_id, post_data)
            # 处理结果
            ret = response_function_map[task.task_type.lower()](resp, task, post_data, task_id)
            #
            r.delete(task_cache_key)
            return ret
    except Exception as e:
        logger.error(f"素材订阅错误:{e}{traceback.format_exc()}")


def get_or_create_selection_plan_company(plan_id, company_id):
    retry_times = 3

    for i in range(retry_times):
        plan_company = ProductSelectionPlanCompany.objects.filter(selection_plan_id=plan_id, company_id=company_id).first()
        if plan_company:
            return plan_company

        # 获取公司最大排序并创建新的计划公司
        max_company_order_dict = (
            ProductSelectionPlanCompany.objects.filter(
                selection_plan_id=plan_id,
                is_deleted=False,
            )
            .values("order")
            .order_by("-order")
            .first()
        )
        if not max_company_order_dict:
            max_company_order = 0
        else:
            max_company_order = max_company_order_dict.get("order")

        try:
            plan_company, _ = ProductSelectionPlanCompany.objects.get_or_create(
                selection_plan_id=plan_id,
                company_id=company_id,
                order=max_company_order + 1,
            )
            return plan_company
        except Exception as e:
            print(f"catch selection company create error: {e}")
            time.sleep(0.5)
    else:
        return


def get_or_create_selection_plan_category(plan_company, category_id):
    retry_times = 3
    for i in range(retry_times):
        plan_category = plan_company.productselectionplancategory_set.all().filter(category_id=category_id).first()
        if plan_category:
            return plan_category

        # 获取二级分类最大排序并创建新的分类
        max_category_order_dict = (
            ProductSelectionPlanCategory.objects.filter(
                plan_company=plan_company,
                is_deleted=False,
            )
            .values("order")
            .order_by("-order")
            .first()
        )
        if not max_category_order_dict:
            max_category_order = 0
        else:
            max_category_order = max_category_order_dict.get("order")

        try:
            plan_category, _ = ProductSelectionPlanCategory.objects.get_or_create(
                plan_company=plan_company,
                category_id=category_id,
                order=max_category_order + 1,
            )
            return plan_category
        except Exception as e:
            print(f"catch selection category create error: {e}")
            time.sleep(0.5)
    else:
        return


@app.task(queue="common_tasks")
def async_add_product_to_plan(
    user_id: int,
    plan_id: int,
    product_id: int,
    post_sku_data: list,
    create_user_type="OP",
    **kwargs,
):
    """
    加入货盘计划
    :param user_id: 当前用户id
    :param plan_id: 货盘计划id
    :param product_id: 商品product_id
    :param post_sku_data: sku列表信息
    :param create_user_type: 创建用户的端
    :param kwargs:
    :return:
    """
    try:
        if not isinstance(post_sku_data, list):
            raise ValueError("提交信息错误")
        if not post_sku_data:
            raise ValueError("sku信息不能为空")
        # 特殊处理
        is_copy_or_bulk_move = kwargs.get("is_copy_or_bulk_move", False)

        try:
            selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
        except ProductSelectionPlan.DoesNotExist:
            raise ValueError(f"货盘`{plan_id}`不存在")

        # 确定是否已经在计划中
        item_qs = ProductSelectionItem.objects.filter(selection_plan_id=plan_id, product_id=product_id, is_deleted=False)
        if item_qs.exists():
            raise ValueError("商品已在货盘中")

        try:
            product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            raise ValueError("数据不存在")
        # 判断商品是否在上架状态
        if product.state != 1:
            raise ValueError("该商品尚未上架")
        # 判断sku data
        skus = product.stockkeepingunit_set.filter(become_history=False).only("sku_id", "physical_inventory", "plan_use_inventory")

        inventory_skus_map = {
            sku.sku_id: {
                "physical_inventory": sku.physical_inventory or 0,
                "plan_use_inventory": sku.plan_use_inventory or 0,
            }
            for sku in skus
        }

        for sku_data in post_sku_data:
            sku_id = sku_data.get("sku_id")
            physical_inventory = sku_data.get("physical_inventory", 0) or 0

            if not str(sku_id).isdigit():
                raise ValueError(f"非法SKU_ID:{sku_id}")

            if not sku_id or not inventory_skus_map.get(int(sku_id)):
                raise ValueError("错误SKU信息,请刷新页面后重试")
            # 特殊处理，不需要校验
            if is_copy_or_bulk_move:
                continue

            sku_inventory_obj = inventory_skus_map.get(int(sku_id))

            can_use_inventory = sku_inventory_obj.get("physical_inventory") - sku_inventory_obj.get("plan_use_inventory")

            if can_use_inventory < 0:
                raise ValueError(f"{sku_id}可用库存为:{can_use_inventory}, 请联系专员维护")

            if physical_inventory > can_use_inventory:
                raise ValueError(f"{sku_id}数量:{physical_inventory}. 已超过可用库存: {can_use_inventory}")

        try:
            with transaction.atomic():
                # 公司
                product_company = product.company
                # 代码里面增加重试逻辑，防止celery冲突
                plan_company = get_or_create_selection_plan_company(plan_id, product_company.company_id)
                if not plan_company:
                    raise ValueError("failed to create plan_company")

                # 公司下的二级分类
                category_list = product.category
                if len(category_list) < 3:
                    raise ValueError("category_list length less than 3")

                plan_category = get_or_create_selection_plan_category(plan_company, category_list[1])
                if not plan_category:
                    raise ValueError("failed to create plan_category")

                # 商品条目
                max_order_dict = (
                    ProductSelectionItem.objects.filter(
                        plan_category=plan_category,
                        is_deleted=False,
                    )
                    .values("order")
                    .order_by("-order")
                    .first()
                )

                max_map_order_dict = (
                    ProductSelectionItem.objects.filter(
                        selection_plan_id=plan_id,
                        is_deleted=False,
                    )
                    .values("map_order")
                    .order_by("-map_order")
                    .first()
                )
                if not max_order_dict:
                    max_order = 0
                else:
                    max_order = max_order_dict.get("order")

                if not max_map_order_dict:
                    max_map_order = 0
                else:
                    max_map_order = max_map_order_dict.get("map_order")

                item_data = {
                    "selection_plan_id": plan_id,
                    "plan_category_id": plan_category.id,
                    "product_id": product_id,
                    "create_user": user_id,
                    "update_user": user_id,
                    "order": max_order + 1,
                    "map_order": max_map_order + 1,
                    "create_user_type": create_user_type,
                    "remark": kwargs.get("item_remark", ""),
                    "selection_reason": kwargs.get("selection_reason", "") or "",
                }

                # 增加sub_product_id
                try:
                    sub_product = SubProduct.objects.get(
                        parent_product__product_id=product_id,
                        owner=selection_plan.distributor,
                    )
                except SubProduct.DoesNotExist:
                    post_data = product.__dict__
                    post_data["parent_product"] = product.pk
                    post_data["owner"] = selection_plan.distributor_id
                    post_data["create_user"] = user_id
                    ser = MyProductCreateSer(data=post_data)
                    if ser.is_valid():
                        sub_product = ser.save()

                        # 创建关联分销商货号数据
                        ProductLinkDistributor(
                            product=product,
                            distributor_id=selection_plan.distributor_id,
                            code=sub_product.code,
                            create_user=user_id,
                        ).save()
                    else:
                        logger.warning(f"异步加入货盘失败,{ser.errors}")
                        sub_product = None

                if sub_product:
                    item_data["sub_product_id"] = sub_product.product_id

                item = ProductSelectionItem.objects.create(**item_data)
                # 库存锁
                ts_point = transaction.savepoint()

                try_times = 3

                for sku_data in post_sku_data:
                    if is_copy_or_bulk_move:
                        sku_id = sku_data.get("sku_id")
                        physical_inventory = sku_data.get("physical_inventory", 0) or 0
                        # 创建item sku
                        ProductSelectionItemSKU.objects.create(
                            selection_plan=selection_plan,
                            sku_id=sku_id,
                            item=item,
                            physical_inventory=physical_inventory,
                            estimated_sales=physical_inventory,
                            remark=sku_data.get("remark", "") or "",
                            create_user=user_id,
                        )
                    else:
                        for i in range(try_times):
                            sku_id = sku_data.get("sku_id")
                            physical_inventory = sku_data.get("physical_inventory", 0) or 0

                            if not str(sku_id).isdigit():
                                transaction.savepoint_rollback(ts_point)
                                raise ValueError(f"非法SKU_ID:{sku_id}")

                            try:
                                _sku = StockKeepingUnit.objects.get(sku_id=sku_id, become_history=False)
                            except StockKeepingUnit.DoesNotExist:
                                transaction.savepoint_rollback(ts_point)
                                raise ValueError(f"SKU_ID:{sku_id}不存在")

                            if (physical_inventory > _sku.can_use_inventory) or (physical_inventory > (_sku.physical_inventory - _sku.plan_use_inventory)):
                                transaction.savepoint_rollback(ts_point)
                                raise ValueError(f"SKU_ID:{sku_id}库存不足")

                            # 判断组合商品库存
                            if product.is_combine:
                                sub_skus = _sku.parent_sub_skus.filter(become_history=False)
                                for sub_sku in sub_skus:
                                    relate_sku = sub_sku.relate_sku
                                    act_need_inventory = sub_sku.num * physical_inventory

                                    if (act_need_inventory > (relate_sku.can_use_inventory or 0)) or (
                                        act_need_inventory > ((relate_sku.physical_inventory or 0) - (relate_sku.plan_use_inventory or 0))
                                    ):
                                        transaction.savepoint_rollback(ts_point)
                                        logger.info(f"组合商品加货盘库存不足 >> {sku_id},{product_id},{relate_sku}")
                                        raise ValueError("组合商品库存不足")

                                    # 更新关联sku的占用库存
                                    relate_sku.plan_use_inventory = F("plan_use_inventory") + act_need_inventory
                                    relate_sku.save(update_fields=["plan_use_inventory"])

                            # 原sku库存
                            origin_sku_plan_use_inventory = _sku.plan_use_inventory
                            new_plan_use_inventory = origin_sku_plan_use_inventory + physical_inventory
                            can_use_inventory = _sku.physical_inventory - new_plan_use_inventory

                            sku_update_rows = StockKeepingUnit.objects.filter(
                                sku_id=sku_id,
                                become_history=False,
                                can_use_inventory__gte=physical_inventory,
                            ).update(
                                plan_use_inventory=new_plan_use_inventory,
                                can_use_inventory=can_use_inventory,
                            )

                            if sku_update_rows == 0:
                                if i == try_times - 1:
                                    transaction.savepoint_rollback(ts_point)
                                    raise ValueError("加入货盘失败,请重试")

                                continue

                            # 创建item sku
                            ProductSelectionItemSKU.objects.create(
                                selection_plan=selection_plan,
                                sku=_sku,
                                item=item,
                                physical_inventory=physical_inventory,
                                remark=sku_data.get("remark", "") or "",
                                create_user=user_id,
                            )
                            break

                transaction.savepoint_commit(ts_point)
                # 写入操作明细日志
                w_plan_record(
                    plan_id=plan_id,
                    record_type=ProductSelectionPlanRecordType.ADD_PROD,
                    content="",
                    create_user=user_id,
                    product_id=product_id,
                    remark=kwargs.get("remark"),
                )

                # todo: 刷新库存
                if kwargs.get("correct_sku_inventory"):
                    correct_sku_inventory.delay(plan_id)

                return "SUCCESS"
        except IntegrityError as e:
            logger.error(str(e))
            raise ValueError(str(e))
    except ValueError as e:
        logger.warning(f"添加到货盘失败,{e}")
        return f"{e}"


@app.task(queue="common_tasks")
def async_move_old_plan_product_to_new_plan_invoker(
    old_plan_id: int,
    user_id: int,
    new_plan_id: int,
    create_user_type: str,
):
    item_qs = ProductSelectionItem.objects.filter(selection_plan_id=old_plan_id, is_deleted=False)
    for item in item_qs:
        sku_id_list = item.product.stockkeepingunit_set.filter(become_history=False).values_list("sku_id", flat=True)

        post_data = [{"sku_id": sku_id, "physical_inventory": 0} for sku_id in sku_id_list]

        # 异步任务调用
        product_id = item.product_id

        async_add_product_to_plan.delay(
            user_id,
            new_plan_id,
            product_id,
            post_data,
            create_user_type,
            item_remark=item.remark,
            is_copy_or_bulk_move=True,
        )


@app.task(queue="common_tasks")
def async_move_plan_item_to_new_plan_invoker(
    old_plan_id: int,
    old_plan_str: str,
    item_pk_list: list,
    new_plan_id: int,
    new_plan_str: str,
    user_id: int,
    create_user_type: str,
    remark: str,
):
    try:
        with transaction.atomic():
            items = ProductSelectionItem.objects.filter(pk__in=item_pk_list)

            for item in items:
                product = item.product

                log_content = f"{old_plan_str}货盘商品{product}移动到新货盘{new_plan_str}"

                # 更新sku占用库存
                item_skus = item.productselectionitemsku_set.filter(become_history=False)

                for item_sku in item_skus:
                    _sku = item_sku.sku
                    _sku.plan_use_inventory = _sku.plan_use_inventory - item_sku.physical_inventory
                    _sku.save(update_fields=["plan_use_inventory", "can_use_inventory"])

                product_id = item.product_id
                sku_id_list = product.stockkeepingunit_set.filter(become_history=False).values_list("sku_id", flat=True)
                post_data = [{"sku_id": sku_id, "physical_inventory": 0} for sku_id in sku_id_list]
                # 防止任务冲突，先改成同步
                move_ret = async_add_product_to_plan(
                    user_id,
                    new_plan_id,
                    product_id,
                    post_data,
                    create_user_type,
                    item_remark=remark,
                    is_copy_or_bulk_move=True,
                )

                if move_ret == "SUCCESS":
                    # 写入操作明细日志
                    w_plan_record(
                        plan_id=old_plan_id,
                        record_type=ProductSelectionPlanRecordType.DEL_PROD,
                        content=log_content,
                        create_user=user_id,
                        product_id=item.product_id,
                    )

                    # 物理删除数据
                    item.delete()

    except Exception as e:
        logger.error(f">>>移动商品到:{new_plan_id}异常:{e}")
    finally:
        # 移除锁
        release_lock(new_plan_id, user_id)


def bulk_query_category(category_ids):
    """
    批量获取分类数据
    """
    category_ids = list(set(category_ids))
    ProductCategoryList = apps.get_model("products", "ProductCategoryList")
    cate_qs = ProductCategoryList.objects.filter(id__in=category_ids).only("id", "name")
    cate_map = {str(i.id): i.name for i in cate_qs}
    return cate_map


@app.task(queue="common_tasks")
def update_product_historical(product_ids, save_again=True):
    """
    更新商品历史记录
    """
    Product = apps.get_model("products", "Product")
    ProductAttrOption = apps.get_model("products", "ProductAttrOption")

    labels_map = bulk_query_labels(product_ids)
    productattroption_query = Prefetch("productattroption_set", queryset=ProductAttrOption.objects.all().select_related("attr", "attr_value"))
    product_qs = Product.objects.filter(id__in=product_ids).select_related("company").prefetch_related(productattroption_query)
    category_ids = []
    for i in product_qs:
        if i.category:
            category_ids.extend(i.category)
    category_map = bulk_query_category(category_ids)
    for obj in product_qs:
        # 创建新的历史记录
        if save_again:
            obj.save()
        # 分类数据
        category_data = []
        for c in obj.category:
            category_data.append({"id": c, "name": category_map.get(c, "")})

        # attr_options 数据
        # attr_options = obj.productattroption_set.select_related("attr", "attr_value")
        attr_options = obj.productattroption_set.all()
        attr_options_data = [
            {
                "id": i.id,
                "name": i.attr.name,
                "attr_id": i.attr_id,
                "value": i.attr_value.name,
                "value_id": i.attr_value_id,
            }
            for i in attr_options
        ]

        # 更新历史记录的自定义字段
        latest_history = obj.history.first()
        if latest_history:
            # 再更新一下最新历史的相关字段，因为很多sku的post_save导致事务中的save不是最终形态
            fields_to_update = [field.name for field in obj._meta.fields if field.name != "id"]
            for field in fields_to_update:
                setattr(latest_history, field, getattr(obj, field))

            latest_history.category_data = category_data
            latest_history.company_data = {"company_id": obj.company.company_id, "company_name": obj.company.name}
            latest_history.attr_options_data = attr_options_data
            latest_history.labels_data = labels_map.get(obj.id, {})
            latest_history.save()


@app.task(queue="common_tasks")
def update_sku_historical(sku_ids, save_again=True):
    """
    更新SKU历史记录
    """
    specs_dict = bulk_query_skus_specs_detail_with_sku_pk_list(sku_ids)
    StockKeepingUnit = apps.get_model("products", "StockKeepingUnit")
    sku_qs = StockKeepingUnit.objects.filter(id__in=sku_ids)
    for obj in sku_qs:
        # 创建新的历史记录
        if save_again:
            obj.save()
        # 更新历史记录的自定义字段
        latest_history = obj.history.first()
        if latest_history:
            latest_history.specs_data = specs_dict.get(obj.id, [])
            latest_history.save()


@app.task(queue="common_tasks")
def edit_dy_product_spec_code(map_id: int):
    try:
        try:
            map_obj = TempProductMap.objects.get(pk=map_id, uploaded=False)
        except TempProductMap.DoesNotExist:
            logger.warning(f"映射: {map_id}不存在")
            return

        if not map_obj.shop_id:
            logger.warning(f"映射: {map_id}不存在店铺id")
            return

        data_shop = DataShop.objects.filter(shop_id=map_obj.shop_id).only("id").first()
        if not data_shop:
            logger.warning(f"映射: {map_id}店铺ID: {map_obj.shop_id}不存在")
            return
        response = update_dy_sku_code(data_shop.pk, map_obj.ex_sku_id, map_obj.spec_code)
        if response.code != 10000:
            map_obj.errors = json.dumps(response.__dict__, ensure_ascii=False)
            map_obj.save(update_fields=["errors"])
        else:
            map_obj.uploaded = True
            map_obj.save(update_fields=["uploaded"])
    except Exception as e:
        logger.error(f"更新抖店商编错误: {e}")


@app.task(queue="common_tasks")
def handle_temp_product_images(product_pk: int, user_id: int):
    """
    处理临时商品的外部图片
    :param product_pk:
    :param user_id:
    :return:
    """
    product = Product.objects.get(pk=product_pk)
    aliyun_certs = generate_sts_credential(
        settings.ALIYUN_OSS_ROLE_ARN_ADD,
        str(uuid.uuid4()),
    )

    main_images = []
    for image_url in product.main_images:
        if "zhulinks.oss-cn-guangzhou.aliyuncs.com" not in image_url:
            image_resp = httpx.get(image_url, timeout=5)

            content_type = image_resp.headers["content-type"]
            image_suffix = content_type.split("/")[-1]

            image_data = image_resp.content
            # 商品名称
            image_path = f"{int(time.time() * 1000)}_{user_id}.{image_suffix}"

            download_url = upload_file_with_bytes(
                aliyun_certs["access_key_id"],
                aliyun_certs["access_key_secret"],
                aliyun_certs["security_token"],
                data=image_data,
                objec_name=image_path,
                base_path="user-upload/prod/images/productMgmt/imgs/",
            )
            main_images.append(download_url)
        else:
            main_images.append(image_url)

    product.main_images = main_images
    product.skip_history_when_saving = True
    product.save(update_fields=["main_images"])

    # 处理sku信息
    skus = product.stockkeepingunit_set.filter(become_history=False)
    skus_images = []
    for sku in skus:
        sku_image_url = sku.image
        if "zhulinks.oss-cn-guangzhou.aliyuncs.com" not in sku_image_url:
            sku_image_resp = httpx.get(sku_image_url, timeout=5)

            sku_content_type = sku_image_resp.headers["content-type"]
            sku_image_suffix = sku_content_type.split("/")[-1]

            sku_image_data = sku_image_resp.content
            # 商品名称
            sku_image_path = f"{int(time.time() * 1000)}_{user_id}.{sku_image_suffix}"

            sku_download_url = upload_file_with_bytes(
                aliyun_certs["access_key_id"],
                aliyun_certs["access_key_secret"],
                aliyun_certs["security_token"],
                data=sku_image_data,
                objec_name=sku_image_path,
                base_path="user-upload/prod/images/productMgmt/imgs/",
            )
            sku.image = sku_download_url
            sku.skip_history_when_saving = True
            sku.save(update_fields=["image"])

            #
            skus_images.append(sku_download_url)
        else:
            skus_images.append(skus_images)

        # 上传图片至qdrant
        images = deepcopy(product.main_images)
        if skus_images:
            images.extend(skus_images)
        add_images_to_qdrant.delay(product_pk, list(set(images)))


@app.task(queue="celery")
def sync_product_to_jst_shop(
    jst_shop_id: int,
    post_data_list: list,
):
    """
    同步商品到聚水潭店铺商品资料
    :return:
    """
    try:
        post_data = {
            "items": [
                {
                    "i_id": post_data["jst_i_id"],
                    "shop_id": jst_shop_id,
                    "original_sku_id": post_data["jst_original_sku_id"],
                    "shop_properties_value": post_data["jst_shop_properties_value"],
                    "sku_sign": post_data["jst_sku_sign"],
                    "name": post_data["jst_name"],
                    "sku_id": post_data["jst_sku_id"],
                    "shop_i_id": post_data["jst_shop_i_id"],
                    "sku_code": post_data["jst_sku_code"],
                    "shop_sku_id": post_data["jst_shop_sku_id"],
                }
                for post_data in post_data_list
            ]
        }
        json_response = jst_client.upload_shop_product(post_data)

        bulk_create_shop_sku_objects = [
            JSTShopSKUMap(
                sku_id=["post_data"],
                sku_spec_code=post_data["spec_code"],
                jst_sku_id=post_data["jst_sku_id"],
                jst_i_id=post_data["jst_i_id"],
                jst_sku_code=post_data["jst_sku_code"],
                jst_shop_i_id=post_data["jst_shop_i_id"],
                jst_shop_sku_id=post_data["jst_shop_sku_id"],
                jst_original_sku_id=post_data["jst_original_sku_id"],
                jst_name=post_data["jst_name"],
                jst_shop_properties_value=post_data["jst_shop_properties_value"],
                jst_sku_sign=post_data.get("jst_sku_sign"),
                jst_shop_id=jst_shop_id,
                uploaded=True,
            )
            for post_data in post_data_list
        ]

        JSTShopSKUMap.objects.bulk_create(bulk_create_shop_sku_objects)
        return {"success": True, "response": str(json_response)}
    except Exception as e:
        logger.warning(f">>上传聚水潭店铺商品失败:{e}--{traceback.format_exc()}")
        fail_create_shop_sku_objects = [
            JSTShopSKUMap(
                sku_id=post_data["post_data"],
                sku_spec_code=post_data["spec_code"],
                jst_sku_id=post_data["jst_sku_id"],
                jst_i_id=post_data["jst_i_id"],
                jst_sku_code=post_data["jst_sku_code"],
                jst_shop_i_id=post_data["jst_shop_i_id"],
                jst_shop_sku_id=post_data["jst_shop_sku_id"],
                jst_original_sku_id=post_data["jst_original_sku_id"],
                jst_name=post_data["jst_name"],
                jst_shop_properties_value=post_data["jst_shop_properties_value"],
                jst_sku_sign=post_data.get("jst_sku_sign"),
                jst_shop_id=jst_shop_id,
                uploaded=False,
                error=str(e),
            )
            for post_data in post_data_list
        ]
        JSTShopSKUMap.objects.bulk_create(fail_create_shop_sku_objects)
        return {"success": False, "errors": e}


@app.task(queue="common_tasks")
def video_split_jpg(oss_url, product_id=None):
    if oss_url is None:
        # 为None 不修改
        return ""
    if oss_url == "":
        if product_id:
            try:
                p_obj = Product.objects.get(product_id=product_id)
                p_obj.slicing_num = 0
                p_obj.slicing_url = ""
                p_obj.video_3D = ""
                p_obj.save()
            except Product.DoesNotExist:
                logger.error("视频切分删除时商品不存在")
        return
    if not oss_url.endswith(".mp4"):
        video_id = uuid.uuid4().hex
        change_file_path = f"user-upload/prod/videos/{video_id}.mp4"
        retry_fc(
            function="video-convert",
            payload={
                "video_url": oss_url,
                "oss_path": change_file_path,
                "delete_original_oss_file": True,
                "original_oss_file_path": oss_url.replace("https://zhulinks.oss-cn-guangzhou.aliyuncs.com/", ""),
            },
            is_async=True,
        )
        oss_url = f"https://zhulinks.oss-cn-guangzhou.aliyuncs.com/user-upload/prod/videos/{video_id}.mp4"
        redis_conn.lpush("videos_change_mp4", json.dumps({"oss_url": oss_url, "product_id": product_id, "num": 0}))
        return {"slicing_num": 0, "slicing_url": oss_url}

    split_video_id = uuid.uuid4().hex
    fc_response = retry_fc(
        function="video-frame-extract",
        payload={
            "video_url": oss_url,
            "oss_path": f"user-upload/prod/videos/{split_video_id}",
            "delta_degrees": 2,  # 默认视频是旋转一周拍摄的，配置每 2 度提取一帧
        },
    )
    result = json.loads(fc_response.body)
    slicing_data = {
        "slicing_num": result["frames_count"],
        "slicing_url": f"https://zhulinks.oss-cn-guangzhou.aliyuncs.com/user-upload/prod/videos/{split_video_id}",
    }
    if product_id:
        try:
            p_obj = Product.objects.get(product_id=product_id)
            p_obj.slicing_num = slicing_data["slicing_num"]
            p_obj.slicing_url = slicing_data["slicing_url"]
            p_obj.video_3D = oss_url
            p_obj.save()
        except Product.DoesNotExist:
            logger.error("视频切分时商品不存在")
    else:
        redis_cache.set(oss_url, slicing_data, 60 * 60 * 24)
    return slicing_data


@app.task(queue="celery")
def check_object_exists():
    video_info = redis_conn.lpop("videos_change_mp4")
    if not video_info:
        return
    video_info = json.loads(video_info)
    if video_info.get("num"):
        video_info["num"] += 1
    else:
        video_info["num"] = 1
    if video_info["num"] > 40:
        return
    aliyun_certs = generate_sts_credential(
        settings.ALIYUN_OSS_ROLE_ARN_ADD,
        str(uuid.uuid4()),
    )
    is_exists = object_is_exists(
        aliyun_certs["access_key_id"],
        aliyun_certs["access_key_secret"],
        aliyun_certs["security_token"],
        video_info["oss_url"].replace("https://zhulinks.oss-cn-guangzhou.aliyuncs.com/", ""),
    )
    if is_exists:
        logger.info(f"{video_info['oss_url']} 视频转换oss已存在, 进行切片")
        video_split_jpg.delay(video_info["oss_url"], video_info["product_id"])
    else:
        logger.info(f"{video_info['oss_url']} 视频转换oss 暂不存在, 稍后重试")
        redis_conn.lpush("videos_change_mp4", json.dumps(video_info))


@app.task(queue="celery")
def insert_cost_price_change_table(data):
    try:
        feishu_client = FeiShuDocx()
        if data["create_date"]:
            data["create_date"] = data["create_date"].replace("T", " ").split(".")[0]
        if data["review_date"]:
            data["review_date"] = data["review_date"].replace("T", " ").split(".")[0]
        gen_data = feishu_client.modify_cost_price(data)
        resp, is_success = feishu_client.table_record_insert_one(gen_data)
        logger.info(f"插入飞书推广价修改多维表格响应:{resp}")
        res = feishu_client.send_modify_cost_price_card(data)
        logger.info(f"发送推广价修改卡片消息响应:{res.text}")
    except Exception as e:
        logger.error("插入飞书推广价修改多维表格失败", str(e))


@app.task(queue="celery")
def insert_qa_product_review_table(data):
    try:
        feishu_client = FeiShuDocx()
        if data.get("create_date"):
            data["create_date"] = data["create_date"].replace("T", " ")[:16]
        gen_data = feishu_client.qa_review_product(data)
        resp, is_success = feishu_client.table_record_insert_one(gen_data)
        logger.info(f"插入飞书质检审核多维表格响应:{resp}")
        if data["is_pass"] and data["is_pass"] == "通过":
            res = feishu_client.send_qa_product_review_card(data)
            logger.info(f"发送质检审核卡片消息响应:{res.text}")
    except Exception as e:
        logger.error("插入飞书推广价修改多维表格失败", str(e))


@app.task(queue="celery")
def insert_zl_self_support_new_product_to_feishu_table(product_id: int):
    """
    珠凌臻品的多维表格任务
    :param product_id:
    :return:
    """
    tables_config = get_feishu_table_config()

    if "extra" not in tables_config:
        return {"success": False, "message": "配置不存在"}

    if "zlzp_new_product" not in tables_config["extra"]:
        return {"success": False, "message": "珠凌配置不存在"}

    table_config = tables_config["extra"]["zlzp_new_product"]

    try:
        product = Product.objects.get(product_id=product_id)
    except Product.DoesNotExist:
        return {"success": False, "message": f"商品{product_id}不存在"}

    sub_product = product.subproduct_set.first()
    if not sub_product:
        return {"success": False, "message": f"商品{product_id}不存在副本商品"}
    is_new_product = True
    total = 1
    processes = product.productreviewprocess_set.all()
    for process in processes:
        reviews = process.productreview_set.filter(
            physical_inventory_exact=True,
            quality_qualified=True,
            price_reasonable=True,
        ).values_list("process_level", flat=True)

        if {"BASIC_REVIEW", "PRICE_REVIEW", "QA_REVIEW"}.issubset(set(reviews)):
            if total > 1:
                is_new_product = False
                break
            total += 1

    if not is_new_product:
        return {"success": True, "message": "不是新品"}
    feishu_cli = FeiShuDocx()

    skus = product.stockkeepingunit_set.filter(become_history=False)
    letters = sub_product.owner.letters
    for sku in skus:
        if not sku.retail_price:
            return {"success": False, "message": f"{sku.sku_id}没有售价"}

        image_url = product.main_images[0]

        table_token = table_config["table_token"]
        table_id = table_config["table_id"]
        resp = feishu_cli.upload_image(image_url, table_token)
        print(resp)
        if not resp:
            return {"success": False, "message": f"商品{product_id}上传图片失败"}
        post_data = {
            "珠凌商品ID": str(product_id),
            "商品主图": [{"file_token": resp["file_token"]}],
            "珠凌新商品名称": sub_product.name,
            "珠凌货号": sub_product.code,
            "规格名": ",".join([spec["value"] for spec in sku.specs]) or "默认规格",
            "商品编码": f"{sku.spec_code}{letters}",
            "现货库存": str(sku.physical_inventory),
            "实际销量": str(sku.sales),
            "成本价": str(sku.cost_price),
            "售价": str(sku.retail_price) or "",
            "固定成本费用": str(sku.retail_price - sku.cost_price),
            # "财务核实售价备注": "",
            # "仓库原商品名称": "",
            # "仓库原款编": "",
            # "仓库原商品编码": "",
        }
        resp, success = feishu_cli.insert_record_to_table(table_token, table_id, post_data)

        if success:
            return {"success": success, "message": f"商品{product_id}推送到多维表格成功"}
        return {"success": success, "resp": f"{resp}"}


@app.task(queue="celery")
def insert_zl_new_product_to_feishu_table(product_id: int):
    """
    珠凌臻品的多维表格任务
    :param product_id:
    :return:
    """
    tables_config = get_feishu_table_config()

    if "extra" not in tables_config:
        return {"success": False, "message": "配置不存在"}

    if "normal_new_product" not in tables_config["extra"]:
        return {"success": False, "message": "珠凌配置不存在"}

    table_config = tables_config["extra"]["normal_new_product"]

    try:
        product = Product.objects.get(product_id=product_id)
    except Product.DoesNotExist:
        return {"success": False, "message": f"商品{product_id}不存在"}

    is_new_product = True
    total = 1
    processes = product.productreviewprocess_set.all()
    for process in processes:
        reviews = process.productreview_set.filter(
            physical_inventory_exact=True,
            quality_qualified=True,
            price_reasonable=True,
        ).values_list("process_level", flat=True)

        if {"BASIC_REVIEW", "PRICE_REVIEW", "QA_REVIEW"}.issubset(set(reviews)):
            if total > 1:
                is_new_product = False
                break

    if not is_new_product:
        return {"success": True, "message": "不是新品"}

    feishu_cli = FeiShuDocx()
    image_url = product.main_images[0]

    table_token = table_config["table_token"]
    table_id = table_config["table_id"]
    resp = feishu_cli.upload_image(image_url, table_token)
    if not resp:
        return {"success": False, "message": f"商品{product_id}上传图片失败"}
    post_data = {
        "珠凌商品ID": str(product_id),
        "商品主图": [{"file_token": resp["file_token"]}],
        "珠凌商品名称": product.name,
        "珠凌货号": product.code,
        "主图相似度": str(float(product.pic_score) * 100) + "%",
    }
    resp, success = feishu_cli.insert_record_to_table(table_token, table_id, post_data)
    if success:
        return {"success": success, "message": f"商品{product_id}推送到多维表格成功"}
    return {"success": success, "resp": f"{resp}"}


@app.task(queue="celery")
def send_pic_score_greate_than_70_notify(product_id):
    """
    发送商品提报的通知
    :param product_id: 商品id
    :return:
    """

    try:
        product = Product.objects.get(product_id=product_id)
    except Product.DoesNotExist:
        return {"success": False, "message": f"商品{product_id}不存在"}

    is_new_product = True
    total = 1
    processes = product.productreviewprocess_set.all()
    for process in processes:
        reviews = process.productreview_set.filter(
            physical_inventory_exact=True,
            quality_qualified=True,
            price_reasonable=True,
        ).values_list("process_level", flat=True)

        if {"BASIC_REVIEW", "PRICE_REVIEW", "QA_REVIEW"}.issubset(set(reviews)):
            if total > 1:
                is_new_product = False
                break

    if not is_new_product:
        return {"success": True, "message": "不是新品"}

    is_new_product = True
    total = 1
    processes = product.productreviewprocess_set.all()
    for process in processes:
        reviews = process.productreview_set.filter(
            physical_inventory_exact=True,
            quality_qualified=True,
            price_reasonable=True,
        ).values_list("process_level", flat=True)

        if {"BASIC_REVIEW", "PRICE_REVIEW", "QA_REVIEW"}.issubset(set(reviews)):
            if total > 1:
                is_new_product = False
                break

    if not is_new_product:
        return {"success": True, "message": "不是新品"}

    feishu_client = FeiShuDocx()
    cfg = get_feishu_robots_config()

    extra = cfg["extra"] or {}

    pic_score = f"{float(product.pic_score or 0) * 100}%"

    if "pic_score" in extra:
        request_url = extra["pic_score"]
    else:
        request_url = "https://open.feishu.cn/open-apis/bot/v2/hook/ed7d4e11-f13b-429d-97ea-e79edeeccf43"

    response = feishu_client.send_main_image_score_greater_than_70(
        request_url,
        product_id,
        product.code,
        product.name,
        product.company.name,
        pic_score,
        product.main_images[0],
        get_user_real_name_by_user_id(product.create_user) or "-",
    )
    logger.info(f">>>>供应商飞书发送主图通知，返回结果:{response.status_code}-{response.text}")


@app.task(queue="common_tasks")
def async_selection_plan_download_task(task_pk: int) -> tuple:
    from products.models_v2.product_config import ProductConfig

    start_time = time.time()
    try:
        task = DownloadTasks.objects.get(pk=task_pk)
    except DownloadTasks.DoesNotExist:
        return False, f"任务:{task_pk}不存在"

    if task.status in ["progressing", "completed", "expired"]:
        return False, f"任务:{task_pk}{task.get_status_display()}"

    # 任务队列
    create_user_id = task.create_user
    max_tasks_count = get_download_max_tasks_count_config()
    current_tasks_count = DownloadTasks.objects.filter(create_user=create_user_id, status="progressing").count()
    if current_tasks_count >= max_tasks_count:
        if task.status == "failed":
            task.status = "pending"
            task.save()
        return False, f"当前用户进行中数量: {max_tasks_count},等待中"

    # 记录celery的任务
    task.celery_id = async_selection_plan_download_task.request.id
    task.start_time = timezone.make_aware(datetime.now())

    try:
        live_author = LiveAuthor.objects.get(distributor__letters="V")
    except LiveAuthor.DoesNotExist:
        task.status = "failed"
        task.error_reason = "请联系管理员增加云上珠宝主播"
        task.save()
        return False, "请联系管理员增加云上珠宝主播"

    params = task.post_data or {}
    fields = params.pop("fields", "")

    download_platform = task.download_platform
    # 是否为分销模式分销商
    is_distributor_mode = False
    if download_platform == "DB" and task.distributor.distributor_mode == 2:
        is_distributor_mode = True

    if fields == "all":
        if download_platform == "OP":
            tmpl_data = OperatorSelectionPlanDownloadAllProductTmpl
        elif is_distributor_mode:
            tmpl_data = DBMode2SelectionPlanDownloadAllProductTmpl
        else:
            tmpl_data = DBOperatorSelectionPlanDownloadAllProductTmpl
    else:
        if download_platform == "OP":
            tmpl_data = OperatorSelectionPlanDownloadProductTmpl
        elif is_distributor_mode:
            tmpl_data = DBMode2SelectionPlanDownloadAllProductTmpl
        else:
            tmpl_data = DBOperatorSelectionPlanDownloadProductTmpl

    if fields in ["spu", "sku"]:
        # 去除供应商id
        tmpl_data.pop("company_id", None)

    # 序列化文件
    serializer_class = OperateSelectionPlanProductListDownloadSerializer if download_platform == "OP" else DBOperateSelectionPlanProductListDownloadSerializer

    try:
        plan = ProductSelectionPlan.objects.get(plan_id=task.source_id)
    except ProductSelectionPlan.DoesNotExist:
        msg = f"货盘:{task.source_id}不存在"
        task.status = "failed"
        task.error_reason = msg
        task.save()
        return False, msg

    try:
        current_user = User.objects.get(user_id=task.create_user, query_all=True)
    except User.DoesNotExist:
        msg = f"用户:{task.create_user}不存在"
        task.status = "failed"
        task.error_reason = msg
        task.save()
        return False, msg

    # 分销商端需要赋值
    # 可能只有底部的关联链接
    if download_platform == "DB":
        task_distributor = task.distributor
        if current_user.distributor != task_distributor:
            setattr(current_user, "distributor", task_distributor)
            # 查询relation
            relation = UserDistributorRelationShip.objects.filter(user=current_user, distributor=task_distributor).first()
            setattr(current_user, "is_distributor_manager", relation.is_distributor_manager)

    # 开始跑任务,更新状态
    task.status = "progressing"
    task.save()

    try:
        display_mode = params.pop("display_mode", None)
        filename = task.filename
        # 特殊处理，查询日志
        _is_special = params.pop("_is_special", ["0"])[0]
        page_desc = "货盘-商品列表下载" if display_mode != "map" else "货盘-排品列表下载"

        # 自定义request
        origin_request = HttpRequest()

        def builtin_absolute_url():
            return task.absolute_url

        setattr(origin_request, "build_absolute_uri", builtin_absolute_url)
        request = Request(origin_request)
        request.user = current_user
        request.auth = {"user_type": download_platform}

        send_selection_plan_listener_task(request, page_desc, plan)

        # Prepare query conditions
        item_query_condition = {"selection_plan_id": task.source_id, "is_deleted": False}
        create_user_type = params.pop("create_user_type", None)
        if create_user_type:
            item_query_condition["create_user_type"] = create_user_type

        item_qs = ProductSelectionItem.objects.filter(**item_query_condition).values(
            "id",
            "product_id",
            "sub_product_id",
            "physical_inventory",
            "remark",
            "has_live",
            "product_confirm_state",
            "mark",
        )

        if download_platform == "DB":
            cannot_see_product_id_list = (
                ProductConfig.objects.exclude(
                    visible_distributor=[],
                )
                .exclude(visible_distributor__contains=task.distributor_id)
                .values_list("product_id", flat=True)
            )
            item_qs = item_qs.exclude(product_id__in=cannot_see_product_id_list)

        # 地图排品
        if display_mode == "map":
            item_qs = item_qs.order_by("map_order")
        else:
            item_qs = item_qs.order_by("plan_category__plan_company__order", "plan_category__order")

        # 货盘标记id列表
        unique_mark_id_set = set()
        item_id_set = set()
        data_dict = {}
        for item in item_qs:
            item_id_set.add(item["id"])

            mark_id_list = item.get("mark")
            data_dict[item["product_id"]] = {
                "remark": item.get("remark"),
                "has_live": item.get("has_live"),
                "physical_inventory": item.get("physical_inventory"),
                "product_confirm_state": item.get("product_confirm_state"),
                "mark": mark_id_list,
            }

            # 标记id
            for mark_id in mark_id_list:
                unique_mark_id_set.add(mark_id)

        product_ids = data_dict.keys()

        # 额外的filter
        filters = dict(is_deleted=False, product_id__in=product_ids)

        # 按照id列表指定的顺序
        custom_product_id_sort = Case(*[When(product_id=product_id, then=idx) for idx, product_id in enumerate(product_ids)])

        # 获取商品
        page_products, re_data, plan_products_qs = custom_filter(
            params,
            Product,
            array_fields=["category"],
            like_fields=["name", "product_id", "code"],
            hybrid_fields=[
                "name",
                "product_id",
                "code__icontains",
                "stockkeepingunit__link_code",
                "stockkeepingunit__spec_code",
            ],
            force_orders=False,
            **filters,
        )
        re_data = []
        num = 0

        # 指定顺序再排序
        plan_products_qs = plan_products_qs.prefetch_related("company").order_by(custom_product_id_sort)

        # -------- product attr options批量查询 -------- #
        need_options_fields = ["款式", "镶嵌", "产地"]
        product_primary_key_list = [product.id for product in plan_products_qs]
        attr_options_qs = ProductAttrOption.objects.filter(
            product_id__in=product_primary_key_list,
            attr__name__in=need_options_fields,
        ).values("product_id", "attr__name", "attr_value__name")
        attr_options_qs_map = {}
        for attr_option in attr_options_qs:
            product_id = attr_option.get("product_id")
            attr_name = attr_option.get("attr__name")
            attr_value_name = attr_option.get("attr_value__name")

            if product_id not in attr_options_qs_map:
                attr_options_qs_map[product_id] = {attr_name: attr_value_name}
            else:
                attr_options_qs_map[product_id][attr_name] = attr_value_name

        # -------- 7天播过 -------- #
        last_7_day = datetime.now() - timedelta(days=7)
        # 查询货盘是否已播
        last_7_day_live_query = (
            ProductSelectionItem.objects.filter(
                product_id__in=product_ids,
                has_live=True,
                selection_plan__live_date_start__gte=last_7_day,
            )
            .values("product_id")
            .annotate(count=Count("id"))
            .filter(count__gt=0)
            .values("product_id", "count")
        )
        live_7_days_ret = {live_ret["product_id"]: live_ret["count"] for live_ret in last_7_day_live_query}
        # -------- category批量查询 -------- #
        category_id_list = []
        for product in plan_products_qs:
            for i in product.category:
                if i not in category_id_list:
                    category_id_list.append(i)

        category_objs = ProductCategoryList.objects.filter(id__in=category_id_list).only("id", "name")
        category_map = {category_obj.id: {"id": category_obj.id, "name": category_obj.name} for category_obj in category_objs}

        # -------- sku批量查询 -------- #
        skus_qs = StockKeepingUnit.objects.filter(product_id__in=product_primary_key_list, become_history=False)
        skus_map = {}
        for sku in skus_qs:
            sku_prod_id = sku.product_id

            if sku_prod_id not in skus_map:
                skus_map[sku_prod_id] = [sku]
                continue
            skus_map[sku_prod_id].append(sku)

        # -------- 历史价格批量查询 -------- #
        if fields == "spu":
            latest_history_info = bulk_query_latest_history_price_with_auth_info_by_product_pk_list(product_primary_key_list)
            v_history_price_map = {}
            need_v_history_price = False
            # 分销商云上珠宝历史价格查询
            if download_platform == "DB":
                if plan.distributor.live_author != live_author:
                    v_history_price_map = bulk_query_latest_history_price_with_auth_info_by_product_pk_list(product_primary_key_list, live_author.author_id)
                    need_v_history_price = True
        else:
            latest_history_info = bulk_query_latest_history_price_with_auth_info(skus_qs)
            v_history_price_map = {}
            need_v_history_price = False
            # 分销商云上珠宝历史价格查询
            if download_platform == "DB":
                if plan.distributor.live_author != live_author:
                    v_history_price_map = bulk_query_latest_history_price_with_auth_info(skus_qs, live_author.author_id)
                    need_v_history_price = True

        # 批量查询sub_product
        sub_product_ids = [i["sub_product_id"] for i in item_qs if i["sub_product_id"]]
        if is_distributor_mode:
            # 移除分销市场、删除的也显示
            sub_products = SubProduct.origin_objects.filter(product_id__in=sub_product_ids, owner__letters="FX")
        else:
            sub_products = SubProduct.objects.filter(product_id__in=sub_product_ids, owner_id=plan.distributor_id, is_deleted=False)

        sub_products_map = {sub_product.parent_product_id: sub_product for sub_product in sub_products}

        product_id_list = [p.product_id for p in plan_products_qs]

        log_object_data = {}
        # 查询历史库存数据
        if _is_special == "1":
            log_object_data = bulk_query_confirm_state_log(int(task.source_id), product_id_list, plan.live_date_end)

        # 批量查询规格值
        skus_specs_dict = bulk_query_skus_specs_detail(skus_qs)

        # 批量查询货盘标记
        label_marks = SelectionPlanLabelMark.objects.filter(pk__in=unique_mark_id_set).only("id", "name")
        label_marks_map = {str(mark.id): mark.name for mark in label_marks}

        if fields in ["all", ""]:
            item_skus = ProductSelectionItemSKU.objects.filter(item_id__in=item_id_set).only("sku_id", "estimated_sales")
            item_skus_estimated_sales_map = {item_sku.sku_id: item_sku.estimated_sales for item_sku in item_skus}

            for product in plan_products_qs:
                re_product = {}
                product_data = serializer_class(instance=product, context={"request": request, "sub_products_map": sub_products_map}).data
                # template data
                for k, v in tmpl_data.items():
                    if k == "mark":
                        item_mark_list = data_dict.get(product.product_id)["mark"]
                        re_product[v] = "、".join([label_marks_map.get(str(i)) for i in item_mark_list])
                    else:
                        value = product_data.get(k)
                        re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value
                # 款式
                product_attr_map = attr_options_qs_map.get(product.id, {})
                for field in need_options_fields:
                    re_product[field] = product_attr_map.get(field, "")

                re_product["商品备注"] = product.remark
                # 分销模式不显示字段
                if not is_distributor_mode:
                    # 近7日是否播过
                    live_past_7_days = bool(live_7_days_ret.get(product.product_id))
                    re_product["是否已播"] = ""
                    if data_dict.get(product.product_id):
                        re_product["是否已播"] = "是" if data_dict.get(product.product_id)["has_live"] else "否"

                    re_product["近7日是否播过"] = "是" if live_past_7_days else "否"

                category_id_and_name_list = [category_map.get(int(i), "") for i in product_data.get("category")]
                name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目"}
                for i in range(len(category_id_and_name_list)):
                    # 全量导出才有分类id值
                    re_product[f"{name_key[i]}ID"] = category_id_and_name_list[i].get("id")
                    re_product[f"{name_key[i]}名称"] = category_id_and_name_list[i].get("name")
                    if i == 3:
                        break

                # sku
                skus_qs = skus_map.get(product.id)
                for sku in skus_qs:
                    num += 1
                    re_sku = {"序号": num}
                    re_sku.update(re_product)
                    re_sku["规格ID"] = sku.sku_id
                    # spec
                    spec_option_list = [f"{i.get('name')},{i.get('value')}" for i in skus_specs_dict.get(sku.pk, []) or []] or ["默认规格"]
                    re_sku["规格"] = ";".join(spec_option_list)

                    if not is_distributor_mode:
                        re_sku["建议售价"] = sku.retail_price

                        # 链接编码
                        re_sku["链接编码"] = sku.link_code
                        # 销量
                        re_sku["销量"] = sku.sales
                        # 需要权限
                        if current_user.has_perm("products.view_cost_price_StockKeepingUnit"):
                            re_sku["推广价"] = sku.cost_price
                        else:
                            re_sku["推广价"] = "***"

                        # todo: 结束货盘可以用历史现货库存
                        re_sku["现货库存"] = sku.physical_inventory
                        re_sku["7天补货库存"] = sku.safety_inventory

                        if _is_special == "1":
                            log_object = log_object_data.get(product.product_id)
                            if not log_object:
                                re_sku["现货库存"] = "-"
                                re_sku["7天补货库存"] = "-"
                            else:
                                sku_log_object = log_object.get(str(sku.sku_id))
                                if not sku_log_object:
                                    _spec_text = ";".join(spec_option_list) or "默认规格"
                                    sku_log_object = log_object.get(_spec_text)
                                    if not sku_log_object:
                                        sku_log_object = {}

                                if not sku_log_object:
                                    re_sku["现货库存"] = "-"
                                    re_sku["7天补货库存"] = "-"
                                else:
                                    re_sku["现货库存"] = sku_log_object.get("physical_inventory")
                                    re_sku["7天补货库存"] = sku_log_object.get("safety_inventory")
                            re_sku["是否确认库存"] = "未确认"
                            if data_dict.get(product.product_id):
                                if data_dict.get(product.product_id)["product_confirm_state"] == 1:
                                    re_sku["是否确认库存"] = "已确认"

                        # 货盘库存
                        re_sku["预估销量"] = item_skus_estimated_sales_map.get(sku.sku_id, "") or ""

                        # 分销商历史价
                        history_price = None
                        history_price_author_id = None
                        history_price_author_name = None
                        latest_sku_obj = latest_history_info.get(sku.id)
                        if latest_sku_obj:
                            history_price = latest_sku_obj.get("history_price", "")
                            history_price_author_id = latest_sku_obj.get("author_id", "")
                            history_price_author_name = latest_sku_obj.get("author_name", "")

                        re_sku["历史价"] = history_price
                        re_sku["历史价主播id"] = history_price_author_id
                        re_sku["历史价主播名"] = history_price_author_name
                        # 云上珠宝历史价
                        if need_v_history_price:
                            v_history_price = None
                            v_latest = v_history_price_map.get(sku.id)
                            if v_latest:
                                v_history_price = v_latest.get("history_price", "")
                            re_sku["云上珠宝历史价"] = v_history_price
                    else:
                        re_sku["现货库存"] = sku.physical_inventory
                        re_sku["采购价格"] = sku.distributor_market_price
                    # 货盘备注
                    re_sku["货盘备注"] = ""
                    if data_dict.get(product.product_id):
                        re_sku["货盘备注"] = data_dict.get(product.product_id)["remark"]

                    re_data.append(re_sku)
        elif fields == "spu":
            num = 0
            can_see_min_cost_price = current_user.has_perm("products.view_min_cost_price_Product")
            can_see_max_cost_price = current_user.has_perm("products.view_max_cost_price_Product")
            for product in plan_products_qs:
                num += 1
                re_product = {"序号": num}
                product_data = serializer_class(instance=product, context={"request": request, "sub_products_map": sub_products_map}).data
                # template data
                for k, v in tmpl_data.items():
                    if k == "mark":
                        item_mark_list = data_dict.get(product.product_id)["mark"]
                        re_product[v] = "、".join([label_marks_map.get(str(i)) for i in item_mark_list])
                    else:
                        value = product_data.get(k)
                        re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value
                # 款式
                product_attr_map = attr_options_qs_map.get(product.id, {})
                for field in need_options_fields:
                    re_product[field] = product_attr_map.get(field, "")

                # 近7日是否播过
                live_past_7_days = bool(live_7_days_ret.get(product.product_id))

                re_product["商品备注"] = product.remark

                if not is_distributor_mode:
                    re_product["是否已播"] = ""
                    if data_dict.get(product.product_id):
                        re_product["是否已播"] = "是" if data_dict.get(product.product_id)["has_live"] else "否"
                    re_product["近7日是否播过"] = "是" if live_past_7_days else "否"

                    category_id_and_name_list = [category_map.get(int(i), "") for i in product_data.get("category")]
                    name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目"}
                    for i in range(len(category_id_and_name_list)):
                        # 全量导出才有分类id值
                        re_product[f"{name_key[i]}名称"] = category_id_and_name_list[i].get("name")
                        if i == 3:
                            break

                    # 建议售价
                    if product.min_retail_price is None and product.max_retail_price is None:
                        retail_price = ""
                    elif product.min_retail_price is None and product.max_retail_price is not None:
                        retail_price = product.max_retail_price
                    elif product.min_retail_price is not None and product.max_retail_price is None:
                        retail_price = product.min_retail_price
                    elif product.min_retail_price == product.max_retail_price:
                        retail_price = product.min_retail_price
                    else:
                        retail_price = f"{product.min_retail_price}-{product.max_retail_price}"

                    re_product["建议售价"] = retail_price
                    # 销量
                    re_product["销量"] = product.sales

                    # 推广价
                    if can_see_min_cost_price:
                        min_cost_price = product.min_cost_price
                    else:
                        min_cost_price = "***"

                    if can_see_max_cost_price:
                        max_cost_price = product.max_cost_price
                    else:
                        max_cost_price = "***"

                    if min_cost_price is None and max_cost_price is None:
                        cost_price = ""
                    elif min_cost_price is None and max_cost_price is not None:
                        cost_price = max_cost_price
                    elif min_cost_price is not None and max_cost_price is None:
                        cost_price = min_cost_price
                    elif min_cost_price == min_cost_price:
                        cost_price = min_cost_price
                    else:
                        cost_price = f"{min_cost_price}-{max_cost_price}"
                    re_product["推广价"] = cost_price

                    re_product["现货库存"] = product.physical_inventory or ""
                    re_product["7天补货库存"] = product.safety_inventory or ""

                    # 货盘库存
                    re_product["预估销量"] = ""
                    if data_dict.get(product.product_id):
                        re_product["预估销量"] = data_dict.get(product.product_id)["physical_inventory"]

                    # 分销商历史价
                    history_price = None
                    history_price_author_name = None
                    latest_sku_obj = latest_history_info.get(product.id)
                    if latest_sku_obj:
                        history_price = latest_sku_obj.get("history_price", "")
                        history_price_author_name = latest_sku_obj.get("author_name", "")

                    re_product["历史价"] = history_price
                    re_product["历史价主播名"] = history_price_author_name
                    # 云上珠宝历史价
                    if need_v_history_price:
                        v_history_price = None
                        v_latest = v_history_price_map.get(product.id)
                        if v_latest:
                            v_history_price = v_latest.get("history_price", "")
                        re_product["云上珠宝历史价"] = v_history_price
                else:
                    re_product["现货库存"] = product.physical_inventory or ""
                    # 采购价格
                    if product.min_distributor_market_price is None and product.max_distributor_market_price is None:
                        market_price = ""
                    elif product.min_distributor_market_price is None and product.max_distributor_market_price is not None:
                        market_price = product.max_distributor_market_price
                    elif product.min_distributor_market_price is not None and product.max_distributor_market_price is None:
                        market_price = product.min_distributor_market_price
                    elif product.min_distributor_market_price == product.max_distributor_market_price:
                        market_price = product.min_distributor_market_price
                    else:
                        market_price = f"{product.min_distributor_market_price}-{product.max_distributor_market_price}"

                    re_product["采购价格"] = market_price
                # 货盘备注
                re_product["货盘备注"] = ""
                if data_dict.get(product.product_id):
                    re_product["货盘备注"] = data_dict.get(product.product_id)["remark"]

                if not is_distributor_mode:
                    # 成本值计算
                    skus_qs = skus_map.get(product.id)
                    physical_price = 0
                    total_physical_price = 0
                    for sku in skus_qs:
                        physical_price += (sku.physical_inventory or 0) * sku.cost_price
                        total_physical_price += ((sku.physical_inventory or 0) + (sku.safety_inventory or 0)) * sku.cost_price

                    re_product["现货库存成本值"] = physical_price
                    re_product["现货+7天库存成本值"] = total_physical_price

                    # 价格范围
                    re_product["价格范围"] = ""
                    price_range_conditions = {
                        "0-50": (0, 50),
                        "50-100": (50, 100),
                        "100-300": (100, 300),
                        "300-500": (300, 500),
                        "500-1000": (500, 1000),
                        "1000-3000": (1000, 3000),
                        "3000以上": (3000, None),
                    }
                    if min_cost_price not in [None, "***"]:
                        for price_range, (low, high) in price_range_conditions.items():
                            if not high:
                                if min_cost_price >= float(low):
                                    re_product["价格范围"] = price_range
                            else:
                                if float(low) <= min_cost_price < float(high):
                                    re_product["价格范围"] = price_range

                re_data.append(re_product)
        else:
            item_skus = ProductSelectionItemSKU.objects.filter(item_id__in=item_id_set).only("sku_id", "estimated_sales")
            item_skus_estimated_sales_map = {item_sku.sku_id: item_sku.estimated_sales for item_sku in item_skus}

            # sku维度导出
            can_see_cost_price = current_user.has_perm("products.view_cost_price_StockKeepingUnit")
            for product in plan_products_qs:
                re_product = {}
                product_data = serializer_class(instance=product, context={"request": request, "sub_products_map": sub_products_map}).data
                # template data
                for k, v in tmpl_data.items():
                    if k == "mark":
                        item_mark_list = data_dict.get(product.product_id)["mark"]
                        re_product[v] = "、".join([label_marks_map.get(str(i)) for i in item_mark_list])
                    else:
                        value = product_data.get(k)
                        re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value
                # 款式
                product_attr_map = attr_options_qs_map.get(product.id, {})
                for field in need_options_fields:
                    re_product[field] = product_attr_map.get(field, "")

                if not is_distributor_mode:
                    # 近7日是否播过
                    live_past_7_days = bool(live_7_days_ret.get(product.product_id))

                    re_product["商品备注"] = product.remark
                    re_product["是否已播"] = ""
                    if data_dict.get(product.product_id):
                        re_product["是否已播"] = "是" if data_dict.get(product.product_id)["has_live"] else "否"
                    re_product["近7日是否播过"] = "是" if live_past_7_days else "否"

                category_id_and_name_list = [category_map.get(int(i), "") for i in product_data.get("category")]
                name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目"}
                for i in range(len(category_id_and_name_list)):
                    # 全量导出才有分类id值
                    re_product[f"{name_key[i]}名称"] = category_id_and_name_list[i].get("name")
                    if i == 3:
                        break

                # sku
                skus_qs = skus_map.get(product.id)
                for sku in skus_qs:
                    num += 1
                    re_sku = {"序号": num}
                    re_sku.update(re_product)
                    # spec
                    spec_option_list = [f"{i.get('name')},{i.get('value')}" for i in skus_specs_dict.get(sku.pk, []) or []] or ["默认规格"]
                    re_sku["规格"] = ";".join(spec_option_list)

                    if not is_distributor_mode:
                        re_sku["建议售价"] = sku.retail_price
                        # 销量
                        re_sku["销量"] = sku.sales
                        # 需要权限
                        if can_see_cost_price:
                            re_sku["推广价"] = sku.cost_price
                        else:
                            re_sku["推广价"] = "***"

                        # todo: 结束货盘可以用历史现货库存
                        re_sku["现货库存"] = sku.physical_inventory
                        re_sku["7天补货库存"] = sku.safety_inventory

                        if _is_special == "1":
                            log_object = log_object_data.get(product.product_id)
                            if not log_object:
                                re_sku["现货库存"] = "-"
                                re_sku["7天补货库存"] = "-"
                            else:
                                sku_log_object = log_object.get(str(sku.sku_id))
                                if not sku_log_object:
                                    _spec_text = ";".join(spec_option_list) or "默认规格"
                                    sku_log_object = log_object.get(_spec_text)
                                    if not sku_log_object:
                                        sku_log_object = {}

                                if not sku_log_object:
                                    re_sku["现货库存"] = "-"
                                    re_sku["7天补货库存"] = "-"
                                else:
                                    re_sku["现货库存"] = sku_log_object.get("physical_inventory")
                                    re_sku["7天补货库存"] = sku_log_object.get("safety_inventory")
                            re_sku["是否确认库存"] = "未确认"
                            if data_dict.get(product.product_id):
                                if data_dict.get(product.product_id)["product_confirm_state"] == 1:
                                    re_sku["是否确认库存"] = "已确认"
                        # 货盘库存
                        re_sku["预估销量"] = item_skus_estimated_sales_map.get(sku.sku_id, "") or ""

                        # 分销商历史价
                        history_price = 0
                        history_price_author_name = 0
                        latest_sku_obj = latest_history_info.get(sku.id)
                        if latest_sku_obj:
                            history_price = latest_sku_obj.get("history_price", "")
                            history_price_author_name = latest_sku_obj.get("author_name", "")

                        re_sku["历史价"] = history_price
                        re_sku["历史价主播名"] = history_price_author_name
                        # 云上珠宝历史价
                        if need_v_history_price:
                            v_history_price = None
                            v_latest = v_history_price_map.get(sku.id)
                            if v_latest:
                                v_history_price = v_latest.get("history_price", "")
                            re_sku["云上珠宝历史价"] = v_history_price
                    else:
                        re_sku["现货库存"] = sku.physical_inventory
                        re_sku["采购价格"] = sku.distributor_market_price

                    # 货盘备注
                    re_sku["货盘备注"] = ""
                    if data_dict.get(product.product_id):
                        re_sku["货盘备注"] = data_dict.get(product.product_id)["remark"]

                    if not is_distributor_mode:
                        # 成本值计算
                        re_sku["现货库存成本值"] = (sku.physical_inventory or 0) * sku.cost_price
                        re_sku["现货+7天库存成本值"] = ((sku.physical_inventory or 0) + (sku.safety_inventory or 0)) * sku.cost_price

                        # 价格范围
                        re_sku["价格范围"] = ""
                        price_range_conditions = {
                            "0-50": (0, 50),
                            "50-100": (50, 100),
                            "100-300": (100, 300),
                            "300-500": (300, 500),
                            "500-1000": (500, 1000),
                            "1000-3000": (1000, 3000),
                            "3000以上": (3000, None),
                        }
                        if sku.cost_price not in [None, "***"]:
                            for price_range, (low, high) in price_range_conditions.items():
                                if not high:
                                    if sku.cost_price >= float(low):
                                        re_sku["价格范围"] = price_range
                                else:
                                    if float(low) <= sku.cost_price < float(high):
                                        re_sku["价格范围"] = price_range

                    re_data.append(re_sku)
        byte_buffer = get_excel_async("商品", re_data, image_columns=[4], is_internal=True)
        if byte_buffer:
            byte_buffer.seek(0)

        # 上传oss
        download_url = upload_object_to_oss_with_bytes(filename, byte_buffer)
        task.status = "completed"
        task.finish_time = timezone.make_aware(datetime.now())
        task.download_url = download_url
        task.save()

        # 通知下一个任务
        next_task = DownloadTasks.objects.only("pk").filter(create_user=create_user_id, status="pending").first()
        if next_task:
            async_selection_plan_download_task.delay(next_task.pk)
        return True, f"任务ID: {task_pk},时间: {datetime.now()}, 耗时: {time.time() - start_time}"
    except Exception as e:
        task.error_reason = str(e)
        task.status = "failed"
        task.save()
        return False, str(e)


@app.task(queue="common_tasks")
def flush_products_market_price(min_cost_price, max_cost_price, markup_percent, products_pk_list: list = None):
    """
    刷新分销市场推广价
    :param min_cost_price: 最小推广价
    :param max_cost_price: 最大推广价
    :param markup_percent: 加价
    :param products_pk_list: 商品主键列表
    :return:
    """
    skus = StockKeepingUnit.objects.filter(
        product__is_in_distributor_market=True,
        become_history=False,
        cost_price__range=(min_cost_price or 0, max_cost_price or 0),
    ).only(
        "pk",
        "product_id",
    )
    # 针对部分商品
    if products_pk_list:
        skus = skus.filter(product_id__in=products_pk_list)
    p_updated_rows = 0
    sku_updated_rows = 0
    paginator = Paginator(skus, 200)
    for page_num in paginator.page_range:
        object_list = paginator.page(page_num).object_list
        sku_pk_list = {obj.pk for obj in object_list}
        product_pk_list = {obj.product_id for obj in object_list}

        if not sku_pk_list:
            continue

        with transaction.atomic():
            # 动态生成占位符
            placeholders = ",".join(["%s"] * len(sku_pk_list))
            # 构造 SQL 查询
            sql = f"""
                    UPDATE products_stockkeepingunit
                    SET distributor_market_price =
                        CASE
                            WHEN CEIL(cost_price * (%s / 100.0)) %% 10 = 1 THEN CEIL(cost_price * (%s / 100.0)) + 1
                            ELSE CEIL(cost_price * (%s / 100.0))
                        END
                    WHERE id IN ({placeholders});
                """

            # 构造参数列表
            params = [markup_percent, markup_percent, markup_percent, *sku_pk_list]

            # 执行 SQL
            with connection.cursor() as cursor:
                cursor.execute(sql, params)
                #
                sku_updated_rows += 0 if cursor.rowcount < 0 else cursor.rowcount
            # 更新商品信息
            sku_ret = (
                StockKeepingUnit.objects.filter(
                    product_id__in=product_pk_list,
                    become_history=False,
                    product__is_in_distributor_market=True,
                )
                .values("product_id")
                .annotate(
                    min_market_price=Min("distributor_market_price"),
                    max_market_price=Max("distributor_market_price"),
                )
                .values("product_id", "min_market_price", "max_market_price")
            )

            tmp_product_pk_list = [ret["product_id"] for ret in sku_ret]
            products = Product.objects.filter(
                pk__in=tmp_product_pk_list,
                is_in_distributor_market=True,
            )
            products_map = {p.pk: p for p in products}

            need_update_product = []
            for ret in sku_ret:
                p_id = ret["product_id"]
                min_market_price = ret["min_market_price"]
                max_market_price = ret["max_market_price"]
                if p_id not in products_map:
                    continue

                product = products_map[p_id]
                product.min_distributor_market_price = min_market_price
                product.max_distributor_market_price = max_market_price
                need_update_product.append(product)

            if need_update_product:
                p_updated_rows += Product.objects.bulk_update(need_update_product, fields=["min_distributor_market_price", "max_distributor_market_price"])

    return {"config": f"{min_cost_price}-{max_cost_price}-{markup_percent}", "sku_updated": sku_updated_rows, "p_updated": p_updated_rows}


@app.task(queue="common_tasks")
def flush_distributor_mode_relates(product_id_list, distributor_id, user_id):
    links = ProductLinkDistributor.objects.filter(product_id__in=product_id_list, distributor_id=distributor_id)
    exists_product_id_list = [link.product_id for link in links]
    links_map = {link.product_id: link for link in links}

    not_exists_id = list(set(product_id_list) - set(exists_product_id_list))
    need_create_objs = []
    need_update_objs = []
    # 更新关联关系
    for link in links:
        tmp_obj = links_map[link.product_id]
        tmp_obj.update_user = user_id
        need_update_objs.append(tmp_obj)

    # 创建关联关系
    sub_products = SubProduct.objects.filter(parent_product__product_id__in=not_exists_id, owner_id=distributor_id).values("parent_product__product_id", "code")
    code_map = {p["parent_product__product_id"]: p["code"] for p in sub_products}
    for tmp_product_id in not_exists_id:
        if tmp_product_id not in code_map:
            continue

        code = code_map[tmp_product_id]
        need_create_objs.append(
            ProductLinkDistributor(
                product_id=tmp_product_id,
                distributor_id=distributor_id,
                code=code,
                create_user=user_id,
            )
        )

    if need_update_objs:
        ProductLinkDistributor.objects.bulk_update(need_update_objs, fields=["update_user"], batch_size=1000)

    if need_create_objs:
        ProductLinkDistributor.objects.bulk_create(need_create_objs, batch_size=1000)

    # todo: 更新副本商品关联。 由于运营商现在不操作货盘，分销商加入都会有副本
    # ProductSelectionItem.objects.filter(
    #     product_id=product.product_id,
    #     selection_plan__distributor=self.current_user.distributor,
    #     selection_plan__state__in=[1, 2],
    # ).update(
    #     sub_product=_sub_product,
    # )


@app.task(queue="common_tasks")
def flush_user_read_selection_plan_status(item_pk_list, user_id):
    with transaction.atomic():
        items = ProductSelectionItem.objects.select_for_update().filter(pk__in=item_pk_list).only("id", "read_users")
        need_update_objs = []
        for item in items:
            read_users = item.read_users
            if read_users is None:
                item.read_users = [user_id]
                need_update_objs.append(item)
            elif user_id not in read_users:
                read_users.append(user_id)
                need_update_objs.append(item)

        rows_updated = 0
        if need_update_objs:
            rows_updated = ProductSelectionItem.objects.bulk_update(need_update_objs, fields=["read_users"])

        return {"rows_updated": rows_updated}


@app.task(queue="common_tasks")
def calculate_ended_plan_data(plan_id):
    try:
        plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
    except ProductSelectionPlan.DoesNotExist:
        return f"not found distributor_id {plan_id}"

    spu_count = plan.productselectionitem_set.filter(is_deleted=False).count()

    item_skus = plan.productselectionitemsku_set.select_related("sku").filter(item__is_deleted=False, become_history=False)

    total_order_count = 0
    total_sales_amount = 0
    total_profit = 0
    for item_sku in item_skus:
        total_order_count += item_sku.order_count or 0
        total_sales_amount += item_sku.sales_amount or 0

        if not item_sku.sku_history_info:
            cost_price = item_sku.sku.cost_price
        else:
            cost_price = item_sku.sku_history_info.get("cost_price") or item_sku.sku.cost_price or 0

        total_profit += (item_sku.actual_price or 0 - Decimal(cost_price)) * item_sku.actual_sales

    plan.spu_count = spu_count
    plan.total_order_count = total_order_count
    plan.total_sales_amount = total_sales_amount
    plan.total_profit = total_profit

    plan.save(update_fields=["spu_count", "total_order_count", "total_sales_amount", "total_profit"])
    return f"finish: {plan_id}, {spu_count}, {total_order_count}, {total_sales_amount}, {total_profit}"


@app.task(queue="common_tasks")
def start_dd_products_synchronous(data_shop_pk=None):
    try:
        logger.info(f"{datetime.now()}抖店商品同步开始。")
        dd_products_synchronous(data_shop_pk)
    except Exception as e:
        logger.error(f"{datetime.now()}抖店同步报错：{e}")


@app.task(queue="common_tasks")
def start_dd_products_detail_synchronous(data_shop_pk=None):
    try:
        logger.info(f"{datetime.now()}抖店商品详情同步。")
        dd_products_detail_synchronous(data_shop_pk)
        logger.info(f"{datetime.now()}抖店商品详情结束。")
    except Exception as e:
        logger.error(f"{datetime.now()}抖店商品详情报错：{e}")


@app.task(queue="common_tasks")
def start_dd_sku_synchronous(data_shop_pk=None):
    try:
        logger.info(f"{datetime.now()}抖店sku商品同步开始。")
        dd_sku_synchronous(data_shop_pk)
    except Exception as e:
        logger.error(f"{datetime.now()}抖店sku同步报错：{e}")


@app.task(queue="common_tasks")
def start_dd_specs_synchronous(data_shop_pk=None):
    try:
        logger.info(f"{datetime.now()}抖店商品规格同步开始。")
        dd_specs_synchronous(data_shop_pk)
        logger.info(f"{datetime.now()}抖店商品规格同步结束。")
    except Exception as e:
        logger.error(f"{datetime.now()}抖店商品规格同步报错：{e}")


@app.task(queue="common_tasks")
def start_create_associations(data_shop_pk=None):
    try:
        logger.info(f"{datetime.now()}抖店sku商品与珠凌匹配开始。")
        dd_sku_association_synchronous(data_shop_pk)
        logger.info(f"{datetime.now()}抖店sku商品与珠凌匹配结束。")
    except Exception as e:
        logger.error(f"{datetime.now()}抖店sku商品与珠凌匹配报错：{e}")


@app.task(queue="common_tasks")
def edit_dd_product_spec_code_synchronous(map_id: int):
    try:
        try:
            map_obj = EXProductsAssociationModel.objects.get(pk=map_id, uploaded=False)
        except EXProductsAssociationModel.DoesNotExist:
            logger.warning(f"映射: {map_id}不存在")
            return

        if not map_obj.shop_id:
            logger.warning(f"映射: {map_id}不存在店铺id")
            return

        data_shop = DataShop.objects.filter(shop_id=map_obj.shop_id).only("id").first()
        if not data_shop:
            logger.warning(f"映射: {map_id}店铺ID: {map_obj.shop_id}不存在")
            return
        response = update_dy_sku_code(data_shop.pk, str(map_obj.ex_product.sku_id), map_obj.spec_code)
        if response.code != 10000:
            map_obj.errors = json.dumps(response.__dict__, ensure_ascii=False)
            map_obj.save(update_fields=["errors"])
            logger.info(f"映射: {map_id}店铺ID: {map_obj.shop_id}成功")
        else:
            map_obj.uploaded = True
            map_obj.save(update_fields=["uploaded"])
    except Exception as e:
        logger.error(f"更新抖店商编错误: {e}")


@app.task(queue="celery")
def correct_sku_inventory(plan_id, sku_id=None):
    item_pk = None
    if sku_id:
        item_obj = (
            ProductSelectionItemSKU.objects.filter(selection_plan__state__in=[1, 2], become_history=False, selection_plan_id=plan_id, item__is_deleted=False).only("item_id").last()
        )
        if not item_obj:
            return f"sku: {sku_id} cannot found in plan_id."

        item_pk = item_obj.item_id

    items = ProductSelectionItem.objects.filter(selection_plan__state__in=[1, 2], is_deleted=False, selection_plan_id=plan_id)
    if item_pk:
        items = items.filter(pk=item_pk)

    product_id_list = []
    need_update_item = []
    for item in items:
        if item.product_id not in product_id_list:
            product_id_list.append(item.product_id)

        item_sku_qs = item.productselectionitemsku_set.filter(become_history=False, item__is_deleted=False, selection_plan__state__in=[1, 2])
        item.physical_inventory = item_sku_qs.aggregate(total_physical=Sum("physical_inventory"))["total_physical"] or 0
        need_update_item.append(item)

    item_rows = ProductSelectionItem.objects.bulk_update(need_update_item, fields=["physical_inventory"], batch_size=1000)
    print(f"更新Item:{item_rows}")

    need_update_skus = []
    skus = StockKeepingUnit.objects.filter(become_history=False, product__product_id__in=product_id_list)
    for sku in skus:
        item_sku_qs = sku.productselectionitemsku_set.filter(become_history=False, item__is_deleted=False, selection_plan__state__in=[1, 2])
        sku.plan_use_inventory = item_sku_qs.aggregate(total_physical=Sum("physical_inventory"))["total_physical"] or 0
        sku.can_use_inventory = sku.physical_inventory - sku.plan_use_inventory

        need_update_skus.append(sku)
    sku_rows = StockKeepingUnit.objects.bulk_update(need_update_skus, fields=["plan_use_inventory", "can_use_inventory"], batch_size=1000)
    print(f"更新SKU:{sku_rows}")

    need_update_products = []
    products = Product.objects.filter(product_id__in=product_id_list, is_deleted=False)
    for product in products:
        item_sku_qs = product.stockkeepingunit_set.filter(become_history=False)
        total_plan_use_inventory = item_sku_qs.aggregate(total_plan_use_inventory=Sum("plan_use_inventory"))["total_plan_use_inventory"] or 0
        product.plan_use_inventory = total_plan_use_inventory
        product.can_use_inventory = product.physical_inventory - product.plan_use_inventory
        need_update_products.append(product)
    prod_rows = Product.objects.bulk_update(need_update_products, fields=["plan_use_inventory", "can_use_inventory"], batch_size=1000)
    print(f"更新商品:{prod_rows}")


@app.task(queue="celery")
def start_dail_new_products_subscribe(mini_program):
    try:
        logger.info(f"小程序{mini_program}每日上新订阅开始推送")
        dail_new_products_subscribe(mini_program)
        logger.info(f"小程序{mini_program}每日上新订阅结束推送")
    except Exception as e:
        logger.error(f"小程序{mini_program}每日上新订阅报错：{str(e)}")


@app.task(queue="common_tasks")
def start_feishu_daily_new_product():
    try:
        logger.info("飞书每日上新开始推送")
        feishu_daily_new_product()
        logger.info("飞书每日上新开始推送结束推送")
    except Exception as e:
        logger.error(f"飞书每日上新开始推送：{str(e)}")


@app.task(queue="common_tasks")
def start_product_region_stats_command(start_date=None, end_date=None):
    try:
        logger.info("开始每日统计商品地区销售数量")
        product_region_stats_command(start_date=start_date, end_date=end_date)
        logger.info("每日统计商品地区销售数量结束")
    except Exception as e:
        logger.error(f"每日统计商品地区销售数量有误：{str(e)}")
