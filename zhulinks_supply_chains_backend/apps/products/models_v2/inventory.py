# -*- coding: utf-8 -*-
from django.db import models

from companies.models import Distributor
from products.models import StockKeepingUnit


class DistributorWarehouseInventory(models.Model):
    """
    信号计算到sku总的现货库存
    """

    sku = models.ForeignKey(
        StockKeepingUnit,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="sku_id",
        verbose_name="关联sku_id",
    )
    distributor = models.ForeignKey(
        Distributor,
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="distributor_id",
        verbose_name="关联分销商",
    )
    warehouse_inventory = models.IntegerField("实际可用仓库库存", default=0)
    qty = models.IntegerField("库存", default=0)
    order_lock_qty = models.IntegerField("订单锁库存", default=0)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "分销商仓库库存"
        verbose_name_plural = verbose_name
