# -*- coding: utf-8 -*-
from django.db import models

from common.basics.exceptions import APIViewException
from utils.common import get_random


class OverseasProductStyle(models.Model):
    name = models.CharField("款式名称", max_length=128, unique=True)

    class Meta:
        verbose_name = "海外榜单商品款式"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


class OverseasProductMaterial(models.Model):
    name = models.CharField("材质名称", max_length=128, unique=True)

    class Meta:
        verbose_name = "海外榜单商品材质"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


def external_product_id_generator():
    for i in range(120):
        p_id = get_random()
        if not OverseasProduct.objects.filter(product_id=p_id).exists():
            return p_id
    else:
        raise APIViewException(err_message="System is busy, please try again later")


class OverseasCountry(models.Model):
    name = models.CharField("国家名称", max_length=32, unique=True)

    class Meta:
        verbose_name = "海外商品的国家"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


class OverseasPlatform(models.Model):
    country = models.ForeignKey(OverseasCountry, on_delete=models.CASCADE, db_constraint=False, verbose_name="所属国家")
    name = models.CharField("平台名称", max_length=32)

    class Meta:
        verbose_name = "海外商品平台"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


class OverseasProduct(models.Model):
    relate_platform = models.ForeignKey(OverseasPlatform, on_delete=models.CASCADE, db_constraint=False, verbose_name="关联的平台")
    product_id = models.BigIntegerField("商品id", unique=True, blank=False, null=False, default=external_product_id_generator)
    platform_product_id = models.CharField("平台商品id", max_length=128)
    currency = models.CharField("基础货币", max_length=3, default="USD")
    sales_price = models.DecimalField("售价", max_digits=10, decimal_places=2)
    name = models.CharField("商品名称", max_length=255)
    brand = models.CharField("商品品牌", max_length=32, null=True, blank=True, default="")
    product_link = models.CharField("商品链接", max_length=300, null=True, blank=True, default="")
    img = models.JSONField("商品图片列表", null=True, blank=True, default=list)
    month_sales = models.IntegerField("月销量", default=0)
    sales = models.IntegerField("总销量", default=0)
    comment_count = models.IntegerField("评论数", default=0)
    positive_comment_rate = models.DecimalField("好评率", max_digits=6, decimal_places=2, default=0)
    publish_date = models.DateTimeField("上架时间", null=True, blank=True, default=None)
    style = models.ForeignKey(OverseasProductStyle, on_delete=models.CASCADE, null=True, blank=True, default=None, verbose_name="关联款式")
    material = models.ForeignKey(OverseasProductMaterial, on_delete=models.CASCADE, null=True, blank=True, default=None, verbose_name="关联材质")
    rank = models.IntegerField("排名(小到大排序)", default=0)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "海外榜单商品"
        verbose_name_plural = verbose_name
        ordering = ("rank",)
        unique_together = ("relate_platform", "platform_product_id", "rank")
