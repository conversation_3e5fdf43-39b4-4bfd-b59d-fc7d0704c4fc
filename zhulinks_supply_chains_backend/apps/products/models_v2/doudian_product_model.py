from django.db import models


class DouDianProduct(models.Model):
    # 商品基础信息
    product_id = models.BigIntegerField(unique=True, verbose_name="商品ID")
    product_id_str = models.CharField(max_length=255, verbose_name="商品ID（字符串类型）")
    out_product_id = models.BigIntegerField(null=True, blank=True, verbose_name="外部商家编码（即将废弃）")
    outer_product_id = models.CharField(null=True, blank=True, max_length=255, verbose_name="外部商家编码")
    name = models.CharField(max_length=255, verbose_name="商品标题")
    description = models.TextField(null=True, blank=True, verbose_name="商品详情")
    delivery_method = models.IntegerField(null=True, blank=True, verbose_name="承诺发货时间（天）")
    cdf_category = models.CharField(max_length=255, null=True, blank=True, verbose_name="海南免税海关限购分类编码")
    status = models.IntegerField(verbose_name="商品状态")
    spec_id = models.BigIntegerField(null=True, blank=True, verbose_name="商品规格ID")
    check_status = models.IntegerField(null=True, blank=True, verbose_name="审核状态")
    brand_id = models.IntegerField(null=True, blank=True, verbose_name="品牌ID（已废弃）")
    is_sub_product = models.BooleanField(default=False, verbose_name="是否是组合商品的子商品")
    draft_status = models.IntegerField(null=True, blank=True, verbose_name="草稿状态")
    category_detail = models.JSONField(null=True, blank=True, verbose_name="类目详情")
    create_time = models.BigIntegerField(verbose_name="创建时间")
    update_time = models.BigIntegerField(verbose_name="更新时间")
    pic = models.JSONField(null=True, blank=True, verbose_name="商品主图")
    spec_pics = models.JSONField(null=True, blank=True, verbose_name="规格图片")
    spec_prices = models.JSONField(null=True, blank=True, verbose_name="商品SKU详情")
    presell_type = models.IntegerField(null=True, blank=True, verbose_name="预售类型")
    maximum_per_order = models.IntegerField(null=True, blank=True, verbose_name="单用户下单限购件数")
    limit_per_buyer = models.IntegerField(null=True, blank=True, verbose_name="单用户累计限购件数")
    minimum_per_order = models.IntegerField(null=True, blank=True, verbose_name="用户每次下单至少购买的件数")
    quality_list = models.JSONField(null=True, blank=True, verbose_name="资质信息")
    logistics_info = models.JSONField(null=True, blank=True, verbose_name="跨境物流信息")
    after_sale_service = models.JSONField(null=True, blank=True, verbose_name="售后服务")
    standard_brand_id = models.IntegerField(null=True, blank=True, verbose_name="品牌库brand id")
    market_price = models.DecimalField(null=True, blank=True, max_digits=30, decimal_places=2, verbose_name="划线价")
    discount_price = models.DecimalField(null=True, blank=True, max_digits=30, decimal_places=2, verbose_name="售卖价")
    car_vin_code = models.CharField(max_length=255, null=True, blank=True, verbose_name="汽车vin码")
    need_recharge_mode = models.BooleanField(null=True, blank=True, default=False, verbose_name="是否是生活娱乐充值模式")
    account_template_id = models.CharField(max_length=255, null=True, blank=True, verbose_name="多账号充值账号模板")
    presell_config_level = models.IntegerField(null=True, blank=True, verbose_name="发货模式")
    delivery_delay_day = models.IntegerField(null=True, blank=True, verbose_name="现货模式的发货天数")
    presell_delay = models.IntegerField(null=True, blank=True, verbose_name="预售发货时间")
    weight_value = models.FloatField(null=True, blank=True, verbose_name="重量数值")
    weight_unit = models.IntegerField(null=True, blank=True, verbose_name="重量单位")
    reference_price_certificate = models.JSONField(null=True, blank=True, verbose_name="参考价凭证")
    quality_inspection_info = models.JSONField(null=True, blank=True, verbose_name="前置质检相关")
    unit_price_info = models.JSONField(null=True, blank=True, verbose_name="单位价格信息")
    main_image_three_to_four = models.JSONField(null=True, blank=True, verbose_name="商品主图3:4")
    is_c2b_switch_on = models.BooleanField(default=False, verbose_name="是否支持c2b小程序")
    micro_app_id = models.CharField(max_length=255, null=True, blank=True, verbose_name="支持c2b定制时的小程序id")
    is_auto_charge = models.BooleanField(null=True, blank=True, default=False, verbose_name="是否是自动充值商品")
    spu_id = models.IntegerField(null=True, blank=True, verbose_name="spu_id")
    short_product_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="导购短标题")
    after_sale_service_v2 = models.JSONField(null=True, blank=True, verbose_name="售后服务新结构")
    format_update_time = models.DateTimeField(null=True, blank=True, verbose_name="更新时间（不同格式）")
    sell_num = models.IntegerField(null=True, blank=True, verbose_name="商品销量")
    product_audit_info = models.JSONField(null=True, blank=True, verbose_name="商品审核结果")
    material_audit_status = models.JSONField(null=True, blank=True, verbose_name="素材审核状态")
    material_audit_reason = models.JSONField(null=True, blank=True, verbose_name="素材审核驳回原因")
    main_pic_3_4 = models.JSONField(null=True, blank=True, verbose_name="商品主图3:4")
    shop_category = models.JSONField(null=True, blank=True, verbose_name="店铺装修分类")
    name_suffix = models.CharField(max_length=255, null=True, blank=True, verbose_name="标题后缀")
    use_brand_name = models.BooleanField(null=True, blank=True, default=False, verbose_name="是否勾选使用品牌名")
    new_step_product = models.BooleanField(null=True, blank=True, default=False, verbose_name="新现货+预售商品标识")
    is_charity_product = models.BooleanField(null=True, blank=True, default=False, verbose_name="是否公益商品")
    charity_id = models.CharField(max_length=255, null=True, blank=True, verbose_name="公益项目id")
    is_evaluate_opened = models.BooleanField(null=True, blank=True, default=False, verbose_name="测评模块是否打开")
    presell_delivery_type = models.IntegerField(null=True, blank=True, verbose_name="预售发货方式配置")
    start_sale_type = models.IntegerField(null=True, blank=True, verbose_name="审核通过后上架售卖时间配置")
    reduce_type = models.IntegerField(null=True, blank=True, verbose_name="库存扣减方式")
    presell_end_time = models.DateTimeField(null=True, blank=True, verbose_name="预售结束时间")
    freight_id = models.IntegerField(null=True, blank=True, verbose_name="运费模板ID")
    material_video_id = models.CharField(max_length=255, null=True, blank=True, verbose_name="主图视频ID")
    pickup_method = models.CharField(max_length=255, null=True, blank=True, verbose_name="提取方式新字段")
    size_info_template_id = models.IntegerField(null=True, blank=True, verbose_name="尺码模板ID")
    white_back_ground_pic_url = models.URLField(null=True, blank=True, verbose_name="白底图url")
    sale_channel_type = models.CharField(max_length=255, null=True, blank=True, verbose_name="销售渠道类型")
    store_id = models.IntegerField(null=True, blank=True, verbose_name="门店ID")
    main_product_id = models.IntegerField(null=True, blank=True, verbose_name="主商品ID")
    sale_limit_id = models.IntegerField(null=True, blank=True, verbose_name="限售模板ID")
    name_prefix = models.CharField(max_length=255, null=True, blank=True, verbose_name="标题前缀")
    product_type = models.IntegerField(choices=[(0, "普通"), (1, "新客商品"), (3, "虚拟"), (6, "玉石闪购"), (7, "云闪购"), (127, "其他类型")], verbose_name="商品类型")
    weight_value = models.FloatField(null=True, blank=True, verbose_name="重量数值")
    weight_unit = models.IntegerField(null=True, blank=True, verbose_name="重量单位")
    img = models.URLField(null=True, blank=True, max_length=255, verbose_name="商品图片URL")
    mobile = models.CharField(max_length=255, null=True, blank=True, verbose_name="手机号")
    extra = models.JSONField(null=True, blank=True, verbose_name="额外信息")
    specs = models.JSONField(null=True, blank=True, verbose_name="规格信息")
    recommend_remark = models.CharField(max_length=255, null=True, blank=True, verbose_name="商家推荐语")
    is_sku_synchronous = models.BooleanField(default=False, verbose_name="是否同步SKU")
    is_detail_synchronous = models.BooleanField(default=False, verbose_name="是否同步详情")
    shop_id = models.BigIntegerField(verbose_name="店铺ID")
    is_package_product = models.BooleanField(default=False, verbose_name="是否是组合商品")
    can_not_combine_reason = models.TextField(null=True, blank=True, verbose_name="不可搭配原因")
    cos_ratio = models.IntegerField(null=True, blank=True, verbose_name="佣金比例")
    pay_type = models.IntegerField(null=True, blank=True, verbose_name="支付类型")

    is_create = models.CharField(null=True, blank=True, max_length=255, verbose_name="抖店创建(废弃)")
    long_pic_url = models.CharField(null=True, blank=True, max_length=255, verbose_name="3:4长图url")
    reference_price = models.DecimalField(null=True, blank=True, max_digits=30, decimal_places=2, verbose_name="参考价")
    appoint_delivery_day = models.IntegerField(null=True, blank=True, verbose_name="可预约发货天数")
    sell_channel = models.JSONField(null=True, blank=True, verbose_name="售卖方式")
    delay_rule = models.JSONField(null=True, blank=True, verbose_name="特殊日期延迟发货规则")
    product_format = models.JSONField(null=True, blank=True, verbose_name="类目属性")
    product_format_new = models.JSONField(null=True, blank=True, verbose_name="类目属性新")
    price_has_tax = models.IntegerField(null=True, blank=True, verbose_name="商品价格是否含税")

    class Meta:
        verbose_name = "抖店商品信息"
        verbose_name_plural = "抖店商品信息"

    def __str__(self):
        return self.name
