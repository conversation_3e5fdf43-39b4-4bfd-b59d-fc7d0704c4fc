# -*- coding: utf-8 -*-
from django.core import exceptions
from django.core.exceptions import FieldError, FullResultSet
from django.db import models, transaction, connections
from django.db.models.expressions import BaseExpression
from django.db.models.sql.compiler import SQLUpdateCompiler
from django.db.models.sql.constants import CURSOR
from django.db.models.sql.subqueries import UpdateQuery as BaseUpdateQuery
from django.dispatch import Signal
from simple_history.models import HistoricalRecords

from products.sources import PRODUCT_NO_NEED_TO_CREATE_HISTORY_FIELDS, SKU_NO_NEED_TO_CREATE_HISTORY_FIELDS

# 更新时创建版本记录
product_update_to_create_historical_signal = Signal()
sku_update_to_create_historical_signal = Signal()


#  ----- 支持product category根据下标更新数据 -----
# objects = ProductManager()
# Product.object.filter().update(category__0=[1]) 更新下标为0，ArrayField第一个值的数据
# Product.object.filter().update(category__0__1=[1,2]) 更新下标为0,1，ArrayField第一个和第二值的数据
# 以__切割，注意坐标和值的列表长度一致
class ProductCompiler(SQLUpdateCompiler):
    def as_sql(self):
        self.pre_sql_setup()
        if not self.query.values:
            return "", ()
        qn = self.quote_name_unless_alias
        values, update_params = [], []
        for field, model, val in self.query.values:
            self.name = name = field.column
            if hasattr(val, "alter_name"):
                self.name = name = val.alter_name(name)
                qn = lambda x: x
                val = val.value
                for idx, name_val in enumerate(name):
                    # todo:完善placeholder
                    values.append("%s = %s" % (qn(name_val), "%s"))
                    update_params.append(val[idx])
                    continue
                continue
            if hasattr(val, "resolve_expression"):
                val = val.resolve_expression(self.query, allow_joins=False, for_save=True)
                if val.contains_aggregate:
                    raise FieldError("Aggregate functions are not allowed in this query " "(%s=%r)." % (field.name, val))
                if val.contains_over_clause:
                    raise FieldError("Window expressions are not allowed in this query " "(%s=%r)." % (field.name, val))
            elif hasattr(val, "prepare_database_save"):
                if field.remote_field:
                    val = val.prepare_database_save(field)
                else:
                    raise TypeError("Tried to update field %s with a model instance, %r. " "Use a value compatible with %s." % (field, val, field.__class__.__name__))
            val = field.get_db_prep_save(val, connection=self.connection)
            # Getting the placeholder for the field.

            if hasattr(field, "get_placeholder") and getattr(field, "get_placeholder") is not None:
                placeholder = field.get_placeholder(val, self, self.connection)
            else:
                placeholder = "%s"
            # name = field.column
            if hasattr(val, "as_sql"):
                sql, params = self.compile(val)
                values.append("%s = %s" % (qn(name), placeholder % sql))
                update_params.extend(params)
            elif val is not None:
                values.append("%s = %s" % (qn(name), placeholder))
                update_params.append(val)
            else:
                values.append("%s = NULL" % qn(name))
        table = self.query.base_table
        result = [
            "UPDATE %s SET" % qn(table),
            ", ".join(values),
        ]

        try:
            where, params = self.compile(self.query.where)
        except FullResultSet:
            params = []
        else:
            result.append("WHERE %s" % where)
        print(" ".join(result), tuple(update_params + params))
        return " ".join(result), tuple(update_params + params)


class UpdateArrayByIndex:
    def __init__(self, indexes, value, field):
        self.indexes = indexes
        self.value = value
        self.base_field = field.base_field

    def alter_name(self, name):
        return [name + f"[{index}]" for index in self.indexes]


class ProductUpdateQuery(BaseUpdateQuery):
    def add_update_values(self, values):
        values_seq = []
        for name, val in values.items():
            if "__" in name:
                indexes = name.split("__")
                field_name = indexes.pop(0)
                field = self.get_meta().get_field(field_name)
                if not isinstance(val, (list, tuple)):
                    val = [val]

                val = UpdateArrayByIndex(indexes=[int(index) + 1 for index in indexes], value=val, field=field)
                model = field.model
            else:
                field = self.get_meta().get_field(name)
                direct = not (field.auto_created and not field.concrete) or not field.concrete
                model = field.model._meta.concrete_model
                if not direct or (field.is_relation and field.many_to_many):
                    raise FieldError("Cannot update model field %r (only non-relations and " "foreign keys permitted)." % field)
                if model is not self.get_meta().concrete_model:
                    self.add_related_update(model, field, val)
                    continue
            values_seq.append((field, model, val))
        return self.add_update_fields(values_seq)


class ProductQuerySet(models.QuerySet):
    def update(self, **kwargs):
        self._not_support_combined_queries("update")
        if self.query.is_sliced:
            raise TypeError("Cannot update a query once a slice has been taken.")
        self._for_write = True
        query = self.query.chain(ProductUpdateQuery)
        query.add_update_values(kwargs)

        # Inline annotations in order_by(), if possible.
        new_order_by = []
        for col in query.order_by:
            if annotation := query.annotations.get(col):
                if getattr(annotation, "contains_aggregate", False):
                    raise exceptions.FieldError(f"Cannot update when ordering by an aggregate: {annotation}")
                new_order_by.append(annotation)
            else:
                new_order_by.append(col)
        query.order_by = tuple(new_order_by)

        # Clear any annotations so that they won't be present in subqueries.
        query.annotations = {}

        with transaction.mark_for_rollback_on_error(using=self.db):
            rows = ProductCompiler(query, connections[self.db], self.db).execute_sql(CURSOR)
        self._result_cache = None
        return rows


class ProductManager(models.Manager):
    def get_queryset(self):
        return ProductQuerySet(model=self.model, using=self._db, hints=self._hints)


# --------------商品manager----------------


class CustomHistoricalRecords(HistoricalRecords):

    def create_historical_record(self, instance, history_type, using=None):
        if not isinstance(instance, models.Model):
            return
        for field in instance._meta.fields:
            value = getattr(instance, field.name)
            if isinstance(value, BaseExpression):
                # 手动处理F表达式
                value = instance.__class__.objects.filter(pk=instance.pk).values_list(field.name, flat=True).first()
                setattr(instance, field.name, value)

        super().create_historical_record(instance, history_type, using=using)


class ProductHistoricalQuerySet(models.QuerySet):
    def update(self, **kwargs):
        # 批量更新对象
        rows = super().update(**kwargs)

        need_to_create_history = True
        # 单独或仅修改过期时间和is_new状态，不创建版本
        if kwargs:
            kwargs_keys = set(kwargs.keys())
            if len(kwargs_keys - PRODUCT_NO_NEED_TO_CREATE_HISTORY_FIELDS) == 0:
                need_to_create_history = False

        if need_to_create_history:
            product_ids_generator = self.values_list("id", flat=True).iterator()
            product_update_to_create_historical_signal.send(sender=self.model, instance=self, product_ids_generator=product_ids_generator)
        return rows


class ProductHistoricalManager(models.Manager):
    def get_queryset(self):
        return ProductHistoricalQuerySet(self.model, using=self._db)


class SKUHistoricalQuerySet(models.QuerySet):
    def update(self, **kwargs):
        # 批量更新对象
        rows = super().update(**kwargs)
        need_to_create_history = True
        # 单独或仅修改现货库存、销量等不创建历史记录
        if kwargs:
            kwargs_keys = set(kwargs.keys())
            if len(kwargs_keys - SKU_NO_NEED_TO_CREATE_HISTORY_FIELDS) == 0:
                need_to_create_history = False

        if need_to_create_history:
            sku_ids_generator = self.values_list("id", flat=True).iterator()
            sku_update_to_create_historical_signal.send(sender=self.model, instance=self, sku_ids_generator=sku_ids_generator)
        return rows


class SKUHistoricalManager(models.Manager):
    def get_queryset(self):
        return SKUHistoricalQuerySet(self.model, using=self._db)


class ProductSaveSkipHistoryManager(models.Manager):
    pass


class SKUSaveSkipHistoryManger(models.Manager):
    pass
