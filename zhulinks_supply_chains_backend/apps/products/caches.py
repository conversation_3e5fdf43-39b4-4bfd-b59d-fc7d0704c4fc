# -*- coding: utf-8 -*-

from datetime import datetime, timedelta

from django.core.cache import cache
from django.utils import timezone

from common.models import ExchangeRate
from products.models import ProductCategoryList


def get_cache_category_with_id(category_id: int | str) -> dict:
    """
    珠凌分类信息
    :param category_id:
    :return:
    """
    if not category_id:
        return {}

    cache_key = f"zhulinks_category_{category_id}"
    val = cache.get(cache_key)
    if val:
        return val

    try:
        cate_qs = ProductCategoryList.objects.get(id=category_id)
        data = {"id": cate_qs.id, "name": cate_qs.name}
        cache.set(cache_key, data, timeout=60 * 60)
        return data
    except ProductCategoryList.DoesNotExist:
        return {}


def get_cache_timeout_until_time(target_time):
    now = timezone.now()
    target = now.replace(hour=target_time.hour, minute=target_time.minute, second=target_time.second, microsecond=0)
    if now > target:
        target += timedelta(days=1)
    return (target - now).total_seconds()


def get_exchange_rate(base, target):
    cache_key = f"exchange_rate:{base}_{target}"
    rate = cache.get(cache_key)
    if rate is None:
        try:
            rate_obj = ExchangeRate.objects.get(base_currency=base, target_currency=target)
            rate = rate_obj.rate
            timeout = get_cache_timeout_until_time(datetime.strptime("07:58", "%H:%M").time())
            cache.set(cache_key, rate, timeout)
        except ExchangeRate.DoesNotExist:
            # 处理汇率不存在的情况，可以抛出异常或返回默认值
            raise ValueError(f"Exchange rate from {base} to {target} not found.")
    return rate
