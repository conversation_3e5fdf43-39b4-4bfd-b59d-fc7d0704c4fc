# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-08-23 14:10:15
# @Last Modified by:
# @Last Modified time: 2023-12-22 17:48:53
import copy
import json
import re
import time
import traceback
import uuid
from copy import deepcopy
from datetime import datetime, timedelta
from functools import reduce
from hashlib import md5
from io import BytesIO
from operator import and_

import pandas as pd
import requests
from common.basic import CommonAPIView, IAPIView, OPAPIView
from common.basics.exceptions import APIViewException, NoPermissionException
from common.basics.views import OperateLogAPIViewMixin, operate_log_decorator, set_log_params
from common.formats import DATE_FORMAT, DATETIME_FORMAT, TIME_FORMAT
from common.models import CustomerService, OperationLog, SystemConfig
from common.serializers import OperationLogSerializer
from common.utils import ArrayReplace, ProductBulkExtraTags, convert_2_aware_time, diff_models, list_to_tree, sorted_distributor_info
from common.utils_v2.processing_translation import convert_ser_data, json_content_translation_replacement
from companies.models import Brand, Company, Distributor
from data_center.utils import get_day_month_range
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.core.exceptions import FieldError
from django.core.paginator import Paginator
from django.db import models, transaction
from django.db.models import Case, Count, F, FloatField, Max, Min, OuterRef, Prefetch, Q, Subquery, Sum, Value, When, Window
from django.db.models.functions import Coalesce, RowNumber
from django.db.utils import IntegrityError
from django.http import FileResponse, QueryDict
from django.utils import timezone
from erp_products.tasks import sync_product_info_to_erp_product
from orders.models import AfterSalesRelatedOrderInfo, ShoppingCart
from products import logger
from products import signals as product_signals
from products.filtersets import (
    CostItemConfigFilter,
    CostPriceTrendFilterSet,
    FrontProductCategoryFilterSet,
    HistoryPriceListFilterSet,
    HistoryPriceTrendFilterSet,
    HostingProductFilterSet,
    LiveNeedsFilterSet,
    ModHistoryCostPriceFilterSet,
    OPProductListFilterSet,
    OPProductStateReviewRecordFilterSet,
    OPSKUInventoryReviewRecordFilterSet,
    ProductAttrListFilterSet,
    ProductLabelsFilterSet,
    ProductSaleRankFilterSet,
    ProductSelectionPlanRecordFilterSet,
    SelectionPlanFilterSet,
    SelectionPlanItemRecycleListFilterSet,
    SKUInventoryFilterSet,
    SPSkuCostPriceReviewRecordFilterSet,
    TempProductFilterSet,
)
from products.logics.common import (
    attr_download_info_handler,
    bulk_delete_selection_products_fc,
    bulk_move_items_to_new_plan,
    clean_specs,
    clean_update_labels,
    common_add_product_to_plan_fc,
    common_create_gift,
    common_create_product,
    common_create_product_attr_options,
    common_create_sku_and_spec_options,
    common_query_item_skus_inventory,
    common_update_gift,
    common_update_plan_item_skus_fc,
    common_update_product_attr_options,
    create_combine_product,
    download_cs_selection_plan_product_list_fc,
    get_cs_selection_plan_items,
    map_item_move_fc,
    new_selection_plan_download_fc,
    op_db_update_sku_and_spec_options,
    plan_profit_margin,
    plan_profit_margin_download_data,
    post_product_link_distributor_fc,
    price_fill_data,
    product_copy_fc,
    product_list_params_handler,
    product_specs_update_handler,
    selection_plan_item_confirm_fc,
    selection_plan_item_move_fc,
    selection_plan_prods_map_fc,
    selection_plan_prods_map_for_feishu,
    sync_to_sub_products,
    update_combine_product,
    update_sku_inventory_fc,
    v2_plan_tree_list,
    validate_category,
)
from products.logics.selection_plan import add_selection_plan_item_mark, bulk_add_products_to_plan, common_modify_estimated_sales
from products.tasks_v2.flush_products_distributor_market_price import flush_products_distributor_market_price
from rest_framework.decorators import api_view, permission_classes
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.request import Request
from rest_framework.views import APIView
from users.models import LiveAuthor, User, get_default_system_user
from users.tasks import ceate_notifications
from utils.download_tmp import (
    OperatorDownloadBulkProductTmpl,
    OperatorDownloadProductTmpl,
    OperatorSelectionPlanDownloadProductTmpl,
    OPProductStateChangeReviewListTmpl,
    OPSKUInventoryReviewRecordListTmpl,
    SelectionPlanItemRecycleListDownloadTmpl,
    get_excel_async,
)
from utils.en_pptx import generate_en_pptx
from utils.http_handle import (
    EmptyListResponse,
    FieldsError,
    IResponse,
    convert_to_ware_time,
    custom_django_filter,
    custom_filter,
)
from utils.jst import JSTAPIClient
from utils.pptx import generate_pptx
from utils.redis_lock import read_lock, release_lock, setnx_lock
from utils.vector_database import search_image
from utils.vectorvein_ai import VectorveinClient

from ..bulk_query import bulk_query_confirm_state, bulk_query_labels, bulk_query_latest_history_price, bulk_query_skus_specs_detail, get_sku_download_specs_option_list
from ..logics.review import get_product_review_info
from ..models import (
    BatchSkuCostPriceRelate,
    CategoryAttrMembership,
    CostItemConfig,
    DistributorMarketProductPriceConfig,
    FrontProductCategoryList,
    HandCard,
    HistoryPrice,
    HostingProducts,
    LiveNeeds,
    Product,
    ProductAddress,
    ProductAdvicePrice,
    ProductAttrList,
    ProductAttrOption,
    ProductAttrValues,
    ProductCategoryList,
    ProductComments,
    ProductLabels,
    ProductLabelTypes,
    ProductLinkDistributor,
    ProductReview,
    ProductReviewedCostPrice,
    ProductReviewExtra,
    ProductReviewPrice,
    ProductReviewProcess,
    ProductSelectionItem,
    ProductSelectionPlan,
    ProductSelectionPlanCategory,
    ProductSelectionPlanCompany,
    ProductSelectionPlanRecord,
    ProductSelectionPlanRecordType,
    ProductStateReason,
    ProductStateReviewRecord,
    QAReviewQuestionType,
    SKUCostPriceReviewRecord,
    SKUInventoryReviewRecord,
    SKUSalesCount,
    StockKeepingUnit,
    SubProduct,
    TempProduct,
    TempProductMap,
    bulk_query_product_hosting_state,
    calculate_history_price,
)
from ..models_v2 import ProductConfig
from ..serializers import (
    CheckProductInOperateSelectionPlanSerializer,
    CombineProductDetailSer,
    CompanySaleRankFilterSer,
    CostItemConfigDetailSerializer,
    CostItemConfigListSerializer,
    CostItemConfigSerializer,
    CSOperateSelectionPlanProductListSerializer,
    FeishuQAProductReviewSerializer,
    HandCardCreateSerializer,
    HandCardDetailSerializer,
    HistoryPriceListSer,
    HistoryPriceSerializer,
    HostingProductListSer,
    HostingProductsCreateSer,
    LiveAuthorSelectSer,
    LiveNeedsInfoSer,
    MyProductCreateSer,
    OPCSSelectionPlanItemSKUDetailSerializer,
    OperateProductBulkListSerializer,
    OperateProductDetailForFeishuSerializer,
    OperateProductDetailSerializer,
    OperateProductListAPISerializer,
    OperateProductListSerializer,
    OperateSelectedProductListSerializer,
    OperateSelectionPlanProductListDownloadSerializer,
    OperateStockKeepingUnitProductDetailSerializer,
    OperateStockKeepingUnitProductListSerializer,
    OPProductModHistoryListSer,
    OPProductStateReviewRecordListDownloadSer,
    OPProductStateReviewRecordListSer,
    OPSkuCostPriceReviewRecordListSer,
    OPSKUInventoryReviewRecordListDownloadSer,
    OPSKUInventoryReviewRecordListSer,
    ProductAttrCreateOrUpdateSer,
    ProductAttrDetailSer,
    ProductAttrListSer,
    ProductAttrListSerializer,
    ProductAttrOptionInfoSer,
    ProductAttrOptionSerializer,
    ProductCategoryListSerializer,
    ProductForHandCardSearchSer,
    ProductGiftSer,
    ProductLabelsCreateOrUpdateSer,
    ProductLabelsDetailSer,
    ProductLabelsListSer,
    ProductLinkDistributorInfoSer,
    ProductReviewCreateSer,
    ProductReviewPriceInfoSer,
    ProductReviewPriceSer,
    ProductSaleRankFilterSer,
    ProductSelectionItemSerializer,
    ProductSelectionPlanListSerializer,
    ProductSelectionPlanReaderSerializer,
    ProductSelectionPlanSerializer,
    ProductSerializer,
    SelectionPlanItemRecycleListDownloadSer,
    SelectionPlanItemRecycleListSer,
    SKUCostPriceReviewRecordCreateSer,
    SKUInventoryInfoSer,
    StockKeepingUnitAPISerializer,
    TempProductListSer,
)
from ..tasks import (
    add_images_to_qdrant,
    async_move_old_plan_product_to_new_plan_invoker,
    delete_images_from_qdrant,
    delete_product,
    edit_dy_product_spec_code,
    generate_en_pptx_async,
    generate_pptx_async,
    handle_temp_product_images,
    insert_cost_price_change_table,
    insert_qa_product_review_table,
    insert_zl_new_product_to_feishu_table,
    insert_zl_self_support_new_product_to_feishu_table,
    send_pic_score_greate_than_70_notify,
    sync_category_to_JST,
    sync_product_to_JST,
    video_split_jpg,
    w_plan_record,
)
from ..tasks_v2.feishu_insert_product_price_change_notification import feishu_insert_product_price_change_notification


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_suppliersku(request):
    url = "/open/webapi/itemapi/suppliersku/getsupplierskulist"
    JstClient = JSTAPIClient()
    biz = {
        "begin": "2024-01-17 17:44:43",
        "end": "2024-03-17 17:44:43",
        "current_page": 1,
        "page_size": 10,
    }
    res = JstClient.request(url, biz)
    logger.info(f"res: {res}")
    return IResponse(data=res)


class OPProductBulkQueryView(OPAPIView):
    def post(self, request: Request):
        """
        批量查询商品接口
        """
        try:
            raw_params = request.query_params.copy()
            raw_data = request.data
            filters = dict(
                is_deleted=False,
            )
            and_Q = None

            ids = raw_data.get("ids")
            if ids:
                assert isinstance(ids, list) and len(ids) <= 500, "ids should be a array and len less than 500"
                id_type = raw_data.get("id_type")
                assert id_type and id_type in ["product_id", "code"], "id_type invalid"

                if id_type == "product_id":
                    filters[f"{id_type}__in"] = ids
                else:
                    if not and_Q:
                        and_Q = Q()
                    and_Q.add(Q(**{"code__in": ids}), Q.OR)
                    and_Q.add(Q(**{"productlinkdistributor__code__in": ids}), Q.OR)

            distributor_id = raw_data.get("distributor_id")
            if distributor_id:
                if not isinstance(distributor_id, (int, str)):
                    return IResponse(code=400, message="invalid params")

            target = Product.get_operator_products(request)

            page_products, re_data, product_qs = custom_filter(
                raw_params,
                target,
                array_fields=["category"],
                like_fields=["name", "product_id", "code", "productlinkdistributor__code"],
                hybrid_fields=["name", "__code", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
                query_Q=and_Q,
                **filters,
            )
            sub_products_map = {}
            if distributor_id:
                sub_products = SubProduct.objects.filter(parent_product__in=page_products, owner_id=distributor_id, is_deleted=False).only("parent_product_id", "code")
                sub_products_map = {sub_product.parent_product_id: sub_product.code for sub_product in sub_products}

            for product_obj in page_products:
                product = OperateProductBulkListSerializer(instance=product_obj, context={"request": request})
                data = product.data
                category = data.get("category")
                category_list = get_category_name_by_id(category)
                data["category"] = category_list

                # 副本货号
                data["sub_code"] = sub_products_map.get(product_obj.pk, "") or ""

                # 添加商品确认，确认状态为1时不允许修改库存数据
                data["product_confirm_state"] = product_obj.product_confirm_state

                sku_qs = product_obj.stockkeepingunit_set.filter(become_history=False)
                skus = OperateStockKeepingUnitProductListSerializer(instance=sku_qs, many=True, context={"request": request})
                data["skus"] = skus.data
                re_data["data"].append(data)

            return IResponse(data=re_data)
        except ValueError as e:
            if "Field 'product_id' expected a number" in str(e):
                return IResponse(message="The expected value type of product id is number, please check the number type", code=400)
            else:
                return IResponse(message=str(e), code=400)
        except Exception as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=500)


class OPProductBulkDownloadView(OPAPIView):
    resource_name = "批量下载商品"
    fronted_page = "商品列表"
    need_format_resource_name = False

    def post(self, request: Request):
        """
        批量下载商品接口
        """
        try:
            re_data = []
            current_user = request.user
            if request.auth.get("user_type") != "OP":
                return IResponse(code=403, message="no permission to operate")

            raw_params = request.query_params.copy()
            raw_data = request.data
            filters = dict(
                is_deleted=False,
            )
            and_Q = None

            ids = raw_data.get("ids")
            if ids:
                assert isinstance(ids, list) and len(ids) <= 500, "ids should be a array and len less than 500"
                id_type = raw_data.get("id_type")
                assert id_type and id_type in ["product_id", "code"], "id_type invalid"
                if id_type == "product_id":
                    filters[f"{id_type}__in"] = ids
                else:
                    if not and_Q:
                        and_Q = Q()
                    and_Q.add(Q(**{"code__in": ids}), Q.OR)
                    and_Q.add(Q(**{"productlinkdistributor__code__in": ids}), Q.OR)

            distributor_id = raw_data.get("distributor_id")
            if distributor_id:
                if not isinstance(distributor_id, (int, str)):
                    return IResponse(code=400, message="invalid params")

            # 筛选运营商的商品
            target = Product.get_operator_products(request)
            page_products, _, product_qs = custom_filter(
                raw_params,
                target,
                array_fields=["category"],
                like_fields=["name", "product_id", "code", "remark", "productlinkdistributor__code"],
                hybrid_fields=["name", "product_id", "__code", "remark", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
                query_Q=and_Q,
                **filters,
            )
            sub_products_map = {}
            if distributor_id:
                sub_products = SubProduct.objects.filter(parent_product__in=page_products, owner_id=distributor_id, is_deleted=False).only("parent_product_id", "code")
                sub_products_map = {sub_product.parent_product_id: sub_product.code for sub_product in sub_products}

            num = 0
            # 查询所属地区
            address_ids = {product.address_id for product in page_products}
            address_objs = ProductAddress.objects.filter(id__in=address_ids)
            address_map = {address.id: address.name for address in address_objs}
            # 日志记录成功下载的product_id
            downloaded_product_ids = []

            for product_qs in page_products:
                re_product = {}
                product_data = OperateProductBulkListSerializer(instance=product_qs, context={"request": request}).data
                # template data
                for k, v in OperatorDownloadBulkProductTmpl.items():
                    if k == "sub_code":
                        re_product[v] = sub_products_map.get(product_qs.pk, "") or ""
                        continue

                    value = product_data.get(k)
                    re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value

                    if k == "address":
                        re_product[v] = address_map.get(product_qs.address_id, "")

                # 商品参数下载
                attr_download_info_handler(re_product, product_qs)

                # category
                category = product_data.get("category")
                category_id_and_name_list = get_category_name_by_id(category_list=category)
                name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目"}
                for i in range(len(category_id_and_name_list)):
                    re_product[f"{name_key[i]}ID"] = category_id_and_name_list[i].get("id")
                    re_product[f"{name_key[i]}名称"] = category_id_and_name_list[i].get("name")
                    if i == 3:
                        break
                # brand
                re_product["区域ID"], re_product["区域名称"] = None, None
                brand = product_data.get("brand")
                if brand:
                    brand_obj = Brand.objects.get(id=brand)
                    re_product["区域ID"], re_product["区域名称"] = brand_obj.id, brand_obj.name

                # 关联货号
                links_qs = ProductLinkDistributor.objects.select_related("distributor").filter(product_id=product_qs.product_id, is_deleted=False)
                data_list = [f"{link.distributor.name}:{link.code}" for link in links_qs]
                data_str = ""
                if data_list:
                    data_str = ";".join(data_list)
                re_product["关联货号"] = data_str

                # sku
                skus_qs = StockKeepingUnit.objects.filter(product=product_qs, become_history=False)
                for sku_qs in skus_qs:
                    num += 1
                    re_sku = {
                        "序号": num,
                    }
                    re_sku.update(re_product)
                    re_sku["规格ID"] = sku_qs.sku_id
                    # spec
                    spec_option_list = get_sku_download_specs_option_list(sku_qs)
                    re_sku["规格"] = ";".join(spec_option_list)
                    re_sku["建议售价"] = sku_qs.retail_price

                    # 链接编码
                    re_sku["链接编码"] = sku_qs.link_code
                    # 销量
                    re_sku["销量"] = sku_qs.sales
                    # 需要权限
                    if current_user.has_perm("products.view_cost_price_StockKeepingUnit"):
                        re_sku["推广价"] = sku_qs.cost_price
                    else:
                        re_sku["推广价"] = "***"
                    re_sku["现货库存"] = sku_qs.physical_inventory
                    re_sku["7天补货库存"] = sku_qs.safety_inventory
                    re_sku["是否已播"] = "是" if sku_qs.has_live else "否"

                    history_price = None
                    history_price_author_id = None
                    history_price_author_name = None
                    latest = HistoryPrice.objects.select_related("author").filter(sku_id=sku_qs.id).order_by("-create_date").first()
                    if latest:
                        history_price = latest.history_price
                        if latest.author:
                            history_price_author_id = latest.author.author_id
                            history_price_author_name = latest.author.name
                    re_sku["历史价"] = history_price
                    re_sku["历史价主播id"] = history_price_author_id
                    re_sku["历史价主播名"] = history_price_author_name
                    re_data.append(re_sku)

                    # 记录操作日志
                    downloaded_product_ids.append(f"商品:{product_qs}")

            filename = f"商品列表{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
            byte_buffer = get_excel_async("商品", re_data, image_columns=[3], is_internal=True)
            if byte_buffer:
                byte_buffer.seek(0)

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=None,
                    model=None,
                    describe="批量下载商品",
                    operate_content="下载商品ID: " + "、".join(downloaded_product_ids),
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return FileResponse(byte_buffer, filename=filename, as_attachment=True)
        except Exception as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=500)


class OPProductBulkDeleteView(OPAPIView):
    resource_name = "批量删除商品"
    fronted_page = "商品列表"
    need_format_resource_name = False

    def post(self, request: Request):
        """
        批量删除商品
        """
        raw_params = request.query_params.copy()
        raw_data = request.data
        filters = dict(is_deleted=False)

        ids = raw_data.get("ids")
        if ids:
            assert isinstance(ids, list) and len(ids) <= 500, "ids should be a array and len less 500"
            filters["product_id__in"] = ids

        # 获取运营商的商品
        target = Product.get_operator_products(request)

        page_products, re_data, product_qs = custom_filter(
            raw_params,
            Product,
            array_fields=["category"],
            like_fields=["name", "product_id", "code", "productlinkdistributor__code"],
            hybrid_fields=["name", "__code", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
            **filters,
        )

        downloaded_product_ids = []
        raw_ids = []
        if product_qs:
            raw_ids = [i.id for i in product_qs]
            downloaded_product_ids = [i.product_id for i in product_qs]
            product_qs.update(is_deleted=True)

        # 删除向量数据库数据
        for i in raw_ids:
            delete_images_from_qdrant.delay(i)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=None,
                model=None,
                describe="批量删除商品",
                operate_content="批量删除商品ID: " + "、".join(downloaded_product_ids),
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse(code=200)


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_product_by_api(request):
    """
    删除商品及oss
    - 删除 sku
    - 删除 attroption
    - 删除 productreview
    """
    try:
        current_user = request.user
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")
        raw_params = request.query_params.copy()

        filters = dict(
            is_deleted=False,
        )
        page_products, re_data, product_qs = custom_filter(
            raw_params,
            Product,
            array_fields=["category"],
            like_fields=["name", "product_id", "code", "productlinkdistributor__code"],
            hybrid_fields=["name", "__code", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
            **filters,
        )
        # product_qs = Product.objects.all()
        page_size = 50
        paginator = Paginator(product_qs.values("product_id"), page_size)
        total_page = paginator.num_pages
        for i in range(total_page):
            page_model_objects = paginator.page(i + 1)
            for product in page_model_objects:
                delete_product.delay(product.get("product_id"))

        return IResponse(data="Completed")
    except Exception as e:
        return IResponse(code=500, message=str(e))


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def op_post_product_by_api(request):
    """
    todo: 从聚水潭爬取的数据同步到珠凌，暂时不改。
    后续通过api接口从珠凌同步到聚水潭数据


    供接口上传商品数据
    根据 external_sku_id 进行判断
    """
    try:
        raw_data = request.data
        current_user = request.user
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")
        raw_data["create_user"], raw_data["update_user"] = current_user.user_id, current_user.user_id
        # assert raw_data.get("skus") and raw_data.get("attr_options") and raw_data.get("company_id"), "required: skus, attr_options, company_id"
        assert raw_data.get("skus") and raw_data.get("company_id"), "required: skus, attr_options, company_id"
        # 只有一个sku
        skus_data = raw_data.pop("skus")[0]
        if raw_data.get("attr_options"):
            attr_options_data = raw_data.pop("attr_options")
        company_id = raw_data.pop("company_id")
        history_price_data = None
        if raw_data.get("history_price"):
            history_price_data = raw_data.pop("history_price")
        try:
            company = Company.objects.get(company_id=company_id)
        except Company.DoesNotExist:
            return IResponse(code=400, message="company_id invalid")
        raw_data["company_id"] = company.id
        # 废弃字段
        if raw_data.get("brand"):
            raw_data["brand_id"] = raw_data.pop("brand")
        # 新增address字段
        if raw_data.get("address"):
            raw_data["address_id"] = raw_data.pop("address")

        if raw_data.get("unit"):
            raw_data["unit_id"] = raw_data.pop("unit")
        if raw_data.get("spec_lists"):
            # 剔除垃圾数据
            new_spec_lists = []
            spec_lists = raw_data.pop("spec_lists")
            for i in spec_lists:
                new_spec_lists.append(
                    {
                        "source_id": i.get("source_id"),
                        "name": i.get("name"),
                        "values": i.get("values"),
                    }
                )
            raw_data["spec_lists"] = new_spec_lists
        # 商品审核
        raw_data["state"] = 1
        # 数据来源
        if not raw_data.get("data_source"):
            raw_data["data_source"] = "OP"
        review_data = {
            "physical_inventory_exact": True,
            "quality_qualified": True,
            "price_reasonable": True,
            "remark": f"{raw_data.get('data_source')}上传的商品",
        }
        external_sku_id = skus_data.get("external_sku_id")
        if not external_sku_id:
            return IResponse(code=400, message="external_sku_id required")

        try:
            with transaction.atomic():
                # == 创建或更新商品数据 ==
                logger.info(f"enter atomic: external_sku_id: {external_sku_id}")
                sku = StockKeepingUnit.objects.filter(external_sku_id=external_sku_id).first()
                if not sku:
                    product = Product.objects.create(**raw_data)
                else:
                    if raw_data.get("company_id"):
                        raw_data["company"] = raw_data.pop("company_id")
                    if raw_data.get("brand_id"):
                        raw_data["brand"] = raw_data.pop("brand_id")
                    if raw_data.get("unit_id"):
                        raw_data["unit"] = raw_data.pop("unit_id")
                    serializer = OperateProductListAPISerializer(sku.product, data=raw_data, partial=True, context={"request": request})
                    if not serializer.is_valid():
                        raise FieldsError(serializer.errors)
                    product = serializer.save()
                # == 创建审核数据 ==
                review_data["create_user"] = raw_data.get("create_user")
                review_data["update_user"] = raw_data.get("update_user")
                product_review, _ = ProductReview.objects.update_or_create(product=product, defaults=review_data)
                # == 创建或更新sku数据 ==
                history_price_data = []
                if skus_data.get("history_price_data"):
                    history_price_data = skus_data.pop("history_price_data")
                try:
                    sku = StockKeepingUnit.objects.get(external_sku_id=external_sku_id, product=product)
                    for key, value in skus_data.items():
                        setattr(sku, key, value)
                    sku.save(**{"request": request})
                except StockKeepingUnit.DoesNotExist:
                    skus_data["product_id"] = product.id
                    skus_data["external_sku_id"] = external_sku_id
                    sku = StockKeepingUnit(**skus_data)
                    sku.save(**{"request": request})
                sku_data = StockKeepingUnitAPISerializer(instance=sku, context={"request": request}).data
                # == 创建或更新ProductAttrOption ==
                options_data = []
                for attr_option_data in attr_options_data:
                    if attr_option_data.get("value"):
                        attr_id = attr_option_data.pop("attr_id")
                        attr, _ = ProductAttrOption.objects.update_or_create(product=product, attr_id=attr_id, value=attr_option_data["value"])
                        attr_data = ProductAttrOptionSerializer(instance=attr).data
                        attr_data["attr_id"] = attr_data.pop("attr")
                        attr_data.pop("product")
                        options_data.append(attr_data)
                # == debug ==
                try:
                    _product = Product.objects.get(product_id=product.product_id, is_deleted=False)
                except Product.DoesNotExist:
                    logger.info(f"test, external_sku_id: {external_sku_id}, product: {product.product_id} not found")
        except ValueError as e:
            logger.error(str(e))
            return IResponse(code=400, message=str(e))
        except IntegrityError as e:
            logger.error(str(e))
            return IResponse(code=500, message=str(e))
        logger.info(f"exit atomic: external_sku_id: {external_sku_id}")

        # 写入历史价
        for history_price in history_price_data:
            history_price["sku_id"] = sku.sku_id
        if history_price_data:
            lastest = None
            for i in history_price_data:
                if str(i.get("author_id", "")) == "3852285068190743":
                    lastest = i
                    continue
                insert_history_price(product_id=product.product_id, raw_data=[i])
            if lastest:
                insert_history_price(product_id=product.product_id, raw_data=[lastest])
        # else:
        #     # 如果没传历史价，并且本身没有历史价，则将零售价转为历史价
        #     history_qs = HistoryPrice.objects.filter(product=product)
        #     if not history_qs.exists():
        #         if sku_data.get("retail_price"):
        #             data = [{"sku_id": sku.sku_id, "history_price": sku_data["retail_price"]}]
        #             insert_history_price(product_id=product.product_id, raw_data=data)

        # 获取最新数据
        new_product = Product.objects.get(pk=product.id)
        # 上传图片至qdrant
        images = deepcopy(new_product.main_images)
        # if new_product.detail_images:
        #     images.extend(deepcopy(new_product.detail_images))
        if sku.image:
            images.append(sku.image)
        add_images_to_qdrant.delay(new_product.id, list(set(images)))

        resp_data = ProductSerializer(instance=new_product, context={"request": request}).data
        category = resp_data.get("category")
        category_list = get_category_name_by_id(category)
        resp_data["category"] = category_list
        resp_data.pop("company")
        resp_data["skus"] = [sku_data]
        resp_data["attr_options"] = options_data

        return IResponse(data=resp_data)
    except AssertionError as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=400, message=str(e))
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=500, message=str(e))


class OperatorProductView(OPAPIView):
    """
    运营商商品
    """

    resource_id_field = "product_id"
    resource_name = "运营商商品"
    fronted_page = "商品列表"
    need_format_resource_name = True

    @staticmethod
    def _insert_sku_retail_price(product: Product, price):
        skus_qs = product.stockkeepingunit_set.filter(become_history=False).only("sku_id", "retail_price")
        # 多个sku 全部retail_price为空的时候填入建议零售价
        retail_prices = [sku.retail_price for sku in skus_qs if sku.retail_price]
        if not retail_prices:
            skus_qs.update(retail_price=price)

    @staticmethod
    def _create_review(process: ProductReviewProcess, raw_data, product, current_user):
        raw_data["process"] = process.process_id
        raw_data["product"] = product.id
        raw_data["create_user"] = current_user.user_id
        review_create_ser = ProductReviewCreateSer(data=raw_data)
        if not review_create_ser.is_valid():
            raise FieldsError(error_value=review_create_ser.errors)

        instance = review_create_ser.save()
        return instance, review_create_ser

    def _basic_review(self, raw_data, product: Product, current_user: User) -> ProductReview:
        """
        基础信息审核
        :param raw_data:
        :param product:
        :param current_user:
        :return:
        """
        if product.state != 0:
            raise APIViewException(err_message="商品未在待审核状态")
        product.productreviewprocess_set.update(become_history=True)

        post_data = raw_data
        is_pass = post_data.get("is_pass")
        if is_pass not in [True, False]:
            raise APIViewException(err_message="invalid params")
        create_data = {
            "physical_inventory_exact": True if is_pass else False,
            "quality_qualified": True,
            "price_reasonable": True,
            "remark": post_data.get("remark", ""),
        }
        with transaction.atomic():
            process = ProductReviewProcess.objects.create(product=product)
            instance, ser = self._create_review(process, create_data, product, current_user)
            # 默认审核不通过
            product_state = 2
            if instance.verified_pass:
                # 如果全部通过, 到第二步 待核价状态
                product_state = 4
                if instance.recommended_price:
                    # 写入历史价
                    self._insert_sku_retail_price(product, instance.recommended_price)

            # 更新商品状态
            product.state = product_state
            # product.state_reason = [ser.data]
            product.save(update_fields=("state",))
        return instance

    def _price_review(self, raw_data, product: Product, current_user: User) -> ProductReview:
        if product.state != 4:
            raise APIViewException(err_message="商品未在待核价状态")

        post_data = raw_data
        is_pass = post_data.get("is_pass")
        if is_pass not in [True, False]:
            raise APIViewException(err_message="invalid params")
        create_data = {
            "process_level": "PRICE_REVIEW",
            "physical_inventory_exact": True,
            "quality_qualified": True,
            "price_reasonable": True if is_pass else False,
            "remark": post_data.get("remark", ""),
            "recommended_price": post_data.get("recommended_price"),
            "review_cost_price": post_data.get("review_cost_price"),
        }
        with transaction.atomic():
            process = product.productreviewprocess_set.filter(become_history=False).last()
            if not process:
                raise APIViewException(err_message="商品审核流程错误,请联系管理员")

            price_review_record = process.productreview_set.filter(process_level="PRICE_REVIEW").last()
            if price_review_record:
                create_data["review_times"] = price_review_record.review_times + 1

            instance, ser = self._create_review(process, create_data, product, current_user)
            # 默认审核不通过
            product_state = 2
            if instance.verified_pass:
                # 如果复核,直接上架
                if instance.review_times > 1 and process.productreview_set.filter(process_level="QA_REVIEW").exists():
                    product_state = 1
                else:
                    # 如果全部通过, 到第三步 待质检状态
                    product_state = 5

                    # 如果是自营供应商的商品，自动通过
                    if product.company.is_self_support:
                        ProductReview(
                            process=process,
                            product=product,
                            process_level="QA_REVIEW",
                            physical_inventory_exact=True,
                            quality_qualified=True,
                            price_reasonable=True,
                            remark="自营商品系统自动通过质检审核",
                            create_user="system",
                        ).save()
                        # 直接上架
                        product_state = 1
                        # 推送到多维表格
                        insert_zl_self_support_new_product_to_feishu_table.delay(product.product_id)

                if instance.recommended_price:
                    # 写入历史价
                    self._insert_sku_retail_price(product, instance.recommended_price)

                # 写入成本价通过审核的历史表
                skus = product.stockkeepingunit_set.filter(become_history=False)
                need_create_reviewed_cost_price_objs = []
                for sku in skus:
                    last_reviewed_cost_price = sku.productreviewedcostprice_set.last()
                    old_value = last_reviewed_cost_price.cost_price if last_reviewed_cost_price else None
                    need_create_reviewed_cost_price_objs.append(
                        ProductReviewedCostPrice(
                            product=product,
                            sku=sku,
                            old_cost_price=old_value,
                            cost_price=sku.cost_price,
                            create_user=current_user.user_id,
                            create_date=instance.create_date,
                        )
                    )
                ProductReviewedCostPrice.objects.bulk_create(need_create_reviewed_cost_price_objs)

            # 更新商品状态
            product.state = product_state
            product.price_review_date = instance.create_date
            # origin_state_reason = product.state_reason
            # origin_state_reason.append(ser.data)
            # product.state_reason = origin_state_reason
            product.save(update_fields=("state", "price_review_date"))
        return instance

    def _qa_review(self, raw_data, product: Product, current_user: User) -> ProductReview:
        """
        质检审核
        :param raw_data:
        :param product:
        :param current_user:
        :return:
        """
        if product.state != 5:
            raise APIViewException(err_message="商品未在待质检状态")

        post_data = raw_data
        is_pass = post_data.get("is_pass")
        if is_pass not in [True, False]:
            raise APIViewException(err_message="审核结果错误")

        total_num = post_data.get("total_num")
        pass_num = post_data.get("pass_num")
        qa_content = post_data.get("qa_content")

        if total_num is None or pass_num is None:
            raise APIViewException(err_message="质检数量或合格数量不能为空")
        if total_num <= 0:
            raise APIViewException(err_message="质检数量必须大于0")
        if pass_num < 0:
            raise APIViewException(err_message="合格数量不能小于0")
        if pass_num > total_num:
            raise APIViewException(err_message="合格数量不能大于质检数量")

        create_data = {
            "process_level": "QA_REVIEW",
            "physical_inventory_exact": True,
            "quality_qualified": True if is_pass else False,
            "price_reasonable": True,
            "remark": post_data.get("remark", ""),
            "recommended_price": post_data.get("recommended_price"),
            "review_cost_price": post_data.get("review_cost_price"),
        }

        with transaction.atomic():
            process = product.productreviewprocess_set.filter(become_history=False).last()
            if not process:
                raise APIViewException(err_message="商品审核流程错误,请联系管理员")

            instance, ser = self._create_review(process, create_data, product, current_user)
            # 默认审核不通过
            product_state = 2
            if instance.verified_pass:
                # 如果全部通过, 上架状态
                product_state = 1
                # 插入多维表格
                if product.pic_score is not None and float(product.pic_score) > 0.7:
                    insert_zl_new_product_to_feishu_table.delay(product.product_id)
                    send_pic_score_greate_than_70_notify.delay(product.product_id)

                if instance.recommended_price:
                    # 写入历史价
                    self._insert_sku_retail_price(product, instance.recommended_price)
            else:
                # 质检信息填写
                batch_name = "普通质检"

                comment_list = []
                for i in qa_content or []:
                    if i.get("is_fine") is False:
                        type_name = i.get("type_name", "") or ""
                        if not type_name:
                            continue

                        question_name = i.get("question_name")
                        if not question_name:
                            continue

                        remark = i.get("question_remark", "") or ""
                        qa_names = "".join([str(i) for i in question_name if i != "质检通过，证书待补充"])

                        comment_list.append(f"{type_name}:{qa_names}")

                comment_text = ";".join(comment_list)
                ts = int(time.time() * 1000)
                hash_value = md5(f"{product.product_id}{batch_name}{comment_text}{ts}".encode()).hexdigest()
                ProductComments(
                    product=product,
                    batch_name=batch_name,
                    comment=comment_text,
                    total_num=total_num,
                    unqualified_num=total_num - pass_num,
                    hash_value=hash_value,
                    create_user=current_user.user_id,
                ).save()

            # 更新商品状态
            product.state = product_state
            product.save(update_fields=("state",))

            # 添加额外信息
            ProductReviewExtra.objects.create(
                review=instance,
                total_num=total_num,
                pass_num=pass_num,
                qa_content=qa_content,
            )

        return instance

    def post(self, request: Request):
        """
        发布商品
        """
        try:
            raw_data = request.data

            assert raw_data.get("skus") and raw_data.get("company_id"), "required: skus, company_id"
            company_id = raw_data.pop("company_id")
            try:
                company = Company.objects.get(company_id=company_id)
            except Company.DoesNotExist:
                return IResponse(code=400, message="company_id invalid")
            raw_data["company"] = company.id

            # 创建人
            raw_data["create_user"] = self.current_user.user_id
            skus_data = raw_data.pop("skus", [])
            attr_options_data = raw_data.pop("attr_options", [])
            gifts = raw_data.pop("gifts", {})
            if gifts:
                raw_data["has_gift"], raw_data["gift_type"] = gifts.get("has_gift"), gifts.get("gift_type")

            if raw_data.get("address_id"):
                raw_data["address"] = raw_data.pop("address_id")
            company_code = company.code

            physical_inventory = skus_data[0].get("physical_inventory")
            cost_price = skus_data[0].get("cost_price")
            # 先校验再创建
            specs_list = raw_data.pop("spec_lists", [])

            # 防止列表的空图片
            if "main_images" in raw_data:
                raw_data["main_images"] = [i for i in (raw_data["main_images"] or []) if i]

            # 指定分销商可见
            visible_distributor = list(set(raw_data.pop("visible_distributor", []) or []))

            if not isinstance(visible_distributor, list):
                raise APIViewException(err_message="分销商参数类型错误")

            if visible_distributor:
                if not self.current_user.visible_distributor:
                    raise APIViewException(err_message="当前用户暂无权限设置指定分销商可见")

                current_user_visible_distributor = [str(i) for i in self.current_user.visible_distributor]
                for i in visible_distributor:
                    if str(i) not in current_user_visible_distributor:
                        raise APIViewException(err_message="分销商信息错误，请刷新页面后重新")

                distributors_count = Distributor.objects.filter(distributor_id__in=visible_distributor).count()
                if len(visible_distributor) != distributors_count:
                    raise APIViewException(err_message="分销商信息错误，请刷新页面后重试")

            with transaction.atomic():
                clean_specs(specs_list, skus_data, request)
                # product
                product_instance = common_create_product(raw_data, specs_list, request, ProductSerializer)
                # sku && spec options
                sku_data, skus_images = common_create_sku_and_spec_options(product_instance, skus_data, request, company_code)
                # product attr options
                attr_options_instance = common_create_product_attr_options(product_instance, attr_options_data)
                # product gift
                gift_type, gifts_instances = common_create_gift(product_instance, gifts, company.id)

                address_id = raw_data.get("address")
                if address_id:
                    address_obj = ProductAddress.objects.filter(id=address_id).first()
                    if address_obj.name == "北京":
                        self._basic_review({"process_level": "BASIC_REVIEW", "is_pass": True, "remark": "系统自动通过"}, product_instance, self.current_user)
                        self._price_review({"process_level": "PRICE_REVIEW", "is_pass": True, "remark": "系统自动通过"}, product_instance, self.current_user)
                        self._qa_review(
                            {
                                "process_level": "QA_REVIEW",
                                "total_num": physical_inventory,
                                "pass_num": physical_inventory,
                                "is_pass": True,
                                "qa_content": [
                                    {"type_id": 1, "type_name": "主石品质", "is_fine": True, "question_name": []},
                                    {"type_id": 2, "type_name": "工艺质量", "is_fine": True, "question_name": []},
                                ],
                                "remark": "系统自动通过",
                            },
                            product_instance,
                            self.current_user,
                        )

                ProductConfig.objects.create(product=product_instance, visible_distributor=visible_distributor)

            # 上传图片至qdrant
            images = deepcopy(product_instance.main_images)
            if skus_images:
                images.extend(skus_images)
            add_images_to_qdrant.delay(product_instance.id, list(set(images)))

            resp_data = ProductSerializer(instance=product_instance, context={"request": self.request}).data
            category = resp_data.get("category")
            category_list = get_category_name_by_id(category)
            resp_data["category"] = category_list

            resp_data.pop("company")
            resp_data["skus"] = sku_data
            attr_options_data = ProductAttrOptionInfoSer(instance=attr_options_instance, many=True).data
            resp_data["attr_options"] = attr_options_data

            # 赠品信息
            if gift_type and gift_type == "AC":
                resp_data["gifts"] = ProductGiftSer(instance=gifts_instances).data
                resp_data["gifts"].pop("gift_product")
                resp_data["gifts"]["gift_products"] = []
            elif gift_type and gift_type == "PO":
                gift_product_list = [d["gift_product"] for d in ProductGiftSer(instance=gifts_instances, many=True).data]
                resp_data["gifts"] = {
                    "has_gift": product_instance.has_gift,
                    "gift_type": "PO",
                    "name": None,
                    "material": None,
                    "image": None,
                    "gift_products": gift_product_list,
                }
            else:
                resp_data["gifts"] = {
                    "has_gift": product_instance.has_gift,
                }

            # 异步视频切片
            video_split_jpg.delay(raw_data.get("video_3D"), product_instance.product_id)
            # 数据同步至聚水潭
            sync_product_to_JST.delay(product_instance.product_id)

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=str(product_instance.product_id),
                    model=Product,
                    describe=f"新增了商品: {product_instance}",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(data=resp_data)
        except IntegrityError as e:
            logger.error(str(e))
            return IResponse(message=str(e), code=500)
        except Exception as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=400)

    def get(self, request: Request):
        """
        获取商品列表
        商品导出需要统一使用筛选

        """
        try:
            normal_sku_qs = StockKeepingUnit.objects.filter(become_history=False)
            target = Product.get_operator_products(request).prefetch_related(
                "handcard",
                "address",
                "brand",
                "company",
                "owner",
                Prefetch("stockkeepingunit_set", queryset=normal_sku_qs),
            )

            # ai模式ticket
            ticket = request._request.headers.get("Ticket")
            if ticket:
                new_ticket_record = self.current_user.aisearchrecords_set.filter(ticket=ticket, input_platform=self.current_user_type).last()
                if not new_ticket_record:
                    return IResponse(data=EmptyListResponse)

                if not new_ticket_record.result:
                    return IResponse(data=EmptyListResponse)

                target = target.filter(product_id__in=new_ticket_record.result)

            re_data, page_products, obj_qs = custom_django_filter(
                request,
                target,
                OPProductListFilterSet,
                need_serialize=False,
            )

            # 查询托管状态
            product_ids = [product_obj.product_id for product_obj in page_products]
            hosting_product_map = bulk_query_product_hosting_state(product_ids)

            labels_map = bulk_query_labels(product_ids)

            # 批量查询锁库存状态
            p_confirm_states = bulk_query_confirm_state(product_ids)

            skus = [sku for product_obj in page_products for sku in product_obj.stockkeepingunit_set.all()]
            # 批零差查询sku的规格
            skus_map = bulk_query_skus_specs_detail(skus)

            latest_history_map = bulk_query_latest_history_price(skus)

            # 批量查询tags
            extra_tags_map = ProductBulkExtraTags(page_products)

            for product_obj in page_products:
                product = OperateProductListSerializer(
                    instance=product_obj,
                    context={
                        "request": self.request,
                        "labels_map": labels_map,
                        "extra_tags_map": extra_tags_map,
                    },
                )
                data = product.data
                category = data.get("category")
                category_list = get_category_name_by_id(category)
                data["category"] = category_list

                # 添加商品确认，确认状态为1时不允许修改库存数据
                data["product_confirm_state"] = p_confirm_states.get(product_obj.product_id)
                # 添加商品关联的货号信息
                data["origin_product_id"] = product_obj.origin_product_id

                sku_qs = product_obj.stockkeepingunit_set.all()

                skus = OperateStockKeepingUnitProductListSerializer(
                    instance=sku_qs,
                    many=True,
                    context={
                        "request": self.request,
                        "skus_map": skus_map,
                        "latest_history_map": latest_history_map,
                    },
                )
                data["skus"] = skus.data

                # 添加状态
                data["is_new"] = product_obj.is_new
                data["hosting_state"] = hosting_product_map.get(product_obj.product_id)

                re_data["data"].append(data)
            return IResponse(data=re_data)
        except FieldError as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=400)
        except Exception as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=500)


class OPCombineProductView(OPAPIView):
    # 组合商品
    def post(self, request: Request):
        post_data = request.data
        product_instance = create_combine_product(post_data, self.current_user, self.current_user_type)
        return IResponse(data={"product_id": product_instance.product_id})


class OPCombineProductDetailView(OPAPIView):
    @staticmethod
    def get_object(product_id) -> Product:
        try:
            product_obj = Product.objects.get(product_id=product_id, is_deleted=False, is_combine=True)
        except Product.DoesNotExist:
            raise APIViewException(err_message="data not found")

        return product_obj

    def get(self, request: Request, product_id: int):
        product_obj = self.get_object(product_id)
        data = CombineProductDetailSer(instance=product_obj, many=False, context={"request": request}).data
        return IResponse(data=data)

    def put(self, request, product_id: int):
        post_data = request.data
        product_obj = self.get_object(product_id)
        product_instance = update_combine_product(post_data, product_obj, self.current_user)
        return IResponse(data={"product_id": product_instance.product_id})


class OperatorProductDetailView(OPAPIView):
    """
    运营商商品详情
    """

    resource_id_field = "product_id"
    resource_name = "商品"
    fronted_page = "商品列表"

    permission_classes = [IsAuthenticated]

    @staticmethod
    def get_obj(product_id) -> Product | None:
        try:
            return Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return

    @staticmethod
    def init_check(company, resource_object):
        """
        Check resources and resource owners
        """
        if resource_object is None:
            raise ValueError("data not found")
        if company.id != resource_object.company.id:
            raise ValueError("no permission to operate")

    def get(self, request, product_id):
        """
        获取商品详情
        """
        try:
            product_obj = self.get_obj(product_id)
            if not product_obj:
                return IResponse(code=400, message="data not found")
            sku_list = []

            skus_qs = StockKeepingUnit.objects.filter(product=product_obj, become_history=False)
            for sku_qs in skus_qs:
                skus_data = OperateStockKeepingUnitProductDetailSerializer(instance=sku_qs, context={"request": self.request}).data
                history_price_data = []
                author_create_date = (
                    HistoryPrice.objects.filter(sku_id=sku_qs.id)
                    .values("author_id")
                    .annotate(effective_author=Coalesce("author_id", Value("")), max_create_date=Max("create_date"))
                    .values("effective_author", "max_create_date")
                )

                for i in author_create_date:
                    filters = {
                        "author_id": i["effective_author"] if i.get("effective_author") else None,
                        "create_date": i.get("max_create_date"),
                        "sku_id": sku_qs.id,
                    }
                    history_price = HistoryPrice.objects.filter(**filters).first()
                    if history_price:
                        history_price_data.append(
                            {
                                "history_price": history_price.history_price,
                                "history_price_author_id": history_price.author.author_id if history_price.author else None,
                                "history_price_author_name": history_price.author.name if history_price.author else None,
                                "create_date": history_price.create_date,
                            }
                        )
                    else:
                        logger.warning(f"history_price not found: sku:{sku_qs.sku_id, i.get('effective_author'), i.get('max_create_date')}")

                skus_data["history_price_data"] = history_price_data
                sku_list.append(skus_data)
            product = OperateProductDetailSerializer(instance=product_obj, context={"request": self.request})

            data = product.data
            data["skus"] = sku_list
            category = data.get("category")
            category_list = get_category_name_by_id(category)
            data["category"] = category_list
            # 添加商品确认，确认状态为1时不允许修改库存数据
            data["product_confirm_state"] = product_obj.product_confirm_state

            attr_options = product_obj.productattroption_set.select_related("attr", "attr_value")
            data["attr_options"] = ProductAttrOptionInfoSer(instance=attr_options, many=True).data

            # 是否需要审核价格
            need_review_price = True
            if ProductReviewPrice.objects.filter(product=product_obj.product_id).exists():
                need_review_price = False
            else:
                advice_price_obj = ProductAdvicePrice.objects.all().order_by("-create_date").first()
                if advice_price_obj and advice_price_obj.advice_price <= product_obj.min_cost_price:
                    need_review_price = False

            data["need_review_price"] = need_review_price

            # 商品关联分销商货号列表
            distributor_links = product_obj.productlinkdistributor_set.filter(is_deleted=False)
            distributor_links_ser = ProductLinkDistributorInfoSer(instance=distributor_links, many=True)
            code_data = distributor_links_ser.data
            data["distributor_links_info"] = sorted_distributor_info(code_data)
            # 商品状态
            data["is_new"] = product_obj.is_new
            data["hosting_state"] = product_obj.hosting_state

            return IResponse(data=data)
        except Exception as e:
            logger.error(f"op product detail: {str(e)}-->{traceback.format_exc()}")
            return IResponse(code=400, message=str(e))

    def patch(self, request, product_id: int):
        """
        更新商品
        """
        try:
            current_user = request.user
            raw_data = request.data
            if raw_data.get("product_id"):
                assert product_id == raw_data.get("product_id"), "The path product_id is inconsistent with the body product_id"

            product_obj = self.get_obj(product_id)
            if not product_obj:
                return IResponse(code=400, message="data not found")

            raw_object = copy.deepcopy(product_obj)
            if raw_data.get("company_id"):
                if product_obj.company.company_id != raw_data["company_id"] and product_obj.data_source == "SP":
                    return IResponse(code=400, message="can not change supplier source data")
                company = Company.objects.filter(company_id=raw_data["company_id"]).first()
                if not company:
                    return IResponse(code=400, message="company not found")
                raw_data["company_id"] = company.id
            else:
                company = product_obj.company

            if raw_data.get("brand"):
                raw_data["brand_id"] = raw_data.pop("brand")

            if raw_data.get("unit"):
                raw_data["unit_id"] = raw_data.pop("unit")

            company_code = company.code

            # 不允许更改的字段
            cannot_update_fields = ["physical_inventory", "safety_inventory", "can_use_inventory"]
            if len(raw_data.keys()) != 1:
                cannot_update_fields.append("state")

            for field in cannot_update_fields:
                if raw_data.get(field):
                    raw_data.pop(field)

            # 防止列表的空图片
            if "main_images" in raw_data:
                raw_data["main_images"] = [i for i in (raw_data["main_images"] or []) if i]

            # 获取原始图片链接
            raw_images = deepcopy(product_obj.main_images)

            # 发现新图片链接
            new_images = deepcopy(raw_data.get("main_images", []))
            if len(new_images) > 15:
                return IResponse(code=400, message="主图不能超过15张")

            skus_data, attr_options_data = None, None
            if raw_data.get("skus") is not None:
                skus_data = raw_data.pop("skus")
            if raw_data.get("attr_options") is not None:
                attr_options_data = raw_data.pop("attr_options")

            # 赠品
            gifts = raw_data.pop("gifts") if raw_data.get("gifts") else {}
            if gifts:
                raw_data["has_gift"], raw_data["gift_type"] = gifts.get("has_gift"), gifts.get("gift_type")

            # 标签处理
            labels = None
            if raw_data.get("labels") is not None:
                labels = raw_data.pop("labels")

            # 指定分销商可见， 提前获取，防止被处理
            visible_distributor = list(set(raw_data.pop("visible_distributor", []) or []))

            # update product
            # exclude
            raw_data["update_user"] = current_user.user_id
            raw_data["update_date"] = timezone.now()
            product_model_fields = [f.name for f in product_obj._meta.fields if f.name != "company"]
            product_model_fields.append("company_id")
            product_model_fields.append("brand_id")
            product_model_fields.append("address_id")
            product_model_fields.append("unit_id")
            raw_data = {key: value for key, value in raw_data.items() if key in product_model_fields}

            # 校验分类
            category = raw_data.get("category")
            validate_category(category)

            # 校验specs_list和specs数据是否一致
            specs_list = raw_data.get("spec_lists") or []
            product_obj.__dict__.update(**raw_data)

            update_fields = list(raw_data.keys())
            resp_data = {}
            sku_change_content = []
            attr_option_change_content = []
            sku_add_imgs = []
            sku_del_imgs = []

            if not isinstance(visible_distributor, list):
                raise APIViewException(err_message="分销商参数类型错误")

            visible_distributor_log_content = ""
            distributor_name_list = []

            if visible_distributor:
                distributor_name_list = Distributor.objects.filter(distributor_id__in=visible_distributor).values_list("name", flat=True)
                if len(visible_distributor) != len(distributor_name_list):
                    raise APIViewException(err_message="分销商信息错误，请刷新页面后重试")

            with transaction.atomic():
                clean_specs(specs_list, skus_data, request)
                if labels is not None:
                    origin_label_id_list, new_label_ids = clean_update_labels(labels, product_obj)

                # update sku and specs
                if skus_data is not None:
                    sku_data, sku_add_imgs, sku_del_imgs, sku_change_content, reset_preview_state = op_db_update_sku_and_spec_options(product_obj, skus_data, request, company_code)
                    if reset_preview_state == 1:
                        update_fields.append("state")
                        product_obj.state = 0

                        # 审核流程变历史数据
                        product_obj.productreviewprocess_set.update(become_history=True)
                        SKUCostPriceReviewRecord.objects.filter(product_id=product_obj.product_id, state=0).update(state=3, review_remark="商品修改，该审批已过去")
                    elif reset_preview_state == 2:
                        update_fields.append("state")
                        product_obj.state = 4

                        # 核价流程变历史数据
                        product_obj.productreview_set.filter(process__become_history=False, process_level="PRICE_REVIEW").update(become_history=True)
                        SKUCostPriceReviewRecord.objects.filter(product_id=product_obj.product_id, state=0).update(state=3, review_remark="商品修改，该审批已过去")
                    elif product_obj.state == 2:
                        # 审核不通过任意修改触发三级审核
                        update_fields.append("state")
                        product_obj.state = 0

                        # 审核流程变历史数据
                        product_obj.productreviewprocess_set.update(become_history=True)
                        SKUCostPriceReviewRecord.objects.filter(product_id=product_obj.product_id, state=0).update(state=3, review_remark="商品修改，该审批已过去")
                    resp_data["skus"] = sku_data
                # attr options
                if attr_options_data is not None:
                    attr_options_data, attr_option_change_content = common_update_product_attr_options(product_obj, attr_options_data)
                    resp_data["attr_options"] = attr_options_data

                # 更新赠品
                common_update_gift(product_obj, gifts, company.id)

                # 保留原始数据
                product_obj.spec_lists = specs_list
                product_obj.save(update_fields=set(update_fields))

                # 更新config配置
                try:
                    product_config = ProductConfig.objects.get(product=product_obj)
                    product_config.visible_distributor = visible_distributor
                    product_config.save()

                    if not product_config.visible_distributor:
                        visible_distributor_log_content = "新增特定分销商可见：{}".format("、".join(distributor_name_list))
                    else:
                        old_distributo_name_list = Distributor.objects.filter(distributor_id__in=product_config.visible_distributor).values_list("name", flat=True)
                        visible_distributor_log_content = "修改特定分销商可见：{}改为{}".format(
                            "、".join(old_distributo_name_list),
                            "、".join(distributor_name_list),
                        )

                except ProductConfig.DoesNotExist:
                    ProductConfig.objects.create(product=product_obj, visible_distributor=visible_distributor)
                    if distributor_name_list:
                        visible_distributor_log_content = "新增特定分销商可见：{}".format("、".join(distributor_name_list))

                # 刷新数据
                product_obj.refresh_from_db()

                # 修改商品specs_list
                product_specs_update_handler(product_obj, specs_list)

                if labels is not None:
                    if origin_label_id_list:
                        product_obj.labels_relate.remove(*origin_label_id_list)
                    for label_id in new_label_ids:
                        product_obj.labels_relate.add(label_id)

                # 更新副本
                sync_to_sub_products(raw_object, product_obj)

            if product_obj.is_in_distributor_market:
                # 刷新分销价格
                configs = DistributorMarketProductPriceConfig.objects.filter(is_deleted=False)
                for config in configs:
                    # flush_products_market_price.delay(config.min_cost_price, config.max_cost_price, config.markup_percentage, [product_obj.pk])
                    flush_products_distributor_market_price.delay(config.min_cost_price, config.max_cost_price, config.markup_percentage, [product_obj.pk])

            # 有新增的图片，上传至qdrant
            images = list(set(new_images) - set(raw_images))
            if sku_add_imgs:
                images.extend(sku_add_imgs)
            if images:
                add_images_to_qdrant.delay(product_obj.id, list(set(images)))

            # 被删除的图片，需要从qdrant中删除
            del_imags = list(set(raw_images) - set(new_images))
            if sku_del_imgs:
                del_imags.extend(sku_del_imgs)
            for d_img in del_imags:
                delete_images_from_qdrant.delay(product_obj.id, raw_data=d_img)

            product_data = ProductSerializer(instance=product_obj, context={"request": self.request}).data
            category = product_data.get("category")
            category_list = get_category_name_by_id(category)
            product_data["category"] = category_list
            resp_data.update(product_data)
            resp_data.pop("company")

            # 异步视频切片
            if raw_object.video_3D != raw_data.get("video_3D"):
                video_split_jpg.delay(raw_data.get("video_3D"), product_id)

            # 数据同步至聚水潭
            sync_product_to_JST.delay(product_obj.product_id)
            # 数据同步到erp
            sync_product_info_to_erp_product.delay(product_obj.product_id)

            # 日志记录
            try:
                operate_content = "更新内容: "

                _product_change_content = diff_models(
                    raw_object,
                    product_obj,
                    private_field=[
                        "physical_inventory",
                        "can_use_inventory",
                        "safety_inventory",
                        "min_cost_price",
                        "max_cost_price",
                        "min_retail_price",
                        "max_retail_price",
                        "min_history_price",
                        "max_history_price",
                        "spec_lists",
                    ],
                )
                if _product_change_content:
                    product_change_content = "\n商品: " + _product_change_content
                    operate_content += product_change_content

                if sku_change_content:
                    operate_content += "\nSKU: " + "、".join(sku_change_content)

                if attr_option_change_content:
                    operate_content += "\n属性: " + "、".join(attr_option_change_content)

                if visible_distributor_log_content:
                    operate_content += f"\n{visible_distributor_log_content}"

                set_log_params(
                    request,
                    resource_id=str(product_id),
                    model=Product,
                    describe=f"修改了商品:{product_obj}",
                    operate_content=operate_content,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}.{traceback.format_exc()}")
                pass

            return IResponse(data=resp_data)
        except IntegrityError as e:
            logger.error(f"{e}{traceback.format_exc()}")
            return IResponse(message=str(e), code=500)
        except Exception as e:
            logger.error(f"{e}{traceback.format_exc()}")
            return IResponse(message=str(e), code=400)

    def delete(self, request, product_id):
        """
        删除商品
        """
        try:
            product = self.get_obj(product_id)
            if not product:
                return IResponse(code=400, message="data not found")

            product.is_deleted = True
            product.save()

            # 删除向量数据库数据
            delete_images_from_qdrant.delay(product.id)
            # 数据同步至聚水潭
            sync_product_to_JST.delay(product.product_id)
            # 日志记录
            sku_list = StockKeepingUnit.objects.filter(product_id=product.id).values("sku_id")
            ShoppingCart.objects.filter(sku__in=sku_list).delete()

            try:
                set_log_params(
                    request,
                    resource_id=product_id,
                    model=Product,
                    describe=f"删除了商品:{product}",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse()
        except Exception as e:
            logger.error(str(e))
            return IResponse(message=str(e), code=400)

    def put(self, request, product_id):
        """修改规格顺序"""
        skus = request.data.get("skus", [])
        sku_map = {i["sku_id"]: i["order"] for i in skus}
        sku_objs = StockKeepingUnit.objects.filter(
            product__product_id=product_id,
            sku_id__in=list(sku_map.keys()),
            become_history=False,
            product__is_deleted=False,
        )
        if sku_objs.count() != len(skus):
            raise APIViewException(err_message="规格信息错误,请重新排序")
        for sku in sku_objs:
            sku.order = sku_map[sku.sku_id]
            sku.save()
        return IResponse()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def selected_product_view(request):
    """
    选品商品池
    """
    try:
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")

        raw_params = request.query_params.copy()

        # 前端参数转换
        if raw_params.get("product_ids"):
            raw_params["product_id__in"] = raw_params.pop("product_ids")[0]

        product_list_params_handler(
            raw_params,
            exclude_fields=[
                "spec_name",
                "spec_value",
                "live_date",
                "distributor_id",
            ],
        )

        filters = dict(
            is_deleted=False,
        )
        page_products, re_data, obj_qs = custom_filter(
            raw_params,
            Product,
            array_fields=["category"],
            like_fields=["name", "product_id", "code", "productlinkdistributor__code"],
            hybrid_fields=["name", "product_id", "__code", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
            **filters,
        )

        for product_obj in page_products:
            product = OperateSelectedProductListSerializer(instance=product_obj, context={"request": request})
            data = product.data
            category = data.get("category")
            if not category:
                data["category"] = []
            else:
                category_list = get_category_name_by_id(category)
                data["category"] = category_list
            re_data["data"].append(data)
        return IResponse(data=re_data)
    except FieldError as e:
        logger.error(f"{e}, {traceback.format_exc()}")
        return IResponse(message=str(e), code=400)
    except Exception as e:
        logger.error(f"{e}, {traceback.format_exc()}")
        return IResponse(message=str(e), code=500)


class OPProductDetailNextView(OPAPIView):
    def get(self, request, product_id):
        """
        支持点击“下一个”的商品详情页
        """
        try:
            # 当前商品
            product_obj = Product.objects.filter(product_id=product_id, is_deleted=False).first()
            if not product_obj:
                return IResponse(code=400, message="data not found")
            # 获取下一个商品主键
            raw_params = request.query_params.copy()

            # 数据来源 from_text=distributor_market
            from_text = ""
            if "from" in raw_params:
                from_text = raw_params.pop("from")[0]

            and_Q = None
            specs_data = []
            # 前端参数转换
            product_list_params_handler(raw_params)
            if specs_data:
                and_Q = reduce(and_, specs_data)

            filters = dict(is_deleted=False, id__lt=product_obj.id)
            page_products, re_data, obj_qs = custom_filter(
                raw_params,
                Product,
                array_fields=["category"],
                like_fields=["name", "product_id", "code", "remark", "productlinkdistributor__code"],
                hybrid_fields=["name", "product_id", "__code", "remark", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
                query_Q=and_Q,
                **filters,
            )
            next_product = obj_qs.first()
            # 商品详情数据
            sku_list = []
            skus_qs = StockKeepingUnit.objects.filter(product=product_obj, become_history=False)
            for sku_qs in skus_qs:
                skus_data = OperateStockKeepingUnitProductDetailSerializer(
                    instance=sku_qs,
                    context={
                        "request": request,
                        "from_text": from_text,
                    },
                ).data
                history_price_data = []
                author_create_date = (
                    HistoryPrice.objects.filter(sku_id=sku_qs.id)
                    .values("author_id")
                    .annotate(effective_author=Coalesce("author_id", Value("")), max_create_date=Max("create_date"))
                    .values("effective_author", "max_create_date")
                )

                for i in author_create_date:
                    filters = {
                        "author_id": i["effective_author"] if i.get("effective_author") else None,
                        "create_date": i.get("max_create_date"),
                        "sku_id": sku_qs.id,
                    }
                    history_price = HistoryPrice.objects.filter(**filters).first()
                    if history_price:
                        history_price_data.append(
                            {
                                "history_price": history_price.history_price,
                                "history_price_author_id": history_price.author.author_id if history_price.author else None,
                                "history_price_author_name": history_price.author.name if history_price.author else None,
                                "create_date": history_price.create_date,
                            }
                        )
                    else:
                        logger.warning(f"history_price not found: sku:{sku_qs.sku_id, i.get('effective_author'), i.get('max_create_date')}")

                skus_data["history_price_data"] = history_price_data
                sku_list.append(skus_data)

            product = OperateProductDetailSerializer(
                instance=product_obj,
                context={
                    "request": request,
                    "from_text": from_text,
                },
            )
            data = product.data
            category = data.get("category")
            category_list = get_category_name_by_id(category)
            data["category"] = category_list

            # 添加商品确认，确认状态为1时不允许修改库存数据
            data["product_confirm_state"] = product_obj.product_confirm_state

            data["skus"] = sku_list

            attr_options_qs = ProductAttrOption.objects.filter(product=product_obj)
            attr_options = ProductAttrOptionInfoSer(instance=attr_options_qs, many=True).data
            data["attr_options"] = attr_options

            # 商品关联分销商货号列表
            distributor_links = product_obj.productlinkdistributor_set.filter(
                is_deleted=False,
            )
            distributor_links_ser = ProductLinkDistributorInfoSer(instance=distributor_links, many=True)
            code_data = distributor_links_ser.data
            data["distributor_links_info"] = sorted_distributor_info(code_data)

            if next_product:
                data["next_product"] = next_product.product_id
            else:
                data["next_product"] = None
            return IResponse(data=data)
        except Exception as e:
            logger.error(str(e))
            return IResponse(code=400, message=str(e))


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@operate_log_decorator("完成抖店链接", "商品详情")
def confirm_doudian_link_view(request, product_id):
    """
    已完成抖店链接
    """
    current_user = request.user
    if request.auth.get("user_type") != "OP":
        return IResponse(code=403, message="no permission to operate")
    # 当前商品
    product_obj = Product.objects.filter(product_id=product_id, is_deleted=False).first()
    if not product_obj:
        return IResponse(code=400, message="data not found")
    if product_obj.has_DouDian_link:
        return IResponse(code=400, message="no need to operate on this resource")

    has_DouDian_link = request.data.get("has_DouDian_link")
    assert has_DouDian_link is not None, "has_DouDian_link required"
    # 更新
    product_obj.has_DouDian_link = has_DouDian_link
    product_obj.confirm_DouDian_link_user = current_user.user_id
    product_obj.update_date = timezone.now()
    product_obj.save(
        update_fields=(
            "has_DouDian_link",
            "confirm_DouDian_link_user",
            "update_date",
        )
    )
    # 日志记录
    try:
        set_log_params(
            request,
            resource_id=product_id,
            model=Product,
            describe=f"完成抖店链接.商品:{product_obj}",
            is_success_input=True,
        )
    except Exception as e:
        logger.warning(f"set_log_params catch error: {e}")
        pass

    return IResponse(code=200)


@operate_log_decorator("修改抖店ID", "后端系统")
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def update_doudian_id_view(request, spec_code):
    raw_data = request.data
    doudian_id = raw_data.get("doudian_id")
    assert doudian_id, "doudian_id required"
    # 不更新update_date
    Product.objects.filter(
        stockkeepingunit__spec_code=spec_code,
        is_deleted=False,
    ).update(doudian_id=doudian_id)

    # 日志记录
    try:
        set_log_params(
            request,
            resource_id=spec_code,
            model=StockKeepingUnit,
            describe=f'SKU商品编码"{spec_code}"商品修改了抖店ID:"{doudian_id}"',
            is_success_input=True,
        )
    except Exception as e:
        logger.warning(f"set_log_params catch error: {e}")
        pass

    return IResponse()


def get_category_name_by_id(category_list):
    """
    获取商品分类列表名字
    """
    assert isinstance(category_list, list), ValueError("category_list must be list")
    re_data = []

    for category_id in category_list:
        cache_key = f"category_{category_id}"
        data = cache.get(cache_key)
        if data is None:
            cate_qs = ProductCategoryList.objects.filter(id=category_id).first()
            if cate_qs:
                data = ProductCategoryListSerializer(instance=cate_qs).data
                re_data.append(data)
                cache.set(cache_key, data, timeout=60 * 60)
        else:
            re_data.append(data)
    return re_data


class OperatorDownloadProductListView(OPAPIView):
    """
    运营商下载商品列表文件
    """

    fronted_page = "商品列表"
    resource_name = "导出商品数据"
    need_format_resource_name = False

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            re_data = []
            current_user = request.user
            raw_params = request.query_params.copy()
            and_Q = None
            specs_data = []
            # 前端参数转换
            # product_ids 为选品下载专用
            if raw_params.get("product_ids"):
                raw_params["product_id__in"] = raw_params.pop("product_ids")[0]
            normal_sku_qs = StockKeepingUnit.objects.filter(become_history=False)
            target = Product.get_operator_products(request).prefetch_related(
                "handcard",
                "address",
                "brand",
                "company",
                "owner",
                Prefetch("stockkeepingunit_set", queryset=normal_sku_qs),
            )

            # ai模式ticket
            ticket = request._request.headers.get("Ticket")
            if ticket:
                new_ticket_record = self.current_user.aisearchrecords_set.filter(ticket=ticket, input_platform=self.current_user_type).last()
                if not new_ticket_record:
                    return IResponse(data=EmptyListResponse)

                if not new_ticket_record.result:
                    return IResponse(data=EmptyListResponse)

                target = target.filter(product_id__in=new_ticket_record.result)

            _, page_products, _ = custom_django_filter(
                request,
                target,
                OPProductListFilterSet,
                need_serialize=False,
            )
            num = 0
            # 查询所属地区
            address_ids = {product.address_id for product in page_products}
            address_objs = ProductAddress.objects.filter(id__in=address_ids)
            address_map = {address.id: address.name for address in address_objs}

            log_product_info = []

            for product_qs in page_products:
                re_product = {}
                product_data = ProductSerializer(instance=product_qs, context={"request": self.request}).data
                # template data
                for k, v in OperatorDownloadProductTmpl.items():
                    value = product_data.get(k)
                    re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value

                    if k == "address":
                        re_product[v] = address_map.get(product_qs.address_id, "")
                # 商品参数下载
                attr_download_info_handler(re_product, product_qs)

                # category
                category = product_data.get("category")
                category_id_and_name_list = get_category_name_by_id(category_list=category)
                name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目"}
                for i in range(len(category_id_and_name_list)):
                    re_product[f"{name_key[i]}ID"] = category_id_and_name_list[i].get("id")
                    re_product[f"{name_key[i]}名称"] = category_id_and_name_list[i].get("name")
                    if i == 3:
                        break
                # brand
                re_product["区域ID"], re_product["区域名称"] = None, None
                brand = product_data.get("brand")
                if brand:
                    brand_obj = Brand.objects.get(id=brand)
                    re_product["区域ID"], re_product["区域名称"] = brand_obj.id, brand_obj.name

                # 关联货号
                links_qs = ProductLinkDistributor.objects.prefetch_related("distributor").filter(product_id=product_qs.product_id, is_deleted=False)
                data_list = [f"{link.distributor.name}:{link.code}" for link in links_qs]
                data_str = ""
                if data_list:
                    data_str = ";".join(data_list)
                re_product["关联货号"] = data_str

                # sku
                skus_qs = StockKeepingUnit.objects.filter(product=product_qs, become_history=False)
                for sku_qs in skus_qs:
                    num += 1
                    re_sku = {
                        "序号": num,
                    }
                    re_sku.update(re_product)
                    re_sku["规格ID"] = sku_qs.sku_id
                    # spec
                    spec_option_list = get_sku_download_specs_option_list(sku_qs)
                    re_sku["规格"] = ";".join(spec_option_list)
                    re_sku["建议售价"] = sku_qs.retail_price

                    # 链接编码
                    re_sku["链接编码"] = sku_qs.link_code
                    # 销量
                    re_sku["销量"] = sku_qs.sales
                    # 需要权限
                    if current_user.has_perm("products.view_cost_price_StockKeepingUnit"):
                        re_sku["推广价"] = sku_qs.cost_price
                    else:
                        re_sku["推广价"] = "***"
                    re_sku["现货库存"] = sku_qs.physical_inventory
                    re_sku["7天补货库存"] = sku_qs.safety_inventory
                    re_sku["是否已播"] = "是" if sku_qs.has_live else "否"

                    history_price = None
                    history_price_author_id = None
                    history_price_author_name = None
                    latest = HistoryPrice.objects.filter(sku_id=sku_qs.id).order_by("-create_date").first()
                    if latest:
                        history_price = latest.history_price
                        if latest.author:
                            history_price_author_id = latest.author.author_id
                            history_price_author_name = latest.author.name
                    re_sku["历史价"] = history_price
                    re_sku["历史价主播id"] = history_price_author_id
                    re_sku["历史价主播名"] = history_price_author_name
                    re_data.append(re_sku)

                    # 记录操作日志
                log_product_info.append(f"商品:{product_qs}")

            filename = f"商品列表{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
            picture_fields = {"主图列表": 2}
            byte_buffer = get_excel_async("商品", re_data, image_columns=[3], is_internal=True)
            if byte_buffer:
                byte_buffer.seek(0)

            # 日志记录
            try:
                operate_content = "导出了商品: " + "、".join(log_product_info) if log_product_info else ""
                set_log_params(
                    request,
                    model=Product,
                    describe="导出了商品",
                    operate_content=operate_content,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return FileResponse(byte_buffer, filename=filename, as_attachment=True)
        except Exception as e:
            logger.info(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def get_excel(self, sheet_name, data):
        byte_buffer = BytesIO()
        df = pd.DataFrame.from_dict(data)
        writer = pd.ExcelWriter(byte_buffer, engine="xlsxwriter")
        df.to_excel(writer, sheet_name=sheet_name, index=False)
        worksheet = writer.sheets[sheet_name]
        for idx, col in enumerate(df):
            length = len(col) * 2 + 1
            worksheet.set_column(idx, idx, length)
        writer.close()
        return byte_buffer


class HistoryPriceView(CommonAPIView):
    permission_classes = [IsAuthenticated]
    fronted_page = "后端系统"
    resource_name = "历史价"
    need_format_resource_name = True

    def get(self, request, product_id):
        """
        product_id: 商品列表传的是product_id, 我的商品传的是parent_product_id
        拉取历史价列表
        """
        try:
            condition = {
                "product_id": product_id,
                "is_deleted": False,
            }

            if self.current_user_type == "SP":
                condition["company_id"] = self.current_user.company_id

            product = Product.objects.get(**condition)
        except Product.DoesNotExist:
            return IResponse(code=400, message="data not found")

        # 复制query
        raw_params: dict = request.query_params.copy()

        filters = {}
        # 获取传递过来的author id列表，如果为空，默认查询全部
        author_id_list = raw_params.pop("author_id", [])
        if author_id_list:
            filters["author_id"] = author_id_list[0]

        # 获取传递过来的sku name，如果为空，默认查询全部
        # 根据sku spec select 接口的响应结果进行筛选
        spec_name = raw_params.pop("spec_name", "")

        # sku_qs = product.stockkeepingunit_set.filter(become_history=False).only("id")
        sku_qs = product.stockkeepingunit_set.all().only("id")

        if spec_name:
            spec_values_list = spec_name[0].split(",")
            sku_qs = sku_qs.filter(specs_value__value__in=spec_values_list)

        # 如果没有匹配的sku信息为None查询
        filters["sku_id__in"] = json.dumps([sku.id for sku in sku_qs] or [None])

        if not raw_params.get("orders"):
            filters["orders"] = json.dumps(["-create_date"])
        raw_params.update(**filters)

        # 结果查询
        page_history_prices, re_data, obj_qs = custom_filter(
            raw_params,
            HistoryPrice,
        )
        history_price_data = HistoryPriceSerializer(instance=page_history_prices, many=True).data
        temp = {}
        for data in history_price_data:
            create_date = data.pop("create_date")
            if create_date not in temp:
                temp[create_date] = []
            temp[create_date].append(data)
        result = []
        for k, v in temp.items():
            result.append({"create_date": k, "data": v})

        re_data["data"] = result
        return IResponse(data=re_data)

    def post(self, request: Request, product_id):
        """
        product_id: 商品列表传的是product_id, 我的商品传的是parent_product_id
        新增历史价
        [
            {
            "sku_id": 12345678,
            "history_price": 15.98,
            "author_id": 1038355500373918
            }
        ]
        """
        try:
            # 是否从我的商品提交
            is_sub = request.query_params.get("is_sub")

            raw_data = request.data
            current_user = request.user

            if request.auth.get("user_type") not in ["OP", "DB"]:
                return IResponse(code=403, message="no permission to operate")

            sub_product_id = ""

            for item in raw_data:
                item["create_user"] = current_user.user_id

                # 校验参数
                if item.get("author_id"):
                    author_id = item.get("author_id")
                    if not isinstance(author_id, (int, str)):
                        return IResponse(code=400, message="invalid params")
                    if not LiveAuthor.objects.filter(author_id=author_id).exists():
                        return IResponse(code=400, message=f"主播{author_id}不存在")

                # 运营商逻辑
                if request.auth.get("user_type") == "OP":
                    if current_user.live_author and not item.get("author_id"):
                        item["author_id"] = current_user.live_author.author_id
                # 判断时候为分销商用户
                if request.auth.get("user_type") == "DB":
                    current_user_distributor = current_user.distributor
                    if not current_user_distributor:
                        return IResponse(code=403, message="user not bind distributor")

                    if not item.get("author_id") and not current_user_distributor.live_author:
                        return IResponse(code=400, message="distributor not bind live author")

                    item["author_id"] = item.get("author_id", current_user_distributor.live_author_id)

                    # 判断有没有副商品
                    if is_sub == "1":
                        try:
                            sub_product = SubProduct.objects.get(parent_product__product_id=product_id, owner_id=current_user_distributor.distributor_id)
                            sub_product_id = sub_product.product_id
                        except SubProduct.DoesNotExist:
                            pass

            insert_history_price(
                product_id,
                raw_data,
                sub_product_id=sub_product_id,
            )

            # 日志记录
            try:
                operate_content = [f"SKU_ID: {item['sku_id']},  历史价格:{item['history_price']}, 主播ID: {item['author_id']}" for item in raw_data]
                set_log_params(
                    request,
                    resource_id=product_id,
                    model=Product,
                    describe=f"商品: {product_id} 新增了历史价",
                    operate_content="、".join(operate_content),
                    content=raw_data,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}")
            return IResponse(code=500, message=str(e))


def insert_history_price(product_id, raw_data, **kwargs):
    """
    插入历史价
    """
    try:
        product = Product.objects.get(product_id=product_id, is_deleted=False)
    except Product.DoesNotExist:
        raise ValueError("data not found")

    sku_ids = {}
    for item in raw_data:
        sku_id = item.get("sku_id")
        history_price = item.get("history_price")
        if not sku_id or history_price is None:
            continue
        sku_ids[sku_id] = item
    # 去除已经成历史的
    normal = []
    skus_qs = StockKeepingUnit.objects.filter(sku_id__in=sku_ids.keys(), become_history=False)
    for sku_qs in skus_qs:
        if sku_ids.get(sku_qs.sku_id):
            normal.append(sku_qs.sku_id)
            # 获取原始sku id
            sku_ids[sku_qs.sku_id]["sku_id"] = sku_qs.id

    not_in = set(sku_ids.keys()) - set(normal)
    for sku_id in not_in:
        sku_ids.pop(sku_id)
    sku_raw_id_list = []
    for k, v in sku_ids.items():
        sku_raw_id_list.append(v["sku_id"])

    # 找到最新的历史价记录进行对比
    # latest_prices = HistoryPrice.objects.filter(product=product, sku_id__in=sku_raw_id_list).values("sku_id").annotate(latest_create_date=Max("create_date"))
    # for price in latest_prices:
    #     latest = HistoryPrice.objects.filter(sku_id=price["sku_id"], create_date=price["latest_create_date"]).first()
    #     item = sku_ids.get(latest.sku.sku_id)
    #     if item and item["history_price"] == float(latest.history_price):
    #         if (item.get("author_id") and latest.author and str(item["author_id"]) == latest.author.author_id) or (not item.get("author_id") and not latest.author):
    #             sku_ids.pop(latest.sku.sku_id)

    sub_product_id = kwargs.get("sub_product_id")

    create_date = update_date = timezone.now()
    for k, v in sku_ids.items():
        data = {
            "product_id": product.id,
            "sku_id": v["sku_id"],
            "author_id": v.get("author_id"),
            "create_date": create_date,
            "history_price": v["history_price"],
            "update_date": update_date,
            "create_user": v.get("create_user"),
        }
        # 我的商品 - 添加
        if sub_product_id:
            data["sub_product_id"] = sub_product_id

        HistoryPrice.objects.create(**data)

    # 获取当前最新有效sku的历史价，计算最大和最小值，更新到商品中
    calculate_history_price(product_id)


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
@operate_log_decorator("删除历史价", fronted_page="后端系统")
def delete_price_history_view(request, product_id, create_date):
    """
    product_id在商品列表是product_id, 在我的商品传来的是parent_product_id
    删除历史价
    """
    try:
        current_user = request.user
        is_sub = request.GET.get("is_sub")

        if request.auth.get("user_type") not in ["OP", "DB"]:
            return IResponse(code=403, message="no permission to operate")

        if request.auth.get("user_type") == "DB" and not current_user.distributor:
            return IResponse(code=400, message="user not bind distributor")

        try:
            query_condition = {
                "product_id": product_id,
                "is_deleted": False,
            }

            product = Product.objects.get(**query_condition)
        except Product.DoesNotExist:
            return IResponse(code=400, message="data not found")

        # 判断能否删除
        if request.auth.get("user_type") == "DB":
            if is_sub == "1":
                if not SubProduct.objects.filter(parent_product__product_id=product_id, owner__id=current_user.distributor_id).exists():
                    return IResponse(code=400, message=f"副商品{product_id}不存在")

        sku_qs = product.stockkeepingunit_set.filter(become_history=False).values("id")
        sku_ids = [sku["id"] for sku in sku_qs]
        his_price_qs = HistoryPrice.objects.filter(sku_id__in=sku_ids, create_date=create_date)
        raw_data = HistoryPriceSerializer(instance=his_price_qs, many=True).data
        his_price_qs.delete()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=product_id,
                model=Product,
                describe=f"删除历史价,商品ID:{product_id}",
                original_content=raw_data,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        # 检查剩余历史价并更新商品对应值
        calculate_history_price(product_id)

        return IResponse(code=200)
    except Exception as e:
        return IResponse(code=500, message=str(e))


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def latest_price_history_view(request, product_id):
    """
    获取最新sku历史价
    """
    try:
        current_user = request.user
        if request.auth.get("user_type") not in ["OP", "DB"]:
            return IResponse(code=403, message="no permission to operate")

        try:
            product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="data not found")

        sku_qs = product.stockkeepingunit_set.filter(become_history=False).values("id")
        sku_ids = [sku["id"] for sku in sku_qs]

        latest_prices = HistoryPrice.objects.filter(sku_id__in=sku_ids).values("sku_id").annotate(latest_create_date=Max("create_date"))
        latest_data = []
        for price in latest_prices:
            latest_price = HistoryPrice.objects.filter(sku_id=price["sku_id"], create_date=price["latest_create_date"]).first()
            his_price_date = HistoryPriceSerializer(instance=latest_price).data
            latest_data.append(his_price_date)

        return IResponse(code=200, data=latest_data)
    except Exception as e:
        return IResponse(code=500, message=str(e))


class ProductReviewView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "商品列表"
    resource_name = "商品审核"

    @staticmethod
    def _insert_sku_retail_price(product: Product, price):
        skus_qs = product.stockkeepingunit_set.filter(become_history=False).only("sku_id", "retail_price")
        # 多个sku 全部retail_price为空的时候填入建议零售价
        retail_prices = [sku.retail_price for sku in skus_qs if sku.retail_price]
        if not retail_prices:
            skus_qs.update(retail_price=price)

    @staticmethod
    def _create_review(process: ProductReviewProcess, raw_data, product, current_user):
        raw_data["process"] = process.process_id
        raw_data["product"] = product.id
        raw_data["create_user"] = current_user.user_id
        review_create_ser = ProductReviewCreateSer(data=raw_data)
        if not review_create_ser.is_valid():
            raise FieldsError(error_value=review_create_ser.errors)

        instance = review_create_ser.save()
        return instance, review_create_ser

    def _basic_review(self, raw_data, product: Product, current_user: User) -> ProductReview:
        """
        基础信息审核
        :param raw_data:
        :param product:
        :param current_user:
        :return:
        """
        if product.state != 0:
            raise APIViewException(err_message="商品未在待审核状态")
        product.productreviewprocess_set.update(become_history=True)

        post_data = raw_data
        is_pass = post_data.get("is_pass")
        if is_pass not in [True, False]:
            raise APIViewException(err_message="invalid params")
        create_data = {
            "physical_inventory_exact": True if is_pass else False,
            "quality_qualified": True,
            "price_reasonable": True,
            "remark": post_data.get("remark", ""),
        }
        with transaction.atomic():
            process = ProductReviewProcess.objects.create(product=product)
            instance, ser = self._create_review(process, create_data, product, current_user)
            # 默认审核不通过
            product_state = 2
            if instance.verified_pass:
                # 如果全部通过, 到第二步 待核价状态
                product_state = 4
                if instance.recommended_price:
                    # 写入历史价
                    self._insert_sku_retail_price(product, instance.recommended_price)

            # 更新商品状态
            product.state = product_state
            # product.state_reason = [ser.data]
            product.save(update_fields=("state",))
        return instance

    def _price_review(self, raw_data, product: Product, current_user: User) -> ProductReview:
        if product.state != 4:
            raise APIViewException(err_message="商品未在待核价状态")

        post_data = raw_data
        is_pass = post_data.get("is_pass")
        if is_pass not in [True, False]:
            raise APIViewException(err_message="invalid params")
        create_data = {
            "process_level": "PRICE_REVIEW",
            "physical_inventory_exact": True,
            "quality_qualified": True,
            "price_reasonable": True if is_pass else False,
            "remark": post_data.get("remark", ""),
            "recommended_price": post_data.get("recommended_price"),
            "review_cost_price": post_data.get("review_cost_price"),
        }
        with transaction.atomic():
            process = product.productreviewprocess_set.filter(become_history=False).last()
            if not process:
                raise APIViewException(err_message="商品审核流程错误,请联系管理员")

            price_review_record = process.productreview_set.filter(process_level="PRICE_REVIEW").last()
            if price_review_record:
                create_data["review_times"] = price_review_record.review_times + 1

            instance, ser = self._create_review(process, create_data, product, current_user)
            # 默认审核不通过
            product_state = 2
            if instance.verified_pass:
                # 如果复核,直接上架
                if instance.review_times > 1 and process.productreview_set.filter(process_level="QA_REVIEW").exists():
                    product_state = 1
                else:
                    # 如果全部通过, 到第三步 待质检状态
                    product_state = 5

                    # 如果是自营供应商的商品，自动通过
                    if product.company.is_self_support:
                        ProductReview(
                            process=process,
                            product=product,
                            process_level="QA_REVIEW",
                            physical_inventory_exact=True,
                            quality_qualified=True,
                            price_reasonable=True,
                            remark="自营商品系统自动通过质检审核",
                            create_user="system",
                        ).save()
                        # 直接上架
                        product_state = 1
                        # 推送到多维表格
                        insert_zl_self_support_new_product_to_feishu_table.delay(product.product_id)

                if instance.recommended_price:
                    # 写入历史价
                    self._insert_sku_retail_price(product, instance.recommended_price)

                # 写入成本价通过审核的历史表
                skus = product.stockkeepingunit_set.filter(become_history=False)
                need_create_reviewed_cost_price_objs = []
                for sku in skus:
                    last_reviewed_cost_price = sku.productreviewedcostprice_set.last()
                    old_value = last_reviewed_cost_price.cost_price if last_reviewed_cost_price else None
                    need_create_reviewed_cost_price_objs.append(
                        ProductReviewedCostPrice(
                            product=product,
                            sku=sku,
                            old_cost_price=old_value,
                            cost_price=sku.cost_price,
                            create_user=current_user.user_id,
                            create_date=instance.create_date,
                        )
                    )
                ProductReviewedCostPrice.objects.bulk_create(need_create_reviewed_cost_price_objs)

            # 更新商品状态
            product.state = product_state
            product.price_review_date = instance.create_date
            # origin_state_reason = product.state_reason
            # origin_state_reason.append(ser.data)
            # product.state_reason = origin_state_reason
            product.save(update_fields=("state", "price_review_date"))
        return instance

    def _qa_review(self, raw_data, product: Product, current_user: User) -> ProductReview:
        """
        质检审核
        :param raw_data:
        :param product:
        :param current_user:
        :return:
        """
        # if product.state != 5:
        #     raise APIViewException(err_message="商品未在待质检状态")

        post_data = raw_data
        is_pass = post_data.get("is_pass")
        if is_pass not in [True, False]:
            raise APIViewException(err_message="审核结果错误")

        total_num = post_data.get("total_num")
        pass_num = post_data.get("pass_num")
        qa_content = post_data.get("qa_content")

        if total_num is None or pass_num is None:
            raise APIViewException(err_message="质检数量或合格数量不能为空")
        if total_num <= 0:
            raise APIViewException(err_message="质检数量必须大于0")
        if pass_num < 0:
            raise APIViewException(err_message="合格数量不能小于0")
        if pass_num > total_num:
            raise APIViewException(err_message="合格数量不能大于质检数量")

        create_data = {
            "process_level": "QA_REVIEW",
            "physical_inventory_exact": True,
            "quality_qualified": True if is_pass else False,
            "price_reasonable": True,
            "remark": post_data.get("remark", ""),
            "recommended_price": post_data.get("recommended_price"),
            "review_cost_price": post_data.get("review_cost_price"),
        }

        with transaction.atomic():
            process = product.productreviewprocess_set.filter(become_history=False).last()
            if not process:
                raise APIViewException(err_message="商品审核流程错误,请联系管理员")

            instance, ser = self._create_review(process, create_data, product, current_user)
            # 默认审核不通过
            product_state = 2
            if instance.verified_pass:
                # 如果全部通过, 上架状态
                product_state = 1
                # 插入多维表格
                if product.pic_score is not None and float(product.pic_score) > 0.7:
                    insert_zl_new_product_to_feishu_table.delay(product.product_id)
                    send_pic_score_greate_than_70_notify.delay(product.product_id)

                if instance.recommended_price:
                    # 写入历史价
                    self._insert_sku_retail_price(product, instance.recommended_price)
            else:
                # 质检信息填写
                batch_name = "普通质检"

                comment_list = []
                for i in qa_content or []:
                    if i.get("is_fine") is False:
                        type_name = i.get("type_name", "") or ""
                        if not type_name:
                            continue

                        question_name = i.get("question_name")
                        if not question_name:
                            continue

                        qa_names = "".join([str(i) for i in question_name if i != "质检通过，证书待补充"])

                        comment_list.append(f"{type_name}:{qa_names}")

                comment_text = ";".join(comment_list)
                ts = int(time.time() * 1000)
                hash_value = md5(f"{product.product_id}{batch_name}{comment_text}{ts}".encode()).hexdigest()
                ProductComments(
                    product=product,
                    batch_name=batch_name,
                    comment=comment_text,
                    total_num=total_num,
                    unqualified_num=total_num - pass_num,
                    hash_value=hash_value,
                    create_user=current_user.user_id,
                ).save()

            # 更新商品状态
            product.state = product_state
            product.save(update_fields=("state",))

            # 添加额外信息
            ProductReviewExtra.objects.create(
                review=instance,
                total_num=total_num,
                pass_num=pass_num,
                qa_content=qa_content,
            )

        return instance

    def get(self, request, product_id):
        """
        获取审核信息
        """
        re_data = get_product_review_info(request, product_id, self.current_user_type)
        return IResponse(data=re_data)

    def post(self, request, product_id):
        """
        商品审核
        """
        try:
            current_user = request.user
            if request.auth.get("user_type") != "OP":
                return IResponse(code=403, message="no permission to operate")
            raw_data = request.data
            process_level = raw_data.get("process_level") or None
            if process_level not in [i[0] for i in ProductReview._meta.get_field("process_level").choices]:
                return IResponse(code=400, message="invalid params")
            # 判断是否有审核权限
            if not current_user.has_permission(f"review:{process_level}", self.current_user_type):
                return IResponse(code=403, message="no permission to operate")

            try:
                product = Product.objects.get(product_id=product_id, is_deleted=False)
            except Product.DoesNotExist:
                return IResponse(code=400, message="data not found")

            # 如果商品上架或者下架不允许审核了
            # if product.state in [1, 3]:
            #     return IResponse(code=400, message="no need to operate on this resource")

            # if product.state == 2:
            #     return IResponse(code=400, message="审核不通过, 请修改商品后重新提审")

            # 创建价格变动通知
            notifications_data = {
                "product_id": product_id,
                "company_id": product.company.company_id,
                "notify_type": "review",
            }

            if process_level == "BASIC_REVIEW":
                instance = self._basic_review(raw_data, product, current_user)
                log_desc = "基础审核: 通过" if instance.verified_pass else "基础审核: 不通过"
            elif process_level == "PRICE_REVIEW":
                instance = self._price_review(raw_data, product, current_user)
                log_desc = "核价审核: 通过" if instance.verified_pass else "核价审核: 不通过"
            elif process_level == "QA_REVIEW":
                instance = self._qa_review(raw_data, product, current_user)
                log_desc = "质量审核: 通过" if instance.verified_pass else "质量审核: 不通过"
                send_feishu_data = FeishuQAProductReviewSerializer(instance)
                # 发送卡片和记录多维表格
                insert_qa_product_review_table.delay(send_feishu_data.data)
            else:
                raise ValueError("method not implement.")

            # 创建消息通知
            if not instance.verified_pass:
                notifications_data["operation_type"] = "review_rejected"
            else:
                notifications_data["operation_type"] = "review_approved"
            notifications_data["content"] = f"商品[{product_id}]：{log_desc}"
            ceate_notifications.delay(notifications_data, ["SP"])

            # 不加入货盘,已取消该逻辑

            # 日志记录
            try:
                if instance.review_times != 1:
                    log_desc = f"第{instance.review_times}次{log_desc}"
                set_log_params(
                    request,
                    resource_id=product_id,
                    model=Product,
                    describe=f"{log_desc}. 商品:{product}",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse()
        except APIViewException as e:
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


def get_raw_product_id_by_picture(raw_params):
    image_url = raw_params.get("image_url")
    image_limit = raw_params.get("qdrant_count")
    url_pattern = re.compile(r"^(https?|ftp)://[^\s/$.?#].[^\s]*$")
    assert image_url, "image_url required"
    assert re.match(url_pattern, image_url), "image_url invalid"

    # 剔除必要参数，余下参数用于商品列表
    raw_params.pop("image_url")
    if image_limit:
        raw_params.pop("qdrant_count")
    else:
        image_limit = 30

    # 搜图
    logger.info(f"limit:{image_limit}-image_url:{image_url}")
    scored_points = search_image(database_id=settings.QDRANT_DATABASE_ID, image_url=image_url, limit=image_limit)
    object_ids = set()
    for point in scored_points:
        object_ids.add(point.payload.get("object_id"))
    return object_ids


def find_product_by_picture(request, raw_params):
    """
    以图搜产品
    珠玑搜相似也复用此函数
    """
    re_data = {}
    object_ids = get_raw_product_id_by_picture(raw_params)
    logger.info(f"object_ids: {object_ids}")
    if not object_ids:
        re_data["count"] = 0
        re_data["total_pages"] = 1
        re_data["current_page"] = 1
        re_data["data"] = []
        return re_data

    query_params = {
        "pk__in": json.dumps(list(object_ids)),
        "is_deleted": "False",
        "company__is_self_support": "False",
        "is_combine": "False",
    }
    raw_params.update(**query_params)
    # 根据id查询产品
    logger.info(f"raw_params: {raw_params}")
    page_products, re_data, obj_qs = custom_filter(raw_params, Product, array_fields=["category"], like_fields=["name", "product_id"])
    product_ids = [product_obj.product_id for product_obj in page_products]
    labels_map = bulk_query_labels(product_ids)
    for product_obj in page_products:
        product = OperateProductListSerializer(instance=product_obj, context={"request": request, "labels_map": labels_map})
        data = product.data
        category = data.get("category")
        category_list = get_category_name_by_id(category)
        data["category"] = category_list

        sku_qs = product_obj.stockkeepingunit_set.filter(become_history=False)
        skus = OperateStockKeepingUnitProductListSerializer(instance=sku_qs, many=True, context={"request": request})
        data["skus"] = skus.data
        re_data["data"].append(data)

    return re_data


def _download_product_handler(request, page_products):
    """
    获取导出商品数据所需字段

    Args:
        request (_type_): _description_
        page_products (_type_): QuerySet

    Returns:
        _type_: _description_
    """
    current_user = request.user
    re_data = []
    num = 0
    log_product_info = []
    for product_qs in page_products:
        re_product = {}
        product_data = ProductSerializer(instance=product_qs, context={"request": request}).data
        # tmeplate data
        for k, v in OperatorDownloadProductTmpl.items():
            value = product_data.get(k)
            re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value

        # 商品参数下载
        attr_download_info_handler(re_product, product_qs)

        # category
        category = product_data.get("category")
        category_id_and_name_list = get_category_name_by_id(category_list=category)
        name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目"}
        for i in range(len(category_id_and_name_list)):
            re_product[f"{name_key[i]}ID"] = category_id_and_name_list[i].get("id")
            re_product[f"{name_key[i]}名称"] = category_id_and_name_list[i].get("name")
            if i == 3:
                break

        re_product["所属地区"] = product_data.get("address_name")
        # brand
        re_product["区域ID"], re_product["区域名称"] = None, None
        brand = product_data.get("brand")
        if brand:
            brand_obj = Brand.objects.get(id=brand)
            re_product["区域ID"], re_product["区域名称"] = brand_obj.id, brand_obj.name

        # 关联货号
        links_qs = ProductLinkDistributor.objects.filter(product_id=product_qs.product_id, is_deleted=False)
        data_list = [f"{link.distributor.name}:{link.code}" for link in links_qs]
        data_str = ""
        if data_list:
            data_str = ";".join(data_list)
        re_product["关联货号"] = data_str

        # sku
        skus_qs = StockKeepingUnit.objects.filter(product=product_qs, become_history=False)
        for sku_qs in skus_qs:
            num += 1
            re_sku = {
                "序号": num,
            }
            re_sku.update(re_product)
            re_sku["规格ID"] = sku_qs.sku_id
            # spec
            spec_option_list = get_sku_download_specs_option_list(sku_qs)
            re_sku["规格"] = ";".join(spec_option_list)
            re_sku["建议售价"] = sku_qs.retail_price

            # 链接编码
            re_sku["链接编码"] = sku_qs.link_code
            # 销量
            re_sku["销量"] = sku_qs.sales
            # 需要权限
            if current_user.has_perm("products.view_cost_price_StockKeepingUnit"):
                re_sku["推广价"] = sku_qs.cost_price
            else:
                re_sku["推广价"] = "***"
            re_sku["现货库存"] = sku_qs.physical_inventory
            re_sku["7天补货库存"] = sku_qs.safety_inventory
            re_sku["是否已播"] = "是" if sku_qs.has_live else "否"

            history_price = None
            history_price_author_id = None
            history_price_author_name = None
            latest = HistoryPrice.objects.filter(sku_id=sku_qs.id).order_by("-create_date").first()
            if latest:
                history_price = latest.history_price
                if latest.author:
                    history_price_author_id = latest.author.author_id
                    history_price_author_name = latest.author.name
            re_sku["历史价"] = history_price
            re_sku["历史价主播id"] = history_price_author_id
            re_sku["历史价主播名"] = history_price_author_name
            re_data.append(re_sku)

        # 记录操作日志
        log_product_info.append(f"商品:{product_qs}")
    operate_content = "导出了商品: " + "、".join(log_product_info) if log_product_info else ""
    return re_data, operate_content


def _find_product_by_picture_download(current_user: User, raw_params):
    """
    以图搜产品，用于导出
    """
    re_data = {}
    image_url = raw_params.get("image_url")
    image_limit = raw_params.get("qdrant_count")
    url_pattern = re.compile(r"^(https?|ftp)://[^\s/$.?#].[^\s]*$")
    assert image_url, "image_url required"
    assert re.match(url_pattern, image_url), "image_url invalid"

    # 剔除必要参数，余下参数用于商品列表
    raw_params.pop("image_url")
    if image_limit:
        raw_params.pop("qdrant_count")
    else:
        image_limit = 30

    # 搜图
    logger.info(f"limit:{image_limit}-image_url:{image_url}")
    scored_points = search_image(database_id=settings.QDRANT_DATABASE_ID, image_url=image_url, limit=image_limit)
    logger.info(f"limit:{len(scored_points)}--{scored_points}")
    object_ids = set()
    for point in scored_points:
        object_ids.add(point.payload.get("object_id"))
    logger.info(f"object_ids: {object_ids}")
    if not object_ids:
        re_data["count"] = 0
        re_data["total_pages"] = 1
        re_data["current_page"] = 1
        re_data["data"] = []
        return IResponse(data=re_data)

    query_params = {
        "pk__in": json.dumps(list(object_ids)),
        "is_deleted": "False",
    }
    raw_params.update(**query_params)
    # 根据id查询产品
    logger.info(f"raw_params: {raw_params}")
    page_products, re_data, obj_qs = custom_filter(raw_params, Product, array_fields=["category"], like_fields=["name", "product_id"])
    return _download_product_handler(current_user, page_products)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def sku_physical_inventory_modify_view(request):
    """
    获取现货库存变动数据
    1.获取相应的变动日志数据
    2.根据日志返回的商品id获取商品数据

    获取以下场景商品信息：
        1.新增商品（新增库存）
        2.修改库存
        3.特定人新增（库存）
    """
    # 变动类型：新增/修改
    #
    raw_params = request.query_params.copy()
    log_params = {
        "resource_name__in": [
            "供应商商品SKU",
            "运营商商品SKU",
            "分销商商品SKU",
            "供应商商品SKU(修改时新增)",
            "运营商商品SKU(修改时新增)",
            "分销商商品SKU(修改时新增)",
        ],
    }
    if raw_params.get("modify_type"):
        modify_type = raw_params.pop("modify_type")[0]
        assert modify_type in ["POST", "DELETE", "PATCH", "GET"], "modify_type invalid"
        log_params["action_type"] = modify_type
    if raw_params.get("operation_user"):
        log_params["username"] = raw_params.pop("operation_user")[0]
    if raw_params.get("operation_time"):
        operation_time = json.loads(raw_params.pop("operation_time")[0])
        assert isinstance(operation_time, list) and len(operation_time) == 2, "operation_time must be a list and len equal 2"
        log_params["operation_time__range"] = operation_time

    product_ids = list(OperationLog.objects.filter(**log_params).values("parent_resource_id").distinct().values_list("parent_resource_id", flat=True))
    if not product_ids:
        re_data = {
            "count": 0,
            "total_pages": 0,
            "current_page": 1,
            "data": [],
        }
        return IResponse(data=re_data)
    new_params = {
        "product_id__in": json.dumps(product_ids),
    }
    raw_params.update(**new_params)
    page_products, re_data, obj_qs = custom_filter(raw_params, Product)

    for product_obj in page_products:
        sku_list = []
        skus_qs = StockKeepingUnit.objects.filter(product=product_obj, become_history=False)
        for sku_qs in skus_qs:
            skus_data = OperateStockKeepingUnitProductDetailSerializer(instance=sku_qs, context={"request": request}).data
            history_price_data = []
            author_create_date = (
                HistoryPrice.objects.filter(sku_id=sku_qs.id)
                .values("author_id")
                .annotate(effective_author=Coalesce("author_id", Value("")), max_create_date=Max("create_date"))
                .values("effective_author", "max_create_date")
            )
            for i in author_create_date:
                filters = {
                    "author_id": i["effective_author"] if i.get("effective_author") else None,
                    "create_date": i.get("max_create_date"),
                    "sku_id": sku_qs.id,
                }
                history_price = HistoryPrice.objects.filter(**filters).first()
                if history_price:
                    history_price_data.append(
                        {
                            "history_price": history_price.history_price,
                            "history_price_author_id": history_price.author.author_id if history_price.author else None,
                            "history_price_author_name": history_price.author.name if history_price.author else None,
                        }
                    )
                else:
                    logger.warning(f"history_price not found: sku:{sku_qs.sku_id, i.get('effective_author'), i.get('max_create_date')}")

            skus_data["history_price_data"] = history_price_data
            sku_list.append(skus_data)
        product = OperateProductDetailSerializer(instance=product_obj, context={"request": request})
        data = product.data

        category = data.get("category")
        category_list = get_category_name_by_id(category)
        data["category"] = category_list
        data["skus"] = sku_list

        attr_options_qs = ProductAttrOption.objects.filter(product=product_obj)
        attr_options = ProductAttrOptionSerializer(instance=attr_options_qs, many=True).data
        for attr_option in attr_options:
            attr_option["attr_id"] = attr_option.pop("attr")
            attr_option.pop("product")
        data["attr_options"] = attr_options
        re_data["data"].append(data)
    return IResponse(data=re_data)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def sku_physical_inventory_modify_log_view(request):
    """
    获取现货库存变动数据
    1.获取相应的变动日志数据
    2.根据日志返回的商品id获取商品数据

    获取以下场景商品信息：
        1.新增商品（新增库存）
        2.修改库存
        3.特定人新增（库存）
    + 1108 货号变动
    """
    # 变动类型：新增/修改
    #
    raw_params = request.query_params.copy()
    log_params = {
        "resource_name__in": [
            "供应商商品SKU",
            "运营商商品SKU",
            "分销商商品SKU",
            "供应商商品SKU(修改时新增)",
            "运营商商品SKU(修改时新增)",
            "分销商商品SKU(修改时新增)",
            "运营商商品货号修改",
            "供应商商品货号修改",
            "新增历史价",
            "运营商商品供应商修改",
        ],
    }

    new_log_params = {
        "content_type": ContentType.objects.get_for_model(Product),
    }
    if raw_params.get("operation_time"):
        operation_time = json.loads(raw_params.pop("operation_time")[0])
        log_params["operation_time__range"] = operation_time
        new_log_params["operation_time__range"] = operation_time

    if raw_params.get("action_types"):
        action_types = json.loads(raw_params.pop("action_types")[0])
        log_params["action_type__in"] = action_types
        new_log_params["action_type__in"] = action_types
    else:
        log_params["action_type__in"] = ["POST", "PATCH"]
        new_log_params["action_type__in"] = ["POST", "PATCH"]

    page_size = raw_params.pop("page_size")[0] if raw_params.get("page_size") else 20
    page = raw_params.pop("page")[0] if raw_params.get("page") else 1

    latest_operations = (
        OperationLog.objects.filter(Q(**log_params) | Q(**new_log_params))
        .values(
            "user_id",
            "username",
            "real_name",
            "action_type",
            obj_id=Case(
                When(parent_resource_id__isnull=True, then=F("resource_id")),
                default=F("parent_resource_id"),
                output_field=models.CharField(),
            ),
        )
        .annotate(operation_time=Max("operation_time"))
        .order_by(
            "-operation_time",
            "user_id",
            "username",
            "real_name",
            "action_type",
            "obj_id",
        )
    )
    paginator = Paginator(latest_operations, page_size)
    page_model_objects = paginator.page(page)

    re_data = {
        "count": page_model_objects.paginator.count,
        "total_pages": page_model_objects.paginator.num_pages,
        "current_page": page_model_objects.number,
        "data": [],
    }

    data = OperationLogSerializer(instance=page_model_objects, many=True).data
    re_data["data"].extend(data)
    return IResponse(data=re_data)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_product_for_feishu_view(request):
    """
    市场新选品飞书多维表格流程获取商品信息
    """
    try:
        re_data = {}
        current_user = request.user
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")

        raw_params = request.query_params.copy()

        and_Q = None
        specs_data = []
        # 前端参数转换
        product_list_params_handler(raw_params)

        if specs_data:
            and_Q = reduce(and_, specs_data)

        filters = dict(
            is_deleted=False,
        )

        distributor_links_prefetch = Prefetch("productlinkdistributor_set", ProductLinkDistributor.objects.filter(is_deleted=False))
        attroption_prefetch = Prefetch("productattroption_set", ProductAttrOption.objects.select_related("attr", "attr_value").all())
        sku_prefetch = Prefetch("stockkeepingunit_set", StockKeepingUnit.objects.filter(become_history=False))
        # 商品副本
        subproduct_prefetch = Prefetch("subproduct_set", SubProduct.objects.filter(alias_code_history__isnull=False).only("alias_code_history"))

        page_products, re_data, obj_qs = custom_filter(
            raw_params,
            Product.objects.prefetch_related(distributor_links_prefetch, attroption_prefetch, "address", "unit", "company", sku_prefetch, subproduct_prefetch),
            array_fields=["category"],
            like_fields=["name", "product_id", "code", "remark", "productlinkdistributor__code"],
            hybrid_fields=["name", "product_id", "__code", "remark", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
            query_Q=and_Q,
            **filters,
        )
        # sku的规则
        skus = [s for p in page_products for s in p.stockkeepingunit_set.all()]
        sku_spec_map = bulk_query_skus_specs_detail(skus)

        for product_obj in page_products:
            sku_list = []
            skus_qs = product_obj.stockkeepingunit_set.all()

            for sku_qs in skus_qs:
                skus_data = OperateStockKeepingUnitProductDetailSerializer(instance=sku_qs, context={"request": request, "sku_spec_map": sku_spec_map}).data
                history_price_data = []
                author_create_date = (
                    HistoryPrice.objects.filter(sku_id=sku_qs.id)
                    .values("author_id")
                    .annotate(effective_author=Coalesce("author_id", Value("")), max_create_date=Max("create_date"))
                    .values("effective_author", "max_create_date")
                )

                for i in author_create_date:
                    filters = {
                        "author_id": i["effective_author"] if i.get("effective_author") else None,
                        "create_date": i.get("max_create_date"),
                        "sku_id": sku_qs.id,
                    }
                    history_price = HistoryPrice.objects.filter(**filters).first()
                    if history_price:
                        history_price_data.append(
                            {
                                "history_price": history_price.history_price,
                                "history_price_author_id": history_price.author.author_id if history_price.author else None,
                                "history_price_author_name": history_price.author.name if history_price.author else None,
                                "create_date": history_price.create_date,
                            }
                        )
                    else:
                        logger.warning(f"history_price not found: sku:{sku_qs.sku_id, i.get('effective_author'), i.get('max_create_date')}")

                skus_data["history_price_data"] = history_price_data
                sku_list.append(skus_data)

            product = OperateProductDetailForFeishuSerializer(instance=product_obj, context={"request": request})
            data = product.data

            # 关联货号信息
            distributor_links = product_obj.productlinkdistributor_set.all()
            distributor_links_ser = ProductLinkDistributorInfoSer(instance=distributor_links, many=True)
            code_data = distributor_links_ser.data
            data["distributor_links_info"] = sorted_distributor_info(code_data)

            category = data.get("category")
            category_list = get_category_name_by_id(category)
            data["category"] = category_list
            data["skus"] = sku_list

            attr_options_qs = product_obj.productattroption_set.all()
            attr_options = ProductAttrOptionInfoSer(instance=attr_options_qs, many=True).data
            data["attr_options"] = attr_options

            sub_products = product_obj.subproduct_set.all()
            t80_code_list = []

            for sub_product in sub_products:
                for code_history in sub_product.alias_code_history:
                    t80_code_list.append(code_history["alias_code"])
            data["t80_code_list"] = t80_code_list
            re_data["data"].append(data)
        return IResponse(data=re_data)
    except FieldError as e:
        logger.error(f"{e}, {traceback.format_exc()}")
        return IResponse(message=str(e), code=400)
    except Exception as e:
        logger.error(f"{e}, {traceback.format_exc()}")
        return IResponse(message=str(e), code=500)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@operate_log_decorator("货盘复制", "货盘计划")
def op_copy_selection_plan_view(request, plan_id):
    """
    复制货盘计划
    """
    # 创建货盘计划
    raw_data = request.data
    current_user = request.user
    if request.auth.get("user_type") != "OP":
        return IResponse(code=403, message="no permission to operate")
    try:
        plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")

    if plan.state != 0:
        return IResponse(code=400, message="货盘计划尚未结束，无法进行复制")

    raw_data["create_user"] = current_user.user_id
    raw_data["update_user"] = current_user.user_id

    # live_author_id = raw_data.get("live_author_id")
    # assert live_author_id, "live_author_id required"

    # 获取分销商
    distributor_id = raw_data.get("distributor_id")
    if not distributor_id:
        return IResponse(code=400, message="缺少分销商参数")
    if not Distributor.objects.filter(distributor_id=str(distributor_id)).exists():
        return IResponse(code=400, message="分销商不存在")

    live_date_end = raw_data.get("live_date_end")
    if not live_date_end:
        return IResponse(code=400, message="缺少结束日期")

    if convert_to_ware_time(live_date_end, "%Y-%m-%d").date() < timezone.now().date():
        return IResponse(code=400, message="live_date_end should be a future date")

    raw_data["distributor"] = raw_data.pop("distributor_id")
    serializer = ProductSelectionPlanSerializer(data=raw_data)
    if not serializer.is_valid():
        raise FieldsError(serializer.errors)
    selection_plan = serializer.save()

    # 获取原货盘计划的商品创建item
    # 不需要过滤已播、未播
    user_id = current_user.user_id
    new_plan_id = selection_plan.plan_id
    create_user_type = request.auth.get("user_type")
    async_move_old_plan_product_to_new_plan_invoker.delay(plan_id, user_id, new_plan_id, create_user_type)

    # 日志记录
    try:
        set_log_params(
            request,
            resource_id=new_plan_id,
            model=ProductSelectionPlan,
            describe=f"复制货盘:{plan}",
            operate_content=f"复制货盘【{plan}】到新货盘【{selection_plan}】",
            is_success_input=True,
        )
    except Exception as e:
        logger.warning(f"set_log_params catch error: {e}")
        pass

    return IResponse(code=202)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def op_check_product_in_plan_view(request, plan_id):
    """
    用于前端检查商品是否在计划中
    """
    re_data = {}
    current_user = request.user
    if request.auth.get("user_type") != "OP":
        return IResponse(code=403, message="no permission to operate")

    raw_params = request.query_params.copy()
    # 前端参数转换
    if raw_params.get("name"):
        raw_params["name__icontains"] = raw_params.pop("name")[0]
    if raw_params.get("category"):
        raw_params["category"] = raw_params.pop("category")[0]
    if raw_params.get("physical_inventory"):
        raw_params["physical_inventory__range"] = raw_params.pop("physical_inventory")[0]
    if raw_params.get("cost_price"):
        raw_params["__cost_price__range"] = raw_params.pop("cost_price")[0]
    if raw_params.get("history_price"):
        raw_params["__history_price__range"] = raw_params.pop("history_price")[0]
    try:
        selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")

    item_qs = ProductSelectionItem.objects.filter(selection_plan_id=plan_id, is_deleted=False).values("product", "remark")
    data_dict = {}
    for item in item_qs:
        data_dict[item["product"]] = item.get("remark")
    product_ids = data_dict.keys()

    filters = dict(is_deleted=False, product_id__in=product_ids)

    # 获取商品
    page_products, re_data, plan_products_qs = custom_filter(
        raw_params,
        Product,
        array_fields=["category"],
        like_fields=["name", "product_id", "productlinkdistributor__code"],
        hybrid_fields=["name", "product_id", "__code", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
        **filters,
    )
    re_data = []
    for product in plan_products_qs:
        data = CheckProductInOperateSelectionPlanSerializer(instance=product, context={"request": request}).data
        re_data.append(data)
    return IResponse(data=re_data)


class OPSelectionPlanProductDownloadView(OPAPIView):
    def get(self, request: Request, plan_id: int):
        """
        货盘计划-商品列表下载
        """
        try:
            return new_selection_plan_download_fc(request, plan_id)
        except Exception as e:
            logger.error(f"catch exception in db plan list download,{e}")
            return IResponse(code=500, message=str(e))


class OPProductPlanBulkView(OPAPIView):
    need_format_resource_name = False
    resource_name = "批量处理商品"
    fronted_page = "货盘选品池"

    def post(self, request: Request, plan_id: int):
        """
        批量将商品加入货盘计划
        """
        post_data = request.data
        product_id_list = post_data.get("product_ids")
        if not product_id_list:
            raise APIViewException()

        if not isinstance(product_id_list, list):
            raise APIViewException(err_message="参数类型错误")

        estimated_sales = post_data.get("estimated_sales") or 0
        if not str(estimated_sales).isdigit():
            raise APIViewException()

        selection_reason = post_data.get("selection_reason") or ""

        # 先查货盘计划状态
        try:
            selection_plan = ProductSelectionPlan.objects.only("state", "name").get(plan_id=plan_id)
        except ProductSelectionPlan.DoesNotExist:
            raise APIViewException(err_message="data not found")

        if selection_plan.state in [0, 2]:
            raise APIViewException(err_message="adding products is not supported in this state")

        bulk_add_products_to_plan(
            request,
            self.current_user,
            plan_id,
            product_id_list,
            self.current_user_type,
            estimated_sales=estimated_sales,
            selection_reason=selection_reason,
            correct_sku_inventory=True,
        )
        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(plan_id),
                model=ProductSelectionPlan,
                describe=f"批量加入商品到货盘计划:{selection_plan.name}",
                operate_content="商品ID：{}".format("、".join(product_id_list)),
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def delete(self, request: Request, plan_id: int):
        """
        批量删除
        :param request:
        :param plan_id:
        :return:
        """
        return bulk_delete_selection_products_fc(request, plan_id)


def plan_state_checker(plan_id):
    """
    检查货盘状态
    """
    try:
        selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
    except ProductSelectionPlan.DoesNotExist:
        raise ValueError("data not found")
    if selection_plan.state in [0, 2]:
        raise ValueError("this state can't edit")


class OPPlanCompanyDetailView(APIView):
    """
    货盘计划公司管理
    """

    permission_classes = [IsAuthenticated]

    def patch(self, request):
        """
        货盘公司顺序移动
        raw_data = [
                {
                    "plan_company_id": 1,
                    "order": 2
                },
            ]

        """
        try:
            current_user = request.user
            if request.auth.get("user_type") != "OP":
                return IResponse(code=403, message="no permission to operate")
            raw_data = request.data
            try:
                with transaction.atomic():
                    for data in raw_data:
                        plan_company_id = data.get("plan_company_id")
                        order = data.get("order")
                        try:
                            plan_company = ProductSelectionPlanCompany.objects.get(id=plan_company_id, is_deleted=False)
                        except ProductSelectionPlanCompany.DoesNotExist:
                            raise ValueError("data not found")
                        plan = plan_company.selection_plan
                        # 先查货盘计划状态
                        plan_state_checker(plan.plan_id)
                        user_id = read_lock(plan.plan_id)
                        if current_user.user_id != user_id:
                            raise ValueError("Not granted editing permissions")
                        # 更新顺序
                        plan_company.order = order
                        plan_company.save(update_fields=["order"])
            except IntegrityError as e:
                logger.error(str(e))
                raise ValueError(str(e))

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class OPPlanCategoryDetailView(APIView):
    """
    货盘计划分类管理
    """

    permission_classes = [IsAuthenticated]

    def patch(self, request):
        """
        货盘分类顺序移动
        raw_data = [
                {
                    "plan_category_id": 1,
                    "order": 2
                },
            ]
        """

        try:
            current_user = request.user
            if request.auth.get("user_type") != "OP":
                return IResponse(code=403, message="no permission to operate")
            raw_data = request.data
            try:
                with transaction.atomic():
                    for data in raw_data:
                        plan_category_id = data.get("plan_category_id")
                        order = data.get("order")

                        try:
                            plan_category = ProductSelectionPlanCategory.objects.get(id=plan_category_id, is_deleted=False)
                        except ProductSelectionPlanCategory.DoesNotExist:
                            raise ValueError("data not found")
                        # 查计划
                        plan = plan_category.plan_company.selection_plan
                        # 先查货盘计划状态
                        plan_state_checker(plan.plan_id)
                        user_id = read_lock(plan.plan_id)
                        if current_user.user_id != user_id:
                            raise ValueError("Not granted editing permissions")
                        # 更新顺序
                        plan_category.order = order
                        plan_category.save(update_fields=["order"])
            except IntegrityError as e:
                logger.error(str(e))
                raise ValueError(str(e))

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class OPPlanProductDetailView(APIView):
    """
    货盘计划商品条目管理
    raw_data = [
        {
            "plan_product_id": 1,
            "order": 2
        },
    ]
    """

    permission_classes = [IsAuthenticated]

    def patch(self, request):
        """
        货盘商品顺序移动
        """

        try:
            current_user = request.user
            if request.auth.get("user_type") != "OP":
                return IResponse(code=403, message="no permission to operate")
            raw_data = request.data
            try:
                with transaction.atomic():
                    for data in raw_data:
                        plan_product_id = data.get("plan_product_id")
                        order = data.get("order")

                        try:
                            plan_product = ProductSelectionItem.objects.get(id=plan_product_id, is_deleted=False)
                        except ProductSelectionItem.DoesNotExist:
                            raise ValueError("data not found")
                        # 查计划
                        plan = plan_product.plan_category.plan_company.selection_plan
                        # 先查货盘计划状态
                        plan_state_checker(plan.plan_id)
                        user_id = read_lock(plan.plan_id)
                        if current_user.user_id != user_id:
                            raise ValueError("Not granted editing permissions")
                        # 更新顺序
                        plan_product.order = order
                        plan_product.save(update_fields=["order"])
            except IntegrityError as e:
                logger.error(str(e))
                raise ValueError(str(e))

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class OPProductSelectionPlanManagerView(OPAPIView):
    """
    货盘计划商品管理
    """

    fronted_page = "货盘计划"
    resource_name = "货盘商品管理"
    need_format_resource_name = False

    @staticmethod
    def plan_state_checker(plan_id) -> ProductSelectionPlan:
        try:
            selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
        except ProductSelectionPlan.DoesNotExist:
            raise ValueError("data not found")
        if selection_plan.state in [0, 2]:
            raise ValueError("this state can't edit")
        return selection_plan

    def post(self, request, plan_id, product_id):
        """
        商品加入货盘计划
        """
        try:
            raw_data = request.data
            current_user = request.user
            # 先查货盘计划状态
            selection_plan = self.plan_state_checker(plan_id)
            # 其他人正在编辑中
            user_id = read_lock(plan_id)
            if user_id and current_user.user_id != user_id:
                return IResponse(code=400, message="The pallet table is being modified and adding products is not supported")
            # 加入货盘计划
            item = common_add_product_to_plan_fc(
                current_user.user_id,
                plan_id,
                product_id,
                selection_plan,
                raw_data.get("sku_data"),
                self.current_user_type,
                selection_reason=raw_data.get("selection_reason"),
            )
            data = ProductSelectionItemSerializer(instance=item).data

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=plan_id,
                    model=ProductSelectionPlan,
                    describe=f"添加商品到货盘计划: {selection_plan}. 商品ID: {product_id}",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(data=data)
        except ValueError as e:
            logger.error(f"{product_id}:{e}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def update_company_category(self, item_query_set):
        """
        item_query_set: ProductSelectionItemQuerySet
        更新一下各个商品的计划公司和计划分类
        因为实际商品的公司和分类可能已经改了

        """
        try:
            for item in item_query_set:
                product = item.product
                company = product.company
                category = product.category
                plan_category = item.plan_category
                plan_company = plan_category.plan_company
                if plan_category.category_id != int(category[1]):
                    print("不相等1")
                    item_qs = plan_category.productselectionitem_set.all()
                    if len(item_qs) > 1:
                        # 该分类下不止当前商品条目，故当前商品应该另建分类
                        new_plan_category = ProductSelectionPlanCategory.objects.create(
                            plan_company=plan_company,
                            category_id=category[1],
                            order=plan_category.order,
                        )
                        item.plan_category = new_plan_category
                        item.save(update_fields=["plan_category"])
                    else:
                        # 该分类下只有当前商品条目，故修改分类的id即可
                        plan_category.category_id = category[1]
                        plan_category.save(update_fields=["category"])
                if plan_company.company.id != company.id:
                    print("不相等2")
                    cate_qs = plan_company.productselectionplancategory_set.all()
                    if len(cate_qs) > 1:
                        # 必须另外建立公司
                        new_plan_company = ProductSelectionPlanCompany.objects.create(
                            selection_plan_id=item.selection_plan.id,
                            company_id=company.company_id,
                            order=plan_company.order,
                        )
                        # 查看该商品条目的分类是否还有其他产品
                        # 如果没有其他产品，则迁移即可
                        item_qs = plan_category.productselectionitem_set.all()
                        if len(item_qs) > 1:
                            # 该分类下不止当前商品条目，故当前商品应该另建分类
                            new_plan_category = ProductSelectionPlanCategory.objects.create(
                                plan_company=new_plan_company,
                                category_id=category[1],
                                order=plan_category.order,
                            )
                            item.plan_category = new_plan_category
                            item.save(update_fields=["plan_category"])
                        else:
                            plan_category.plan_company = new_plan_company
                            plan_category.save(update_fields=["plan_company"])
                    else:
                        _item_qs = cate_qs[0].productselectionitem_set.all()
                        if len(_item_qs) > 1:
                            # 必须另建公司
                            new_plan_company = ProductSelectionPlanCompany.objects.create(
                                selection_plan_id=item.selection_plan.id,
                                company_id=company.company_id,
                                order=plan_company.order,
                            )
                            # 另建分类
                            new_plan_category = ProductSelectionPlanCategory.objects.create(
                                plan_company=new_plan_company,
                                category_id=category[1],
                                order=plan_category.order,
                            )
                            item.plan_category = new_plan_category
                            item.save(update_fields=["plan_category"])
                        else:
                            # 更新公司id
                            plan_company.company_id = company.company_id
                            plan_company.save(update_fields=["company_id"])
        except Exception as e:
            print(f"{str(e)}--{traceback.format_exc()}")

    def get(self, request, plan_id):
        """
        货盘计划的商品列表
        """
        try:
            return v2_plan_tree_list(
                request,
                plan_id,
            )
            # return plan_tree_data(
            #     request,
            #     plan_id,
            #     OperateSelectionPlanProductListSerializer,
            #     OperateSelectionPlanListStockKeepingUnitProductDetailSerializer,
            # )
        except Exception as e:
            logger.warning(f"db plan tree exception,{e},{traceback.format_exc()}")
            return IResponse(code=500, message="服务器内部错误")

    def patch(self, request, plan_id, product_id):
        """
        编辑货盘计划商品（主要编辑备注）
        """
        try:
            current_user = request.user
            # 先查货盘计划状态
            self.plan_state_checker(plan_id)

            user_id = read_lock(plan_id)
            if current_user.user_id != user_id:
                return IResponse(code=400, message="Not granted editing permissions")
            remark = request.data.get("remark")
            try:
                item = ProductSelectionItem.objects.get(selection_plan=plan_id, product=product_id, is_deleted=False)
            except ProductSelectionItem.DoesNotExist:
                logger.error(f"ProductSelectionItem({plan_id}-{product_id}) not found")
                return IResponse(code=400, message="data not found")

            if item.product.is_deleted:
                return IResponse(code=400, message="商品已删除,无法进行编辑操作")

            # 记录原数据
            original_content = item.remark

            item.remark = remark
            item.save()

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=plan_id,
                    model=ProductSelectionPlan,
                    describe=f'更新了货盘计划"{plan_id}"的商品"{product_id}"备注',
                    operate_content=f'更新内容. 备注: "{original_content}"修改为"{remark}"',
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            # 写入操作明细日志
            if not original_content:
                record_type = ProductSelectionPlanRecordType.ADD_REMARK
                plan_record_content = f"新增备注: {remark}"
            else:
                record_type = ProductSelectionPlanRecordType.MOD_REMARK
                plan_record_content = f'备注:"{original_content}"修改为"{remark}"'

            w_plan_record(
                plan_id=plan_id,
                record_type=record_type,
                content=plan_record_content,
                create_user=current_user.user_id,
                product_id=product_id,
            )

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def delete(self, request, plan_id, product_id):
        """
        从货盘计划中移除商品
        """
        try:
            current_user = request.user
            product = Product.objects.filter(product_id=product_id, is_deleted=False).select_related("company").first()
            if not product:
                return IResponse(code=400, message="data not found")
            # 先查货盘计划状态
            selection_plan = self.plan_state_checker(plan_id)

            user_id = read_lock(plan_id)
            if current_user.user_id != user_id:
                return IResponse(code=400, message="Not granted editing permissions")
            try:
                item = ProductSelectionItem.objects.get(selection_plan=plan_id, product=product_id, is_deleted=False)
            except ProductSelectionItem.DoesNotExist:
                logger.error(f"ProductSelectionItem({plan_id}-{product_id}) not found")
                return IResponse(code=400, message="data not found")

            with transaction.atomic():
                # 释放sku库存
                item_skus = item.productselectionitemsku_set.filter(become_history=False)
                for item_sku in item_skus:
                    sku = StockKeepingUnit.objects.select_for_update().get(sku_id=item_sku.sku_id)
                    # 减去sku的占用库存
                    sku.plan_use_inventory = F("plan_use_inventory") - (item_sku.physical_inventory or 0)
                    sku.save(update_fields=["plan_use_inventory", "can_use_inventory"])
                    # 组合商品释放占用库存
                    if sku.product.is_combine:
                        sub_skus = sku.parent_sub_skus.filter(become_history=False)
                        for sub_sku in sub_skus:
                            relate_sku = StockKeepingUnit.objects.select_for_update().get(sku_id=sub_sku.relate_sku_id)
                            relate_sku.plan_use_inventory = F("plan_use_inventory") - (item_sku.physical_inventory * sub_sku.num)
                            relate_sku.save(update_fields=["plan_use_inventory"])

                item.is_deleted = True
                item.delete_reason = request.data.get("delete_reason") or ""
                item.update_user = current_user.user_id
                # 释放锁定状态
                item.product_confirm_state = 0
                item.save()

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=plan_id,
                    model=ProductSelectionPlan,
                    describe=f'货盘计划"{plan_id}"移除商品"{product_id}"',
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            # 插入操作明细日志
            w_plan_record(
                plan_id=plan_id,
                record_type=ProductSelectionPlanRecordType.DEL_PROD,
                content="",
                create_user=current_user.user_id,
                product_id=product_id,
            )

            # 创建消息通知
            notifications_data = {
                "product_id": product_id,
                "company_id": product.company.company_id,
                "notify_type": "plan",
                "operation_type": "removed_plan",
                "content": f"商品已从直播日期【{selection_plan.live_date_start}】货盘移除",
            }
            ceate_notifications.delay(notifications_data, user_type_list=["SP"])
            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class OPProductSelectionPlanSKUInventoryManager(OPAPIView):
    """
    修改sku库存
    """

    need_format_resource_name = False
    fronted_page = "货盘计划"
    resource_name = "货盘库存"

    def get(self, request: Request, plan_id: int, product_id: int):
        re_data = common_query_item_skus_inventory(request, plan_id, product_id)
        return IResponse(data=re_data)

    def patch(self, request: Request, plan_id: int, product_id: int):
        return common_update_plan_item_skus_fc(request, plan_id, product_id)


class OPProductSelectionPlanProfitMarginView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "货盘利润"
    resource_name = "货盘利润"

    def get(self, request, plan_id):
        re_data, _ = plan_profit_margin(request, plan_id, show_sub_code=False)
        if not re_data:
            return IResponse(code=400, message="data not found")
        return IResponse(data=re_data)

    def post(self, request, plan_id):
        """
        修改预估销量
        :param request:
        :param plan_id:
        :return:
        """
        return common_modify_estimated_sales(request, plan_id)


class OPProductSelectionPlanProfitMarginDownloadView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "货盘利润-下载"
    resource_name = "货盘利润-下载"

    def get(self, request, plan_id):
        data, plan_name = plan_profit_margin_download_data(request, plan_id)
        byte_buffer = get_excel_async("货盘利润", data, image_columns=[3], is_internal=True)
        if byte_buffer:
            byte_buffer.seek(0)
        filename = f"货盘利润：{plan_name}-{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
        return FileResponse(byte_buffer, filename=filename, as_attachment=True)


class OPProductSelectionPlanView(OPAPIView):
    """
    运营商货盘计划
    """

    fronted_page = "货盘列表"
    resource_name = "货盘计划"
    need_format_resource_name = True

    def post(self, request):
        """
        新增货盘计划
        """
        raw_data = request.data
        current_user = request.user
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")
        raw_data["create_user"] = current_user.user_id
        raw_data["update_user"] = current_user.user_id
        # 获取分销商
        distributor_id = raw_data.get("distributor_id")
        assert distributor_id, "distributor_id required"
        raw_data["distributor"] = raw_data.pop("distributor_id")

        live_date_end = raw_data.get("live_date_end")
        assert live_date_end, "live_date_end required"
        if convert_to_ware_time(live_date_end, "%Y-%m-%d").date() < timezone.now().date():
            return IResponse(code=400, message="live_date_end should be a future date")

        serializer = ProductSelectionPlanSerializer(data=raw_data)
        if serializer.is_valid():
            product_plan = serializer.save()
            data = ProductSelectionPlanReaderSerializer(instance=product_plan).data

            # 写入日志
            w_plan_record(
                plan_id=product_plan.plan_id,
                record_type=ProductSelectionPlanRecordType.ADD_PLAN,
                content="",
                create_user=current_user.user_id,
            )

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=str(product_plan.plan_id),
                    model=ProductSelectionPlan,
                    describe=f"新增了货盘计划. 货盘:{product_plan}",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(data=data)
        raise FieldsError(serializer.errors)

    def get(self, request):
        """
        货盘计划列表
        """
        try:
            target = ProductSelectionPlan.objects.all().order_by("-live_date_end")
            re_data, page_plans, obj_qs = custom_django_filter(
                request,
                target,
                SelectionPlanFilterSet,
                need_serialize=False,
                force_order=False,
            )
            page_plans_data = ProductSelectionPlanListSerializer(instance=page_plans, many=True).data
            for plan_data in page_plans_data:
                # 获取正在编辑的用户
                plan_id = plan_data.get("plan_id")
                user_id = read_lock(plan_id)
                real_name = None
                if user_id:
                    try:
                        user = User.objects.get(user_id=user_id)
                    except User.DoesNotExist:
                        logger.error(f"user {user_id} not exist")
                        real_name = f"user {user_id} not exist"
                    else:
                        real_name = user.real_name if user.real_name else user.user_id
                plan_data["editing_user"] = real_name
            re_data["data"].extend(page_plans_data)
            return IResponse(data=re_data)
        except FieldError as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=400)
        except Exception as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=500)


class OPProductSelectionPlanDetailView(OperateLogAPIViewMixin, APIView):
    """
    运营商货盘计划详情
    """

    permission_classes = [IsAuthenticated]
    fronted_page = "货盘详情"
    resource_name = "货盘计划"
    need_format_resource_name = True

    def put(self, request, plan_id):
        """
        编辑货盘计划
        """
        raw_data = request.data
        current_user = request.user
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")
        raw_data["update_user"] = current_user.user_id

        # 检查状态
        try:
            selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
        except ProductSelectionPlan.DoesNotExist:
            return IResponse(code=400, message="data not found")
        if selection_plan.state in [0, 2]:
            return IResponse(code=400, message="this state can't edit")

        # 查看是否有人正在编辑
        # user_id = read_lock(plan_id)
        # if user_id and current_user.user_id != user_id:
        #     return IResponse(code=400, message="Not granted editing permissions")

        # 原始数据，用于日志记录
        original_content = deepcopy(selection_plan.__dict__)
        original_content.pop("_state")
        # 原始model, 进行对比
        origin_model = deepcopy(selection_plan)

        # 获取分销商
        distributor_id = raw_data.get("distributor_id")
        assert distributor_id, "distributor_id required"
        raw_data["distributor"] = raw_data.pop("distributor_id")

        live_date_end = raw_data.get("live_date_end")
        if live_date_end and convert_to_ware_time(live_date_end, "%Y-%m-%d").date() < timezone.now().date():
            return IResponse(code=400, message="live_date_end should be a future date")

        serializer = ProductSelectionPlanSerializer(instance=selection_plan, data=raw_data, partial=True)
        if serializer.is_valid():
            _plan = serializer.save()
            data = ProductSelectionPlanReaderSerializer(instance=_plan).data
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=plan_id,
                    model=ProductSelectionPlan,
                    describe=f"修改了货盘计划: {selection_plan}",
                    raw_object=origin_model,
                    new_object=_plan,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            # 写入操作明细日志
            w_plan_record(
                plan_id=_plan.plan_id,
                record_type=ProductSelectionPlanRecordType.MOD_PLAN,
                content=f"{diff_models(origin_model, _plan)}",
                create_user=current_user.user_id,
            )

            return IResponse(data=data)
        raise FieldsError(serializer.errors)

    def patch(self, request, plan_id):
        """
        只用于锁定和解锁
        """
        raw_data = request.data
        current_user = request.user
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")
        raw_data["update_user"] = current_user.user_id
        # 查看是否有人正在编辑
        user_id = read_lock(plan_id)
        if user_id:
            return IResponse(code=400, message="plan which editing state can't be locked")

        try:
            selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
        except ProductSelectionPlan.DoesNotExist:
            return IResponse(code=400, message="data not found")

        state = raw_data.get("state")
        assert state in [1, 2], "state should be in [1, 2]"

        if selection_plan.state == state:
            return IResponse(code=400, message="no need to operate on this resource")

        # 保存状态
        selection_plan.state = state
        selection_plan.save(update_fields=["state"])

        w_plan_record(
            plan_id=selection_plan.plan_id,
            record_type=ProductSelectionPlanRecordType.LOCK_PLAN if state == 2 else ProductSelectionPlanRecordType.RELEASE_PLAN,
            content="",
            create_user=current_user.user_id,
        )
        data = ProductSelectionPlanReaderSerializer(instance=selection_plan).data
        return IResponse(data=data)


class ProductSelectionPlanEditStatusView(APIView):
    """
    商品货盘计划编辑状态管理
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, plan_id):
        """
        读取编辑状态
        """
        current_user = request.user
        user_id = read_lock(plan_id)
        allowed = False

        try:
            selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
        except ProductSelectionPlan.DoesNotExist:
            selection_plan = None

        if not user_id or current_user.user_id == user_id:
            if selection_plan and selection_plan.state == 1:
                # 同时计划状态必须是可以编辑的
                allowed = True

        re_data = {
            "allowed": allowed,
            "user_id": user_id,
            "user_name": None,
            "user_mobile": None,
        }
        if user_id:
            user = User.objects.get(user_id=user_id)
            re_data["user_name"] = user.real_name
            re_data["user_mobile"] = user.mobile

        return IResponse(code=200, data=re_data)

    def post(self, request, plan_id):
        """
        获取编辑权限
        """
        try:
            current_user = request.user
            key = plan_id
            value = current_user.user_id

            try:
                selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
            except ProductSelectionPlan.DoesNotExist:
                selection_plan = None

            re_data = {"allowed": False}
            # 商品本身属于不可编辑状态
            if selection_plan and selection_plan.state in [0, 2]:
                return IResponse(code=200, data=re_data)
            else:
                lock_acquired = setnx_lock(key, value, 86400)
                if lock_acquired is True or lock_acquired == value:
                    # 获得锁或本人已经成功加锁
                    re_data["allowed"] = True
                return IResponse(code=200, data=re_data)
        except Exception as e:
            logger.error(f"{e}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def delete(self, request, plan_id):
        """
        退出编辑状态
        """
        current_user = request.user
        key = plan_id
        value = current_user.user_id
        res = release_lock(key, value)
        re_data = {"exited": res}
        return IResponse(code=200, data=re_data)


class HistoryPriceAuthorSelectView(APIView):
    """
    直播历史价格主播筛选列表
    """

    permission_classes = [IsAuthenticated]

    def get(self, request: Request, product_id: int):
        live_authors = LiveAuthor.objects.filter(historyprice__product__product_id=product_id, historyprice__product__is_deleted=False).only("author_id", "name").distinct()
        live_author_select_ser = LiveAuthorSelectSer(live_authors, many=True)
        return IResponse(data=live_author_select_ser.data)


class HistoryPriceSKUSelectView(APIView):
    """
    直播历史价格SKU筛选列表
    """

    permission_classes = [IsAuthenticated]

    def get(self, request: Request, product_id: int):
        try:
            product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(data=[])

        skus = product.stockkeepingunit_set.filter(become_history=False)
        re_data = []

        for sku in skus:
            re_data.append(",".join(sku.specs_value.values_list("value", flat=True)))

        return IResponse(data=re_data)


class MiniProductReviewPriceView(OPAPIView):
    """小程序核价信息"""

    def get(self, request: Request, product_id: int):
        """核价信息"""
        try:
            product = Product.objects.get(product_id=product_id)
        except Product.DoesNotExist:
            return IResponse(code=400, message="data not found")

        review_price_info = ProductReviewPrice.objects.filter(product=product).order_by("-create_date").first()
        if not review_price_info:
            return IResponse(code=400, message="data not found")
        ser = ProductReviewPriceInfoSer(instance=review_price_info, many=False)
        re_data = ser.data
        return IResponse(data=re_data)

    def post(self, request: Request, product_id: int):
        """审核价格"""
        try:
            product = Product.objects.get(product_id=product_id)
        except Product.DoesNotExist:
            return IResponse(code=400, message="data not found")

        if ProductReviewPrice.objects.filter(product=product).exists():
            return IResponse(code=400, message="商品已经核价,无法重新操作")

        post_data = request.data.copy()

        # 信息添加
        post_data["product"] = product.product_id
        post_data["create_user"] = self.current_user.user_id

        create_ser = ProductReviewPriceSer(data=post_data)
        if not create_ser.is_valid():
            raise FieldError(create_ser.errors)
        created_obj = create_ser.save()
        ser = ProductReviewPriceSer(instance=created_obj, many=False)
        re_data = ser.data
        return IResponse(data=re_data)


class OPSelectionPlanItemRecycleListView(OPAPIView):
    """货盘计划回收站商品列表"""

    def get(self, request: Request, selection_plan_id: int):
        item_product_id_list = ProductSelectionItem.objects.select_related("product").filter(selection_plan_id=selection_plan_id, is_deleted=True)
        re_data, page_objects, _ = custom_django_filter(
            request,
            item_product_id_list,
            SelectionPlanItemRecycleListFilterSet,
            None,
            need_serialize=False,
        )

        list_data = SelectionPlanItemRecycleListSer(instance=page_objects, many=True, context={"request": request}).data
        re_data["data"] = list_data

        return IResponse(data=re_data)


class OPSelectionPlanItemRecycleDetailView(OPAPIView):
    """货盘计划商品回收站管理"""

    def _get_obj(self, item_id: int) -> ProductSelectionItem:
        try:
            selection_item = ProductSelectionItem.objects.get(pk=item_id, is_deleted=True)
        except ProductSelectionItem.DoesNotExist:
            raise APIViewException(err_message="资源不存在,请刷新页面后重新操作")
        selection_plan_state = selection_item.selection_plan.state
        if selection_plan_state == 0:
            raise APIViewException(err_message="该货盘计划已结束,无法操作")
        if selection_plan_state == 2:
            raise APIViewException(err_message="该货盘计划已锁定,无法操作")
        if selection_item.product.is_deleted:
            raise APIViewException(err_message="该商品已被删除,无法还原")

        return selection_item

    @transaction.atomic
    def patch(self, request: Request, item_id: int):
        """还原商品到货盘计划"""
        selection_item = self._get_obj(item_id)

        plan_id = selection_item.selection_plan_id
        product_id = selection_item.product_id

        user_id = read_lock(plan_id)
        current_user_user_id = self.current_user.user_id
        if user_id and current_user_user_id != user_id:
            return IResponse(code=400, message="plan is editing by other user")
        # 判断是否在其他进行中的选品计划
        if ProductSelectionItem.objects.filter(
            product_id=product_id,
            selection_plan_id=plan_id,
            is_deleted=False,
        ).exists():
            return IResponse(code=400, message="该商品已在该货盘计划")
        # 扣库存
        ts_point = transaction.savepoint()
        # 修改数据为False
        selection_item.is_deleted = False
        selection_item.update_user = self.current_user.user_id
        selection_item.save()

        item_skus = selection_item.productselectionitemsku_set.filter(become_history=False)
        try_times = 3
        post_sku_data = [{"sku_id": item_sku.sku_id, "physical_inventory": item_sku.physical_inventory} for item_sku in item_skus]
        for sku_data in post_sku_data:
            for i in range(try_times):
                sku_id = sku_data.get("sku_id")
                physical_inventory = sku_data.get("physical_inventory", 0) or 0

                if not str(sku_id).isdigit():
                    transaction.savepoint_rollback(ts_point)
                    raise ValueError(f"非法SKU_ID:{sku_id}")

                try:
                    _sku = StockKeepingUnit.objects.get(sku_id=sku_id, become_history=False)
                except StockKeepingUnit.DoesNotExist:
                    transaction.savepoint_rollback(ts_point)
                    raise ValueError("SKU不存在")

                if (physical_inventory > _sku.can_use_inventory) or (physical_inventory > (_sku.physical_inventory - _sku.plan_use_inventory)):
                    transaction.savepoint_rollback(ts_point)
                    logger.info(f"加货盘库存不足 >> {sku_id},{product_id}")
                    raise ValueError("库存不足")

                # 判断组合商品库存
                if _sku.product.is_combine:
                    sub_skus = _sku.parent_sub_skus.filter(become_history=False)
                    for sub_sku in sub_skus:
                        relate_sku = sub_sku.relate_sku
                        act_need_inventory = sub_sku.num * physical_inventory

                        if (act_need_inventory > relate_sku.can_use_inventory) or (act_need_inventory > relate_sku.physical_inventory - relate_sku.plan_use_inventory):
                            transaction.savepoint_rollback(ts_point)
                            logger.info(f"组合商品加货盘库存不足 >> {sku_id},{product_id},{relate_sku}")
                            raise ValueError("组合商品库存不足")

                        # 更新关联sku的占用库存
                        relate_sku.plan_use_inventory = F("plan_use_inventory") + act_need_inventory
                        relate_sku.save(update_fields=["plan_use_inventory"])

                # 原sku库存,触发信号修改商品可用库存
                _sku.plan_use_inventory = F("plan_use_inventory") + physical_inventory
                _sku.save(update_fields=["plan_use_inventory"])
                break

        plan_record_remark = ""
        # 物理删除其他的回收站item数据
        need_delete_items = ProductSelectionItem.objects.filter(
            selection_plan_id=plan_id,
            product_id=product_id,
            is_deleted=True,
        ).exclude(id=item_id)
        if need_delete_items:
            original_content = json.dumps(
                [
                    {
                        "plan_id": plan_id,
                        "product_id": product_id,
                        "remark": item.remark,
                        "delete_reason": item.delete_reason,
                        "update_user": item.update_user,
                        "create_date": timezone.localtime(item.create_date).strftime(DATETIME_FORMAT),
                        "update_date": timezone.localtime(item.update_date).strftime(DATETIME_FORMAT),
                    }
                    for item in need_delete_items
                ],
                ensure_ascii=False,
            )
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=str(plan_id),
                    model=ProductSelectionPlan,
                    describe="货盘回收站恢复商品时,后台系统自动删除回收站相同数据",
                    operate_content=f"删除商品ID:{product_id},删除条数:{need_delete_items.count()}",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            plan_record_remark = f"还原后删除其他相同数据:{original_content}"
            need_delete_items.delete()
        transaction.savepoint_commit(ts_point)
        # 插入操作明细日志
        w_plan_record(
            plan_id=int(plan_id),
            record_type=ProductSelectionPlanRecordType.RESTORE_PROD,
            content="",
            create_user=current_user_user_id,
            product_id=int(product_id),
            remark=plan_record_remark,
        )
        return IResponse()


class OPSelectionPlanItemRecycleListDownloadView(OPAPIView):
    def get(self, request: Request, selection_plan_id: int):
        item_product_id_list = ProductSelectionItem.objects.select_related(
            "product",
            "selection_plan",
        ).filter(
            selection_plan_id=selection_plan_id,
            is_deleted=True,
            product__is_deleted=False,
        )
        _, page_objes, _ = custom_django_filter(
            request,
            item_product_id_list,
            SelectionPlanItemRecycleListFilterSet,
            need_serialize=False,
        )

        items = SelectionPlanItemRecycleListDownloadSer(instance=page_objes, many=True, context={"request": request}).data

        products_map = {selection_item.product_id: selection_item.product for selection_item in page_objes.object_list}

        re_data = []
        count = 1
        for item in items:
            tmp_data = {"序号": count}
            for k, v in SelectionPlanItemRecycleListDownloadTmpl.items():
                if k == "attr":
                    attr_download_info_handler(tmp_data, products_map.get(item.get("product_id")))
                    continue

                if k != "category":
                    tmp_data[v] = item.get(k, "")
                    continue
                tmp_category_list = ["一级分类", "二级分类", "三级分类"]
                for idx, category_value in enumerate(item.pop("category")):
                    tmp_data[f"{tmp_category_list[idx]}ID"] = category_value["id"]
                    tmp_data[f"{tmp_category_list[idx]}名称"] = category_value["name"]

            re_data.append(tmp_data)
            count += 1
        filename = f"货盘{selection_plan_id}回收站商品列表-{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
        byte_buffer = get_excel_async("回收站商品列表", re_data, image_columns=[6], is_internal=True)
        if byte_buffer:
            byte_buffer.seek(0)
        return FileResponse(byte_buffer, filename=filename, as_attachment=True)


class OPSelectionPlanRecordsCountsView(OPAPIView):
    """操作明细统计"""

    def get(self, request: Request, plan_id: int):
        records = (
            ProductSelectionPlanRecord.objects.filter(
                selection_plan_id=plan_id,
                record_type__in=(
                    ProductSelectionPlanRecordType.ADD_PROD,
                    ProductSelectionPlanRecordType.DEL_PROD,
                ),
            )
            .values("record_type", "create_user")
            .annotate(counts=Count("record_type"))
        )

        user_id_list = [record["create_user"] for record in records]
        users = User.objects.filter(user_id__in=user_id_list).only("user_id", "real_name")
        user_map = {str(user.user_id): user.real_name or user.username for user in users}

        tmp_dict = {}
        for record in records:
            user_id = record["create_user"]
            record_type = record["record_type"]

            data = {
                "record_type": record_type,
                "counts": record["counts"],
            }
            if user_id in tmp_dict:
                if record_type == ProductSelectionPlanRecordType.ADD_PROD:
                    tmp_dict[user_id]["data"].insert(0, data)
                else:
                    tmp_dict[user_id]["data"].append(data)

            else:
                tmp_dict[user_id] = {
                    "user_id": user_id,
                    "real_name": user_map.get(user_id, ""),
                    "data": [data],
                }

        return IResponse(data=tmp_dict.values())


class SelectionPlanRecordsView(OPAPIView):
    """明细记录"""

    def get(self, request: Request, plan_id: int):
        try:
            records = ProductSelectionPlanRecord.objects.filter(selection_plan_id=plan_id)
            re_data, page_objes, _ = custom_django_filter(
                request,
                records,
                ProductSelectionPlanRecordFilterSet,
                order_fields=("-create_date",),
                need_serialize=False,
            )

            # 查询用户
            user_id_list = set([record.create_user for record in page_objes])
            users = User.objects.filter(user_id__in=user_id_list).only("user_id", "username", "real_name")
            user_map = {str(user.user_id): f"{user.username}({user.real_name or user.user_id})" for user in users}

            today_date = timezone.now().date()
            yesterday_date = today_date - timedelta(days=1)

            tmp_data_dict = {}
            for obj in page_objes:
                record_date = timezone.localtime(obj.create_date).date()
                if record_date == today_date:
                    date_name = "今天"
                elif record_date == yesterday_date:
                    date_name = "昨天"
                else:
                    date_name = timezone.localtime(obj.create_date).strftime(DATE_FORMAT)

                time_str = timezone.localtime(obj.create_date).strftime(TIME_FORMAT)

                operator = user_map.get(obj.create_user)

                if obj.record_type in (
                    ProductSelectionPlanRecordType.ADD_PROD,
                    ProductSelectionPlanRecordType.DEL_PROD,
                    ProductSelectionPlanRecordType.RESTORE_PROD,
                ):
                    content = obj.get_record_type_display() + f" ID:{obj.product_id}"
                elif obj.record_type in (
                    ProductSelectionPlanRecordType.ADD_PLAN,
                    ProductSelectionPlanRecordType.MOD_PLAN,
                    ProductSelectionPlanRecordType.DEL_PLAN,
                    ProductSelectionPlanRecordType.LOCK_PLAN,
                    ProductSelectionPlanRecordType.RELEASE_PLAN,
                ):
                    content = obj.get_record_type_display() + f" ID:{obj.selection_plan_id}"
                else:
                    content = obj.get_record_type_display() + ". " + obj.content

                if date_name in tmp_data_dict:
                    # data append
                    tmp_data_dict[date_name]["data"].append(
                        {
                            "time": time_str,
                            "operator": operator,
                            "content": content,
                        }
                    )
                else:
                    tmp_data_dict[date_name] = {
                        "date_desc": date_name,
                        "data": [
                            {
                                "time": time_str,
                                "operator": operator,
                                "content": content,
                            }
                        ],
                    }
            re_data["data"] = tmp_data_dict.values()
            return IResponse(data=re_data)
        except:
            print(traceback.format_exc())
            return IResponse()


class OPCSProductSelectionPlanItemView(OPAPIView):
    def get(self, request: Request, plan_id: int):
        re_data = get_cs_selection_plan_items(
            request,
            plan_id,
            CSOperateSelectionPlanProductListSerializer,
            OPCSSelectionPlanItemSKUDetailSerializer,
        )
        return IResponse(data=re_data)


class OPCSProductSelectionPlanItemLinkView(OPAPIView):
    """关联客服"""

    def patch(self, request: Request, plan_id: int, product_id: int):
        try:
            item_record = ProductSelectionItem.objects.get(
                selection_plan_id=plan_id,
                product_id=product_id,
                is_deleted=False,
            )
        except ProductSelectionItem.DoesNotExist:
            return IResponse(code=400, message="data not found")

        link_cs_id = request.data.get("link_cs_id") or 0
        link_cs_remark = request.data.get("link_cs_remark") or ""

        try:
            cs = CustomerService.objects.get(id=link_cs_id)
        except CustomerService.DoesNotExist:
            return IResponse(code=400, message="error request params")

        # 保存并写入明细日志
        old_record = deepcopy(item_record)
        item_record.link_cs = cs
        item_record.link_cs_remark = link_cs_remark
        item_record.save()
        # 写入操作明细日志
        w_plan_record(
            plan_id=plan_id,
            record_type=ProductSelectionPlanRecordType.MOD_CS_LINK,
            content=f"{diff_models(old_record, item_record)}",
            create_user=self.current_user.user_id,
            product_id=product_id,
        )
        return IResponse()


class OPCSProductSelectionPlanItemDownloadView(OPAPIView):
    def get(self, request: Request, plan_id: int):
        return download_cs_selection_plan_product_list_fc(
            request,
            plan_id,
            OperateSelectionPlanProductListDownloadSerializer,
            OperatorSelectionPlanDownloadProductTmpl,
        )


class OPSelectionMapItemMoveView(OPAPIView):
    """排品地图移动排序"""

    def patch(self, request: Request):
        return map_item_move_fc(request)


class OPSelectionPlanProdsMapView(OPAPIView):
    """
    货盘计划-排品地图
    """

    def get(self, request, plan_id):
        return selection_plan_prods_map_fc(request, plan_id)


class OPSelectionItemMoveView(OPAPIView):
    """货盘商品移动到其他货盘"""

    def post(self, request: Request, plan_id: int, plan_product_id: int):
        new_plan_id = request.data.get("new_plan_id")
        remark = request.data.get("remark", "")
        post_sku_data = request.data.get("sku_data")
        if not new_plan_id:
            return IResponse(code=400, message="invalid params")

        return selection_plan_item_move_fc(
            request,
            plan_id,
            new_plan_id,
            plan_product_id,
            remark,
            post_sku_data,
        )


class OPSelectionItemBulkMoveView(OPAPIView, OperateLogAPIViewMixin):
    resource_name = "批量移动"
    fronted_page = "货盘计划"
    need_format_resource_name = False

    def post(self, request: Request):
        bulk_move_items_to_new_plan(request, self.current_user)
        return IResponse(code=202)


class OPProductCopyView(OPAPIView):
    """商品复制"""

    resource_name = "商品复制"
    fronted_page = "商品列表"
    need_format_resource_name = False

    def post(self, request: Request, product_id: int):
        return product_copy_fc(request, product_id)


class OPLiveNeedsView(OPAPIView):
    """分销商直播需求列表"""

    def get(self, request: Request):
        live_needs = LiveNeeds.objects.all()
        re_data, _, _ = custom_django_filter(
            request,
            live_needs,
            LiveNeedsFilterSet,
            LiveNeedsInfoSer,
        )
        return IResponse(data=re_data)


class OPLiveNeedsDetailView(OPAPIView):
    """直播需求详情"""

    def get(self, request: Request, needs_id: int):
        try:
            live_needs = LiveNeeds.objects.get(needs_id=needs_id)
        except LiveNeeds.DoesNotExist:
            return IResponse(code=400, message="data not found")
        ser = LiveNeedsInfoSer(live_needs, many=False)
        re_data = ser.data
        return IResponse(data=re_data)

    def patch(self, request: Request, needs_id: int):
        state = request.data.get("state")
        if not state:
            return IResponse(code=400, message="invalid state")
        try:
            live_needs = LiveNeeds.objects.get(needs_id=needs_id)
        except LiveNeeds.DoesNotExist:
            return IResponse(code=400, message="data not found")

        if live_needs.state == 1:
            return IResponse(code=400, message="该直播需求已确认过，无需重新确认")

        live_needs.state = state
        live_needs.confirm_user = self.current_user.user_id
        live_needs.confirm_date = timezone.now()
        live_needs.save()
        return IResponse()


class OPSelectionItemConfirmView(OPAPIView):
    """货盘质检"""

    def patch(self, request: Request, plan_product_id: int):
        """状态修改，不需要开启修改状态即可"""
        return selection_plan_item_confirm_fc(request, plan_product_id)


class OPProductLinkDistributorView(OPAPIView):
    """商品关联分销商货号"""

    def post(self, request: Request, product_id: int):
        return post_product_link_distributor_fc(request, product_id)


class OPProductLinkDistributorDetailView(OPAPIView):
    """商品关联分销商货号修改"""

    def patch(self, request: Request, links_id: int):
        code = request.data.get("code")
        if not code:
            return IResponse(code=400, message="invalid params")
        try:
            links_record = ProductLinkDistributor.objects.get(pk=links_id, is_deleted=False)
        except ProductLinkDistributor.DoesNotExist:
            return IResponse(code=400, message="data not found")

        links_record.code = code
        links_record.update_user = request.user.user_id
        links_record.save()
        return IResponse()

    def delete(self, request: Request, links_id: int):
        try:
            links_record = ProductLinkDistributor.objects.get(pk=links_id, is_deleted=False)
        except ProductLinkDistributor.DoesNotExist:
            return IResponse(code=400, message="data not found")

        links_record.is_deleted = True
        links_record.update_user = request.user.user_id
        links_record.save()

        # 取消关联
        Product.objects.filter(product_id=links_record.linked_product_id).update(origin_product=None)

        return IResponse()


class OPSKUListView(OPAPIView):
    def get(self, request: Request):
        # 特定供应商
        visible_supplier = self.current_user.visible_supplier

        target = (
            StockKeepingUnit.objects.annotate(
                tmp_product_id=F("product__product_id"),
                product_name=F("product__name"),
                product_code=F("product__code"),
                company_name=F("product__company__name"),
                company_id=F("product__company__company_id"),
                product_main_images=F("product__main_images"),
                is_combine=F("product__is_combine"),
            )
            .prefetch_related("specs_value", "specs_value__spec_key")
            .filter(become_history=False, product__is_deleted=False)
        )

        if visible_supplier:
            target = target.filter(product__company_id__in=visible_supplier)

        re_data, _, _ = custom_django_filter(
            request,
            target,
            SKUInventoryFilterSet,
            SKUInventoryInfoSer,
        )
        return IResponse(data=re_data)


class OPSKUInventoryView(OPAPIView):
    """库存修改"""

    def patch(self, request: Request, sku_id: int):
        return update_sku_inventory_fc(request, sku_id)


class SelectionPlanRecordsDownloadView(OPAPIView):
    """货盘明细表导出"""

    def get(self, request: Request):
        # todo: implement
        return IResponse()


class ProductCategoryManagerView(OPAPIView):
    """
    分类数据管理
    """

    fronted_page = "分类管理"
    resource_name = "分类"

    # 分类修改，运营商端
    def put(self, request: Request, category_id: int):
        name = request.data.get("name")
        new_parent_id = request.data.get("parent_id")
        is_distributor_display = request.data.get("is_distributor_display", False)

        try:
            category = ProductCategoryList.objects.get(id=category_id, is_deleted=False)
        except ProductCategoryList.DoesNotExist:
            return IResponse(code=400, message="data not found")

        if any([new_parent_id, category.parent_id]) and not all([new_parent_id, category.parent_id]):
            return IResponse(code=400, message="仅支持同级分类修改")

        need_replace = []
        raw_log_category = copy.deepcopy(category)
        if category.parent_id != new_parent_id:
            raw_qs = ProductCategoryList.objects.fetch_all_parents(category_id, include_self=False)
            old_category_ids = [i.id for i in raw_qs]

            try:
                new_parent_category = ProductCategoryList.objects.get(id=new_parent_id, is_deleted=False)
            except ProductCategoryList.DoesNotExist:
                return IResponse(code=400, message="父级ID不存在,请刷新页面后重试")

            if new_parent_category.level != category.parent.level:
                return IResponse(code=400, message="仅支持同级分类修改")

            new_id_raw_qs = ProductCategoryList.objects.fetch_all_parents(new_parent_id)
            new_category_ids = [i.id for i in new_id_raw_qs]

            for idx, val in enumerate(new_category_ids):
                old_val = old_category_ids[idx]
                if val != old_val:
                    need_replace.append((str(old_val), str(val)))

            category.parent = new_parent_category

        with transaction.atomic():
            if need_replace:
                # 更新商品category,直播需求category
                for need in need_replace:
                    old_val, val = need
                    pu_rows = Product.objects.filter(category__contains=[category_id]).update(category=ArrayReplace("category", old_val, val))
                    print(f"{old_val} changed to {val},updated product {pu_rows} rows")
                    nu_rows = LiveNeeds.objects.filter(live_category__contains=[category_id]).update(live_category=ArrayReplace("live_category", old_val, val))
                    print(f"{old_val} changed to {val},updated live_needs {nu_rows} rows")
            category.name = name
            category.update_user = self.current_user.user_id
            category.is_distributor_display = is_distributor_display
            category.save()
        # 同步数据至聚水潭
        # 聚水潭的分类名字在等级内是唯一的，和此处设计不同，如果有重复名称数据会误删
        sync_category_to_JST.delay(category.id)

        # 更新缓存
        cache_key = f"category_{category_id}"
        cate_data = {"id": category_id, "name": category.name, "is_distributor_display": category.is_distributor_display}
        cache.set(cache_key, cate_data, timeout=60 * 60)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(category_id),
                model=ProductCategoryList,
                describe=f"修改了分类. 分类ID:{category_id}",
                raw_object=raw_log_category,
                new_object=category,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def delete(self, request: Request, category_id: int):
        try:
            category = ProductCategoryList.objects.get(pk=category_id, is_deleted=False)
        except ProductCategoryList.DoesNotExist:
            return IResponse(code=400, message="data not found")

        if ProductCategoryList.objects.filter(parent=category, is_deleted=False).exists():
            return IResponse(code=400, message="请先删除子级分类")

        if LiveNeeds.objects.filter(live_category__contains=[category_id]).exists():
            return IResponse(code=400, message="请先删除包含此分类的直播需求")

        if Product.objects.filter(category__contains=[category_id], is_deleted=False).exists():
            return IResponse(code=400, message="请先删除包含此分类的所有商品")

        category.is_deleted = True
        category.update_user = self.current_user.user_id
        category.save()

        # 同步数据至聚水潭
        # sync_category_to_JST.delay(category.id)

        # 删除缓存
        cache_key = f"category_{category_id}"
        cache.delete(cache_key)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(category_id),
                model=ProductCategoryList,
                describe=f"删除了分类. 分类ID:{category_id}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class ProductCategoryAttrManagerView(OPAPIView):
    fronted_page = "属性管理"
    resource_name = "分类属性关联"
    need_format_resource_name = False

    def post(self, request: Request):
        try:
            category_id = request.data.get("category_id")
            attr_id = request.data.get("attr_id")
            category = ProductCategoryList.objects.get(id=category_id, is_deleted=False)
            attr = ProductAttrList.objects.get(id=attr_id, is_deleted=False)
        except ProductCategoryList.DoesNotExist:
            return IResponse(code=400, message="分类不存在,请刷新列表后重试")
        except ProductAttrList.DoesNotExist:
            return IResponse(code=400, message="属性值不存在,请刷新列表后重试")

        if category.categoryattrmembership_set.filter(attr_id=attr_id).exists():
            return IResponse(code=400, message="分类已关联属性,不需要重新关联")

        ship = CategoryAttrMembership(category=category, attr=attr)
        ship.save()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(ship.id),
                model=CategoryAttrMembership,
                describe=f'分类"{category}"关联了属性"{attr}"',
                is_success_input=True,
            )
        except Exception as e:
            print(traceback.format_exc())
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def delete(self, request: Request):
        category_id = request.data.get("category_id")
        attr_id = request.data.get("attr_id")
        if not CategoryAttrMembership.objects.filter(category_id=category_id, attr_id=attr_id).exists():
            return IResponse(code=400, message="关联不存在")
        if ProductAttrOption.objects.filter(attr__categoryattrmembership__category_id=category_id, attr_id=attr_id, product__is_deleted=False).exists():
            return IResponse(code=400, message="该属性已有商品使用")

        ship = CategoryAttrMembership.objects.get(category_id=category_id, attr_id=attr_id)
        ship.delete()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(ship.id),
                model=CategoryAttrMembership,
                describe=f'分类"{ship.category}"取消了关联属性"{ship.attr}"',
                is_success_input=True,
            )
        except Exception as e:
            print(traceback.format_exc())
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class ProductCategoryAttrLinksView(OPAPIView):
    """
    分类关联的属性
    """

    def get(self, request: Request, category_id: int):
        raw_params = QueryDict(query_string=f"categoryattrmembership__category_id={category_id}")

        page_model_obj, re_data, _ = custom_filter(
            raw_params,
            ProductAttrList,
        )

        ser = ProductAttrListSerializer(instance=page_model_obj, many=True)
        re_data["data"] = ser.data
        return IResponse(data=re_data)


class ProductAttrManagerView(OPAPIView):
    """商品属性管理"""

    fronted_page = "属性管理"
    resource_name = "属性"
    need_format_resource_name = True

    def get(self, request: Request):
        """属性列表"""
        target = ProductAttrList.objects.filter(is_deleted=False)
        re_data, _, _ = custom_django_filter(
            request,
            target,
            ProductAttrListFilterSet,
            ProductAttrListSer,
            order_fields=[
                "id",
            ],
            force_order=False,
        )
        return IResponse(data=re_data)

    def post(self, request: Request):
        """创建属性"""
        raw_data = request.data
        current_user_id = self.current_user.user_id
        raw_data["create_user"] = current_user_id

        # 默认是空列表
        values = raw_data.pop("values", [])

        create_ser = ProductAttrCreateOrUpdateSer(data=request.data)
        if not create_ser.is_valid():
            raise FieldsError(create_ser.errors)

        with transaction.atomic():
            instance = create_ser.save()
            # 下拉列表才创建值
            if instance.attr_type == "select":
                new_values = []
                for val in values:
                    if not val.get("name"):
                        continue

                    new_values.append(val.get("name"))

                ProductAttrValues.objects.bulk_create(
                    [
                        ProductAttrValues(
                            attr=instance,
                            name=val,
                            create_user=current_user_id,
                        )
                        for val in new_values
                    ]
                )

        # 发送同步规格信号
        product_signals.attr_sync_to_spec_signal.send(sender=None, instance=instance, created=True)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(create_ser.instance.id),
                model=ProductAttrList,
                describe=f"新增了属性. 属性:{instance}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class ProductAttrDetailManagerView(OPAPIView):
    fronted_page = "属性管理"
    resource_name = "属性"
    need_format_resource_name = True

    def get(self, request: Request, attr_id: int):
        try:
            attr = ProductAttrList.objects.get(id=attr_id, is_deleted=False)
        except ProductAttrList.DoesNotExist:
            return IResponse(code=400, message="data not found")

        ser = ProductAttrDetailSer(instance=attr, many=False)
        return IResponse(data=ser.data)

    def put(self, request: Request, attr_id: int):
        """编辑属性"""
        try:
            attr = ProductAttrList.objects.get(id=attr_id, is_deleted=False)
        except ProductAttrList.DoesNotExist:
            return IResponse(code=400, message="data not found")

        raw_data = request.data

        raw_data["update_user"] = self.current_user.user_id
        create_ser = ProductAttrCreateOrUpdateSer(instance=attr, data=raw_data)
        if not create_ser.is_valid():
            return IResponse(code=400, message=create_ser.errors)

        with transaction.atomic(savepoint=False):
            # 处理values
            # [{'id':null,'name':"name"},{'id':2,'name':'new_v1'}]
            if attr.attr_type == "select":
                values = raw_data.pop("values", [])
                if not isinstance(values, list):
                    return IResponse(code=400, message="invalid params")
                # 只处理有name值的数据
                values = [val for val in values if val.get("name")]

                post_values_ids = {val.get("id") for val in values if val.get("id")}
                if attr.productattrvalues_set.filter(id__in=post_values_ids).count() != len(post_values_ids):
                    return IResponse(code="400", message="错误值输入, 请刷新列表后重试")

                exist_attr_values = attr.productattrvalues_set.filter(is_deleted=False)

                exist_attr_values_ids = {exist_attr.id for exist_attr in exist_attr_values}
                # 删除旧的数据
                need_delete_values_ids = exist_attr_values_ids.difference(post_values_ids)
                attr.productattrvalues_set.filter(id__in=need_delete_values_ids).update(is_deleted=True)

                # todo:删除product attr option的值对应
                # todo:清空值的id
                ProductAttrOption.objects.filter(attr_value_id__in=need_delete_values_ids).delete()

                # 更新数据
                exist_attr_values_map = {str(exist_attr_value.id): exist_attr_value for exist_attr_value in exist_attr_values}

                need_add_objects = []
                need_update_objects = []

                for v in values:
                    _v_id = v.get("id")
                    _v_name = v.get("name")
                    if not _v_id:
                        need_add_objects.append(ProductAttrValues(attr=attr, name=_v_name))
                        continue

                    _v_id = str(_v_id)
                    if _v_name != exist_attr_values_map.get(_v_id).name:
                        exist_attr_values_map.get(_v_id).name = _v_name
                        need_update_objects.append(exist_attr_values_map.get(_v_id))

                ProductAttrValues.objects.bulk_create(need_add_objects, batch_size=1000)
                ProductAttrValues.objects.bulk_update(need_update_objects, fields=["name"], batch_size=1000)

                # todo: 修改product attr option的值.

            raw_instance = copy.deepcopy(attr)
            updated_instance = create_ser.save()

        # 发送同步规格信号
        product_signals.attr_sync_to_spec_signal.send(sender=None, instance=updated_instance, created=False)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(attr_id),
                model=ProductAttrList,
                describe=f"修改了属性. 属性ID:{attr_id}",
                raw_object=raw_instance,
                new_object=updated_instance,
                is_success_input=True,
            )
        except Exception as e:
            print(e)
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def delete(self, request: Request, attr_id: int):
        """删除属性"""
        try:
            attr = ProductAttrList.objects.get(id=attr_id, is_deleted=False)
        except ProductAttrList.DoesNotExist:
            return IResponse(code=400, message="data not found")
        # 关联分类不支持删除
        if attr.categoryattrmembership_set.exists():
            return IResponse(code=400, message="该属性关联了分类,不允许删除")

        # 关联商品后不支持删除
        if attr.productattroption_set.exists():
            return IResponse(code=400, message="该属性关联了商品,不允许删除")

        attr.is_deleted = True
        attr.save()

        # 发送信号更新数据
        product_signals.attr_sync_to_spec_signal.send(sender=None, instance=attr, created=False)
        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(attr_id),
                model=ProductAttrList,
                describe=f"删除了属性. 属性:{attr}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class OPHostingProductView(OPAPIView):
    """
    运营商操作托管商品
    """

    def get(self, request: Request):
        hosting_products = HostingProducts.objects.select_related(
            "product",
            "product__company",
        ).filter(is_deleted=False, product__is_deleted=False, state__in=[1, 2])

        re_data, _, _ = custom_django_filter(
            request=request,
            target=hosting_products,
            filter_set=HostingProductFilterSet,
            iserializer=HostingProductListSer,
            order_fields=["-create_date"],
            need_serialize=True,
        )
        return IResponse(data=re_data)

    def post(self, request: Request):
        raw_data = request.data

        raw_data["create_user"] = self.current_user.user_id

        ser = HostingProductsCreateSer(data=raw_data)
        if not ser.is_valid():
            raise FieldsError(ser.errors)

        ser.save()
        return IResponse()


class OPHostingProductDetailView(OPAPIView):
    def patch(self, request: Request, hosting_id: int):
        """
        更新状态,先只有hosting_id
        :param hosting_id:
        :param request:
        :return:
        """
        try:
            hosting_product = HostingProducts.objects.get(
                hosting_id=hosting_id,
                state__in=[1, 2],
                is_deleted=False,
                product__is_deleted=False,
            )
        except HostingProducts.DoesNotExist:
            return IResponse(code=400, message="data not found")
        if hosting_product.state == 2:
            return IResponse(code=400, message="该商品托管已过期, 无需托管")

        hosting_product.state = 3
        hosting_product.update_user = self.current_user.user_id
        hosting_product.save()
        return IResponse()


class OPProductLabelTypesEnumView(OPAPIView):
    """
    商品标签类型枚举
    """

    def get(self, request: Request):
        product_label_types = ProductLabelTypes.objects.all()
        re_data = [
            {
                "id": product_label_type.pk,
                "name": product_label_type.name,
                "order": product_label_type.order,
            }
            for product_label_type in product_label_types
        ]
        return IResponse(data=re_data)


class OPProductLabelsView(OPAPIView):
    """
    标签管理
    """

    need_format_resource_name = True
    fronted_page = "标签管理"
    resource_name = "标签"

    def get(self, request: Request):
        """
        标签列表
        :param request:
        :return:
        """
        product_labels = ProductLabels.objects.select_related("l_type").filter(display=True)

        re_data, _, _ = custom_django_filter(
            request=request,
            target=product_labels,
            filter_set=ProductLabelsFilterSet,
            iserializer=ProductLabelsListSer,
            need_serialize=True,
        )
        return IResponse(data=re_data)

    def post(self, request: Request):
        """
        新增标签
        :param request:
        :return:
        """
        raw_data = request.data
        raw_data["create_user"] = self.current_user.user_id
        raw_data["l_type"] = raw_data.pop("l_type_id", None)
        ser = ProductLabelsCreateOrUpdateSer(data=raw_data)
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        instance = ser.save()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=instance.pk,
                model=ProductLabels,
                describe=f"新增了标签:{instance}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class OPProductLabelsDetailView(OPAPIView):
    """
    标签管理
    """

    need_format_resource_name = True
    fronted_page = "标签管理"
    resource_name = "标签"

    @staticmethod
    def _get_obj(label_pk: int) -> ProductLabels:
        try:
            label = ProductLabels.objects.get(pk=label_pk, is_deleted=False)
        except ProductLabels.DoesNotExist:
            raise APIViewException(err_message="data not found")

        if not label.can_edit or not label.is_normal:
            raise APIViewException(err_message="系统标签不允许修改和删除")

        return label

    def get(self, request: Request, label_pk: int):
        """
        标签详情
        :param request:
        :param label_pk:
        :return:
        """
        label = self._get_obj(label_pk)
        ser = ProductLabelsDetailSer(instance=label)
        return IResponse(data=ser.data)

    def put(self, request: Request, label_pk: int):
        """
        更新标签
        :param request:
        :return:
        """
        label = self._get_obj(label_pk)
        raw_data = request.data
        raw_data["update_user"] = self.current_user.user_id
        raw_data["l_type"] = raw_data.pop("l_type_id", None)
        raw_label = copy.deepcopy(label)
        ser = ProductLabelsCreateOrUpdateSer(instance=label, data=raw_data)
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        instance = ser.save()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=instance.pk,
                model=ProductLabels,
                describe=f"修改了标签:{instance}.",
                raw_object=raw_label,
                new_object=instance,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def delete(self, request: Request, label_pk: int):
        label = self._get_obj(label_pk)
        if label.link_product_count > 0:
            return IResponse(code=400, message="该标签已被使用,不允许删除")

        label.update_user = self.current_user.user_id
        label.is_deleted = True
        label.save()
        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=label.pk,
                model=ProductLabels,
                describe=f"删除了标签:{label}.",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class OPSelectionPlanProdsMapForFeishuView(OPAPIView):
    """
    货盘计划-排品地图
    """

    def get(self, request, plan_id):
        return selection_plan_prods_map_for_feishu(request, plan_id)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def AI_generate_core_selling_point_view(request, product_id):
    """
    AI 生成核心卖点
    款式、产地   分类（二级）可以传
    {
        'type': 'Text',
        'title': '通义千问输出',
        'value': '**日本海水珍珠项链**\n1. 珍贵的日本海水珍珠，以其优良的光泽和圆润度闻名，品质卓越，彰显高雅气质。\n2. 精心挑选每一颗珍珠，确保色泽均匀，表面光滑无瑕，闪耀着迷人的珍珠光泽。\n3. 独特的项链设计，将大海的浪漫与珍珠的温婉完美融合，无论是日常搭配还是特殊场合，都能展现女性的尊贵与品味。'
        }
    """
    try:
        product = Product.objects.filter(is_deleted=False, product_id=product_id).first()
        if not product:
            return IResponse(code=400, message="data not found")
        data = ProductForHandCardSearchSer(instance=product).data
        text = []
        text.append(f"名称：{data.get('name')}")
        if data.get("origin"):
            text.append(f"产地：{data.get('origin')}")
        if data.get("style"):
            text.append(f"款式：{data.get('style')}")
        # 分类
        _c = None
        category = data.get("category", [])
        if category and len(category) >= 2:
            _c = category[1].get("name", "")
        if _c:
            text.append(f"类别：{_c}")
        full_text = ",".join(text)
        # ask AI
        headers = {
            "VECTORVEIN-API-KEY": settings.AI_API_KEY,
            "VECTORVEIN-API-VERSION": settings.AI_VERSION,
        }
        payload = {
            "wid": settings.AI_WID,
            "output_scope": "output_fields_only",
            "wait_for_completion": True,
            "input_fields": [
                {
                    "node_id": settings.AI_NODE_ID,
                    "field_name": "商品信息",
                    "value": full_text,
                }
            ],
        }
        response = requests.post(settings.AI_URL, headers=headers, json=payload, timeout=50)
        if response:
            json_data = response.json()
            val = ""
            if json_data.get("data"):
                val = json_data["data"][0].get("value")
            return IResponse(data=val[:250])
        else:
            return IResponse(code=500, message=response.status_code)
    except Exception as e:
        logger.error(f"ask ai: {product_id}--{str(e)}")
        return IResponse(code=500, message=str(e))


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def AI_generate_precautions_view(request, product_id):
    try:
        product = Product.objects.filter(is_deleted=False, product_id=product_id).first()
        if not product:
            return IResponse(code=400, message="data not found")
        data = ProductForHandCardSearchSer(instance=product).data
        text = []
        text.append(f"名称：{data.get('name')}")
        if data.get("origin"):
            text.append(f"产地：{data.get('origin')}")
        if data.get("style"):
            text.append(f"款式：{data.get('style')}")
        # 分类
        _c = None
        category = data.get("category", [])
        if category and len(category) >= 2:
            _c = category[1].get("name", "")
        if _c:
            text.append(f"类别：{_c}")
        full_text = ",".join(text)
        v_client = VectorveinClient()
        response = v_client.ai_generate_precautions(full_text)
        if response:
            json_data = response.json()
            val = ""
            if json_data.get("data"):
                val = json_data["data"][0].get("value")
            return IResponse(data=val[:250])
        else:
            return IResponse(code=500, message=response.status_code)
    except Exception as e:
        logger.error(f"ask ai: {product_id}--{str(e)}")
        return IResponse(code=500, message=str(e))


class HandCardView(OPAPIView):
    need_format_resource_name = True
    fronted_page = "商品列表-手卡"
    resource_name = "手卡"

    permission_classes = [IsAuthenticated]

    def post(self, request, product_id):
        """
        新增手卡
        """
        try:
            product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="data not found")

        if getattr(product, "handcard", None):
            return IResponse(code=400, message="data existed")

        data = request.data
        data["product"] = product_id

        serializer = HandCardCreateSerializer(data=data)
        if serializer.is_valid():
            instance = serializer.save()
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=instance.product_id,
                    model=HandCard,
                    describe=f"新增了手卡:{product_id}.",
                    raw_object=instance,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass
            return IResponse(code=200)
        return IResponse(code=400, message=str(serializer.errors))

    def get(self, request, product_id):
        """
        查看手卡预览
        """
        try:
            handcard = HandCard.objects.filter(product_id=product_id, product__is_deleted=False).select_related("product")
            if not handcard:
                return IResponse(code=400, message="handcard not found")
            handcard_data = HandCardDetailSerializer(handcard.first(), context={"current_user": self.current_user}).data
            return IResponse(data=handcard_data)
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def put(self, request, product_id):
        """
        编辑手卡
        """
        try:
            product = Product.objects.get(product_id=product_id, is_deleted=False, handcard__isnull=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="handcard not found")

        raw_instance = product.handcard
        data = request.data
        data["product"] = product_id
        serializer = HandCardCreateSerializer(instance=raw_instance, data=data, partial=True)
        if serializer.is_valid():
            instance = serializer.save()
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=instance.product_id,
                    model=HandCard,
                    describe=f"修改了手卡:{product_id}.",
                    raw_object=raw_instance,
                    new_object=instance,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(code=200)
        return IResponse(code=400, message=str(serializer.errors))


class DownloadHandCardView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "商品列表-手卡-下载"
    resource_name = "手卡-下载"

    permission_classes = [IsAuthenticated]

    def get(self, request, product_id):
        try:
            handcard = HandCard.objects.filter(product_id=product_id, product__is_deleted=False).select_related("product")
            if not handcard:
                return IResponse(code=400, message="handcard not found")
            handcard_data = HandCardDetailSerializer(handcard.first(), context={"current_user": self.current_user}).data

            language = request.headers.get("Language", "zh")

            if language == "en":
                convert_data = convert_ser_data(handcard_data)

                translated_content = json_content_translation_replacement(convert_data)

                ppt_file = generate_en_pptx([translated_content])
            else:
                ppt_file = generate_pptx([handcard_data])
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=product_id,
                    model=HandCard,
                    describe=f"下载了手卡:{product_id}.",
                    raw_object=handcard,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return FileResponse(ppt_file, filename=f"手卡_{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.pptx", as_attachment=True)
        except Exception as e:
            print(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


@operate_log_decorator("批量下载手卡", "批量下载手卡")
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def bulk_download_handcard_view(request):
    """
    批量下载手卡
    """
    product_ids = request.data.get("product_ids")
    assert product_ids and len(product_ids) <= 500, "product_ids required and len < 500"
    try:
        handcard = HandCard.objects.filter(product_id__in=product_ids, product__is_deleted=False).select_related("product")
        if not handcard:
            return IResponse(code=400, message="handcard not found")
        handcard_data = HandCardDetailSerializer(handcard, many=True, context={"current_user": request.user}).data

        language = request.headers.get("Language", "zh")
        if language == "en":
            task = generate_en_pptx_async.delay(handcard_data)
            re_data = {"task_id": task.id}
        else:
            task = generate_pptx_async.delay(handcard_data)
            re_data = {"task_id": task.id}

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=None,
                model=HandCard,
                describe=f"批量下载了手卡:{product_ids}.",
                raw_object=None,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse(data=re_data)
    except Exception as e:
        print(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=500, message=str(e))


class OPCostPriceHistoryView(OPAPIView):
    """
    成本价列表
    """

    def get(self, request: Request):
        mod_histories = ProductReviewedCostPrice.objects.filter(product__is_deleted=False, product__state__in=[1, 4, 5]).order_by("-id")

        re_data, _, _ = custom_django_filter(
            request,
            mod_histories,
            ModHistoryCostPriceFilterSet,
            OPProductModHistoryListSer,
        )
        return IResponse(re_data)


class OPCostPriceTrendView(OPAPIView):
    def get(self, request: Request, product_id: int):
        params = request.query_params.copy()

        histories = ProductReviewedCostPrice.objects.filter(product_id=product_id, product__is_deleted=False)
        filter_set = CostPriceTrendFilterSet(params, queryset=histories)
        qs = filter_set.qs
        data_range = json.loads(filter_set.data.get("create_date"))

        items = [
            {
                "date": timezone.make_aware(timezone.make_naive(obj.create_date)),
                "cost_price": obj.cost_price,
            }
            for obj in qs
        ]

        sku_id = filter_set.data.get("sku_id")
        if not sku_id:
            # 按照商品最低成本价计算
            grouped_data = {}
            for item in items:
                key = item["date"].date()
                if key not in grouped_data:
                    grouped_data[key] = {
                        "date": item["date"].strftime(DATE_FORMAT),
                        "cost_price": item["cost_price"],
                    }
                else:
                    if item["cost_price"] < grouped_data[key]["cost_price"]:
                        grouped_data[key] = {
                            "date": item["date"].strftime(DATE_FORMAT),
                            "cost_price": item["cost_price"],
                        }

            grouped_data_vals = sorted([{"date": val["date"], "cost_price": val["cost_price"]} for val in grouped_data.values()], key=lambda x: x["date"])

            data_date_list = [val["date"] for val in grouped_data_vals]

            data_date_list.extend(data_range)
            # 找出最小和最大的日期
            min_date, max_date = min(data_date_list), max(data_date_list)
            min_qs = (
                ProductReviewedCostPrice.objects.filter(
                    product_id=product_id,
                    product__is_deleted=False,
                    create_date__lte=min_date,
                )
                .values("product")
                .annotate(min_cost_price=Min("cost_price"))
            )
            #
            min_cost_price = min_qs[0]["min_cost_price"] if min_qs else 0
            # 填充数据
            final_data = price_fill_data(min_date, max_date, grouped_data_vals, min_cost_price)
        else:
            # 根据日期分组，选择时间最新的数据
            grouped_data = {}
            for item in items:
                key = item["date"].date()
                if key not in grouped_data:
                    grouped_data[key] = item
                else:
                    if item["date"] > grouped_data[key]["date"]:
                        grouped_data[key] = item

            # 处理成日期,date
            data_values = sorted([{"date": val["date"].strftime(DATE_FORMAT), "cost_price": val["cost_price"]} for val in grouped_data.values()], key=lambda x: x["date"])

            data_date_list = [val["date"] for val in data_values]

            data_date_list.extend(data_range)
            # 找出最小和最大的日期
            min_date, max_date = min(data_date_list), max(data_date_list)

            min_qs = ProductReviewedCostPrice.objects.filter(
                product_id=product_id,
                product__is_deleted=False,
                sku_id=sku_id,
                create_date__lte=min_date,
            ).last()
            #
            min_cost_price = min_qs.cost_price if min_qs else 0
            # 填充数据
            final_data = price_fill_data(min_date, max_date, data_values, min_cost_price)

        return IResponse(data=final_data)


class HistoryPriceListView(OPAPIView):
    def get(self, request: Request):
        target = HistoryPrice.objects.select_related("product", "sku").filter(product__is_deleted=False)

        re_data, _, _ = custom_django_filter(
            request,
            target,
            HistoryPriceListFilterSet,
            HistoryPriceListSer,
        )
        return IResponse(data=re_data)


class OPHistoryPriceTrendView(OPAPIView):
    @staticmethod
    def fetch_latest_histories(histories, product_id, sku_id=None, author_id=None, min_date=None):
        base_filter = {
            "product__product_id": product_id,
            "product__is_deleted": False,
            "create_date__lte": min_date,
        }
        if sku_id:
            base_filter["sku__sku_id"] = sku_id
        if author_id:
            base_filter["author_id"] = author_id

        history_price_window = Window(expression=RowNumber(), partition_by=F("author"), order_by=F("create_date").desc())
        latest_histories = (
            histories.filter(**base_filter)
            .annotate(row_number=history_price_window)
            .filter(row_number=1)
            .values(
                "author_id",
                "author__name",
                "history_price",
            )
        )
        return latest_histories

    @staticmethod
    def process_author_qs(re_data, author_qs, min_date, max_date):
        for history_obj in author_qs:
            min_history_price = history_obj["history_price"] or 0
            final_data = price_fill_data(min_date, max_date, [], min_history_price, "history_price")
            re_data.append(
                {
                    "author_id": history_obj["author_id"],
                    "author_name": history_obj["author__name"],
                    "items": final_data,
                }
            )

    @staticmethod
    def process_prices_map(re_data, prices_map, data_range, product_id, compare_field="history_price", sku_id=None):
        for author_info, items in prices_map.items():
            author_id, author_name = author_info.split(":")
            grouped_data = {}
            grouped_data_vals = []
            for item in items:
                if compare_field == "history_price":
                    # 没有sku_id默认最小
                    key = item["date"].date()
                    if key not in grouped_data:
                        grouped_data[key] = {
                            "date": item["date"].strftime(DATE_FORMAT),
                            "history_price": item["history_price"],
                        }
                    else:
                        if item["history_price"] < grouped_data[key]["history_price"]:
                            grouped_data[key] = {
                                "date": item["date"].strftime(DATE_FORMAT),
                                "history_price": item["history_price"],
                            }

                    grouped_data_vals = sorted(
                        [
                            {
                                "date": val["date"],
                                "history_price": val["history_price"],
                            }
                            for val in grouped_data.values()
                        ],
                        key=lambda x: x["date"],
                    )
                else:
                    # 根据日期分组，选择时间最新的数据
                    key = item["date"].date()
                    if key not in grouped_data:
                        grouped_data[key] = item
                    else:
                        if item["date"] > grouped_data[key]["date"]:
                            grouped_data[key] = item

                    # 处理成日期,date
                    grouped_data_vals = sorted(
                        [
                            {
                                "date": val["date"].strftime(DATE_FORMAT),
                                "history_price": val["history_price"],
                            }
                            for val in grouped_data.values()
                        ],
                        key=lambda x: x["date"],
                    )

            data_date_list = [val["date"] for val in grouped_data_vals]
            data_date_list.extend(data_range)
            min_date, max_date = min(data_date_list), max(data_date_list)

            min_qs_filter = {
                "product__product_id": product_id,
                "product__is_deleted": False,
                "create_date__lte": min_date,
                "author_id": author_id,
            }
            if sku_id:
                min_qs_filter["sku__sku_id"] = sku_id

            min_qs = HistoryPrice.objects.filter(**min_qs_filter).last()
            min_history_price = min_qs.history_price if min_qs else 0

            final_data = price_fill_data(min_date, max_date, grouped_data_vals, min_history_price, "history_price")

            re_data.append(
                {
                    "author_id": author_id,
                    "author_name": author_name,
                    "items": final_data,
                }
            )

    def get(self, request: Request, product_id: int):
        params = request.query_params.copy()
        histories = (
            HistoryPrice.objects.filter(
                product__is_deleted=False,
                product__product_id=product_id,
                author__isnull=False,
            )
            .select_related("author")
            .only(
                "create_date",
                "history_price",
                "author__author_id",
                "author__name",
            )
        )

        filter_set = HistoryPriceTrendFilterSet(params, queryset=histories)
        qs = filter_set.qs
        data_range = json.loads(filter_set.data.get("create_date"))

        prices_map = {}
        for obj in qs:
            author_id = obj.author_id
            author_name = obj.author.name
            author_key = f"{author_id}:{author_name}"

            _data = {
                "date": timezone.make_aware(timezone.make_naive(obj.create_date)),
                "history_price": obj.history_price,
            }

            if author_key not in prices_map:
                prices_map[author_key] = [_data]
            else:
                prices_map[author_key].append(_data)

        sku_id = filter_set.data.get("sku_id")
        author_id = filter_set.data.get("author_id")

        re_data = []

        if not sku_id:
            if not prices_map:
                min_date, max_date = data_range
                author_qs = self.fetch_latest_histories(histories, product_id, author_id=author_id, min_date=min_date)
                self.process_author_qs(re_data, author_qs, min_date, max_date)
            else:
                self.process_prices_map(re_data, prices_map, data_range, product_id)
        else:
            if not prices_map:
                min_date, max_date = data_range
                author_qs = self.fetch_latest_histories(histories, product_id, sku_id=sku_id, author_id=author_id, min_date=min_date)
                self.process_author_qs(re_data, author_qs, min_date, max_date)
            else:
                self.process_prices_map(re_data, prices_map, data_range, product_id, sku_id)
        return IResponse(data=re_data)


class CostItemConfigAPIView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "商品管理-成本项配置"
    resource_name = "成本项配置"

    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            data = request.data
            data["create_user"] = self.current_user.user_id
            with transaction.atomic():
                serializer = CostItemConfigSerializer(data=data)
                if serializer.is_valid():
                    serializer.save()
                    return IResponse(serializer.data)
                # raise FieldsError(serializer.errors)
                return IResponse(message=str(FieldsError(serializer.errors)), code=400)
        except ValidationError as e:
            return IResponse(code=400, message=e.args[0])
        except Exception as e:
            return IResponse(message=str(e), code=500)

    def get(self, request):
        re_data, _, _ = custom_django_filter(request, CostItemConfig, CostItemConfigFilter, CostItemConfigListSerializer)
        return IResponse(data=re_data)


class CostItemConfigDetailAPIView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "商品管理-成本项配置-详情"
    resource_name = "成本项配置-详情"

    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        try:
            return CostItemConfig.objects.get(pk=pk)
        except CostItemConfig.DoesNotExist:
            return None

    def get(self, request, pk):
        cfg = self.get_object(pk)
        if not cfg:
            return IResponse(code=400, message="data not found")
        re_data = CostItemConfigDetailSerializer(instance=cfg).data
        return IResponse(data=re_data)

    def put(self, request, pk):
        cfg = self.get_object(pk)
        if not cfg:
            return IResponse(code=400, message="data not found")
        if cfg.status in ["IE", "ED"]:
            return IResponse(code=400, message="this state can't edit")

        try:
            with transaction.atomic():
                serializer = CostItemConfigSerializer(cfg, data=request.data)
                if serializer.is_valid():
                    serializer.save()
                    return IResponse(serializer.data)
                return IResponse(message=str(FieldsError(serializer.errors)), code=400)
        except ValidationError as e:
            return IResponse(code=400, message=e.args[0])
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            print(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(message=str(e), code=500)

    def delete(self, request, pk):
        cfg = self.get_object(pk)
        if not cfg:
            return IResponse(code=400, message="data not found")
        cfg.is_deleted = True
        cfg.save()
        return IResponse(code=200)


class OPSKUInventoryChangeReviewView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "库存管理"
    resource_name = "库存审核"

    def get(self, request: Request):
        if self.current_user.visible_supplier:
            return IResponse(data={"count": 0, "total_pages": 1, "current_page": 1, "data": []})

        target = SKUInventoryReviewRecord.objects.select_related(
            "product",
            "sku",
        ).order_by("-create_date")
        re_data, _, _ = custom_django_filter(
            request,
            target,
            OPSKUInventoryReviewRecordFilterSet,
            OPSKUInventoryReviewRecordListSer,
        )

        return IResponse(data=re_data)

    @staticmethod
    def change_inventory(record, sku):
        if record.action_type == "increase":
            sku.physical_inventory = F("physical_inventory") + record.change_inventory
        else:
            sku.physical_inventory = F("physical_inventory") - record.change_inventory
        sku.safety_inventory = record.after_safety_inventory
        sku.skip_history_when_saving = True
        sku.save(update_fields=["physical_inventory", "safety_inventory"])
        sku.refresh_from_db()

        record.after_change_inventory = sku.physical_inventory

        # 判断商品库存信息
        try:
            # 发送运营商和供应商信息
            config = SystemConfig.objects.filter(code=SystemConfig.STOCK_WARNING_CONF, enable=True).first()
            if config and "inventory_warning_enable" in config.extra and config.extra["inventory_warning_enable"] is True:
                inventory_threshold = config.extra.get("inventory_threshold")

                product = sku.product

                if inventory_threshold and (product.physical_inventory or 0) <= int(inventory_threshold):
                    # 创建消息通知
                    notifications_data = {
                        "product_id": product.product_id,
                        "company_id": product.company.company_id,
                        "notify_type": "stock_warning",
                        "operation_type": "stock_warning",
                        "content": f"现货库存已经低于{inventory_threshold}",
                    }
                    ceate_notifications.delay(notifications_data, ["SP", "OP"])
        except Exception as e:
            logger.warning(f"现货库存预警发送信息失败, {e}")

    @staticmethod
    def simulate_inventory_change(record):
        if record.action_type == "increase":
            record.after_change_inventory = record.before_change_inventory + record.change_inventory
        else:
            record.after_change_inventory = record.before_change_inventory - record.change_inventory

    @staticmethod
    def finalize_record(record, remark, current_user):
        record.remark = remark
        record.review_user = current_user
        record.review_date = timezone.now()

    def patch(self, request: Request):
        record_id = request.data.get("id")
        state = request.data.get("state")
        remark = request.data.get("remark") or ""

        if state not in [1, 2]:
            return IResponse(code=400, message="审核结果类型错误,请刷新后重试")

        try:
            record = SKUInventoryReviewRecord.objects.get(pk=record_id)
        except SKUInventoryReviewRecord.DoesNotExist:
            return IResponse(code=400, message="申请记录不存在,请刷新列表后重试")

        if record.state != 0:
            return IResponse(code=400, message="该记录已审核过,无法重新审核")

        with transaction.atomic():
            sku = record.sku
            before_change_inventory = copy.deepcopy(sku.physical_inventory)
            record.before_change_inventory = before_change_inventory

            if state == 1:
                self.change_inventory(record, sku)
                record.state = 1
            else:
                self.simulate_inventory_change(record)
                record.state = 2

            self.finalize_record(record, remark, self.current_user)
            record.save()

        try:
            set_log_params(
                request,
                resource_id=record_id,
                model=SKUInventoryReviewRecord,
                describe=f"库存审核, 结果【{record.get_state_display()}】",
                operate_content=f"库存审核, 结果【{record.get_state_display()}】, 商品:{record.sku.product}, SKU:{record.sku}",
                is_success_input=True,
            )
            # 创建消息通知
            notifications_data = {
                "product_id": record.sku.product.product_id,
                "company_id": record.sku.product.company.company_id,
                "notify_type": "stock",
                "operation_type": "stock_approved" if record.get_state_display() == "通过" else "stock_rejected",
                "content": f"商品[{record.sku.product.product_id}]库存审核：{record.get_state_display()}",
            }
            ceate_notifications.delay(notifications_data, user_type_list=["SP"])

        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class OPSKUInventoryChangeReviewDownloadView(OPAPIView):
    def get(self, request: Request):
        target = SKUInventoryReviewRecord.objects.select_related(
            "product",
            "sku",
        ).order_by("-create_date")
        re_data, _, _ = custom_django_filter(
            request,
            target,
            OPSKUInventoryReviewRecordFilterSet,
            OPSKUInventoryReviewRecordListDownloadSer,
        )

        download_data = []
        for idx, data in enumerate(re_data["data"]):
            tmp_data = {"序号": idx + 1}
            for k, v in OPSKUInventoryReviewRecordListTmpl.items():
                if k == "category":
                    tmp_category_list = ["一级分类", "二级分类", "三级分类"]
                    for c_idx, category_value in enumerate(data.pop("category")):
                        tmp_data[f"{tmp_category_list[c_idx]}ID"] = category_value["id"]
                        tmp_data[f"{tmp_category_list[c_idx]}名称"] = category_value["name"]

                    continue

                tmp_data[v] = data.get(k, "")

            download_data.append(tmp_data)
        filename = f"库存修改审核{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
        byte_buffer = get_excel_async("库存修改审核", download_data, image_columns=[4], is_internal=True)
        if byte_buffer:
            byte_buffer.seek(0)

        return FileResponse(byte_buffer, filename=filename, as_attachment=True)


class OPProductStateChangeReviewView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "上下架管理"
    resource_name = "上下架审核"

    def get(self, request: Request):
        if self.current_user.visible_supplier:
            return IResponse(data={"count": 0, "total_pages": 1, "current_page": 1, "data": []})

        records = ProductStateReviewRecord.objects.select_related("product", "reason").order_by("-create_date")

        re_data, _, _ = custom_django_filter(
            request,
            records,
            OPProductStateReviewRecordFilterSet,
            OPProductStateReviewRecordListSer,
        )

        return IResponse(data=re_data)

    def patch(self, request: Request):
        record_id = request.data.get("id")
        state = request.data.get("state")
        remark = request.data.get("remark") or ""

        if state not in [1, 2]:
            return IResponse(code=400, message="审核状态错误,请刷新后重试")

        try:
            record = ProductStateReviewRecord.objects.get(pk=record_id)
        except ProductStateReviewRecord.DoesNotExist:
            return IResponse(code=400, message="上下架记录不存在,请刷新后重试")

        if record.state != 0:
            return IResponse(code=400, message="该记录已审核过, 无法重新审核")

        with transaction.atomic():
            product = record.product

            if state == 1:
                if record.review_type == 1:
                    # 上架
                    if product.state in [0, 4, 5]:
                        return IResponse(code=400, message="当前商品正在审核，无法上架")
                    if product.state == 2:
                        return IResponse(code=400, message="当前商品三级审核不通过，无法上架")
                    product.state = 1
                else:
                    # 下架
                    product.state = 3
                product.listing_delisting_reason = record.reason.name
                product.state_remark = record.remark
                product.save(update_fields=["state", "listing_delisting_reason", "state_remark"])

            # 记录保存
            record.state = state
            record.review_user = self.current_user
            record.review_date = timezone.now()
            record.remark = remark
            record.save()

        try:
            set_log_params(
                request,
                resource_id=record_id,
                model=ProductStateReviewRecord,
                describe=f"库存审核, 结果【{record.get_state_display()}】",
                operate_content=f"库存审核, 结果【{record.get_state_display()}】. 商品:{product},当前状态:{product.get_state_display()}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def post(self, request: Request):
        product_id = request.data.get("product_id")
        remark = request.data.get("remark") or ""
        if not product_id:
            return IResponse(code=400, message="missing params")

        try:
            reason = ProductStateReason.objects.filter(name="违规下架").first()
            if not reason:
                return IResponse(code=400, message="理由不存在或未启用,请刷新页面后重试")
        except ProductStateReason.DoesNotExist:
            return IResponse(code=400, message="理由不存在或未启用,请刷新页面后重试")

        try:
            product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="商品不存在")

        if product.state == 3:
            return IResponse(code=400, message="商品已经下架状态")
        elif product.state != 1:
            return IResponse(code=400, message="商品未在上架状态,无法提交下架申请")

        if ProductStateReviewRecord.objects.filter(product=product, review_type=2, state=0):
            return IResponse(code=400, message="有相同的申请正在待审核,无需重新提交申请")

        record = ProductStateReviewRecord(
            product=product,
            review_type=2,
            reason=reason,
            remark=remark,
            create_user=self.current_user,
        )
        record.save()
        product.state = 3
        product.listing_delisting_reason = record.reason.name
        product.state_remark = record.remark
        product.save(update_fields=["state", "listing_delisting_reason", "state_remark"])

        # 记录保存
        record.state = 1
        record.review_user = get_default_system_user()
        record.review_date = timezone.now()
        record.remark = remark
        record.review_remark = "系统自动审核"
        record.save()
        try:
            set_log_params(
                request,
                resource_id=record.id,
                model=ProductStateReviewRecord,
                describe=f"违规下架, 结果【{record.get_state_display()}】",
                operate_content=f"违规下架, 结果【{record.get_state_display()}】. 商品:{product},当前状态:{product.get_state_display()}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass
        return IResponse()


class OPProductStateChangeReviewDownloadView(OPAPIView):
    def get(self, request: Request):
        records = ProductStateReviewRecord.objects.select_related("product", "reason").order_by("-create_date")

        re_data, _, _ = custom_django_filter(
            request,
            records,
            OPProductStateReviewRecordFilterSet,
            OPProductStateReviewRecordListDownloadSer,
        )

        download_data = []
        for idx, data in enumerate(re_data["data"]):
            tmp_data = {"序号": idx + 1}
            for k, v in OPProductStateChangeReviewListTmpl.items():
                if k == "category":
                    tmp_category_list = ["一级分类", "二级分类", "三级分类"]
                    for c_idx, category_value in enumerate(data.pop("category")):
                        tmp_data[f"{tmp_category_list[c_idx]}ID"] = category_value["id"]
                        tmp_data[f"{tmp_category_list[c_idx]}名称"] = category_value["name"]

                    continue

                tmp_data[v] = data.get(k, "")

            download_data.append(tmp_data)
        filename = f"上下架审核{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
        byte_buffer = get_excel_async("上下架审核", download_data, image_columns=[4], is_internal=True)
        if byte_buffer:
            byte_buffer.seek(0)

        return FileResponse(byte_buffer, filename=filename, as_attachment=True)


class OPTempProductView(OPAPIView):
    """
    临时商品列表
    """

    need_format_resource_name = False
    fronted_page = "临时商品"
    resource_name = "临时商品"

    def get(self, request: Request):
        distributor_prefetch = Prefetch("distributor", queryset=Distributor.objects.only("distributor_id", "name"))
        temp_products = TempProduct.objects.prefetch_related(distributor_prefetch).order_by("state", "-order_date")

        re_data, _, _ = custom_django_filter(
            request,
            temp_products,
            TempProductFilterSet,
            TempProductListSer,
            need_serialize=True,
            force_order=False,
        )

        return IResponse(data=re_data)

    def merge_action(self, request, temp_product: TempProduct, post_data):
        """
        合并操作
        :return:
        """
        inner_sku_id = post_data.get("inner_sku_id")
        distributor_id = post_data.get("distributor_id")
        if not all([inner_sku_id, distributor_id]):
            raise APIViewException(err_message="缺少必填参数")
        try:
            distributor = Distributor.objects.get(distributor_id=distributor_id)
            inner_sku = StockKeepingUnit.objects.get(sku_id=inner_sku_id, become_history=False)
        except Distributor.DoesNotExist:
            raise APIViewException(err_message="分销商不存在,请刷新页面后重试")
        except StockKeepingUnit.DoesNotExist:
            raise APIViewException(err_message="系统sku不存在或已成为历史数据,请刷新页面后重试")

        if TempProductMap.objects.filter(sku=inner_sku, distributor=distributor).exists():
            raise APIViewException(err_message="已合并过该分销商SKU,请重新选择")
        inner_product = inner_sku.product

        with transaction.atomic():
            try:
                sub_product = SubProduct.objects.get(parent_product=inner_product, owner=distributor)
            except SubProduct.DoesNotExist:
                post_data = inner_product.__dict__
                post_data["parent_product"] = inner_product.pk
                post_data["owner"] = distributor.distributor_id
                post_data["create_user"] = self.current_user.user_id
                ser = MyProductCreateSer(data=post_data)
                if not ser.is_valid():
                    logger.warning(f"合并加入我的商品失败:{ser.errors}")
                    raise APIViewException(err_message="自动加入我的商品失败,请联系管理员或稍后重试")
                sub_product = ser.save()

            inner_spec_code = f"{inner_sku.spec_code}{distributor.letters}"
            p_map = TempProductMap(
                map_type="merge",
                ex_product_id=temp_product.product_id,
                ex_sku_id=temp_product.sku_id,
                sub_product=sub_product,
                sku=inner_sku,
                distributor=distributor,
                spec_code=inner_spec_code,
                shop_id=temp_product.shop_id,
            )
            p_map.save()

            # 更新所有的临时商品信息
            TempProduct.objects.filter(product_id=temp_product.product_id, sku_id=temp_product.sku_id).update(
                state=1,
                remark=f"相同product_id和SKU更新合并状态,原数据:{temp_product.pk}",
            )
        p_map.refresh_from_db()

        # 日志记录
        try:
            main_product = sub_product.parent_product
            set_log_params(
                request,
                resource_id=str(main_product.product_id),
                model=Product,
                describe=f"合并商品: {main_product}到抖店店铺:{temp_product.shop_name}, 抖店商品ID:{temp_product.product_id},",
                operate_content=f"商品:{main_product},SKU_ID:{inner_sku_id},新商编:{inner_spec_code}; 抖店商品ID:{temp_product.product_id},SKU_ID:{temp_product.sku_id},抖店原编号:{temp_product.raw_spec_code}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return p_map

    def claim_action(self, request, temp_product: TempProduct):
        raw_data = request.data.get("product_info")

        assert raw_data.get("skus") and raw_data.get("company_id"), "required: skus, company_id"

        distributor_id = raw_data.get("distributor_id")
        if not distributor_id:
            raise APIViewException(err_message="缺少分销商信息")
        try:
            distributor = Distributor.objects.get(distributor_id=distributor_id)
        except Distributor.DoesNotExist:
            raise APIViewException(err_message="分销商不存在,请刷新后重试")

        company_id = raw_data.pop("company_id")
        try:
            company = Company.objects.get(company_id=company_id)
        except Company.DoesNotExist:
            raise APIViewException(err_message="company_id invalid")

        raw_data["company"] = company.id
        # 创建人
        raw_data["create_user"] = self.current_user.user_id
        skus_data = raw_data.pop("skus", [])
        attr_options_data = raw_data.pop("attr_options", [])
        gifts = raw_data.pop("gifts", {})
        if gifts:
            raw_data["has_gift"], raw_data["gift_type"] = gifts.get("has_gift"), gifts.get("gift_type")

        if raw_data.get("address_id"):
            raw_data["address"] = raw_data.pop("address_id")
        company_code = company.code

        # 先校验再创建
        specs_list = raw_data.pop("spec_lists", [])
        with transaction.atomic():
            clean_specs(specs_list, skus_data, request)
            # product
            product_instance = common_create_product(raw_data, specs_list, request, ProductSerializer)
            # sku && spec options
            sku_data, skus_images = common_create_sku_and_spec_options(product_instance, skus_data, request, company_code)
            # product attr options
            attr_options_instance = common_create_product_attr_options(product_instance, attr_options_data)
            # product gift
            gift_type, gifts_instances = common_create_gift(product_instance, gifts, company.id)

            # 自动三级审核
            process = ProductReviewProcess.objects.create(product=product_instance)

            ProductReview.objects.bulk_create(
                [
                    ProductReview(
                        process=process,
                        product=product_instance,
                        process_level=level[0],
                        physical_inventory_exact=True,
                        quality_qualified=True,
                        price_reasonable=True,
                        remark="临时商品认领自动审核.",
                        create_user="system",
                    )
                    for level in ProductReview.PROCESS_LEVEL_CHOICES
                ]
            )

            product_instance.state = 1
            product_instance.skip_history_when_saving = True
            product_instance.save(update_fields=["state"])

            # 加入我的商品
            post_data = product_instance.__dict__
            post_data["parent_product"] = product_instance.pk
            post_data["owner"] = distributor.distributor_id
            post_data["create_user"] = self.current_user.user_id
            ser = MyProductCreateSer(data=post_data)
            if not ser.is_valid():
                logger.warning(f"合并加入我的商品失败:{ser.errors}")
                raise APIViewException(err_message="自动加入我的商品失败,请联系管理员或稍后重试")
            sub_product = ser.save()

            # 创建关联分销商货号数据
            ProductLinkDistributor(
                product=product_instance,
                distributor=distributor,
                code=sub_product.code,
                create_user=request.user.user_id,
            ).save()

            sku_info = sku_data[0]
            main_spec_code = sku_info["spec_code"]

            inner_spec_code = f"{main_spec_code}{distributor.letters}"
            inner_sku_id = sku_info["sku_id"]
            p_map = TempProductMap(
                map_type="claim",
                ex_product_id=temp_product.product_id,
                ex_sku_id=temp_product.sku_id,
                sub_product=sub_product,
                sku_id=inner_sku_id,
                distributor=distributor,
                spec_code=inner_spec_code,
                shop_id=temp_product.shop_id,
            )
            p_map.save()

            # 更新所有的临时商品信息
            TempProduct.objects.filter(product_id=temp_product.product_id, sku_id=temp_product.sku_id).update(
                state=1,
                remark=f"相同product_id和SKU更新合并状态,原数据:{temp_product.pk}",
            )

        # 处理临时商品
        handle_temp_product_images.delay(product_instance.pk, self.current_user.user_id)

        p_map.refresh_from_db()
        # 日志记录
        try:
            main_product = sub_product.parent_product
            set_log_params(
                request,
                resource_id=str(main_product.product_id),
                model=Product,
                describe=f"认领商品: {main_product}到抖店店铺:{temp_product.shop_name}, 抖店商品ID:{temp_product.product_id},",
                operate_content=f"商品:{main_product},SKU_ID:{inner_sku_id},新商编:{inner_spec_code}; 抖店商品ID:{temp_product.product_id},SKU_ID:{temp_product.sku_id},抖店原编号:{temp_product.raw_spec_code}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return p_map

    def put(self, request: Request):
        post_data = request.data
        uni_id = post_data.get("uni_id")
        action = post_data.get("action")

        if action not in ["claim", "merge"]:
            raise APIViewException(err_message="系统只支持合并和认领操作")

        try:
            temp_product = TempProduct.objects.get(uni_id=uni_id)
        except TempProduct.DoesNotExist:
            raise APIViewException(err_message="data not found")

        if temp_product.state != 0:
            raise APIViewException(err_message=f"该商品{temp_product.get_state_display()}, 无法继续操作")

        if action == "merge":
            p_map = self.merge_action(request, temp_product, post_data)
        else:
            p_map = self.claim_action(request, temp_product)

        edit_dy_product_spec_code.delay(p_map.pk)
        return IResponse()


class OpSkuCostPriceChangeView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "修改推广价"
    resource_name = "修改推广价"

    def post(self, request: Request):
        post_data = request.data
        product_id = post_data.get("product_id")

        skus_data = post_data.get("skus")

        if not isinstance(skus_data, list):
            raise APIViewException(err_message="sku格式错误")

        # 判断有没有修改推广价权限
        if not self.current_user.has_perm_with_codename(
            "backend:change_cost_price",
            self.current_user_type,
            self.current_client_code,
        ):
            raise NoPermissionException

        try:
            p_temp = Product.objects.get(product_id=product_id)
        except Product.DoesNotExist:
            raise APIViewException(err_message="商品已删除")
        if p_temp.state in [0, 4, 5]:
            raise APIViewException(err_message="审核中，请勿修改推广价")
        if p_temp.state == 3:
            raise APIViewException(err_message="商品未上架，请勿修改推广价")
        if p_temp.state == 2:
            process = ProductReviewProcess.objects.filter(product_id=p_temp.id, become_history=False).last()
            if process:
                history_process = ProductReview.objects.filter(process_id=process.process_id)
                for history_data in history_process:
                    if not all((history_data.physical_inventory_exact, history_data.quality_qualified, history_data.price_reasonable)):
                        raise APIViewException(err_message="请先完成三级审核")
        clean_data = []
        for sku_data in skus_data:
            if not isinstance(sku_data, dict):
                raise APIViewException(err_message="sku信息错误")
            after_change_cost_price = sku_data.get("after_change_cost_price")
            if not after_change_cost_price:
                continue
            sku_data["create_user"] = request.user
            clean_data.append(sku_data)

        # 提交幂等
        idempotent_key = md5(str(skus_data).encode()).hexdigest()
        if cache.get(f"apply_cost_price_{idempotent_key}"):
            raise APIViewException(err_message="点击太快啦!")
        cache.set(f"apply_cost_price_{idempotent_key}", True, 1)

        if not SKUCostPriceReviewRecord.objects.filter(product_id=product_id, state=0).exists():
            batch_map = {}
            for item in clean_data:
                item["batch_id"] = uuid.uuid4().hex
                if isinstance(item["sku_id"], str):
                    batch_map[item["batch_id"]] = [item["sku_id"]]
                else:
                    batch_map[item["batch_id"]] = item["sku_id"]
            create_ser = SKUCostPriceReviewRecordCreateSer(data=clean_data, many=True)
            if not create_ser.is_valid():
                raise FieldsError(create_ser.errors)

            with transaction.atomic():
                records = create_ser.save()

                for record in records:
                    sku = record.sku
                    product = record.product
                    if product.state in [1, 2]:
                        product.state = 4
                        product.productreview_set.filter(process__become_history=False, process_level="PRICE_REVIEW").update(become_history=True)
                    if float(sku.cost_price) >= float(record.after_change_cost_price):
                        record.action_type = "fall"
                    else:
                        record.action_type = "rise"
                    batch_list = []
                    for sku_item in batch_map[record.batch_id]:
                        batch_list.append(BatchSkuCostPriceRelate(batch_id=record.batch_id, sku_id=sku_item))
                    if batch_list:
                        BatchSkuCostPriceRelate.objects.bulk_create(batch_list)
                    record.save()
                    product.save()
                    try:
                        set_log_params(
                            request,
                            resource_id=product_id,
                            model=Product,
                            describe="修改sku推广价",
                            operate_content=f"修改推广价, 修改之前：{record.apply_cost_price}， 修改之后：{record.after_change_cost_price}, 商品:{record.sku.product}, SKU:{record.sku}",
                            is_success_input=True,
                        )
                    except Exception as e:
                        logger.warning(f"set_log_params catch error: {e}")
                        pass
            return IResponse()

        return IResponse(code=400, message="商品推广价还在审核中，请勿操作")


class OpSkuCostPriceChangeReviewView(OPAPIView):
    need_format_resource_name = False
    fronted_page = "审核推广价管理"
    resource_name = "审核推广价管理"

    def get(self, request):
        target = SKUCostPriceReviewRecord.objects.select_related(
            "product",
            "sku",
        ).order_by("-create_date")

        user_visible_supplier = self.current_user.visible_supplier
        if user_visible_supplier:
            target = target.filter(product__company__company_id__in=user_visible_supplier)

        re_data, _, _ = custom_django_filter(
            request,
            target,
            SPSkuCostPriceReviewRecordFilterSet,
            OPSkuCostPriceReviewRecordListSer,
        )
        # 统计待审核、已审核、未审核、已取消的数量
        review_counts = {
            "review_wait": 0,
            "review_pass": 0,
            "review_fail": 0,
            "review_cancel": 0,
        }

        if user_visible_supplier:
            review_qs_counts = (
                SKUCostPriceReviewRecord.objects.filter(
                    product__company__company_id__in=user_visible_supplier,
                )
                .values("state")
                .annotate(count=Count("id"))
                .values("state", "count")
            )
        else:
            review_qs_counts = SKUCostPriceReviewRecord.objects.values("state").annotate(count=Count("id")).values("state", "count")
        for qs_count in review_qs_counts:
            state = qs_count["state"]
            count = qs_count["count"]

            if state == 0:
                review_counts["review_wait"] += count
            elif state == 1:
                review_counts["review_pass"] += count
            elif state == 2:
                review_counts["review_fail"] += count
            else:
                review_counts["review_cancel"] += count
        re_data["review_counts"] = review_counts
        return IResponse(data=re_data)

    def post(self, request: Request):
        record_id = request.data.get("id")
        state = request.data.get("state")
        remark = request.data.get("remark") or ""

        if state not in [1, 2]:
            return IResponse(code=400, message="审核结果类型错误,请刷新后重试")

        try:
            record = SKUCostPriceReviewRecord.objects.get(pk=record_id)
            exist_other = SKUCostPriceReviewRecord.objects.filter(product_id=record.product_id, state=0)
            flag = len(exist_other) <= 1
        except SKUCostPriceReviewRecord.DoesNotExist:
            return IResponse(code=400, message="申请记录不存在")

        if record.state != 0:
            return IResponse(code=400, message="该记录已审核过,无法重新审核")

        if not remark:
            return IResponse(code=400, message="请填写备注")
        sku = record.sku
        try:
            sku = StockKeepingUnit.objects.get(
                sku_id=sku.sku_id,
                become_history=False,
                product__is_deleted=False,
            )
        except StockKeepingUnit.DoesNotExist:
            record.state = 3
            record.save()
            raise APIViewException(err_message="该SKU信息不存在或已成为历史")

        # try:
        #     p_temp = Product.objects.get(product_id=sku.product.product_id)
        # except Product.DoesNotExist:
        #     raise APIViewException(err_message="该商品不存在")
        # process = ProductReviewProcess.objects.filter(product_id=p_temp.id, become_history=False).first()
        # if process:
        #         record.state = 3
        #         record.save()
        #         raise APIViewException(err_message="正在进行三级审核，该审批已经过期")
        # if float(sku.cost_price) != float(record.apply_cost_price):
        #     record.state = 3
        #     record.save()
        #     raise APIViewException(err_message="原推广价已经变更，该审批已经失效")
        with transaction.atomic():
            if state == 1:
                sku.cost_price = record.after_change_cost_price
                sku.save()
                if flag and sku.product.state == 4:
                    sku.product.state = 1
                    sku.product.save()
                    sku.product.productreview_set.filter(process__become_history=False, process_level="PRICE_REVIEW").update(become_history=False)
                record.state = 1
            else:
                record.state = 2
                if sku.product.state == 4:
                    sku.product.state = 2
                    sku.product.save()

            record.remark = remark
            record.review_user = self.current_user
            record.review_date = timezone.now()
            record.save()

        record.state = record.get_state_display()
        record_info = OPSkuCostPriceReviewRecordListSer(record)
        if state == 1:
            batch_info = BatchSkuCostPriceRelate.objects.filter(batch_id=record.batch_id)
            if batch_info:
                for item in batch_info:
                    item.sku.cost_price = record.after_change_cost_price
                    item.sku.save()
                    ProductReviewedCostPrice.objects.create(
                        product=item.sku.product,
                        sku=item.sku,
                        old_cost_price=record.apply_cost_price,
                        cost_price=record.after_change_cost_price,
                        create_user=record.create_user_id,
                        create_date=record.create_date,
                    )
            insert_cost_price_change_table.delay(record_info.data)

            # 刷新分销价格
            configs = DistributorMarketProductPriceConfig.objects.filter(is_deleted=False)
            for config in configs:
                # flush_products_market_price.delay(config.min_cost_price, config.max_cost_price, config.markup_percentage, [sku.product_id])
                flush_products_distributor_market_price.delay(config.min_cost_price, config.max_cost_price, config.markup_percentage, [sku.product_id])

            # 触发同步到聚水潭逻辑
            product_id = sku.product.product_id
            sync_product_to_JST.delay(product_id)
            # 同步到erps
            sync_product_info_to_erp_product.delay(product_id)

        try:
            set_log_params(
                request,
                resource_id=record.product_id,
                model=Product,
                describe=f"推广价审核, 结果【{record.get_state_display()}】",
                operate_content=f"推广价审核, 结果【{record.get_state_display()}】, 商品:{record.sku.product}, SKU:{record.sku}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        # feishu_insert_product_price_change_notification.delay(record.id)
        feishu_insert_product_price_change_notification.delay(record.id)
        return IResponse()


class OPQAQuestionEnumView(OPAPIView):
    def get(self, request: Request):
        type_name = request.query_params.get("type_name")
        if not type_name:
            return IResponse(code=400, message="invalid params")

        q_type = QAReviewQuestionType.objects.filter(name=type_name, enable=True).last()
        if not q_type:
            return IResponse()
        questions = q_type.get_questions_list_with_cache()
        re_data = {
            "type_id": q_type.pk,
            "type_name": q_type.name,
            "type_short_desc": q_type.short_desc,
            "questions": questions,
        }
        return IResponse(data=re_data)


class OPSubProductAliasCodeChangeView(OPAPIView):
    """
    更改分销商别称货号接口
    """

    def put(self, request: Request):
        original_code = request.data.get("original_code")
        alias_code = request.data.get("alias_code")

        if not original_code or not alias_code:
            return IResponse(code=400, message="缺少货号或别称货号")

        if original_code == alias_code:
            return IResponse(code=400, message="相同货号不处理")

        #
        sub_product = SubProduct.objects.filter(code=original_code).first()
        if not sub_product:
            return IResponse(code=400, message="商品不存在")

        if sub_product.alias_code == alias_code:
            return IResponse(code=400, message="别称货号与上次相同")

        sub_product.alias_code = alias_code
        alias_code_history = sub_product.alias_code_history
        changes = {
            "alias_code": alias_code,
            "create_time": convert_2_aware_time(timezone.now()),
            "real_name": self.current_user.real_name,
            "user_id": self.current_user.user_id,
        }

        if not alias_code_history:
            sub_product.alias_code_history = [changes]
        else:
            sub_product.alias_code_history.append(changes)
        sub_product.save(update_fields=["alias_code", "alias_code_history"])
        return IResponse()


class FrontProductCategoryView(OperateLogAPIViewMixin):
    """
    商品分类数据
    """

    fronted_page = "前台分类管理"
    resource_name = "前台分类"
    need_format_resource_name = True

    permission_classes = [AllowAny]

    def get(self, request: Request):
        # 获取前台类目树结构
        try:
            raw_data = request.query_params
            if raw_data:
                category_qs = FrontProductCategoryList.objects.filter(is_deleted=False).only("id", "name", "parent_id", "order", "is_big_image", "image_url").order_by("order")
                qs = FrontProductCategoryFilterSet(raw_data, category_qs).qs
                data = [
                    {
                        "id": obj.id,
                        "name": obj.name,
                        "order": obj.order,
                        "parent_id": obj.parent_id,
                        "is_big_image": obj.is_big_image,
                        "image_url": obj.image_url,
                    }
                    for obj in qs
                ]
                data = sorted(data, key=lambda x: x["order"])
            else:
                category_qs = FrontProductCategoryList.objects.filter(is_deleted=False).values("id", "name", "parent_id", "order", "is_big_image", "image_url").order_by("order")
                data = list_to_tree(category_qs)
            return IResponse(data=data)
        except Exception as e:
            print(f"{e}-{traceback.format_exc()}")
            return IResponse()

    def post(self, request: Request):
        # 新建类目
        if request.auth is None:
            return IResponse(code=401, message="身份认证信息未提供。")
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")

        name = request.data.get("name")
        new_parent_id = request.data.get("parent_id")

        if not name:
            return IResponse(code=400, message="请输入分类名称")

        if new_parent_id is None:
            parent = None
            level = 1
            order = FrontProductCategoryList.objects.filter(parent_id__isnull=True).count() + 1
        else:
            try:
                parent = FrontProductCategoryList.objects.get(id=new_parent_id, is_deleted=False)
                level = parent.level + 1
                if level == 4:
                    return IResponse(code=400, message="最多添加三级分类")
                order = FrontProductCategoryList.objects.filter(level=level).count() + 1
            except FrontProductCategoryList.DoesNotExist:
                return IResponse(code=400, message="data not found")

            if parent.level == 4:
                return IResponse(code=400, message="系统仅支持四级分类")

        instance = FrontProductCategoryList(
            name=name,
            parent=parent,
            level=level,
            order=order,
            create_user=request.user.user_id,
        )
        instance.save()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(instance.id),
                model=FrontProductCategoryList,
                describe=f"新增了前台分类. 分类ID:{instance.id}",
                is_success_input=True,
            )
        except Exception as e:
            print(traceback.format_exc())
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def put(self, request, category_id):
        # 更新类目
        if request.auth is None:
            return IResponse(code=401, message="身份认证信息未提供。")
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")
        data = request.data
        try:
            category = FrontProductCategoryList.objects.get(pk=category_id, is_deleted=False)
        except FrontProductCategoryList.DoesNotExist:
            return IResponse(code=400, message="data not found")
        if data.get("name") is not None:
            category.name = data["name"]
        if data.get("order") is not None:
            category.order = data["order"]
        if data.get("parent_id") is not None and data["parent_id"] != category.parent_id:
            try:
                parent_category = FrontProductCategoryList.objects.get(pk=data["parent_id"], is_deleted=False)
            except FrontProductCategoryList.DoesNotExist:
                return IResponse(code=400, message="所修改的父级分类不存在")
            if parent_category.level != category.parent.level:
                return IResponse(code=400, message="仅支持同级分类修改")
            category.parent_id = data["parent_id"]
        if category.level == 3 and data.get("image_url") is not None:
            category.image_url = data.get("image_url", "")
        if category.level == 2 and data.get("is_big_image") is not None:
            category.is_big_image = data.get("is_big_image", False)
        category.update_user = request.user.user_id
        category.update_date = timezone.now()
        category.save()
        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(category_id),
                model=FrontProductCategoryList,
                describe=f"跟新了前台分类. 分类ID:{category_id}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def delete(self, request: Request, category_id: int):
        # 删除类目
        if request.auth is None:
            return IResponse(code=401, message="身份认证信息未提供。")
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")
        try:
            category = FrontProductCategoryList.objects.get(pk=category_id, is_deleted=False)
        except FrontProductCategoryList.DoesNotExist:
            return IResponse(code=400, message="data not found")

        if FrontProductCategoryList.objects.filter(parent=category, is_deleted=False).exists():
            return IResponse(code=400, message="请先删除子级分类")

        category.is_deleted = True
        category.update_user = request.user.user_id
        category.save()

        # 删除缓存
        # cache_key = f"front_category_{category_id}"
        # cache.delete(cache_key)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(category_id),
                model=FrontProductCategoryList,
                describe=f"删除了前台分类. 分类ID:{category_id}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class FrontProductCategoryRelatedView(OperateLogAPIViewMixin):
    """
    商品分类数据
    """

    fronted_page = "前台分类管理"
    resource_name = "前台分类"
    need_format_resource_name = True

    permission_classes = [IsAuthenticated]

    def post(self, request):
        # 类目排序
        data = request.data
        with transaction.atomic():
            for fc_item in data.get("front_category", []):
                try:
                    category = FrontProductCategoryList.objects.get(pk=fc_item["category_id"], is_deleted=False)
                except FrontProductCategoryList.DoesNotExist:
                    return IResponse(code=400, message="data not found")
                category.order = fc_item["order"]
                category.update_user = request.user.user_id
                category.update_date = timezone.now()
                category.save()
        return IResponse()

    def get(self, request, category_id):
        # 获取关联后台分类
        data = {}
        try:
            category = FrontProductCategoryList.objects.get(pk=category_id, is_deleted=False)
        except FrontProductCategoryList.DoesNotExist:
            return IResponse(code=400, message="data not found")
        data["style"] = category.style
        data["image_url"] = category.image_url
        data["is_big_image"] = category.is_big_image
        data["back_category_info"] = []
        for category in category.back_category:
            data["back_category_info"].append(get_category_name_by_id([i for i in category if i is not None]))
        return IResponse(data)

    def patch(self, request, category_id):
        # 增加关联分类
        data = request.data
        back_category = data.get("back_category", [])
        style = data.get("style", [])
        # 后续改动：支持关联一二级分类
        # if not all((len(i) == 3 and isinstance(i, list) for i in back_category)):
        #     return IResponse(code=400, message="三级目录结构不正确，请重新选择")
        # third_category = [i[3]["id"] for i in back_category]
        try:
            category = FrontProductCategoryList.objects.get(pk=category_id, is_deleted=False)
        except FrontProductCategoryList.DoesNotExist:
            return IResponse(code=400, message="data not found")
        if category.level != 3:
            raise APIViewException(err_message="只有第三级前台分类才能能关联后台分类")
        if back_category:
            for sub_category in back_category:
                for i in range(3 - len(sub_category)):
                    sub_category.append(None)
            category.back_category = back_category
        else:
            category.back_category = []
        # style_id = ProductAttrList.objects.filter(name="款式").first()
        if style:
            # style_info = ProductAttrValues.objects.filter(name__in=style, attr=style_id)
            # style_name = [i.name for i in style_info]
            category.style = style
        else:
            category.style = []
        category.save()
        return IResponse()

    def delete(self, request, category_id):
        data = request.data
        back_category = data.get("back_category", [])
        if not back_category:
            raise APIViewException(err_message="缺少后台目录参数")
        back_category_ids = [str(i) for i in back_category]
        for i in range(3 - len(back_category_ids)):
            back_category_ids.append(None)
        try:
            category = FrontProductCategoryList.objects.get(pk=category_id, is_deleted=False)
        except FrontProductCategoryList.DoesNotExist:
            return IResponse(code=400, message="data not found")
        old_back_category = category.back_category
        old_back_category.remove(back_category_ids)
        category.back_category = [list(i) for i in old_back_category]
        category.save()
        return IResponse()


class OPCompanySaleRankDataView(OPAPIView):
    def get(self, request: Request):
        params = request.query_params
        company_name = params.get("company_name", "")
        filters = {"is_deleted": False, "product__is_deleted": False}

        if company_name:
            filters["name__icontains"] = company_name
        sku_counts_by_company = (
            Company.objects.filter(**filters)
            .annotate(sku_count=Count("product__stockkeepingunit__id", filter=Q(product__stockkeepingunit__become_history=False), distinct=True))
            .order_by("-sku_count")
            # .values('id', 'name', 'sku_count')
        )
        res_data, _, _ = custom_django_filter(request, sku_counts_by_company, iserializer=CompanySaleRankFilterSer, order_fields=["-sku_count"], force_order=False)

        return IResponse(data=res_data)


class OPProductSaleRankDataView(OPAPIView):
    def get(self, request: Request):
        today, day_range, month_range = get_day_month_range()
        sales_count_subquery = (
            SKUSalesCount.objects.filter(product_id=OuterRef("id"), calc_date__range=month_range)
            .values("product_id")
            .annotate(product_month_count=Count("sku_id", distinct=True))
            .values("product_month_count")
        )

        sku_count_subquery = (
            StockKeepingUnit.objects.filter(product_id=OuterRef("id"), product__is_deleted=False, become_history=False)
            .values("product_id")
            .annotate(sku_count=Count("sku_id"))
            .values("sku_count")
        )

        products = (
            Product.objects.filter(is_deleted=False)
            .annotate(product_month_count=Subquery(sales_count_subquery, output_field=FloatField()), sku_count=Subquery(sku_count_subquery, output_field=FloatField()))
            .annotate(product_month_rate=Coalesce("product_month_count", float(0)) / Coalesce("sku_count", float(1)))
        )

        #  商品维度以本月动销排序，很耗时，改成如上模式
        #     sku_counts_by_company = (
        #         Product.objects
        #         .filter(**filters)
        #         .annotate(
        #             sku_count=Count('stockkeepingunit__id',
        #                             filter=Q(stockkeepingunit__become_history=False), distinct=True)
        #         )
        #         .order_by('-update_date')
        #         # .values('id', 'name', 'sku_count')
        #     )
        res_data, _, _ = custom_django_filter(request, products, ProductSaleRankFilterSet, iserializer=ProductSaleRankFilterSer)

        return IResponse(data=res_data)


class OPThirtyProductSaleRankDataView(IAPIView):
    def verify_user(self):
        if self.request.auth.get("user_type") not in ["OP", "DB"]:
            raise APIViewException(code=403, err_message="no permission to operate")
        if self.request.auth.get("user_type") == "DB" and not self.current_user.distributor:
            raise APIViewException(err_message="user not bind distributor")

    def get(self, request, product_id):
        today = datetime.now()
        thirty_after = today - timedelta(days=29)

        try:
            product_obj = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="商品已被删除")
        day_product_sale = (
            SKUSalesCount.objects.filter(product=product_obj, calc_date__range=[thirty_after, today]).values("calc_date").annotate(sales_count=Sum("sales")).order_by("calc_date")
        )
        day_product_sale_map = {i["calc_date"].strftime("%Y-%m-%d"): i["sales_count"] for i in day_product_sale}
        thirty_sale_list = []
        for i in range(30):
            curr_date = (thirty_after + timedelta(days=i)).strftime("%Y-%m-%d")
            thirty_sale_list.append({"date": curr_date, "sale": day_product_sale_map.get(curr_date, 0)})

        return IResponse(data=thirty_sale_list)


class OPThirtyProductDBCountDataView(IAPIView):
    def verify_user(self):
        if self.request.auth.get("user_type") not in ["OP", "DB"]:
            raise APIViewException(code=403, err_message="no permission to operate")
        if self.request.auth.get("user_type") == "DB" and not self.current_user.distributor:
            raise APIViewException(err_message="user not bind distributor")

    def get(self, request, product_id):
        today = datetime.now()
        thirty_after = today - timedelta(days=29)

        try:
            product_obj = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="商品已被删除")
        day_product_db_count = (
            ProductSelectionItem.objects.filter(product=product_obj, has_live=True, is_deleted=False, selection_plan__live_date_start__range=[thirty_after, today])
            .values(live_date_start=F("selection_plan__live_date_start"))
            .annotate(db_count=Count("selection_plan__distributor"))
            .order_by("live_date_start")
        )
        day_product_db_count_map = {i["live_date_start"].strftime("%Y-%m-%d"): i["db_count"] for i in day_product_db_count}
        thirty_db_count_list = []
        for i in range(30):
            curr_date = (thirty_after + timedelta(days=i)).strftime("%Y-%m-%d")
            thirty_db_count_list.append({"date": curr_date, "count": day_product_db_count_map.get(curr_date, 0)})

        return IResponse(data=thirty_db_count_list)


class OPThirtyProductReturnRateDataView(IAPIView):
    def verify_user(self):
        if self.request.auth.get("user_type") not in ["OP", "DB"]:
            raise APIViewException(code=403, err_message="no permission to operate")
        if self.request.auth.get("user_type") == "DB" and not self.current_user.distributor:
            raise APIViewException(err_message="user not bind distributor")

    def get(self, request, product_id):
        today = datetime.now()
        thirty_after = today - timedelta(days=29)

        try:
            product_obj = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="商品已被删除")

        # 售后退货 as_order__as_type__in=[0, 1, 3] 暂时不过滤
        day_product_return = (
            AfterSalesRelatedOrderInfo.objects.filter(relate_product=product_obj, is_pass=1, original_order__order_time__range=[thirty_after, today])
            .values(order_time=F("original_order__order_time"))
            .annotate(return_count=Count("sub_as_order_id"))
        )
        day_product_return_map = {i["order_time"].strftime("%Y-%m-%d"): i["return_count"] for i in day_product_return}

        # 销售数量
        day_product_sale = (
            SKUSalesCount.objects.filter(product=product_obj, calc_date__range=[thirty_after, today]).values("calc_date").annotate(sales_count=Sum("sales")).order_by("calc_date")
        )
        day_product_sale_map = {i["calc_date"].strftime("%Y-%m-%d"): i["sales_count"] for i in day_product_sale}

        thirty_db_count_list = []
        for i in range(30):
            curr_date = (thirty_after + timedelta(days=i)).strftime("%Y-%m-%d")
            thirty_db_count_list.append(
                {"date": curr_date, "rate": (day_product_return_map.get(curr_date, 0) / day_product_sale_map.get(curr_date)) if day_product_sale_map.get(curr_date) else 0}
            )

        return IResponse(data=thirty_db_count_list)


class OPHistoryProductDetailStatisticsDataView(IAPIView):
    def verify_user(self):
        if self.request.auth.get("user_type") not in ["OP", "DB"]:
            raise APIViewException(code=403, err_message="no permission to operate")
        if self.request.auth.get("user_type") == "DB" and not self.current_user.distributor:
            raise APIViewException(err_message="user not bind distributor")

    def get(self, request, product_id):
        try:
            product_obj = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="商品已被删除")

        today = datetime.now()
        thirty_after = today - timedelta(days=29)

        sales_count = SKUSalesCount.objects.filter(
            product=product_obj,
            calc_date__range=(thirty_after, today),
        ).aggregate(total_sales=Sum("sales"))["total_sales"]
        data = {
            "sales_count": sales_count,
            "db_count": 0,
            "return_rate": 0,
        }

        db_count = (
            ProductSelectionItem.objects.filter(
                product=product_obj,
                has_live=True,
                is_deleted=False,
                create_date__range=(thirty_after, today),
            )
            .values("selection_plan__distributor")
            .distinct()
        )
        if db_count:
            data["db_count"] = len(db_count)
        # as_order__as_type__in=[0, 1, 3] 暂时不过滤
        return_count = AfterSalesRelatedOrderInfo.objects.filter(relate_product=product_obj, is_pass=1).count()
        if return_count and data["sales_count"]:
            data["return_rate"] = return_count / data["sales_count"]

        return IResponse(data=data)


class OPSelectionPlanMarkView(OPAPIView):
    def put(self, request: Request):
        return add_selection_plan_item_mark(request, self.current_user, self.current_user_type)
