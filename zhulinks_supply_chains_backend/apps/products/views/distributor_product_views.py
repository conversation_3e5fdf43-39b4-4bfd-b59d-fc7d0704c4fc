# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-11-03 10:34:39
# @Last Modified by:
# @Last Modified time: 2023-12-22 18:15:12
import copy
import json
import re
import traceback
from copy import deepcopy
from datetime import datetime, timedelta, date, time
from functools import reduce
from io import BytesIO
from operator import and_

import pandas as pd
from django.core.exceptions import FieldError
from django.db import transaction
from django.db.models import Max, Value, Count, Q, Prefetch, F, Case, When, Sum
from django.db.models.functions import Coalesce
from django.db.utils import IntegrityError
from django.http import FileResponse
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.request import Request
from rest_framework.views import APIView

from common.basic import DBAPIView
from common.basics.views import set_log_params, operate_log_decorator, OperateLogAPIViewMixin
from common.basics.exceptions import APIViewException, DataNotFoundException
from common.basics.paginator import custom_paginator
from common.formats import DATE_FORMAT, DATETIME_FORMAT, TIME_FORMAT
from common.models import CustomerService, LiveAuthor
from common.utils import ProductBulkExtraTags, sorted_distributor_info, get_current_user_type
from common.utils import diff_models
from common.utils_v2.processing_translation import convert_ser_data, json_content_translation_replacement
from companies.models import Brand, Company, Distributor, Stall
from products import logger
from products.filtersets import (
    MyProductFilterSet,
    SelectionPlanItemRecycleListFilterSet,
    LiveNeedsFilterSet,
    DBSelectionPoolFilterSet,
    ProductSelectionPlanRecordFilterSet,
    GoodProductNoteFilterSet,
    GoodProductNoteCommentFilterSet,
    SelectionPlanFilterSet,
    DBProductPriceCompareFilterSet,
    DBProductListFilterSet,
    PlanV2FilterSet,
    TempDBProductListFilterSet,
)
from products.logics.common import (
    download_cs_selection_plan_product_list_fc,
    get_cs_selection_plan_items,
    map_item_move_fc,
    selection_plan_prods_map_fc,
    selection_plan_item_move_fc,
    selection_plan_item_confirm_fc,
    post_product_link_distributor_fc,
    product_copy_fc,
    new_selection_plan_download_fc,
    product_list_params_handler,
    clean_specs,
    validate_category,
    common_update_product_attr_options,
    op_db_update_sku_and_spec_options,
    product_specs_update_handler,
    common_create_product_attr_options,
    common_create_sku_and_spec_options,
    common_create_product,
    common_update_gift,
    attr_download_info_handler,
    common_add_product_to_plan_fc,
    common_query_item_skus_inventory,
    common_update_plan_item_skus_fc,
    bulk_delete_selection_products_fc,
    clean_update_labels,
    v2_plan_tree_list,
    sync_to_sub_products,
    plan_profit_margin,
    plan_profit_margin_download_data,
    bulk_move_items_to_new_plan,
    bulk_add_to_my_product,
    create_combine_product,
    update_combine_product,
    v2_distributor_mode_plan_tree_list,
)
from products.logics.selection_plan import add_selection_plan_item_mark, bulk_add_products_to_plan, common_modify_estimated_sales
from products.serializer import SelectionPlanItemDetailSer
from products.tasks import w_plan_record, send_product_create_notify, generate_en_pptx_async
from users.models import User
from users.tasks import ceate_notifications
from utils.download_tmp import (
    DBM2SelectionPlanItemRecycleListDownloadTmpl,
    MyProductDownloadTmpl,
    get_excel_async,
    DBDownloadProductTmpl,
    DBOperatorSelectionPlanDownloadProductTmpl,
    DBSelectionPlanItemRecycleListDownloadTmpl,
    DistributorMarketMyProductDownloadTmpl,
)
from utils.en_pptx import generate_en_pptx
from utils.http_handle import (
    IResponse,
    FieldsError,
    custom_filter,
    EmptyListResponse,
)
from utils.http_handle import convert_to_ware_time, custom_django_filter
from utils.pptx import generate_pptx
from utils.redis_lock import (
    read_lock,
    gen_redis_conn,
    redis_conn,
)
from zhuji.models import (
    Product as ZhuJiProduct,
)
from zhuji.serializers import ProductPriceCompareSer as ZhuJiProductPriceCompareSer
from .operator_product_views import (
    get_category_name_by_id,
    insert_history_price,
    get_raw_product_id_by_picture,
)
from ..bulk_query import (
    bulk_query_labels,
    bulk_query_latest_history_price,
    bulk_query_confirm_state,
    get_sku_download_specs_option_list,
    bulk_query_skus_specs_detail,
    bulk_query_product_category_ranks,
    bulk_query_product_refund_rate,
    batch_calc_prod_trend,
    bulk_query_similar_product_result,
    bulk_query_ladder_price_settings,
    bulk_query_products_promotions,
)
from ..logics.product_properties import get_product_distribute_price_settings_info
from ..logics.review import get_product_review_info
from ..models import (
    Product,
    ProductSelectionPlanRecord,
    ProductSelectionPlanRecordType,
    SubProduct,
    StockKeepingUnit,
    HistoryPrice,
    ProductSelectionPlan,
    ProductSelectionItem,
    ProductAddress,
    ProductSelectionPlanCompany,
    ProductSelectionPlanCategory,
    LiveNeeds,
    ProductLinkDistributor,
    bulk_query_product_hosting_state,
    calculate_history_price,
    HandCard,
    SKUCostPriceReviewRecord,
    GoodProductNote,
    GoodProductNoteGiveLike,
    GoodProductNoteComment,
    QAReviewQuestionType,
    UserWishList,
    DistributorMarketSubProductRelate,
)
from ..serializers import (
    CSDBSelectionPlanProductListSerializer,
    DBCSSelectionPlanItemSKUDetailSerializer,
    DistributorProductListSerializer,
    DistributorStockKeepingUnitProductListSerializer,
    MyProductProductListSerializer,
    DBSKUDetailSer,
    DBMyProductDetailSer,
    DBProductCreateSer,
    DBProductDetailSer,
    DBProductSelectionPlanSerializer,
    DBProductSelectionPlanReaderSerializer,
    DBProductSelectionPlanListSerializer,
    ProductSelectionItemSerializer,
    ProductSerializer,
    DBOperateSelectionPlanProductListDownloadSerializer,
    CheckProductInOperateSelectionPlanSerializer,
    OperateStockKeepingUnitProductDetailSerializer,
    SelectionPlanItemRecycleListSer,
    SelectionPlanItemRecycleListDownloadSer,
    LiveNeedsCreateSer,
    LiveNeedsInfoSer,
    ProductLinkDistributorInfoSer,
    ProductAttrOptionInfoSer,
    DBProductDetailSerializer,
    MyProductCreateSer,
    MyProductNoCodeUpdateSer,
    HistoryPriceSerializer,
    DBSelectionPoolSer,
    MyProductCodeUpdateSer,
    DBHandCardDetailSerializer,
    CombineProductDetailSer,
    MyProductCombineProductDetailSer,
    VisitorModeProductListSerializer,
    VisitorModeDistributorStockKeepingUnitProductListSerializer,
    GoodProductNoteList,
    GoodProductNoteCommentList,
    DBStockKeepingUnitProductVisitorDetailSerializer,
    DBProductVisitorDetailSerializer,
    DistributorMarketMyProductProductListSerializer,
    DistributorMarketProductListSerializer,
    DistributorMarketSKUListSerializer,
    DBMarketProductDetailSerializer,
    DBDistributeMarketSKUDetail,
    DBMarketSKUDetailSer,
    DBProductSelectionPlanReportSer,
)
from ..tasks import (
    add_images_to_qdrant,
    delete_images_from_qdrant,
    sync_product_to_JST,
    generate_pptx_async,
    async_move_old_plan_product_to_new_plan_invoker,
)
from ..tasks_v2.feishu_notifications import send_user_wish_messages


class DistributorProductView(DBAPIView):
    """
    分销商商品
    """

    resource_id_field = "product_id"
    resource_name = "商品"
    fronted_page = "商品列表"
    need_format_resource_name = True

    @staticmethod
    def _get_self_support_products(request):
        """
        珠凌臻品只获取自营供应商的商品
        :param request:
        :return:
        """
        exclude_c_list = list(Company.objects.filter(is_self_support=True).order_by().values_list("pk", flat=True))
        # 特定供应商可见
        target = (
            Product.get_distributor_products(request)
            .prefetch_related(
                "company",
                "owner",
                "address",
                "handcard",
                "company__stall_set",
            )
            .filter(company_id__in=exclude_c_list, is_combine=False, state=1)
        )
        return target

    @staticmethod
    def _get_normal_products(request):
        """
        正常获取商品
        :param request:
        :return:
        """
        exclude_c_list = list(Company.objects.filter(is_self_support=True).order_by().values_list("pk", flat=True))
        # 特定供应商可见商品
        target = (
            Product.get_distributor_products(request, need_annotate_warehouse_inventory=True)
            .prefetch_related(
                "company",
                "owner",
                "address",
                "handcard",
                "company__stall_set",
            )
            .exclude(company_id__in=exclude_c_list)
        )
        return target

    def get(self, request: Request):
        """
        获取商品列表
        """
        try:
            target = self._get_self_support_products(request) if self.is_self_distributor else self._get_normal_products(request)
            # 分销模式显示的是FX子商品信息
            sub_products_map = {}
            order_map = {}

            filter_set = DBProductListFilterSet
            if self.is_distributor_mode:
                target = target.filter(
                    is_deleted=False,
                    state=1,
                    is_in_distributor_market=True,
                    company__is_self_support=False,
                    is_combine=False,
                    subproduct__owner__letters="FX",
                    subproduct__is_deleted=False,
                ).order_by("-subproduct__create_date__date", "min_distributor_market_price")

                order_map = {
                    "create_date": "subproduct__create_date",
                    "update_date": "subproduct__update_date",
                }
                filter_set = TempDBProductListFilterSet

            # ai模式ticket
            ticket = request._request.headers.get("Ticket")
            if ticket:
                new_ticket_record = self.current_user.aisearchrecords_set.filter(ticket=ticket, input_platform=self.current_user_type).last()
                if not new_ticket_record:
                    return IResponse(data=EmptyListResponse)

                if not new_ticket_record.result:
                    return IResponse(data=EmptyListResponse)

                target = target.filter(product_id__in=new_ticket_record.result)

            re_data, page_products, _ = custom_django_filter(
                request,
                target,
                filter_set,
                need_serialize=False,
                force_order=False,
                order_map=order_map,
            )
            # 批量查询sku
            product_pks = [p.id for p in page_products]
            skus = StockKeepingUnit.objects.filter(become_history=False, product_id__in=product_pks)
            # 批量查询最新历史价
            skus_specs = bulk_query_skus_specs_detail(skus)
            # 最新历史价映射
            history_price_map = bulk_query_latest_history_price(skus)
            # 查看排行榜信息
            ranks_map = bulk_query_product_category_ranks(product_pks)

            skus_map = {}
            for sku in skus:
                if sku.product_id not in skus_map:
                    skus_map[sku.product_id] = [sku]
                else:
                    skus_map[sku.product_id].append(sku)
            sub_products_code_map = {}
            if not self.is_distributor_mode:
                # 查询sub_product_code
                sub_products = SubProduct.objects.filter(
                    parent_product_id__in=product_pks,
                    is_deleted=False,
                    owner=self.current_user.distributor,
                ).only("parent_product_id", "code")
                sub_products_code_map = {sub_product.parent_product_id: sub_product.code for sub_product in sub_products}
            else:
                # 查询sub_product_code
                sub_products = SubProduct.objects.filter(
                    parent_product_id__in=product_pks,
                    is_deleted=False,
                    owner__letters="FX",
                )
                sub_products_map = {sub_product.parent_product_id: sub_product for sub_product in sub_products}
            # 查询状态
            # bulk_fetch_product_confirm_state([p.product_id for p in page_products])

            # 查询托管状态
            product_ids = [product_obj.product_id for product_obj in page_products]
            hosting_product_map = bulk_query_product_hosting_state(product_ids)
            # 锁库存状态
            confirm_state_map = bulk_query_confirm_state(product_ids)
            # 标签
            labels_map = bulk_query_labels(product_ids, fronted_display=True)
            # 批量查询趋势
            trend_map = batch_calc_prod_trend(page_products)
            # 批量查询质检信息
            extra_tags_map = ProductBulkExtraTags(page_products)
            # 批量查询退费率
            has_permission, refund_rates_map = bulk_query_product_refund_rate(self.current_user, self.current_user_type, product_ids)
            # 查询相似品
            similar_products = bulk_query_similar_product_result(product_ids)
            # 阶梯价格
            ladder_price_settings = {}
            # 查询商品优惠信息
            promotions_map = {}
            if self.is_distributor_mode:
                ladder_price_settings = bulk_query_ladder_price_settings(product_ids)
                promotions_map = bulk_query_products_promotions(product_ids, self.current_user)

            for product_obj in page_products:
                if self.is_distributor_mode:
                    product_ser = DistributorMarketProductListSerializer(
                        instance=product_obj,
                        context={
                            "request": self.request,
                            "labels_map": labels_map,
                            "trend_map": trend_map,
                            "extra_tags_map": extra_tags_map,
                            "ranks_map": ranks_map,
                            "sub_products_map": sub_products_map,
                            "ladder_price_settings": ladder_price_settings,
                            "promotions_map": promotions_map,
                        },
                    )
                else:
                    product_ser = DistributorProductListSerializer(
                        instance=product_obj,
                        context={
                            "request": self.request,
                            "labels_map": labels_map,
                            "trend_map": trend_map,
                            "extra_tags_map": extra_tags_map,
                            "ranks_map": ranks_map,
                        },
                    )
                data = product_ser.data
                category = data.get("category")
                category_list = get_category_name_by_id(category)
                data["category"] = category_list
                # 添加商品确认，确认状态为1时不允许修改库存数据
                data["product_confirm_state"] = confirm_state_map.get(product_obj.product_id)
                # 添加商品关联的货号信息
                data["origin_product_id"] = product_obj.origin_product_id
                data["sub_code"] = sub_products_code_map.get(product_obj.id) or ""
                sku_qs = skus_map.get(product_obj.id) or []
                if self.is_distributor_mode:
                    skus = DistributorMarketSKUListSerializer(
                        instance=sku_qs,
                        many=True,
                        context={
                            "request": self.request,
                            "skus_specs": skus_specs,
                            "history_price_map": history_price_map,
                        },
                    )
                else:
                    skus = DistributorStockKeepingUnitProductListSerializer(
                        instance=sku_qs,
                        many=True,
                        context={
                            "request": self.request,
                            "skus_specs": skus_specs,
                            "history_price_map": history_price_map,
                        },
                    )
                data["skus"] = skus.data
                # 添加状态
                data["is_new"] = product_obj.is_new
                data["hosting_state"] = hosting_product_map.get(product_obj.product_id)
                data["refund_rate"] = "***" if not has_permission else (refund_rates_map.get(product_obj.product_id, None) or None)

                # 相似品
                data["similar_products"] = similar_products.get(product_obj.product_id)

                re_data["data"].append(data)

            return IResponse(data=re_data)
        except FieldError as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=400)
        except Exception as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=500)

    def post(self, request):
        """
        分销商发布商品
        """
        try:
            # 判断用户是否为分销商
            current_user = self.current_user
            # 判断公司
            raw_data = request.data

            try:
                company_id = raw_data.get("company_id")
                if not company_id:
                    return IResponse(message="必须填写供应商信息")
                company = Company.objects.get(company_id=company_id)
            except Company.DoesNotExist:
                return IResponse(message="供应商信息错误")

            company_code = company.code

            # 当前用户的信息记录
            raw_data["create_user"] = current_user.user_id
            # 关联分销商
            raw_data["data_source"] = "DB"
            raw_data["owner"] = current_user.distributor.distributor_id
            raw_data["address"] = raw_data.get("address_id") or None
            raw_data["company"] = company.id

            # 单独处理sku和额外的属性
            skus_data = raw_data.pop("skus", [])
            attr_options_data = raw_data.pop("attr_options", [])

            # 开启事务创建 product，sku ，attr options
            with transaction.atomic():
                # 先校验再创建
                specs_list = raw_data.pop("spec_lists", [])
                clean_specs(specs_list, skus_data, request)
                # product
                product_instance = common_create_product(raw_data, specs_list, request, DBProductCreateSer)
                # sku && spec options
                sku_data, sku_images = common_create_sku_and_spec_options(product_instance, skus_data, request, company_code)
                # product attr options
                attr_options_instance = common_create_product_attr_options(product_instance, attr_options_data)

            new_product = product_instance

            # 上传图片至qdrant
            images = deepcopy(new_product.main_images)
            # if new_product.detail_images:
            #     images.extend(deepcopy(new_product.detail_images))
            if sku_images:
                images.extend(sku_images)
            add_images_to_qdrant.delay(new_product.id, list(set(images)))

            # 发送飞书通知
            try:
                live_date = new_product.live_date
                if isinstance(live_date, date):
                    live_date = datetime.combine(live_date, time())
                str_live_date = live_date.astimezone(timezone.get_current_timezone()).strftime(DATE_FORMAT)

                task_cost_price = (
                    new_product.min_cost_price if new_product.min_cost_price == new_product.max_cost_price else f"{new_product.min_cost_price}-{new_product.max_cost_price}"
                )
                send_product_create_notify.delay(
                    new_product.product_id,
                    new_product.code,
                    new_product.name,
                    new_product.company.name,
                    str_live_date,
                    new_product.get_state_display(),
                    new_product.owner.name,
                    task_cost_price,
                    new_product.main_images[0],
                )
            except Exception as e:
                logger.warning(f"DB创建商品发送飞书通知失败,{e}.{traceback.format_exc()}")
                pass

            resp_data = DBProductDetailSer(instance=new_product, context={"request": self.request}).data
            category = resp_data.get("category")
            category_list = get_category_name_by_id(category)
            resp_data["category"] = category_list

            resp_data.pop("company")
            resp_data["skus"] = sku_data

            attr_options_data = ProductAttrOptionInfoSer(instance=attr_options_instance, many=True).data
            resp_data["attr_options"] = attr_options_data

            # 数据同步至聚水潭
            sync_product_to_JST.delay(new_product.product_id)

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=new_product.product_id,
                    model=Product,
                    describe=f"新增了商品:{new_product}",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(data=resp_data)
        except IntegrityError as e:
            logger.error(str(e))
            return IResponse(message=str(e), code=500)
        except Exception as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=400)


class DistributorProductDetailView(DBAPIView):
    """分销商商品详情"""

    resource_id_field = "product_id"
    resource_name = "商品"
    fronted_page = "商品列表"

    permission_classes = [IsAuthenticated]

    def replace_sub_product_data(self, data):
        # 如果是分销模式，替换为副本商品内容
        try:
            if self.is_distributor_mode:
                sub_product = SubProduct.objects.get(
                    parent_product_id=data.pop("id"),
                    parent_product__state=1,
                    parent_product__is_in_distributor_market=True,
                    is_deleted=False,
                    owner__letters="FX",
                    parent_product__is_deleted=False,
                )
                data["sub_product_id"] = sub_product.product_id
                data["name"] = sub_product.name
                data["main_images"] = sub_product.main_images[:3]
                data["size"] = sub_product.size
                data["create_date"] = sub_product.create_date
                data["code"] = sub_product.code
                data["sub_code"] = sub_product.code

                # 替换详情图片
                raw_detail = sub_product.detail_images or ""
                if len(sub_product.main_images) > 3:
                    for img_url in sub_product.main_images[3:]:
                        re_pattern = f'<p><img src="{img_url}" alt=".*" data-href="{img_url}" style="width: 100%;"/></p>'
                        raw_detail = re.sub(re_pattern, "", raw_detail)
                data["detail_images"] = raw_detail
            else:
                sub_product = SubProduct.objects.get(
                    parent_product_id=data.pop("id"),
                    is_deleted=False,
                    owner=self.current_user.distributor,
                    parent_product__is_deleted=False,
                )
                data["sub_code"] = sub_product.code
        except SubProduct.DoesNotExist:
            data["sub_code"] = ""

    @staticmethod
    def _get_obj(product_id: int, _request) -> Product:
        try:
            product = Product.get_distributor_products(_request).get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            raise APIViewException(err_message="data not found")
        return product

    def get(self, request: Request, product_id: int):
        product = self._get_obj(product_id, request)
        is_in_distributor_market = product.is_in_distributor_market

        if self.is_distributor_mode:
            sku_ser = DBDistributeMarketSKUDetail
            product_ser = DBMarketProductDetailSerializer
        else:
            sku_ser = OperateStockKeepingUnitProductDetailSerializer
            product_ser = DBProductDetailSerializer

        sku_list = []
        skus_qs = StockKeepingUnit.objects.filter(product=product, become_history=False)
        for sku_qs in skus_qs:
            skus_data = sku_ser(
                instance=sku_qs,
                context={
                    "request": self.request,
                    "is_in_distributor_market": is_in_distributor_market,
                },
            ).data
            history_price_data = []
            author_create_date = (
                HistoryPrice.objects.filter(sku_id=sku_qs.id)
                .values("author_id")
                .annotate(effective_author=Coalesce("author_id", Value("")), max_create_date=Max("create_date"))
                .values("effective_author", "max_create_date")
            )

            for i in author_create_date:
                filters = {
                    "author_id": i["effective_author"] if i.get("effective_author") else None,
                    "create_date": i.get("max_create_date"),
                    "sku_id": sku_qs.id,
                }
                history_price = HistoryPrice.objects.filter(**filters).first()
                if history_price:
                    history_price_data.append(
                        {
                            "history_price": history_price.history_price,
                            "history_price_author_id": history_price.author.author_id if history_price.author else None,
                            "history_price_author_name": history_price.author.name if history_price.author else None,
                            "create_date": history_price.create_date,
                        }
                    )

            skus_data["history_price_data"] = history_price_data
            # skus_data["image"] = skus_data["image"] if skus_data["image"] else product.main_images[0]
            sku_list.append(skus_data)

        # 商品详情信息
        detail_ser = product_ser(instance=product, many=False, context={"request": request})
        data = detail_ser.data
        data["skus"] = sku_list
        # 替换内容
        self.replace_sub_product_data(data)
        # category
        category_list = get_category_name_by_id(data.get("category"))
        data["category"] = category_list

        # 添加商品确认，确认状态为1时不允许修改库存数据
        data["product_confirm_state"] = product.product_confirm_state
        # attr信息
        attr_options = product.productattroption_set.select_related("attr", "attr_value")
        data["attr_options"] = ProductAttrOptionInfoSer(instance=attr_options, many=True).data

        # 商品关联分销商货号列表
        distributor_links = product.productlinkdistributor_set.filter(is_deleted=False)
        distributor_links_ser = ProductLinkDistributorInfoSer(instance=distributor_links, many=True)
        code_data = distributor_links_ser.data
        data["distributor_links_info"] = sorted_distributor_info(code_data)

        # 商品状态
        data["is_new"] = product.is_new
        data["hosting_state"] = product.hosting_state
        data["promotions"] = []
        # 是否已经点了想要
        if self.is_distributor_mode:
            data["has_wish"] = product.userwishlist_set.filter(user=self.current_user, wish_type=2, distributor=self.current_distributor).exists()
            promotions_map = bulk_query_products_promotions([product.product_id], self.current_user)
            data["promotions"] = promotions_map.get(product.product_id) or []
            # 分销模式, 查询批发信息
            data["distribute_price_settings_info"] = get_product_distribute_price_settings_info(product)

            if not is_in_distributor_market:
                distributor_market_price_list = [i.get("distributor_market_price") for i in sku_list if i.get("distributor_market_price")]

                data["min_distributor_market_price"] = min(distributor_market_price_list) if distributor_market_price_list else None
                data["max_distributor_market_price"] = max(distributor_market_price_list) if distributor_market_price_list else None

        else:
            data["has_wish"] = product.userwishlist_set.filter(user=self.current_user, wish_type=1).exists()

        # 用户想要个数
        data["user_wish_count"] = product.userwishlist_set.filter(wish_type=1).count()
        return IResponse(data=data)

    def patch(self, request: Request, product_id):
        """
        更新商品
        """
        try:
            current_user = request.user
            raw_data = request.data

            if raw_data.get("product_id"):
                assert product_id == raw_data.get("product_id"), "The path product_id is inconsistent with the body product_id"

            product_obj = self._get_obj(product_id, request)

            raw_object = copy.deepcopy(product_obj)
            if raw_data.get("company_id"):
                if product_obj.company.company_id != raw_data["company_id"] and product_obj.data_source == "SP":
                    return IResponse(code=400, message="can not change supplier source data")
                company = Company.objects.filter(company_id=raw_data["company_id"]).first()
                if not company:
                    return IResponse(code=400, message="company not found")
                raw_data["company_id"] = company.id
            else:
                company = product_obj.company

            if raw_data.get("brand"):
                raw_data["brand_id"] = raw_data.pop("brand")

            if raw_data.get("unit"):
                raw_data["unit_id"] = raw_data.pop("unit")

            company_code = company.code

            # 不允许更改的字段
            cannot_update_fields = ["physical_inventory", "safety_inventory", "can_use_inventory"]
            if len(raw_data.keys()) != 1:
                cannot_update_fields.append("state")
            for field in cannot_update_fields:
                if raw_data.get(field):
                    raw_data.pop(field)

            # 获取原始图片链接
            raw_images = deepcopy(product_obj.main_images)
            # if product_obj.detail_images:
            #     raw_images.extend(deepcopy(product_obj.detail_images))
            # 发现新图片链接
            new_images = deepcopy(raw_data.get("main_images", []))
            if len(new_images) > 15:
                return IResponse(code=400, message="主图不能超过15张")

            # if raw_data.get("detail_images"):
            #     new_images.extend(raw_data["detail_images"])

            skus_data, attr_options_data = None, None
            if raw_data.get("skus") is not None:
                skus_data = raw_data.pop("skus")
            if raw_data.get("attr_options") is not None:
                attr_options_data = raw_data.pop("attr_options")

            # 赠品
            gifts = raw_data.pop("gifts") if raw_data.get("gifts") else {}
            if gifts:
                raw_data["has_gift"], raw_data["gift_type"] = gifts.get("has_gift"), gifts.get("gift_type")

            # 标签处理
            labels = None
            if raw_data.get("labels") is not None:
                labels = raw_data.pop("labels")
            # update product
            # exclude
            raw_data["update_user"] = current_user.user_id
            raw_data["update_date"] = timezone.now()
            product_model_fields = [f.name for f in product_obj._meta.fields if f.name != "company"]
            product_model_fields.append("company_id")
            product_model_fields.append("brand_id")
            product_model_fields.append("address_id")
            product_model_fields.append("unit_id")
            raw_data = {key: value for key, value in raw_data.items() if key in product_model_fields}

            # 校验分类
            category = raw_data.get("category")
            validate_category(category)
            # 校验specs_list和specs数据是否一致
            specs_list = raw_data.get("spec_lists") or []
            product_obj.__dict__.update(**raw_data)

            update_fields = list(raw_data.keys())
            resp_data = {}
            sku_change_content = []
            attr_option_change_content = []
            sku_add_imgs = []
            sku_del_imgs = []
            with transaction.atomic():
                clean_specs(specs_list, skus_data, request)
                if labels is not None:
                    origin_label_id_list, new_label_ids = clean_update_labels(labels, product_obj)

                # update sku and specs
                if skus_data is not None:
                    sku_data, sku_add_imgs, sku_del_imgs, sku_change_content, reset_preview_state = op_db_update_sku_and_spec_options(product_obj, skus_data, request, company_code)

                    if reset_preview_state == 1:
                        update_fields.append("state")
                        product_obj.state = 0

                        # 审核流程变历史数据
                        product_obj.productreviewprocess_set.update(become_history=True)
                        SKUCostPriceReviewRecord.objects.filter(product_id=product_obj.product_id, state=0).update(state=3, review_remark="商品修改，该审批已过去")

                    elif reset_preview_state == 2:
                        update_fields.append("state")
                        product_obj.state = 4

                        # 核价流程变历史数据
                        product_obj.productreview_set.filter(process__become_history=False, process_level="PRICE_REVIEW").update(become_history=True)
                        SKUCostPriceReviewRecord.objects.filter(product_id=product_obj.product_id, state=0).update(state=3, review_remark="商品修改，该审批已过去")

                    resp_data["skus"] = sku_data
                # attr options
                if attr_options_data is not None:
                    attr_options_data, attr_option_change_content = common_update_product_attr_options(product_obj, attr_options_data)
                    resp_data["attr_options"] = attr_options_data

                # 更新赠品
                common_update_gift(product_obj, gifts, company.id)

                # 保留原始数据
                product_obj.spec_lists = specs_list
                product_obj.save(update_fields=set(update_fields))

                # 刷新数据
                product_obj.refresh_from_db()

                # 修改商品specs_list
                product_specs_update_handler(product_obj, specs_list)

                if labels is not None:
                    if origin_label_id_list:
                        product_obj.labels_relate.remove(*origin_label_id_list)
                    for label_id in new_label_ids:
                        product_obj.labels_relate.add(label_id)

                # 更新副本
                sync_to_sub_products(raw_object, product_obj)

            # 有新增的图片，上传至qdrant
            images = list(set(new_images) - set(raw_images))
            if sku_add_imgs:
                images.extend(sku_add_imgs)
            if images:
                add_images_to_qdrant.delay(product_obj.id, list(set(images)))

            # 被删除的图片，需要从qdrant中删除
            del_imags = list(set(raw_images) - set(new_images))
            if sku_del_imgs:
                del_imags.extend(sku_del_imgs)
            for d_img in del_imags:
                delete_images_from_qdrant.delay(product_obj.id, raw_data=d_img)

            product_data = ProductSerializer(instance=product_obj, context={"request": self.request}).data
            category = product_data.get("category")
            category_list = get_category_name_by_id(category)
            product_data["category"] = category_list

            resp_data.update(product_data)
            resp_data.pop("company")

            # 数据同步至聚水潭
            sync_product_to_JST.delay(product_obj.product_id)

            # 日志记录
            try:
                operate_content = "更新内容: "

                _product_change_content = diff_models(
                    raw_object,
                    product_obj,
                    private_field=[
                        "physical_inventory",
                        "can_use_inventory",
                        "safety_inventory",
                        "min_cost_price",
                        "max_cost_price",
                        "min_retail_price",
                        "max_retail_price",
                        "min_history_price",
                        "max_history_price",
                        "spec_lists",
                    ],
                )
                if _product_change_content:
                    product_change_content = "\n商品: " + _product_change_content
                    operate_content += product_change_content

                if sku_change_content:
                    operate_content += "\nSKU: " + "、".join(sku_change_content)

                if attr_option_change_content:
                    operate_content += "\n属性: " + "、".join(attr_option_change_content)

                set_log_params(
                    request,
                    resource_id=str(product_id),
                    model=Product,
                    describe=f"修改了商品:{product_obj}",
                    operate_content=operate_content,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(data=resp_data)
        except IntegrityError as e:
            logger.error(str(e))
            return IResponse(message=str(e), code=500)
        except Exception as e:
            logger.error(f"{e}-->{traceback.format_exc()}")
            return IResponse(message=str(e), code=400)


class DistributorProductVisitorDetailView(APIView):
    # 访客模式下商品详情
    permission_classes = [AllowAny]

    @staticmethod
    def _get_obj(product_id: int):
        try:
            product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            raise APIViewException(err_message="data not found")
        return product

    def get(self, request: Request, product_id: int):
        platform = request.query_params.get("platform", "1") or "1"
        product = self._get_obj(product_id)
        # unit
        sku_list = []
        skus_qs = StockKeepingUnit.objects.filter(product=product, become_history=False)
        for sku_qs in skus_qs:
            skus_data = DBStockKeepingUnitProductVisitorDetailSerializer(instance=sku_qs, context={"request": self.request}).data
            history_price_data = []
            author_create_date = (
                HistoryPrice.objects.filter(sku_id=sku_qs.id)
                .values("author_id")
                .annotate(effective_author=Coalesce("author_id", Value("")), max_create_date=Max("create_date"))
                .values("effective_author", "max_create_date")
            )

            for i in author_create_date:
                filters = {
                    "author_id": i["effective_author"] if i.get("effective_author") else None,
                    "create_date": i.get("max_create_date"),
                    "sku_id": sku_qs.id,
                }
                history_price = HistoryPrice.objects.filter(**filters).first()
                if history_price:
                    history_price_data.append(
                        {
                            "history_price": history_price.history_price,
                            "history_price_author_id": history_price.author.author_id if history_price.author else None,
                            "history_price_author_name": history_price.author.name if history_price.author else None,
                            "create_date": history_price.create_date,
                        }
                    )
                else:
                    logger.warning(f"history_price not found: sku:{sku_qs.sku_id, i.get('effective_author'), i.get('max_create_date')}")

            skus_data["history_price_data"] = history_price_data
            sku_list.append(skus_data)

        # 商品详情信息
        detail_ser = DBProductVisitorDetailSerializer(instance=product, many=False, context={"request": request})
        data = detail_ser.data

        # 主图，详情图
        if platform != "1":
            main_images = data["main_images"][:]
            data["main_images"] = main_images[:3]
            # 替换详情图片
            raw_detail = data["detail_images"] or ""
            if len(main_images) > 3:
                for img_url in main_images[3:]:
                    re_pattern = f'<p><img src="{img_url}" alt=".*" data-href="{img_url}" style="width: 100%;"/></p>'
                    raw_detail = re.sub(re_pattern, "", raw_detail)
            data["detail_images"] = raw_detail

        # 甄选
        if platform == "1":
            # 副货号
            try:
                # 未登录的情况
                db_obj = Distributor.objects.filter(is_self_support=True).first()
                if db_obj:
                    sub_product = SubProduct.objects.get(parent_product_id=product.pk, is_deleted=False, owner=db_obj, parent_product__is_deleted=False)
                    data["sub_code"] = sub_product.code
            except SubProduct.DoesNotExist:
                data["sub_code"] = ""
        else:
            # 副货号
            try:
                # 未登录的情况
                sub_product = SubProduct.objects.get(parent_product_id=product.pk, is_deleted=False, owner__letters="FX")
                data["sub_code"] = sub_product.code
                data["main_images"] = sub_product.main_images[:3]
                # 替换详情图片
                raw_detail = sub_product.detail_images or ""
                if len(sub_product.main_images) > 3:
                    for img_url in sub_product.main_images[:3]:
                        re_pattern = f'<p><img src="{img_url}" alt=".*" data-href="{img_url}" style="width: 100%;"/></p>'
                        raw_detail = re.sub(re_pattern, "", raw_detail)
                data["detail_images"] = raw_detail
            except SubProduct.DoesNotExist:
                data["sub_code"] = ""

        # category
        category_list = get_category_name_by_id(data.get("category"))
        data["category"] = category_list

        # 添加商品确认，确认状态为1时不允许修改库存数据
        data["product_confirm_state"] = product.product_confirm_state
        data["skus"] = sorted(sku_list, key=lambda x: x["order"])

        # attr信息
        attr_options = product.productattroption_set.select_related("attr", "attr_value")
        data["attr_options"] = ProductAttrOptionInfoSer(instance=attr_options, many=True).data

        # 商品关联分销商货号列表
        distributor_links = product.productlinkdistributor_set.filter(is_deleted=False)
        distributor_links_ser = ProductLinkDistributorInfoSer(instance=distributor_links, many=True)
        code_data = distributor_links_ser.data
        data["distributor_links_info"] = sorted_distributor_info(code_data)

        # 商品状态
        data["is_new"] = product.is_new
        data["hosting_state"] = product.hosting_state

        # 用户想要个数
        data["user_wish_count"] = product.userwishlist_set.count()
        return IResponse(data=data)


class DBBulkAddToMyProductView(DBAPIView):
    def normal_bulk_create_sub_products(self, raw_data):
        """
        普通分销商 加入我的商品 -> 添加一个商品副本
        :param raw_data:
        :return:
        """
        current_user = self.current_user
        current_distributor = self.current_distributor
        messages = []
        for product_id in raw_data:
            try:
                product = Product.objects.get(product_id=product_id, is_deleted=False)
            except Product.DoesNotExist:
                messages.append(f"{product_id}不存在")
                continue

            if SubProduct.objects.filter(parent_product=product, is_deleted=False, owner=current_user.distributor).exists():
                continue

            post_data = product.__dict__
            post_data["parent_product"] = product.pk
            post_data["owner"] = current_user.distributor.distributor_id
            post_data["create_user"] = current_user.user_id
            ser = MyProductCreateSer(data=post_data)
            if not ser.is_valid():
                messages.append(str(FieldsError(ser.errors)))
                continue
            with transaction.atomic():
                if product.is_combine:
                    product_list = product.substockkeepingunit_set.filter(become_history=False).values_list("relate_sku__product__product_id", flat=True)
                    bulk_add_to_my_product(product_list, current_user)

                my_product = ser.save()
                # 创建关联分销商货号数据
                ProductLinkDistributor(
                    product=product,
                    distributor=current_distributor,
                    code=my_product.code,
                    create_user=current_user.user_id,
                ).save()

                # 判断是否有
                ProductSelectionItem.objects.filter(
                    product_id=product_id,
                    selection_plan__distributor=current_distributor,
                    selection_plan__state__in=[1, 2],
                ).update(
                    sub_product=my_product,
                )

        msg = None
        if messages:
            msg = ",".join(messages)
            logger.info(str(messages))

        return msg

    def distributor_mode_bulk_create_sub_products(self, raw_data):
        """
        分销模式分销商 加入我的商品 ->
        1. 找到分销商代码为FX的商品副本
        2. 添加副本的关联
        :param raw_data:
        :return:
        """
        current_user = self.current_user
        current_distributor = self.current_distributor
        messages = []

        need_create_objs = []
        for product_id in raw_data:
            try:
                product = Product.objects.get(product_id=product_id, is_deleted=False)
            except Product.DoesNotExist:
                messages.append(f"{product_id}不存在")
                continue

            # 分销模式加入到我的商品 -> 添加副本商品跟分销商的关联
            try:
                sub_product = SubProduct.objects.get(
                    parent_product__product_id=product_id,
                    parent_product__state=1,
                    parent_product__is_in_distributor_market=True,
                    is_deleted=False,
                    owner__letters="FX",
                    parent_product__is_deleted=False,
                )
            except SubProduct.DoesNotExist:
                messages.append(f"{product_id}不在分销市场")
                continue

            if DistributorMarketSubProductRelate.objects.filter(sub_product=sub_product, owner_id=self.current_user.distributor_id).exists():
                continue

            need_create_objs.append(
                DistributorMarketSubProductRelate(
                    sub_product=sub_product,
                    owner=current_distributor,
                    create_user=current_user.user_id,
                )
            )
        if need_create_objs:
            DistributorMarketSubProductRelate.objects.bulk_create(need_create_objs, batch_size=1000)

        msg = None
        if messages:
            msg = ",".join(messages)
            logger.info(str(messages))

        return msg

    def post(self, request: Request):
        """
        批量加入我的商品
        """

        raw_data = request.data

        assert isinstance(raw_data, list), "data should be a list"

        if self.is_distributor_mode:
            msg = self.distributor_mode_bulk_create_sub_products(raw_data)
        else:
            msg = self.normal_bulk_create_sub_products(raw_data)

        return IResponse(code=200, message=msg)


class MyProductView(DBAPIView):
    """
    我的商品
    """

    def get(self, request: Request):
        """
        我的商品列表
        """
        # 转换order_by排序字段
        order_map = {
            "sales": "parent_product__sales",
            "physical_inventory": "parent_product__physical_inventory",
        }

        sub_product_relates = None
        if self.is_distributor_mode:
            # 分销模式 - 我的商品
            sub_product_relates = DistributorMarketSubProductRelate.objects.filter(owner_id=request.user.distributor.distributor_id)
            sub_id = [i.sub_product_id for i in sub_product_relates]
            products = (
                SubProduct.objects.prefetch_related(
                    "parent_product",
                    "parent_product__address",
                    "parent_product__unit",
                    "parent_product__handcard",
                )
                .filter(pk__in=sub_id, is_in_distributor_market=True)
                .annotate(
                    warehouse_inventory=Sum(
                        "parent_product__stockkeepingunit__warehouse_inventory",
                        filter=Q(parent_product__stockkeepingunit__become_history=False),
                    ),
                )
            )
        else:
            products = SubProduct.get_distributor_sub_products(
                request,
                prefetch_fields=[
                    "parent_product",
                    "parent_product__handcard",
                    "parent_product__address",
                    "parent_product__unit",
                    "parent_product__company",
                ],
            ).annotate(
                warehouse_inventory=Sum(
                    "parent_product__stockkeepingunit__warehouse_inventory",
                    filter=Q(parent_product__stockkeepingunit__become_history=False),
                ),
            )

        re_data, page_sub_products, qs = custom_django_filter(
            request,
            products,
            MyProductFilterSet,
            None,
            need_serialize=False,
            order_map=order_map,
        )

        product_id_list = [item.parent_product.product_id for item in page_sub_products.object_list]
        # 标签查询
        labels_map = bulk_query_labels(product_id_list, fronted_display=True)

        if self.is_distributor_mode:
            promotions_map = bulk_query_products_promotions(product_id_list, self.current_user)
            re_data["data"] = DistributorMarketMyProductProductListSerializer(
                instance=page_sub_products,
                many=True,
                context={
                    "request": request,
                    "labels_map": labels_map,
                    "sub_product_relates": sub_product_relates,
                    "promotions_map": promotions_map,
                    "is_distributor_mode": True,
                },
            ).data
        else:
            re_data["data"] = MyProductProductListSerializer(
                instance=page_sub_products,
                many=True,
                context={
                    "request": request,
                    "labels_map": labels_map,
                },
            ).data
        return IResponse(data=re_data)


class MyProductDownloadView(DBAPIView):
    def get(self, request: Request):
        try:
            current_user = self.current_user
            re_data = []

            if self.is_distributor_mode:
                products = DistributorMarketSubProductRelate.objects.filter(owner_id=request.user.distributor.distributor_id)
                sub_id = [i.sub_product_id for i in products]
                products = SubProduct.objects.filter(pk__in=sub_id)
            else:
                products = SubProduct.get_distributor_sub_products(request)

            _, page_sub_products, _ = custom_django_filter(
                request,
                products,
                MyProductFilterSet,
                None,
                need_serialize=False,
            )

            page_sub_products = page_sub_products.object_list.select_related("parent_product")
            # 查询所属地区
            address_ids = {sub_product.parent_product.address_id for sub_product in page_sub_products}
            address_objs = ProductAddress.objects.filter(id__in=address_ids)
            address_map = {address.id: address.name for address in address_objs}

            product_id_list = [item.parent_product.product_id for item in page_sub_products]
            labels_map = bulk_query_labels(product_id_list)

            num = 0

            for sub_product_qs in page_sub_products:
                re_product = {}
                if self.is_distributor_mode:
                    product_data = DistributorMarketMyProductProductListSerializer(instance=sub_product_qs, context={"request": self.request, "labels_map": labels_map}).data
                    for k, v in DistributorMarketMyProductDownloadTmpl.items():
                        value = product_data.get(k)

                        re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value
                        if k == "address":
                            re_product[v] = address_map.get(sub_product_qs.parent_product.address_id, "")
                else:
                    product_data = MyProductProductListSerializer(instance=sub_product_qs, context={"request": self.request, "labels_map": labels_map}).data
                    # template data
                    for k, v in MyProductDownloadTmpl.items():
                        value = product_data.get(k)

                        re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value
                        if k == "address":
                            re_product[v] = address_map.get(sub_product_qs.parent_product.address_id, "")
                # 款式
                attr_download_info_handler(re_product, sub_product_qs.parent_product)

                # category
                category_id_and_name_list = product_data.get("category")
                name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目"}
                for i in range(len(category_id_and_name_list)):
                    re_product[f"{name_key[i]}ID"] = category_id_and_name_list[i].get("id")
                    re_product[f"{name_key[i]}名称"] = category_id_and_name_list[i].get("name")
                    if i == 3:
                        break

                # sku
                skus_qs = sub_product_qs.parent_product.stockkeepingunit_set.filter(become_history=False)
                for sku_qs in skus_qs:
                    num += 1
                    re_sku = {"序号": num}
                    re_sku.update(re_product)
                    re_sku["规格ID"] = sku_qs.sku_id
                    # spec
                    spec_option_list = get_sku_download_specs_option_list(sku_qs)
                    re_sku["规格"] = ";".join(spec_option_list)
                    re_sku["建议售价"] = sku_qs.retail_price

                    # 链接编码
                    re_sku["链接编码"] = sku_qs.link_code
                    # 销量
                    re_sku["销量"] = sku_qs.sales
                    if self.is_distributor_mode:
                        re_sku["采购价"] = sku_qs.distributor_market_price
                    else:
                        # 需要权限
                        if current_user.has_perm("products.view_cost_price_StockKeepingUnit"):
                            re_sku["推广价"] = sku_qs.cost_price
                        else:
                            re_sku["推广价"] = "***"

                    re_sku["现货库存"] = sku_qs.physical_inventory
                    re_sku["7天补货库存"] = sku_qs.safety_inventory

                    history_price = None
                    history_price_author_id = None
                    history_price_author_name = None
                    latest = HistoryPrice.objects.filter(sku_id=sku_qs.id).order_by("-create_date").first()
                    if latest:
                        history_price = latest.history_price
                        if latest.author:
                            history_price_author_id = latest.author.author_id
                            history_price_author_name = latest.author.name
                    re_sku["历史价"] = history_price
                    re_sku["历史价主播id"] = history_price_author_id
                    re_sku["历史价主播名"] = history_price_author_name
                    re_sku["是否已播"] = "是" if sku_qs.has_live else "否"

                    re_data.append(re_sku)
            filename = f"我的商品列表{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
            byte_buffer = get_excel_async("商品", re_data, image_columns=[3], is_internal=True)
            if byte_buffer:
                byte_buffer.seek(0)

            return FileResponse(byte_buffer, filename=filename, as_attachment=True)
        except Exception as e:
            logger.info(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class MyProductDetailView(DBAPIView):
    """
    我的商品 单个商品
    """

    need_format_resource_name = True
    fronted_page = "我的商品"
    resource_name = "我的商品"

    @staticmethod
    def _get_object(product_id: int, request: Request) -> SubProduct:
        try:
            sub_product_obj = SubProduct.get_distributor_sub_products(request).get(product_id=product_id)
        except SubProduct.DoesNotExist:
            raise APIViewException(err_message="data not found")
        return sub_product_obj

    def get(self, request: Request, product_id: int):
        """
        我的商品 -> 商品详情
        :param request:
        :param product_id:
        :return:
        """
        try:
            sub_product_obj = self._get_object(product_id, request)
            product_detail_ser = DBMyProductDetailSer(
                instance=sub_product_obj,
                context={
                    "request": self.request,
                    "is_distributor_mode": self.is_distributor_mode,
                },
            )
            data = product_detail_ser.data

            parent_product_info = data.pop("parent_product_info")

            data["parent_product_id"] = parent_product_info.pop("product_id")
            data["parent_product_name"] = parent_product_info.pop("name")
            data["parent_product_code"] = parent_product_info.pop("code")
            # 分类
            category = parent_product_info.pop("category")
            category_list = get_category_name_by_id(category)
            data["category"] = category_list

            # get owner's distributor letters
            letters = sub_product_obj.owner.letters
            if not letters:
                return IResponse(code=500, message=f"subproduct({product_id}) owner's letters not found")

            # sku
            skus_qs = sub_product_obj.parent_product.stockkeepingunit_set.filter(become_history=False)
            if self.is_distributor_mode:
                sku_list = DBMarketSKUDetailSer(instance=skus_qs, many=True, context={"request": self.request, "letters": letters}).data
            else:
                sku_list = DBSKUDetailSer(instance=skus_qs, many=True, context={"request": self.request, "letters": letters}).data
            data["skus"] = sku_list
            # 商品参数
            attr_options = sub_product_obj.parent_product.productattroption_set.select_related("attr", "attr_value")
            data["attr_options"] = ProductAttrOptionInfoSer(instance=attr_options, many=True).data
            data.update(parent_product_info)
            return IResponse(data=data)
        except Exception as e:
            logger.error(f"{traceback.format_exc()} --> {e}")
            return IResponse(code=400, message=str(e))

    def post(self, request: Request, product_id: int):
        """
        单个商品加入我的商品
        注意该product_id为原数据的商品id
        """
        try:
            current_distributor = request.user.distributor
            product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message=f'商品"{product_id}"不存在')

        if product.state != 1:
            return IResponse(code=400, message=f"商品:{product}未上架,无法加入我的商品库")
        if self.is_distributor_mode:
            try:
                sub_product = SubProduct.objects.get(
                    parent_product__product_id=product_id,
                    parent_product__state=1,
                    parent_product__is_in_distributor_market=True,
                    is_deleted=False,
                    owner__letters="FX",
                    parent_product__is_deleted=False,
                )
                if DistributorMarketSubProductRelate.objects.filter(sub_product=sub_product, owner_id=self.current_user.distributor.distributor_id).exists():
                    return IResponse(code=400, message="该商品已加入到我的商品,无须重新加入")
                else:
                    DistributorMarketSubProductRelate.objects.create(
                        sub_product=sub_product,
                        owner_id=self.current_user.distributor.distributor_id,
                        create_user=self.current_user.user_id,
                    )
                    return IResponse()
            except SubProduct.DoesNotExist:
                return IResponse(code=400, message=f'商品"{product_id}"不存在或未上架')
        if SubProduct.objects.filter(
            parent_product__product_id=product_id,
            owner__id=self.current_user.distributor_id,
        ).exists():
            return IResponse(code=400, message="该商品已加入到我的商品,无须重新加入")

        post_data = product.__dict__
        post_data["parent_product"] = product.pk
        post_data["owner"] = self.current_user.distributor.distributor_id
        post_data["create_user"] = self.current_user.user_id
        ser = MyProductCreateSer(data=post_data)
        if not ser.is_valid():
            raise FieldsError(ser.errors)

        with transaction.atomic():
            # 如果是组合商品, 把包含的商品先添加到我的商品
            if product.is_combine:
                product_list = product.substockkeepingunit_set.filter(become_history=False).values_list("relate_sku__product__product_id", flat=True)
                bulk_add_to_my_product(product_list, self.current_user)

            my_product = ser.save()

            # 创建关联分销商货号数据
            ProductLinkDistributor(
                product=product,
                distributor=current_distributor,
                code=my_product.code,
                create_user=request.user.user_id,
            ).save()

            # 判断是否有
            ProductSelectionItem.objects.filter(
                product_id=product_id,
                selection_plan__distributor=current_distributor,
                selection_plan__state__in=[1, 2],
            ).update(
                sub_product=my_product,
            )

        return IResponse()

    def patch(self, request, product_id):
        """
        编辑我的商品
        只支持标题、主图、详情图、备注、尺寸编辑
        """
        sub_product = self._get_object(product_id, request)

        raw_sub_product = copy.deepcopy(sub_product)
        raw_images = sub_product.main_images
        raw_data = request.data
        raw_data["update_user"] = self.current_user.user_id
        ser = MyProductCodeUpdateSer(instance=sub_product, data=raw_data, partial=True, context={"request": request})
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        instance = ser.save()

        # 有新增的图片，上传至qdrant
        new_images = instance.main_images
        images = list(set(new_images) - set(raw_images))
        if images:
            add_images_to_qdrant.delay(sub_product.parent_product_id, list(set(images)))

        # 被删除的图片，需要从qdrant中删除
        del_imags = list(set(raw_images) - set(new_images))
        for d_img in del_imags:
            delete_images_from_qdrant.delay(sub_product.parent_product.id, raw_data=d_img)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(product_id),
                model=SubProduct,
                describe=f"更新我的商品.商品ID:{sub_product}",
                raw_object=raw_sub_product,
                new_object=instance,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class MyProductCombineProductDetailView(DBAPIView):
    @staticmethod
    def get_object(product_id) -> SubProduct:
        try:
            product_obj = SubProduct.objects.get(product_id=product_id, is_deleted=False, parent_product__is_combine=True)
        except SubProduct.DoesNotExist:
            raise APIViewException(err_message="data not found")

        return product_obj

    def get(self, request: Request, product_id: int):
        product_obj = self.get_object(product_id)
        data = MyProductCombineProductDetailSer(instance=product_obj, many=False, context={"request": request}).data
        return IResponse(data=data)


class MyProductHistoryPriceView(DBAPIView):
    permission_classes = [IsAuthenticated]
    fronted_page = "我的商品"
    resource_name = "历史价"
    need_format_resource_name = True

    def _get_object(self, sub_product_id: int) -> SubProduct:
        try:
            sub_product = SubProduct.objects.get(
                product_id=sub_product_id,
                is_deleted=False,
                owner=self.current_user.distributor,
            )
        except Product.DoesNotExist:
            raise APIViewException(code=400, err_message="data not found")

        if not sub_product.parent_product:
            raise APIViewException(code=400, err_message="该尚未绑定主商品")

        if sub_product.parent_product.is_deleted:
            raise APIViewException(code=400, err_message="该商品的主商品已被删除,无法添加历史价")
        return sub_product

    def get(self, request: Request, product_id: int):
        """
        拉取历史价列表
        """

        sub_product = self._get_object(product_id)

        # 复制query
        raw_params: dict = request.query_params.copy()

        filters = {}
        # 获取传递过来的author id列表，如果为空，默认查询全部
        author_id_list = raw_params.pop("author_id", [])
        if author_id_list:
            filters["author_id"] = author_id_list[0]

        # 获取传递过来的sku name，如果为空，默认查询全部
        # 根据sku spec select 接口的响应结果进行筛选
        spec_name = raw_params.pop("spec_name", "")

        # sku_qs = sub_product.parent_product.stockkeepingunit_set.filter(become_history=False).only("id")
        sku_qs = sub_product.parent_product.stockkeepingunit_set.all().only("id")

        if spec_name:
            spec_values_list = spec_name[0].split(",")
            sku_qs = sku_qs.filter(specs_value__value__in=spec_values_list)

        # 如果没有匹配的sku信息为None查询
        filters["sku_id__in"] = json.dumps([sku.id for sku in sku_qs] or [None])
        raw_params.update(**filters)

        # 结果查询
        page_history_prices, re_data, obj_qs = custom_filter(
            raw_params,
            HistoryPrice,
        )
        history_price_data = HistoryPriceSerializer(instance=page_history_prices, many=True).data
        temp = {}
        for data in history_price_data:
            create_date = data.pop("create_date")
            if create_date not in temp:
                temp[create_date] = []
            temp[create_date].append(data)
        result = []
        for k, v in temp.items():
            result.append({"create_date": k, "data": v})

        re_data["data"] = result
        return IResponse(data=re_data)

    def post(self, request: Request, product_id: int):
        """
        新增历史价
        [
            {
            "sku_id": 12345678,
            "history_price": 15.98,
            "author_id": 1038355500373918
            }
        ]
        """
        try:
            sub_product = self._get_object(product_id)

            raw_data = request.data
            current_user = self.current_user
            current_user_type = get_current_user_type(request)

            for item in raw_data:
                item["create_user"] = current_user.user_id
                # 运营商逻辑
                if current_user_type == "OP":
                    if current_user.live_author and not item.get("author_id"):
                        item["author_id"] = current_user.live_author.author_id
                # 判断时候为分销商用户
                if current_user_type == "DB":
                    current_user_distributor = current_user.distributor
                    if not current_user_distributor:
                        return IResponse(code=403, message="user not bind distributor")

                    if not item.get("author_id") and not current_user_distributor.live_author:
                        return IResponse(code=400, message="distributor not bind live author")

                    author_id = item.get("author_id")
                    if author_id:
                        if not LiveAuthor.objects.filter(author_id=author_id).exists():
                            raise APIViewException(err_message="主播不存在,请刷新列表后重试")
                    else:
                        author_id = current_user_distributor.live_author_id

                    item["author_id"] = author_id

            insert_history_price(
                sub_product.parent_product.product_id,
                raw_data,
                sub_product_id=product_id,
            )

            # 日志记录
            try:
                operate_content = [f"SKU_ID: {item['sku_id']},  历史价格:{item['history_price']}, 主播ID: {item['author_id']}" for item in raw_data]
                set_log_params(
                    request,
                    resource_id=str(product_id),
                    model=Product,
                    describe=f"我的商品: {product_id} 新增了历史价",
                    operate_content="、".join(operate_content),
                    content=raw_data,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse()
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}")
            return IResponse(code=500, message=str(e))


class MyProductHistoryPriceDeleteView(DBAPIView):
    resource_name = "删除历史价"
    fronted_page = "我的商品"
    need_format_resource_name = False

    def delete(self, request: Request, product_id: int, create_date: str):
        """
        删除历史价
        """
        try:
            try:
                sub_product = SubProduct.objects.get(
                    product_id=product_id,
                    is_deleted=False,
                    owner=self.current_user.distributor,
                    parent_product__is_deleted=False,
                )
            except Product.DoesNotExist:
                return IResponse(code=400, message="data not found")

            sku_qs = sub_product.parent_product.stockkeepingunit_set.filter(become_history=False).values("id")
            sku_ids = [sku["id"] for sku in sku_qs]
            his_price_qs = HistoryPrice.objects.filter(sku_id__in=sku_ids, create_date=create_date)
            raw_data = HistoryPriceSerializer(instance=his_price_qs, many=True).data
            his_price_qs.delete()

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=str(product_id),
                    model=Product,
                    describe=f"删除历史价,商品ID:{product_id}",
                    original_content=raw_data,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            # 检查剩余历史价并更新商品对应值
            calculate_history_price(sub_product.parent_product.product_id)

            return IResponse()
        except Exception as e:
            logger.error(f"{traceback.format_exc()}-->{e}")
            return IResponse(code=500, message=str(e))


class MyProductHistoryLatestPriceView(DBAPIView):
    """
    获取最新sku历史价
    """

    def get(self, request: Request, product_id: int):
        try:
            try:
                sub_product = SubProduct.objects.get(
                    product_id=product_id,
                    is_deleted=False,
                    parent_product__is_deleted=False,
                    owner=self.current_user.distributor,
                )
            except SubProduct.DoesNotExist:
                return IResponse(code=400, message="data not found")

            sku_qs = sub_product.parent_product.stockkeepingunit_set.filter(become_history=False).values("id")
            sku_ids = [sku["id"] for sku in sku_qs]

            latest_prices = HistoryPrice.objects.filter(sku_id__in=sku_ids).values("sku_id").annotate(latest_create_date=Max("create_date"))
            latest_data = []
            for price in latest_prices:
                latest_price = HistoryPrice.objects.filter(sku_id=price["sku_id"], create_date=price["latest_create_date"]).first()
                his_price_date = HistoryPriceSerializer(instance=latest_price).data
                latest_data.append(his_price_date)

            return IResponse(code=200, data=latest_data)
        except Exception as e:
            return IResponse(code=500, message=str(e))


class MyProductHistoryPriceSKUSelectView(DBAPIView):
    """
    直播历史价格SKU筛选列表
    """

    def get(self, request: Request, product_id: int):
        try:
            sub_product = SubProduct.objects.get(
                product_id=product_id,
                is_deleted=False,
                owner=self.current_user.distributor,
                parent_product__is_deleted=False,
            )
        except SubProduct.DoesNotExist:
            return IResponse(data=[])

        skus = sub_product.parent_product.stockkeepingunit_set.filter(become_history=False)
        re_data = []

        for sku in skus:
            re_data.append(",".join(sku.specs_value.values_list("value", flat=True)))

        return IResponse(data=re_data)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@operate_log_decorator("货盘复制", "货盘计划")
def db_copy_selection_plan_view(request, plan_id):
    """
    复制货盘计划
    """
    # 创建货盘计划
    raw_data = request.data
    current_user = request.user
    if request.auth.get("user_type") != "DB":
        return IResponse(code=403, message="no permission to operate")
    try:
        plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")

    if plan.state != 0:
        return IResponse(code=400, message="货盘计划尚未结束，无法进行复制")

    raw_data["create_user"] = current_user.user_id
    raw_data["update_user"] = current_user.user_id

    # 获取分销商
    if not current_user.distributor:
        return IResponse(code=400, message="user not bind distributor")

    live_date_end = raw_data.get("live_date_end")
    assert live_date_end, "live_date_end required"
    if convert_to_ware_time(live_date_end, "%Y-%m-%d").date() < timezone.now().date():
        return IResponse(code=400, message="live_date_end should be a future date")

    raw_data["distributor"] = current_user.distributor.distributor_id
    serializer = DBProductSelectionPlanSerializer(data=raw_data)
    if not serializer.is_valid():
        raise FieldsError(serializer.errors)
    selection_plan = serializer.save()

    # 获取原货盘计划的商品创建item
    # new_items = []
    # 过滤has live
    # 获取原货盘计划的商品创建item
    # 不需要过滤已播、未播
    user_id = current_user.user_id
    new_plan_id = selection_plan.plan_id
    create_user_type = request.auth.get("user_type")
    async_move_old_plan_product_to_new_plan_invoker.delay(plan_id, user_id, new_plan_id, create_user_type)

    # 日志记录
    try:
        set_log_params(
            request,
            resource_id=new_plan_id,
            model=ProductSelectionPlan,
            describe=f"复制货盘:{plan}",
            operate_content=f"复制货盘【{plan}】到新货盘【{selection_plan}】",
            is_success_input=True,
        )
    except Exception as e:
        logger.warning(f"set_log_params catch error: {e}")
        pass

    return IResponse(code=202)


class DBProductSelectionPlanProfitMarginView(DBAPIView):
    need_format_resource_name = False
    fronted_page = "货盘利润"
    resource_name = "货盘利润"

    def get(self, request, plan_id):
        if not self.current_user.can_view_plan_profit:
            return IResponse(code=400, message="no permission to operate")

        re_data, _ = plan_profit_margin(request, plan_id)
        if not re_data:
            return IResponse(code=400, message="data not found")
        return IResponse(data=re_data)

    def post(self, request, plan_id):
        return common_modify_estimated_sales(request, plan_id)


class DBProductSelectionPlanProfitMarginDownloadView(DBAPIView):
    need_format_resource_name = False
    fronted_page = "货盘利润-下载"
    resource_name = "货盘利润-下载"

    def get(self, request, plan_id):
        if not self.current_user.can_view_plan_profit:
            return IResponse(code=400, message="no permission to operate")

        data, plan_name = plan_profit_margin_download_data(request, plan_id)
        byte_buffer = get_excel_async("货盘利润", data, image_columns=[3], is_internal=True)
        if byte_buffer:
            byte_buffer.seek(0)
        filename = f"货盘利润：{plan_name}-{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
        return FileResponse(byte_buffer, filename=filename, as_attachment=True)


class DBProductSelectionPlanView(DBAPIView):
    """
    分销商货盘计划
    """

    fronted_page = "货盘列表"
    resource_name = "货盘计划"
    need_format_resource_name = True

    def post(self, request):
        """
        新增货盘计划
        """
        try:
            raw_data = request.data
            current_user = request.user
            if request.auth.get("user_type") != "DB":
                return IResponse(code=403, message="no permission to operate")
            raw_data["create_user"] = current_user.user_id
            raw_data["update_user"] = current_user.user_id

            # 获取分销商
            if not current_user.distributor:
                return IResponse(code=400, message="user not bind distributor")
            raw_data["distributor"] = current_user.distributor.distributor_id

            live_date_end = raw_data.get("live_date_end")
            assert live_date_end, "live_date_end required"
            if convert_to_ware_time(live_date_end, "%Y-%m-%d").date() < timezone.now().date():
                return IResponse(code=400, message="live_date_end should be a future date")

            serializer = DBProductSelectionPlanSerializer(data=raw_data)
            if serializer.is_valid():
                product_plan = serializer.save()
                data = DBProductSelectionPlanReaderSerializer(instance=product_plan).data

                # 写入日志
                w_plan_record(
                    plan_id=product_plan.plan_id,
                    record_type=ProductSelectionPlanRecordType.ADD_PLAN,
                    content="",
                    create_user=current_user.user_id,
                )

                # 日志记录
                try:
                    set_log_params(
                        request,
                        resource_id=str(product_plan.plan_id),
                        model=ProductSelectionPlan,
                        describe=f"新增了货盘计划. 货盘:{product_plan}",
                        is_success_input=True,
                    )
                except Exception as e:
                    logger.warning(f"set_log_params catch error: {e}")
                    pass

                return IResponse(data=data)
            raise FieldsError(serializer.errors)
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def get(self, request):
        """
        货盘计划列表
        """
        try:
            target = ProductSelectionPlan.objects.filter(distributor_id=self.current_distributor.distributor_id).order_by("-live_date_end")
            re_data, page_plans, obj_qs = custom_django_filter(
                request,
                target,
                SelectionPlanFilterSet,
                need_serialize=False,
                force_order=False,
            )
            page_plans_data = DBProductSelectionPlanListSerializer(instance=page_plans, many=True).data
            for plan_data in page_plans_data:
                # 获取正在编辑的用户
                plan_id = plan_data.get("plan_id")
                user_id = read_lock(plan_id)
                real_name = None
                if user_id:
                    try:
                        user = User.objects.get(user_id=user_id)
                    except User.DoesNotExist:
                        logger.error(f"user {user_id} not exist")
                        real_name = f"user {user_id} not exist"
                    else:
                        real_name = user.real_name if user.real_name else user.user_id
                plan_data["editing_user"] = real_name
            re_data["data"].extend(page_plans_data)
            return IResponse(data=re_data)
        except FieldError as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=400)
        except Exception as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=500)


class DBProductSelectionPlanDetailView(OperateLogAPIViewMixin, APIView):
    """
    分销商货盘计划详情
    """

    permission_classes = [IsAuthenticated]
    fronted_page = "货盘详情"
    resource_name = "货盘计划"
    need_format_resource_name = True

    def put(self, request, plan_id):
        """
        编辑货盘计划
        """
        raw_data = request.data
        current_user = request.user
        if request.auth.get("user_type") != "DB":
            return IResponse(code=403, message="no permission to operate")
        raw_data["update_user"] = current_user.user_id

        # 检查状态
        if not current_user.distributor:
            return IResponse(code=400, message="user not bind distributor")
        try:
            selection_plan = ProductSelectionPlan.objects.get(
                plan_id=plan_id,
                distributor_id=current_user.distributor.distributor_id,
            )
        except ProductSelectionPlan.DoesNotExist:
            return IResponse(code=400, message="data not found")
        if selection_plan.state in [0, 2]:
            return IResponse(code=400, message="this state can't edit")

        # 查看是否有人正在编辑
        # user_id = read_lock(plan_id)
        # if user_id and current_user.user_id != user_id:
        #     return IResponse(code=400, message="plan is editing by other user")

        # 原始数据，用于日志记录
        original_content = deepcopy(selection_plan.__dict__)
        original_content.pop("_state")

        origin_model = deepcopy(selection_plan)

        # 获取分销商
        if not current_user.distributor:
            return IResponse(code=400, message="user not bind distributor")
        raw_data["distributor"] = current_user.distributor.distributor_id

        live_date_end = raw_data.get("live_date_end")
        if live_date_end and convert_to_ware_time(live_date_end, "%Y-%m-%d").date() < timezone.now().date():
            return IResponse(code=400, message="live_date_end should be a future date")

        serializer = DBProductSelectionPlanSerializer(instance=selection_plan, data=raw_data, partial=True)
        if serializer.is_valid():
            _plan = serializer.save()
            data = DBProductSelectionPlanReaderSerializer(instance=_plan).data
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=plan_id,
                    model=ProductSelectionPlan,
                    describe=f"修改了货盘计划: {selection_plan}",
                    raw_object=origin_model,
                    new_object=_plan,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            # 写入操作明细日志
            w_plan_record(
                plan_id=_plan.plan_id,
                record_type=ProductSelectionPlanRecordType.MOD_PLAN,
                content=f"{diff_models(origin_model, _plan)}",
                create_user=current_user.user_id,
            )

            return IResponse(data=data)
        raise FieldsError(serializer.errors)

    def patch(self, request, plan_id):
        """
        只用于锁定和解锁
        """
        raw_data = request.data
        current_user = request.user
        if request.auth.get("user_type") != "DB":
            return IResponse(code=403, message="no permission to operate")
        raw_data["update_user"] = current_user.user_id

        # 校验是否是自己分销商商名下的计划
        if not current_user.distributor:
            return IResponse(code=400, message="user not bind distributor")
        try:
            selection_plan = ProductSelectionPlan.objects.get(
                plan_id=plan_id,
                distributor_id=current_user.distributor.distributor_id,
            )
        except ProductSelectionPlan.DoesNotExist:
            return IResponse(code=400, message="data not found")

        # 查看是否有人正在编辑
        user_id = read_lock(plan_id)
        if user_id:
            return IResponse(code=400, message="plan which editing state can't be locked")

        state = raw_data.get("state")
        assert state in [1, 2], "state should be in [1, 2]"
        new_data = {"state": state}
        if selection_plan.state == state:
            return IResponse(code=400, message="no need to operate on this resource")

        # 保存状态
        selection_plan.state = state
        selection_plan.save(update_fields=["state"])

        w_plan_record(
            plan_id=selection_plan.plan_id,
            record_type=ProductSelectionPlanRecordType.LOCK_PLAN if state == 2 else ProductSelectionPlanRecordType.RELEASE_PLAN,
            content="",
            create_user=current_user.user_id,
        )
        data = DBProductSelectionPlanReaderSerializer(instance=selection_plan).data
        return IResponse(data=data)


def plan_state_checker(plan_id):
    """
    检查货盘状态
    """
    try:
        selection_plan = ProductSelectionPlan.objects.get(plan_id=plan_id)
    except ProductSelectionPlan.DoesNotExist:
        raise ValueError("data not found")
    if selection_plan.state in [0, 2]:
        raise ValueError("this state can't edit")


class DBPlanCompanyDetailView(APIView):
    """
    货盘计划公司管理
    """

    permission_classes = [IsAuthenticated]

    def patch(self, request):
        """
        货盘公司顺序移动
        raw_data = [
                {
                    "plan_company_id": 1,
                    "order": 2
                },
            ]

        """
        try:
            current_user = request.user
            if request.auth.get("user_type") != "DB":
                return IResponse(code=403, message="no permission to operate")
            raw_data = request.data
            try:
                with transaction.atomic():
                    for data in raw_data:
                        plan_company_id = data.get("plan_company_id")
                        order = data.get("order")
                        try:
                            plan_company = ProductSelectionPlanCompany.objects.get(id=plan_company_id, is_deleted=False)
                        except ProductSelectionPlanCompany.DoesNotExist:
                            raise ValueError("data not found")
                        plan = plan_company.selection_plan
                        # 先查货盘计划状态
                        plan_state_checker(plan.plan_id)
                        user_id = read_lock(plan.plan_id)
                        if current_user.user_id != user_id:
                            raise ValueError("Not granted editing permissions")
                        # 更新顺序
                        plan_company.order = order
                        plan_company.save(update_fields=["order"])
            except IntegrityError as e:
                logger.error(str(e))
                raise ValueError(str(e))

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class DBPlanCategoryDetailView(APIView):
    """
    货盘计划分类管理
    """

    permission_classes = [IsAuthenticated]

    def patch(self, request):
        """
        货盘分类顺序移动
        raw_data = [
                {
                    "plan_category_id": 1,
                    "order": 2
                },
            ]
        """

        try:
            current_user = request.user
            if request.auth.get("user_type") != "DB":
                return IResponse(code=403, message="no permission to operate")
            raw_data = request.data
            try:
                with transaction.atomic():
                    for data in raw_data:
                        plan_category_id = data.get("plan_category_id")
                        order = data.get("order")

                        try:
                            plan_category = ProductSelectionPlanCategory.objects.get(id=plan_category_id, is_deleted=False)
                        except ProductSelectionPlanCategory.DoesNotExist:
                            raise ValueError("data not found")
                        # 查计划
                        plan = plan_category.plan_company.selection_plan
                        # 先查货盘计划状态
                        plan_state_checker(plan.plan_id)
                        user_id = read_lock(plan.plan_id)
                        if current_user.user_id != user_id:
                            raise ValueError("Not granted editing permissions")
                        # 更新顺序
                        plan_category.order = order
                        plan_category.save(update_fields=["order"])
            except IntegrityError as e:
                logger.error(str(e))
                raise ValueError(str(e))

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class DBPlanProductDetailView(APIView):
    """
    货盘计划商品条目管理
    raw_data = [
        {
            "plan_product_id": 1,
            "order": 2
        },
    ]
    """

    permission_classes = [IsAuthenticated]

    def patch(self, request):
        """
        货盘商品顺序移动
        """

        try:
            current_user = request.user
            if request.auth.get("user_type") != "DB":
                return IResponse(code=403, message="no permission to operate")
            raw_data = request.data
            try:
                with transaction.atomic():
                    for data in raw_data:
                        plan_product_id = data.get("plan_product_id")
                        order = data.get("order")

                        try:
                            plan_product = ProductSelectionItem.objects.get(id=plan_product_id, is_deleted=False)
                        except ProductSelectionItem.DoesNotExist:
                            raise ValueError("data not found")
                        # 查计划
                        plan = plan_product.plan_category.plan_company.selection_plan
                        # 先查货盘计划状态
                        plan_state_checker(plan.plan_id)
                        user_id = read_lock(plan.plan_id)
                        if current_user.user_id != user_id:
                            raise ValueError("Not granted editing permissions")
                        # 更新顺序
                        plan_product.order = order
                        plan_product.save(update_fields=["order"])
            except IntegrityError as e:
                logger.error(str(e))
                raise ValueError(str(e))

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class DBProductSelectionPlanManagerView(DBAPIView):
    """
    货盘计划商品管理
    """

    fronted_page = "货盘计划"
    resource_name = "货盘商品管理"
    need_format_resource_name = False

    @staticmethod
    def plan_state_checker(plan_id, distributor_id) -> ProductSelectionPlan:
        try:
            selection_plan = ProductSelectionPlan.objects.get(
                plan_id=plan_id,
                distributor_id=distributor_id,
            )
        except ProductSelectionPlan.DoesNotExist:
            raise ValueError("data not found")
        if selection_plan.state in [0, 2]:
            raise ValueError("this state can't edit")
        return selection_plan

    def post(self, request, plan_id, product_id):
        """
        商品加入货盘计划

        {"sku_data":[{"sku_id":107172751,"physical_inventory":7}]}
        {"sku_data":[{"sku_id":150401794,"estimated_sales":0,"physical_inventory":12}],"selection_reason":"1423"}

        estimated_sales 跟 physical_inventory 一起变动，不改变可用库存的计算
        """
        try:
            raw_data = request.data
            current_user = request.user
            # 先查货盘计划状态
            selection_plan = self.plan_state_checker(plan_id, self.current_user.distributor.distributor_id)

            # 其他人正在编辑中
            user_id = read_lock(plan_id)
            if user_id and current_user.user_id != user_id:
                return IResponse(code=400, message="The pallet table is being modified and adding products is not supported")
            # 加入货盘计划
            sku_data = raw_data.get("sku_data")
            selection_reason = raw_data.get("selection_reason")

            item = common_add_product_to_plan_fc(
                current_user.user_id,
                plan_id,
                product_id,
                selection_plan,
                sku_data,
                self.current_user_type,
                distributor_mode=self.is_distributor_mode,
                selection_reason=selection_reason,
            )
            data = ProductSelectionItemSerializer(instance=item).data

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=plan_id,
                    model=ProductSelectionPlan,
                    describe=f"添加商品到货盘计划: {selection_plan}. 商品ID: {product_id}",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(data=data)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def get(self, request, plan_id):
        """
        货盘计划的商品列表
        """
        try:
            # 分销模式特定函数
            if self.is_distributor_mode:
                return v2_distributor_mode_plan_tree_list(request, plan_id)

            return v2_plan_tree_list(request, plan_id)
        except Exception as e:
            logger.warning(f"db plan tree exception,{e},{traceback.format_exc()}")
            return IResponse(code=500, message="服务器内部错误")

    def patch(self, request, plan_id, product_id):
        """
        编辑货盘计划商品（主要编辑备注）
        """
        try:
            current_user = request.user
            if request.auth.get("user_type") != "DB":
                return IResponse(code=401, message="no permission to operate")
            # 先查货盘计划状态
            self.plan_state_checker(plan_id, current_user.distributor.distributor_id)

            user_id = read_lock(plan_id)
            if current_user.user_id != user_id:
                return IResponse(code=400, message="Not granted editing permissions")
            remark = request.data.get("remark")
            try:
                item = ProductSelectionItem.objects.get(selection_plan=plan_id, product=product_id, is_deleted=False)
            except ProductSelectionItem.DoesNotExist:
                logger.error(f"ProductSelectionItem({plan_id}-{product_id}) not found")
                return IResponse(code=400, message="data not found")

            if item.product.is_deleted:
                return IResponse(code=400, message="商品已删除,无法进行编辑操作")

            # 记录原数据
            original_content = item.remark

            item.remark = remark
            item.save()

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=plan_id,
                    model=ProductSelectionPlan,
                    describe=f'更新了货盘计划"{plan_id}"的商品"{product_id}"备注',
                    operate_content=f'更新内容. 备注: "{original_content}"修改为"{remark}"',
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            # 写入操作明细日志
            if not original_content:
                record_type = ProductSelectionPlanRecordType.ADD_REMARK
                plan_record_content = f"新增备注: {remark}"
            else:
                record_type = ProductSelectionPlanRecordType.MOD_REMARK
                plan_record_content = f'备注: "{original_content}]"修改为"{remark}"'

            w_plan_record(
                plan_id=plan_id,
                record_type=record_type,
                content=plan_record_content,
                create_user=current_user.user_id,
                product_id=product_id,
            )

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def delete(self, request, plan_id, product_id):
        """
        从货盘计划中移除商品
        """
        try:
            current_user = request.user
            if request.auth.get("user_type") != "DB":
                return IResponse(code=401, message="no permission to operate")

            product = Product.objects.filter(product_id=product_id, is_deleted=False).select_related("company").first()
            if not product:
                return IResponse(code=400, message="data not found")

            # 先查货盘计划状态
            selection_plan = self.plan_state_checker(plan_id, current_user.distributor.distributor_id)

            user_id = read_lock(plan_id)
            if current_user.user_id != user_id:
                return IResponse(code=400, message="Not granted editing permissions")
            try:
                item = ProductSelectionItem.objects.get(selection_plan=plan_id, product=product_id, is_deleted=False)
            except ProductSelectionItem.DoesNotExist:
                logger.error(f"ProductSelectionItem({plan_id}-{product_id}) not found")
                return IResponse(code=400, message="data not found")

            with transaction.atomic():
                # 释放sku库存
                item_skus = item.productselectionitemsku_set.filter(become_history=False)
                for item_sku in item_skus:
                    sku = StockKeepingUnit.objects.select_for_update().get(sku_id=item_sku.sku_id)
                    # 减去sku的占用库存
                    sku.plan_use_inventory = F("plan_use_inventory") - (item_sku.physical_inventory or 0)
                    sku.save(update_fields=["plan_use_inventory", "can_use_inventory"])
                    # 组合商品释放占用库存
                    if sku.product.is_combine:
                        sub_skus = sku.parent_sub_skus.filter(become_history=False)
                        for sub_sku in sub_skus:
                            relate_sku = StockKeepingUnit.objects.select_for_update().get(sku_id=sub_sku.relate_sku_id)
                            relate_sku.plan_use_inventory = F("plan_use_inventory") - (item_sku.physical_inventory * sub_sku.num or 0)
                            relate_sku.save(update_fields=["plan_use_inventory"])

                item.is_deleted = True
                item.delete_reason = request.data.get("delete_reason") or ""
                item.update_user = current_user.user_id
                # 释放锁定状态
                item.product_confirm_state = 0
                item.save()

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=plan_id,
                    model=ProductSelectionPlan,
                    describe=f'货盘计划"{plan_id}"移除商品"{product_id}"',
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            # 插入操作明细日志
            w_plan_record(
                plan_id=plan_id,
                record_type=ProductSelectionPlanRecordType.DEL_PROD,
                content="",
                create_user=current_user.user_id,
                product_id=product_id,
            )

            # 创建消息通知
            notifications_data = {
                "product_id": product_id,
                "company_id": product.company.company_id,
                "notify_type": "plan",
                "operation_type": "removed_plan",
                "content": f"商品已从直播日期【{selection_plan.live_date_start}】货盘移除",
            }
            ceate_notifications.delay(notifications_data, user_type_list=["SP"])

            return IResponse(code=200)
        except ValueError as e:
            logger.error(f"{str(e)}")
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class DBProductSelectionPlanSKUInventoryManager(DBAPIView):
    """
    修改sku库存
    """

    need_format_resource_name = False
    fronted_page = "货盘计划"
    resource_name = "货盘库存"

    def get(self, request: Request, plan_id: int, product_id: int):
        re_data = common_query_item_skus_inventory(request, plan_id, product_id)
        return IResponse(data=re_data)

    def patch(self, request: Request, plan_id: int, product_id: int):
        return common_update_plan_item_skus_fc(request, plan_id, product_id)


class DBSelectionPlanProductDownloadView(DBAPIView):
    def get(self, request: Request, plan_id: int):
        """
        货盘计划-商品列表下载
        """
        try:
            return new_selection_plan_download_fc(request, plan_id)
        except Exception as e:
            logger.error(f"catch exception in db plan list download,{e}")
            return IResponse(code=500, message=str(e))


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def db_check_product_in_plan_view(request, plan_id):
    """
    用于前端检查商品是否在计划中
    """
    re_data = {}
    current_user = request.user
    if request.auth.get("user_type") != "DB":
        return IResponse(code=403, message="no permission to operate")
    if not current_user.distributor:
        return IResponse(code=400, message="user not bind distributor")

    raw_params = request.query_params.copy()
    # 前端参数转换
    if raw_params.get("name"):
        raw_params["name__icontains"] = raw_params.pop("name")[0]
    if raw_params.get("category"):
        raw_params["category"] = raw_params.pop("category")[0]
    if raw_params.get("physical_inventory"):
        raw_params["physical_inventory__range"] = raw_params.pop("physical_inventory")[0]
    if raw_params.get("cost_price"):
        raw_params["__cost_price__range"] = raw_params.pop("cost_price")[0]
    if raw_params.get("history_price"):
        raw_params["__history_price__range"] = raw_params.pop("history_price")[0]
    try:
        selection_plan = ProductSelectionPlan.objects.get(
            plan_id=plan_id,
            distributor_id=current_user.distributor.distributor_id,
        )
    except ProductSelectionPlan.DoesNotExist:
        return IResponse(code=400, message="data not found")

    item_qs = ProductSelectionItem.objects.filter(selection_plan_id=plan_id, is_deleted=False).values("product", "remark")
    data_dict = {}
    for item in item_qs:
        data_dict[item["product"]] = item.get("remark")
    product_ids = data_dict.keys()

    filters = dict(is_deleted=False, product_id__in=product_ids)

    # 获取商品
    page_products, re_data, plan_products_qs = custom_filter(
        raw_params,
        Product,
        array_fields=["category"],
        like_fields=["name", "product_id", "code", "productlinkdistributor__code"],
        hybrid_fields=["name", "product_id", "__code", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
        **filters,
    )
    re_data = []
    for product in plan_products_qs:
        data = CheckProductInOperateSelectionPlanSerializer(instance=product, context={"request": request}).data
        re_data.append(data)
    return IResponse(data=re_data)


class DBProductPlanBulkView(DBAPIView):
    permission_classes = [IsAuthenticated]

    need_format_resource_name = False
    resource_name = "批量处理商品"
    fronted_page = "货盘选品池"

    def post(self, request: Request, plan_id: int):
        """
        批量将商品加入货盘计划
        """
        post_data = request.data

        if "id_type" in post_data:
            # 共用批量加入的逻辑

            estimated_sales = 0
            selection_reason = ""
            correct_sku_inventory = False

            id_type = request.data.get("id_type", "product_id")
            if id_type not in ["product_id", "code"]:
                raise APIViewException(err_message="Invalid id_type")
            ids = request.data.get("ids", [])
            if not ids:
                raise APIViewException(err_message="please enter product id")
            if not isinstance(ids, list):
                raise APIViewException(err_message="ids must be a list")
            if len(ids) > 500:
                raise APIViewException(err_message="ids should be a array and len less than 500")

            # 获取分销商商品
            if id_type == "product_id":
                filter_condition = {"product_id__in": ids}
            else:
                product_pk_list = SubProduct.objects.filter(
                    code__in=ids,
                    is_deleted=False,
                    owner=self.current_distributor,
                ).values_list("parent_product_id", flat=True)
                filter_condition = {"pk__in": product_pk_list}

            try:
                product_id_list = list(Product.get_distributor_products(request).filter(**filter_condition).values_list("product_id", flat=True))
            except ValueError:
                product_id_list = []

        else:
            product_id_list = post_data.get("product_ids")
            if not product_id_list:
                raise APIViewException()

            if not isinstance(product_id_list, list):
                raise APIViewException(err_message="参数类型错误")

            estimated_sales = post_data.get("estimated_sales") or 0
            if not str(estimated_sales).isdigit():
                raise APIViewException()

            selection_reason = post_data.get("selection_reason") or ""
            correct_sku_inventory = True

        # 先查货盘计划状态
        try:
            selection_plan = ProductSelectionPlan.objects.only("state").get(
                plan_id=plan_id,
                distributor_id=self.current_distributor.distributor_id,
            )
        except ProductSelectionPlan.DoesNotExist:
            raise APIViewException(err_message="data not found")

        if selection_plan.state in [0, 2]:
            raise APIViewException(err_message="adding products is not supported in this state")

        bulk_add_products_to_plan(
            request,
            self.current_user,
            plan_id,
            product_id_list,
            self.current_user_type,
            estimated_sales=estimated_sales,
            selection_reason=selection_reason,
            correct_sku_inventory=correct_sku_inventory,
        )
        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(plan_id),
                model=ProductSelectionPlan,
                describe=f"批量加入商品到货盘计划:{selection_plan.name}",
                operate_content="商品ID：{}".format("、".join(product_id_list)),
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def delete(self, request: Request, plan_id: int):
        """
        批量删除
        :param request:
        :param plan_id:
        :return:
        """
        return bulk_delete_selection_products_fc(request, plan_id)


class DBDownloadProductListView(DBAPIView):
    """
    分销商下载商品列表文件
    """

    fronted_page = "商品列表"
    resource_name = "导出商品数据"
    need_format_resource_name = False

    def get(self, request: Request):
        try:
            re_data = []
            current_user = self.current_user
            raw_params = request.query_params.copy()
            and_Q = None
            specs_data = []
            # 前端参数转换
            # product_ids 为选品下载专用
            if raw_params.get("product_ids"):
                raw_params["product_id__in"] = raw_params.pop("product_ids")[0]

            product_list_params_handler(raw_params)

            if specs_data:
                and_Q = reduce(and_, specs_data)

            filters = dict(
                is_deleted=False,
            )

            target = Product.get_distributor_products(request)
            page_products, _, obj_qs = custom_filter(
                raw_params,
                array_fields=["category"],
                like_fields=["name", "product_id", "code", "remark", "productlinkdistributor__code"],
                hybrid_fields=["name", "product_id", "__code", "remark", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
                query_Q=and_Q,
                **filters,
            )
            # 查询所属地区
            address_ids = {product.address_id for product in page_products}
            address_objs = ProductAddress.objects.filter(id__in=address_ids)
            address_map = {address.id: address.name for address in address_objs}

            num = 0

            log_product_info = []

            for product_qs in page_products:
                re_product = {}
                product_data = ProductSerializer(instance=product_qs, context={"request": self.request}).data
                # tmeplate data
                for k, v in DBDownloadProductTmpl.items():
                    value = product_data.get(k)
                    re_product[v] = Product.PRODUCT_STATE_CHOICES[value][1] if k == "state" else value
                    if k == "address":
                        re_product[v] = address_map.get(product_qs.address_id, "")
                # 商品参数下载
                attr_download_info_handler(re_product, product_qs)
                # category
                category = product_data.get("category")
                category_id_and_name_list = get_category_name_by_id(category_list=category)
                name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目"}
                for i in range(len(category_id_and_name_list)):
                    re_product[f"{name_key[i]}ID"] = category_id_and_name_list[i].get("id")
                    re_product[f"{name_key[i]}名称"] = category_id_and_name_list[i].get("name")
                    if i == 3:
                        break
                # brand
                re_product["区域ID"], re_product["区域名称"] = None, None
                brand = product_data.get("brand")
                if brand:
                    brand_obj = Brand.objects.get(id=brand)
                    re_product["区域ID"], re_product["区域名称"] = brand_obj.id, brand_obj.name

                # sku
                skus_qs = StockKeepingUnit.objects.filter(product=product_qs, become_history=False)
                for sku_qs in skus_qs:
                    num += 1
                    re_sku = {
                        "序号": num,
                    }
                    re_sku.update(re_product)
                    re_sku["规格ID"] = sku_qs.sku_id
                    # spec
                    spec_option_list = get_sku_download_specs_option_list(sku_qs)
                    re_sku["规格"] = ";".join(spec_option_list)
                    re_sku["建议售价"] = sku_qs.retail_price

                    # 链接编码
                    re_sku["链接编码"] = sku_qs.link_code
                    # 销量
                    re_sku["销量"] = sku_qs.sales
                    # 需要权限
                    if current_user.has_perm("products.view_cost_price_StockKeepingUnit"):
                        re_sku["推广价"] = sku_qs.cost_price
                    else:
                        re_sku["推广价"] = "***"

                    re_sku["现货库存"] = sku_qs.physical_inventory
                    re_sku["7天补货库存"] = sku_qs.safety_inventory

                    history_price = None
                    history_price_author_id = None
                    history_price_author_name = None
                    latest = HistoryPrice.objects.filter(sku_id=sku_qs.id).order_by("-create_date").first()
                    if latest:
                        history_price = latest.history_price
                        if latest.author:
                            history_price_author_id = latest.author.author_id
                            history_price_author_name = latest.author.name
                    re_sku["历史价"] = history_price
                    re_sku["历史价主播id"] = history_price_author_id
                    re_sku["历史价主播名"] = history_price_author_name
                    re_sku["是否已播"] = "是" if sku_qs.has_live else "否"
                    re_data.append(re_sku)

                # 记录操作日志
                log_product_info.append(f"商品:{product_qs}")

            filename = f"商品列表{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
            picture_fields = {"主图列表": 2}
            byte_buffer = get_excel_async("商品", re_data, image_columns=[3], is_internal=True)
            if byte_buffer:
                byte_buffer.seek(0)

            # 日志记录
            try:
                operate_content = "导出了商品: " + "、".join(log_product_info) if log_product_info else ""
                set_log_params(
                    request,
                    model=Product,
                    describe="导出了商品",
                    operate_content=operate_content,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return FileResponse(byte_buffer, filename=filename, as_attachment=True)
        except Exception as e:
            logger.info(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def get_excel(self, sheet_name, data):
        byte_buffer = BytesIO()
        df = pd.DataFrame.from_dict(data)
        writer = pd.ExcelWriter(byte_buffer, engine="xlsxwriter")
        df.to_excel(writer, sheet_name=sheet_name, index=False)
        worksheet = writer.sheets[sheet_name]
        for idx, col in enumerate(df):
            length = len(col) * 2 + 1
            worksheet.set_column(idx, idx, length)
        writer.close()
        return byte_buffer


class DoudianConfirmView(DBAPIView):
    resource_name = "完成抖店链接"
    fronted_page = "商品详情"
    need_format_resource_name = False

    def post(self, request: Request, product_id):
        """
        已完成抖店链接
        """
        # 当前商品
        product_obj = Product.objects.filter(product_id=product_id, is_deleted=False).first()
        if not product_obj:
            return IResponse(code=400, message="data not found")

        if product_obj.has_DouDian_link:
            return IResponse(code=400, message="no need to operate on this resource")

        has_DouDian_link = request.data.get("has_DouDian_link")
        assert has_DouDian_link is not None, "has_DouDian_link required"
        # 更新
        product_obj.has_DouDian_link = has_DouDian_link
        product_obj.confirm_DouDian_link_user = self.current_user.user_id
        product_obj.update_date = timezone.now()
        product_obj.save(
            update_fields=(
                "has_DouDian_link",
                "confirm_DouDian_link_user",
                "update_date",
            )
        )

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=product_id,
                model=Product,
                describe=f"完成抖店链接.商品:{product_obj}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class DBSelectionPlanItemRecycleListView(DBAPIView):
    """货盘计划回收站商品列表"""

    def get(self, request: Request, selection_plan_id: int):
        item_product_id_list = ProductSelectionItem.objects.select_related("product", "sub_product").filter(
            selection_plan_id=selection_plan_id,
            is_deleted=True,
        )

        re_data, page_objects, _ = custom_django_filter(
            request,
            item_product_id_list,
            SelectionPlanItemRecycleListFilterSet,
            None,
            need_serialize=False,
        )

        list_data = SelectionPlanItemRecycleListSer(
            instance=page_objects,
            many=True,
            context={
                "request": request,
                "is_distributor_mode": self.is_distributor_mode,
            },
        ).data
        re_data["data"] = list_data
        return IResponse(data=re_data)


class DBSelectionPlanItemRecycleDetailView(DBAPIView):
    """货盘计划商品回收站管理"""

    resource_name = "货盘计划-回收站"
    fronted_page = "货盘回收站"
    need_format_resource_name = False

    def _get_obj(self, item_id: int):
        try:
            selection_item = ProductSelectionItem.objects.get(
                pk=item_id,
                is_deleted=True,
                selection_plan__distributor__id=self.current_user.distributor_id,
            )
        except ProductSelectionItem.DoesNotExist:
            raise APIViewException(err_message="资源不存在,请刷新页面后重新操作")
        selection_plan = selection_item.selection_plan.state
        if selection_item.selection_plan.state == 0:
            raise APIViewException(err_message="该货盘计划已结束,无法操作")
        if selection_item.selection_plan.state == 2:
            raise APIViewException(err_message="该货盘计划已锁定,无法操作")

        return selection_item

    @transaction.atomic
    def patch(self, request: Request, item_id: int):
        """还原商品到货盘计划"""
        selection_item = self._get_obj(item_id)

        plan_id = selection_item.selection_plan_id
        product_id = selection_item.product_id

        user_id = read_lock(plan_id)
        current_user_user_id = self.current_user.user_id
        if user_id and current_user_user_id != user_id:
            return IResponse(code=400, message="plan is editing by other user")

        # 判断是否在其他进行中的选品计划
        if ProductSelectionItem.objects.filter(
            product_id=product_id,
            selection_plan_id=plan_id,
            is_deleted=False,
        ).exists():
            return IResponse(code=400, message="该商品已在该货盘计划")

        # 扣库存
        ts_point = transaction.savepoint()
        # 修改数据为False
        selection_item.is_deleted = False
        selection_item.update_user = self.current_user.user_id
        selection_item.save()

        item_skus = selection_item.productselectionitemsku_set.filter(become_history=False)
        try_times = 3
        post_sku_data = [{"sku_id": item_sku.sku_id, "physical_inventory": item_sku.physical_inventory} for item_sku in item_skus]
        for sku_data in post_sku_data:
            for i in range(try_times):
                sku_id = sku_data.get("sku_id")
                physical_inventory = sku_data.get("physical_inventory", 0) or 0

                if not str(sku_id).isdigit():
                    transaction.savepoint_rollback(ts_point)
                    raise ValueError(f"非法SKU_ID:{sku_id}")

                try:
                    _sku = StockKeepingUnit.objects.select_related().get(sku_id=sku_id, become_history=False)
                except StockKeepingUnit.DoesNotExist:
                    transaction.savepoint_rollback(ts_point)
                    raise ValueError(f"SKU_ID:{sku_id}不存在")

                if (physical_inventory > _sku.can_use_inventory) or (physical_inventory > (_sku.physical_inventory - _sku.plan_use_inventory)):
                    transaction.savepoint_rollback(ts_point)
                    logger.info(f"加货盘库存不足 >> {sku_id},{product_id}")
                    raise ValueError("库存不足")

                # 判断组合商品库存
                if _sku.product.is_combine:
                    sub_skus = _sku.parent_sub_skus.filter(become_history=False)
                    for sub_sku in sub_skus:
                        relate_sku = sub_sku.relate_sku
                        act_need_inventory = sub_sku.num * physical_inventory

                        if (act_need_inventory > relate_sku.can_use_inventory) or (act_need_inventory > relate_sku.physical_inventory - relate_sku.plan_use_inventory):
                            transaction.savepoint_rollback(ts_point)
                            logger.info(f"组合商品加货盘库存不足 >> {sku_id},{product_id},{relate_sku}")
                            raise ValueError("组合商品库存不足")

                        # 更新关联sku的占用库存
                        relate_sku.plan_use_inventory = F("plan_use_inventory") + act_need_inventory
                        relate_sku.save(update_fields=["plan_use_inventory"])

                # 原sku库存,触发信号修改商品可用库存
                _sku.plan_use_inventory = F("plan_use_inventory") + physical_inventory
                _sku.save(update_fields=["plan_use_inventory"])
                break

        plan_record_remark = ""
        # 物理删除其他的回收站item数据
        need_delete_items = ProductSelectionItem.objects.filter(
            selection_plan_id=plan_id,
            product_id=product_id,
            is_deleted=True,
        ).exclude(id=item_id)
        if need_delete_items:
            original_content = json.dumps(
                [
                    {
                        "plan_id": plan_id,
                        "product_id": product_id,
                        "remark": item.remark,
                        "delete_reason": item.delete_reason,
                        "update_user": item.update_user,
                        "create_date": timezone.localtime(item.create_date).strftime(DATETIME_FORMAT),
                        "update_date": timezone.localtime(item.update_date).strftime(DATETIME_FORMAT),
                    }
                    for item in need_delete_items
                ],
                ensure_ascii=False,
            )

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=plan_id,
                    model=ProductSelectionPlan,
                    describe="货盘回收站恢复商品时,后台系统自动删除回收站相同数据",
                    operate_content=f"删除商品ID:{product_id},删除条数:{need_delete_items.count()}",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            plan_record_remark = f"还原后删除其他相同数据:{original_content}"
            need_delete_items.delete()
        transaction.savepoint_commit(ts_point)
        # 插入操作明细日志
        w_plan_record(
            plan_id=plan_id,
            record_type=ProductSelectionPlanRecordType.RESTORE_PROD,
            content="",
            create_user=current_user_user_id,
            product_id=product_id,
            remark=plan_record_remark,
        )
        return IResponse()


class DBSelectionPlanItemRecycleListDownloadView(DBAPIView):
    """回收站列表导出接口"""

    def get(self, request: Request, selection_plan_id: int):
        item_product_id_list = ProductSelectionItem.objects.select_related("product", "selection_plan", "sub_product").filter(
            selection_plan_id=selection_plan_id,
            is_deleted=True,
            # selection_plan__distributor__id=self.current_user.distributor_id,
            product__is_deleted=False,
        )
        _, page_objes, _ = custom_django_filter(
            request,
            item_product_id_list,
            SelectionPlanItemRecycleListFilterSet,
            need_serialize=False,
        )

        items = SelectionPlanItemRecycleListDownloadSer(instance=page_objes, many=True, context={"request": request}).data

        products_map = {selection_item.product_id: selection_item.product for selection_item in page_objes.object_list}

        tmpl = DBSelectionPlanItemRecycleListDownloadTmpl if not self.is_distributor_mode else DBM2SelectionPlanItemRecycleListDownloadTmpl

        re_data = []
        count = 1
        for item in items:
            tmp_data = {"序号": count}
            for k, v in tmpl.items():
                if k == "attr":
                    attr_download_info_handler(tmp_data, products_map.get(item.get("product_id")))
                    continue

                if k != "category":
                    tmp_data[v] = item.get(k, "")
                    continue
                tmp_category_list = ["一级分类", "二级分类", "三级分类"]
                for idx, category_value in enumerate(item.pop("category")):
                    tmp_data[f"{tmp_category_list[idx]}ID"] = category_value["id"]
                    tmp_data[f"{tmp_category_list[idx]}名称"] = category_value["name"]

            re_data.append(tmp_data)
            count += 1
        filename = f"货盘{selection_plan_id}回收站商品列表-{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
        byte_buffer = get_excel_async("回收站商品列表", re_data, image_columns=[6], is_internal=True)
        if byte_buffer:
            byte_buffer.seek(0)
        return FileResponse(byte_buffer, filename=filename, as_attachment=True)


class DBSelectionPlanRecordsCountsView(DBAPIView):
    """操作明细统计"""

    def get(self, request: Request, plan_id: int):
        print(self.current_user)
        cur_user_distributo_id = self.current_user.distributor.distributor_id

        print(cur_user_distributo_id)
        records = (
            ProductSelectionPlanRecord.objects.filter(
                selection_plan__distributor_id=cur_user_distributo_id,
                selection_plan_id=plan_id,
                record_type__in=(
                    ProductSelectionPlanRecordType.ADD_PROD,
                    ProductSelectionPlanRecordType.DEL_PROD,
                ),
            )
            .values("record_type", "create_user")
            .annotate(counts=Count("record_type"))
        )

        user_id_list = [record["create_user"] for record in records]
        users = User.objects.filter(user_id__in=user_id_list).only("user_id", "real_name")
        user_map = {str(user.user_id): user.real_name or user.username for user in users}

        tmp_dict = {}
        for record in records:
            user_id = record["create_user"]
            record_type = record["record_type"]

            data = {
                "record_type": record_type,
                "counts": record["counts"],
            }
            if user_id in tmp_dict:
                if record_type == ProductSelectionPlanRecordType.ADD_PROD:
                    tmp_dict[user_id]["data"].insert(0, data)
                else:
                    tmp_dict[user_id]["data"].append(data)

            else:
                tmp_dict[user_id] = {
                    "user_id": user_id,
                    "real_name": user_map.get(user_id, ""),
                    "data": [data],
                }

        return IResponse(data=tmp_dict.values())


class DBSelectionPlanRecordsView(DBAPIView):
    """明细记录"""

    def get(self, request: Request, plan_id: int):
        cur_user_distributo_id = self.current_user.distributor.distributor_id
        try:
            records = ProductSelectionPlanRecord.objects.filter(
                selection_plan__distributor_id=cur_user_distributo_id,
                selection_plan_id=plan_id,
            )
            re_data, page_objes, _ = custom_django_filter(
                request,
                records,
                ProductSelectionPlanRecordFilterSet,
                order_fields=("-create_date",),
                need_serialize=False,
            )

            # 查询用户
            user_id_list = set([record.create_user for record in page_objes])
            users = User.objects.filter(user_id__in=user_id_list).only("user_id", "username", "real_name")
            user_map = {str(user.user_id): f"{user.username}({user.real_name or user.user_id})" for user in users}

            today_date = timezone.now().date()
            yesterday_date = today_date - timedelta(days=1)

            tmp_data_dict = {}
            for obj in page_objes:
                record_date = timezone.localtime(obj.create_date).date()
                if record_date == today_date:
                    date_name = "今天"
                elif record_date == yesterday_date:
                    date_name = "昨天"
                else:
                    date_name = timezone.localtime(obj.create_date).strftime(DATE_FORMAT)

                time_str = timezone.localtime(obj.create_date).strftime(TIME_FORMAT)

                operator = user_map.get(obj.create_user)

                if obj.record_type in (
                    ProductSelectionPlanRecordType.ADD_PROD,
                    ProductSelectionPlanRecordType.DEL_PROD,
                    ProductSelectionPlanRecordType.RESTORE_PROD,
                ):
                    content = obj.get_record_type_display() + f" ID:{obj.product_id}"
                elif obj.record_type in (
                    ProductSelectionPlanRecordType.ADD_PLAN,
                    ProductSelectionPlanRecordType.MOD_PLAN,
                    ProductSelectionPlanRecordType.DEL_PLAN,
                    ProductSelectionPlanRecordType.LOCK_PLAN,
                    ProductSelectionPlanRecordType.RELEASE_PLAN,
                ):
                    content = obj.get_record_type_display() + f" ID:{obj.selection_plan_id}"
                else:
                    content = obj.get_record_type_display() + ". " + obj.content

                if date_name in tmp_data_dict:
                    # data append
                    tmp_data_dict[date_name]["data"].append(
                        {
                            "time": time_str,
                            "operator": operator,
                            "content": content,
                        }
                    )
                else:
                    tmp_data_dict[date_name] = {
                        "date_desc": date_name,
                        "data": [
                            {
                                "time": time_str,
                                "operator": operator,
                                "content": content,
                            }
                        ],
                    }
            re_data["data"] = tmp_data_dict.values()
            return IResponse(data=re_data)
        except:
            print(traceback.format_exc())
            return IResponse()


class DBCSProductSelectionPlanItemView(DBAPIView):
    """客服货盘计划列表数据"""

    def get(self, request: Request, plan_id: str):
        re_data = get_cs_selection_plan_items(
            request,
            plan_id,
            CSDBSelectionPlanProductListSerializer,
            DBCSSelectionPlanItemSKUDetailSerializer,
        )
        return IResponse(data=re_data)


class DBCSProductSelectionPlanItemLinkView(DBAPIView):
    """关联客服"""

    def patch(self, request: Request, plan_id: str, product_id: int):
        if not self.current_user.distributor:
            return IResponse(code=400, message="user not bind distributor")

        try:
            item_record = ProductSelectionItem.objects.get(
                selection_plan_id=plan_id,
                selection_plan__distributor_id=self.current_user.distributor.distributor_id,
                product_id=product_id,
                is_deleted=False,
            )
        except ProductSelectionItem.DoesNotExist:
            return IResponse(code=400, message="data not found")

        link_cs_id = request.data.get("link_cs_id") or 0
        link_cs_remark = request.data.get("link_cs_remark") or ""

        try:
            cs = CustomerService.objects.get(id=link_cs_id)
        except CustomerService.DoesNotExist:
            return IResponse(code=400, message="error request params")

        # 保存并写入明细日志
        old_record = deepcopy(item_record)
        item_record.link_cs = cs
        item_record.link_cs_remark = link_cs_remark
        item_record.save()
        # 写入操作明细日志
        w_plan_record(
            plan_id=plan_id,
            record_type=ProductSelectionPlanRecordType.MOD_CS_LINK,
            content=f"{diff_models(old_record, item_record)}",
            create_user=self.current_user.user_id,
            product_id=product_id,
        )
        return IResponse()


class DBCSProductSelectionPlanItemDownloadView(DBAPIView):
    """
    客服货盘表导出
    """

    def get(self, request: Request, plan_id: int):
        return download_cs_selection_plan_product_list_fc(
            request,
            plan_id,
            DBOperateSelectionPlanProductListDownloadSerializer,
            DBOperatorSelectionPlanDownloadProductTmpl,
        )


class DBSelectionMapItemMoveView(DBAPIView):
    """排品地图移动排序"""

    def patch(self, request: Request):
        return map_item_move_fc(request)


class DBSelectionPlanProdsMapView(DBAPIView):
    """
    货盘计划-排品地图
    """

    def get(self, request, plan_id: int):
        return selection_plan_prods_map_fc(request, plan_id)


class DBSelectionItemMoveView(DBAPIView):
    def post(self, request: Request, plan_id: int, plan_product_id: int):
        new_plan_id = request.data.get("new_plan_id")
        remark = request.data.get("remark", "")
        post_sku_data = request.data.get("sku_data")
        if not new_plan_id:
            return IResponse(code=400, message="invalid params")

        return selection_plan_item_move_fc(
            request,
            plan_id,
            new_plan_id,
            plan_product_id,
            remark,
            post_sku_data,
        )


class DBSelectionItemBulkMoveView(DBAPIView, OperateLogAPIViewMixin):
    resource_name = "批量移动"
    fronted_page = "货盘计划"
    need_format_resource_name = False

    def post(self, request: Request):
        bulk_move_items_to_new_plan(request, self.current_user)
        return IResponse(code=202)


class DBProductCopyView(DBAPIView):
    """商品复制"""

    resource_name = "商品复制"
    fronted_page = "商品列表"
    need_format_resource_name = False

    def post(self, request: Request, product_id: int):
        return product_copy_fc(request, product_id)


class DBLiveNeedsView(DBAPIView):
    """分销商直播需求列表"""

    def get(self, request: Request):
        live_needs = LiveNeeds.objects.filter(distributor__id=self.current_user.distributor_id)
        re_data, _, _ = custom_django_filter(
            request,
            live_needs,
            LiveNeedsFilterSet,
            LiveNeedsInfoSer,
        )
        return IResponse(data=re_data)

    def post(self, request: Request):
        """提报直播需求"""
        try:
            raw_data = request.data
            # 补充当前用户信息
            raw_data["create_user"] = self.current_user.user_id
            raw_data["distributor"] = self.current_user.distributor.distributor_id
            ser = LiveNeedsCreateSer(data=request.data)
            if not ser.is_valid():
                err = []
                for k, v in ser.errors.items():
                    err.append(f"{k}:{str(v[0])}")
                err_msg = "".join(err)
                return IResponse(code=400, message=err_msg)
            ser.save()
            return IResponse()
        except Exception as e:
            print(f">>save live needs error,{e},{traceback.format_exc()}")
            return IResponse(code=500, message="internal server error")


class DBLiveNeedsDetailView(DBAPIView):
    """直播需求详情"""

    def get(self, request: Request, needs_id: int):
        try:
            live_needs = LiveNeeds.objects.get(
                needs_id=needs_id,
                distributor__id=self.current_user.distributor_id,
            )
        except LiveNeeds.DoesNotExist:
            return IResponse(code=400, message="data not found")
        ser = LiveNeedsInfoSer(live_needs, many=False)
        re_data = ser.data
        return IResponse(data=re_data)


class DBSelectionItemConfirmView(DBAPIView):
    """商品确认"""

    def patch(self, request: Request, plan_product_id: int):
        """状态修改，不需要开启修改状态即可"""
        return selection_plan_item_confirm_fc(request, plan_product_id)


class DBProductLinkDistributorView(DBAPIView):
    def post(self, request: Request, product_id: int):
        return post_product_link_distributor_fc(request, product_id)


class DBProductLinkDistributorDetailView(DBAPIView):
    """商品关联分销商货号修改"""

    def patch(self, request: Request, links_id: int):
        code = request.data.get("code")
        if not code:
            return IResponse(code=400, message="invalid params")
        try:
            links_record = ProductLinkDistributor.objects.get(pk=links_id, is_deleted=False)
        except ProductLinkDistributor.DoesNotExist:
            return IResponse(code=400, message="data not found")

        links_record.code = code
        links_record.update_user = request.user.user_id
        links_record.save()
        return IResponse()

    def delete(self, request: Request, links_id: int):
        try:
            links_record = ProductLinkDistributor.objects.get(pk=links_id, is_deleted=False)
        except ProductLinkDistributor.DoesNotExist:
            return IResponse(code=400, message="data not found")

        links_record.is_deleted = True
        links_record.update_user = request.user.user_id
        links_record.save()
        return IResponse()


# class DBSKUInventoryView(DBAPIView):
#     """库存修改"""
#
#     def patch(self, request: Request, sku_id: int):
#         return update_sku_inventory_fc(request, sku_id)


class DBSelectionPlanItemUpdateView(DBAPIView):
    """
    货盘item修改
    """

    resource_name = "货盘商品修改"
    fronted_page = "货盘计划"
    need_format_resource_name = False

    def put(self, request: Request, plan_product_id: int):
        """
        item更改
        1. 原有副本商品的修改商品
        2. 没有副本商品的加入我的商品后并修改
        :param request:
        :param plan_product_id:
        :return:
        """
        post_data = request.data

        try:
            item = ProductSelectionItem.objects.get(
                pk=plan_product_id,
                is_deleted=False,
                selection_plan__distributor=self.current_user.distributor,
            )
        except ProductSelectionItem.DoesNotExist:
            return IResponse(code=400, message="data not found")

        # 检查货盘情况
        plan_state_checker(item.selection_plan_id)
        # user_id = read_lock(item.selection_plan_id)
        # if self.current_user.user_id != user_id:
        #     raise APIViewException(code=403, err_message="Not granted editing permissions")

        # 有副本商品数据，直接更新副本商品数据
        if item.sub_product:
            old_log_sub_product = copy.deepcopy(item.sub_product)

            update_ser = MyProductCodeUpdateSer(instance=item.sub_product, data=post_data, context={"request": request})
            if not update_ser.is_valid():
                raise FieldsError(update_ser.errors)
            update_instance = update_ser.save()

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=str(plan_product_id),
                    model=ProductSelectionItem,
                    describe=f'货盘计划:{item.selection_plan.name}, 修改商品副本"{item.sub_product}"数据.',
                    raw_object=old_log_sub_product,
                    new_object=update_instance,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse()

        # 没有副本商品数据
        sub_product = SubProduct.objects.filter(parent_product=item.product, is_deleted=False, owner=self.current_user.distributor).first()
        if sub_product:
            post_data["update_user"] = self.current_user.user_id
            update_ser = MyProductCodeUpdateSer(instance=sub_product, data=post_data, context={"request": request})
            if not update_ser.is_valid():
                raise FieldsError(update_ser.errors)
            with transaction.atomic():
                update_ser.save()

                # 加入item
                item.sub_product = sub_product
                item.update_user = self.current_user.user_id
                item.save()

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=str(plan_product_id),
                    model=ProductSelectionItem,
                    describe=f'货盘计划:{item.selection_plan.name}, 加入商品副本"{item.sub_product}"数据.',
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse()

        # 组织商品参数
        post_data["parent_product"] = item.product.id
        post_data["owner"] = self.current_user.distributor.distributor_id
        post_data["create_user"] = self.current_user.user_id
        create_ser = MyProductCreateSer(data=post_data)
        if not create_ser.is_valid():
            raise FieldsError(create_ser.errors)
        with transaction.atomic():
            created_sub_product = create_ser.save()
            # 更新item信息
            item.sub_product = created_sub_product
            item.update_user = self.current_user.user_id
            item.save()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(plan_product_id),
                model=ProductSelectionItem,
                describe=f'货盘计划:{item.selection_plan.name}, 修改并生成商品副本"{item.sub_product}"数据.',
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class DBSelectionPoolView(DBAPIView):
    def get(self, request: Request):
        sku_prefetch = Prefetch("parent_product__stockkeepingunit_set", StockKeepingUnit.objects.filter(become_history=False))
        stall_prefetch = Prefetch("parent_product__company__stall_set", Stall.objects.filter(is_deleted=False).only("code", "address", "id", "company_id"))

        sub_products = SubProduct.get_distributor_sub_products(
            request,
            prefetch_fields=[
                "parent_product",
                "parent_product__company",
                "parent_product__address",
                "parent_product__handcard",
                sku_prefetch,
                stall_prefetch,
            ],
            is_distributor_mode=self.is_distributor_mode,
        )

        re_data, page_sub_products, qs = custom_django_filter(
            request,
            sub_products,
            DBSelectionPoolFilterSet,
            None,
            need_serialize=False,
        )

        ser_data = DBSelectionPoolSer(
            instance=page_sub_products,
            many=True,
            context={
                "request": request,
                "is_distributor_mode": self.is_distributor_mode,
            },
        ).data
        for data in ser_data:
            data.update(data.pop("product_info", {}))

        re_data["data"] = ser_data
        return IResponse(data=re_data)


class DBMyProductInfoSync(DBAPIView):
    resource_name = "商品详情同步"
    fronted_page = "我的商品"
    need_format_resource_name = False

    def post(self, request: Request, product_id: int):
        """
        我的商品 - 信息同步
        从主商品同步到我的商品
        名称、主图、详情、备注、尺寸
        :param request:
        :param product_id: 我的商品 sub_product product_id
        :return:
        """
        try:
            sub_product = SubProduct.objects.get(
                product_id=product_id,
                is_deleted=False,
                parent_product__is_deleted=False,
                owner_id=self.current_user.distributor.distributor_id,
            )
        except SubProduct.DoesNotExist:
            return IResponse(code=400, message="date not found")

        raw_sub_product = copy.deepcopy(sub_product)
        raw_images = sub_product.main_images

        post_data = sub_product.parent_product.__dict__
        post_data["update_user"] = self.current_user.user_id

        update_ser = MyProductNoCodeUpdateSer(instance=sub_product, data=post_data)
        if not update_ser.is_valid():
            raise FieldsError(update_ser.errors)
        instance = update_ser.save()

        new_images = instance.main_images
        images = list(set(new_images) - set(raw_images))
        # 上传图片到以图搜图数据库
        if images:
            add_images_to_qdrant.delay(sub_product.parent_product_id, list(set(images)))
        # 删除图片
        del_imgs = [i for i in raw_images if i not in new_images]
        for d_img in del_imgs:
            delete_images_from_qdrant.delay(sub_product.parent_product_id, raw_data=d_img)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(product_id),
                model=SubProduct,
                describe=f"同步商品数据到副本: 商品ID:{sub_product.parent_product.product_id}",
                raw_object=raw_sub_product,
                new_object=instance,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class MyProductSearchByPicture(DBAPIView):
    """
    我的商品以图搜图
    """

    resource_name = "我的商品列表-以图搜图"
    fronted_page = "我的商品列表-以图搜图"
    need_format_resource_name = False

    def get(self, request: Request):
        try:
            re_data = {}
            if request.auth.get("user_type") not in ["DB"]:
                return IResponse(code=403, message="no permission to operate")
            raw_params = request.query_params.copy()
            parent_raw_ids = get_raw_product_id_by_picture(raw_params)
            if not parent_raw_ids:
                re_data["count"] = 0
                re_data["total_pages"] = 1
                re_data["current_page"] = 1
                re_data["data"] = []
                return re_data

            products = SubProduct.objects.select_related(
                "parent_product",
            ).filter(
                owner_id=self.current_user.distributor.distributor_id,
                parent_product__pk__in=list(parent_raw_ids),
                parent_product__is_deleted=False,
                is_deleted=False,
            )

            re_data, page_sub_products, qs = custom_django_filter(
                request,
                products,
                MyProductFilterSet,
                None,
                need_serialize=False,
            )
            product_id_list = [item.parent_product.product_id for item in page_sub_products.object_list]
            labels_map = bulk_query_labels(product_id_list)
            re_data["data"] = MyProductProductListSerializer(instance=page_sub_products, many=True, context={"request": request, "labels_map": labels_map}).data
            return IResponse(data=re_data)

        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class DBHandCardView(DBAPIView):
    need_format_resource_name = True
    fronted_page = "分销商商品列表-手卡"
    resource_name = "手卡"

    permission_classes = [IsAuthenticated]

    def get(self, request, product_id):
        """
        查看手卡预览
        """
        show_subcode = request.query_params.get("show_subcode")
        if show_subcode:
            assert show_subcode in ["True", "true", "1", 1, "False", "false", "0", 0], "show_subcode should be bool type"
            show_subcode = True if show_subcode in ["True", "true", "1", 1] else False
        if show_subcode:
            sub_product_qs = SubProduct.objects.filter(
                owner_id=self.current_user.distributor.distributor_id,
                parent_product__is_deleted=False,
                is_deleted=False,
            )
            handcard = (
                HandCard.objects.filter(product_id=product_id, product__is_deleted=False)
                .select_related("product")
                .prefetch_related(Prefetch("product__subproduct_set", queryset=sub_product_qs))
            )
        else:
            handcard = HandCard.objects.filter(product_id=product_id, product__is_deleted=False).select_related("product")
        if not handcard:
            return IResponse(code=400, message="handcard not found")
        handcard_data = DBHandCardDetailSerializer(handcard.first(), context={"show_subcode": show_subcode, "current_user": self.current_user}).data
        # 获取用户偏好语言

        language = request.headers.get("Language", "zh")
        if language == "en":
            key_name = f"{product_id}_handcard"
            redis_handcard_data = redis_conn.get(key_name)
            if redis_handcard_data:
                redis_handcard_data_json = json.loads(redis_handcard_data)
                return IResponse(data=redis_handcard_data_json)

            # def custom_serializer(obj):
            #     if isinstance(obj, Decimal):
            #         return float(obj)  # 或者 str(obj) 转为字符串
            #     raise TypeError(f"Type {type(obj)} not serializable")
            # 转换为 JSON
            # handcard_json = json.dumps(handcard_data, ensure_ascii=False, indent=4, default=custom_serializer)
            # print(f"handcard_json:{handcard_json}")
            # v_client = VectorveinClient()
            # content_translation = v_client.json_content_translation(handcard_json)
            # todo 应急在业务过程中保存在redis中，之后优化中间件
            # redis_conn.set(key_name, json.dumps(content_translation, ensure_ascii=False))
            # return IResponse(data=handcard_data)
        return IResponse(data=handcard_data)


class DBDownloadHandCardView(DBAPIView):
    need_format_resource_name = True
    fronted_page = "商品列表-手卡-下载"
    resource_name = "手卡-下载"
    permission_classes = [IsAuthenticated]

    def get(self, request, product_id):
        try:
            show_subcode = request.query_params.get("show_subcode")
            if show_subcode:
                assert show_subcode in ["True", "true", "1", 1, "False", "false", "0", 0], "show_subcode should be bool type"
                show_subcode = True if show_subcode in ["True", "true", "1", 1] else False
            if show_subcode:
                sub_product_qs = SubProduct.objects.filter(
                    owner_id=self.current_user.distributor.distributor_id,
                    parent_product__is_deleted=False,
                    is_deleted=False,
                )
                handcard = (
                    HandCard.objects.filter(product_id=product_id, product__is_deleted=False)
                    .select_related("product")
                    .prefetch_related(Prefetch("product__subproduct_set", queryset=sub_product_qs))
                )
            else:
                handcard = HandCard.objects.filter(product_id=product_id, product__is_deleted=False).select_related("product")
            if not handcard:
                return IResponse(code=400, message="handcard not found")

            handcard_data = DBHandCardDetailSerializer(handcard.first(), context={"show_subcode": show_subcode, "current_user": self.current_user}).data

            language = request.headers.get("Language", "zh")
            if language == "en":
                convert_data = convert_ser_data(handcard_data)

                translated_content = json_content_translation_replacement(convert_data)

                ppt_file = generate_en_pptx([translated_content])
            else:
                ppt_file = generate_pptx([handcard_data])

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=product_id,
                    model=HandCard,
                    describe=f"下载了手卡:{product_id}.",
                    raw_object=handcard,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")

            return FileResponse(ppt_file, filename=f"手卡_{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.pptx", as_attachment=True)
        except Exception as e:
            print(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


@operate_log_decorator("批量下载手卡", "批量下载手卡")
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def db_bulk_download_handcard_view(request):
    """
    批量下载手卡
    """
    product_ids = request.data.get("product_ids")
    show_subcode = request.data.get("show_subcode")
    assert product_ids and len(product_ids) <= 500, "product_ids required and len < 500"
    try:
        if show_subcode:
            sub_product_qs = SubProduct.objects.filter(
                owner_id=request.user.distributor.distributor_id,
                parent_product__is_deleted=False,
                is_deleted=False,
            )
            handcard = (
                HandCard.objects.filter(product_id__in=product_ids, product__is_deleted=False)
                .select_related("product")
                .prefetch_related(Prefetch("product__subproduct_set", queryset=sub_product_qs))
            )
        else:
            handcard = HandCard.objects.filter(product_id__in=product_ids, product__is_deleted=False).select_related("product")
        if not handcard:
            return IResponse(code=400, message="handcard not found")

        handcard_data = DBHandCardDetailSerializer(handcard, many=True, context={"show_subcode": show_subcode, "current_user": request.user}).data

        language = request.headers.get("Language", "zh")
        if language == "en":
            task = generate_en_pptx_async.delay(handcard_data)
            re_data = {"task_id": task.id}
        else:
            task = generate_pptx_async.delay(handcard_data)
            re_data = {"task_id": task.id}

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=None,
                model=HandCard,
                describe=f"批量下载了手卡:{product_ids}.",
                raw_object=None,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")

        return IResponse(data=re_data)
    except Exception as e:
        print(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=500, message=str(e))


class DBCombineProductView(DBAPIView):
    # 组合商品
    def post(self, request: Request):
        post_data = request.data
        product_instance = create_combine_product(post_data, self.current_user, self.current_user_type)
        return IResponse(data={"product_id": product_instance.product_id})


class DBCombineProductDetailView(DBAPIView):
    @staticmethod
    def get_object(product_id, request) -> Product:
        try:
            product_obj = Product.get_distributor_products(request).get(product_id=product_id, is_deleted=False, is_combine=True)
        except Product.DoesNotExist:
            raise APIViewException(err_message="data not found")

        return product_obj

    def get(self, request: Request, product_id: int):
        product_obj = self.get_object(product_id, request)
        data = CombineProductDetailSer(instance=product_obj, many=False, context={"request": request}).data
        return IResponse(data=data)

    def put(self, request, product_id: int):
        post_data = request.data

        product_obj = self.get_object(product_id, request)

        product_instance = update_combine_product(post_data, product_obj, self.current_user)
        return IResponse(data={"product_id": product_instance.product_id})


class ProductVisitorModeQueryView(APIView):
    permission_classes = [AllowAny]

    def mall_visitor_products(self, request):
        """
        获取商品列表
        """
        try:
            raw_params = request.query_params.copy()

            and_Q = None
            specs_data = []

            # 处理前端传来的参数
            # todo: 分销商商品列表应该是只有自己创建的商品

            # 特殊处理code
            code = raw_params.get("code")
            if code:
                code = raw_params.pop("code")[0]
                if str(code).endswith("J"):
                    code = code[:-1]

                if not and_Q:
                    and_Q = Q()
                and_Q.add(Q(**{"code__icontains": code}), Q.OR)
                and_Q.add(Q(**{"subproduct__code__icontains": code}), Q.OR)

            product_list_params_handler(raw_params)
            if specs_data:
                and_Q = reduce(and_, specs_data)
            redis_conn = gen_redis_conn()
            visitor_physical_inventory = redis_conn.get("visitor_physical_inventory")
            if not visitor_physical_inventory:
                visitor_physical_inventory = 20
                redis_conn.set("visitor_physical_inventory", 20)
            else:
                visitor_physical_inventory = visitor_physical_inventory.decode()
            filters = dict(is_deleted=False, physical_inventory__lte=visitor_physical_inventory, physical_inventory__gt=0, company__is_self_support=1, is_combine=False)

            if raw_params.get("has_handcard"):
                if not and_Q:
                    and_Q = Q()
                val = raw_params.pop("has_handcard")[0]
                if val in ["False", "false", "0", 0]:
                    val = True
                elif val in ["True", "true", "1", 1]:
                    val = False
                else:
                    raise ValueError(f"has_handcard must be a bool value, but got {val}")
                and_Q.add(Q(handcard__isnull=val), Q.AND)

            page_products, re_data, obj_qs = custom_filter(
                raw_params,
                Product,
                array_fields=["category"],
                like_fields=["name", "product_id", "code", "remark", "productlinkdistributor__code"],
                hybrid_fields=["name", "product_id", "__code", "remark", "stockkeepingunit__link_code", "stockkeepingunit__spec_code", "productlinkdistributor__code"],
                query_Q=and_Q,
                **filters,
            )
            page_products = page_products.object_list.select_related("company", "owner", "address", "handcard")

            # 批量查询sku
            product_pks = [p.id for p in page_products]
            skus = StockKeepingUnit.objects.filter(become_history=False, product_id__in=product_pks)
            # 批量查询最新历史价
            skus_specs = bulk_query_skus_specs_detail(skus)
            # 最新历史价映射
            history_price_map = bulk_query_latest_history_price(skus)

            skus_map = {}
            for sku in skus:
                if sku.product_id not in skus_map:
                    skus_map[sku.product_id] = [sku]
                else:
                    skus_map[sku.product_id].append(sku)

            # 查询sub_product_code
            db_obj = Distributor.objects.filter(is_self_support=True).first()
            sub_products = SubProduct.objects.filter(parent_product_id__in=product_pks, is_deleted=False, owner=db_obj).only("parent_product_id", "code")
            sub_products_code_map = {sub_product.parent_product_id: sub_product.code for sub_product in sub_products}

            # 查询状态
            # bulk_fetch_product_confirm_state([p.product_id for p in page_products])

            # 查询托管状态
            product_ids = [product_obj.product_id for product_obj in page_products]
            hosting_product_map = bulk_query_product_hosting_state(product_ids)

            labels_map = bulk_query_labels(product_ids)
            self.request.auth = {}
            for product_obj in page_products:
                product_ser = VisitorModeProductListSerializer(instance=product_obj, context={"request": self.request, "labels_map": labels_map})
                data = product_ser.data
                category = data.get("category")
                category_list = get_category_name_by_id(category)
                data["category"] = category_list
                # 添加商品确认，确认状态为1时不允许修改库存数据
                data["product_confirm_state"] = product_obj.product_confirm_state
                # 添加商品关联的货号信息
                data["origin_product_id"] = product_obj.origin_product_id
                data["sub_code"] = sub_products_code_map.get(product_obj.id) or ""

                # sku_qs = product_obj.stockkeepingunit_set.filter(become_history=False)
                sku_qs = skus_map.get(product_obj.id) or []
                skus = VisitorModeDistributorStockKeepingUnitProductListSerializer(
                    instance=sku_qs,
                    many=True,
                    context={
                        "request": self.request,
                        "skus_specs": skus_specs,
                        "history_price_map": history_price_map,
                    },
                )
                data["skus"] = skus.data

                # 添加状态
                data["is_new"] = product_obj.is_new
                data["hosting_state"] = hosting_product_map.get(product_obj.product_id)

                re_data["data"].append(data)
            return IResponse(data=re_data)
        except FieldError as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=400)
        except Exception as e:
            logger.error(f"{e}, {traceback.format_exc()}")
            return IResponse(message=str(e), code=500)

    def distribute_visitor_products(self, request):
        """
        分销模式，把价格低的20个商品排前面
        :param request:
        :return:
        """
        sub_products = SubProduct.objects.filter(
            is_deleted=False,
            parent_product__state=1,
            parent_product__is_in_distributor_market=True,
            parent_product__company__is_self_support=False,
            parent_product__is_combine=False,
            owner__letters="FX",
        ).order_by("parent_product__min_distributor_market_price")

        # 兼容前端传来的update_date和create_update, 按照副本商品的日期排序
        order_fields_str = request.query_params.get("orders")
        if order_fields_str and order_fields_str != "[]":
            order_fields_list = json.loads(order_fields_str)
            _fields = ["update_date", "-update_date", "create_date", "-create_date"]
            for _field in _fields:
                if _field in order_fields_list:
                    order_fields_list.remove(_field)
                    # sub_products = sub_products.order_by(_field)
            request.query_params["orders"] = json.dumps(order_fields_list)
        sub_products = sub_products[:20]
        _, page_sub_products = custom_paginator(sub_products, request)
        sub_products_map = {sub_product.parent_product_id: sub_product for sub_product in page_sub_products}
        # 指定id排序
        product_pk_list = list(sub_products_map.keys())
        preserved_order = Case(*[When(id=p_pk, then=pos) for pos, p_pk in enumerate(product_pk_list)])
        target = (
            Product.objects.select_related("company", "address")
            .prefetch_related("handcard", "company__stall_set")
            .filter(pk__in=product_pk_list, is_deleted=False, state=1, is_in_distributor_market=True)
            .order_by(preserved_order)
        )

        re_data, page_products, _ = custom_django_filter(
            request,
            target,
            DBProductListFilterSet,
            need_serialize=False,
            force_order=False,
        )
        # 批量查询sku
        product_pks = [p.id for p in page_products]
        skus = StockKeepingUnit.objects.filter(become_history=False, product_id__in=product_pks)
        # 批量查询最新历史价
        skus_specs = bulk_query_skus_specs_detail(skus)
        # 最新历史价映射
        # history_price_map = bulk_query_latest_history_price(skus)
        history_price_map = {}
        # 查看排行榜信息
        ranks_map = bulk_query_product_category_ranks(product_pks)

        skus_map = {}
        for sku in skus:
            if sku.product_id not in skus_map:
                skus_map[sku.product_id] = [sku]
            else:
                skus_map[sku.product_id].append(sku)

        # 查询sub_product_code
        sub_products_code_map = {sub_product.parent_product_id: sub_product.code for sub_product in sub_products}

        # 查询托管状态
        product_ids = [product_obj.product_id for product_obj in page_products]
        # hosting_product_map = bulk_query_product_hosting_state(product_ids)
        hosting_product_map = {}
        # 锁库存状态
        # confirm_state_map = bulk_query_confirm_state(product_ids)
        confirm_state_map = {}
        # 标签
        labels_map = bulk_query_labels(product_ids)
        # 批量查询趋势
        trend_map = batch_calc_prod_trend(page_products)
        # 批量查询质检信息
        extra_tags_map = ProductBulkExtraTags(page_products)
        # 批量查询退费率
        has_permission, refund_rates_map = False, {}
        # 查询相似品
        # similar_products = bulk_query_similar_product_result(product_ids)
        similar_products = {}
        # 阶梯价格
        ladder_price_settings = bulk_query_ladder_price_settings(product_ids)

        for product_obj in page_products:
            product_ser = DistributorMarketProductListSerializer(
                instance=product_obj,
                context={
                    "labels_map": labels_map,
                    "trend_map": trend_map,
                    "extra_tags_map": extra_tags_map,
                    "ranks_map": ranks_map,
                    "sub_products_map": sub_products_map,
                    "ladder_price_settings": ladder_price_settings,
                },
            )

            data = product_ser.data
            category = data.get("category")
            category_list = get_category_name_by_id(category)
            data["category"] = category_list
            # 添加商品确认，确认状态为1时不允许修改库存数据
            data["product_confirm_state"] = confirm_state_map.get(product_obj.product_id)
            # 添加商品关联的货号信息
            data["origin_product_id"] = product_obj.origin_product_id
            data["sub_code"] = sub_products_code_map.get(product_obj.id) or ""
            sku_qs = skus_map.get(product_obj.id) or []

            skus = DistributorMarketSKUListSerializer(
                instance=sku_qs,
                many=True,
                context={
                    "skus_specs": skus_specs,
                    "history_price_map": history_price_map,
                },
            )

            data["skus"] = skus.data

            # 添加状态
            data["is_new"] = product_obj.is_new
            data["hosting_state"] = hosting_product_map.get(product_obj.product_id)
            data["refund_rate"] = "***" if not has_permission else (refund_rates_map.get(product_obj.product_id, None) or None)

            # 相似品
            data["similar_products"] = similar_products.get(product_obj.product_id)

            re_data["data"].append(data)

        return IResponse(data=re_data)

    def get(self, request: Request):
        platform = "1"
        if "platform" in request.query_params:
            platform = request.query_params.pop("platform")[0]
        if platform == "1":
            return self.mall_visitor_products(request)
        elif platform == "2":
            return self.distribute_visitor_products(request)

        return IResponse()


class DBProductReviewView(DBAPIView):
    def get(self, request, product_id):
        """
        获取审核信息
        """
        re_data = get_product_review_info(request, product_id, self.current_user_type)
        return IResponse(data=re_data)


class DBGoodProductNoteView(DBAPIView):
    def post(self, request, product_id):
        """
        发布好物种草笔记
        """
        data = request.data
        if not data.get("content"):
            raise APIViewException(err_message="请填写笔记内容")

        try:
            product = Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="商品已删除")

        instance = GoodProductNote.objects.create(
            images=data.get("images", []),
            video=data.get("video", []),
            content=data["content"],
            product=product,
            create_user=request.user.user_id,
        )
        instance.save()
        return IResponse(data=[])

    def get(self, request, product_id):
        # 获取商品关联的好物笔记
        try:
            Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            raise APIViewException(err_message="商品已删除")

        note_query = GoodProductNote.objects.filter(product_id=product_id, is_deleted=False)

        re_data, page_objs, _ = custom_django_filter(request, note_query, GoodProductNoteFilterSet, need_serialize=False, order_fields=("-create_date",))
        re_data["data"] = GoodProductNoteList(instance=page_objs, many=True, context={"request": request}).data

        return IResponse(data=re_data)

    def put(self, request, related_id):
        # 商品点赞
        data = request.data
        give_like_type = data.get("give_like_type")
        if give_like_type == "PT":
            try:
                Product.objects.get(product_id=related_id, is_deleted=False)
            except Product.DoesNotExist:
                raise APIViewException(err_message="商品已删除")
        elif give_like_type == "NT":
            try:
                GoodProductNote.objects.get(note_id=related_id, is_deleted=False)
            except GoodProductNote.DoesNotExist:
                raise APIViewException(err_message="笔记已删除")
        elif give_like_type == "CT":
            try:
                GoodProductNoteComment.objects.get(comment_id=related_id, is_deleted=False)
            except GoodProductNoteComment.DoesNotExist:
                raise APIViewException(err_message="评论已删除")
        else:
            raise APIViewException(err_message="暂不支持改点赞")
        exist = GoodProductNoteGiveLike.objects.filter(related_id=related_id, give_like_type=give_like_type, create_user=request.user.user_id)
        if exist.exists():
            exist.delete()
        else:
            GoodProductNoteGiveLike.objects.create(related_id=related_id, give_like_type=give_like_type, create_user=request.user.user_id)

        return IResponse()

    def delete(self, request, note_id):
        # 删除笔记
        try:
            note_obj = GoodProductNote.objects.get(note_id=note_id, is_deleted=False)
        except GoodProductNote.DoesNotExist:
            raise APIViewException(err_message="笔记已删除")

        note_obj.is_deleted = True
        note_obj.save()
        return IResponse()


class DBGoodNoteView(DBAPIView):
    def get(self, request, note_id):
        # 获取商品关联的好物笔记
        try:
            note_obj = GoodProductNote.objects.get(note_id=note_id, is_deleted=False)
        except GoodProductNote.DoesNotExist:
            raise APIViewException(err_message="笔记已删除")
        data = GoodProductNoteList(instance=note_obj, context={"request": request}).data
        return IResponse(data=data)


class DBGoodProductNoteCommentView(DBAPIView):
    def post(self, request, note_id):
        # 发布评论
        data = request.data
        if not data.get("content"):
            raise APIViewException(err_message="请填写评论内容")

        try:
            note_obj = GoodProductNote.objects.get(note_id=note_id, is_deleted=False)
        except GoodProductNote.DoesNotExist:
            return IResponse(code=400, message="笔记已删除")

        instance = GoodProductNoteComment.objects.create(
            content=data["content"],
            note=note_obj,
            create_user=request.user.user_id,
        )
        instance.save()
        return IResponse(data=[])

    def get(self, request, note_id):
        # 获取评论列表
        try:
            GoodProductNote.objects.get(note_id=note_id, is_deleted=False)
        except GoodProductNote.DoesNotExist:
            raise APIViewException(err_message="笔记已删除")

        comment_query = GoodProductNoteComment.objects.filter(note_id=note_id, is_deleted=False)

        re_data, page_objs, _ = custom_django_filter(request, comment_query, GoodProductNoteCommentFilterSet, need_serialize=False, order_fields=("-create_date",))
        re_data["data"] = GoodProductNoteCommentList(instance=page_objs, many=True, context={"request": request}).data

        return IResponse(data=re_data)

    def delete(self, request, comment_id):
        # 删除评论
        try:
            comment_obj = GoodProductNoteComment.objects.get(comment_id=comment_id, is_deleted=False)
        except GoodProductNoteComment.DoesNotExist:
            raise APIViewException(err_message="评论已删除")

        comment_obj.is_deleted = True
        comment_obj.save()
        return IResponse()


class DBQAQuestionEnumView(DBAPIView):
    def get(self, request: Request):
        type_name = request.query_params.get("type_name")
        if not type_name:
            return IResponse(code=400, message="invalid params")

        q_type = QAReviewQuestionType.objects.filter(name=type_name, enable=True).last()
        if not q_type:
            return IResponse()
        questions = q_type.get_questions_list_with_cache()
        re_data = {
            "type_id": q_type.pk,
            "type_name": q_type.name,
            "type_short_desc": q_type.short_desc,
            "questions": questions,
        }
        return IResponse(data=re_data)


class DBUserWishView(DBAPIView):
    @staticmethod
    def get_obj(product_id) -> Product:
        try:
            product = Product.objects.get(product_id=product_id)
        except Product.DoesNotExist:
            raise APIViewException(err_message="商品不存在")
        return product

    @property
    def wish_type(self):
        # 1 臻品想要  2甄选预购
        _wish_type = 1 if self.is_self_distributor else 2
        return _wish_type

    def post(self, request: Request, product_id: int):
        post_data = request.data
        product = self.get_obj(product_id)
        number = post_data.get("number")
        if not number:
            number = 1
        elif not isinstance(number, int):
            raise APIViewException(err_message="预购数量类型错误")

        filter_conditions = dict(
            product=product,
            user=self.current_user,
            wish_type=self.wish_type,
            distributor=self.current_distributor,
        )
        defaults_data = {"number": number}
        user_wish, created = UserWishList.objects.update_or_create(
            **filter_conditions,
            defaults=defaults_data,
        )

        if created:
            # 发送通知
            send_user_wish_messages.delay(user_wish.pk)

        return IResponse()

    def delete(self, request: Request, product_id: int):
        product = self.get_obj(product_id)
        UserWishList.objects.filter(
            product=product,
            user=self.current_user,
            wish_type=self.wish_type,
            distributor=self.current_distributor,
        ).delete()
        return IResponse()


class DBSelectionPlanMarkView(DBAPIView):
    def put(self, request: Request):
        return add_selection_plan_item_mark(request, self.current_user, self.current_user_type)


class DBProductPriceCompareView(DBAPIView):
    def get(self, request: Request, product_id: int):
        """
        比价
        :param request:
        :param product_id:
        :return:
        """

        try:
            Product.objects.get(product_id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            return IResponse(code=400, message="data not found")
        products = ZhuJiProduct.objects.all().order_by("-sales")
        re_data, _, _ = custom_django_filter(
            request,
            products,
            DBProductPriceCompareFilterSet,
            ZhuJiProductPriceCompareSer,
            force_order=False,
            need_serialize=True,
        )
        return IResponse(data=re_data)


class DBSimilarProducts(DBAPIView):
    def get(self, request: Request, product_id: int):
        try:
            product = Product.get_distributor_products(request).get(product_id=product_id)
        except Product.DoesNotExist:
            return IResponse(code=400, message="data not found")

        external_product_id_list = product.similarproductrelate_set.filter(is_deleted=False).order_by("id").values_list("external_product_id", flat=True)
        # 按照id列表指定的顺序
        custom_external_id_sort = Case(*[When(external_id=external_product_id, then=idx) for idx, external_product_id in enumerate(external_product_id_list)])
        target = ZhuJiProduct.objects.filter(external_id__in=external_product_id_list).order_by(custom_external_id_sort)

        re_data, _, _ = custom_django_filter(
            request,
            target,
            None,
            ZhuJiProductPriceCompareSer,
            force_order=False,
            need_serialize=True,
        )
        return IResponse(data=re_data)


class DBSelectionPlanReportView(DBAPIView):
    """
    货盘报表, 只查询结束的货盘
    """

    def get(self, request: Request):
        target = ProductSelectionPlan.objects.filter(distributor_id=self.current_distributor.distributor_id, state=0)

        re_data, page_plans, obj_qs = custom_django_filter(
            request,
            target,
            SelectionPlanFilterSet,
            DBProductSelectionPlanReportSer,
            force_order=False,
            order_map={
                "live_date": "live_date_start",
            },
        )
        return IResponse(data=re_data)


class DBSelectionPlanReportDetailView(DBAPIView):
    def get(self, request: Request, plan_id: int):
        try:
            plan = ProductSelectionPlan.objects.get(
                distributor_id=self.current_distributor.distributor_id,
                state=0,
                plan_id=plan_id,
            )
        except ProductSelectionPlan.DoesNotExist:
            raise DataNotFoundException()

        items = (
            plan.productselectionitem_set.filter(is_deleted=False)
            .prefetch_related("product", "product__company", "sub_product")
            .annotate(
                total_sales=Sum("productselectionitemsku__actual_sales"),
                total_sales_amount=Sum("productselectionitemsku__sales_amount"),
            )
            .order_by("-total_sales")
        )
        re_data, _, _ = custom_django_filter(
            request,
            items,
            PlanV2FilterSet,
            SelectionPlanItemDetailSer,
            need_serialize=True,
            force_order=False,
        )
        return IResponse(data=re_data)
