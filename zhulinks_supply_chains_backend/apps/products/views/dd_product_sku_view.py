from http.client import responses

from products.filters import DDProductSkuFilter
from products.models_v2 import DouDianSKU, DouDianProduct
from products.permissions import OPPermissions
from products.serializer import DDProductSkuSerializer
from utils.api_page_number_pagination import ApiPageNumberPagination
from utils.base_model_view_set import BaseMysqlModelExportViewSet


class DDProductSkuViews(BaseMysqlModelExportViewSet):
    queryset = DouDianSKU.objects.all().order_by("-create_time")
    pagination_class = ApiPageNumberPagination
    filterset_class = DDProductSkuFilter
    serializer_class = DDProductSkuSerializer
    permission_classes = [OPPermissions]

    def list(self, request, code=200, count=0, status=None, *args, **kwargs):
        product_id = request.query_params.get("product_id")
        response = super().list(request, code=code, count=count, status=status, *args, **kwargs)

        re_data = response.data.get("data", {}).get("data", {})
        associate_sku = {"associate_sku": re_data}

        if product_id:
            dd_product_data = DouDianProduct.objects.filter(product_id=product_id).values("name", "pic").first() or {}
            associate_sku.update(dd_product_data)
        else:
            associate_sku.update({"name": "", "pic": ""})

        response.data["data"]["data"] = associate_sku
        return response
