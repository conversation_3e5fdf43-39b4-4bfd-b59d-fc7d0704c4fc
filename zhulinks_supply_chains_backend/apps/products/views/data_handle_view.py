import traceback
from django.db.models import Q
from django.core.paginator import Paginator
from products.models import (
    Product,
    StockKeepingUnit,
    SubProduct,
    ProductRecord,
    SkuRecord,
    SubProductRecord,
    SubSkuRecord,
    Distributor,
    ProductSelectionItem,
    ProductLinkDistributor,
    HistoryPrice,
)
from utils.http_handle import IResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from django.db.utils import IntegrityError

from companies.models import Company
from django.core.paginator import Paginator
from utils.common import get_random_number_str

from utils.http_handle import IResponse
from products import logger


def get_all_distributor_map():
    _objects = Distributor.objects.all()
    return {dis.letters: dis.distributor_id for dis in _objects}


def generate_code():
    unique = False
    while not unique:
        code = get_random_number_str(6)
        unique = not Product.objects.filter(code=code).exists()
    return code


def generate_spec_code(company_code):
    unique = False
    while not unique:
        spec_code = f"J{company_code}" + get_random_number_str(6)
        unique = not StockKeepingUnit.objects.filter(spec_code=spec_code).exists()
    return spec_code


def get_all_company_map():
    _objects = Company.objects.all()
    return {com.id: com.code for com in _objects}


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def create_subproduct_by_code(request, product_id, code):
    """
    对于DB这种实际已经存在主商品的，实际不需要恢复
    只需在主商品下创建一个对于分销商货号副本即可
    """
    try:
        current_user = request.user
        if not current_user.is_superuser:
            return IResponse(code=400, message="no permission to operate")

        product = Product.objects.filter(product_id=product_id).first()
        if not product:
            return IResponse(code=400, message=f"product {product_id} not found")
        distributor_map = get_all_distributor_map()
        distributor_map = {k: v for k, v in sorted(distributor_map.items(), key=lambda item: len(item[0]), reverse=True)}

        # 获取货号对应的分销商
        if not Product.objects.filter(code=code).exists():
            return IResponse(code=400, message=f"raw product not found which code is {code}")
        distributor_id = None
        for k, v in distributor_map.items():
            if not k:
                return IResponse(code=400, message="some distributor not found letters")
            if code.upper().startswith(k):
                distributor_id = v
                break
        if not distributor_id:
            return IResponse(code=400, message="not support opertate for this code")

        # 为主商品创建一条指定货号新副本
        with transaction.atomic():
            add_to_subproduct(
                product.product_id,
                distributor_id,
                main_product_id=product.id,
                main_product_product_id=product.product_id,
                is_main=True,
                code=code,
            )
    except ValueError as e:
        logger.error(f"ValueError: {e}")
        return IResponse(code=400, message=str(e))
    except IntegrityError as e:
        logger.error(f"IntegrityError: {e}")
        return IResponse(code=500, message=str(e))
    logger.info(f"{current_user.username} create subproduct for {product_id} with code {code}")
    return IResponse(code=200, data="created")


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def restore_main_product_view(request, product_id):
    """
    处理恢复数据：
    场景：
    按原来策略，更新日期在2023年12月31日之前的数据在数据清洗前已经被删除
    但是，实际场景业务人又需要恢复，故需要进行如下处理：
    （1）主商品数据需要创建副本
    （2）！关联数据不需要副本
    （3）修改主数据编码为新式编码
    （4）主数据副本需要创建关联货号
    """
    current_user = request.user
    if not current_user.is_superuser:
        return IResponse(code=400, message="no permission to operate")

    product = Product.objects.filter(product_id=product_id).first()
    if not product:
        return IResponse(code=400, message=f"product {product_id} not found")
    distributor_map = get_all_distributor_map()
    distributor_map = {k: v for k, v in sorted(distributor_map.items(), key=lambda item: len(item[0]), reverse=True)}

    company_map = get_all_company_map()

    try:
        with transaction.atomic():
            # ==== 创建主商品分销商副本及副本关联货号 ====
            code = product.code
            distributor_id = None
            for k, v in distributor_map.items():
                if not k:
                    logger.error(f"some distributor not found letters:{distributor_map}")
                    return IResponse(code=400, message="some distributor not found letters")
                if code.upper().startswith(k):
                    distributor_id = v
                    break

            if not distributor_id:
                logger.error(f"The operation of this type of product code({code}) is not supported or the changed product has been processed correctly:{distributor_map}")
                return IResponse(code=400, message=f"The operation of this type of product code({code}) is not supported or the changed product has been processed correctly.")
            add_to_subproduct(
                product.product_id,
                distributor_id,
                main_product_id=product.id,
                main_product_product_id=product.product_id,
                is_main=True,
            )

            # ==== 修改主数据货号为新编码 ====
            old_code = product.code
            new_code = generate_code()
            product.code = new_code
            company_code = company_map.get(product.company_id)

            # 记录表
            product_record, _ = ProductRecord.objects.get_or_create(
                product_id=product.product_id,
                old_code=old_code,
                new_code=new_code,
            )
            skus = product.stockkeepingunit_set.filter(become_history=False)
            for sku in skus:
                old_spec_code = sku.spec_code
                new_spec_code = generate_spec_code(company_code)
                sku.spec_code = new_spec_code
                sku.save()
                # 记录表
                SkuRecord.objects.get_or_create(
                    product_record_id=product_record.product_id,
                    sku_id=sku.sku_id,
                    old_spec_code=old_spec_code,
                    new_spec_code=new_spec_code,
                )
            product.has_new_code = True
            product.handle_type = "M"
            product.save()
    except ValueError as e:
        logger.error(f"ValueError: {e}")
        return IResponse(code=400, message=str(e))
    except IntegrityError as e:
        logger.error(f"IntegrityError: {e}")
        return IResponse(code=500, message=str(e))
    logger.info(f"{current_user.username} restored product {product_id}")
    return IResponse(code=200, data="restore success")


def add_to_subproduct(product_id, distributor_id, main_product_id=None, main_product_product_id=None, is_main=False, code=None):
    """
    创建商品分销商副本
    Args:
        main_product_id (_type_): _description_
        product_id (_type_): _description_
        distributor_id (_type_): _description_
    """
    try:
        product = Product.objects.filter(product_id=product_id, is_deleted=False).first()
        if not product:
            raise ValueError(f"add_to_subproduct {product_id} not found")
        if product.code and product.code.startswith("V"):
            if not is_main:
                is_main = True
        if SubProduct.objects.filter(
            parent_product_id=main_product_id if main_product_id else product.id,
            owner_id=distributor_id,
        ).exists():
            # 该商品已加入副本, todo: 处理记录
            raise ValueError(f"product: {product_id} already existed in subproduct list for distributor: {distributor_id}")

        # todo: bug,如果两个原货号相同的恢复，就有两个相同货号的商品副本
        sub_product = SubProduct.objects.create(
            parent_product_id=main_product_id if main_product_id else product.id,
            code=code if code else product.code,
            name=product.name,
            main_images=product.main_images,
            detail_images=product.detail_images,
            remark=product.remark,
            owner_id=distributor_id,
        )

        # 判断是否有货盘
        ProductSelectionItem.objects.filter(
            product_id=product.product_id,
            selection_plan__distributor=distributor_id,
            selection_plan__state__in=[1, 2],
        ).update(
            sub_product=sub_product,
        )

        # 创建商品副本记录
        sub_product_record, _ = SubProductRecord.objects.get_or_create(
            old_parent_product_id=product.product_id,
            new_parent_product_id=main_product_product_id if main_product_product_id else None,
            product_id=sub_product.product_id,
            old_code=sub_product.code,
        )

        # 创建关联货号记录
        if not ProductLinkDistributor.objects.filter(
            product=sub_product.parent_product,
            distributor=sub_product.owner,
            code=sub_product.code,
            is_deleted=False,
        ).exists():
            ProductLinkDistributor.objects.create(
                product=sub_product.parent_product,
                distributor=sub_product.owner,
                linked_product_id=sub_product.product_id,
                code=sub_product.code,
                is_sub_linked=True,
            )

        # 创建sku副本记录并修改历史价绑定关系
        if not is_main:
            skus = product.stockkeepingunit_set.filter(become_history=False)
            for sku in skus:
                # 修改sku绑定,绑到主商品
                sku.product_id = main_product_id
                sku.become_history = True
                sku.save()

                # 修改历史价绑定
                his_qs = HistoryPrice.objects.filter(sku_id=sku.id)
                his_qs.update(product_id=main_product_id, sub_product_id=product.product_id, raw_product_id=product.product_id)

                SubSkuRecord.objects.get_or_create(
                    product_record_id=sub_product_record.product_id,
                    sku_id=sku.sku_id,
                    old_spec_code=sku.spec_code,
                )
    except Exception as e:
        print(f"e: {e}--{traceback.format_exc()}")
        raise e
