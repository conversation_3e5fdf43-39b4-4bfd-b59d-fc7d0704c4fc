# -*- coding: utf-8 -*-
# @Author: <PERSON>
# @Date:   2024-06-04 23:03:38
# @Last Modified by:   <PERSON>
# @Last Modified time: 2024-06-04 23:42:56
from django.db import connection
from django.db.models import Max
from django.utils import timezone
from django_cte import With
from rest_framework.request import Request

from common.basic import DBAPIView
from common.basics.views import set_log_params, OperateLogAPIViewMixin
from common.basics.exceptions import APIViewException
from common.utils import list_to_tree
from companies.models import DataApplicationShops, DataShop, dy_product_application, DataShopsDailyUseLog
from products import logger
from products.data_shops_logics.datashop_brands import init_brands
from products.data_shops_logics.datashop_category import bulk_insert_or_update_data_shop_category, fetch_categories, init_first_level_category
from products.data_shops_logics.datashop_product import add_product, edit_product
from products.data_shops_logics.datashop_property import init_properties
from products.data_shops_logics.datashop_rules import fetch_rules, fetch_rules_with_cache
from products.models import DataCategory, DataShopCategoryRelate, DataShopProductMap, DataShopUploadTasks, SubProduct
from products.serializers import DataCategorySer, DataCatePropertiesSer
from products.sql import FetchDyCateParentsSQL
from utils.http_handle import IResponse


def validata_data_shop(shop_id) -> DataShop:
    if not shop_id or not isinstance(shop_id, (str, int)):
        raise APIViewException(err_message="invalid params")

    try:
        data_shop = DataShop.objects.get(shop_id=shop_id)
    except DataShop.DoesNotExist:
        raise APIViewException(err_message="data not found")

    product_application = dy_product_application()
    relate = DataApplicationShops.objects.filter(application=product_application, data_shop=data_shop).first()
    if not relate:
        raise APIViewException(err_message="商品应用无关联店铺,请刷新店铺列表后重试")

    if not relate.enable:
        raise APIViewException(err_message="店铺尚未启用")

    if not relate.is_authorized:
        raise APIViewException(err_message="店铺尚未授权")

    return data_shop


class DataShopsView(DBAPIView):
    def get(self, request: Request):
        application = dy_product_application()

        shops_relates = application.dataapplicationshops_set.filter(enable=True, is_authorized=True).values(
            "data_shop__shop_id",
            "data_shop__name",
        )

        re_data = [
            {
                "shop_id": shop_relate["data_shop__shop_id"],
                "name": shop_relate["data_shop__name"],
            }
            for shop_relate in shops_relates
        ]
        return IResponse(data=re_data)


class DataShopCategoryView(DBAPIView):
    def get(self, request: Request):
        params = request.query_params.copy()
        shop_id = params.get("shop_id")
        data_shop = validata_data_shop(shop_id)

        category_id = params.get("category_id")
        # 是否同步最新数据
        is_sync = params.get("is_sync", "0")

        # 第一级别分类
        if not category_id:
            if is_sync == "1" or not data_shop.datacategory_set.filter(level=1).exists():
                init_first_level_category(data_shop.pk)

            categories = data_shop.datacategory_set.filter(level=1)
        else:
            try:
                data_category = data_shop.datacategory_set.get(category_id=category_id)
            except DataCategory.DoesNotExist:
                return IResponse(code=400, message="分类不存在")

            relate = DataShopCategoryRelate.objects.filter(datashop=data_shop, datacategory=data_category).last()

            if is_sync == "1" or (not relate.sub_inited and not data_category.is_leaf):
                # 查询抖店
                categories_response = fetch_categories(data_shop.pk, int(category_id))
                if categories_response:
                    bulk_insert_or_update_data_shop_category(data_shop.pk, categories_response)

                    # 已经初始化
                    relate.sub_inited = True
                    relate.save(update_fields=["sub_inited"])

            categories = data_category.datacategory_set.filter(
                parent=data_category,
                level=int(data_category.level) + 1,
                data_shop__shop_id=shop_id,
            )

        ser = DataCategorySer(instance=categories, many=True)
        return IResponse(data=ser.data)


class DataShopCateTreeView(DBAPIView):
    def get(self, request: Request):
        shop_id = request.query_params.get("shop_id")
        data_shop = validata_data_shop(shop_id)
        all_categories = data_shop.datacategory_set.all().values("category_id", "name", "level", "is_leaf", "auth_required", "shop_parent_id")
        re_data = list_to_tree(
            all_categories,
            root_val="0",
            id_key="category_id",
            parent_id_key="shop_parent_id",
        )
        return IResponse(data=re_data)


class DataShopCateBrandsView(DBAPIView):
    def get(self, request: Request):
        params = request.query_params.copy()
        shop_id = params.get("shop_id")
        category_id = params.get("category_id")
        data_shop = validata_data_shop(shop_id)

        if not category_id or not isinstance(category_id, (str, int)):
            return IResponse(code=400, message="分类ID错误")

        # 是否同步最新数据
        is_sync = params.get("is_sync", "0")

        try:
            data_category = data_shop.datacategory_set.get(category_id=category_id)
        except DataCategory.DoesNotExist:
            return IResponse(code=400, message="分类不存在")

        relate = DataShopCategoryRelate.objects.filter(datashop=data_shop, datacategory=data_category).last()
        if not relate:
            init_brands(data_shop.pk, category_id)
            relate.brand_inited = True
            relate.save(update_fields=["brand_inited"])
        elif is_sync == "1" or not relate.brand_inited:
            init_brands(data_shop.pk, category_id)

            relate.brand_inited = True
            relate.save(update_fields=["brand_inited"])

        relates = data_category.datacategorybrand_set.select_related("brand", "data_category")
        re_data = {
            "auth_brand_list": [],
            "brand_list": [],
        }

        for relate in relates:
            brand = relate.brand
            if relate.is_authed:
                re_data["auth_brand_list"].append(
                    {
                        "brand_id": brand.brand_id,
                        "name_cn": brand.name_cn,
                        "name_en": brand.name_en,
                    }
                )
            else:
                re_data["brand_list"].append(
                    {
                        "brand_id": brand.brand_id,
                        "name_cn": brand.name_cn,
                        "name_en": brand.name_en,
                    }
                )

        return IResponse(data=re_data)


class DataShopCatePropertyView(DBAPIView):
    def get(self, request: Request):
        params = request.query_params.copy()
        shop_id = params.get("shop_id")
        category_id = params.get("category_id")
        data_shop = validata_data_shop(shop_id)
        if not category_id or not isinstance(category_id, (str, int)):
            return IResponse(code=400, message="分类信息错误,请刷新页面后重试")
        # 是否同步最新数据
        is_sync = params.get("is_sync", "0")

        try:
            data_category = data_shop.datacategory_set.get(category_id=category_id)
        except DataCategory.DoesNotExist:
            return IResponse(code=400, message="分类不存在")

        relate = DataShopCategoryRelate.objects.filter(datashop=data_shop, datacategory=data_category).last()

        if not relate:
            init_properties(data_shop.pk, data_category.pk, category_id)
            relate.property_inited = True
            relate.save(update_fields=["property_inited"])
        elif is_sync == "1" or not relate.property_inited:
            init_properties(data_shop.pk, data_category.pk, category_id)
            relate.property_inited = True
            relate.save(update_fields=["property_inited"])

        properties = data_category.datacateproperties_set.all().order_by("id")
        re_data = DataCatePropertiesSer(instance=properties, many=True).data
        return IResponse(data=re_data)


def process_image_options(main_images, post_data, distributor):
    options_data_list = []
    image_options_field = ["rings_images_options", "after_sales_images_options"]
    for field in image_options_field:
        val = post_data.get(field)
        if not isinstance(val, dict):
            raise APIViewException(err_message="图片信息选项信息错误,请刷新后重试")

        order = val.get("order")

        if val.get("is_upload"):
            if not order:
                if field == "rings_images_options":
                    raise APIViewException(err_message="圈口图片上传必须填写顺序")
                else:
                    raise APIViewException(err_message="售后图片上传必须填写顺序")

            if not str(order).isdigit():
                raise APIViewException(err_message="排序信息必须为数字类型")

            if order < 1:
                raise APIViewException(err_message="图片排序不能小于1")
            if order > 5:
                raise APIViewException(err_message="图片排序不能大于5")
            options_data_list.append({"name": field, "details": {"is_upload": val.get("is_upload"), "order": int(order)}})

    # 排序
    options_data_list = sorted(options_data_list, key=lambda x: (x["details"]["order"], x["name"] != "rings_images_options"))

    # 调整相同的 order
    for i in range(1, len(options_data_list)):
        if options_data_list[i]["details"]["order"] != 5 and options_data_list[i]["details"]["order"] == options_data_list[i - 1]["details"]["order"]:
            options_data_list[i]["details"]["order"] += 1
        elif options_data_list[i]["details"]["order"] == options_data_list[i - 1]["details"]["order"]:
            options_data_list[i - 1]["details"]["order"] -= 1

    for _image_data in options_data_list:
        name = _image_data["name"]
        order = int(_image_data["details"]["order"])

        if name == "rings_images_options" and distributor.rings_image:
            ring_img = distributor.rings_image
            if not ring_img.startswith("http://") and not ring_img.startswith("https://"):
                raise APIViewException(err_message="圈口图片格式错误,请修改")
            image = ring_img
        elif name == "after_sales_images_options" and distributor.after_sales_image:
            after_sales_img = distributor.after_sales_image
            if not after_sales_img.startswith("http://") and not after_sales_img.startswith("https://"):
                raise APIViewException(err_message="圈口图片格式错误,请修改")
            image = after_sales_img
        else:
            continue

        act_order = max(0, order - 1)
        main_images.insert(act_order, image)

    return main_images


def calculate_string_width(s):
    width = 0
    for char in s:
        # 判断是否为汉字
        if "\u4e00" <= char <= "\u9fff":
            width += 2
        else:
            width += 1
    return width


class DataShopProductView(DBAPIView):
    resource_id_field = "product_id"
    resource_name = "同步商品到抖店"
    fronted_page = "我的商品"
    need_format_resource_name = False

    @staticmethod
    def _validate_value_type(vals):
        for val in vals:
            if not isinstance(val, (str, int)):
                raise APIViewException(err_message="invalid params")

    def post(self, request: Request):
        post_data = request.data
        shop_id = post_data.get("shop_id")
        category_leaf_id = post_data.get("category_id")
        sub_product_id = post_data.get("product_id")
        properties = post_data.get("properties")
        name = post_data.get("name")
        main_images = post_data.get("main_images")
        presell_type = post_data.get("presell_type", 0)

        if presell_type not in [0, 1]:
            raise APIViewException(err_message="发货模式错误")

        self._validate_value_type([shop_id, category_leaf_id, sub_product_id])
        if not name or calculate_string_width(name) < 8:
            raise APIViewException(err_message="商品标题最短不能低于8个汉字,请修改商品标题")
        elif calculate_string_width(name) > 60:
            raise APIViewException(err_message="商品标题最大不能超过60个字符,请修改商品标题")

        if not main_images:
            raise APIViewException(err_message="缺少主图信息, 请先更新商品的主图")

        if not isinstance(main_images, list):
            raise APIViewException(err_message="商品主图参数错误,请刷新后重试")

        main_images = process_image_options(main_images, post_data, self.current_user.distributor)
        if len(main_images) < 3:
            raise APIViewException(err_message="上传抖店最少需要上传3张图片")
        # 只提取5张图片
        main_images = main_images[:5]

        data_shop = validata_data_shop(shop_id)

        try:
            sub_product = SubProduct.objects.get(
                product_id=sub_product_id,
                is_deleted=False,
                parent_product__is_deleted=False,
            )
        except SubProduct.DoesNotExist:
            raise APIViewException(err_message="商品不存在或已删除,请刷新页面后重试")

        if sub_product.parent_product.is_combine:
            raise APIViewException(err_message="系统暂不支持组合商品上传抖店")

        category = data_shop.datacategory_set.filter(category_id=category_leaf_id).first()
        if not category:
            raise APIViewException(err_message="店铺分类不存在,请刷新页面后重试")
        if not category.is_leaf:
            raise APIViewException(err_message="分类必须为最后一级分类")

        newest_task = sub_product.datashopuploadtasks_set.filter(data_shop_id=data_shop.pk).last()
        if newest_task:
            if newest_task.state == 4:
                raise APIViewException(err_message="商品已存在,无需重新上传")
            elif newest_task.state in [0, 2]:
                raise APIViewException(err_message="商品正在上传,请稍后刷新查看状态")

        state, msg = add_product(
            request,
            data_shop.pk,
            sub_product,
            category_leaf_id,
            properties,
            name,
            main_images,
            presell_type=presell_type,
        )
        if state == 0:
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=sub_product.product_id,
                    model=SubProduct,
                    describe=f"同步商品`{sub_product}`到抖店【待审核】",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(code=202)
        if state == 1:
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=sub_product.product_id,
                    model=SubProduct,
                    describe=f"同步商品`{sub_product}`到抖店【成功】",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(code=200)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=sub_product.product_id,
                model=SubProduct,
                describe=f"同步商品`{sub_product}`到抖店【失败】,{msg}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        # 记录常用记录
        DataShopsDailyUseLog.objects.update_or_create(
            user=self.current_user,
            distributor=self.current_distributor,
            data_shop=data_shop,
            defaults={
                "operate_time": timezone.now(),
            },
        )
        return IResponse(code=400, message=msg)


class DataShopProductDetailView(DBAPIView):
    resource_id_field = "product_id"
    resource_name = "同步商品到抖店"
    fronted_page = "我的商品"
    need_format_resource_name = False

    @staticmethod
    def _validate_value_type(vals):
        for val in vals:
            if not isinstance(val, (str, int)):
                raise APIViewException(err_message="invalid params")

    def get(self, request: Request):
        params = request.query_params.copy()
        shop_id = params.get("shop_id")
        sub_product_id = params.get("product_id")
        if not all([shop_id, sub_product_id]):
            raise APIViewException(err_message="店铺或商品参数缺失")

        self._validate_value_type([shop_id, sub_product_id])
        data_shop = validata_data_shop(shop_id)

        try:
            sub_product = SubProduct.objects.get(
                product_id=sub_product_id,
                is_deleted=False,
                parent_product__is_deleted=False,
            )
        except SubProduct.DoesNotExist:
            raise APIViewException(err_message="商品不存在或已删除,请刷新页面后重试")

        product_map = DataShopProductMap.objects.filter(data_shop=data_shop, sub_product=sub_product).select_related("task").last()
        if not product_map:
            return IResponse()

        task = product_map.task

        if not task.category_id_list:
            category_id_list = []

            with connection.cursor() as cursor:
                objs = cursor.execute(FetchDyCateParentsSQL, params=[task.cate_leaf_id])
                for obj in objs:
                    category_id_list.insert(0, obj[1])

            task.category_id_list = category_id_list
            task.save(update_fields=["category_id_list"])
        task_properties = task.properties
        # 获取属性
        data_category = data_shop.datacategory_set.get(category_id=task.cate_leaf_id)
        properties = data_category.datacateproperties_set.all().order_by("id")

        re_task_properties = {}
        for _property in properties:
            key_id = _property.property_id
            if key_id not in task_properties:
                if _property.value_type == "measure":
                    unit_name = _property.measure_templates[0]["value_modules"][0]["units"][0]["unit_name"]
                    re_task_properties[key_id] = {"key": key_id, "name": "", "value": 0, "diy_type": 0, "unit": unit_name}
                elif _property.value_type == "multi_select":
                    re_task_properties[key_id] = {
                        "key": key_id,
                        "name": [],
                        "value": [],
                        "diy_type": 0,
                    }
                else:
                    re_task_properties[key_id] = {
                        "key": key_id,
                        "name": "",
                        "value": 0,
                        "diy_type": 0,
                    }
            else:
                old_properties = task_properties[key_id]
                if _property.value_type == "measure":
                    unit_name = _property.measure_templates[0]["value_modules"][0]["units"][0]["unit_name"]
                    old_property = old_properties[0]
                    re_task_properties[key_id] = {
                        "key": key_id,
                        "name": old_property.get("name").replace(unit_name, ""),
                        "value": old_property.get("value"),
                        "diy_type": old_property.get("diy_type"),
                        "unit": unit_name,
                    }
                elif _property.value_type == "multi_select":
                    # 多选处理
                    re_task_properties[key_id] = {
                        "diy_type": 0,
                        "key": key_id,
                        "name": [_tmp.get("name") for _tmp in old_properties if _tmp.get("name")],
                        "value": [_tmp.get("value") for _tmp in old_properties if _tmp.get("value")],
                    }
                else:
                    old_property = old_properties[0]
                    re_task_properties[key_id] = {
                        "key": key_id,
                        "name": old_property.get("name"),
                        "value": old_property.get("value"),
                        "diy_type": old_property.get("diy_type"),
                    }

        re_data = {
            "platform_product_id": product_map.platform_product_id,
            "shop_id": data_shop.shop_id,
            "name": task.product_name,
            "category_id": task.cate_leaf_id,
            "category_id_list": task.category_id_list,
            "properties": task_properties,
            "new_properties": re_task_properties.values(),
            "presell_type": task.presell_type,
        }

        return IResponse(data=re_data)

    def put(self, request: Request):
        post_data = request.data
        shop_id = post_data.get("shop_id")
        category_leaf_id = post_data.get("category_id")
        sub_product_id = post_data.get("product_id")
        properties = post_data.get("properties")
        name = post_data.get("name")
        main_images = post_data.get("main_images")
        presell_type = post_data.get("presell_type", 0)

        if presell_type not in [0, 1]:
            raise APIViewException(err_message="发货模式错误")

        self._validate_value_type([shop_id, category_leaf_id, sub_product_id])
        if not name or calculate_string_width(name) < 8:
            raise APIViewException(err_message="商品标题最短不能低于8个汉字,请修改商品标题")
        elif calculate_string_width(name) > 60:
            raise APIViewException(err_message="商品标题最大不能超过60个字符,请修改商品标题")

        if not main_images:
            raise APIViewException(err_message="缺少主图信息, 请先更新商品的主图")

        if not isinstance(main_images, list):
            raise APIViewException(err_message="商品主图参数错误,请刷新后重试")

        main_images = process_image_options(main_images, post_data, self.current_user.distributor)
        if len(main_images) < 3:
            raise APIViewException(err_message="上传抖店最少需要上传3张图片")
        # 只提取5张图片
        main_images = main_images[:5]

        data_shop = validata_data_shop(shop_id)
        try:
            sub_product = SubProduct.objects.get(
                product_id=sub_product_id,
                is_deleted=False,
                parent_product__is_deleted=False,
            )
        except SubProduct.DoesNotExist:
            raise APIViewException(err_message="商品不存在或已删除,请刷新页面后重试")

        # 校验分类数据
        category = data_shop.datacategory_set.filter(category_id=category_leaf_id).first()
        if not category:
            raise APIViewException(err_message="店铺分类不存在,请刷新页面后重试")
        if not category.is_leaf:
            raise APIViewException(err_message="分类必须为最后一级分类")

        last_map = sub_product.datashopproductmap_set.filter(data_shop=data_shop).last()
        if not last_map:
            raise APIViewException(err_message="商品尚未上传到该店铺,无法进行编辑")

        newest_task = sub_product.datashopuploadtasks_set.filter(data_shop_id=data_shop.pk).last()
        if newest_task and newest_task.state in [0, 2]:
            raise APIViewException(err_message="商品正在上传,请稍后刷新查看状态")

        state, msg = edit_product(
            request,
            last_map,
            data_shop.pk,
            sub_product,
            category_leaf_id,
            properties,
            name,
            main_images,
            presell_type=presell_type,
        )

        if state == 0:
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=sub_product.product_id,
                    model=SubProduct,
                    describe=f"编辑商品`{sub_product}`同步到抖店【待审核】",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(code=202)
        if state == 1:
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=sub_product.product_id,
                    model=SubProduct,
                    describe=f"编辑商品`{sub_product}`同步到抖店【成功】",
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(code=200)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=sub_product.product_id,
                model=SubProduct,
                describe=f"编辑商品`{sub_product}`同步到抖店【失败】",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass
        return IResponse(code=400, message=msg)


class DataShopMyProductUploadListView(DBAPIView):
    def get(self, request: Request):
        sub_product_id = request.query_params.get("product_id")
        if not sub_product_id or not isinstance(sub_product_id, (int, str)):
            raise APIViewException(err_message="invalid params")

        try:
            sub_product = SubProduct.objects.get(
                product_id=sub_product_id,
                owner_id=self.current_user.distributor.distributor_id,
                is_deleted=False,
                parent_product__is_deleted=False,
            )
        except SubProduct.DoesNotExist:
            return IResponse(code=400, message="data not found")

        group_qs = DataShopUploadTasks.objects.filter(sub_product=sub_product, task_type="ADD").values("sub_product_id").annotate(max_id=Max("id"))

        group_qs_cte = With(group_qs)

        cte_qs = group_qs_cte.join(
            DataShopUploadTasks.objects.select_related("data_shop").only(
                "data_shop__shop_id",
                "data_shop__name",
                "state",
                "error_reason",
            ),
            id=group_qs_cte.col.max_id,
        ).with_cte(group_qs_cte)

        re_data = [
            {
                "shop_id": _data.data_shop.shop_id,
                "shop_name": _data.data_shop.name,
                "state": _data.state,
                "state_text": _data.get_state_display(),
                "error_reason": _data.error_reason,
            }
            for _data in cte_qs
        ]
        return IResponse(data=re_data)


class DataShopProductRulesView(DBAPIView):
    def get(self, request: Request):
        category_id = request.query_params.get("category_id")
        data = fetch_rules(3360, category_id)
        return IResponse(data=data)


class DataShopsDailyUseLogView(DBAPIView):
    def get(self, request: Request):
        logs = (
            DataShopsDailyUseLog.objects.filter(
                user=self.current_user,
                distributor=self.current_distributor,
            )
            .order_by("-operate_time")
            .values("data_shop_id", "data_shop__name")[:8]
        )
        re_data = [
            {
                "shop_id": log["data_shop_id"],
                "shop_name": log["data_shop__name"],
            }
            for log in logs
        ]
        return IResponse(data=re_data)

    def delete(self, request: Request):
        post_data = request.data
        shop_id = post_data.get("shop_id")
        if not shop_id:
            raise APIViewException

        DataShopsDailyUseLog.objects.filter(data_shop_id=shop_id, user=self.current_user, distributor=self.current_distributor).delete()
        return IResponse()


class DataShopsCategoryRulesView(DBAPIView):
    def get(self, request: Request):
        params = request.query_params
        shop_id = params.get("shop_id")
        category_id = params.get("category_id")

        data_shop = validata_data_shop(shop_id)
        rule = fetch_rules_with_cache(data_shop.id, category_id)

        re_data = {
            "time_sku_presell_with_normal_rule_support": rule["fulfillment_rule"]["time_sku_presell_with_normal_rule"]["support"],
        }
        return IResponse(data=re_data)
