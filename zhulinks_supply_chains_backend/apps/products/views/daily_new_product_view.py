from rest_framework.views import APIView

from users.models import UserMiniProgramSubscribe
from utils.http_handle import IResponse


class DailyNewProductView(APIView):
    def get_user_profile(self, request):
        """获取用户订阅信息"""
        return request.user.userminiprogramsubscribe_set.select_related("user").first()

    def get(self, request, *args, **kwargs):
        user_profile = self.get_user_profile(request)
        if not user_profile:
            return IResponse(code=400, message="User does not exist")
        return IResponse(data={"is_read": user_profile.is_read})

    def post(self, request, *args, **kwargs):
        user_profile = self.get_user_profile(request)
        if not user_profile:
            return IResponse(code=400, message="User does not exist")
        user_profile.is_read = True
        user_profile.save(update_fields=["is_read"])
        return IResponse(data=None)
