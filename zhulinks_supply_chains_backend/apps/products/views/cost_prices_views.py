# -*- coding: utf-8 -*-
from django.db.models import Window, F
from django.db.models.functions import Rank
from rest_framework.request import Request

from common.basic import OPAPIView
from common.basics.exceptions import APIViewException
from products.models import ProductModHistory
from utils.http_handle import IResponse


class LastCostPriceRecordView(OPAPIView):
    def post(self, request: Request):
        product_ids = request.data.get("product_ids")
        if not product_ids:
            raise APIViewException

        mod_histories = (
            ProductModHistory.objects.filter(
                mod_field_name="cost_price",
                product_id__in=product_ids,
            )
            .annotate(row=Window(expression=Rank(), partition_by="product_id", order_by=F("create_date").desc()))
            .filter(row=1)
            .only("product_id", "create_date")
            .all()
        )

        re_data = [
            {
                "product_id": mod_history.product_id,
                "newest_modify_time": mod_history.create_date,
            }
            for mod_history in mod_histories
        ]
        return IResponse(data=re_data)
