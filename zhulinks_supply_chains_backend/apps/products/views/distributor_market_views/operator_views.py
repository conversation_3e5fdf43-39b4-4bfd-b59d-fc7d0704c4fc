# -*- coding: utf-8 -*-
import copy
import decimal
from decimal import ROUND_HALF_UP

from django.db import transaction
from django.db.models import Prefetch, F
from django.utils import timezone
from rest_framework.request import Request

from common import logger
from common.basic import OPAPIView
from common.basics.views import set_log_params, OperateLogAPIViewMixin
from common.basics.exceptions import APIViewException
from common.utils import ProductBulkExtraTags, diff_models
from companies.models import Distributor
from orders.new_models import ShippingTemplateModel
from products.bulk_query import (
    bulk_query_labels,
    bulk_query_confirm_state,
    bulk_query_skus_specs_detail,
    bulk_query_latest_history_price,
    bulk_query_products_promotions,
)
from products.filtersets import OPDistributorMarketProductListFilterSet, OPNotInDistributorMarketProductListFilterSet
from products.logics.distribute_market import process_price_with_rule
from products.models import (
    StockKeepingUnit,
    Product,
    bulk_query_product_hosting_state,
    get_category_name_by_id,
    SubProduct,
    sub_product_code_generator,
    DistributorMarketProductPriceConfig,
)
from products.models_v2.distribute_market import ProductDistributePriceSettings
from products.serializers import (
    OPDistributorMarketProductPriceConfigListSer,
    OPDistributorMarketProductsListSer,
    OPRawDistributorMarketProductsListSer,
    OPDistributorMarketSKUListSer,
)
from products.tasks import flush_products_market_price, sync_product_to_JST, flush_distributor_mode_relates
from products.tasks_v2 import validate_distributor_product_info_task
from utils.http_handle import custom_django_filter, IResponse
from products.tasks_v2.flush_products_distributor_market_price import flush_products_distributor_market_price
from products.models_v2 import DistributedProductInfoModel


def _validate_distribute_product(product_id, new_add: bool = False) -> Product:
    try:
        product = Product.objects.get(product_id=product_id)
    except Product.DoesNotExist:
        raise APIViewException(err_message="商品不存在")

    if product.state != 1:
        raise APIViewException(err_message="商品未上架,无法设置分销商价格")

    if product.is_deleted:
        raise APIViewException(err_message="商品已删除,无法设置分销商价格")

    if new_add:
        if product.is_in_distributor_market:
            raise APIViewException(err_message="商品已在分销市场")
    else:
        if not product.is_in_distributor_market:
            raise APIViewException(err_message="商品未加入到分销市场")

    return product


class OPDistributorMarketProductView(OPAPIView, OperateLogAPIViewMixin):
    """
    运营商 - 分销市场商品列表
    """

    fronted_page = "分销市场"
    resource_name = "商品分销价"
    need_format_resource_name = False

    def get(self, request: Request, *args, **kwargs):
        """
        获取商品列表
        """
        is_in_distributor_market = request.query_params.get("is_in_distributor_market", "0")

        if is_in_distributor_market not in ["0", "1"]:
            raise APIViewException(err_message="分销商市场参数错误")

        normal_sku_qs = StockKeepingUnit.objects.filter(become_history=False)
        sku_prefetch = Prefetch("stockkeepingunit_set", queryset=normal_sku_qs)

        price_configs = None

        if is_in_distributor_market == "0":
            filter_set = OPNotInDistributorMarketProductListFilterSet
            # 未分销
            target = (
                Product.get_operator_products(request, need_annotate_warehouse_inventory=True)
                .prefetch_related(
                    "handcard",
                    "address",
                    sku_prefetch,
                )
                .filter(is_in_distributor_market=False, state=1, company__is_self_support=False, is_combine=False)
                .order_by("-create_date")
            )

            # 查询价格配置
            price_configs = DistributorMarketProductPriceConfig.objects.all()
        else:
            filter_set = OPDistributorMarketProductListFilterSet
            # 分销中的商品
            sub_products_prefetch = Prefetch("subproduct_set", queryset=SubProduct.objects.filter(is_in_distributor_market=True, owner__letters="FX"))
            target = (
                Product.get_operator_products(request, is_deleted=None, need_annotate_warehouse_inventory=True)
                .prefetch_related(
                    "handcard",
                    "address",
                    sku_prefetch,
                    sub_products_prefetch,
                )
                .filter(is_in_distributor_market=True, company__is_self_support=False, subproduct__owner__letters="FX", is_combine=False)
                .order_by("-subproduct__create_date")
                .distinct()
            )

        if kwargs.get("internal_pid_list"):
            target = target.filter(product_id__in=kwargs.get("internal_pid_list"))

        re_data, page_products, obj_qs = custom_django_filter(
            request,
            target,
            filter_set,
            need_serialize=False,
            force_order=False,
            order_map={"physical_inventory": "warehouse_inventory"},  # 分销市场用仓库库存，排序需要映射
        )

        # 查询托管状态
        product_ids = [product_obj.product_id for product_obj in page_products]
        hosting_product_map = bulk_query_product_hosting_state(product_ids)

        labels_map = bulk_query_labels(product_ids)

        # 批量查询锁库存状态
        p_confirm_states = bulk_query_confirm_state(product_ids)

        skus = [sku for product_obj in page_products for sku in product_obj.stockkeepingunit_set.all()]
        # 批量查询sku的规格
        skus_map = bulk_query_skus_specs_detail(skus)

        # 最新卖价
        latest_history_map = bulk_query_latest_history_price(skus)

        # 批量查询tags
        extra_tags_map = ProductBulkExtraTags(page_products)

        # 优惠信息
        promotions_map = {}

        if is_in_distributor_market == "1":
            # 分销市场的商品返回优惠信息
            promotions_map = bulk_query_products_promotions(product_ids)

        # FX子商品信息
        sub_products_map = {}
        distribute_price_settings_map = {}
        if is_in_distributor_market == "1":
            sub_products_map = {sub_product.parent_product_id: sub_product for product in page_products for sub_product in product.subproduct_set.all()}

            # 查询价格配置
            _settings = ProductDistributePriceSettings.objects.filter(product_id__in=product_ids, become_history=False)
            for _setting in _settings:
                _data = {
                    "shipping_template_id": _setting.shipping_template_id,
                    "ladder_settings": _setting.ladder_settings,
                }
                if _setting.product_id not in distribute_price_settings_map:
                    distribute_price_settings_map[_setting.product_id] = {_setting.set_type: _data}
                else:
                    distribute_price_settings_map[_setting.product_id][_setting.set_type] = _data

        for product_obj in page_products:
            if is_in_distributor_market == "1":
                product_ser = OPDistributorMarketProductsListSer(
                    instance=product_obj,
                    context={
                        "request": self.request,
                        "labels_map": labels_map,
                        "extra_tags_map": extra_tags_map,
                        "sub_products_map": sub_products_map,
                    },
                )
            else:
                product_ser = OPRawDistributorMarketProductsListSer(
                    instance=product_obj,
                    context={
                        "request": self.request,
                        "labels_map": labels_map,
                        "extra_tags_map": extra_tags_map,
                        "sub_products_map": sub_products_map,
                    },
                )

            data = product_ser.data
            category = data.get("category")
            category_list = get_category_name_by_id(category)
            data["category"] = category_list

            # 添加商品确认，确认状态为1时不允许修改库存数据
            data["product_confirm_state"] = p_confirm_states.get(product_obj.product_id)
            # 添加商品关联的货号信息
            data["origin_product_id"] = product_obj.origin_product_id

            sku_qs = product_obj.stockkeepingunit_set.all()

            skus = OPDistributorMarketSKUListSer(
                instance=sku_qs,
                many=True,
                context={
                    "request": self.request,
                    "skus_map": skus_map,
                    "latest_history_map": latest_history_map,
                    "is_in_distributor_market": is_in_distributor_market,
                    "price_configs": price_configs,
                },
            )

            skus_data = skus.data
            # 如果在分销市场， 返回模板id和阶梯价格给前端
            if is_in_distributor_market == "1":
                sku_price_settings = distribute_price_settings_map.get(product_obj.product_id, {}).get(1, {})

                sku_template_id = sku_price_settings.get("shipping_template_id")

                ladder_settings = distribute_price_settings_map.get(product_obj.product_id, {}).get(2, {})
                data["distributor_price_settings"] = {"sku_price_settings": None, "ladder_price_settings": None}

                if sku_template_id:
                    data["distributor_price_settings"]["sku_price_settings"] = {"shipping_template_id": sku_template_id}

                ladder_prices = []
                if ladder_settings:
                    ladder_prices = ladder_settings.get("ladder_settings") or []
                    data["distributor_price_settings"]["ladder_price_settings"] = {
                        "shipping_template_id": ladder_settings.get("shipping_template_id"),
                        "data": ladder_prices,
                    }

                try:
                    if not sku_price_settings:
                        data["min_distributor_market_price"] = min([i["distributor_market_price"] for i in ladder_prices])
                        data["max_distributor_market_price"] = max([i["distributor_market_price"] for i in ladder_prices])
                except Exception as e:
                    pass

            data["skus"] = skus_data

            # 添加状态
            data["is_new"] = product_obj.is_new
            data["hosting_state"] = hosting_product_map.get(product_obj.product_id)

            # 优惠信息
            data["promotions"] = promotions_map.get(product_obj.product_id, []) or []

            re_data["data"].append(data)

        return IResponse(data=re_data)

    def get_pre_sale_distributor_market_price(self, cost_price, price_configs):
        cost_price = decimal.Decimal(str(cost_price))
        _calc_price = decimal.Decimal(0)

        for price_config in price_configs:
            if cost_price > decimal.Decimal(str(price_config.min_cost_price)):  # 将 min_cost_price 也转为 Decimal
                _calc_price = cost_price * decimal.Decimal(price_config.markup_percentage / 100)

        price = _calc_price.quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
        rule_price = process_price_with_rule(price)
        return rule_price

    def _sku_price_settings_validate(self, skus_data, product):
        if not skus_data:
            raise APIViewException(err_message="缺少规格信息")
        # 查询价格配置
        price_configs = DistributorMarketProductPriceConfig.objects.all()

        # 查询规格信息
        product_skus = product.stockkeepingunit_set.filter(become_history=False)
        product_skus_map = {sku.sku_id: sku for sku in product_skus}

        need_update_sku_objs = []
        min_market_price = None
        max_market_price = None
        min_promotion_price = None
        max_promotion_price = None
        log_operate_content_list = []
        # todo 优化代码结构
        for sku_data in skus_data:
            sku_id = int(sku_data.get("sku_id"))
            distributor_market_price = sku_data.get("distributor_market_price")
            # 获取分销推广价
            distributor_promotion_price = sku_data.get("distributor_promotion_price")
            if (not sku_id) or (not str(sku_id).isdigit()):
                raise APIViewException(err_message="规格信息错误,请刷新后重试")

            try:
                cleaned_distributor_market_price = decimal.Decimal(distributor_market_price)
                cleaned_distributor_market_price = cleaned_distributor_market_price.quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
            except Exception as e:
                raise APIViewException(err_message="分销价错误")
            try:
                cleaned_distributor_promotion_price = decimal.Decimal(distributor_promotion_price)
                cleaned_distributor_promotion_price = cleaned_distributor_promotion_price.quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
            except Exception as e:
                raise APIViewException(err_message="分销推广价错误")

            if cleaned_distributor_market_price < 0 or cleaned_distributor_market_price > 9999999.99:
                raise APIViewException(err_message="分销推广价错误,请重新输入")

            if cleaned_distributor_promotion_price < 0 or cleaned_distributor_promotion_price > 9999999.99:
                raise APIViewException(err_message="分销推广价错误,请重新输入")

            if sku_id not in product_skus_map:
                raise APIViewException(err_message="规格信息不存在")
            sku = product_skus_map[sku_id]

            # 分销价不能低于分销推广价
            if cleaned_distributor_market_price < cleaned_distributor_promotion_price:
                raise APIViewException(err_message="商品规格报价中，分销价不能小于分销推广价")

            # # 不能低于商品的最高推广价
            # # print(f"max_cost_price:{product.max_cost_price}")
            # if cleaned_distributor_promotion_price < cleaned_distributor_market_price:
            #     raise APIViewException(err_message="分销价格不能低于商品分销推广价")
            #
            # # # 不能低于商品的最高推广价
            # if cleaned_distributor_market_price < (sku.cost_price or 0):
            #     raise APIViewException(err_message="分销价不得小于该商品sku推广价")
            # # # 分销推广价不能低于商品的最高推广价
            # if cleaned_distributor_promotion_price < (sku.cost_price or 0):
            #     raise APIViewException(err_message="分销推广价不得小于该商品sku推广价")

            # 旧价格
            old_market_price = copy.deepcopy(sku.distributor_market_price)

            # 如果本来没有价格，重新计算出旧价格
            if not old_market_price:
                old_market_price = self.get_pre_sale_distributor_market_price(sku.cost_price, price_configs)
            # 旧分销推广价
            old_promotion_price = copy.deepcopy(sku.distributor_promotion_price)

            if old_market_price != cleaned_distributor_market_price:
                sku.distributor_market_price = cleaned_distributor_market_price
                need_update_sku_objs.append(sku)
            else:
                # V2.9.7修改：根据分销推广价计算分销价
                cleaned_distributor_market_price = self.get_pre_sale_distributor_market_price(distributor_promotion_price, price_configs)
                if cleaned_distributor_market_price > 9999999.99:
                    raise APIViewException(err_message="商品规格报价中，分销价计算后大于9999999.99，请重新输入分销推广价。")
                sku.distributor_market_price = cleaned_distributor_market_price
                need_update_sku_objs.append(sku)

            log_old_market_price = "-" if old_market_price is None else old_market_price
            log_operate_content_list.append(f"{sku.sku_id}分销价:{log_old_market_price}改成{cleaned_distributor_market_price}")

            # if cleaned_distributor_market_price < cleaned_distributor_promotion_price:
            #     raise APIViewException(err_message="商品规格报价中，分销价不能小于分销推广价")

            if old_promotion_price != cleaned_distributor_promotion_price:
                sku.distributor_promotion_price = cleaned_distributor_promotion_price
                need_update_sku_objs.append(sku)

                log_old_promotion_price = "-" if old_promotion_price is None else old_promotion_price
                log_operate_content_list.append(f"{sku.sku_id}分销推广价:{log_old_promotion_price}改成{cleaned_distributor_promotion_price}")

            if min_market_price is None:
                min_market_price = cleaned_distributor_market_price
            elif cleaned_distributor_market_price < min_market_price:
                min_market_price = cleaned_distributor_market_price

            if max_market_price is None:
                max_market_price = cleaned_distributor_market_price
            elif cleaned_distributor_market_price > max_market_price:
                max_market_price = cleaned_distributor_market_price

            if min_promotion_price is None:
                min_promotion_price = cleaned_distributor_promotion_price
            elif cleaned_distributor_promotion_price < min_promotion_price:
                min_promotion_price = cleaned_distributor_promotion_price

            if max_promotion_price is None:
                max_promotion_price = cleaned_distributor_promotion_price
            elif cleaned_distributor_promotion_price > max_promotion_price:
                max_promotion_price = cleaned_distributor_promotion_price

        old_product_min_market_price = copy.deepcopy(product.min_distributor_market_price)
        old_product_max_market_price = copy.deepcopy(product.max_distributor_market_price)

        old_product_min_promotion_price = copy.deepcopy(product.min_distributor_promotion_price)
        old_product_max_promotion_price = copy.deepcopy(product.max_distributor_promotion_price)

        # 商品最大最小分销价格
        product.update_user = self.current_user.user_id
        product.min_distributor_market_price = min_market_price
        product.max_distributor_market_price = max_market_price
        # 修改分销推广价
        product.min_distributor_promotion_price = min_promotion_price
        product.max_distributor_promotion_price = max_promotion_price

        content = ""
        log_old_product_min_market_price = "-" if old_product_min_market_price is None else old_product_min_market_price
        log_old_product_max_market_price = "-" if old_product_max_market_price is None else old_product_max_market_price

        log_old_product_min_promotion_price = "-" if old_product_min_promotion_price is None else old_product_min_promotion_price
        log_old_product_max_promotion_price = "-" if old_product_max_promotion_price is None else old_product_max_promotion_price
        # 日志记录
        if old_product_min_market_price != min_market_price and old_product_max_market_price != max_market_price:
            content = f"商品最小分销价:{log_old_product_min_market_price}改成:{min_market_price}; 最大分销价:{log_old_product_max_market_price}改成:{max_market_price}"
        elif old_product_min_market_price != min_market_price:
            content = f"商品最小分销价:{log_old_product_min_market_price}改成:{min_market_price};"
        elif old_product_max_market_price != max_market_price:
            content = f"商品最大分销价:{log_old_product_max_market_price}改成:{max_market_price}"

        # 分销推广价
        if old_product_min_promotion_price != min_promotion_price and old_product_max_promotion_price != max_promotion_price:
            content = f"商品最小分销推广价:{log_old_product_min_promotion_price}改成:{min_promotion_price}; 最大分销价:{log_old_product_max_promotion_price}改成:{max_promotion_price}"
        elif old_product_min_promotion_price != min_promotion_price:
            content = f"商品最小分销推广价:{log_old_product_min_promotion_price}改成:{min_promotion_price};"
        elif old_product_max_promotion_price != max_promotion_price:
            content = f"商品最大分销推广价:{log_old_product_max_promotion_price}改成:{max_promotion_price}"

        if content:
            operate_log = content + "\n" + "、".join(log_operate_content_list)
        else:
            operate_log = "、".join(log_operate_content_list)

        return product, need_update_sku_objs, operate_log

    @staticmethod
    def _ladder_price_settings_validate(data, product):
        if not data:
            raise APIViewException(err_message="缺少阶梯价格设置信息")

        for _data in data:
            purchase_count = _data.get("purchase_count")
            distributor_market_price = _data.get("distributor_market_price")

            if not purchase_count:
                raise APIViewException(err_message="缺少起购数量")

            if not distributor_market_price:
                raise APIViewException(err_message="缺少起购分销价格")

            try:
                cleaned_distributor_market_price = decimal.Decimal(distributor_market_price)
                cleaned_distributor_market_price = cleaned_distributor_market_price.quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
            except Exception as e:
                raise APIViewException(err_message="分销价格错误")

            if cleaned_distributor_market_price < 0 or cleaned_distributor_market_price > 9999999.99:
                raise APIViewException(err_message="推广价错误,请重新输入")

            # # 不能低于商品的最高推广价
            if cleaned_distributor_market_price < (product.max_distributor_promotion_price or 0):
                raise APIViewException(err_message=f"阶梯报价中，分销价格不能低于商品最高分销推广价，商品最高分销推广价为：{product.max_distributor_promotion_price}")

            # 转成float存json到数据库
            _data["distributor_market_price"] = float(cleaned_distributor_market_price)

        # 按照 purchase_count 进行升序排序
        sorted_data = sorted(data, key=lambda x: x["purchase_count"])

        # 验证排序后的列表，确保每个 purchase_count 都比前一个 purchase_count 大
        for i in range(1, len(sorted_data)):
            if sorted_data[i]["purchase_count"] <= sorted_data[i - 1]["purchase_count"]:
                raise ValueError("起购数量必须比前面的大")

        return sorted_data

    def update_or_create_objs(self, post_data, new_add: bool = False):
        product_id = post_data.get("product_id")
        if not product_id:
            raise APIViewException
        sku_price_settings = post_data.get("sku_price_settings")
        ladder_price_settings = post_data.get("ladder_price_settings")
        distributed_product_info_data = post_data.get("distributed_product_info_data")

        if not sku_price_settings and not ladder_price_settings:
            raise APIViewException(err_message="价格设置不能同时为空")

        product = _validate_distribute_product(product_id, new_add)
        product_name = distributed_product_info_data.get("product_name", "") if distributed_product_info_data else product.name
        main_images = distributed_product_info_data.get("main_images", []) if distributed_product_info_data else product.main_images
        price_template_update_content = ""
        need_update_sku_objs = None
        operate_log = None
        ladder_change_log = None
        product_updated = False
        sku_shipping_type_obj = None
        ladder_shipping_type_setting_obj = None
        sub_product = None

        if sku_price_settings:
            shipping_template_id = sku_price_settings.get("shipping_template_id")
            if shipping_template_id:
                try:
                    shipping_template = ShippingTemplateModel.objects.get(pk=shipping_template_id)
                except ShippingTemplateModel.DoesNotExist:
                    raise APIViewException(err_message="商品价格运费模板不存在")

                sku_shipping_type_obj = product.productdistributepricesettings_set.filter(set_type=1).last()

                if not sku_shipping_type_obj:
                    sku_shipping_type_obj = ProductDistributePriceSettings(
                        product_id=product_id,
                        set_type=1,
                        shipping_template_id=shipping_template_id,
                        create_user=self.current_user.user_id,
                    )
                else:
                    # 更新内容
                    old_template = sku_shipping_type_obj.shipping_template
                    price_template_update_content = diff_models(old_template, shipping_template)

                    sku_shipping_type_obj.shipping_template_id = shipping_template_id
                    sku_shipping_type_obj.update_user = self.current_user.user_id
                    sku_shipping_type_obj.become_history = False

            skus_data = sku_price_settings.get("data")
            product_updated = True
            product, need_update_sku_objs, operate_log = self._sku_price_settings_validate(skus_data, product)
        else:
            product.productdistributepricesettings_set.filter(set_type=1).update(become_history=True)

        if ladder_price_settings:
            data = ladder_price_settings.get("data")

            cleaned_data = self._ladder_price_settings_validate(data, product)

            shipping_template_id = ladder_price_settings.get("shipping_template_id")
            if shipping_template_id:
                if not ShippingTemplateModel.objects.filter(pk=shipping_template_id).exists():
                    raise APIViewException(err_message="商品价格运费模板不存在")

            ladder_shipping_type_setting_obj = product.productdistributepricesettings_set.filter(set_type=2).last()

            if not ladder_shipping_type_setting_obj:
                ladder_shipping_type_setting_obj = ProductDistributePriceSettings(
                    product_id=product_id,
                    set_type=2,
                    ladder_settings=cleaned_data,
                    shipping_template_id=shipping_template_id,
                    create_user=self.current_user.user_id,
                )
            else:
                old_settings_str = "、".join(["{}>>{}".format(i["purchase_count"], i["distributor_market_price"]) for i in ladder_shipping_type_setting_obj.ladder_settings])
                new_settings_str = "、".join(["{}>>{}".format(i["purchase_count"], i["distributor_market_price"]) for i in cleaned_data])
                ladder_shipping_type_setting_obj.shipping_template_id = shipping_template_id
                ladder_shipping_type_setting_obj.ladder_settings = cleaned_data
                ladder_shipping_type_setting_obj.update_user = self.current_user.user_id
                ladder_shipping_type_setting_obj.become_history = False
                if old_settings_str != new_settings_str:
                    ladder_change_log = f"价格设置, 修改前:{old_settings_str}。修改后:{new_settings_str}"
        else:
            product.productdistributepricesettings_set.filter(set_type=2).update(become_history=True)

            if not product.min_distributor_market_price:
                # 如果没有分销价格，更新商品信息
                # DONE 修改分销价
                configs = DistributorMarketProductPriceConfig.objects.filter(is_deleted=False)
                for config in configs:
                    # flush_products_market_price.delay(config.min_cost_price, config.max_cost_price,config.markup_percentage, product_pk_list)
                    flush_products_distributor_market_price.delay(config.min_cost_price, config.max_cost_price, config.markup_percentage, [product.pk])

        if new_add:
            product.is_in_distributor_market = True
            product_updated = True
            # 获取内置分销商
            fx_distributor, _ = Distributor.objects.get_distribute_object()
            # 新增副本商品
            sub_product = SubProduct.origin_objects.filter(parent_product=product, owner=fx_distributor).last()
            if sub_product:
                sub_product.name = product_name
                sub_product.main_images = main_images
                sub_product.detail_images = product.detail_images
                sub_product.remark = product.remark
                sub_product.size = product.size
                sub_product.is_in_distributor_market = True
                sub_product.update_user = self.current_user.user_id
                sub_product.create_date = timezone.now()
            else:
                sub_product = SubProduct(
                    parent_product_id=product.pk,
                    name=product_name,
                    main_images=main_images,
                    detail_images=product.detail_images,
                    remark=product.remark,
                    size=product.size,
                    owner=fx_distributor,
                    create_user=self.current_user.user_id,
                    is_in_distributor_market=True,
                )

        with transaction.atomic():
            if need_update_sku_objs:
                StockKeepingUnit.objects.bulk_update(need_update_sku_objs, fields=["distributor_market_price", "distributor_promotion_price"])

            if product_updated:
                product.save(
                    update_fields=[
                        "update_user",
                        "min_distributor_market_price",
                        "max_distributor_market_price",
                        "is_in_distributor_market",
                        # V2.9.7新增分销推广价
                        "min_distributor_promotion_price",
                        "max_distributor_promotion_price",
                    ]
                )

            if sku_shipping_type_obj:
                sku_shipping_type_obj.save()

            if ladder_shipping_type_setting_obj:
                ladder_shipping_type_setting_obj.save()

            if sub_product:
                sub_product.save()

            # 获取或创建 DistributedProductInfoModel 实例
            distributed_product_info, created = DistributedProductInfoModel.objects.get_or_create(parent_product=product, defaults={"product_name": product_name, "main_images": main_images})

            # 如果实例已存在且需要更新
            if not created:
                distributed_product_info.product_name = product_name
                distributed_product_info.main_images = main_images
                distributed_product_info.save()

        # 同步到聚水潭
        return operate_log, price_template_update_content, ladder_change_log, product_id, product, sub_product

    def post(self, request: Request):
        post_data = request.data
        operate_log, price_template_update_content, ladder_change_log, product_id, product, sub_product = self.update_or_create_objs(post_data, new_add=True)
        # 同步到聚水潭
        sync_product_to_JST.delay(product.product_id, sub_product.product_id)
        distributor, _ = Distributor.objects.get_distribute_object()
        # 增加关联关系
        flush_distributor_mode_relates.delay([product.product_id], distributor.distributor_id, self.current_user.user_id)
        # ai检测
        validate_distributor_product_info_task.delay(product.product_id, distributor.distributor_id)
        # 日志记录
        try:
            remark_str = ""
            if operate_log and price_template_update_content:
                remark_str = f"加入分销市场, 规格报价更新: {operate_log} || {price_template_update_content}"
            elif operate_log:
                remark_str = f"加入分销市场, 规格报价更新: {operate_log}"
            elif price_template_update_content:
                remark_str = f"加入分销市场, 规格报价更新: {price_template_update_content}"

            if not remark_str:
                if ladder_change_log:
                    remark_str = f"加入分销市场, 阶梯报价更新: {ladder_change_log}"
            else:
                if ladder_change_log:
                    remark_str += f"\n阶梯报价更新: {ladder_change_log}"

            set_log_params(
                request,
                model=Product,
                resource_id=product_id,
                describe=f"加入分销市场:{product}",
                remark=remark_str,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def put(self, request: Request):
        post_data = request.data

        operate_log, price_template_update_content, ladder_change_log, product_id, product, sub_product = self.update_or_create_objs(post_data)

        # 日志记录
        try:
            remark_str = ""
            if operate_log and price_template_update_content:
                remark_str = f"分销价格更新, 规格报价更新: {operate_log} || {price_template_update_content}"
            elif operate_log:
                remark_str = f"分销价格更新, 规格报价更新: {operate_log}"
            elif price_template_update_content:
                remark_str = f"分销价格更新, 规格报价更新: {price_template_update_content}"

            if not remark_str:
                if ladder_change_log:
                    remark_str = f"分销价格更新, 阶梯报价更新: {ladder_change_log}"
            else:
                if ladder_change_log:
                    remark_str += f"\n阶梯报价更新: {ladder_change_log}"

            set_log_params(
                request,
                model=Product,
                resource_id=product_id,
                describe=f"更新商品:{product}分销价格",
                remark=remark_str,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class OPDistributorMarketProductManagerView(OPAPIView):
    """
    运营商 - 分销市场 加入分销商市场
    """

    fronted_page = "分销市场"
    resource_name = "商品管理"
    need_format_resource_name = False

    def post(self, request: Request):
        product_ids = request.data.get("product_ids")
        if not product_ids:
            raise APIViewException(err_message="invalid params")
        if len(product_ids) > 200:
            raise APIViewException(err_message="最大允许批量添加200个商品")
        letters = "FX"

        products = Product.objects.filter(product_id__in=product_ids, state=1, is_deleted=False)
        if products.count() != len(product_ids):
            raise APIViewException(err_message="商品信息错误,请刷新页面后重试")

        distributor, created = Distributor.objects.get_distribute_object()

        need_create_sub_products = []
        need_update_sub_products = []
        need_update_products = []

        product_pk_list = [p.pk for p in products]
        product_id_list = [p.product_id for p in products]
        # 分销的商品
        sub_products = SubProduct.origin_objects.filter(parent_product_id__in=product_pk_list, owner=distributor)
        sub_products_map = {sub_product.parent_product_id: sub_product for sub_product in sub_products}

        # 批量创建货号
        need_create_product_pk_list = [i for i in product_pk_list if i not in sub_products_map]
        product_pk_code_map = sub_product_code_generator(letters, need_create_product_pk_list)

        # sync to JST list
        need_sync_to_jst_id_list = []

        with transaction.atomic():
            for product in products:
                # 更新商品的市场状态
                product.is_in_distributor_market = True
                need_update_products.append(product)

                product_pk = product.pk

                # 更新副本商品
                if product_pk in sub_products_map:
                    sub_product = sub_products_map[product_pk]
                    sub_product.name = product.name
                    sub_product.main_images = product.main_images
                    sub_product.detail_images = product.detail_images
                    sub_product.remark = product.remark
                    sub_product.size = product.size
                    sub_product.is_in_distributor_market = True
                    sub_product.update_user = self.current_user.user_id
                    sub_product.create_date = timezone.now()
                    sub_product.update_date = timezone.now()
                    need_update_sub_products.append(sub_product)

                    # 同步到聚水潭
                    need_sync_to_jst_id_list.append((product.product_id, sub_product.product_id))

                else:
                    _code = product_pk_code_map.get(product_pk)
                    if not _code:
                        raise APIViewException(err_message="生成货号失败,请稍后重试")

                    _sub_product = SubProduct(
                        parent_product_id=product_pk,
                        code=_code,
                        inited_code=_code,
                        name=product.name,
                        main_images=product.main_images,
                        detail_images=product.detail_images,
                        remark=product.remark,
                        size=product.size,
                        owner=distributor,
                        create_user=self.current_user.user_id,
                        is_in_distributor_market=True,
                    )

                    need_create_sub_products.append(_sub_product)
                    # 同步到聚水潭
                    need_sync_to_jst_id_list.append((product.product_id, _sub_product.product_id))

            # 模板设置成历史
            ProductDistributePriceSettings.objects.filter(product_id__in=product_id_list).update(become_history=True)

            # 更新商品的市场状态
            if need_update_products:
                Product.objects.bulk_update(need_update_products, fields=["is_in_distributor_market"])

            # 创建副本商品
            if need_create_sub_products:
                SubProduct.objects.bulk_create(need_create_sub_products)

            # 更新副本商品
            if need_update_sub_products:
                SubProduct.origin_objects.bulk_update(
                    need_update_sub_products,
                    fields=(
                        "name",
                        "main_images",
                        "detail_images",
                        "remark",
                        "size",
                        "is_in_distributor_market",
                        "update_user",
                        "update_date",
                        "create_date",
                    ),
                )

        # 更新商品信息
        configs = DistributorMarketProductPriceConfig.objects.filter(is_deleted=False)
        # DONE 修改计算方式
        for config in configs:
            # flush_products_market_price.delay(config.min_cost_price, config.max_cost_price, config.markup_percentage, product_pk_list)
            flush_products_distributor_market_price.delay(config.min_cost_price, config.max_cost_price, config.markup_percentage, product_pk_list)

        # 同步到聚水潭
        for p_id, sub_p_id in need_sync_to_jst_id_list:
            sync_product_to_JST.delay(p_id, sub_p_id)

        if product_id_list:
            flush_distributor_mode_relates.delay(product_id_list, distributor.distributor_id, self.current_user.user_id)

        # 全部ai检测
        for pid in product_ids:
            validate_distributor_product_info_task.delay(pid, distributor.distributor_id)

        # 日志记录
        try:
            content = "加入商品到分销市场. 商品ID: {}".format("、".join([str(i) for i in product_ids]))
            set_log_params(
                request,
                model=Product,
                describe=f"加入商品到分销市场",
                operate_content=content,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()

    def delete(self, request: Request):
        product_ids = request.data.get("product_ids")
        if not product_ids:
            raise APIViewException(err_message="invalid params")

        with transaction.atomic():
            SubProduct.objects.filter(
                owner__letters="FX",
                is_in_distributor_market=True,
                parent_product__product_id__in=product_ids,
            ).update(
                is_in_distributor_market=False,
                update_user=self.current_user.user_id,
            )

            Product.objects.filter(product_id__in=product_ids).update(
                is_in_distributor_market=False, min_distributor_promotion_price=F("min_cost_price"), max_distributor_promotion_price=F("max_cost_price")
            )
            StockKeepingUnit.objects.filter(product__product_id__in=product_ids).update(distributor_promotion_price=F("cost_price"))
            DistributedProductInfoModel.objects.filter(parent_product__product_id__in=product_ids).delete()
        # 日志记录
        try:
            content = "从分销市场移除商品. 商品ID: {}".format("、".join([str(i) for i in product_ids]))
            set_log_params(
                request,
                model=Product,
                describe=f"从分销市场移除商品",
                operate_content=content,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class OPDistributorMarketProductPriceView(OPAPIView):
    fronted_page = "分销市场"
    resource_name = "商品分销价"
    need_format_resource_name = False

    def put(self, request: Request):
        post_data = request.data

        product_id = post_data.get("product_id")
        skus_data = post_data.get("skus_data")

        if (not product_id) or (not skus_data) or (not isinstance(skus_data, list)) or (not isinstance(product_id, (int, str))):
            raise APIViewException(err_message="invalid params")

        product = _validate_distribute_product(product_id)

        for sku_data in skus_data:
            sku_id = sku_data.get("sku_id")
            distributor_market_price = sku_data.get("distributor_market_price")

            if (not sku_id) or (not str(sku_id).isdigit()):
                raise APIViewException(err_message="规格信息错误,请刷新后重试")

            try:
                cleaned_distributor_market_price = decimal.Decimal(distributor_market_price)
                cleaned_distributor_market_price = cleaned_distributor_market_price.quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
            except Exception as e:
                raise APIViewException(err_message="分销价格错误")

            if cleaned_distributor_market_price < 0 or cleaned_distributor_market_price > 9999999.99:
                raise APIViewException(err_message="推广价错误,请重新输入")

            # 不能低于商品的最高推广价
            if cleaned_distributor_market_price < (product.max_cost_price or 0):
                raise APIViewException(err_message="分销价格不能低于商品最高推广价")

            sku_data["distributor_market_price"] = cleaned_distributor_market_price

        product_skus = product.stockkeepingunit_set.filter(become_history=False)
        product_skus_map = {sku.sku_id: sku for sku in product_skus}

        need_update_sku_objs = []
        min_market_price = None
        max_market_price = None

        log_operate_content_list = []
        #
        for sku_data in skus_data:
            sku_id = int(sku_data.get("sku_id"))
            distributor_market_price = sku_data.get("distributor_market_price")

            if sku_id not in product_skus_map:
                raise APIViewException(err_message="规格信息不存在")

            sku = product_skus_map[sku_id]
            # 旧价格
            old_market_price = copy.deepcopy(sku.distributor_market_price)

            if old_market_price != distributor_market_price:
                sku.distributor_market_price = distributor_market_price
                need_update_sku_objs.append(sku)

                log_old_market_price = "-" if old_market_price is None else old_market_price
                log_operate_content_list.append(f"{sku.sku_id}分销价:{log_old_market_price}改成{distributor_market_price}")

            if min_market_price is None:
                min_market_price = distributor_market_price
            elif distributor_market_price < min_market_price:
                min_market_price = distributor_market_price

            if max_market_price is None:
                max_market_price = distributor_market_price
            elif distributor_market_price > min_market_price:
                max_market_price = distributor_market_price

        old_product_min_market_price = copy.deepcopy(product.min_distributor_market_price)
        old_product_max_market_price = copy.deepcopy(product.max_distributor_market_price)

        product.update_user = self.current_user.user_id
        product.min_distributor_market_price = min_market_price
        product.max_distributor_market_price = max_market_price

        with transaction.atomic():
            StockKeepingUnit.objects.bulk_update(need_update_sku_objs, fields=["distributor_market_price"])
            product.save(update_fields=["update_user", "min_distributor_market_price", "max_distributor_market_price"])

        # 日志记录
        try:
            content = ""
            log_old_product_min_market_price = "-" if old_product_min_market_price is None else old_product_min_market_price
            log_old_product_max_market_price = "-" if old_product_max_market_price is None else old_product_max_market_price

            # 日志记录
            if old_product_min_market_price != min_market_price and old_product_max_market_price != max_market_price:
                content = f"商品最小分销价:{log_old_product_min_market_price}改成:{min_market_price}; 最大分销价:{log_old_product_max_market_price}改成:{max_market_price}"
            elif old_product_min_market_price != min_market_price:
                content = f"商品最小分销价:{log_old_product_min_market_price}改成:{min_market_price};"
            elif old_product_max_market_price != max_market_price:
                content = f"商品最大分销价:{log_old_product_max_market_price}改成:{max_market_price}"

            if content:
                operate_log = content + "\n" + "、".join(log_operate_content_list)
            else:
                operate_log = "、".join(log_operate_content_list)

            set_log_params(
                request,
                model=Product,
                resource_id=product_id,
                describe=f"更新商品:{product}分销价格",
                operate_content=operate_log,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()


class OPDistributorMarketProductPriceConfigView(OPAPIView):
    """
    分销市场 - 价格配置
    """

    fronted_page = "分销市场"
    resource_name = "价格配置"
    need_format_resource_name = False

    @staticmethod
    def _validate_config_post_data(post_data, exist_config_map) -> list:
        # 记录上一个结束的价格
        previous_end_price = None
        cleaned_data = []
        for idx, data in enumerate(post_data):
            config_id = data.get("id")
            if config_id:
                if not isinstance(config_id, (int, str)):
                    raise APIViewException(err_message="配置ID错误")
                if int(config_id) not in exist_config_map:
                    raise APIViewException(err_message="配置信息错误,请刷新页面后重试")

            # 成本价
            min_cost_price = data.get("min_cost_price", 0) or 0
            max_cost_price = data.get("max_cost_price", 0) or 0

            if min_cost_price < 0 or max_cost_price < 0:
                raise APIViewException(err_message=f"第{idx + 1}行：成本价不能小于0")

            if max_cost_price < min_cost_price:
                raise APIViewException(err_message=f"第{idx + 1}行：最大推广价不能小于最小推广价")

            # 起购数量
            min_purchase_num = data.get("min_purchase_num", 0) or 0
            max_purchase_num = data.get("max_purchase_num", 0) or 0

            if min_purchase_num < 0 or max_purchase_num < 0:
                raise APIViewException(err_message=f"第{idx + 1}行：起购数量不能小于0")

            if max_purchase_num < min_purchase_num:
                raise APIViewException(err_message=f"第{idx + 1}行：最大起购数量不能小于最小起购数量")

            # 加价百分比
            markup_percentage = data.get("markup_percentage", 0) or 0
            if markup_percentage < 0:
                raise APIViewException(err_message=f"第{idx + 1}行：加价百分比不能小于0")

            if previous_end_price is not None and previous_end_price > min_cost_price:
                raise APIViewException(err_message=f"第{idx + 1}行：起始价格不能小于前一行结束价格")

            previous_end_price = max_cost_price
            cleaned_data.append(
                {
                    "config_id": config_id,
                    "min_cost_price": min_cost_price,
                    "max_cost_price": max_cost_price,
                    "min_purchase_num": min_purchase_num,
                    "max_purchase_num": max_purchase_num,
                    "markup_percentage": markup_percentage,
                }
            )
        return cleaned_data

    def get(self, request: Request):
        configs = DistributorMarketProductPriceConfig.objects.filter(is_deleted=False)
        ser = OPDistributorMarketProductPriceConfigListSer(instance=configs, many=True)
        return IResponse(data=ser.data)

    def put(self, request: Request):
        post_data = request.data.get("data")
        if not post_data or not isinstance(post_data, list):
            raise APIViewException(err_message="invalid params")

        exist_id_list = [data.get("id") for data in post_data if data.get("id")]

        exist_configs = DistributorMarketProductPriceConfig.objects.filter(is_deleted=False, pk__in=exist_id_list)
        exist_config_map = {config.pk: config for config in exist_configs}

        cleand_data = self._validate_config_post_data(post_data, exist_config_map)

        # 需要更新商品的配置id
        no_need_delete_pk_list = []

        operation_log_list = []
        with transaction.atomic():
            for data in cleand_data:
                config_id = data.get("config_id")

                min_cost_price = data["min_cost_price"]
                max_cost_price = data["max_cost_price"]
                min_purchase_num = data["min_purchase_num"]
                max_purchase_num = data["max_purchase_num"]
                markup_percentage = data["markup_percentage"]

                if config_id:
                    # 更新配置
                    int_config_id = int(config_id)
                    tmp_config = exist_config_map[int_config_id]

                    # 旧数据
                    original_obj = copy.deepcopy(tmp_config)
                    tmp_config.min_cost_price = min_cost_price
                    tmp_config.max_cost_price = max_cost_price
                    tmp_config.min_purchase_num = min_purchase_num
                    tmp_config.max_purchase_num = max_purchase_num
                    tmp_config.markup_percentage = markup_percentage
                    tmp_config.update_user = self.current_user.user_id
                    tmp_config.save()

                    # 操作日志
                    content = diff_models(original_obj, tmp_config)
                    if content:
                        operation_log_list.append(f"更新配置{tmp_config}：{content}")

                    no_need_delete_pk_list.append(tmp_config.pk)
                else:
                    c = DistributorMarketProductPriceConfig.objects.create(
                        min_cost_price=min_cost_price,
                        max_cost_price=max_cost_price,
                        min_purchase_num=min_purchase_num,
                        max_purchase_num=max_purchase_num,
                        markup_percentage=markup_percentage,
                        create_user=self.current_user.user_id,
                    )
                    operation_log_list.append(f"新增配置{c}")

                    no_need_delete_pk_list.append(c.id)

            if no_need_delete_pk_list:
                configs = DistributorMarketProductPriceConfig.objects.filter(is_deleted=False).exclude(pk__in=no_need_delete_pk_list)

                for config in configs:
                    operation_log_list.append(f"删除配置{config}")
                    config.update_user = self.current_user.user_id
                    config.is_deleted = True
                    config.save()

        # 日志记录
        try:
            operate_log = ""
            if operation_log_list:
                operate_log = "\n" + "、".join(operation_log_list)

            if operate_log:
                set_log_params(
                    request,
                    model=DistributorMarketProductPriceConfig,
                    describe=f"更新分销价格配置",
                    operate_content=operate_log,
                    is_success_input=True,
                )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()
