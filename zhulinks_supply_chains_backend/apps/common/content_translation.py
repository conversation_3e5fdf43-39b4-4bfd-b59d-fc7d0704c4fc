from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from utils.http_handle import IResponse
from utils.json_response import JsonResponse
from utils.vectorvein_ai import VectorveinClient


class ContentTranslationView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = request.data
        translation_list = data.get("translation_list", {})
        content_translation_list = {}

        if not isinstance(translation_list, dict):
            return JsonResponse(message="翻译信息有误", status=400)

        vectorvein_client = VectorveinClient()
        for key, value in translation_list.items():
            try:
                resp = vectorvein_client.content_translation(value)
                translated_data = resp.get("data", [])
                if translated_data:
                    content_translation_list[key] = translated_data[0].get("value", "")
                else:
                    content_translation_list[key] = None
            except Exception as e:
                content_translation_list[key] = f"Error: {str(e)}"

        return JsonResponse(data=content_translation_list, status=200)

