# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-05-26 15:57:23
# @Last Modified by:   <PERSON><PERSON> <PERSON>
# @Last Modified time: 2024-05-27 17:29:19
from rest_framework import permissions
from rest_framework.permissions import IsAuthenticated

from common.basics.exceptions import APIViewException


class OrPermissions(permissions.BasePermission):
    def __init__(self, *permissions):
        self.permissions = permissions

    def has_permission(self, request, view):
        return any([perm().has_permission(request, view) for perm in self.permissions])

    def has_object_permission(self, request, view, obj):
        return any(perm().has_object_permission(request, view, obj) for perm in self.permissions)

    def __call__(self, *args, **kwargs):
        return self


class HasValidApiKey(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method == "OPTIONS":
            return True

        api_key = request.META.get("HTTP_ZHULINKS_API_KEY")
        if not api_key:
            return False
        user_app = getattr(request, "user_app", None)
        if not user_app:
            return False
        request.user = user_app.user
        return True


class HasAdminApiKey(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method == "OPTIONS":
            return True

        api_key = request.META.get("HTTP_ZHULINKS_API_KEY")
        if not api_key:
            return False
        user_app = getattr(request, "user_app", None)
        if not user_app:
            return False
        if not user_app.user.is_staff:
            return False

        request.user = user_app.user
        return True


class OPPermissions(IsAuthenticated):
    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            return False

        if request.auth.get("user_type") != "OP":
            raise APIViewException(code=403, err_message="no permission to operate")
        return True
