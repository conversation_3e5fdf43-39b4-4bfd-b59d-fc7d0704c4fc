# -*- coding: utf-8 -*-
import warnings
from datetime import datetime, time as dt_time
from datetime import timedelta
from functools import partial

import django_filters
from django.conf import settings
from django.db.models import Q
from django.utils import timezone
from django.utils.dateparse import parse_date, parse_datetime
from django_filters import Filter

from common import logger
from common.basics.exceptions import ParseException, APIViewException

IContainCharField = partial(django_filters.CharFilter, lookup_expr="icontains")


class DateFilter(django_filters.CharFilter):
    """
    查询当月的数据
    日期类筛选字段
    前端传递的信息 2024-01-31
    字符串
    """

    def __init__(self, *args, **kwargs):
        self.is_required = kwargs.pop("required", False)
        super().__init__(*args, **kwargs)

    def filter(self, qs, value):
        name = self.field_name

        if self.is_required and not value:
            raise APIViewException(err_message="日期是必填参数")

        try:
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", category=RuntimeWarning)
                in_value = datetime.strptime(value, "%Y-%m")

                if not in_value:
                    return self.get_method(qs)()

                first_day_of_month = in_value.replace(day=1)
                month_start = timezone.make_aware(datetime.combine(first_day_of_month, datetime.min.time()))
                next_month = first_day_of_month + timedelta(days=32)
                first_day_of_next_month = next_month.replace(day=1)
                month_end = timezone.make_aware(datetime.combine(first_day_of_next_month, datetime.min.time()))
                conditions = {f"{name}__gte": month_start, f"{name}__lt": month_end}
                return self.get_method(qs)(**conditions)
        except Exception as e:
            logger.warning(f"日期格式:{e}")
            raise ParseException(err_message="日期格式错误")


class DateTimeMinToMaxFilter(Filter):
    """
    create_date = DateTimeRangeFilter(field_name="create_time")
    定义即可，不用显式加入Meta fields
    """

    def filter(self, queryset, value):
        # 获取字段名
        field_name = self.field_name
        # 检查是否有 _min 参数
        min_value = self.parent.data.get(f"{field_name}_min")
        if min_value:
            queryset = queryset.filter(**{f"{field_name}__gte": min_value})

        # 检查是否有 _max 参数
        max_value = self.parent.data.get(f"{field_name}_max")
        if max_value:
            queryset = queryset.filter(**{f"{field_name}__lte": max_value})

        return queryset


class MultipleDateTimeRangeFilter(Filter):
    def __init__(self, *args, **kwargs):
        self._min_field_name = kwargs.pop("min_field_name", None)
        self._max_field_name = kwargs.pop("max_field_name", None)

        if not self._min_field_name or not self._max_field_name:
            raise ValueError("Must defined min_field_name and max_field_name")

        super().__init__(*args, **kwargs)

    def filter(self, qs, value):
        # 获取字段名
        field_name = self.field_name
        min_value = self.parent.data.get(f"{field_name}_min")
        max_value = self.parent.data.get(f"{field_name}_max")

        if not min_value and not max_value:
            return qs

        if not min_value and max_value is not None:
            return qs.filter(**{f"{self._min_field_name}__lte": max_value})
        elif min_value is not None and max_value is None:
            return qs.filter(**{f"{self._max_field_name}__gte": min_value})
        else:
            or_q = Q()
            or_q.add(Q(**{f"{self._min_field_name}__range": (min_value, max_value)}), "OR")
            or_q.add(Q(**{f"{self._max_field_name}__range": (min_value, max_value)}), "OR")
            or_q.add(
                Q(
                    Q(**{f"{self._min_field_name}__lte": min_value}) & Q(**{f"{self._max_field_name}__gte": max_value}),
                ),
                "OR",
            )

            return qs.filter(or_q)


class DateTimeStartToEndFilter(Filter):
    """
    增强版时间范围过滤器：
    - 自动处理日期相同情况下的时间范围扩展
    - 支持时区感知（兼容USE_TZ配置）
    - 示例：create_date = DateTimeStartToEndFilter(field_name="create_time")
    """

    def filter(self, queryset, value):
        field_name = self.field_name
        filter_field = None
        for k, v in self.parent.filters.items():
            if v == self:
                filter_field = k
                break


        min_value = self.parent.data.get(f"{filter_field}_start")
        max_value = self.parent.data.get(f"{filter_field}_end")

        # 解析起始时间
        if min_value:
            min_dt = self._parse_datetime(min_value, dt_time.min)
            if min_dt:
                queryset = queryset.filter(**{f"{field_name}__gte": min_dt})

        # 解析结束时间并处理相同日期
        if max_value:
            max_dt = self._parse_datetime(max_value, dt_time.max)
            if max_dt and min_value:
                # 检测日期部分是否相同
                min_dt_parsed = self._parse_datetime(min_value, dt_time.min)
                if min_dt_parsed and min_dt_parsed.date() == max_dt.date():
                    max_dt = datetime.combine(max_dt.date(), dt_time.max)
                    if timezone.is_naive(max_dt) and settings.USE_TZ:
                        max_dt = timezone.make_aware(max_dt)
            if max_dt:
                queryset = queryset.filter(**{f"{field_name}__lte": max_dt})

        return queryset

    def _parse_datetime(self, value, default_time):
        """统一解析日期/时间字符串为时区敏感的datetime对象"""
        dt = parse_datetime(value)
        if not dt:
            date_obj = parse_date(value)
            if date_obj:
                dt = datetime.combine(date_obj, default_time)
        if dt and timezone.is_naive(dt) and settings.USE_TZ:
            dt = timezone.make_aware(dt)
        return dt


class CSVFilter(Filter):
    # 前端传来 1,2,3,4
    def filter(self, qs, value):
        if not value:
            return qs

        # 分割逗号参数并去重
        values = list(set(value.split(",")))

        # 构建 Q 对象实现 OR 逻辑
        q_objects = Q()
        for val in values:
            if self.lookup_expr == "in":
                # 直接使用 __in 查询
                return qs.filter(**{self.field_name + "__in": values})
            else:
                q_objects |= Q(**{self.field_name: val})

        return qs.filter(q_objects).distinct()
