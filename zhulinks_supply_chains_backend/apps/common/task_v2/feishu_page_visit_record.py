import time
from datetime import datetime

from django.conf import settings

from products import logger
from utils.feishu import FeiShuDocx
from zhulinks_supply_chains_backend.celery import app


@app.task(queue="common_tasks")
def feishu_page_visit_record():
    """
    数据埋点同步到多维表格
    """
    from common.models_v2 import PageVisitRecord

    # 获取配置
    feishu_page_visit_record_table_id = getattr(settings, "FEISHU_PAGE_VISIT_RECORD_TABLE_ID", None)
    feishu_page_visit_record_table_token = getattr(settings, "FEISHU_PAGE_VISIT_RECORD_TABLE_TOKEN", None)
    if not feishu_page_visit_record_table_id or not feishu_page_visit_record_table_token:
        logger.error("埋点数据推送缺少配置信息")
        return

    # 初始化 Feishu 客户端
    feishu_client = FeiShuDocx(
        table_id=feishu_page_visit_record_table_id,
        table_app_token=feishu_page_visit_record_table_token,
    )
    records = {"records": []}
    all_data = PageVisitRecord.objects.filter(is_send=False)
    for data in all_data:
        page_url = data.page_url
        url_name = data.url_name
        event = data.event
        product_id = str(data.product_id) if data.product_id else " "
        product_name = data.product_name if data.product_name else " "
        visit_count = int(data.visit_count)
        user_id = str(data.user.user_id)
        user_name = data.user_name
        user_phone = data.user_phone
        stay_duration = float(data.stay_duration)  # 将 Decimal 转换为 float
        visit_start_time = data.visit_start_time.isoformat() if data.visit_start_time else None  # 转为 ISO 格式字符串
        visit_end_time = data.visit_end_time.isoformat() if data.visit_end_time else None
        record = create_page_visit_record(page_url, url_name, event, product_id, product_name, visit_count, user_id, user_name, user_phone, stay_duration, visit_start_time, visit_end_time)
        records["records"].append({"fields": record})
    if records["records"]:
        try:
            feishu_client.table_record_insert_dict(records)
            all_data.update(is_send=True)
            logger.info(f"成功同步 {len(records['records'])} 条记录到飞书多维表格")
        except Exception as e:
            logger.error(f"同步记录到飞书多维表格失败：{str(e)}")


def convert_to_timestamp(date_str: str) -> int:
    """
    将日期字符串转换为毫秒级时间戳
    """
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        return int(time.mktime(date_obj.timetuple())) * 1000
    except ValueError as e:
        logger.error(f"日期格式错误：{date_str}，错误信息：{str(e)}")
        return 0


def create_page_visit_record(
    page_url,
    url_name,
    event,
    product_id,
    product_name,
    visit_count,
    user_id,
    user_name,
    user_phone,
    stay_duration,
    visit_start_time,
    visit_end_time,
) -> dict:
    """
    创建单个商品的记录
    """
    return {
        "页面URL": page_url,
        "页面名称": url_name,
        "页面/按钮": event,
        "商品ID": product_id,
        "商品名称": product_name,
        "访问次数": visit_count,
        "访问用户ID": user_id,
        "用户姓名": user_name,
        "用户手机号": user_phone,
        "停留时长s": stay_duration,
        "访问初始时间": to_unix_timestamp(visit_start_time),
        "访问结束时间": to_unix_timestamp(visit_end_time),
    }


def to_unix_timestamp(value):
    if isinstance(value, str):
        try:
            # 尝试解析 ISO 格式字符串为 datetime 对象
            value = datetime.fromisoformat(value)
        except ValueError:
            raise ValueError(f"Invalid datetime string format: {value}")
    if isinstance(value, datetime):
        return int(value.timestamp() * 1000)  # 转为毫秒级 Unix 时间戳
    return None
