# Generated by Django 4.2.1 on 2023-10-18 10:40

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("common", "0004_operationlog"),
    ]

    operations = [
        migrations.RenameField(
            model_name="operationlog",
            old_name="resource_chinese_name",
            new_name="resource_cn_name",
        ),
        migrations.AddField(
            model_name="operationlog",
            name="content",
            field=models.TextField(blank=True, null=True, verbose_name="内容"),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="describe",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="描述"
            ),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="original_content",
            field=models.TextField(blank=True, null=True, verbose_name="原内容"),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="original_sku_cost_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="原商品sku成本价",
            ),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="original_sku_physical_inventory",
            field=models.BigIntegerField(
                blank=True, null=True, verbose_name="原商品sku现货库存"
            ),
        ),
    ]
