# Generated by Django 5.0.8 on 2024-10-22 02:00

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0045_aisearchrecords_query_times"),
        ("products", "0319_subproduct_is_in_distributor_market"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AISelectionPlanSearchRecords",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "input_platform",
                    models.CharField(
                        choices=[("OP", "运营商端"), ("DB", "分销商端")],
                        default="OP",
                        max_length=2,
                        verbose_name="输入平台",
                    ),
                ),
                (
                    "total_cost_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="总成本",
                    ),
                ),
                (
                    "total_spu_count",
                    models.IntegerField(
                        blank=True, default=0, null=True, verbose_name="SPU总数量"
                    ),
                ),
                (
                    "total_estimated_sales",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="预估总销量",
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=128,
                        null=True,
                        verbose_name="直播地址",
                    ),
                ),
                ("execute_sql", models.TextField(verbose_name="执行的sql")),
                (
                    "ticket",
                    models.CharField(
                        blank=True,
                        default=uuid.uuid4,
                        max_length=64,
                        null=True,
                        verbose_name="生成的票据",
                    ),
                ),
                (
                    "result",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="查询结果"
                    ),
                ),
                (
                    "query_times",
                    models.PositiveSmallIntegerField(
                        default=1, verbose_name="查询该输入次数"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "plan",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.productselectionplan",
                        to_field="plan_id",
                        verbose_name="货盘计划",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        to_field="user_id",
                        verbose_name="搜索用户",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="AISelectionPlanSearchRecordsDetail",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("category_id_list", models.JSONField(verbose_name="分类列表")),
                (
                    "styles",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="款式列表"
                    ),
                ),
                (
                    "sales_price_range",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="售价范围列表"
                    ),
                ),
                (
                    "refund_rate_range",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="退费率范围"
                    ),
                ),
                (
                    "latest_7_days_sales_range",
                    models.JSONField(
                        blank=True,
                        default=list,
                        null=True,
                        verbose_name="近7日销量范围列表",
                    ),
                ),
                (
                    "latest_30_days_sales_range",
                    models.JSONField(
                        blank=True,
                        default=list,
                        null=True,
                        verbose_name="近30日销量范围列表",
                    ),
                ),
                (
                    "labels",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="标签列表"
                    ),
                ),
                (
                    "record",
                    models.OneToOneField(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="common.aiselectionplansearchrecords",
                        verbose_name="所属记录",
                    ),
                ),
            ],
            options={
                "verbose_name": "智能货盘搜索记录明细",
                "verbose_name_plural": "智能货盘搜索记录明细",
            },
        ),
    ]
