# Generated by Django 4.2.7 on 2024-02-19 03:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("companies", "0032_stall_is_deleted"),
        ("common", "0016_alter_operationlog_content"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="operationlog",
            options={
                "ordering": ("-operation_time",),
                "verbose_name": "操作日志",
                "verbose_name_plural": "操作日志",
            },
        ),
        migrations.AddField(
            model_name="operationlog",
            name="company",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="companies.company",
                to_field="company_id",
                verbose_name="所属公司",
            ),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="distributor",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="companies.distributor",
                to_field="distributor_id",
                verbose_name="所属分销商",
            ),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="mobile",
            field=models.CharField(default="", max_length=20, verbose_name="手机号"),
            preserve_default=False,
        ),
    ]
