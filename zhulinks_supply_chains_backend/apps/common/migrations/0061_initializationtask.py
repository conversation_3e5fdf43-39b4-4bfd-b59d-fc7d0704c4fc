# Generated by Django 5.1.2 on 2024-12-13 07:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0060_merge_20241212_1708"),
    ]

    operations = [
        migrations.CreateModel(
            name="InitializationTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="任务名称")),
                (
                    "module_path",
                    models.Char<PERSON>ield(max_length=255, verbose_name="模块路径"),
                ),
                (
                    "function_name",
                    models.Char<PERSON>ield(max_length=255, verbose_name="函数名称"),
                ),
                (
                    "arguments",
                    models.JSONField(blank=True, null=True, verbose_name="参数"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "初始化任务",
                "verbose_name_plural": "初始化任务",
            },
        ),
    ]
