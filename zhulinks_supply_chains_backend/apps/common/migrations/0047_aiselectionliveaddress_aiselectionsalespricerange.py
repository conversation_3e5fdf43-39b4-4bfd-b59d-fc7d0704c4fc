# Generated by Django 5.0.8 on 2024-10-22 06:22

from django.db import migrations, models


def init_base_selection_data(*args, **kwargs):
    from common.models import AISelectionLiveAddress, AISelectionSalesPriceRange

    live_address = ["可塘", "华林E馆", "华林C馆", "诸暨", "其他"]
    AISelectionLiveAddress.objects.bulk_create([AISelectionLiveAddress(name=address) for address in live_address])

    price_range = (
        (0, 50),
        (50, 300),
        (300, 500),
        (500, 1000),
        (1000, None),
    )

    AISelectionSalesPriceRange.objects.bulk_create([AISelectionSalesPriceRange(min_price=i[0], max_price=i[1]) for i in price_range])


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0046_aiselectionplansearchrecords_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AISelectionLiveAddress",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, verbose_name="名称")),
                (
                    "order",
                    models.PositiveSmallIntegerField(default=0, help_text="排序(数字越大排越后)", verbose_name="排序"),
                ),
            ],
            options={
                "verbose_name": "智能选品-直播地点",
                "verbose_name_plural": "智能选品-直播地点",
            },
        ),
        migrations.CreateModel(
            name="AISelectionSalesPriceRange",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("min_price", models.IntegerField(default=0, verbose_name="最小售价")),
                (
                    "max_price",
                    models.IntegerField(blank=True, default=None, null=True, verbose_name="最大售价"),
                ),
            ],
            options={
                "verbose_name": "智能选品-售价范围",
                "verbose_name_plural": "智能选品-售价范围",
            },
        ),
        migrations.RunPython(init_base_selection_data),
    ]
