# Generated by Django 4.2.7 on 2024-02-05 05:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("common", "0014_alter_operationlog_describe"),
    ]

    operations = [
        migrations.AddField(
            model_name="operationlog",
            name="content_type",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="contenttypes.contenttype",
                verbose_name="操作对象",
            ),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="fronted_page",
            field=models.CharField(
                blank=True, default="", max_length=128, null=True, verbose_name="操作页面"
            ),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="operate_content",
            field=models.TextField(blank=True, null=True, verbose_name="操作具体内容"),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="platform",
            field=models.CharField(
                blank=True, default="", max_length=12, null=True, verbose_name="操作平台"
            ),
        ),
        migrations.AddField(
            model_name="operationlog",
            name="remark",
            field=models.TextField(blank=True, null=True, verbose_name="备注内容"),
        ),
    ]
