# Generated by Django 4.2.1 on 2023-10-09 05:29

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("common", "0002_alter_area_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="LiveAuthor",
            fields=[
                (
                    "author_id",
                    models.CharField(
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                        verbose_name="主播id",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="主播"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "内部主播管理",
                "verbose_name_plural": "内部主播管理",
            },
        ),
    ]
