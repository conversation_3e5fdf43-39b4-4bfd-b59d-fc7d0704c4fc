# Generated by Django 5.0.8 on 2024-08-29 06:20

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0035_alter_downloadtasks_download_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="systemconfig",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="systemconfig",
            name="create_user",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=32,
                null=True,
                verbose_name="创建用户",
            ),
        ),
        migrations.AddField(
            model_name="systemconfig",
            name="update_date",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
        migrations.AddField(
            model_name="systemconfig",
            name="update_user",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=32,
                null=True,
                verbose_name="更新用户",
            ),
        ),
    ]
