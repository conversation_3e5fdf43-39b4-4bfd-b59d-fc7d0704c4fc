# Generated by Django 5.0.8 on 2024-10-14 09:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0038_alter_systemprotocol_key"),
    ]

    operations = [
        migrations.AlterField(
            model_name="systemprotocol",
            name="key",
            field=models.CharField(
                choices=[
                    ("SELF_DB_USER_AGREEMENT", "自营分销商-用户协议"),
                    ("DB_USER_AGREEMENT", "分销商-用户协议"),
                    ("OP_USER_AGREEMENT", "运营商-用户协议"),
                    ("SP_USER_AGREEMENT", "供应商-用户协议"),
                    ("SELF_DB_PRIVACY_AGREEMENT", "自营分销商-隐私协议"),
                    ("DB_PRIVACY_AGREEMENT", "分销商-隐私协议"),
                    ("OP_PRIVACY_AGREEMENT", "运营商-隐私协议"),
                    ("SP_PRIVACY_AGREEMENT", "供应商-隐私协议"),
                    ("AFTER_SALES_PROTOCOLS_KEY", "自营分销商-内购须知"),
                    ("DBM2_AFTER_SALES_PROTOCOLS_KEY", "分销模式-内购须知"),
                    ("BASIC_REVIEW_GUIDE", "基础审核指引"),
                    ("PRICE_REVIEW_GUIDE", "价格审核指引"),
                    ("QA_REVIEW_GUIDE", "质检审核指引"),
                ],
                default="",
                max_length=32,
                verbose_name="类型",
            ),
        ),
    ]
