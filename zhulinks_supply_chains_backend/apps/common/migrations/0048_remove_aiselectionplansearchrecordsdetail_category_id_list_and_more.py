# Generated by Django 5.0.8 on 2024-10-22 11:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0047_aiselectionliveaddress_aiselectionsalespricerange"),
        ("products", "0320_auto_20241022_1447"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="aiselectionplansearchrecordsdetail",
            name="category_id_list",
        ),
        migrations.AddField(
            model_name="aiselectionplansearchrecordsdetail",
            name="category_id",
            field=models.ForeignKey(
                db_constraint=False,
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                to="products.productcategorylist",
                verbose_name="分类ID",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="aiselectionplansearchrecordsdetail",
            name="category_name",
            field=models.CharField(
                blank=True, default="", null=True, verbose_name="分类名称"
            ),
        ),
    ]
