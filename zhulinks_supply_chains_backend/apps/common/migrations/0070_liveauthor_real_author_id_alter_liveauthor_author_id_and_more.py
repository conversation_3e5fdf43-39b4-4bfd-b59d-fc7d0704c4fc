# Generated by Django 5.0.8 on 2025-01-13 07:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("common", "0069_alter_liveauthor_options_liveauthor_live_stream_id_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="liveauthor",
            name="real_author_id",
            field=models.CharField(
                blank=True,
                default="",
                max_length=255,
                null=True,
                verbose_name="真实达人ID",
            ),
        ),
        migrations.AlterField(
            model_name="liveauthor",
            name="author_id",
            field=models.CharField(
                max_length=50,
                primary_key=True,
                serialize=False,
                verbose_name="达人信息",
            ),
        ),
        migrations.AlterField(
            model_name="orderpreprocessingrule",
            name="rule_type",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, "商品名称包含关键字"),
                    (2, "商编匹配"),
                    (3, "分销商货号关键字母"),
                    (4, "达人ID关键字"),
                    (5, "直播间ID关键字"),
                ],
                unique=True,
                verbose_name="规则类型",
            ),
        ),
    ]
