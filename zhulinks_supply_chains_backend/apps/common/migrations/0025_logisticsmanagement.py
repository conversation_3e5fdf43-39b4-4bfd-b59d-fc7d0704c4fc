# Generated by Django 5.0.7 on 2024-07-17 02:38

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("common", "0024_alter_contact_order"),
    ]

    operations = [
        migrations.CreateModel(
            name="LogisticsManagement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "logistics_id",
                    models.CharField(default="", max_length=100, verbose_name="物流编号"),
                ),
                (
                    "logistics_name",
                    models.CharField(default="", max_length=100, verbose_name="物流名称"),
                ),
                (
                    "type",
                    models.IntegerField(
                        choices=[("1", "快递"), ("2", "物流")], default=1, verbose_name="类型"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "物流管理",
                "verbose_name_plural": "物流管理",
                "db_table": "logistics_management",
            },
        ),
    ]
