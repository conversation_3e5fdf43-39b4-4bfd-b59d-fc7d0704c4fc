# -*- coding: utf-8 -*-
import os.path
import string
import uuid
from hashlib import md5

from common import logger
from common.storages import OssStorage
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.db import models
from django.db.models import Manager, Q, UniqueConstraint
from django.utils.crypto import get_random_string
from tinymce.models import HTMLField
from utils.aliyun_functions import generate_sts_credential
from utils.aliyun_oss import delete_object, upload_file_with_bytes
from utils.redis_lock import redis_cache


class AreaManager(Manager):
    def get_parents_by_id_list(self, id_list: list):
        if not id_list:
            raise ValueError("id_list cannot be empty list.")
        raw_sql = """WITH RECURSIVE Path AS (SELECT *
                        FROM common_area t
                        WHERE t.id in (%s)
                        UNION ALL
                        SELECT t1.*
                        FROM common_area t1
                                 JOIN Path p ON t1.id = p.parent_id)
SELECT *
FROM Path;"""

        my_list_str = ",".join(map(str, id_list))
        return self.raw(raw_sql % my_list_str)

    def get_parents_dict_by_id_list(self, id_list: list):
        """
        查询多个id的父级，并处理成字段形式和对应的父级
        :param id_list: 需要查询父级的id列表
        :return: id所对应的列表
        {
            110106: [
                {"id": 110000, "name": "北京", "parent_id": None},
                {"id": 110100, "name": "北京市", "parent_id": 110000},
                {"id": 110106, "name": "丰台区", "parent_id": 110100},
            ]
        }

        """
        results = self.get_parents_by_id_list(id_list)
        regions = [{"id": result.id, "name": result.name, "parent_id": result.parent_id} for result in results]

        region_dict = {region["id"]: region for region in regions}

        # 递归函数来查找所有父级
        def find_ancestors(region_id, region_dict):
            ancestors = []
            while region_id is not None:
                region = region_dict.get(region_id)
                if region:
                    ancestors.append(region)
                    region_id = region["parent_id"]
                else:
                    break
            return ancestors[::-1]

        re_data = {target_id: find_ancestors(target_id, region_dict) for target_id in id_list}
        return re_data


class Area(models.Model):
    name = models.CharField("名称", max_length=50)
    parent = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        related_name="subs",
        null=True,
        blank=True,
        verbose_name="上级行政区划",
    )

    objects = AreaManager()

    class Meta:
        verbose_name = "行政区划"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name

    def get_all_parents(self):
        """递归获取所有父级区域"""
        parents = []
        parent = self.parent
        while parent:
            parents.append(parent.id)
            parent = parent.parent
        return parents


class LiveAuthor(models.Model):
    # 关联了其他表，为了不改动这里，新起一个字段使用
    author_id = models.CharField("达人主键", max_length=50, primary_key=True, editable=False)
    # 订单更新 real_author_id
    real_author_id = models.CharField("真实达人ID", max_length=255, null=True, blank=True, default="")
    name = models.CharField("达人名称", max_length=100, blank=True, null=True)
    live_room_ids = models.JSONField("直播间ID列表", null=True, blank=True, default=list)
    code = models.CharField("编码", max_length=10, blank=True, null=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "达人管理"
        verbose_name_plural = verbose_name

    def save(self, *args, **kwargs):
        if not self.author_id:
            self.author_id = auth_code_generator()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name}({self.author_id})"


def auth_code_generator():
    _tmp_author_id = get_random_string(17, allowed_chars=string.digits)
    for i in range(100):
        if not LiveAuthor.objects.filter(author_id=_tmp_author_id).exists():
            return _tmp_author_id
        _tmp_author_id = get_random_string(17, allowed_chars=string.digits)
    else:
        raise ValueError("cannot generate auth code in 100 times")


class OperationLog(models.Model):
    ACTION_TYPE_CHOICES = (
        ("GET", "查询"),
        ("POST", "新增"),
        ("PUT", "修改"),
        ("PATCH", "修改"),
        ("DELETE", "删除"),
    )
    username = models.CharField("用户名", max_length=150, blank=False, null=False)
    user_id = models.BigIntegerField("用户id", blank=True, null=True)
    real_name = models.CharField("真实姓名", max_length=100, blank=True, null=True)
    mobile = models.CharField(verbose_name="手机号", max_length=20, blank=False, null=False)
    action_type = models.CharField("操作类型", choices=ACTION_TYPE_CHOICES, max_length=7, blank=False, null=False)
    # 操作平台, OP/DB/SP 运营商、分销商、供应商
    platform = models.CharField(verbose_name="操作平台", max_length=12, null=True, blank=True, default="")
    url_path = models.CharField("资源路径", max_length=120, blank=False, null=False)
    resource_name = models.CharField("资源名称", max_length=56, blank=True, null=True)
    # resource_cn_name = models.CharField("资源中文名称", max_length=56, blank=True, null=True)
    ############## 废弃字段
    parent_resource_id = models.CharField("父级资源ID", max_length=56, blank=True, null=True)
    ###############
    resource_id = models.CharField("资源ID", max_length=56, blank=True, null=True)
    content_type = models.ForeignKey(
        ContentType,
        models.SET_NULL,
        verbose_name="操作对象",
        blank=True,
        null=True,
        default=None,
    )
    ############# 废弃字段
    sku_cost_price = models.DecimalField("商品sku成本价", max_digits=10, decimal_places=2, blank=True, null=True)
    original_sku_cost_price = models.DecimalField("原商品sku成本价", max_digits=10, decimal_places=2, blank=True, null=True)
    sku_physical_inventory = models.BigIntegerField("商品sku现货库存", blank=True, null=True)
    original_sku_physical_inventory = models.BigIntegerField("原商品sku现货库存", blank=True, null=True)
    ############# 废弃字段

    describe = models.TextField("描述", blank=True, null=True)
    # content记录请求数据, 开发者使用
    content = models.TextField("请求内容", blank=True, null=True)
    original_content = models.TextField("原内容", blank=True, null=True)
    request_id = models.CharField("请求id", max_length=36)
    # 用于列表
    fronted_page = models.CharField(verbose_name="操作页面", max_length=128, null=True, blank=True, default="")
    operate_content = models.TextField(verbose_name="操作具体内容", blank=True, null=True)
    remark = models.TextField(verbose_name="备注内容", blank=True, null=True)
    # 所属 区分日志
    distributor = models.ForeignKey(
        "companies.Distributor",
        to_field="distributor_id",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        default=None,
        verbose_name="所属分销商",
    )
    company = models.ForeignKey(
        "companies.Company",
        to_field="company_id",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        default=None,
        verbose_name="所属公司",
    )

    operation_time = models.DateTimeField("操作时间")
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "操作日志"
        verbose_name_plural = verbose_name
        ordering = ("-operation_time",)
        indexes = [
            models.Index(
                name="action_type_url_path",
                fields=["action_type", "url_path"],
            ),
            models.Index(
                name="resource_id_sku_cost_price",
                fields=["parent_resource_id", "resource_id", "sku_cost_price"],
            ),
            models.Index(
                name="resource_id_sku_inventory",
                fields=["parent_resource_id", "resource_id", "sku_physical_inventory"],
            ),
            models.Index(
                name="operation_time_action",
                fields=["operation_time", "resource_name", "action_type"],
            ),
            models.Index(
                name="resource_name_index",
                fields=[
                    "resource_name",
                ],
            ),
            models.Index(
                name="request_id_resource_id_index",
                fields=["request_id", "resource_id"],
            ),
        ]


class CustomerService(models.Model):
    name = models.CharField("客服名称", max_length=32, null=False, blank=False, unique=True)
    remark = models.TextField("备注信息", null=True, blank=True, default="")

    class Meta:
        verbose_name = "客服管理"
        verbose_name_plural = verbose_name


class SystemProtocolChoices(models.enums.TextChoices):
    # 用户协议
    _SELF_DB_USER_AGREEMENT = "SELF_DB_USER_AGREEMENT", "自营分销商-用户协议"
    _DB_USER_AGREEMENT = "DB_USER_AGREEMENT", "分销商-用户协议"
    _OP_USER_AGREEMENT = "OP_USER_AGREEMENT", "运营商-用户协议"
    _SP_USER_AGREEMENT = "SP_USER_AGREEMENT", "供应商-用户协议"
    # 隐私协议
    _SELF_DB_PRIVACY_AGREEMENT = "SELF_DB_PRIVACY_AGREEMENT", "自营分销商-隐私协议"
    _DB_PRIVACY_AGREEMENT = "DB_PRIVACY_AGREEMENT", "分销商-隐私协议"
    _OP_PRIVACY_AGREEMENT = "OP_PRIVACY_AGREEMENT", "运营商-隐私协议"
    _SP_PRIVACY_AGREEMENT = "SP_PRIVACY_AGREEMENT", "供应商-隐私协议"
    # 售后协议
    _AFTER_SALES_PROTOCOLS_KEY = "AFTER_SALES_PROTOCOLS_KEY", "自营分销商-内购须知"
    _DBM2_AFTER_SALES_PROTOCOLS_KEY = "DBM2_AFTER_SALES_PROTOCOLS_KEY", "分销模式-内购须知"
    _AFTER_SALES_SERVICES_KEY = "AFTER_SALES_SERVICES_KEY", "售后服务协议"
    # 审核缓存redis key值，对应model key便于清除缓存
    _BASIC_REVIEW_GUIDE = "BASIC_REVIEW_GUIDE", "基础审核指引"
    _PRICE_REVIEW_GUIDE = "PRICE_REVIEW_GUIDE", "价格审核指引"
    _QA_REVIEW_GUIDE = "QA_REVIEW_GUIDE", "质检审核指引"


PROTOCOL_KEYS_MAP = {
    "OP": [SystemProtocolChoices._OP_USER_AGREEMENT, SystemProtocolChoices._OP_PRIVACY_AGREEMENT],
    "SP": [SystemProtocolChoices._SP_USER_AGREEMENT, SystemProtocolChoices._SP_PRIVACY_AGREEMENT],
    "DB": [SystemProtocolChoices._DB_USER_AGREEMENT, SystemProtocolChoices._DB_PRIVACY_AGREEMENT],
    "SELF_DB": [SystemProtocolChoices._SELF_DB_USER_AGREEMENT, SystemProtocolChoices._SELF_DB_PRIVACY_AGREEMENT],
}


class SystemProtocol(models.Model):
    _CACHE_KEY = "SYS_PROTOCOLS"
    key = models.CharField(verbose_name="类型", max_length=32, default="", null=False, blank=False, choices=SystemProtocolChoices)
    name = models.CharField(verbose_name="协议名称", max_length=32, default="", null=False, blank=False)
    content = HTMLField(verbose_name="协议内容")
    display = models.BooleanField(verbose_name="是否显示", default=True)
    order = models.PositiveSmallIntegerField(verbose_name="排序", default=0, help_text="数字越小排得越前")
    remark = models.CharField("备注信息", max_length=50, null=True, blank=True, default="")
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_user = models.CharField(verbose_name="最后更新人", default="", null=True, blank=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "系统协议"
        verbose_name_plural = verbose_name
        ordering = ("order",)

    def __str__(self):
        return self.name + f"({self.id})"

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        super().save(force_insert, force_update, using, update_fields)

        for user_type, keys in PROTOCOL_KEYS_MAP.items():
            if self.key in keys:
                redis_cache.delete(user_type + self._CACHE_KEY)
        else:
            redis_cache.delete(self.key)

    @classmethod
    def get_cache_list(cls, user_type):
        _cache_key = user_type + cls._CACHE_KEY
        if user_type not in PROTOCOL_KEYS_MAP:
            return

        keys = PROTOCOL_KEYS_MAP[user_type]

        val = redis_cache.get(_cache_key)
        if val:
            return val

        protocols = SystemProtocol.objects.filter(key__in=keys).order_by("order").only("id", "name", "content")
        re_data = [
            {
                "id": protocol.id,
                "name": protocol.name,
                "content": protocol.content,
                "display": protocol.display,
                "order": protocol.order,
            }
            for protocol in protocols
        ]
        # 12小时缓存
        redis_cache.set(_cache_key, re_data, 60 * 60 * 12)
        return re_data

    @classmethod
    def get_after_sales_protocols_cache(cls, user_type: str = None):
        key_map = {
            "SELF_DB": SystemProtocolChoices._AFTER_SALES_PROTOCOLS_KEY,
            "DB_M2": SystemProtocolChoices._DBM2_AFTER_SALES_PROTOCOLS_KEY,
        }

        key = key_map[user_type]
        val = redis_cache.get(key)
        if val:
            return val

        protocols = SystemProtocol.objects.filter(key=key).order_by("order").only("id", "key", "name", "content")
        re_data = {}
        if protocols:
            protocol = protocols.first()
            re_data = {
                "id": protocol.id,
                "key": protocol.key,
                "name": protocol.name,
                "content": protocol.content,
                "display": protocol.display,
                "order": protocol.order,
            }

        # 12小时缓存
        redis_cache.set(key, re_data, 60 * 60 * 12)
        return re_data

    @classmethod
    def get_after_sale_service_cache(cls):
        key = SystemProtocolChoices._AFTER_SALES_SERVICES_KEY
        val = redis_cache.get(key)
        if val:
            return val

        services = SystemProtocol.objects.filter(key=key).order_by("order").only("id", "key", "name", "content")
        re_data = {}
        if services:
            service = services.first()
            re_data = {
                "id": service.id,
                "key": service.key,
                "name": service.name,
                "content": service.content,
                "display": service.display,
                "order": service.order,
            }

        # 12小时缓存
        redis_cache.set(key, re_data, 60 * 60 * 12)
        return re_data


def get_protocol_with_key(protocol_key) -> dict:
    """
    审核指引会使用
    :param protocol_key:
    :return:
    """
    val = redis_cache.get(protocol_key)
    if val is not None:
        return val

    protocol = SystemProtocol.objects.filter(key=protocol_key).order_by("order").only("id", "key", "name", "content").first()
    re_data = {}
    if protocol:
        re_data = {
            "id": protocol.id,
            "name": protocol.name,
            "content": protocol.content,
            "display": protocol.display,
            "order": protocol.order,
        }
        redis_cache.set(protocol_key, re_data, 60 * 60 * 12)
    return re_data


class SystemConfig(models.Model):
    PRODUCT_EXPIRE_CODE = "PRODUCT_EXPIRE"
    PRODUCT_HOSTING_CODE = "PRODUCT_HOSTING"
    JST_START_TS = "JST_START_TS"
    PLAN_GAP_DAYS = "PLAN_GAP_DAYS"
    # 售后开始ts
    JST_AS_START_TS = "JST_AS_TS"
    JST_ORDER_START_TS = "JST_ORDER_TS"
    # 抖店订单订阅的最大id
    DY_ORDER_START_ID = "DY_ORDER_START_ID"
    # 售后寄送地址
    AFTER_SALES_DEFAULT_ADDRESS = "AFTER_SALES_DEFAULT_ADDRESS"
    AFTER_SALES_REASON_TEXT = "AFTER_SALES_REASON_TEXT"
    # 飞书多维表格
    FEISHU_TABLE_CODE = "FEISHU_TABLE_CONF"
    FEISHU_ROBOTS_CONF = "FEISHU_ROBOTS_CONF"
    # 每人最大下载任务数量
    MAX_DOWNLOAD_TASKS = "MAX_DOWNLOAD_TASKS"

    # 库存预警设置
    STOCK_WARNING_CONF = "STOCK_WARNING_CONF"
    # 数据组项目域名
    DATA_ANALYZE_DOMAIN = "DATA_ANALYZE_DOMAIN"
    # 入库单-处理excel的大小，单位是Mb
    INBOUND_ORDER_EXCEL_MAX_CONTENT_LENGTH = "INBOUND_ORDER_EXCEL_MAX_LEN"
    #
    MAX_USER_NOTIFICATION_KEEP_DAYS = "MAX_USER_NOTIFICATION_KEEP_DAYS"

    name = models.CharField(verbose_name="配置名称", max_length=12)
    desc = models.CharField(verbose_name="配置简要描述", max_length=128, null=True, blank=True, default="")
    code = models.CharField(verbose_name="唯一代码", max_length=32, unique=True, null=False, blank=False)
    value = models.CharField(verbose_name="配置值", max_length=128, null=False, blank=False)
    extra = models.JSONField(verbose_name="额外配置", null=True, blank=True, default=dict)
    config_type = models.CharField(
        verbose_name="配置类型",
        max_length=12,
        choices=(
            ("sys", "系统"),
            ("others", "其他"),
        ),
        default="sys",
    )
    enable = models.BooleanField(verbose_name="是否启动", default=True)
    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True, db_index=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True, db_index=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "系统配置"
        verbose_name_plural = verbose_name
        constraints = [
            UniqueConstraint(
                fields=["code"],
                condition=Q(enable=False),
                name="unique_code_with_enable",
            )
        ]

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        super().save(force_insert, force_update, using, update_fields)

        if self.pk and self.code in (self.FEISHU_TABLE_CODE, self.MAX_DOWNLOAD_TASKS, self.FEISHU_ROBOTS_CONF):
            redis_cache.delete(f"system_cfg:{self.code}")
        else:
            # 保持即更新缓存
            config_info = {
                "code": self.code,
                "value": self.value,
                "name": self.name,
                "desc": self.desc,
                "extra": self.extra,
                "config_type": self.config_type,
            }
            redis_cache.set(self.code, config_info)

    @classmethod
    def get_value_by_code(cls, key):
        config_info = redis_cache.get(key)
        if config_info:
            return config_info
        try:
            config_obj = SystemConfig.objects.get(code=key)
        except SystemConfig.DoesNotExist:
            return {}
        config_info = {
            "code": config_obj.code,
            "value": config_obj.value,
            "name": config_obj.name,
            "desc": config_obj.desc,
            "extra": config_obj.extra,
            "config_type": config_obj.config_type,
        }
        redis_cache.set(key, config_info)
        return config_info


def get_product_expire_config() -> SystemConfig:
    """
    获取商品新品过期时间
    :return:
    """
    try:
        config = SystemConfig.objects.get(code=SystemConfig.PRODUCT_EXPIRE_CODE, enable=True)
    except SystemConfig.DoesNotExist:
        config = SystemConfig.objects.create(
            name="新商品过期时间",
            desc="新商品过期时间，默认10天",
            code=SystemConfig.PRODUCT_EXPIRE_CODE,
            value=10,
            enable=True,
        )

    if not config.value or not str(config.value).isdigit():
        config.value = 10
        config.save()

    return config


def get_product_hosting_config() -> SystemConfig:
    """
    获取商品托管时间
    :return:
    """
    try:
        config = SystemConfig.objects.get(code=SystemConfig.PRODUCT_HOSTING_CODE, enable=True)
    except SystemConfig.DoesNotExist:
        config = SystemConfig.objects.create(name="商品托管时间", desc="商品托管时间，默认10天", code=SystemConfig.PRODUCT_HOSTING_CODE, value=10)

    if not config.value or not str(config.value).isdigit():
        config.value = 10
        config.save()

    return config


def get_selection_plan_max_gap_days_config() -> SystemConfig:
    """
    获取货盘直播时间最大间隔天数
    后期可以用redis缓存
    :return:
    """
    try:
        config = SystemConfig.objects.get(code=SystemConfig.PLAN_GAP_DAYS, enable=True)
    except SystemConfig.DoesNotExist:
        config = SystemConfig.objects.create(name="货盘直播时间最大天数", desc="货盘直播时间最大天数，默认2天", code=SystemConfig.PLAN_GAP_DAYS, value=2)

    if not config.value or not str(config.value).isdigit():
        config.value = 2
        config.save()

    return config


def get_dy_order_fetcher_config() -> SystemConfig:
    try:
        config = SystemConfig.objects.get(code=SystemConfig.DY_ORDER_START_ID, enable=True)
    except SystemConfig.DoesNotExist:
        config = SystemConfig.objects.create(
            name="抖店订单拉取设置(勿动)",
            desc="记录抖店订单拉取的配置信息，非开发人员勿动",
            code=SystemConfig.DY_ORDER_START_ID,
            value=2,
        )
    return config


def get_feishu_table_config() -> dict:
    code = SystemConfig.FEISHU_TABLE_CODE
    key = f"system_cfg:{code}"
    val = redis_cache.get(key)
    if val:
        return val

    try:
        config = SystemConfig.objects.get(code=code, enable=True)
    except SystemConfig.DoesNotExist:
        config = SystemConfig.objects.create(
            name="飞书多维表格设置(勿动)",
            desc="飞书多维表格的配置信息，非开发人员勿动",
            code=code,
            value=0,
            extra={
                "zlzp_new_product": {"table_id": "tblHNx2z2SnjPPiZ", "table_token": "JxsmbjTquak6jXsF1JvcG5lkned"},
                "normal_new_product": {"table_id": "tblqOVPl2LtgmKmP", "table_token": "My98bgE8eaz4uqsLpRYcI0KQnrb"},
            },
        )
    re_data = {
        "value": config.value,
        "extra": config.extra,
    }
    redis_cache.set(key, re_data)
    return re_data


def get_feishu_robots_config() -> dict:
    """
    飞书机器人通知配置
    :return:
    """

    code = SystemConfig.FEISHU_ROBOTS_CONF
    key = f"system_cfg:{code}"
    val = redis_cache.get(key)
    if val:
        return val

    try:
        config = SystemConfig.objects.get(code=code, enable=True)
    except SystemConfig.DoesNotExist:
        config = SystemConfig.objects.create(
            name="飞书多维表格机器人设置",
            desc="飞书多维表格机器人的配置信息，非开发人员勿动",
            code=code,
            value=0,
            extra={"pic_score": "https://open.feishu.cn/open-apis/bot/v2/hook/ed7d4e11-f13b-429d-97ea-e79edeeccf43"},
        )
    re_data = {
        "value": config.value,
        "extra": config.extra,
    }
    redis_cache.set(key, re_data)
    return re_data


def get_robot_url(code):
    cfg = get_feishu_robots_config()
    extra = cfg["extra"] or {}
    return extra.get(code, "")


def get_feishu_table_config_info(code):
    """
    获取飞书多维表格信息

    code:
        shopping_car 购物车
        db_login_info 分销商登录信息
        erp_sales_order_sync erp销售订单同步

    :param code: 珠凌唯一code
    :return: table_id, table_token 或者 None/None
    """
    # 表格设置
    tbl_cfg = get_feishu_table_config()
    tbl_cfg_extra = tbl_cfg["extra"] or {}
    if code not in tbl_cfg_extra:
        return None, None
    try:
        table_id = tbl_cfg_extra[code]["table_id"]
        table_token = tbl_cfg_extra[code]["table_token"]

        return table_id, table_token
    except Exception as e:
        logger.warning(f"解析飞书多维表格错误: {e}")
        return None, None


def get_download_max_tasks_count_config() -> int:
    """
    用户最大下载数量
    :return:
    """

    code = SystemConfig.MAX_DOWNLOAD_TASKS
    key = f"system_cfg:{code}"
    val = redis_cache.get(key)
    if val:
        return val

    try:
        config = SystemConfig.objects.get(code=code, enable=True)
    except SystemConfig.DoesNotExist:
        config = SystemConfig.objects.create(
            name="用户最大下载文件数量",
            desc="用户最大下载文件数量, 注意最小为1",
            code=code,
            value=5,
        )
    value = int(config.value)
    redis_cache.set(key, int(value))
    return value


def get_inbound_excel_parse_config() -> int:
    """
    用户最大下载数量
    :return:
    """

    code = SystemConfig.INBOUND_ORDER_EXCEL_MAX_CONTENT_LENGTH
    key = f"system_cfg:{code}"
    val = redis_cache.get(key)
    if val:
        return val

    try:
        config = SystemConfig.objects.get(code=code, enable=True)
    except SystemConfig.DoesNotExist:
        config = SystemConfig.objects.create(
            name="入库单excel阈值",
            desc="入库单-处理excel的大小，单位是Mb, 默认是10Mb",
            code=code,
            value=10,
        )
    value = int(config.value)
    redis_cache.set(key, int(value))
    return value


def get_user_notification_max_keep_days() -> int:
    """
    用户最大下载数量
    :return:
    """

    code = SystemConfig.MAX_USER_NOTIFICATION_KEEP_DAYS
    key = f"system_cfg:{code}"
    val = redis_cache.get(key)
    if val:
        return val

    try:
        config = SystemConfig.objects.get(code=code, enable=True)
    except SystemConfig.DoesNotExist:
        config = SystemConfig.objects.create(
            name="用户消息已读记录保留天数",
            desc="用户消息已读记录保留天数，天数内的数据会根据status判断是否已读，超过天数的status记录会物理删除~， 默认7天",
            code=code,
            value=7,
        )
    value = int(config.value)
    redis_cache.set(key, int(value))
    return value


class Contact(models.Model):
    _base_path = "user-upload/contact/test/" if getattr(settings, "ENV") == "test" else "user-upload/contact/prod/"
    name = models.CharField("联系人姓名", max_length=64)
    img = models.ImageField("二维码图片", max_length=255, storage=OssStorage())
    order = models.PositiveSmallIntegerField("排序(数字越小排越前)", default=0)
    img_name = models.CharField("图片名称", max_length=255, editable=False)
    base_path = models.CharField("图片基本路径", max_length=255, editable=False)
    hash_code = models.CharField("哈希值", max_length=255, editable=False)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "联系人设置"
        verbose_name_plural = verbose_name

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        if not self.pk:
            if Contact.objects.count() >= 3:
                raise ValueError("联系人最大只能创建三个")

            _, extension = os.path.splitext(self.img.name)
            md5_hash = md5()
            for chunk in self.img.chunks():
                md5_hash.update(chunk)
            # 哈希值
            hash_code = md5_hash.hexdigest()
            image_md5_name = hash_code + extension.lower()

            aliyun_certs = generate_sts_credential(
                settings.ALIYUN_OSS_ROLE_ARN_ADD,
                str(uuid.uuid4()),
            )
            download_url = upload_file_with_bytes(
                aliyun_certs["access_key_id"],
                aliyun_certs["access_key_secret"],
                aliyun_certs["security_token"],
                self.img.file.file.getvalue(),
                image_md5_name,
                base_path=self._base_path,
            )
            self.img = download_url
            self.hash_code = hash_code
            self.base_path = self._base_path
            self.img_name = image_md5_name
            return super().save(force_insert, force_update, using, update_fields)
        else:
            if not self.img.file:
                self.img = ""
                self.hash_code = ""
                self.base_path = ""
                self.img_name = ""
                return super().save(force_insert, force_update, using, update_fields)

            _, extension = os.path.splitext(self.img.name)
            md5_hash = md5()
            for chunk in self.img.chunks():
                md5_hash.update(chunk)
            # 哈希值
            hash_code = md5_hash.hexdigest()
            image_md5_name = hash_code + extension.lower()

            origin_obj = Contact.objects.get(pk=self.pk)
            if origin_obj.hash_code == hash_code:
                return super().save(force_insert, force_update, using, update_fields)

            if Contact.objects.filter(hash_code=self.hash_code).exclude(pk=self.pk).count() > 1:
                return super().save(force_insert, force_update, using, update_fields)

            # 删除旧oss文件
            del_aliyun_certs = generate_sts_credential(settings.ALIYUN_OSS_ROLE_ARN_DEL, str(uuid.uuid4()))
            delete_object(
                del_aliyun_certs["access_key_id"],
                del_aliyun_certs["access_key_secret"],
                del_aliyun_certs["security_token"],
                origin_obj.base_path,
                origin_obj.img_name,
            )
            aliyun_certs = generate_sts_credential(
                settings.ALIYUN_OSS_ROLE_ARN_ADD,
                str(uuid.uuid4()),
            )
            download_url = upload_file_with_bytes(
                aliyun_certs["access_key_id"],
                aliyun_certs["access_key_secret"],
                aliyun_certs["security_token"],
                self.img.file.file.getvalue(),
                image_md5_name,
                base_path=self._base_path,
            )
            self.img = download_url
            self.hash_code = hash_code
            self.base_path = self._base_path
            self.img_name = image_md5_name
            return super().save(force_insert, force_update, using, update_fields)

    def delete(self, using=None, keep_parents=False):
        if Contact.objects.filter(hash_code=self.hash_code).count() > 1:
            return super().delete(using, keep_parents)
        # 删除旧oss文件
        del_aliyun_certs = generate_sts_credential(settings.ALIYUN_OSS_ROLE_ARN_DEL, str(uuid.uuid4()))
        delete_object(
            del_aliyun_certs["access_key_id"],
            del_aliyun_certs["access_key_secret"],
            del_aliyun_certs["security_token"],
            self.base_path,
            self.img_name,
        )
        return super().delete(using, keep_parents)


class LogisticsManagement(models.Model):
    logistics_id = models.CharField(verbose_name="物流编号", max_length=100, default="", null=False, blank=False)
    logistics_name = models.CharField(verbose_name="物流名称", max_length=100, default="", null=False, blank=False)
    origin = models.IntegerField(
        verbose_name="类型",
        choices=(
            (1, "抖店"),
            (2, "聚水谭"),
        ),
        default=1,
        null=False,
        blank=False,
    )
    type = models.IntegerField(
        verbose_name="类型",
        choices=(
            (1, "快递"),
            (2, "物流"),
        ),
        default=1,
        null=False,
        blank=False,
    )
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        db_table = "logistics_management"
        verbose_name = "物流管理"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.logistics_name


class FeiShuMedias(models.Model):
    hash_code = models.CharField("哈希值", max_length=200, unique=True)
    info = models.JSONField("飞书附件信息")

    class Meta:
        verbose_name = "飞书附件记录"
        verbose_name_plural = verbose_name

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        super().save(force_insert, force_update, using, update_fields)

        if self.pk:
            redis_cache.delete(f"fs_medis:{self.hash_code}")

    @classmethod
    def get_with_cache_by_code(cls, code: str):
        key = f"fs_medis:{code}"
        val = redis_cache.get(key)
        if val:
            return val

        obj = cls.objects.filter(hash_code=code).first()
        if not obj:
            return None

        redis_cache.set(key, obj.info, 60 * 60 * 2)
        return obj.info


class DownloadTasksTypeChoices(models.enums.TextChoices):
    PRODUCTS = "products", "商品下载"
    SELECTION_PLAN = "selection_plan", "货盘下载"
    HAND_CARD = "hand_card", "手卡下载"
    ORDERS = "orders", "订单下载"
    REVIEWS = "reviews", "审核下载"
    ERP_SALES_ORDER = "erp_sales_order", "销售订单"
    INBOUND_ORDER = "inbound_order", "采购入库订单"
    OUTBOUND_ORDER = "outbound_order", "采购退货订单"
    CUSTOMER_SETTLE_ORDER = "customer_settle_order", "客户结算订单"
    SUPPLIER_SETTLE_ORDER = "supplier_settle_order", "供应商结算订单"
    SKU_INVENTORY = "sku_inventory", "库存查询"
    ERP_PRODUCT_SKU = "erp_supplier_sku", "ERP商品SKU"
    ERP_PRODUCT_PLAN = "erp_supplier_sku_plan", "生产计划"


class DownloadTasks(models.Model):
    status = models.CharField(
        "任务状态",
        choices=(
            ("pending", "等待中"),
            ("progressing", "下载中"),
            ("completed", "下载完成"),
            ("failed", "下载失败"),
            ("expired", "已失效"),
        ),
        default="pending",
    )
    filename = models.CharField("下载文件名称", max_length=128, default="")
    download_type = models.CharField(
        "下载类型",
        choices=DownloadTasksTypeChoices,
        max_length=32,
        default=DownloadTasksTypeChoices.PRODUCTS,
    )
    download_platform = models.CharField(
        "下载端",
        choices=(
            ("OP", "运营商"),
            ("DB", "分销商"),
            ("SP", "供应商"),
        ),
        default="OP",
    )
    source_id = models.CharField("资源ID", null=True, blank=True, max_length=128, default="")
    download_page = models.CharField("下载页面", max_length=32, default="")
    absolute_url = models.CharField("地址", max_length=300, null=True, blank=True)
    # 用于取消任务
    celery_id = models.CharField("Celery任务ID", null=True, blank=True, default="")
    download_url = models.CharField("文件下载链接", max_length=300, null=True, blank=True, default="")
    # url数据
    query_data = models.JSONField("Url过滤数据", null=True, blank=True, default=dict)
    # post data数据.. 之前有部分的任务，url条件在post_data里面
    post_data = models.JSONField("Body请求数据", null=True, blank=True, default=dict)
    error_reason = models.TextField("错误原因", null=True, blank=True, default="")
    distributor = models.ForeignKey(
        "companies.Distributor",
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="distributor_id",
        verbose_name="所属分销商",
    )
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="company_id",
        verbose_name="所属供应商",
    )

    create_user = models.CharField("创建用户", max_length=32, blank=True, null=True, db_index=True)
    update_user = models.CharField("更新用户", max_length=32, blank=True, null=True, db_index=True)
    expired_time = models.DateTimeField("失效时间", null=True, blank=True, default=None)
    start_time = models.DateTimeField("开始时间", null=True, blank=True, default=None)
    finish_time = models.DateTimeField("完成时间", null=True, blank=True, default=None)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        verbose_name = "下载中心"
        verbose_name_plural = verbose_name


class AISearchRecords(models.Model):
    user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="user_id",
        verbose_name="搜索用户",
    )
    input_text = models.CharField("输入内容", max_length=255)
    input_platform = models.CharField(
        "输入平台",
        choices=(
            ("OP", "运营商端"),
            ("DB", "分销商端"),
        ),
        max_length=2,
        default="OP",
    )
    output_data = models.JSONField("接口返回的数据", null=True, blank=True, default=list)
    execute_ai_model = models.CharField("调用模型", max_length=32, null=True, blank=True, default="")
    execute_sql = models.TextField("执行的sql")
    data_source = models.PositiveSmallIntegerField(
        "调用入口",
        choices=(
            (1, "AI搜索"),
            (2, "AI选品"),
        ),
        default=1,
    )
    is_success = models.BooleanField("是否成功", default=False)
    error1 = models.TextField("模型1错误信息")
    error2 = models.TextField("模型2错误信息")
    plan_id = models.CharField("货盘ID", null=True, blank=True, default="")
    ticket = models.CharField("生成的票据", max_length=64, null=True, blank=True)
    result = models.JSONField("查询结果", null=True, blank=True, default=list)
    revoke_time = models.IntegerField("模型调用时间(毫秒)", default=0)
    query_times = models.PositiveSmallIntegerField("查询该输入次数", default=1)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "AI搜索记录"
        verbose_name_plural = verbose_name


class AISelectionPlanSearchRecords(models.Model):
    user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="user_id",
        verbose_name="搜索用户",
    )
    input_platform = models.CharField(
        "输入平台",
        choices=(
            ("OP", "运营商端"),
            ("DB", "分销商端"),
        ),
        max_length=2,
        default="OP",
    )
    plan = models.ForeignKey(
        "products.ProductSelectionPlan",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="plan_id",
        verbose_name="货盘计划",
    )
    total_cost_price = models.DecimalField(
        "总成本",
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        default=0,
    )
    total_spu_count = models.IntegerField("SPU总数量", null=True, blank=True, default=0)
    total_estimated_sales = models.DecimalField(
        "预估总销量",
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        default=0,
    )
    address = models.CharField("直播地址", max_length=128, null=True, blank=True, default="")
    execute_sql = models.TextField("执行的sql")
    ticket = models.CharField("生成的票据", max_length=64, null=True, blank=True, default=uuid.uuid4)
    result = models.JSONField("查询结果", null=True, blank=True, default=list)
    query_times = models.PositiveSmallIntegerField("查询该输入次数", default=1)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "智能货盘搜索记录"
        verbose_name_plural = verbose_name


class AISelectionPlanSearchRecordsDetail(models.Model):
    record = models.ForeignKey(
        AISelectionPlanSearchRecords,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name="所属记录",
    )

    category = models.ForeignKey(
        "products.ProductCategoryList",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="id",
        verbose_name="分类ID",
    )
    category_name = models.CharField("分类名称", null=True, blank=True, default="")
    styles = models.JSONField("款式列表", null=True, blank=True, default=list)
    sales_price_range = models.JSONField("售价范围列表", null=True, blank=True, default=list)
    refund_rate_range = models.JSONField("退费率范围", null=True, blank=True, default=list)
    latest_7_days_sales_range = models.JSONField("近7日销量范围列表", null=True, blank=True, default=list)
    latest_30_days_sales_range = models.JSONField("近30日销量范围列表", null=True, blank=True, default=list)
    labels = models.JSONField("标签列表", null=True, blank=True, default=list)

    class Meta:
        verbose_name = "智能货盘搜索记录明细"
        verbose_name_plural = verbose_name


class AISelectionLiveAddress(models.Model):
    cache_key = "ai:selection_live_address"

    name = models.CharField("名称", max_length=50)
    order = models.PositiveSmallIntegerField("排序", default=0, help_text="排序(数字越大排越后)")

    class Meta:
        verbose_name = "智能选品-直播地点"
        verbose_name_plural = verbose_name

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        super().save(force_insert, force_update, using, update_fields)

        cache.delete(self.cache_key)

    @classmethod
    def get_cache_data(cls):
        val = cache.get(cls.cache_key)
        if val:
            return val

        addresses = AISelectionLiveAddress.objects.all().order_by("order")
        re_data = [{"id": address.id, "name": address.name} for address in addresses]
        cache.set(cls.cache_key, re_data)
        return re_data


class AISelectionSalesPriceRange(models.Model):
    cache_key = "ai:selection_sales_price_range"

    min_price = models.IntegerField("最小售价", default=0)
    max_price = models.IntegerField("最大售价", null=True, blank=True, default=None)

    class Meta:
        verbose_name = "智能选品-售价范围"
        verbose_name_plural = verbose_name

    @classmethod
    def get_cache_data(cls):
        val = cache.get(cls.cache_key)
        if val:
            return val

        price_ranges = AISelectionSalesPriceRange.objects.all()
        re_data = [
            {
                "id": price_range.id,
                "min_price": price_range.min_price,
                "max_price": price_range.max_price,
            }
            for price_range in price_ranges
        ]
        cache.set(cls.cache_key, re_data)
        return re_data


class MiniProgramShareRecord(models.Model):
    share_platform = models.PositiveSmallIntegerField(
        "分享端",
        choices=(
            (1, "臻品"),
            (2, "甄选"),
        ),
        default=1,
    )
    scene = models.CharField("参数", max_length=32)
    download_url = models.ImageField("图片地址", max_length=300)
    ts = models.CharField("分享时间戳(毫秒)", max_length=32)
    product = models.ForeignKey(
        "products.Product",
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        to_field="product_id",
        verbose_name="关联商品",
    )
    share_user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="user_id",
        null=True,
        blank=True,
        default=None,
        related_name="share_user",
        verbose_name="分享用户",
    )
    share_date = models.DateTimeField("分享时间", auto_now_add=True)
    login_user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        db_constraint=False,
        to_field="user_id",
        null=True,
        blank=True,
        default=None,
        related_name="login_user",
        verbose_name="登录用户",
    )
    login_date = models.DateTimeField("登录时间", null=True, blank=True, default=None)

    class Meta:
        verbose_name = "小程序分享记录"
        verbose_name_plural = verbose_name


class ExchangeRate(models.Model):
    base_currency = models.CharField("基础货币", max_length=3, default="USD")
    target_currency = models.CharField("转换货币", max_length=3)
    rate = models.DecimalField(verbose_name="汇率", max_digits=15, decimal_places=6)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间")

    class Meta:
        verbose_name = "汇率表"
        verbose_name_plural = verbose_name
        unique_together = ("base_currency", "target_currency")


class OrderPreprocessingRule(models.Model):
    CACHE_KEY = "order_preprocessing_rule"
    RULE_TYPES = [
        (1, "商品名称包含关键字"),
        (2, "商编匹配"),
        (3, "分销商货号关键字母"),
        (4, "达人ID关键字"),
        (5, "直播间ID关键字"),
    ]

    rule_type = models.PositiveSmallIntegerField("规则类型", choices=RULE_TYPES, unique=True, editable=False)
    order = models.PositiveSmallIntegerField("排序", help_text="排序：从小到大", default=1)
    hit_times = models.IntegerField("命中次数", default=0)
    enabled = models.BooleanField("是否启用", default=False)

    class Meta:
        verbose_name = "订单预处理规则"
        verbose_name_plural = verbose_name
        ordering = ["order"]

    def __str__(self):
        return f"{self.get_rule_type_display()} (Order {self.order})"

    @classmethod
    def get_cache_rules(cls):
        cache_val = cache.get(cls.CACHE_KEY)
        if cache_val:
            return cache_val

        rules = OrderPreprocessingRule.objects.filter(enabled=True).order_by("order")
        cache.set(cls.CACHE_KEY, rules, 3600 * 24)
        return rules
