# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-06-12 17:07:41
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-12-22 13:58:32
import decimal
import json
import re
import time
import traceback
from datetime import datetime, date, timedelta
from decimal import ROUND_HALF_UP
from functools import cached_property
from functools import lru_cache

from dateutil import parser
from django.apps import apps
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Func, Expression, F, Value
from django.utils import timezone
from django.utils.crypto import get_random_string
from django.utils.timezone import get_current_timezone
from rest_framework.exceptions import ErrorDetail
from rest_framework.pagination import PageNumberPagination
from rest_framework.parsers import BaseParser
from rest_framework.response import Response
from rest_framework.views import exception_handler
from rest_framework_simplejwt.exceptions import InvalidToken

from utils.vectorvein_ai import vectorvein_client
from . import logger
from .formats import DATETIME_FORMAT
from .models import LiveAuthor


def get_spec_code(author_id):
    """
    生成商品编码
    主播代码+地址大写首字母（GZ）+当天日期（231101）+4位随机数
    主播代码：云上珠宝01、小宝珠宝02、欧巴珠宝03、曹掌柜06
    Args:
        author_id (_type_): 主播id
    """
    # author_id_code_map = {
    #     "3852285068190743": "01",
    #     "64561426221": "02",
    #     "2287402037034232": "03",
    #     "3650013905302647": "06",
    #     "3386124269020078": "05",
    # }
    # code = author_id_code_map.get(author_id)
    live_author = LiveAuthor.objects.filter(author_id=author_id).first()
    if not live_author or not live_author.code:
        return None
    code = live_author.code
    today = timezone.now().date().strftime("%Y%m%d")[2:]
    random_str = get_random_string(length=4, allowed_chars="1234567890")
    res = f"{code}GZ{today}{random_str}"
    return res


def custom_exception_handler(exc, context):
    # 首先调用REST framework默认的异常处理，
    # 以获得标准的错误响应。
    response = exception_handler(exc, context)

    # 用于自定义exception 提供的response_code
    try:
        if response:
            response_code = response.status_code
        else:
            response_code = exc.err_code
    except:
        response_code = 500

    if isinstance(exc, InvalidToken):
        error_message = exc.detail.get("code")
    elif isinstance(exc, ValidationError):
        if hasattr(exc, "message_dict"):
            error_message = ",".join(f"{field}: {', '.join(map(str, errors))}" for field, errors in exc.message_dict.items())
        elif hasattr(exc, "messages"):
            error_message = ",".join(map(str, exc.messages))
        else:
            error_message = str(exc)
    elif response and isinstance(response.data, dict):
        error_message_list = []
        for field, errors in response.data.items():
            if isinstance(errors, list):
                cleaned_errors = [str(error).strip("[]").strip("'\"") for error in errors if isinstance(error, (ErrorDetail, str))]
                error_message_list.extend(cleaned_errors)
            elif isinstance(errors, (ErrorDetail, str)):
                error_message_list.append(str(errors).strip("[]").strip("'\""))
        error_message = ",".join(error_message_list)

    else:
        error_message = str(exc)
    if response_code == 500:
        logger.warning(traceback.format_exc())
    response_data = {
        "success": False,
        "code": response_code,
        "errorMessage": error_message,
        "data": None,
    }
    if response:
        response.status_code = 200
        response.data = response_data
    else:
        response = Response(status=200, data=response_data)

    return response


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 2
    page_query_param = "page"
    page_size_query_param = "page_size"
    max_page_size = 1000


# 用于日志埋点的resource name记录
RESOURCE_NAME_MAP = {
    "user_type:method:url_name": "不同用户端资源操作名称",
    "OP:PUT:op.product.selection_plan.detail": "运营商-编辑货盘计划",
    "OP:PATCH:op.product.selection_plan_manager": "运营商-编辑货盘计划商品备注",
    "OP:DELETE:op.product.selection_plan_manager": "运营商-从货盘计划中移除商品",
    "OP:POST:op.product.selection_plan_manager": "运营商-将商品加入货盘计划",
    "OP:POST:op.product.to_plan_bulk": "运营商-将商品批量加入货盘计划",
}


class PlainTextParser(BaseParser):
    media_type = "text/plain"

    def parse(self, stream, media_type=None, parser_context=None):
        return stream.read()


def list_to_tree(data_list: list, root_val=None, id_key="id", parent_id_key="parent_id", children_key="subs"):
    def sort_by_order(nodes):
        has_order = sorted([n for n in nodes if "order" in n], key=lambda x: x["order"])
        no_order = [n for n in nodes if "order" not in n]
        return has_order + no_order

    # 创建节点字典并初始化所有节点的子节点列表
    node_dict = {node[id_key]: node for node in data_list}
    for node in data_list:
        node.setdefault(children_key, [])  # 安全初始化，避免覆盖已有子节点

    # 构建树结构
    for node in data_list:
        parent_id = node.get(parent_id_key)
        if parent_id in node_dict:
            parent = node_dict[parent_id]
            parent[children_key].append(node)

    # 递归排序所有层级
    def recursive_sort(nodes):
        sorted_nodes = sort_by_order(nodes)
        for n in sorted_nodes:
            if n[children_key]:
                n[children_key] = recursive_sort(n[children_key])
        return sorted_nodes

    # 处理根节点并执行全树排序
    tree_list = [n for n in data_list if n.get(parent_id_key) == root_val]
    return recursive_sort(tree_list)


class SimpleFunc(Func):
    def __init__(self, field, *values, **extra):
        if not isinstance(field, Expression):
            field = F(field)
            if values and not isinstance(values[0], Expression):
                values = [Value(v) for v in values]
        super().__init__(field, *values, **extra)


class ArrayReplace(SimpleFunc):
    """
    psql: 替换ArrayFiled里面某个值
    注意是全部值都会更换
    示例
    category = ArrayField()
    ArrayReplace('field_name','old_value','replace_value')
    xxxxmodel.objects.filter().update(category = ArrayReplace('category','old_value','replace_value'))
    """

    function = "ARRAY_REPLACE"


def diff_models(
    origin_model_object,
    update_model_object,
    split_symbol: str = "、",
    private_field: list = None,
) -> str:
    """
    比较两个模型对象的变化
    :param origin_model_object: 旧模型对象
    :param update_model_object: 新模型对象
    :param split_symbol: 分隔符
    :param private_field: 私有字段列表, 在model对象配置 _log_private_field 列表即可
    :return: 变化字符串
    """
    try:
        if private_field is None:
            private_field = []
        private_field += getattr(origin_model_object.__class__, "_log_private_field", [])

        if not origin_model_object or not update_model_object:
            return ""

        model_meta = origin_model_object.__class__._meta
        diff_list = []

        # 比较普通字段
        skipped_fields = ["create_date", "update_date", "create_user", "update_user"]
        for field in model_meta.fields:
            if field.name in skipped_fields + private_field:
                continue
            old_val = field.value_from_object(origin_model_object)
            new_val = field.value_from_object(update_model_object)
            if old_val == new_val:
                continue
            if isinstance(field, models.ForeignKey):
                diff = process_foreign_key_field(field, old_val, new_val)
                if diff:
                    diff_list.append(diff)
                continue
            if field.choices:
                old_val = getattr(origin_model_object, f"get_{field.name}_display")()
                new_val = getattr(update_model_object, f"get_{field.name}_display")()
            if field.name == "password":
                diff_list.append(f"{field.verbose_name}:更新了新密码")
                continue
            diff_list.append(f'{field.verbose_name}:"{old_val}"修改为:"{new_val}"')

        # 比较多对多字段
        for field in model_meta.many_to_many:
            diffs = get_m2m_diff(field, update_model_object, split_symbol)
            diff_list.extend(diffs)

        return split_symbol.join(diff_list)
    except Exception as e:
        logger.warning("diff_models error: %s" % e)
        return ""


def process_foreign_key_field(field, old_val, new_val):
    """
    处理外键字段的差异
    :param field: 字段
    :param old_val: 旧值
    :param new_val: 新值
    :return: 差异字符串
    """
    if old_val == new_val:
        return None
    old_obj = get_related_object(field, old_val)
    new_obj = get_related_object(field, new_val)
    return f'{field.verbose_name}:"{old_obj}"修改为:"{new_obj}"'


def get_related_object(field, val):
    """
    获取相关对象的字符串表示
    :param field: 外键字段
    :param val: 字段值
    :return: 对象字符串
    """
    if val is None:
        return "-"

    if field.to_fields:
        conditions = {f: val for f in field.to_fields if f}
    else:
        conditions = {"pk": val}

    if not conditions:
        conditions = {"pk": val}

    obj = field.related_model.objects.filter(**conditions).first()
    return str(obj or "-")


def get_m2m_diff(field, update_model_object, split_symbol):
    """
    获取多对多字段的差异
    :param field: 多对多字段
    :param update_model_object: 新模型对象
    :param split_symbol: 分隔符
    :return: 差异列表
    """
    signal_field_name = getattr(update_model_object.__class__, field.name).through.__name__
    signal_add_field = f"__{signal_field_name}_add__"
    signal_remove_field = f"__{signal_field_name}_remove__"

    if hasattr(update_model_object, signal_add_field) or hasattr(update_model_object, signal_remove_field):
        new_add_pks = getattr(update_model_object, signal_add_field)
        remove_add_pks = getattr(update_model_object, signal_remove_field)
        current_relation_data = list(getattr(update_model_object, field.name).all())
        original_relation_id_list = [i.pk for i in current_relation_data if i.pk not in new_add_pks] + (remove_add_pks or [])
        old_relation_data = list(field.related_model.objects.filter(pk__in=original_relation_id_list))
        old_val = sorted(old_relation_data, key=lambda x: x.pk)
        new_val = sorted(current_relation_data, key=lambda x: x.pk)
        if old_val != new_val:
            old_v = split_symbol.join([str(i) for i in old_val])
            new_v = split_symbol.join([str(i) for i in new_val])
            return [f'{field.verbose_name}:"{old_v}"修改为:"{new_v}"']
        else:
            return []
    return []


def parse_to_datetime(input_str: str, min_time=False, max_time=False) -> str:
    """
    将字符串转化成datetime类型的字符
    :param max_time:
    :param input_str: 输入的字符串
    :param min_time: 是否是最小，否则是最大。默认最小
    :return:
    """

    if min_time and max_time:
        raise ValueError("min_time and max_time cannot be true at the same time")

    try:
        parsed_date = datetime.strptime(input_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        try:
            parsed_date = datetime.strptime(input_str, "%Y-%m-%d")
        except ValueError:
            raise ValueError("无法解析输入的日期字符串")

    # if isinstance(parsed_date, datetime):
    #     return timezone.make_aware(parsed_date, get_current_timezone()).strftime("%Y-%m-%d %H:%M:%S")

    # 如果解析结果是date类型，则将其转换为datetime类型
    if isinstance(parsed_date, date):
        parsed_date = datetime.combine(parsed_date, datetime.min.time())

    if min_time:
        parsed_date = datetime.combine(parsed_date.date(), datetime.min.time())
    elif max_time:
        parsed_date = datetime.combine(parsed_date.date(), datetime.max.time()) + timedelta(seconds=1)

    return timezone.make_aware(parsed_date, get_current_timezone()).strftime("%Y-%m-%d %H:%M:%S")


class ProductExtraTagsFields:
    elements = (
        "price_verified",
        "qa_verified",
    )


class ProductExtraTags(ProductExtraTagsFields):
    """
    商品额外标签统一处理
    在elements加字段, 并实现 get_字段 方法
    """

    def __init__(self, product):
        self._product = product

    def to_dict(self):
        re_data = {}
        for element in self.elements:
            func = getattr(self, f"get_{element}")
            if not func:
                re_data[element] = None
            else:
                re_data[element] = func()
        return re_data

    @cached_property
    def data(self):
        return self._get_review_verified_count()

    def _get_review_verified_count(self) -> dict:
        review_records = self._product.productreview_set.filter(
            process_level__in=["PRICE_REVIEW", "QA_REVIEW"],
            process__become_history=False,
            become_history=False,
        )
        data = {record.process_level: record.verified_pass for record in review_records}
        return data

    def get_price_verified(self):
        return self.data.get("PRICE_REVIEW", None)

    def get_qa_verified(self):
        return self.data.get("QA_REVIEW", None)


class ProductBulkExtraTags(ProductExtraTagsFields):
    """
    批量查询
    商品额外标签统一处理
    在elements加字段, 并实现 get_字段 方法
    """

    def __init__(self, product_set):
        self._product_set = product_set

    @cached_property
    def data(self):
        return self._get_review_verified_result()

    def _get_review_verified_result(self) -> dict:
        ProductReview = apps.get_model("products", "ProductReview")

        product_pk_list = [p.pk for p in self._product_set]
        review_records = ProductReview.objects.filter(
            product_id__in=product_pk_list,
            process_level__in=["PRICE_REVIEW", "QA_REVIEW"],
            process__become_history=False,
            become_history=False,
        )
        _data = {}

        for record in review_records:
            product_pk = record.product_id
            if product_pk not in _data:
                _data[product_pk] = {record.process_level: record.verified_pass}
            else:
                _data[product_pk].update({record.process_level: record.verified_pass})
        return _data

    def get(self, product_pk):
        if not self.data.get(product_pk):
            return {
                "price_verified": None,
                "qa_verified": None,
            }
        else:
            _d = self.data.get(product_pk)
            return {
                "price_verified": _d.get("PRICE_REVIEW", None),
                "qa_verified": _d.get("QA_REVIEW", None),
            }


def system_labels_handler_fc(data: list):
    """
    分销商热卖标签只显示最高值的那个
    :param data:

    {
        "id": label_relate.label_id,
        "name": label_relate.show_name,
        "can_edit": label_relate.label.can_edit,
        "act_value": label_relate.act_value,
        "distributor_letters": label_relate.distributor_letters,
    }

    :return:
    """

    def custom_sort(item):
        return item.get("act_value", 0)

    values_with_letters = [item for item in data if item.get("distributor_letters") is not None]

    max_value_item = max(values_with_letters, key=custom_sort) if values_with_letters else None

    sorted_data = sorted(values_with_letters, key=custom_sort)

    need_remove = [i for i in sorted_data if i != max_value_item]

    filtered_list = [j for j in data if j not in need_remove]

    ret = [
        {
            "id": x["id"],
            "name": x["name"],
            "can_edit": x["can_edit"],
        }
        for x in filtered_list
    ]

    return ret


def convert_2_aware_time(date_time: datetime):
    if not date_time:
        return ""
    return timezone.make_aware(timezone.make_naive(date_time)).strftime(DATETIME_FORMAT)


def convert_str_2_aware_time(date_time_str: str):
    """
    将日期时间字符串转换为 datetime 对象，并格式化为指定格式。

    :param date_time_str: 日期时间字符串（支持多种格式）
    :return: 格式化后的字符串，或空字符串（如果输入为空或无法解析）
    """
    if not date_time_str:
        return ""

    try:
        # 尝试使用 ISO 8601 格式解析
        dt = datetime.fromisoformat(date_time_str)
    except ValueError:
        try:
            # 如果 ISO 8601 解析失败，尝试使用 dateutil 解析
            dt = parser.parse(date_time_str)
        except Exception as e:
            # 如果所有解析都失败，返回空字符串并打印错误信息
            print(f"无法解析日期时间字符串：{date_time_str}，错误：{e}")
            return ""

    # 如果输入有时区信息，转换为本地时间（无时区）
    if timezone.is_aware(dt):
        dt = timezone.make_naive(dt)

    # 返回格式化后的字符串
    return dt.strftime(DATETIME_FORMAT)


def convert_datatime_val_2_aware_time(datetime_val: str | datetime) -> datetime | None:
    if not datetime_val:
        return

    val = datetime_val
    if isinstance(datetime_val, str):
        val = datetime.strptime(datetime_val, DATETIME_FORMAT)

    if timezone.is_naive(val):
        return timezone.make_aware(val)

    return timezone.make_aware(timezone.make_naive(val))


def convert_timestamp_2_aware_time(timestamp: int, output_str: bool = False) -> datetime | None | str:
    if not timestamp:
        return

    data = convert_datatime_val_2_aware_time(datetime.fromtimestamp(timestamp))
    if output_str:
        return data.strftime(DATETIME_FORMAT)
    return data


def convert_cent_to_decimal(val: int) -> decimal.Decimal:
    if not val:
        return decimal.Decimal(0)

    cent_decimal = decimal.Decimal(val) / 100
    covert_val = cent_decimal.quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
    return covert_val


def convert_value_to_decimal(val) -> decimal.Decimal:
    if not val:
        return decimal.Decimal(0)

    val_decimal = decimal.Decimal(val)
    covert_val = val_decimal.quantize(decimal.Decimal("0.01"), rounding=ROUND_HALF_UP)
    return covert_val


def parse_spec_code(raw_spec_code: str) -> (str, str, str):
    """
    解析spec_code
    :param raw_spec_code:原始商编
    :return:原字符串,匹配出来的spec_code,分销商代码
    """
    pattern = re.compile(r"^(J\d+)([A-Za-z]{1,3})$")
    match = pattern.match(raw_spec_code)
    if not match:
        return raw_spec_code, "", ""

    sku_spec_code = match.group(1)
    letters = match.group(2)
    return raw_spec_code, sku_spec_code or "", letters or ""


def sensitive_fields_save_start_end(content):
    if content:
        start = content[0]
        end = content[-1]
        return f"{start}****{end}"
    else:
        return content


def remove_user_agent_info(user_agent):
    pattern = r"\([^)]*?0x[0-9a-fA-F]+[^)]*?\)"
    cleaned_user_agent = re.sub(pattern, "", user_agent)
    cleaned_user_agent = cleaned_user_agent.replace("4G", "").replace("2G", "").replace("3G", "").replace("WIFI", "")
    return cleaned_user_agent


def split_user_address(text):
    """
    return :
    {'status': 200, 'msg': 'FINISHED', 'data': [{'type': 'Text', 'title': '', 'value': '{"province": "陕西省", "city": "西安市", "district": "长安区", "detail": "航天基地航创广场C座", "username": "张强", "phone": "18092584890"}'}]}

    """
    try:
        result = vectorvein_client.fetch_address(text)
        if result.get("data"):
            address_info = json.loads(result["data"][0].get("value"))
            return address_info
        return {}
    except Exception:
        logger.error("拆分用户地址错误")
        return {}


def sorted_distributor_info(data):
    if not data:
        return data

    try:

        def sort_key(item):
            code = item.get("code", " ") or " "  # Get "code", default to a space if not found or empty
            sorted_seq = ["V", "T", "G", "MJ", "AL"]

            for i, prefix in enumerate(sorted_seq):
                if code.startswith(prefix):
                    return i

            return len(sorted_seq)

        return sorted(data, key=sort_key)
    except Exception as e:
        logger.warning(f"排序错误:{e}")
        return data


def is_valid_phone_number(phone_number):
    # 定义手机号的正则表达式
    pattern = r"^1[3-9]\d{9}$"

    # 使用re.match来匹配字符串
    if re.match(pattern, phone_number):
        return True
    else:
        return False


def get_current_user_type(request):
    """统一处理获取当前request的用户类型"""
    if request.auth:
        return request.auth.get("user_type")
    return


class CategoryFindAncestorsCache:
    def __init__(self, data):
        self.data = data
        self.id_map = {item["id"]: item for item in data}
        self._last_update = time.time()
        # 缓存数据结构：{ target_id: [path_list] }
        self.cache = {}

    def update_data(self, new_data):
        """数据更新时自动清空缓存"""
        self.data = new_data
        self.id_map = {item["id"]: item for item in new_data}
        self.cache.clear()
        self._last_update = time.time()

    @lru_cache(maxsize=128)  # 基于访问频率的缓存淘汰
    def find_parents(self, target_id):
        """带缓存的查询方法"""
        path = []
        current_id = target_id
        while current_id is not None:
            if current_id not in self.id_map:
                break
            node = self.id_map[current_id]
            path.insert(0, {"id": node["id"], "name": node["name"]})
            current_id = node["parent_id"]
        return path
