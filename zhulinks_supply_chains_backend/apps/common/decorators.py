# -*- coding: utf-8 -*-
import time
from functools import wraps

from django.core.cache import cache


def timing_decorator(debug=False):
    def decorator(func):
        def wrapper(*args, **kwargs):
            if not debug:
                result = func(*args, **kwargs)
            else:
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                elapsed_time = end_time - start_time
                print(f"Function '{func.__name__}' executed in {elapsed_time:.4f} seconds")
            return result

        return wrapper

    return decorator


class DistributedLock:
    def __init__(self, key, expire=60):
        self.key = f"lock:{key}"
        self.expire = expire

    def acquire(self):
        return cache.add(self.key, 1, self.expire)

    def release(self):
        cache.delete(self.key)


def distributed_lock(key, expire=60):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            lock = DistributedLock(key, expire)
            if not lock.acquire():
                return "Task already running"
            try:
                return func(*args, **kwargs)
            finally:
                lock.release()

        return wrapper

    return decorator
