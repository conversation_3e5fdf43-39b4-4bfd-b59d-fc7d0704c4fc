# -*- coding: utf-8 -*-

from django.urls import path

from data_center.views import (
    DataOverviewView,
    DataCategorySalesStatisticsView,
    DataProductSalesStatisticsView,
    ProductCategorySalesRankView,
    DistributorModePurchaseDataView,
    HotProductRankView,
    HistoryHotProductRankView, SelectionPlanDataOverviewView,
)

urlpatterns = [
    # 数据概况
    path("overview", DataOverviewView.as_view(), name="data_center.overview"),
    # 类目销售统计
    path("category_sales_statistics", DataCategorySalesStatisticsView.as_view(), name="data_center.category_sales_statistics"),
    # 商品销售统计
    path("product_sales_statistics", DataProductSalesStatisticsView.as_view(), name="data_center.product_sales_statistics"),
    # 分类榜单
    path("category_sales/rank", ProductCategorySalesRankView.as_view(), name="data_center.category_sales_rank"),
    # 分销模式 - 采购数据
    path("purchase_data", DistributorModePurchaseDataView.as_view(), name="dbm2_purchase_data"),
    # 排行榜 - 爆品
    path("rank/hot_products", HotProductRankView.as_view(), name="rank.hot_products"),
    # 排行榜 - 历史榜
    path("rank/history_hot_products", HistoryHotProductRankView.as_view(), name="rank.history_hot_products"),
    # 货盘数据概览 - (货盘统计在product模块)
    path("selection_plan/overview", SelectionPlanDataOverviewView.as_view(), name="selection_plan.overview"),
]
