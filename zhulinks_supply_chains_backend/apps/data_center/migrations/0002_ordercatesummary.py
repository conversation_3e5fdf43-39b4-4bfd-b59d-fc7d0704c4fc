# Generated by Django 5.0.6 on 2024-06-06 05:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data_center", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderCateSummary",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("category_id", models.CharField(max_length=100)),
                ("order_date", models.DateTimeField()),
                ("total", models.IntegerField()),
                ("total_price", models.DecimalField(decimal_places=2, max_digits=10)),
            ],
            options={
                "db_table": "category_orders_sumary",
                "managed": False,
            },
        ),
        migrations.RunSQL(
            """CREATE MATERIALIZED VIEW "category_orders_summary" AS SELECT
( "products_product"."category" ) [ 1 ] AS "category_id",
DATE ( "orders_raworder"."order_date" ) AS "order_date",
COALESCE ( SUM ( "orders_raworder"."count" ), 0 ) AS "total",
COALESCE ( SUM ( "orders_raworder"."total_price" ), 0 ) AS "total_price" 
FROM
	"orders_raworder"
	INNER JOIN "products_product" ON ( "orders_raworder"."product_id" = "products_product"."id" ) 
WHERE
	"orders_raworder"."product_id" IS NOT NULL 
GROUP BY
	1,
	DATE ( order_date );"""
        ),
    ]
