# -*- coding: utf-8 -*-
import django_filters

from common.basic import RangeFilter
from common.basics.filtersets import DateFilter
from common.filtersets import DateRangeFilter, BaseDateFilterSet
from data_center.models import OrderSummary
from products.models import ProductSelectionItemSKU


class OrderSummaryFilterSet(BaseDateFilterSet):
    order_date = DateRangeFilter(field_name="order_date", contains_end_date=True)
    product_id = django_filters.CharFilter(field_name="product_id", lookup_expr="contains")

    class Meta:
        model = OrderSummary
        fields = ("order_date", "product_id")


class OrderSummaryCountAndSalesAmountFilterSet(BaseDateFilterSet):
    sales_count = RangeFilter(field_name="sales_count")
    sales_amount = RangeFilter(field_name="sales_amount")

    class Meta:
        model = OrderSummary
        fields = (
            "sales_count",
            "sales_amount",
        )


class WXSelectionPlanOverviewFilterSet(BaseDateFilterSet):
    calc_date = DateFilter(field_name="selection_plan__live_date_start", required=True)

    class Meta:
        model = ProductSelectionItemSKU
        fields = ("calc_date",)


class PCSelectionPlanOverviewFilterSet(BaseDateFilterSet):
    calc_date = DateRangeFilter(field_name="selection_plan__live_date_start", required=True, contains_end_date=True, is_date=True)

    class Meta:
        model = ProductSelectionItemSKU
        fields = ("calc_date",)
