# -*- coding: utf-8 -*-
from datetime import datetime, timedelta
from django.utils import timezone


def get_day_month_range() -> (tuple, tuple):
    """
    根据当前日期获取本周和本月的日期区间
    :return:
    """
    today = timezone.now().date()
    first_day_of_month = today.replace(day=1)

    today_start = timezone.make_aware(datetime.combine(today, datetime.min.time()))
    today_end = timezone.make_aware(datetime.combine(today, datetime.max.time()))

    month_start = timezone.make_aware(datetime.combine(first_day_of_month, datetime.min.time()))
    next_month = first_day_of_month + timedelta(days=32)
    first_day_of_next_month = next_month.replace(day=1)
    month_end = timezone.make_aware(datetime.combine(first_day_of_next_month, datetime.min.time()))

    return today, (today_start, today_end), (month_start, month_end)
