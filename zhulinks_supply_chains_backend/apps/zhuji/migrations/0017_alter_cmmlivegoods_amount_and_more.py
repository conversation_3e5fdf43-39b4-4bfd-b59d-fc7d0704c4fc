# Generated by Django 4.2.1 on 2023-09-25 02:56

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("zhuji", "0016_alter_cmmliveoverviewdaily_amount_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="cmmlivegoods",
            name="amount",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="销额",
            ),
        ),
        migrations.AlterField(
            model_name="cmmlivegoods",
            name="gmv_volume",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="cmmlivegoods",
            name="product_pv",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=20, null=True
            ),
        ),
        migrations.AlterField(
            model_name="cmmliveoverview",
            name="uv",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="uv",
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="sales_amount",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="销售额",
            ),
        ),
    ]
