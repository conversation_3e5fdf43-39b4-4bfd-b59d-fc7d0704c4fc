# Generated by Django 4.2.1 on 2023-09-08 10:05

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("zhuji", "0002_cmmlivegoods_cmmliveoverview_spidertask_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="cmmlivegoods",
            name="create_time",
        ),
        migrations.RemoveField(
            model_name="cmmlivegoods",
            name="update_time",
        ),
        migrations.RemoveField(
            model_name="cmmliveoverview",
            name="create_time",
        ),
        migrations.RemoveField(
            model_name="cmmliveoverview",
            name="update_time",
        ),
        migrations.AddField(
            model_name="cmmlivegoods",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="cmmlivegoods",
            name="update_date",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
        migrations.AddField(
            model_name="cmmliveoverview",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="cmmliveoverview",
            name="update_date",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
    ]
