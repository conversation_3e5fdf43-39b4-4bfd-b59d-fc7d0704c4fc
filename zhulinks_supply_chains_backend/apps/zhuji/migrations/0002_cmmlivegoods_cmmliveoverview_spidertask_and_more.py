# Generated by Django 4.2.1 on 2023-09-08 07:38

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("zhuji", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CMMLiveGoods",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="主播"
                    ),
                ),
                ("room_id", models.CharField(max_length=50, verbose_name="直播间id")),
                ("product_id", models.CharField(max_length=50, verbose_name="商品id")),
                (
                    "product_title",
                    models.CharField(
                        blank=True, max_length=500, null=True, verbose_name="商品名"
                    ),
                ),
                (
                    "product_url",
                    models.CharField(
                        blank=True, max_length=300, null=True, verbose_name="商品链接"
                    ),
                ),
                ("is_sku", models.<PERSON><PERSON>an<PERSON>ield(blank=True, max_length=20, null=True)),
                (
                    "has_detail",
                    models.<PERSON><PERSON>an<PERSON>ield(blank=True, max_length=20, null=True),
                ),
                (
                    "has_volume",
                    models.BooleanField(blank=True, max_length=20, null=True),
                ),
                ("brand_name", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "introduce_duration",
                    models.IntegerField(blank=True, null=True, verbose_name="累计讲解时长"),
                ),
                (
                    "sales",
                    models.IntegerField(blank=True, null=True, verbose_name="销量"),
                ),
                (
                    "final_sales",
                    models.IntegerField(blank=True, null=True, verbose_name="实际销量"),
                ),
                (
                    "returned_rate",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="销额",
                    ),
                ),
                (
                    "commission_rate",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("image", models.CharField(blank=True, max_length=300, null=True)),
                ("brand_code", models.CharField(blank=True, max_length=20, null=True)),
                ("initial_sales", models.IntegerField(blank=True, null=True)),
                ("click_percent", models.IntegerField(blank=True, null=True)),
                ("shop_name", models.CharField(blank=True, max_length=255, null=True)),
                ("sub_title", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "encore_count",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("platform", models.CharField(blank=True, max_length=20, null=True)),
                ("source", models.IntegerField(blank=True, null=True)),
                (
                    "start_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="上架时间"),
                ),
                (
                    "stop_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="下架时间"),
                ),
                (
                    "volume",
                    models.IntegerField(blank=True, null=True, verbose_name="成交量"),
                ),
                (
                    "final_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="实际价格",
                    ),
                ),
                (
                    "min_price",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "product_pv",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "max_price",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "gmv_volume",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "dy_promotion_id",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("bubble_pv", models.IntegerField(blank=True, null=True)),
                (
                    "amount_text",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="销额范围"
                    ),
                ),
                ("for_sale", models.IntegerField(blank=True, null=True)),
                ("id_status", models.IntegerField(blank=True, null=True)),
                ("detail_status", models.IntegerField(blank=True, null=True)),
                ("create_time", models.DateTimeField(blank=True, null=True)),
                ("update_time", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "直播商品",
                "verbose_name_plural": "直播商品",
            },
        ),
        migrations.CreateModel(
            name="CmmLiveOverview",
            fields=[
                (
                    "room_id",
                    models.CharField(
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                        verbose_name="直播间ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="主播"
                    ),
                ),
                (
                    "begin_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="开播时间"),
                ),
                (
                    "room_finish_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="下播时间"),
                ),
                (
                    "live_duration",
                    models.IntegerField(blank=True, null=True, verbose_name="持续时间"),
                ),
                (
                    "watch_cnt",
                    models.IntegerField(blank=True, null=True, verbose_name="观看人次"),
                ),
                (
                    "amount_min",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="本场销额下限"
                    ),
                ),
                (
                    "amount_max",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="本场销额上限"
                    ),
                ),
                (
                    "uv",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="uv",
                    ),
                ),
                (
                    "average_residence_time",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="观众平均停留时长"
                    ),
                ),
                (
                    "user_peak",
                    models.IntegerField(blank=True, null=True, verbose_name="人气峰值"),
                ),
                (
                    "average_user_count",
                    models.IntegerField(blank=True, null=True, verbose_name="平均在线"),
                ),
                (
                    "total_favorited",
                    models.IntegerField(blank=True, null=True, verbose_name="发送弹幕"),
                ),
                (
                    "like_count",
                    models.IntegerField(blank=True, null=True, verbose_name="累计点赞"),
                ),
                (
                    "increment_follower_count",
                    models.IntegerField(blank=True, null=True, verbose_name="涨粉人数"),
                ),
                (
                    "convert_fan_rate",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=10,
                        null=True,
                        verbose_name="转粉率",
                    ),
                ),
                (
                    "volume_min",
                    models.IntegerField(blank=True, null=True, verbose_name="销量下限"),
                ),
                (
                    "volume_max",
                    models.IntegerField(blank=True, null=True, verbose_name="销量上限"),
                ),
                (
                    "product_size",
                    models.IntegerField(blank=True, null=True, verbose_name="上架商品"),
                ),
                (
                    "conversion_rate_percent",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=10,
                        null=True,
                        verbose_name="带货转化率",
                    ),
                ),
                (
                    "flow_status",
                    models.IntegerField(blank=True, null=True, verbose_name="流量状态"),
                ),
                (
                    "barrage_status",
                    models.IntegerField(blank=True, null=True, verbose_name="弹幕状态"),
                ),
                (
                    "good_status",
                    models.IntegerField(blank=True, null=True, verbose_name="商品状态"),
                ),
                (
                    "create_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "直播数据总览",
                "verbose_name_plural": "直播数据总览",
            },
        ),
        migrations.CreateModel(
            name="SpiderTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("page", models.SmallIntegerField(verbose_name="页码")),
                ("status", models.SmallIntegerField(verbose_name="是否成功标志")),
                (
                    "task_type",
                    models.CharField(
                        choices=[("OD", "抖音"), ("AS", "快手")],
                        max_length=2,
                        verbose_name="下单方式",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="数据开始的时间")),
                ("end_date", models.DateField(verbose_name="数据结束的时间")),
                ("supplier_id", models.BigIntegerField(verbose_name="供应商id")),
                (
                    "supplier_name",
                    models.CharField(max_length=56, verbose_name="供应商名字"),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "Spider任务",
                "verbose_name_plural": "Spider任务",
            },
        ),
        migrations.AlterField(
            model_name="cmmflow",
            name="name",
            field=models.CharField(
                blank=True, max_length=50, null=True, verbose_name="主播"
            ),
        ),
        migrations.AlterField(
            model_name="cmmlivedetail",
            name="name",
            field=models.CharField(
                blank=True, max_length=50, null=True, verbose_name="主播"
            ),
        ),
        migrations.AddConstraint(
            model_name="spidertask",
            constraint=models.UniqueConstraint(
                fields=("supplier_id", "page", "start_date", "end_date"),
                name="unique_supplier_id_page_start_date_end_date",
            ),
        ),
        migrations.AddConstraint(
            model_name="cmmlivegoods",
            constraint=models.UniqueConstraint(
                fields=("room_id", "product_id"), name="unique_room_id_product_id"
            ),
        ),
    ]
