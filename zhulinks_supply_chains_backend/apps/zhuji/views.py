# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-08-31 16:45:10
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-12-25 13:39:48
# views.py
import json
import re
import traceback
import urllib.parse
import uuid
from datetime import datetime, timedelta

from django.core.exceptions import FieldError
from django.db import transaction
from django.db.models import (
    Sum,
    Count,
    F,
    Window,
    ExpressionWrapper,
    fields,
    Func,
    Q,
)
from django.db.models.functions import Rank
from django.http import FileResponse
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.request import Request
from rest_framework.views import APIView

from common.basic import DBAPIView, CommonAPIView
from common.basics.exceptions import APIViewException
from products.views.operator_product_views import find_product_by_picture
from users.models import User, UserDataSource, UserExtraAuth, UserInfos
from utils.download_tmp import LiveProductRankTmpl, get_excel_async, ZhujiProductListTmpl
from utils.feishu import FeiShuDocx
from utils.http_handle import (
    IResponse,
    custom_django_filter,
    custom_filter,
    convert_to_ware_time,
)
from utils.redis_lock import read_lock, gen_redis_conn
from zhuji import logger
from zhuji.filtersets import (
    CMMSearchTasksFilterSet,
    DataInsigntFilterSet,
    NewProductInspirationFilterSet,
    SelectionPlanCMMGoodsFilterSet,
    CmmLiveOverviewFilterSet,
    ProductLabelsFilterSet,
    DataInsigntProductFilterSet,
)
from .basic import CMM_TASK_LOCK_NAME, AuthAPIView, OKResponse, CMM_TASK_QUEUE_NAME
from .bulk_query import get_category_name_by_id
from .models import (
    SpiderTask,
    CMMFlow,
    CMMLiveDetail,
    CMMLiveGoods,
    CmmLiveOverview,
    Product,
    ProductCategoryList,
    CmmLiveOverviewDaily,
    LiveAuthor,
    SpiderCookie,
    ZhujiDBLiveComment,
    ProductLiveData,
    DataInsignt,
    CMMSearchTasks,
    FocusArea,
    DataInsigntLogs,
    NewProductInspiration,
    ProductLabels,
    ProductLabelRelate,
    ProductStyle,
    DataInsigntProduct,
)
from .serializers import (
    SpiderTaskSerializer,
    CMMFlowSerializer,
    CMMLiveDetailSerializer,
    CMMLiveGoodsSerializer,
    CmmLiveOverviewSerializer,
    ProductSerializer,
    ProductListSerializer,
    ProductDetailSerializer,
    CmmLiveOverviewDailySerializer,
    LiveAuthorSerializer,
    CmmLiveOverviewListSerializer,
    SpiderCookieSerializer,
    ProductLiveDataSerializer,
    SelectedProductListSerializer,
    DataInsigntCreateSer,
    DataInsigntQuerySer,
    DataInsigntUpdateSer,
    CMMSearchTasksSer,
    FocusAreaSerializer,
    ProductLiveDataRankSer,
    NewProductInspirationSer,
    NewProductInspirationListSer,
    NewProductInspirationDeatilSer,
    UserWatchCmmLiveOverviewListSer,
    UserWatchCmmLiveOverviewProductInfoSer,
    ProductLabelsListSer,
    DataInsigntProductListViewSer,
)
from .tasks import insert_live_comment, fetch_cmm_goods, send_msg_to_fs


class SpiderTaskList(APIView):
    """
    Spider任务
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        raw_params = request.query_params.copy()
        page_tasks, re_data, obj_qs = custom_filter(raw_params, SpiderTask)
        serializer = SpiderTaskSerializer(page_tasks, many=True)
        re_data["data"] = serializer.data
        return IResponse(data=re_data)

    def post(self, request):
        serializer = SpiderTaskSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data, code=200)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)


class SpiderTaskDetail(APIView):
    """
    Spider任务详情
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        try:
            return SpiderTask.objects.get(pk=pk)
        except SpiderTask.DoesNotExist:
            return None

    def get(self, request, pk):
        spider_task = self.get_object(pk)
        if not spider_task:
            return IResponse(code=400, message="data not found")
        serializer = SpiderTaskSerializer(spider_task)
        return IResponse(data=serializer.data)

    def patch(self, request, pk):
        spider_task = self.get_object(pk)
        if not spider_task:
            return IResponse(code=400, message="data not found")
        serializer = SpiderTaskSerializer(spider_task, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        spider_task = self.get_object(pk)
        if not spider_task:
            return IResponse(code=400, message="data not found")
        spider_task.delete()
        return IResponse(code=200)


class CMMFlowList(APIView):
    """
    直播流量分析
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        raw_params = request.query_params.copy()
        page_cmms_flow, re_data, obj_qs = custom_filter(raw_params, CMMFlow)
        serializer = CMMFlowSerializer(page_cmms_flow, many=True)
        re_data["data"] = serializer.data
        return IResponse(data=re_data)

    def post(self, request):
        """
        直播流量分析新增
        """
        room_id = request.data.get("room_id")
        time_node = request.data.get("time_node")
        try:
            instance = CMMFlow.objects.get(room_id=room_id, time_node=time_node)
        except CMMFlow.DoesNotExist:
            # 如果模型实例不存在，创建一个新的实例
            instance = None
        serializer = CMMFlowSerializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data, code=200)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)


class CMMFlowDetail(APIView):
    """
    直播流量分析详情
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, room_id, time_node):
        try:
            time_node = convert_to_ware_time(time_node)
            return CMMFlow.objects.get(room_id=room_id, time_node=time_node)
        except CMMFlow.DoesNotExist:
            return None

    def get(self, request, room_id, time_node):
        cmm = self.get_object(room_id, time_node)
        if not cmm:
            return IResponse(code=400, message="data not found")
        serializer = CMMFlowSerializer(cmm)
        return IResponse(data=serializer.data)

    def patch(self, request, room_id, time_node):
        cmm = self.get_object(room_id, time_node)
        if not cmm:
            return IResponse(code=400, message="data not found")
        serializer = CMMFlowSerializer(cmm, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, room_id, time_node):
        cmm = self.get_object(room_id, time_node)
        if not cmm:
            return IResponse(code=400, message="data not found")
        cmm.delete()
        return IResponse(status=200)


class CMMLiveDetailList(APIView):
    """
    直播详情数据
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        raw_params = request.query_params.copy()
        page_cmm_live, re_data, obj_qs = custom_filter(raw_params, CMMLiveDetail)
        serializer = CMMLiveDetailSerializer(page_cmm_live, many=True)
        re_data["data"] = serializer.data
        return IResponse(data=re_data)

    def post(self, request):
        """
        直播详情新增
        """
        author_id = request.data.get("author_id")
        begin_time = request.data.get("begin_time")
        try:
            instance = CMMLiveDetail.objects.get(author_id=author_id, begin_time=begin_time)
        except CMMLiveDetail.DoesNotExist:
            # 如果模型实例不存在，创建一个新的实例
            instance = None
        serializer = CMMLiveDetailSerializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data, code=200)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)


class CMMLiveDetailDetail(APIView):
    """
    直播详情数据-详情
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, author_id, begin_time):
        try:
            begin_time = convert_to_ware_time(begin_time)
            return CMMLiveDetail.objects.get(author_id=author_id, begin_time=begin_time)
        except CMMLiveDetail.DoesNotExist:
            return None

    def get(self, request, author_id, begin_time):
        live_detail = self.get_object(author_id, begin_time)
        if not live_detail:
            return IResponse(code=400, message="data not found")
        serializer = CMMLiveDetailSerializer(live_detail)
        return IResponse(data=serializer.data)

    def patch(self, request, author_id, begin_time):
        live_detail = self.get_object(author_id, begin_time)
        if not live_detail:
            return IResponse(code=400, message="data not found")
        serializer = CMMLiveDetailSerializer(live_detail, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, author_id, begin_time):
        live_detail = self.get_object(author_id, begin_time)
        if not live_detail:
            return IResponse(code=400, message="data not found")
        live_detail.delete()
        return IResponse(status=200)


class CMMLiveGoodsList(APIView):
    """
    直播商品
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        raw_params = request.query_params.copy()
        page_live_goods, re_data, obj_qs = custom_filter(raw_params, CMMLiveGoods)
        serializer = CMMLiveGoodsSerializer(page_live_goods, many=True)
        re_data["data"] = serializer.data
        return IResponse(data=re_data)

    def post(self, request):
        """
        直播商品新增
        """
        room_id = request.data.get("room_id")
        external_id = request.data.get("external_id")
        try:
            instance = CMMLiveGoods.objects.get(room_id=room_id, external_id=external_id)
        except CMMLiveGoods.DoesNotExist:
            # 如果模型实例不存在，创建一个新的实例
            instance = None
        serializer = CMMLiveGoodsSerializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data, status=200)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)


def get_rank_data(count_limit, raw_params):
    """
    获取直播商品排行榜数据
    """

    if raw_params.get("start_time__range"):
        time_range = json.loads(raw_params["start_time__range"])
        if time_range:
            start_first, start_second = time_range[0], time_range[1]
            # 高值需要加上29小时，然后重新写入参数
            first = (datetime.strptime(start_first, "%Y-%m-%d") + timedelta(hours=5, minutes=0, seconds=0)).strftime("%Y-%m-%d %H:%M:%S")
            second = (datetime.strptime(start_second, "%Y-%m-%d") + timedelta(hours=28, minutes=59, seconds=59)).strftime("%Y-%m-%d %H:%M:%S")
            raw_params["start_time__range"] = json.dumps([first, second])

    author_ids = json.loads(raw_params.get("author_id__in")) if raw_params.get("author_id__in") else []
    length = len(author_ids)
    if length > 0 and count_limit > 1000 // length:
        count_limit = 1000 // length

    page_live_goods, re_data, obj_qs = custom_filter(raw_params, CMMLiveGoods)
    top_lives = []

    # 未输入主播时
    if not author_ids:
        # top_live_goods = obj_qs.values("external_id").annotate(total_sales=Sum("sales")).order_by("-total_sales", "external_id")[:count_limit]
        top_live_goods = obj_qs.values("external_id", "volume").order_by(F("volume").desc(nulls_last=True), "external_id")[:count_limit]

        # 计算product live data
        if top_live_goods:
            top_lives.append(top_live_goods)
    else:
        for author_id in author_ids:
            top_live_goods = (
                obj_qs.filter(author_id=author_id)
                .values(
                    "author_id",
                    "external_id",
                    "name",
                    "start_time",
                    "introduce_duration",
                    "stop_time",
                    "volume",
                )
                .order_by(F("volume").desc(nulls_last=True), "external_id")[:count_limit]
            )

            if top_live_goods:
                top_lives.append(top_live_goods)

    # 获取商品信息

    product_external_id_set = {i["external_id"] for top_live_goods in top_lives for i in top_live_goods}
    products = Product.objects.filter(external_id__in=product_external_id_set).values(
        "external_id",
        "main_images",
        "product_title",
        "external_category",
        "price",
    )

    products_map = {p["external_id"]: p for p in products}

    category_ids_set = {i for p in products for i in p["external_category"] or []}
    category_list = ProductCategoryList.objects.filter(id__in=category_ids_set).only("id", "name")
    category_map = {c.id: {"id": c.id, "name": c.name} for c in category_list}

    re_data = []
    for top_live_goods in top_lives:
        for rank, item in enumerate(top_live_goods):
            rank += 1
            re_product = {}
            re_product["rank"] = rank
            re_product["external_id"] = item["external_id"]
            re_product["total_sales"] = item["volume"]
            re_product["author_id"] = item.get("author_id")
            re_product["author_name"] = item.get("name")
            re_product["user_count"] = None
            re_product["start_time"] = timezone.make_naive(item["start_time"], timezone.get_current_timezone()).strftime("%Y-%m-%d %H:%M:%S") if item.get("start_time") else None
            re_product["stop_time"] = timezone.make_naive(item["stop_time"], timezone.get_current_timezone()).strftime("%Y-%m-%d %H:%M:%S") if item.get("stop_time") else None
            _product = products_map.get(item["external_id"])
            if _product:
                re_product.update(_product)
                category = _product.get("external_category")
                if not category:
                    re_product["external_category"] = []
                else:
                    re_product["external_category"] = [category_map.get(int(i)) for i in category]

                # 获取讲解结束时的直播在线人数
                start_time = item.get("start_time")
                introduce_duration = item.get("introduce_duration")
                author_id = item.get("author_id")
                if start_time:
                    end_time = start_time + timedelta(hours=0, minutes=0, seconds=introduce_duration)
                else:
                    end_time = None

                class AbsSeconds(Func):
                    function = "ABS"
                    template = "%(function)s(EXTRACT(EPOCH FROM %(expressions)s))"

                if end_time:
                    # todo:优化
                    result = (
                        CMMFlow.objects.filter(author_id=author_id)
                        .annotate(
                            time_diff=ExpressionWrapper(F("time_node") - end_time, output_field=fields.DateTimeField()),
                            time_diff_abs=ExpressionWrapper(AbsSeconds(F("time_diff")), output_field=fields.FloatField()),
                        )
                        .order_by("time_diff_abs")
                        .values("user_count", "time_diff", "time_diff_abs")
                        .first()
                    )
                    # 计算时间差的表达式
                    if result:
                        re_product["user_count"] = result["user_count"]
            re_data.append(re_product)

    return re_data


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def cmm_live_goods_rank(request):
    """
    直播商品排行榜
    """
    try:
        count_limit = 20
        raw_params = request.query_params.copy()
        # 前端参数转换
        if raw_params.get("start_time"):
            raw_params["start_time__range"] = raw_params.pop("start_time")[0]
        if raw_params.get("limit"):
            count_limit = int(raw_params.pop("limit")[0])
        if raw_params.get("author_ids"):
            raw_params["author_id__in"] = raw_params.pop("author_ids")[0]

        if count_limit > 1000:
            count_limit = 1000
        re_data = get_rank_data(count_limit, raw_params)
        return IResponse(data=re_data)
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def cmm_live_goods_rank_download_async(request):
    """
    直播商品排行榜-下载(异步)
    """
    try:
        count_limit = 100
        raw_params = request.query_params.copy()
        # 前端参数转换
        if raw_params.get("start_time"):
            raw_params["start_time__range"] = raw_params.pop("start_time")[0]
        if raw_params.get("limit"):
            count_limit = int(raw_params.pop("limit")[0])
        if raw_params.get("author_ids"):
            raw_params["author_id__in"] = raw_params.pop("author_ids")[0]
        if count_limit > 1000:
            count_limit = 1000

        re_data = get_rank_data(count_limit, raw_params)

        new_data_list = []
        for item in re_data:
            new_data = {}
            # tmeplate data
            for k, v in LiveProductRankTmpl.items():
                value = item.get(k)
                new_data[v] = value
            # category
            category = item.get("external_category")
            name_key = {0: "一级类目", 1: "二级类目", 2: "三级类目", 3: "四级类目", 4: "五级类目"}
            if category:
                for i in range(len(category)):
                    new_data[f"{name_key[i]}ID"] = category[i].get("id")
                    new_data[f"{name_key[i]}名称"] = category[i].get("name")
                    if i == 4:
                        break
            new_data_list.append(new_data)
        filename = f"直播商品销量排行榜{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
        picture_fields = {"主图列表": 2}
        byte_buffer = get_excel_async("销量排行榜", new_data_list, image_columns=[3])
        if byte_buffer:
            byte_buffer.seek(0)
        return FileResponse(byte_buffer, filename=filename, as_attachment=True)
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=500, message=str(e))


class CMMLiveGoodsDetail(APIView):
    """
    直播商品详情
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, room_id, external_id):
        try:
            return CMMLiveGoods.objects.get(room_id=room_id, external_id=external_id)
        except CMMLiveGoods.DoesNotExist:
            return None

    def get(self, request, room_id, external_id):
        live_goods = self.get_object(room_id, external_id)
        if not live_goods:
            return IResponse(code=400, message="data not found")
        serializer = CMMLiveGoodsSerializer(live_goods)
        return IResponse(data=serializer.data)

    def patch(self, request, room_id, external_id):
        live_goods = self.get_object(room_id, external_id)
        if not live_goods:
            return IResponse(code=400, message="data not found")
        serializer = CMMLiveGoodsSerializer(live_goods, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, room_id, external_id):
        live_goods = self.get_object(room_id, external_id)
        if not live_goods:
            return IResponse(code=400, message="data not found")
        live_goods.delete()
        return IResponse(status=200)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_overview_for_chart(request):
    """
    直播数据总览列表-图表（直播间）
    """
    raw_params = request.query_params.copy()
    # 前端参数转换
    if raw_params.get("room_id"):
        raw_params["room_id__in"] = raw_params.pop("room_id")[0]
    if raw_params.get("begin_time"):
        raw_params["begin_time__range"] = raw_params.pop("begin_time")[0]

    # 需要展示的字段
    show_fields = raw_params.get("show_fields", [])
    if show_fields:
        show_fields = json.loads(raw_params.pop("show_fields")[0])
    hidden_fields = [
        "traffic_data",
        "volume_data",
    ]
    hidden_fields = set(hidden_fields) - set(show_fields)

    raw_params["orders"] = json.dumps(["-create_date"])
    page_overviews, re_data, obj_qs = custom_filter(raw_params, CmmLiveOverview)
    serializer = CmmLiveOverviewListSerializer(page_overviews, many=True, context={"hidden_fields": hidden_fields})
    re_data["data"] = serializer.data
    return IResponse(data=re_data)


class CmmLiveOverviewList(APIView):
    """
    直播数据总览
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        raw_params = request.query_params.copy()
        if raw_params.get("begin_time"):
            raw_params["begin_time__range"] = raw_params.pop("begin_time")[0]

        # 需要展示的字段
        show_fields = raw_params.get("show_fields", [])
        if show_fields:
            show_fields = json.loads(raw_params.pop("show_fields")[0])
        hidden_fields = [
            "traffic_data",
            "volume_data",
            "product_data",
            "explain_product_data",
        ]
        hidden_fields = set(hidden_fields) - set(show_fields)

        raw_params["orders"] = json.dumps(["-create_date"])
        page_overviews, re_data, obj_qs = custom_filter(raw_params, CmmLiveOverview)
        serializer = CmmLiveOverviewListSerializer(page_overviews, many=True, context={"hidden_fields": hidden_fields})
        re_data["data"] = serializer.data
        return IResponse(data=re_data)

    def post(self, request):
        """
        直播数据总览新增
        """
        # room_id = request.data.get("room_id")
        # try:
        #     instance = CmmLiveOverview.objects.get(room_id=room_id)
        # except CmmLiveOverview.DoesNotExist:
        #     # 如果模型实例不存在，创建一个新的实例
        #     instance = None
        # serializer = CmmLiveOverviewSerializer(instance, data=request.data, context={"hidden_fields": []})
        serializer = CmmLiveOverviewSerializer(data=request.data, context={"hidden_fields": []})
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data, status=200)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)


class CmmLiveOverviewDetail(APIView):
    """
    直播数据总览详情
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, room_id):
        try:
            return CmmLiveOverview.objects.get(room_id=room_id)
        except CmmLiveOverview.DoesNotExist:
            return None

    def get(self, request, room_id):
        overview = self.get_object(room_id)
        if not overview:
            return IResponse(code=400, message="data not found")
        serializer = CmmLiveOverviewSerializer(overview, context={"hidden_fields": []})
        return IResponse(data=serializer.data)

    def patch(self, request, room_id):
        overview = self.get_object(room_id)
        if not overview:
            return IResponse(code=400, message="data not found")
        serializer = CmmLiveOverviewSerializer(overview, data=request.data, partial=True, context={"hidden_fields": []})
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, room_id):
        overview = self.get_object(room_id)
        if not overview:
            return IResponse(code=400, message="data not found")
        overview.delete()
        return IResponse(status=200)


class ProductCategoryView(APIView):
    """
    商品分类数据
    """

    permission_classes = [AllowAny]

    def get(self, request):
        def get_obj(obj):
            res = {
                "id": obj.id,
                "name": obj.name,
                "subs": [],
            }
            return res

        data = []
        first_levels = ProductCategoryList.objects.filter(parent__isnull=True)
        for first_level in first_levels:
            f_obj = get_obj(first_level)
            second_levels = first_level.subs.all()

            for second_level in second_levels:
                s_obj = get_obj(second_level)
                f_obj["subs"].append(s_obj)
                third_levels = second_level.subs.all()
                for third_level in third_levels:
                    t_obj = get_obj(third_level)
                    s_obj["subs"].append(t_obj)
                    fourth_levels = third_level.subs.all()
                    for fourth_level in fourth_levels:
                        fo_obj = get_obj(fourth_level)
                        t_obj["subs"].append(fo_obj)

            data.append(f_obj)
        return IResponse(data=data)


def check_and_update_category(category_detail):
    """
    检查并更新类目
    Args:
        "category_detail": {
            "first_cid": 20023,
            "first_cname": "翡翠/和田玉/琥珀蜜蜡/其他玉石",
            "second_cid": 20407,
            "second_cname": "和田玉",
            "third_cid": 34852,
            "third_cname": "手镯",
            "fourth_cid": 0,
            "fourth_cname": "",
        },

    Returns:
        category_ids: []
    """
    category_ids = []
    parent_id = category_detail.get("first_cid")
    parent_value = category_detail.get("first_cname")
    if not parent_id or not parent_value:
        raise ValueError("first_cid and first_cname required")
    parent, _ = ProductCategoryList.objects.update_or_create(id=parent_id, defaults={"name": parent_value})
    category_ids.append(parent_id)

    second_id = category_detail.get("second_cid")
    second_value = category_detail.get("second_cname")
    if not second_id or not second_value:
        return category_ids
    second, _ = ProductCategoryList.objects.update_or_create(id=second_id, defaults={"name": second_value, "parent": parent})
    category_ids.append(second_id)

    third_id = category_detail.get("third_cid")
    third_value = category_detail.get("third_cname")
    if not third_id or not third_value:
        return category_ids
    third, _ = ProductCategoryList.objects.update_or_create(id=third_id, defaults={"name": third_value, "parent": second})
    category_ids.append(third_id)

    fourth_id = category_detail.get("fourth_cid")
    fourth_value = category_detail.get("fourth_cname")
    if not fourth_id or not fourth_value:
        return category_ids
    third, _ = ProductCategoryList.objects.update_or_create(id=fourth_id, defaults={"name": fourth_value, "parent": third})
    category_ids.append(third_id)
    return category_ids


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def selected_product_view(request):
    """
    珠玑选品商品池
    """
    try:
        re_data = {}
        current_user = request.user
        if request.auth.get("user_type") != "OP":
            return IResponse(code=403, message="no permission to operate")

        raw_params = request.query_params.copy()

        # 前端参数转换
        if raw_params.get("product_ids"):
            raw_params["product_id__in"] = raw_params.pop("product_ids")[0]
        if raw_params.get("price"):
            raw_params["price__range"] = raw_params.pop("price")[0]
        if raw_params.get("inventory"):
            raw_params["inventory__range"] = raw_params.pop("inventory")[0]
        if raw_params.get("sales"):
            raw_params["sales__range"] = raw_params.pop("sales")[0]
        if raw_params.get("listed_at"):
            raw_params["listed_at__range"] = raw_params.pop("listed_at")[0]

        page_products, re_data, obj_qs = custom_filter(
            raw_params,
            Product,
            array_fields=["external_category"],
            like_fields=["product_title", "shop_name", "search_keywords"],
        )
        for product_obj in page_products:
            product = SelectedProductListSerializer(instance=product_obj, context={"request": request})
            data = product.data
            category = data.get("external_category")
            if not category:
                data["external_category"] = []
            else:
                category_list = get_category_name_by_id(category)
                data["external_category"] = category_list
            re_data["data"].append(data)
        return IResponse(data=re_data)
    except FieldError as e:
        logger.error(f"{e}, {traceback.format_exc()}")
        return IResponse(message=str(e), code=400)
    except Exception as e:
        logger.error(f"{e}, {traceback.format_exc()}")
        return IResponse(message=str(e), code=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def find_product_by_picture_view(request, product_id):
    try:
        re_data = {}
        current_user = request.user
        if request.auth.get("user_type") not in ["OP", "DB"]:
            return IResponse(code=403, message="no permission to operate")
        raw_params = request.query_params.copy()
        # 获取商品
        try:
            product = Product.objects.get(product_id=product_id)
        except Product.DoesNotExist:
            return IResponse(code=400, message="data not found")
        if not product.main_images:
            return IResponse(code=400, message="product main image not found")
        image_url = product.main_images[0]
        if not image_url.startswith("https://zhulinks.oss"):
            return IResponse(code=400, message="product main image url not valid")
        raw_params = request.query_params.copy()
        raw_params["image_url"] = image_url
        # 以图搜图
        re_data = find_product_by_picture(request, raw_params)
        return IResponse(data=re_data)
    except Exception as e:
        logger.error(f"{e}, {traceback.format_exc()}")
        return IResponse(message=str(e), code=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def product_list_download(request):
    """
    商品列表下载
    """
    try:
        raw_params = request.query_params.copy()
        # 前端参数转换
        # product_ids 为选品下载专用
        if raw_params.get("product_ids"):
            raw_params["product_id__in"] = raw_params.pop("product_ids")[0]
        if raw_params.get("price"):
            raw_params["price__range"] = raw_params.pop("price")[0]
        if raw_params.get("inventory"):
            raw_params["inventory__range"] = raw_params.pop("inventory")[0]
        if raw_params.get("sales"):
            raw_params["sales__range"] = raw_params.pop("sales")[0]
        if raw_params.get("listed_at"):
            raw_params["listed_at__range"] = raw_params.pop("listed_at")[0]
        if raw_params.get("create_date"):
            raw_params["create_date__range"] = raw_params.pop("create_date")[0]

        page_products, re_data, obj_qs = custom_filter(
            raw_params,
            Product,
            array_fields=["external_category"],
            like_fields=["product_title", "shop_name", "search_keywords"],
        )
        data_list = ProductListSerializer(page_products, many=True).data
        new_data_list = []
        for data in data_list:
            new_data = {}
            # tmeplate data
            for k, v in ZhujiProductListTmpl.items():
                value = data.get(k)
                if value and k == "source_platform":
                    for i in Product.SOURCE_PLATFORM_CHOICE:
                        if i[0] == value:
                            new_data[v] = i[1]
                            continue
                else:
                    new_data[v] = value
            # category
            category = data.get("external_category")
            if category:
                category_list = get_category_name_by_id(category)
                for i in range(len(category_list)):
                    new_data[f"{i+1}级类目ID"] = category_list[i].get("id")
                    new_data[f"{i+1}级类目名称"] = category_list[i].get("name")
            new_data_list.append(new_data)
        filename = f"珠玑商品列表{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
        byte_buffer = get_excel_async("商品", new_data_list, image_columns=[3])
        if byte_buffer:
            byte_buffer.seek(0)
        return FileResponse(byte_buffer, filename=filename, as_attachment=True)
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=500, message=str(e))


class ProductList(APIView):
    """
    商品
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        raw_params = request.query_params.copy()
        # 前端参数转换
        if raw_params.get("price"):
            raw_params["price__range"] = raw_params.pop("price")[0]
        if raw_params.get("inventory"):
            raw_params["inventory__range"] = raw_params.pop("inventory")[0]
        if raw_params.get("sales"):
            raw_params["sales__range"] = raw_params.pop("sales")[0]
        if raw_params.get("listed_at"):
            raw_params["listed_at__range"] = raw_params.pop("listed_at")[0]
        if raw_params.get("create_date"):
            raw_params["create_date__range"] = raw_params.pop("create_date")[0]

        page_products, re_data, obj_qs = custom_filter(
            raw_params,
            Product,
            array_fields=["external_category"],
            like_fields=["product_title", "shop_name", "search_keywords"],
        )
        serializer = ProductListSerializer(page_products, many=True)
        data_list = serializer.data
        for data in data_list:
            category = data.get("external_category")
            if not category:
                data["external_category"] = []
                continue

            category_list = get_category_name_by_id(category)
            data["external_category"] = category_list
        re_data["data"] = data_list
        return IResponse(data=re_data)

    def post(self, request):
        """
        新增商品
        Args:
            "category_detail": {
                "first_cid": 20023,
                "first_cname": "翡翠/和田玉/琥珀蜜蜡/其他玉石",
                "second_cid": 20407,
                "second_cname": "和田玉",
                "third_cid": 34852,
                "third_cname": "手镯",
                "fourth_cid": 0,
                "fourth_cname": "",
            },

        Returns:
            _type_: _description_
        """
        try:
            data = request.data
            if not data.get("category_detail"):
                category_detail = None
            else:
                category_detail = data.pop("category_detail")
                if not category_detail:
                    category_detail = None
            if not category_detail:
                data["external_category"] = []
            else:
                external_category = check_and_update_category(category_detail)
                data["external_category"] = external_category
            # 排除sales、sales_amount，此二值通过直播商品进行计算
            pop_fields = ("sales", "sales_amount")
            for field in pop_fields:
                if data.get(field):
                    data.pop(field)
            # 标签
            post_labels = data.pop("labels", None)
            if post_labels is not None and not isinstance(post_labels, list):
                return IResponse(code=400, message="标签类型错误")
            # 外部商品id
            external_id = data.get("external_id")
            try:
                instance = Product.objects.get(external_id=external_id)
            except Product.DoesNotExist:
                # 如果模型实例不存在，创建一个新的实例
                instance = None

            # 从product_link找product_id
            product_link = data.get("product_link")
            if product_link:
                ret = re.findall("\\?id=(.*?)&", product_link)
                if ret:
                    data["dy_product_id"] = ret[0]

            serializer = ProductSerializer(instance, data=data)
            if not serializer.is_valid():
                return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)

            with transaction.atomic():
                product = serializer.save()

                # 创建款式
                if product.style:
                    ProductStyle.objects.get_or_create(name=product.style)

                # 删除原有的标签关联
                product.productlabelrelate_set.all().delete()
                # 新增标签
                result_dict = {}
                if post_labels is not None:
                    clean_labels = [str(i).strip() for i in post_labels]
                    existing_labels = ProductLabels.objects.filter(name__in=clean_labels).values_list("name", "id")
                    existing_labels_dict = dict(existing_labels)

                    # 准备要插入的新标签列表
                    new_labels = []

                    for label_name in clean_labels:
                        if label_name not in existing_labels_dict:
                            new_labels.append(ProductLabels(name=label_name, label_type=1))
                        else:
                            result_dict[label_name] = existing_labels_dict[label_name]

                    # 批量插入新标签
                    if new_labels:
                        ProductLabels.objects.bulk_create(new_labels)
                        # 查询新插入的标签的 primary_key
                        new_labels_dict = {label.name: label.id for label in ProductLabels.objects.filter(name__in=[label.name for label in new_labels])}

                        # 合并结果
                        result_dict.update(new_labels_dict)

                    if result_dict:
                        label_id_list = result_dict.values()
                        ProductLabelRelate.objects.bulk_create([ProductLabelRelate(product=product, label_id=label_id) for label_id in label_id_list])

            return IResponse(data=serializer.data, code=200)
        except ValueError as e:
            return IResponse(code=400, message=str(e))


class ProductDetail(APIView):
    """
    商品详情
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, product_id):
        try:
            return Product.objects.get(product_id=product_id)
        except Product.DoesNotExist:
            return None

    def get(self, request, product_id):
        product = self.get_object(product_id)
        if not product:
            return IResponse(code=400, message="data not found")
        serializer = ProductDetailSerializer(product)
        data = serializer.data
        category = data.get("external_category")
        if not category:
            data["external_category"] = []
        else:
            category_list = get_category_name_by_id(category)
            data["external_category"] = category_list
        return IResponse(data=data)

    def patch(self, request, product_id):
        product = self.get_object(product_id)
        if not product:
            return IResponse(code=400, message="data not found")
        serializer = ProductSerializer(product, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, product_id):
        product = self.get_object(product_id)
        if not product:
            return IResponse(code=400, message="data not found")
        product.delete()
        return IResponse(code=200)


class CmmLiveOverviewDailyList(APIView):
    """
    每日数据总览
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """{
            author_id_1:[
                {
                    "the_date": "2023-09-12",
                    "volume": 100,
                    "watch_cnt": 1000,
                    "amount": 500.50,
                    "avg_volume": 10,
                    "avg_watch_cnt": 100,
                    "avg_amount": 50.25
                },
                {
                    "the_date": "2023-09-13",
                    "volume": 100,
                    "watch_cnt": 1000,
                    "amount": 500.50,
                    "avg_volume": 10,
                    "avg_watch_cnt": 100,
                    "avg_amount": 50.25
                },
            ]
        }
        """
        try:
            raw_params = request.query_params.copy()
            # 前端参数转换
            if raw_params.get("author_id"):
                raw_params["author_id__in"] = raw_params.pop("author_id")[0]
            if raw_params.get("the_date"):
                raw_params["the_date__range"] = raw_params.pop("the_date")[0]

            page_products, _, obj_qs = custom_filter(raw_params, CmmLiveOverviewDaily)
            serializer = CmmLiveOverviewDailySerializer(obj_qs, many=True)
            re_data = {}
            for data in serializer.data:
                author_id = data.pop("author_id")
                if author_id in re_data:
                    re_data[author_id].append(data)
                else:
                    re_data[author_id] = [data]

            return IResponse(data=re_data)
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))

    def post(self, request):
        author_id = request.data.get("author_id")
        the_date = request.data.get("the_date")
        try:
            instance = CmmLiveOverviewDaily.objects.get(author_id=author_id, the_date=the_date)
        except CmmLiveOverviewDaily.DoesNotExist:
            # 如果模型实例不存在，创建一个新的实例
            instance = None
        serializer = CmmLiveOverviewDailySerializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data, code=200)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)


class CmmLiveOverviewDailyDetail(APIView):
    """
    每日数据总览详情
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, author_id, the_date):
        try:
            return CmmLiveOverviewDaily.objects.get(author_id=author_id, the_date=the_date)
        except CmmLiveOverviewDaily.DoesNotExist:
            return None

    def get(self, request, author_id, the_date):
        object = self.get_object(author_id, the_date)
        if not object:
            return IResponse(code=400, message="data not found")
        serializer = CmmLiveOverviewDailySerializer(object)
        return IResponse(data=serializer.data)

    def patch(self, request, author_id, the_date):
        object = self.get_object(author_id, the_date)
        if not object:
            return IResponse(code=400, message="data not found")
        serializer = CmmLiveOverviewDailySerializer(object, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, author_id, the_date):
        object = self.get_object(author_id, the_date)
        if not object:
            return IResponse(code=400, message="data not found")
        object.delete()
        return IResponse(code=200)


class FocusAreaList(APIView):
    def get(self, request):
        focus_areas = FocusArea.objects.all()
        serializer = FocusAreaSerializer(focus_areas, many=True)
        return IResponse(data=serializer.data)

    def post(self, request):
        serializer = FocusAreaSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data)
        return IResponse(code=400, message=str(serializer.errors))


class LiveAuthorList(APIView):
    """
    主播
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        _, _, obj_qs = custom_filter(request.query_params, LiveAuthor, force_orders=False)
        serializer = LiveAuthorSerializer(obj_qs, many=True)
        return IResponse(data=serializer.data)

    def post(self, request):
        raw_data = request.data
        re_data = []
        for data in raw_data:
            author_id = data.get("author_id")
            try:
                instance = LiveAuthor.objects.get(author_id=author_id)
            except LiveAuthor.DoesNotExist:
                # 如果模型实例不存在，创建一个新的实例
                instance = None
            serializer = LiveAuthorSerializer(instance, data=data)
            if serializer.is_valid():
                new_ins = serializer.save()
                re_data.append(LiveAuthorSerializer(instance=new_ins).data)
        return IResponse(data=re_data, status=200)


class LiveAuthorDetail(APIView):
    """
    主播详情
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, author_id):
        try:
            return LiveAuthor.objects.get(author_id=author_id)
        except LiveAuthor.DoesNotExist:
            return None

    def get(self, request, author_id):
        object = self.get_object(author_id)
        if not object:
            return IResponse(code=400, message="data not found")
        serializer = LiveAuthorSerializer(object)
        return IResponse(data=serializer.data)

    def patch(self, request, author_id):
        object = self.get_object(author_id)
        if not object:
            return IResponse(code=400, message="data not found")
        serializer = LiveAuthorSerializer(object, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, author_id):
        object = self.get_object(author_id)
        if not object:
            return IResponse(code=400, message="data not found")
        object.delete()
        return IResponse(code=200)


class SpiderCookieListView(APIView):
    """
    Spider任务cookie
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        raw_params = request.query_params.copy()
        page_products, re_data, obj_qs = custom_filter(raw_params, SpiderCookie)
        serializer = SpiderCookieSerializer(page_products, many=True)
        re_data["data"] = serializer.data
        return IResponse(data=re_data)

    def post(self, request):
        task_type = request.data.get("task_type")
        try:
            instance = SpiderCookie.objects.get(task_type=task_type)
        except SpiderCookie.DoesNotExist:
            # 如果模型实例不存在，创建一个新的实例
            instance = None

        serializer = SpiderCookieSerializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data, code=200)
        return IResponse(message=str(serializer.errors), code=400)


class SpiderCookieDetailView(APIView):
    """
    Spider任务详情
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, task_type):
        try:
            return SpiderCookie.objects.get(task_type=task_type)
        except SpiderCookie.DoesNotExist:
            return None

    def get(self, request, task_type):
        spider_cookie = self.get_object(task_type)
        if spider_cookie:
            serializer = SpiderCookieSerializer(spider_cookie)
            return IResponse(data=serializer.data)
        return IResponse(code=400, message="data not found")

    def patch(self, request, task_type):
        spider_cookie = self.get_object(task_type)
        if spider_cookie:
            serializer = SpiderCookieSerializer(spider_cookie, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return IResponse(data=serializer.data)
            return IResponse(message=str(serializer.errors), code=400)
        return IResponse(code=400, message="data not found")

    def delete(self, request, task_type):
        spider_cookie = self.get_object(task_type)
        if spider_cookie:
            spider_cookie.delete()
            return IResponse(code=200)
        return IResponse(code=400, message="data not found")


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def download_live_comment_detail_view(request):
    """
    下载直播场次的弹幕统计详情
    """
    try:
        raw_params = request.query_params.copy()
        params = {}
        for k, v in raw_params.items():
            v = urllib.parse.unquote_plus(v)
            if k.find("__range") != -1 or k.find("__in") != -1:
                v = json.loads(v)
            if len(v) != 0:
                params[k] = v

        begin_time = ""
        author_id = params.get("author_id")
        if params.get("begin_time"):
            time_range = json.loads(params.pop("begin_time"))
            assert len(time_range) == 2, "begin_time is a range include start date and end date"
            if time_range:
                begin_first, begin_second = time_range[0], time_range[1]
                # 高值需要加上29小时，然后重新写入参数
                if begin_first:
                    first = (datetime.strptime(begin_first, "%Y-%m-%d") + timedelta(hours=5, minutes=0, seconds=0)).strftime("%Y-%m-%d %H:%M:%S")
                    params["begin_time__gte"] = convert_to_ware_time(first)
                    begin_time += begin_first + "-"
                if begin_second:
                    second = (datetime.strptime(begin_second, "%Y-%m-%d") + timedelta(hours=28, minutes=59, seconds=59)).strftime("%Y-%m-%d %H:%M:%S")
                    params["begin_time__lte"] = convert_to_ware_time(second)
                    begin_time += begin_second

        re_data = (
            ZhujiDBLiveComment.zhuji_db()
            .filter(**params)
            .values("material", "style", "word")
            .annotate(word_count=Count("word"))
            .order_by("-word_count")
            .annotate(rank=Window(expression=Rank(), order_by=F("word_count").desc()))
        )
        new_data_list = []
        for item in re_data:
            new_data = {}
            new_data["排行"] = item.get("rank")
            new_data["材质"] = item.get("material")
            new_data["款式"] = item.get("style")
            new_data["关键词"] = item.get("word")
            new_data["频次"] = item.get("word_count")
            new_data_list.append(new_data)
        author_name = "全量"
        if author_id:
            live_author = LiveAuthor.objects.filter(author_id=author_id).first()
            if live_author:
                author_name = live_author.name
            else:
                author_name = author_id

        if begin_time:
            filename = f"直播弹幕关键词-{author_name}-{begin_time}.xlsx"
        else:
            filename = f"直播弹幕关键词-{author_name}.xlsx"
        byte_buffer = get_excel_async("弹幕词频排行", new_data_list, image_columns=[])
        if byte_buffer:
            byte_buffer.seek(0)
        return FileResponse(byte_buffer, filename=filename, as_attachment=True)
    except Exception as e:
        logger.info(f"{str(e)}--{traceback.format_exc()}")
        return IResponse(code=500, message=str(e))


class ZhujiDBLiveCommentListView(APIView):
    """
    弹幕
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        获取弹幕直播场次列表
        """
        result = (
            ZhujiDBLiveComment.zhuji_db()
            .values("room_id", "author_id", "author_name", "begin_time")
            .annotate(
                comment_count=Count("comment"),
            )
        )
        re_data = []
        for i in result:
            re_data.append(i)

        return IResponse(data=re_data)

    def post(self, request):
        """
        新增弹幕数据
        room_id: author_id+begin_time
        e.g.3852285068190743_2023_10_12_19_00_00
        """
        raw_data = request.data
        assert isinstance(raw_data, list), "data type should be array"
        data_len = len(raw_data)
        page_size = 100
        for i in range(0, data_len, page_size):
            data_list = raw_data[i : i + page_size]
            insert_live_comment.delay(data_list)
        return IResponse(code=200)


class ProductLiveDataAPIView(APIView):
    """
    商品的直播数据

    在珠玑商品列表，点击销量接口查看相关数据
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, product_id):
        """
        获取指定商品的直播数据

        Args:
            request (_type_): _description_
            product_id (_type_): 商品ID或商品外部ID

        Returns:
            _type_: _description_
        """
        try:
            product = Product.objects.get(Q(product_id__iexact=product_id) | Q(external_id__iexact=product_id))
        except Product.DoesNotExist:
            return IResponse(code=400, message="product not found")

        raw_params = request.query_params.copy()
        # 前端参数转换
        if raw_params.get("live_date"):
            raw_params["live_date__range"] = raw_params.pop("live_date")[0]
        extra_dict = {"product_id": product.id}
        page_live_data, re_data, obj_qs = custom_filter(raw_params, ProductLiveData, **extra_dict)
        serializer = ProductLiveDataSerializer(page_live_data, many=True)
        re_data["data"] = serializer.data
        return IResponse(data=re_data)

    def post(self, request, product_id):
        """
        新增指定商品的直播数据

        Args:
            request (_type_): _description_
            product_id (_type_): 商品ID或商品外部ID

        Returns:
            _type_: 返回此次新增直播数据
        """
        re_data = []
        try:
            product = Product.objects.get(Q(product_id__iexact=product_id) | Q(external_id__iexact=product_id))
        except Product.DoesNotExist:
            return IResponse(code=400, message="product not found")

        for data in request.data:
            live_data, _ = ProductLiveData.objects.update_or_create(product=product, live_date=data.pop("live_date"), defaults=data)
            re_data.append({"live_date": live_data.live_date, "volume": live_data.volume, "price": live_data.price, "sales_amount": live_data.sales_amount})
        return IResponse(data=re_data)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def DY_product_sales_rank(request):
    """
    DY销量榜
    """

    try:
        current_user = request.user
        if not current_user.can_view_zhuji_rank:
            return IResponse(code=403, message="no permission to operate")

        live_date = request.query_params.get("live_date")
        _query = Q()
        if live_date:
            live_date = json.loads(live_date)
            assert isinstance(live_date, list) and len(live_date) == 2, "live_date should be a array"
            _query = Q(live_date__gte=live_date[0]) & Q(live_date__lte=live_date[1])

        volume_qs = (
            ProductLiveData.objects.filter(_query)
            .select_related("product")
            .values("product__product_id", "product__main_images", "product__product_title", "product__price", "product__external_category")
            .annotate(hot_volume=Sum("volume"))
            .order_by("-hot_volume")
        )

        re_data, _, qs = custom_django_filter(request, volume_qs, iserializer=ProductLiveDataRankSer, need_serialize=True, force_order=False)

        return IResponse(data=re_data)
    except Exception as e:
        logger.error(f"DY_product_sales_rank: {str(e)}--{traceback.format_exc()}")
        return IResponse(code=500, message=str(e))


class ProductLiveDataDetailAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, product_id, live_date):
        """
        删除指定商品的指定时间的直播数据
        Args:
            request (_type_): _description_
            product_id (_type_): 商品ID或商品外部ID
            live_date (_type_): 直播日期

        Returns:
            _type_: _description_
        """
        try:
            product = Product.objects.get(Q(product_id__iexact=product_id) | Q(external_id__iexact=product_id))
        except Product.DoesNotExist:
            return IResponse(code=400, message="product not found")

        try:
            instance = ProductLiveData.objects.get(product=product, live_date=live_date)
        except ProductLiveData.DoesNotExist:
            return IResponse(code=400, message="data not found")
        instance.delete()
        return IResponse(code=200)


class DataInsigntView(AuthAPIView):
    model = DataInsignt

    def get(self, request: Request):
        """
        洞察日志列表
        """
        # 列表queryset
        # raw_params = request.query_params.copy()
        # # 强制排序
        # raw_params["orders"] = raw_params.get("orders", '["-log_date"]')
        # page_sources, re_data, _ = custom_filter(raw_params, self.model, is_deleted=False)
        # list_ser = DataInsigntQuerySer(instance=page_sources, many=True, context={"request": request})
        # re_data["data"] = list_ser.data
        insignts = (
            DataInsignt.objects.filter(is_deleted=False)
            .annotate(
                has_read=Count("datainsigntlogs", filter=Q(datainsigntlogs__user=request.user)),
            )
            .order_by("-log_date")
        )
        re_data, _, _ = custom_django_filter(request, insignts, DataInsigntFilterSet, DataInsigntQuerySer, order_fields=[])

        return OKResponse(data=re_data)

    def post(self, request: Request):
        """
        创建洞察日志
        """
        raw_data = request.data
        raw_data["creator"] = self.current_user.pk
        ser = DataInsigntCreateSer(data=raw_data)
        ser.is_valid(raise_exception=True)
        ser.save()
        return OKResponse(data=ser.data)


class DataInsigntDetailView(AuthAPIView):
    model = DataInsignt

    def get(self, request: Request, log_id: int):
        """
        获取洞察日志详情
        """
        target_obj = (
            DataInsignt.objects.filter(is_deleted=False)
            .annotate(
                has_read=Count("datainsigntlogs", filter=Q(datainsigntlogs__user=request.user)),
            )
            .first()
        )
        ser = DataInsigntQuerySer(instance=target_obj, many=False, context={"request": request})
        re_data = ser.data
        return OKResponse(data=re_data)

    def put(self, request: Request, log_id: int):
        """
        更新洞察日志
        """
        target_obj = self.get_object(log_id, raise_exection=True)
        update_ser = DataInsigntUpdateSer(instance=target_obj, data=request.data)
        update_ser.is_valid(raise_exception=True)
        update_ser.save()
        return OKResponse(data=update_ser.data)

    def delete(self, request: Request, log_id: int):
        """
        删除单个洞察日志
        """
        # is_deleted 不固定写get_object方法中，有些model不适用basemodel
        target_obj = self.get_object(log_id, raise_exection=True)
        # 逻辑删除
        target_obj.is_deleted = True
        target_obj.save()
        return OKResponse()


class CMMGoodsSearchView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request: Request):
        return IResponse()

        keyword = request.query_params.get("keyword")
        sales_range = request.query_params.get("sales_range") or ""
        price_range = request.query_params.get("price_range") or ""

        if not keyword:
            return IResponse(code=400, message="请输入搜索关键词")

        current_user_id = request.user.user_id

        today = datetime.now().date()

        search_record = CMMSearchTasks.objects.filter(
            query_date=today,
            keyword=keyword,
            sales_range=sales_range,
            price_range=price_range,
        ).first()
        # 是否为自己创建的任务,已经完成的
        if search_record and search_record.create_user == str(current_user_id):
            if search_record.state in [0, 1]:
                return IResponse(
                    code=200,
                    data={
                        "task_id": search_record.task_id,
                        "state": 1,
                        "form_link": search_record.form_link,
                        "message": "相同任务正在运行, 请勿重复提交",
                    },
                )

            if search_record.state == 2:
                return IResponse(
                    code=200,
                    data={
                        "task_id": search_record.task_id,
                        "state": 2,
                        "form_link": search_record.form_link,
                    },
                )

        task_id = timezone.now().strftime("%Y%m%d%H%M%S") + uuid.uuid4().hex

        # 保存为一个任务
        new_task = CMMSearchTasks(
            query_date=today,
            keyword=keyword,
            sales_range=sales_range,
            price_range=price_range,
            task_id=task_id,
            notice_users=[current_user_id],
            create_user=current_user_id,
        )
        if search_record and search_record.state == 2:
            # 成功，返回form_link, 提醒用户
            new_task.state = 2
            new_task.form_link = search_record.form_link

            # todo:notify user
            cli = FeiShuDocx()
            feishu_response = cli.get_user_id(request.user.mobile)
            feishu_user_list = feishu_response["data"]["user_list"]

            user_data = feishu_user_list[0]
            if not user_data.get("user_id"):
                new_task.remark = f"飞书无法获取该用户信息, {feishu_user_list}"
            else:
                UserExtraAuth.objects.update_or_create(
                    user_id=request.user,
                    data_source=UserDataSource.FEISHU,
                    defaults={"extenal_id": user_data.get("user_id")},
                )
                send_msg_to_fs.delay(user_data.get("user_id"), search_record.form_link)
            new_task.save()
            return IResponse(code=200, data={"state": 2, "task_id": task_id, "form_link": search_record.form_link})

        new_task.save()

        # task q
        redis_cli = gen_redis_conn()
        if read_lock(CMM_TASK_LOCK_NAME):
            q_counts = redis_cli.llen(CMM_TASK_QUEUE_NAME)
            redis_cli.lpush(CMM_TASK_QUEUE_NAME, task_id)
            return IResponse(code=200, data={"task": task_id, "state": 1, "message": f"任务已在队列. 前面有{q_counts}个任务正在排队"})
        else:
            request_url = request._request.build_absolute_uri()
            notify_url = request_url.split("?")[0] + "/notify"
            fetch_cmm_goods.delay(task_id, notify_url)
            return IResponse(code=200, data={"task_id": task_id, "state": 1, "message": "任务正在运行,请稍后查看任务状态."})


class CMMGoodsSearchNotifyView(APIView):
    """外部搜索 飞书回调函数"""

    permission_classes = [IsAuthenticated]

    def put(self, request: Request):
        return IResponse()
        task_id = request.data.get("task_id") or ""
        form_link = request.data.get("form_link") or ""
        if not all([task_id, form_link]):
            return IResponse(code=400, message="数据缺失")
        try:
            task = CMMSearchTasks.objects.get(task_id=task_id)
        except CMMSearchTasks.DoesNotExist:
            return IResponse(code=400, message="data not found")

        # 发送通知
        tasks = CMMSearchTasks.objects.filter(
            query_date=task.query_date,
            keyword=task.keyword,
            sales_range=task.sales_range,
            price_range=task.price_range,
            create_date__gte=task.create_date,
        )

        tasks.update(form_link=form_link, notify_date=timezone.now())

        # 映射
        need_notify_users = set([u_id for task in tasks for u_id in task.notice_users])
        zhulink_users = User.objects.filter(user_id__in=need_notify_users).only("user_id", "mobile")

        mobile_list = []
        user_mobile_map = {}
        for zhulink_user in zhulink_users:
            mobile_list.append(zhulink_user.mobile)
            user_mobile_map[zhulink_user.mobile] = zhulink_user

        # 获取飞书的用户信息
        cli = FeiShuDocx()
        feishu_response = cli.get_user_id(mobile_list)
        feishu_user_list = feishu_response["data"]["user_list"]

        feishu_user_map = {feishu_user["mobile"]: feishu_user.get("user_id") for feishu_user in feishu_user_list if feishu_user.get("user_id")}

        for mobile, zhulink_user in user_mobile_map.items():
            if feishu_user_map.get(mobile):
                UserExtraAuth.objects.update_or_create(
                    user_id=zhulink_user,
                    data_source=UserDataSource.FEISHU,
                    defaults={"extenal_id": feishu_user_map[mobile]},
                )

                send_msg_to_fs.delay(feishu_user_map[mobile], form_link)
        return IResponse()


class CMMSearchTasksListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request: Request):
        return IResponse()
        tasks = CMMSearchTasks.objects.all()
        re_data, _, _ = custom_django_filter(request, tasks, CMMSearchTasksFilterSet, CMMSearchTasksSer, need_serialize=True)
        return IResponse(data=re_data)


class CMMSearchTasksDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request: Request, task_id: str):
        return IResponse()
        try:
            task = CMMSearchTasks.objects.get(task_id=task_id)
        except CMMSearchTasks.DoesNotExist:
            return IResponse(code=400, message="data not found")

        ser = CMMSearchTasksSer(instance=task, many=False)
        return IResponse(data=ser.data)


class DataInsigntLogsView(APIView):
    """洞察日志提醒，已读、未读提醒"""

    permission_classes = [IsAuthenticated]

    def post(self, request: Request):
        current_user = request.user
        data_insignt_id = request.data.get("id")
        if not data_insignt_id:
            return IResponse(code=400, message="invalid params")
        try:
            data_insignt = DataInsignt.objects.get(pk=data_insignt_id, is_deleted=False)
        except DataInsignt.DoesNotExist:
            return IResponse(code=400, message="data not found")

        # 不重新记录
        if DataInsigntLogs.objects.filter(data_insignt_id=data_insignt_id, user=current_user).exists():
            return IResponse(code=400, message="no need log again.")

        DataInsigntLogs.objects.create(
            data_insignt=data_insignt,
            user=current_user,
            log_time=timezone.now(),
            read_time=request.data.get("read_time"),
        )
        return IResponse()


class DataInsigntNotifyView(AuthAPIView):
    """洞察日志提醒   前端菜单显示未读提醒"""

    permission_classes = [IsAuthenticated]

    def get(self, request: Request):
        total_record = DataInsignt.objects.filter(is_deleted=False).count()

        read_record = DataInsigntLogs.objects.filter(user=request.user, data_insignt__is_deleted=False).count()

        re_data = {"read": read_record, "no_read": total_record - read_record}
        return IResponse(data=re_data)


class DataInsigntBatchReadView(AuthAPIView):
    """洞察日志批量阅读"""

    permission_classes = [IsAuthenticated]

    def post(self, request: Request):
        current_user = request.user
        data_insignts = DataInsignt.objects.filter(is_deleted=False).exclude(datainsigntlogs__user=current_user)
        DataInsigntLogs.objects.bulk_create(
            [
                DataInsigntLogs(
                    data_insignt=data_insignt,
                    user=current_user,
                    read_time=0,
                    log_time=timezone.now(),
                )
                for data_insignt in data_insignts
            ]
        )
        return IResponse()


class NewProductInspirationView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        灵感列表
        """

        current_user = request.user
        if not current_user.can_view_zhuji_rank:
            return IResponse(code=403, message="no permission to operate")

        qs = NewProductInspiration.objects.filter(is_deleted=False)
        re_data, _, _ = custom_django_filter(
            request,
            qs,
            NewProductInspirationFilterSet,
            NewProductInspirationListSer,
            need_serialize=True,
        )
        return IResponse(data=re_data)

    def post(self, request):
        """
        新增灵感
        """

        current_user = request.user
        if not current_user.can_view_zhuji_rank:
            return IResponse(code=403, message="no permission to operate")

        note_id = request.data.get("note_id")
        assert note_id, "note_id required"
        try:
            instance = NewProductInspiration.objects.get(note_id=note_id)
        except NewProductInspiration.DoesNotExist:
            instance = None
        serializer = NewProductInspirationSer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return IResponse(data=serializer.data, code=200)
        return IResponse(message=str(serializer.errors), code=status.HTTP_400_BAD_REQUEST)


class NewProductInspirationDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, note_id):
        try:
            return NewProductInspiration.objects.get(note_id=note_id)
        except NewProductInspiration.DoesNotExist:
            return None

    def get(self, request, note_id):
        """
        灵感详情
        """
        current_user = request.user
        if not current_user.can_view_zhuji_rank:
            return IResponse(code=403, message="no permission to operate")

        inspiration = self.get_object(note_id)
        if not inspiration:
            return IResponse(code=400, message="data not found")
        serializer = NewProductInspirationDeatilSer(inspiration)
        return IResponse(data=serializer.data)

    def delete(self, request, note_id):

        current_user = request.user
        if not current_user.can_view_zhuji_rank:
            return IResponse(code=403, message="no permission to operate")

        inspiration = self.get_object(note_id)
        if not inspiration:
            return IResponse(code=400, message="data not found")
        inspiration.delete()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_key_word_enum_view(request):

    current_user = request.user
    if not current_user.can_view_zhuji_rank:
        return IResponse(code=403, message="no permission to operate")

    key_words_qs = NewProductInspiration.objects.values_list("key_word", flat=True).distinct().order_by("key_word")
    re_data, page_objects, _ = custom_django_filter(request, key_words_qs, need_serialize=False, force_order=False)
    return IResponse(data=list(page_objects))


class UserSelectionPlanListView(DBAPIView):
    """
    用户盯货盘 列表数据
    """

    def validate_permission(self):
        if not getattr(self.current_user, "can_watch_plan"):
            raise APIViewException(err_message="暂无权限")

    def get(self, request: Request):
        self.validate_permission()
        try:
            infos = self.current_user.userinfos
            author_ids = infos.watch_selection_plans
            if not author_ids:
                author_ids = []
        except UserInfos.DoesNotExist:
            author_ids = []
        # 查询今天的直播
        plans = CmmLiveOverview.objects.filter(author_id__in=author_ids).order_by("-begin_time")
        re_data, _, _ = custom_django_filter(
            request,
            plans,
            CmmLiveOverviewFilterSet,
            UserWatchCmmLiveOverviewListSer,
            force_order=False,
            need_serialize=True,
        )
        return IResponse(data=re_data)


class UserSelectionPlanInfoView(DBAPIView):
    def validate_permission(self):
        if not getattr(self.current_user, "can_watch_plan"):
            raise APIViewException(err_message="暂无权限")

    def get(self, request: Request, room_id: str):
        self.validate_permission()
        try:
            infos = self.current_user.userinfos
        except UserInfos.DoesNotExist:
            return IResponse()

        author_ids = infos.watch_selection_plans
        if not author_ids:
            return IResponse()

        products = CMMLiveGoods.objects.filter(author_id__in=author_ids, room_id=room_id).annotate(sales_amount=F("volume") * F("final_price")).order_by("-start_time")
        re_data, _, _ = custom_django_filter(
            request,
            products,
            SelectionPlanCMMGoodsFilterSet,
            UserWatchCmmLiveOverviewProductInfoSer,
            need_serialize=True,
            force_order=False,
        )

        return IResponse(data=re_data)


class ZhuJiProductLabelsView(CommonAPIView):
    def get(self, request: Request):
        re_data, _, _ = custom_django_filter(
            request,
            ProductLabels,
            ProductLabelsFilterSet,
            ProductLabelsListSer,
            force_order=False,
            need_serialize=True,
        )

        return IResponse(data=re_data)


class ZhuJiProductStyleEnumView(CommonAPIView):
    def get(self, request: Request):
        styles = ProductStyle.objects.filter(is_deleted=False).values("id", "name")
        return IResponse(data=styles)


class ZhuJiProductLabelsEnumView(CommonAPIView):
    def get(self, request: Request):
        labels = ProductLabels.objects.all().values("id", "name")
        return IResponse(data=labels)


class DataInsigntProductView(CommonAPIView):
    def get(self, request: Request):
        products = DataInsigntProduct.objects.filter(sales__gt=0).order_by("-release_time")
        re_data, _, _ = custom_django_filter(
            request,
            products,
            DataInsigntProductFilterSet,
            DataInsigntProductListViewSer,
            force_order=False,
            need_serialize=True,
        )

        # 更新状态
        user_infos, _ = UserInfos.objects.update_or_create(user=self.current_user, defaults={"new_on_zhuji_product": False})
        return IResponse(data=re_data)

    def post(self, request: Request):
        post_data = request.data
        items = post_data.get("items")
        if not items:
            raise APIViewException()

        external_id_list = [item.get("external_id") for item in items if item.get("external_id")]
        products = DataInsigntProduct.objects.filter(external_id__in=external_id_list)
        products_map = {product.external_id: product for product in products}

        update_fields = [
            "product_name",
            "release_time",
            "image",
            "author_id",
            "author_name",
            "price",
            "sales",
        ]
        need_create_objs = []
        need_update_objs = []

        for item in items:
            external_id = item.get("external_id")
            if not external_id:
                continue

            if external_id not in products_map:
                need_create_objs.append(DataInsigntProduct(external_id=external_id, **{field: item.get(field) for field in update_fields}))
            else:
                obj = products_map[external_id]
                for field in update_fields:
                    setattr(obj, field, item.get(field))
                need_update_objs.append(obj)
        create_rows = 0
        updated_rows = 0
        if need_create_objs:
            create_rows = len(DataInsigntProduct.objects.bulk_create(need_create_objs, batch_size=1000))

        if need_update_objs:
            updated_rows = DataInsigntProduct.objects.bulk_update(need_update_objs, fields=update_fields)

        if create_rows > 0 or updated_rows > 0:
            # 更新所有用户的大盘上新状态
            UserInfos.objects.filter(new_on_zhuji_product=False).update(new_on_zhuji_product=True)

        return IResponse(data={"create_rows": create_rows, "updated_rows": updated_rows})
