# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-10-12 15:32:02
# @Last Modified by:   <PERSON>
# @Last Modified time: 2023-12-25 14:43:55
import os
import re
import traceback
import uuid
import hashlib
import time
import mimetypes
from celery import shared_task
from datetime import datetime
from common.basics.paginator import self_cursor_paginator

from utils.cmm_goods_search.CmmCrawl import CmmCrawler
from common.formats import DATE_STR_FORMAT
from zhuji.basic import CMM_TASK_LOCK_NAME, CMM_TASK_QUEUE_NAME
from .models import (
    CMMSearchTasks,
    ProductCategoryList,
    ZhujiDBLiveComment,
    Product,
    ProductLiveData,
)
from utils.redis_lock import read_lock, setnx_lock, release_lock, gen_redis_conn
from utils.feishu import FeiShuDocx
from zhuji import logger
from utils.http_handle import convert_to_ware_time
from utils.aliyun_oss import upload_file
from django.core.paginator import Paginator
from django.utils import timezone
from django.conf import settings
from utils.aliyun_functions import generate_sts_credential, retry_fc
from utils.download_img import afetch_images


def check_and_update_category(category_detail):
    """
    检查并更新类目
    Args:
        "category_detail": {
            "first_cid": 20023,
            "first_cname": "翡翠/和田玉/琥珀蜜蜡/其他玉石",
            "second_cid": 20407,
            "second_cname": "和田玉",
            "third_cid": 34852,
            "third_cname": "手镯",
            "fourth_cid": 0,
            "fourth_cname": "",
        },

    Returns:
        category_ids: []
    """
    category_ids = []
    parent_id = category_detail.get("first_cid")
    parent_value = category_detail.get("first_cname")
    if not parent_id or not parent_value:
        raise ValueError("first_cid and first_cname required")
    parent, _ = ProductCategoryList.objects.update_or_create(id=parent_id, defaults={"name": parent_value})
    category_ids.append(parent_id)

    second_id = category_detail.get("second_cid")
    second_value = category_detail.get("second_cname")
    if not second_id or not second_value:
        return category_ids
    second, _ = ProductCategoryList.objects.update_or_create(id=second_id, defaults={"name": second_value, "parent": parent})
    category_ids.append(second_id)

    third_id = category_detail.get("third_cid")
    third_value = category_detail.get("third_cname")
    if not third_id or not third_value:
        return category_ids
    third, _ = ProductCategoryList.objects.update_or_create(id=third_id, defaults={"name": third_value, "parent": second})
    category_ids.append(third_id)

    fourth_id = category_detail.get("fourth_cid")
    fourth_value = category_detail.get("fourth_cname")
    if not fourth_id or not fourth_value:
        return category_ids
    third, _ = ProductCategoryList.objects.update_or_create(id=fourth_id, defaults={"name": fourth_value, "parent": third})
    category_ids.append(third_id)
    return category_ids


@shared_task
def insert_live_comment(data_list):
    try:
        live_comments = []
        update_fields = [
            "room_id",
            "author_id",
            "author_name",
            "begin_time",
            "comment",
            "crawl_time",
            "material",
            "style",
            "word",
            "fansclub_level",
            "new_user_grade_level",
        ]
        for data in data_list:
            if data.get("begin_time"):
                data["begin_time"] = convert_to_ware_time(data["begin_time"])
            if data.get("crawl_time"):
                data["crawl_time"] = convert_to_ware_time(data["crawl_time"])
            live_comments.append(ZhujiDBLiveComment(**data))
        ZhujiDBLiveComment.zhuji_db().bulk_create(live_comments, update_fields=update_fields)
        return "Success"
    except Exception as e:
        logger.info(str(e))
        return f"Failed: {str(e)}"


@shared_task
def refresh_yesterday_sales():
    """
    刷新昨日销量数据
    """
    product_qs = Product.objects.all()

    # 设置批次大小
    page_size = 50
    paginator = Paginator(product_qs, page_size)
    total_page = paginator.num_pages
    for i in range(total_page):
        product_list = []
        page_model_objects = paginator.page(i + 1)
        for product in page_model_objects:
            # 获取昨日销量
            today = timezone.now().date()
            yesterday = today - timezone.timedelta(days=1)
            live_data = ProductLiveData.objects.filter(product=product, live_date=yesterday).first()
            if live_data:
                product.yesterday_sales = live_data.volume
            else:
                product.yesterday_sales = None
            product_list.append(product)

        if product_list:
            Product.objects.bulk_create(
                product_list,
                update_conflicts=True,
                update_fields=[
                    "yesterday_sales",
                ],
                unique_fields=[
                    "product_id",
                ],
            )


def notify_next_task(notify_url: str):
    redis_conn = gen_redis_conn()
    if redis_conn.exists(CMM_TASK_QUEUE_NAME):
        _, next_task_id = redis_conn.brpop(CMM_TASK_QUEUE_NAME, 3)
        if next_task_id:
            fetch_cmm_goods.delay(next_task_id.decode(), notify_url)
        logger.info(f">>>> go ahead with next task. {next_task_id.decode()}")


@shared_task
def fetch_cmm_goods(task_id: str, notify_url: str):
    """
    获取蝉妈妈的商品数据

    Args:
        task_id (str): 任务ID
        notify_url (str): 回调地址


    total_goods
    {
        "external_id": "-34UnSd3uwqCOEtngm-JyRlLPtmKFl1N",
        "product_title": "Hantina 【达人专属】万字符雕刻手镯男女情侣款开口手镯",
        "brand_name": "",
        "price": 49.9,
        "sales": 37339,
        "sales_amount": 1863216.0999999999,
        "shop_id": "FAVXhMEv",
        "shop_name": "韩缇娜高定首饰",
        "shop_link": "https://haohuo.jinritemai.com/views/shop/index?id=FAVXhMEv",
        "source_platform": "CMM",
        "main_images": [
            "https://cdn-images.chanmama.com/douyin/product/ae6f251bf9cec2926c7625d807b90e83.jpeg?source=https%3A%2F%2Fp26-item.ecombdimg.com%2Fimg%2Fecom-shop-material%2FFAVXhMEv_m_7847d2b055dc9bbb1a435035fdead655_sx_566891_www1024-1024~tplv-5mmsx3fupr-resize_q%3A1080%3A1080%3Aq90.png"
            ],
        "price_range": "1-100",
        "short_url": "https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3653242996515841320&origin_type=pc_compass_manage",
        "category": "时尚饰品 > 手镯",
        "category_dict":
            {
                "second_cname": "手镯",
                "fourth_cid": 0,
                "fifth_cid": 0,
                "fifth_cname": "",
                "first_cname": "时尚饰品",
                "second_cid": 20996,
                "third_cid": 0,
                "third_cname": "",
                "fourth_cname": "",
                "sixth_cid": 0,
                "sixth_cname": "",
                "first_cid": 20080
                }
            }
    }
    """
    try:
        lock_acquired = setnx_lock(CMM_TASK_LOCK_NAME, task_id, 60 * 20)
        if lock_acquired is True or lock_acquired == task_id:
            try:
                task = CMMSearchTasks.objects.get(task_id=task_id)
            except CMMSearchTasks.DoesNotExist:
                return f"NOT FOUND {task_id}"

            if task == 2:
                return f"TASK DONE {task_id}"

            # 运行中的状态
            task.state = 1
            task.save()

            # 任务信息
            query_date = task.query_date
            keyword = task.keyword
            price_range = task.price_range
            sales_range = task.sales_range

            error_msg = ""

            total_goods_info = None
            for _ in range(3):
                try:
                    cmm_crawler = CmmCrawler()
                    # 爬取全部任务
                    total_goods_info = cmm_crawler.get_data(keyword, price_range, sales_range)
                    if total_goods_info:
                        break
                except Exception as e:
                    error_msg = f"爬虫任务失败: {e}"
                    time.sleep(1)
                    continue

            if not total_goods_info:
                CMMSearchTasks.objects.filter(task_id=task_id).update(remark=error_msg, state=3)
                return f"TASK FAIL {task_id}"

            print(f">>>爬取到任务数据{len(total_goods_info)}")

            # 异步发送数据到飞书
            # insert_data_to_feishu.delay(total_goods_info, query_date, keyword, price_range, sales_range, task_id, notify_url)

            # 下载图片 main_images默认只有一个,
            image_url_dict = [
                {
                    "external_id": goods["external_id"],
                    "url": goods["main_images"][0],
                }
                for goods in total_goods_info
                if goods["main_images"]
            ]
            # 异步下载图片
            tmp_images_dict = afetch_images(image_url_dict)
            print(f">>>获取的图片数量:{len(tmp_images_dict)}")
            external_id_list = [goods["external_id"] for goods in total_goods_info]

            # 获取oss凭证信息
            aliyun_certs = generate_sts_credential(
                settings.ALIYUN_OSS_ROLE_ARN_ADD,
                str(uuid.uuid4()),
            )

            oss_base_path = "https://zhulinks.oss-cn-guangzhou.aliyuncs.com/"

            need_create_products = []
            need_update_products = []

            products = Product.objects.filter(external_id__in=external_id_list)
            exsits_products_map = {product.external_id: product for product in products}

            # 只创建到 zhuji.Product
            for goods_info in total_goods_info:
                main_image_url = goods_info["main_images"][0]
                external_id = goods_info["external_id"]

                file_info = tmp_images_dict.get(main_image_url)
                if file_info:
                    image_id = file_info["image_id"]
                    suffix = file_info["suffix"]
                    local_filename = file_info["tmp_file_path"]
                    filename = f"productMgmt/zhuji_images_cmm_search/{image_id}.{suffix}"
                    # 上传到oss
                    batch_upload_file.delay(
                        aliyun_certs,
                        local_filename,
                        filename,
                    )

                    # 重新赋值main_images
                    base_dir = "crawler_data/prod/images/"
                    goods_info["main_images"][0] = os.path.join(oss_base_path, base_dir, filename)

                # if content_bytes:
                #     suffix = (mimetypes.guess_type(main_image_url)[0] or "").split("/")[-1] or "png"

                #     image_id = hashlib.md5(content_bytes + external_id.encode()).hexdigest()
                #     filename = f"productMgmt/zhuji_images_cmm_search/{image_id}.{suffix}"
                #     # todo:上传到oss
                #     batch_download_file.delay(aliyun_certs=aliyun_certs, data=content_bytes, filename=filename)
                #     # 重新赋值main_images
                #     base_dir = "crawler_data/prod/images/"
                #     goods_info["main_images"][0] = os.path.join(oss_base_path, base_dir, filename)

                # 去除不需要存储的字段
                goods_info.pop("short_url")
                goods_info.pop("category")
                goods_info.pop("price_range")
                category_dict = goods_info.pop("category_dict")

                # 处理分类
                external_category = check_and_update_category(category_dict)
                goods_info["external_category"] = external_category

                if exsits_products_map.get(external_id):
                    update_obj = exsits_products_map.get(external_id)

                    for field_name, field_val in goods_info.items():
                        setattr(update_obj, field_name, field_val)
                    need_update_products.append(update_obj)
                else:
                    need_create_products.append(Product(**goods_info))

            if need_update_products:
                Product.objects.bulk_update(
                    need_update_products,
                    fields=[
                        "product_title",
                        "brand_name",
                        "price",
                        "sales",
                        "sales_amount",
                        "shop_id",
                        "shop_name",
                        "shop_link",
                        "source_platform",
                        "main_images",
                    ],
                    batch_size=1000,
                )
            if need_create_products:
                Product.objects.bulk_create(need_create_products, batch_size=1000)

            print(f">>>需要更新的对象:{len(need_update_products)}")
            print(f">>>需要创建的对象:{len(need_create_products)}")

            origin_task_create_date = task.create_date
            # 保存数据库
            CMMSearchTasks.objects.filter(
                query_date=query_date,
                keyword=keyword,
                sales_range=sales_range,
                price_range=price_range,
                create_date__gte=origin_task_create_date,
            ).update(state=2)
            return f"SUCCESS {task_id}"
    except Exception as e:
        logger.error(f" >>> TASK {task_id} catch error in fetch_cmm_goods, {e},{traceback.format_exc()}")
        # 保存失败状态
        task.state = 3
        task.save()
    finally:
        # 查询下一个任务
        notify_next_task(notify_url)
        release_lock(CMM_TASK_LOCK_NAME, task_id)


@shared_task
def batch_upload_file(
    aliyun_certs: dict,
    local_filename: str,
    object_name: str,
):
    upload_file(
        aliyun_certs["access_key_id"],
        aliyun_certs["access_key_secret"],
        aliyun_certs["security_token"],
        local_filename,
        object_name,
    )

    os.remove(local_filename)
    # upload_file_with_bytes(
    #     aliyun_certs["access_key_id"],
    #     aliyun_certs["access_key_secret"],
    #     aliyun_certs["security_token"],
    #     data=data,
    #     objec_name=filename,
    # )


@shared_task
def insert_data_to_feishu(goods_data, query_date, keyword, price_range, sales_range, task_id, notify_url):
    """
    调用云函数 insert_feishu

    Args:
        goods_data (_type_): _description_
        query_date (_type_): _description_
        keyword (_type_): _description_
        price_range (_type_): _description_
        sale_range (_type_): _description_
    """
    try:
        payload = {
            "query_date": query_date.strftime(DATE_STR_FORMAT),
            "keyword": keyword,
            "price_range": price_range,
            "sales_range": sales_range,
            "task_id": task_id,
            "notify_url": notify_url,
            "data": goods_data,
        }

        response = retry_fc(
            service="zhulinks",
            function="feishu_insert",
            payload=payload,
            is_async=False,
            timeout=60 * 20,
            region="cn-hangzhou",
        )
        # 有回调地址，暂时使用回调地址的方式
        logger.info(f">>>>insert feishu table response: {response}")
    except Exception as e:
        logger.info(f">>>>insert feishu table with exception: {e}")


@shared_task
def send_msg_to_fs(user_id: str, form_url: str):
    """
    批量发送数据给飞书用户

    Args:
        user_id (str): 用户id, 飞书的open_id
        form_url (str): 多维表格url
    """
    # todo: 批量发送信息
    cli = FeiShuDocx()
    send_response = cli.send_user_message(user_id, form_url)
    print(f"batch_send_msg_to_fs result:{send_response}, {user_id}, {form_url}")


@shared_task
def scan_cmm_tasks():
    tasks = CMMSearchTasks.objects.filter(state__in=[0, 1, 3], query_date__gte=timezone.now().date())
    for task in tasks:
        if read_lock(CMM_TASK_LOCK_NAME) == task.task_id:
            continue

        redis_conn = gen_redis_conn()
        # 判断是否在redis列表里面
        elements = redis_conn.lrange(CMM_TASK_QUEUE_NAME, 0, -1)
        if task.task_id.encode() not in elements:
            redis_conn.lpush(CMM_TASK_QUEUE_NAME, task.task_id)

            logger.info(f">>>添加任务到redis列表: {task.task_id}")


def clean_zhuji_products(batch_size: int = 1000):
    offset = 0

    while True:
        products = Product.objects.filter(product_link__isnull=False).exclude(product_link="").only("id", "product_link")[offset : offset + batch_size]

        if not products:
            break

        need_update_objs = []
        for product in products:
            link_str = product.product_link
            ret = re.findall("\\?id=(.*?)&", link_str)
            if not ret:
                continue
            dy_product_id = ret[0]
            product.dy_product_id = dy_product_id
            need_update_objs.append(product)

        if need_update_objs:
            Product.objects.bulk_update(need_update_objs, fields=["dy_product_id"], batch_size=1000)

        offset += batch_size
