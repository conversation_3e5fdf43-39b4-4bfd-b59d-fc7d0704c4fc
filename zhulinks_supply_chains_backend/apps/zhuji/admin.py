# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-08-31 16:45:10
# @Last Modified by:   <PERSON>
# @Last Modified time: 2023-12-25 13:12:15
from django.contrib import admin
from .models import (
    Product,
    CMMLiveGoods,
    ProductCategoryList,
    ProductLiveData,
    DataInsignt,
    CMMSearchTasks,
    LiveAuthor,
    NewProductInspiration,
    CmmLiveOverview,
    ProductStyle,
    ProductLabels,
    ProductLabelRelate,
    DataInsigntProduct,
)
from utils.admin_optimization import NoCountPaginator

# Register your models here.


@admin.register(Product)
class ZhujiProductAdmin(admin.ModelAdmin):
    list_display = (
        "external_id",
        "product_id",
        "product_title",
        "external_category",
        "sku_price",
        "status",
        "source_platform",
        "create_date",
        "update_date",
    )
    list_filter = (
        "status",
        "source_platform",
    )
    search_fields = (
        "external_id",
        "product_id",
        "product_title",
        "external_category",
        "product_code",
        "sku_id",
        "shop_name",
        "search_keywords",
    )
    date_hierarchy = "create_date"
    ordering = [
        "-create_date",
    ]


@admin.register(CMMLiveGoods)
class CMMLiveGoodsAdmin(admin.ModelAdmin):
    list_display = (
        "external_id",
        "product_title",
        "author_id",
        "name",
        "room_id",
        "room_title",
        "sales",
        "create_date",
        "update_date",
    )
    list_filter = (
        "has_detail",
        "has_volume",
        "platform",
        "source",
    )
    search_fields = (
        "external_id",
        "author_id",
        "name",
        "room_id",
        "room_title",
        "product_title",
        "brand_name",
        "shop_name",
    )
    date_hierarchy = "create_date"
    ordering = ["-update_date"]


@admin.register(ProductCategoryList)
class ProductCategoryListAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
        "parent",
        "create_date",
        "update_date",
    )
    list_filter = (
        "create_date",
        "update_date",
    )
    search_fields = (
        "id",
        "name",
    )
    readonly_fields = (
        "create_date",
        "update_date",
    )


@admin.register(ProductLiveData)
class ProductLiveDataAdmin(admin.ModelAdmin):

    raw_id_fields = ["product"]

    show_full_result_count = False
    paginator = NoCountPaginator
    list_per_page = 50

    list_display = (
        "product_title",
        "product_id",
        "live_date",
        "volume",
    )
    # list_filter = (
    #     "product",
    #     "live_date",
    # )
    search_fields = (
        "product__product_title",
        "product__product_id",
        "product__external_id",
    )

    fields = (
        "product",
        "live_date",
        "volume",
        "price",
        "sales_amount",
    )

    def product_title(self, obj):
        return obj.product.product_title if obj.product else ""

    def product_id(self, obj):
        return obj.product.product_id if obj.product else ""


@admin.register(DataInsignt)
class DataInsigntAdmin(admin.ModelAdmin):
    list_display = ("id", "introduce", "log_date", "creator", "create_date")
    list_filter = ("log_date",)
    search_fields = ("introduce", "content")


@admin.register(CMMSearchTasks)
class CMMSearchTasksAdmin(admin.ModelAdmin):
    list_display = (
        "query_date",
        "keyword",
        "sales_range",
        "price_range",
        "form_link",
        "task_id",
        "state",
        "notice_users",
        "remark",
        "notify_date",
        "create_user",
        "create_date",
        "update_date",
    )
    list_filter = ("query_date", "create_user")


from django.contrib import admin
from .models import LiveAuthor, FocusArea

# 定义一个ModelAdmin类来自定义admin界面


@admin.register(FocusArea)
class FocusAreaAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(LiveAuthor)
class LiveAuthorAdmin(admin.ModelAdmin):
    list_display = (
        "author_id",
        "name",
        "list_focus_areas",
        "create_date",
        "update_date",
    )
    search_fields = (
        "author_id",
        "name",
    )
    filter_horizontal = ("focus_areas",)  # This provides a nice interface for selecting many-to-many relationships
    list_filter = (
        "focus_areas",
        "create_date",
    )

    def list_focus_areas(self, obj):
        return ", ".join([fa.name for fa in obj.focus_areas.all()])

    list_focus_areas.short_description = "关注领域"

    # Optionally, override get_queryset to optimize performance by reducing database queries
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related("focus_areas")
        return queryset


@admin.register(NewProductInspiration)
class NewProductInspirationAdmin(admin.ModelAdmin):
    list_display = (
        "note_id",
        "note_title",
        "key_word",
        "liked_count",
        "collection_count",
        "comment_count",
        "share_count",
    )
    search_fields = (
        "note_id",
        "note_title",
        "key_word",
    )
    list_filter = (
        "liked_count",
        "collection_count",
        "comment_count",
        "share_count",
        "sort_by",
    )


@admin.register(CmmLiveOverview)
class CmmLiveOverviewAdmin(admin.ModelAdmin):
    list_per_page = 20
    list_display = [
        field.name
        for field in CmmLiveOverview._meta.fields
        if field.name
        not in [
            "traffic_data",
            "volume_data",
            "product_data",
            "explain_product_data",
            "author_avatar",
        ]
    ]

    list_filter = (
        "begin_time",
        "room_finish_time",
        "name",
    )


@admin.register(ProductStyle)
class ProductStyleAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "is_deleted")
    search_fields = ("name",)


@admin.register(ProductLabels)
class ProductLabelsAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "label_type", "create_date")
    search_fields = ("name",)


@admin.register(ProductLabelRelate)
class ProductLabelRelateAdmin(admin.ModelAdmin):
    list_display = ("id", "product", "label")
    search_fields = (
        "product__product_id",
        "product__product_code",
        "product__sku_name",
        "product__product_title",
    )
    autocomplete_fields = ("product", "label")
    list_filter = ("label", )


@admin.register(DataInsigntProduct)
class DataInsigntProductAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "product_id",
        "external_id",
        "product_name",
        "release_time",
        "image",
        "author_id",
        "author_name",
        "price",
        "sales",
    )

    date_hierarchy = "release_time"

    search_fields = ("product_id", "product_name", "external_id")
    list_filter = ("author_name",)
