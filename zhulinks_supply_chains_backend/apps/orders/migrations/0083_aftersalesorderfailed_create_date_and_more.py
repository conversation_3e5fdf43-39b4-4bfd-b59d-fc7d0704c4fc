# Generated by Django 5.0.6 on 2024-06-13 07:21

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0082_orderskuinventorychangelog"),
    ]

    operations = [
        migrations.AddField(
            model_name="aftersalesorderfailed",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="aftersalesorderfailed",
            name="update_date",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
    ]
