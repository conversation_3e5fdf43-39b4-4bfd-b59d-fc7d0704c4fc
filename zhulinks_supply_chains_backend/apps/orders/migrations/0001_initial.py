# Generated by Django 4.2.1 on 2023-08-01 03:21

from django.db import migrations, models
import django.db.models.deletion
import functools
import utils.common


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("companies", "0020_alter_company_qualification_images"),
        ("products", "0021_product_has_link_code_product_inventory_warning_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Order",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "order_id",
                    models.CharField(
                        default=functools.partial(
                            utils.common.get_random,
                            *(),
                            **{"is_number": False, "prefix": "PO"},
                        ),
                        max_length=11,
                        unique=True,
                        verbose_name="订单编号",
                    ),
                ),
                (
                    "parant_order_id",
                    models.CharField(max_length=11, verbose_name="父订单编号"),
                ),
                ("ex_order_id", models.CharField(max_length=19, verbose_name="外部订单号")),
                (
                    "count",
                    models.IntegerField(blank=True, null=True, verbose_name="商品数"),
                ),
                (
                    "total_cost_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="总成本价",
                    ),
                ),
                (
                    "total_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="总价",
                    ),
                ),
                (
                    "delivery_method",
                    models.CharField(
                        choices=[("GL", "普通物流")],
                        default="GL",
                        max_length=2,
                        verbose_name="配送方式",
                    ),
                ),
                (
                    "delivery_num",
                    models.CharField(
                        blank=True, max_length=28, null=True, verbose_name="快递单号"
                    ),
                ),
                (
                    "logistics_company",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="快递公司"
                    ),
                ),
                (
                    "receiver_name",
                    models.CharField(
                        blank=True, max_length=28, null=True, verbose_name="收件人姓名"
                    ),
                ),
                (
                    "receiver_phone",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="收件人手机"
                    ),
                ),
                (
                    "receiver_province",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="收件人省份"
                    ),
                ),
                (
                    "receiver_city",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="收件人城市"
                    ),
                ),
                (
                    "receiver_district",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="收件人区县"
                    ),
                ),
                (
                    "receiver_town",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="收件人镇"
                    ),
                ),
                (
                    "receiver_address",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="收件人地址"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("RD", "备货中"),
                            ("DL", "已发货"),
                            ("FN", "已完成"),
                            ("CL", "已关闭"),
                        ],
                        max_length=2,
                        null=True,
                        verbose_name="状态",
                    ),
                ),
                (
                    "order_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="订单日期"),
                ),
                (
                    "order_platform",
                    models.CharField(
                        choices=[("DY", "抖音"), ("KS", "快手")],
                        default="DY",
                        max_length=2,
                        verbose_name="下单方式",
                    ),
                ),
                (
                    "order_source_id",
                    models.CharField(blank=True, null=True, verbose_name="下单渠道id"),
                ),
                (
                    "order_source",
                    models.CharField(blank=True, null=True, verbose_name="下单渠道"),
                ),
                (
                    "order_type",
                    models.CharField(
                        choices=[("PO", "商品订单")],
                        default="PO",
                        max_length=2,
                        verbose_name="订单类型",
                    ),
                ),
                (
                    "delivery_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="发货日期"),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "重组的订单",
                "verbose_name_plural": "重组的订单",
            },
        ),
        migrations.CreateModel(
            name="OrderMap",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ex_order_id", models.BigIntegerField(verbose_name="外部订单号")),
                (
                    "parant_order_id",
                    models.CharField(
                        default=functools.partial(
                            utils.common.get_random,
                            *(),
                            **{"is_number": False, "prefix": "PO"},
                        ),
                        unique=True,
                        verbose_name="父订单编号",
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "订单映射表",
                "verbose_name_plural": "订单映射表",
            },
        ),
        migrations.CreateModel(
            name="OrderStatusHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("order_id", models.CharField(max_length=11, verbose_name="订单编号")),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("RD", "备货中"),
                            ("DL", "已发货"),
                            ("FN", "已完成"),
                            ("CL", "已关闭"),
                        ],
                        max_length=2,
                        null=True,
                        verbose_name="状态",
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "订单状态历史",
                "verbose_name_plural": "订单状态历史",
            },
        ),
        migrations.CreateModel(
            name="RawOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "parant_order_id",
                    models.CharField(max_length=11, verbose_name="父订单编号"),
                ),
                (
                    "raw_order_id",
                    models.CharField(
                        default=functools.partial(
                            utils.common.get_random,
                            *(),
                            **{"is_number": False, "prefix": "PO"},
                        ),
                        max_length=11,
                        unique=True,
                        verbose_name="订单编号",
                    ),
                ),
                ("ex_order_id", models.CharField(max_length=19, verbose_name="外部订单号")),
                ("style_code", models.CharField(max_length=19, verbose_name="款号")),
                ("product_code", models.CharField(max_length=19, verbose_name="商品编码")),
                (
                    "order_type",
                    models.CharField(
                        choices=[("PO", "商品订单")],
                        default="PO",
                        max_length=2,
                        verbose_name="订单类型",
                    ),
                ),
                (
                    "product_name",
                    models.CharField(
                        blank=True, max_length=80, null=True, verbose_name="商品名称"
                    ),
                ),
                (
                    "retail_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="零售价",
                    ),
                ),
                (
                    "count",
                    models.IntegerField(blank=True, null=True, verbose_name="数量"),
                ),
                (
                    "total_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="总价",
                    ),
                ),
                (
                    "order_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="订单日期"),
                ),
                (
                    "pay_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="付款日期"),
                ),
                (
                    "shop_buyer_id",
                    models.CharField(blank=True, null=True, verbose_name="买家账号"),
                ),
                (
                    "order_platform",
                    models.CharField(
                        choices=[("DY", "抖音"), ("KS", "快手")],
                        default="DY",
                        max_length=2,
                        verbose_name="下单方式",
                    ),
                ),
                (
                    "pay_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="应付",
                    ),
                ),
                (
                    "freight",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="运费",
                    ),
                ),
                (
                    "order_source_id",
                    models.CharField(blank=True, null=True, verbose_name="下单渠道id"),
                ),
                (
                    "order_source",
                    models.CharField(blank=True, null=True, verbose_name="下单渠道"),
                ),
                (
                    "paid_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="实付款",
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="优惠金额",
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[("AP", "支付宝支付"), ("WP", "微信支付"), ("DP", "抖音支付")],
                        default="DP",
                        max_length=2,
                        verbose_name="支付方式",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("RD", "备货中"),
                            ("DL", "已发货"),
                            ("FN", "已完成"),
                            ("CL", "已关闭"),
                        ],
                        max_length=2,
                        null=True,
                        verbose_name="状态",
                    ),
                ),
                (
                    "buyer_message",
                    models.TextField(blank=True, null=True, verbose_name="买家留言"),
                ),
                (
                    "buyer_note",
                    models.TextField(blank=True, null=True, verbose_name="买家备注"),
                ),
                (
                    "delivery_method",
                    models.CharField(
                        choices=[("GL", "普通物流")],
                        default="GL",
                        max_length=2,
                        verbose_name="配送方式",
                    ),
                ),
                (
                    "logistics_company",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="快递公司"
                    ),
                ),
                (
                    "delivery_num",
                    models.CharField(
                        blank=True, max_length=28, null=True, verbose_name="快递单号"
                    ),
                ),
                (
                    "receiver_name",
                    models.CharField(
                        blank=True, max_length=28, null=True, verbose_name="收件人姓名"
                    ),
                ),
                (
                    "receiver_phone",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="收件人手机"
                    ),
                ),
                (
                    "receiver_province",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="收件人省份"
                    ),
                ),
                (
                    "receiver_city",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="收件人城市"
                    ),
                ),
                (
                    "receiver_district",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="收件人区县"
                    ),
                ),
                (
                    "receiver_town",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="收件人镇"
                    ),
                ),
                (
                    "receiver_address",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="收件人地址"
                    ),
                ),
                (
                    "plan_delivery_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="计划发货日期"),
                ),
                (
                    "remaining_delivery_date",
                    models.CharField(blank=True, null=True, verbose_name="剩余发货日期"),
                ),
                (
                    "delivery_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="发货日期"),
                ),
                (
                    "confirm_receipt_date",
                    models.DateTimeField(blank=True, null=True, verbose_name="确认收货时间"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="创建用户"
                    ),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="更新用户"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        verbose_name="供应商",
                    ),
                ),
                (
                    "order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.order",
                        verbose_name="重建的订单表",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        verbose_name="商品",
                    ),
                ),
                (
                    "sku",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.stockkeepingunit",
                        verbose_name="商品sku",
                    ),
                ),
            ],
            options={
                "verbose_name": "订单",
                "verbose_name_plural": "订单",
            },
        ),
        migrations.AddConstraint(
            model_name="orderstatushistory",
            constraint=models.UniqueConstraint(
                fields=("order_id", "status"), name="order_id_status_index"
            ),
        ),
        migrations.AddIndex(
            model_name="ordermap",
            index=models.Index(
                fields=["ex_order_id", "parant_order_id"],
                name="ex_order_parant_order_index",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="companies.company",
                verbose_name="供应商",
            ),
        ),
        migrations.AddIndex(
            model_name="raworder",
            index=models.Index(fields=["product_name"], name="product_name_index"),
        ),
        migrations.AddIndex(
            model_name="raworder",
            index=models.Index(fields=["product_name"], name="parant_order_id_index"),
        ),
        migrations.AddConstraint(
            model_name="raworder",
            constraint=models.UniqueConstraint(
                fields=("ex_order_id", "product_code"),
                name="unique_ex_order_id_product_code_index",
            ),
        ),
        migrations.AddConstraint(
            model_name="order",
            constraint=models.UniqueConstraint(
                fields=("parant_order_id", "company"),
                name="parant_order_id_company_index",
            ),
        ),
    ]
