# Generated by Django 4.2.11 on 2024-04-26 09:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "orders",
            "0033_remove_raworder_unique_ex_order_id_product_code_index_and_more",
        ),
    ]

    operations = [
        migrations.AddIndex(
            model_name="raworder",
            index=models.Index(
                fields=["product", "order_date"], name="product_order_date_index"
            ),
        ),
        migrations.AddIndex(
            model_name="raworder",
            index=models.Index(
                fields=["product", "order_date"], name="product_db_letters_index"
            ),
        ),
    ]
