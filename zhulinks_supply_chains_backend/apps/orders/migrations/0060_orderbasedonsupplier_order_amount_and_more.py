# Generated by Django 5.0.6 on 2024-06-03 01:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0059_orderbasedonsupplier_order_platform_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="orderbasedonsupplier",
            name="order_amount",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="订单金额（单位：分）",
            ),
        ),
        migrations.AddField(
            model_name="orderbasedonsupplier",
            name="pay_amount",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="支付金额（单位：分）",
            ),
        ),
        migrations.AlterField(
            model_name="orderbasedonsupplier",
            name="paid_amount",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="实付款",
            ),
        ),
    ]
