# Generated by Django 4.2.1 on 2023-08-08 14:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0009_remove_aftersales_as_type_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SubBill",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "business_type",
                    models.CharField(
                        blank=True,
                        choices=[("PO", "商品订单"), ("AS", "售后订单")],
                        max_length=2,
                        null=True,
                        verbose_name="业务类型",
                    ),
                ),
                (
                    "business_number",
                    models.CharField(max_length=11, verbose_name="业务单号"),
                ),
                (
                    "bill_type",
                    models.CharField(
                        blank=True,
                        choices=[("IN", "收入"), ("EX", "支出")],
                        max_length=2,
                        null=True,
                        verbose_name="收支类型",
                    ),
                ),
                (
                    "product_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="商品金额",
                    ),
                ),
                (
                    "settlement_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="结算金额",
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "bill",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="orders.bill",
                        verbose_name="账单",
                    ),
                ),
            ],
            options={
                "verbose_name": "子账单",
                "verbose_name_plural": "子账单",
            },
        ),
    ]
