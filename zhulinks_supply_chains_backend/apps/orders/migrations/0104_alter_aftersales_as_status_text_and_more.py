# Generated by Django 5.0.6 on 2024-07-09 09:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0055_distributor_push_inited_code"),
        ("orders", "0103_refetchtasks_message_alter_refetchtasks_status"),
        ("products", "0257_skuinventoryreviewrecord_review_remark"),
    ]

    operations = [
        migrations.AlterField(
            model_name="aftersales",
            name="as_status_text",
            field=models.CharField(
                blank=True,
                db_index=True,
                default="",
                max_length=255,
                null=True,
                verbose_name="售后状态文案",
            ),
        ),
        migrations.AddIndex(
            model_name="aftersalesrelatedorderinfo",
            index=models.Index(
                fields=["is_gift", "relate_product"],
                name="orders_afte_is_gift_909cbd_idx",
            ),
        ),
    ]
