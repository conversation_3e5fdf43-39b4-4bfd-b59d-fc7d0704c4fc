# Generated by Django 4.2.1 on 2023-08-07 08:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0006_bill_bill_unique_company_bill_start_bill_end_index"),
    ]

    operations = [
        migrations.AddField(
            model_name="aftersales",
            name="bill",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="orders.bill",
                verbose_name="账单",
            ),
        ),
        migrations.AddField(
            model_name="bill",
            name="request_pay_date",
            field=models.DateTimeField(blank=True, null=True, verbose_name="请款时间"),
        ),
        migrations.AddField(
            model_name="bill",
            name="request_pay_username",
            field=models.CharField(
                blank=True, default=None, max_length=56, null=True, verbose_name="请款用户名"
            ),
        ),
        migrations.AddField(
            model_name="bill",
            name="total_cost_amount",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="按成本价算账单结算总额",
            ),
        ),
        migrations.AddField(
            model_name="bill",
            name="total_cost_expenditure",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="按成本价算支出总额",
            ),
        ),
        migrations.AddField(
            model_name="bill",
            name="total_cost_income",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=10,
                null=True,
                verbose_name="按成本价算收入总额",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="bill",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="orders.bill",
                verbose_name="账单",
            ),
        ),
    ]
