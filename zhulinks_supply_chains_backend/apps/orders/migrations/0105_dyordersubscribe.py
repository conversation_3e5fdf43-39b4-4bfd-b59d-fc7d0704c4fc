# Generated by Django 5.0.6 on 2024-07-09 13:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0104_alter_aftersales_as_status_text_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="DyOrderSubscribe",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "biz",
                    models.IntegerField(
                        choices=[
                            (0, "未知"),
                            (1, "鲁班"),
                            (2, "小店"),
                            (3, "好好学习"),
                        ],
                        default=0,
                        verbose_name="业务来源",
                    ),
                ),
                (
                    "platform_create_time",
                    models.DateTimeField(verbose_name="平台创建时间"),
                ),
                (
                    "order_status",
                    models.IntegerField(default=0, verbose_name="订单状态"),
                ),
                (
                    "order_type",
                    models.IntegerField(
                        choices=[
                            (0, "实物"),
                            (2, "普通虚拟"),
                            (4, "poi核销"),
                            (5, "三方核销"),
                            (6, "服务市场"),
                        ],
                        default=1,
                        verbose_name="订单类型",
                    ),
                ),
                ("p_id", models.CharField(max_length=32, verbose_name="父订单ID")),
                (
                    "s_ids",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="子订单列表"
                    ),
                ),
                ("shop_id", models.CharField(max_length=32, verbose_name="店铺ID")),
                (
                    "flag",
                    models.CharField(
                        max_length=128, unique=True, verbose_name="唯一标识"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "抖店订单订阅",
                "verbose_name_plural": "抖店订单订阅",
            },
        ),
    ]
