# Generated by Django 5.0.6 on 2024-06-05 07:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0049_remove_datashop_enable_dataapplicationshops_enable"),
        ("orders", "0066_originalorder_shop_id_update_time_index"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="order",
            index=models.Index(
                fields=["order_date", "total_price"], name="order_date_total_price_idx"
            ),
        ),
    ]
