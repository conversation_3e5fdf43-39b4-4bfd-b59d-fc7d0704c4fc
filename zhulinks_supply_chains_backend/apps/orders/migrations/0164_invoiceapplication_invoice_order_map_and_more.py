# Generated by Django 5.1.2 on 2024-11-28 09:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0163_alter_invoiceapplication_invoice_content_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="invoiceapplication",
            name="invoice_order_map",
            field=models.ForeignKey(
                db_constraint=False,
                default=1,
                error_messages={"unique": "该订单号已经申请过发票了，请勿重复提交。"},
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="orders.ordermap",
                verbose_name="订单号",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="invoiceapplication",
            name="order_based_on_supplier",
            field=models.OneToOneField(
                blank=True,
                db_constraint=False,
                error_messages={"unique": "该订单号已经申请过发票了，请勿重复提交。"},
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="orders.orderbasedonsupplier",
                verbose_name="订单号",
            ),
        ),
    ]
