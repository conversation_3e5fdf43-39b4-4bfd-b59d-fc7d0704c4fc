# Generated by Django 4.2.1 on 2023-08-07 05:40

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import functools
import utils.common


class Migration(migrations.Migration):
    dependencies = [
        ("companies", "0020_alter_company_qualification_images"),
        ("orders", "0005_remove_raworder_parant_order_id_index"),
    ]

    operations = [
        migrations.CreateModel(
            name="Bill",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "bill_code",
                    models.CharField(
                        default=functools.partial(
                            utils.common.get_random,
                            *(),
                            **{"is_number": False, "prefix": "GYSZD"},
                        ),
                        max_length=14,
                        unique=True,
                        verbose_name="账单编号",
                    ),
                ),
                ("bill_start", models.DateField(verbose_name="账单开始时间")),
                ("bill_end", models.DateField(verbose_name="账单结束时间")),
                (
                    "bill_cycle",
                    models.CharField(
                        blank=True,
                        choices=[("WS", "周结"), ("MS", "月结")],
                        max_length=2,
                        null=True,
                        verbose_name="结算周期",
                    ),
                ),
                (
                    "total_income",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="收入总额",
                    ),
                ),
                (
                    "total_expenditure",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="支出总额",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="账单结算总额",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("TS", "待结算"),
                            ("TC", "待确认"),
                            ("PR", "待请款"),
                            ("PP", "待付款"),
                            ("SD", "已结算"),
                        ],
                        default="TS",
                        max_length=2,
                        null=True,
                        verbose_name="状态",
                    ),
                ),
                (
                    "request_pay_code",
                    models.CharField(
                        blank=True,
                        default=None,
                        max_length=11,
                        null=True,
                        unique=True,
                        verbose_name="请款编号",
                    ),
                ),
                (
                    "pay_account",
                    models.CharField(
                        blank=True, max_length=19, null=True, verbose_name="付款账号"
                    ),
                ),
                (
                    "receiver_account",
                    models.CharField(
                        blank=True, max_length=19, null=True, verbose_name="供应商收款账号"
                    ),
                ),
                (
                    "pay_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="付款金额",
                    ),
                ),
                (
                    "payment_proof",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            blank=True, max_length=300, null=True, verbose_name="付款凭证"
                        ),
                        blank=True,
                        default=list,
                        null=True,
                        size=5,
                        verbose_name="付款凭证图片列表",
                    ),
                ),
                ("remark", models.TextField(blank=True, null=True, verbose_name="备注")),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        verbose_name="供应商",
                    ),
                ),
            ],
            options={
                "verbose_name": "账单",
                "verbose_name_plural": "账单",
            },
        ),
        migrations.AddConstraint(
            model_name="bill",
            constraint=models.UniqueConstraint(
                fields=("company", "bill_start", "bill_end"),
                name="unique_company_bill_start_bill_end_index",
            ),
        ),
    ]
