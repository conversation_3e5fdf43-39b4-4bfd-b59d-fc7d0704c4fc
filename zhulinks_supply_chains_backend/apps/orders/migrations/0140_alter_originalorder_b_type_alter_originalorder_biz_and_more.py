# Generated by Django 5.0.8 on 2024-08-15 10:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0139_alter_orders_originalorder_index"),
    ]

    operations = [
        migrations.AlterField(
            model_name="originalorder",
            name="b_type",
            field=models.IntegerField(
                blank=True, db_index=True, null=True, verbose_name="下单端"
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="biz",
            field=models.IntegerField(
                blank=True, db_index=True, null=True, verbose_name="业务来源"
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="code",
            field=models.CharField(
                blank=True, db_index=True, max_length=20, null=True, verbose_name="货号"
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="create_date",
            field=models.DateTimeField(
                auto_now_add=True, db_index=True, null=True, verbose_name="创建时间"
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="originalorder",
            name="finish_time",
            field=models.DateTimeField(
                blank=True, db_index=True, null=True, verbose_name="订单完成时间"
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="out_product_id",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=100,
                null=True,
                verbose_name="商品外部编码",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="out_sku_id",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=100,
                null=True,
                verbose_name="外部Skuid",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="parent_order_id",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=100,
                null=True,
                verbose_name="父订单号（店铺订单号）",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="product_version",
            field=models.BigIntegerField(
                blank=True, db_index=True, null=True, verbose_name="商品版本号"
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="raw_product_id",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=255,
                null=True,
                verbose_name="原系统商品ID",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="raw_product_id_str",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=255,
                null=True,
                verbose_name="原系统商品ID，字符串型",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="raw_product_name",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=255,
                null=True,
                verbose_name="原系统商品名称",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="raw_product_pic",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=300,
                null=True,
                verbose_name="原系统商品图片",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="raw_sku_id",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=255,
                null=True,
                verbose_name="原系统商品skuId",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="raw_spec_code",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=33,
                null=True,
                verbose_name="商品编码",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="shop_name",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=255,
                null=True,
                verbose_name="商户名称",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="spec_code",
            field=models.CharField(
                blank=True,
                db_index=True,
                max_length=30,
                null=True,
                verbose_name="主商品商品编码",
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="update_date",
            field=models.DateTimeField(
                auto_now=True, db_index=True, null=True, verbose_name="更新时间"
            ),
        ),
    ]
