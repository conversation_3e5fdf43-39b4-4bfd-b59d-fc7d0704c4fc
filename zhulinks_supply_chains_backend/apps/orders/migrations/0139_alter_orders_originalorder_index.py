# -*- coding: utf-8 -*-
from django.db import migrations


def generate_index_sql(index_name, table_name, column_name):
    return f"""DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1
            FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE c.relname = '{index_name}'
              AND n.nspname = 'public'
        ) THEN
            CREATE INDEX {index_name} ON {table_name} ({column_name});
        END IF;
    END $$;"""


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0138_alter_aftersalesrelatedorderinfo_db_letters_and_more"),
    ]

    operations = [
        # 手动建立了索引，补充迁移文件
        migrations.RunSQL(
            generate_index_sql("orders_originalorder_raw_sku_id_idx", "orders_originalorder", "raw_sku_id"),
        ),
        # 手动建立了索引，补充迁移文件
        migrations.RunSQL(
            generate_index_sql("orders_originalorder_raw_sku_id_idx", "orders_originalorder", "raw_spec_code"),
        ),
    ]
