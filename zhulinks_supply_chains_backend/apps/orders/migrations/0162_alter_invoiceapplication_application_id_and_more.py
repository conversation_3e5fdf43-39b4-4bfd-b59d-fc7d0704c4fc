# Generated by Django 5.1.2 on 2024-11-28 02:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0161_alter_invoiceapplication_order_based_on_supplier"),
    ]

    operations = [
        migrations.AlterField(
            model_name="invoiceapplication",
            name="application_id",
            field=models.CharField(
                db_index=True,
                editable=False,
                error_messages={"unique": "该申请编号已经存在，请勿重复提交。"},
                max_length=50,
                unique=True,
                verbose_name="申请编号",
            ),
        ),
        migrations.AlterField(
            model_name="invoiceapplication",
            name="order_based_on_supplier",
            field=models.ForeignKey(
                db_constraint=False,
                default=1,
                error_messages={"unique": "该订单号已经申请过发票了，请勿重复提交。"},
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="orders.orderbasedonsupplier",
                unique=True,
                verbose_name="订单号",
            ),
            preserve_default=False,
        ),
    ]
