# Generated by Django 4.2.11 on 2024-05-06 09:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "orders",
            "0033_remove_raworder_unique_ex_order_id_product_code_index_and_more",
        ),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="raworder",
            name="unique_ex_order_id_product_code_index",
        ),
        migrations.RemoveIndex(
            model_name="raworder",
            name="product_name_raworder_index",
        ),
        migrations.RenameField(
            model_name="raworder",
            old_name="raw_product_code",
            new_name="raw_spec_code",
        ),
        migrations.RemoveField(
            model_name="raworder",
            name="product_code",
        ),
        migrations.RemoveField(
            model_name="raworder",
            name="product_name",
        ),
        migrations.AddConstraint(
            model_name="raworder",
            constraint=models.UniqueConstraint(
                fields=("ex_order_id", "raw_spec_code"),
                name="unique_ex_order_id_spec_code_index",
            ),
        ),
    ]
