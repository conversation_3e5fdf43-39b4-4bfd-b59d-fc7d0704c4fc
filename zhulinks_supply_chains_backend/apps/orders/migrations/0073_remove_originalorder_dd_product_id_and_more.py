# Generated by Django 5.0.6 on 2024-06-07 06:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0072_originalorder_raw_product_name"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="originalorder",
            name="DD_product_id",
        ),
        migrations.RemoveField(
            model_name="originalorder",
            name="DD_product_id_str",
        ),
        migrations.RemoveField(
            model_name="originalorder",
            name="DD_product_name",
        ),
        migrations.RemoveField(
            model_name="originalorder",
            name="DD_sku_id",
        ),
        migrations.RemoveField(
            model_name="originalorder",
            name="DD_sku_specs",
        ),
        migrations.AddField(
            model_name="originalorder",
            name="from_mall",
            field=models.BooleanField(
                blank=True, default=False, null=True, verbose_name="是否为商城数据"
            ),
        ),
        migrations.AddField(
            model_name="originalorder",
            name="raw_product_id",
            field=models.IntegerField(
                blank=True, null=True, verbose_name="原系统商品ID"
            ),
        ),
        migrations.AddField(
            model_name="originalorder",
            name="raw_product_id_str",
            field=models.CharField(
                blank=True,
                max_length=50,
                null=True,
                verbose_name="原系统商品ID，字符串型",
            ),
        ),
        migrations.AddField(
            model_name="originalorder",
            name="raw_product_pic",
            field=models.CharField(
                blank=True, max_length=300, null=True, verbose_name="原系统商品图片"
            ),
        ),
        migrations.AddField(
            model_name="originalorder",
            name="raw_sku_id",
            field=models.IntegerField(
                blank=True, null=True, verbose_name="原系统商品skuId"
            ),
        ),
        migrations.AddField(
            model_name="originalorder",
            name="raw_sku_specs",
            field=models.JSONField(
                blank=True, max_length=255, null=True, verbose_name="原系统规格信息"
            ),
        ),
        migrations.AlterField(
            model_name="originalorder",
            name="raw_product_name",
            field=models.CharField(
                blank=True, max_length=255, null=True, verbose_name="原系统商品名称"
            ),
        ),
    ]
