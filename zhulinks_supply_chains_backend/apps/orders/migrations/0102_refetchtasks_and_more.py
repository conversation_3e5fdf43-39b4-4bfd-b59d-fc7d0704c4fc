# Generated by Django 5.0.6 on 2024-07-05 03:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0101_alter_originalorder_theme_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="RefetchTasks",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("shop_pk", models.IntegerField(verbose_name="店铺主键")),
                (
                    "shop_id",
                    models.CharField(max_length=32, verbose_name="平台店铺shop_id"),
                ),
                ("start_time", models.IntegerField(verbose_name="开始更新时间")),
                ("end_time", models.IntegerField(verbose_name="结束更新时间")),
                (
                    "calculate_inventory_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="开始核算库存时间"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "待执行"),
                            ("running", "执行中"),
                            ("success", "执行成功"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="任务状态",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "指定日期订单补数任务表",
                "verbose_name_plural": "指定日期订单补数任务表",
            },
        ),
        migrations.AddConstraint(
            model_name="refetchtasks",
            constraint=models.UniqueConstraint(
                fields=("shop_id", "start_time", "end_time"),
                name="shop_id_start_end_time_index",
            ),
        ),
    ]
