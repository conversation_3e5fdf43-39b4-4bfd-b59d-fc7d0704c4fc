# -*- coding: utf-8 -*-
from django.db import transaction
from django.db.models.signals import post_save, pre_delete
from django.dispatch import Signal, receiver

from orders.models import OriginalOrder, ShoppingCart, ShoppingCartRecordMap
from orders.tasks import update_as_order_relate_task
from orders.tasks_v2.shopping_cart import create_or_update_feishu_shopping_car_record_task, delete_shopping_car_record_task


def load_signals():
    pass


as_order_relate_update = Signal()


@receiver(as_order_relate_update)
def update_as_order_relate_info(sender, **kwargs):
    order_map = kwargs["order_map"]
    if order_map:
        update_as_order_relate_task.delay(order_map)


@receiver(post_save, sender=OriginalOrder)
def order_status_handler(sender, instance: OriginalOrder, created, raw, *args, **kwargs):
    if not created:
        if instance.order_status == "CL":
            sp_as_order = instance.order_based_on_supplier
            #
            order_status_list = sp_as_order.originalorder_set.all().values_list("order_status", flat=True)
            if not set(order_status_list).difference({"CL"}):
                sp_as_order.order_status = "CL"
                sp_as_order.save()


@receiver(post_save, sender=ShoppingCart)
def shopping_cart_insert_or_update(sender, instance: ShoppingCart, created, raw, *args, **kwargs):
    transaction.on_commit(lambda: create_or_update_feishu_shopping_car_record_task.delay(instance.pk))


@receiver(pre_delete, sender=ShoppingCart)
def shopping_cart_delete(sender, instance: ShoppingCart, *args, **kwargs):
    try:
        record_id = instance.shoppingcartrecordmap.record_id
    except ShoppingCartRecordMap.DoesNotExist:
        record_id = None

    # delete_shopping_car_record_task(record_id)
    print(f">>>> {record_id}")

    if record_id:
        transaction.on_commit(lambda: delete_shopping_car_record_task.delay(record_id))
