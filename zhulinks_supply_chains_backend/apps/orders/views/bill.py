# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-08-07 13:41:56
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-09-28 14:18:00
import copy
import decimal
import traceback
from copy import deepcopy
from datetime import datetime
from functools import partial

from django.db.models import Count, Sum
from django.http import FileResponse
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from common.basics.views import set_log_params, OperateLogAPIViewMixin
from companies.models import DataShop
from orders import logger
from utils.common import get_random
from utils.download_tmp import get_excel_async, SubBillListDownloadTmpl
from utils.http_handle import IResponse, custom_filter, custom_django_filter
from ..filtersets import BillListFilterSet, SubBillFilterSet
from ..models.bills import Bill, SubBill
from ..serializers import BillSerialziers, SubBillSerialziers, SubBillDownloadSerializers


class BillView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        current_user = request.user
        filters = {"company": current_user.company, "status__in": ["TC", "PR", "PP", "SD"]} if request.auth.get("user_type") == "SP" else {}

        bill_query = Bill.objects.filter(**filters)
        re_data, _, _ = custom_django_filter(request, bill_query, filter_set=BillListFilterSet, order_fields=["-create_date"], iserializer=BillSerialziers)
        return IResponse(data=re_data)


class SubBillListDownloadView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, bill_code):
        current_user = request.user
        filters = {"company": current_user.company} if request.auth.get("user_type") == "SP" else {}

        try:
            bill = Bill.objects.get(bill_code=bill_code, **filters)
        except Bill.DoesNotExist:
            return IResponse(code=400, message="data not found")

        bill_query = SubBill.objects.filter(bill_id=bill.id)
        _, page_objs, _ = custom_django_filter(
            request,
            bill_query,
            SubBillFilterSet,
            partial(SubBillSerialziers, context={"request": request}),
            order_fields=["-create_date"],
            force_order=False,
        )
        re_data = SubBillDownloadSerializers(instance=page_objs, many=True, context={"request": request}).data
        for item in re_data:
            _tmp_data = {}
            for k, v in SubBillListDownloadTmpl.items():
                _tmp_data[v] = item.get(k, "")

            re_data.append(_tmp_data)

        filename = f"账单明细{datetime.strftime(datetime.now(), '%Y%m%d%H%M%S')}.xlsx"
        byte_buffer = get_excel_async("账单明细", re_data, image_columns=[9])
        if byte_buffer:
            byte_buffer.seek(0)

        return FileResponse(byte_buffer, filename=filename, as_attachment=True)


class BillDetailView(OperateLogAPIViewMixin, APIView):
    resource_name = "账单管理"
    fronted_page = "账单管理"
    permission_classes = [IsAuthenticated]
    need_format_resource_name = True

    def get(self, request, bill_code):
        current_user = request.user
        company = current_user.company

        try:
            if request.auth.get("user_type") == "OP":
                bill = Bill.objects.get(bill_code=bill_code)
            else:
                assert company, "only operator can not bind company"
                bill = Bill.objects.get(bill_code=bill_code, company=company)
        except Bill.DoesNotExist:
            return IResponse(code=404, message="data not found")
        bill_data = BillSerialziers(instance=bill).data
        return IResponse(data=bill_data)

    def patch(self, request, bill_code):
        try:
            current_user = request.user
            company = current_user.company

            try:
                if request.auth.get("user_type") == "OP":
                    bill = Bill.objects.get(bill_code=bill_code)
                else:
                    assert company, "only operator can not bind company"
                    bill = Bill.objects.get(bill_code=bill_code, company=company)
            except Bill.DoesNotExist:
                return IResponse(code=404, message="data not found")

            raw_bill = copy.deepcopy(bill)
            describe = ""
            update_data = request.data
            update_fields = []
            if request.auth.get("user_type") == "SP":
                status = update_data.get("status")
                assert status in ("PR", "PP"), "supplier only can comfirmed or request pay."
                if not status:
                    return IResponse(code=403, message="no permission to operate")
                if status == "PR":
                    # 确认时将待确认改为待请款
                    assert bill.status == "TC", "请待确认后再请款"
                    bill.status = status
                    update_fields.append("status")
                    describe = f"账单进行了确认操作"
                if status == "PP":
                    # 请款时将待请款改为待付款
                    assert bill.status == "PR", "pending pay after pending request pay"
                    bill.request_pay_code = partial(get_random, prefix="QP", is_number=False)()
                    bill.request_pay_date = timezone.now()
                    bill.request_pay_username = current_user.username
                    bill.status = status
                    update_fields.extend(["request_pay_code", "request_pay_date", "request_pay_username", "status"])
                    describe = f"账单进行了请款操作"
            elif request.auth.get("user_type") == "OP":
                pay_bank_card_number = update_data.get("pay_bank_card_number")
                receiver_bank_account_name = update_data.get("receiver_bank_account_name")
                receiver_bank_card_number = update_data.get("receiver_bank_card_number")
                receiver_bank_branch_name = update_data.get("receiver_bank_branch_name")
                pay_amount = update_data.get("pay_amount")
                payment_proof = update_data.get("payment_proof")
                remark = update_data.get("remark")

                assert receiver_bank_card_number is not None and pay_amount is not None, "comfirmed pay should provide pay_account, receiver_account, pay_amount"
                assert len(receiver_bank_card_number.strip()) != 0, "pay_account, receiver_account required valid value"
                update_fields.extend(
                    [
                        "pay_bank_account_name",
                        "pay_bank_card_number",
                        "pay_bank_branch_name",
                        "receiver_bank_account_name",
                        "receiver_bank_card_number",
                        "receiver_bank_branch_name",
                        "pay_amount",
                        "payment_proof",
                        "remark",
                        "status",
                        "pay_date",
                    ]
                )

                # try:
                #     pay_bank = Bank.objects.get(bank_card_number=pay_bank_card_number)
                # except Bank.DoesNotExist:
                #     return IResponse(code=400, message="该支付银行卡未配置")
                #
                # bill.pay_bank_account_name = pay_bank.bank_account_name
                # bill.pay_bank_card_number = pay_bank.bank_card_number
                # bill.pay_bank_branch_name = pay_bank.bank_branch_name

                bill.receiver_bank_account_name = receiver_bank_account_name
                bill.receiver_bank_card_number = receiver_bank_card_number
                bill.receiver_bank_branch_name = receiver_bank_branch_name

                bill.pay_amount = pay_amount
                bill.payment_proof = payment_proof
                bill.remark = remark
                bill.status = "SD"
                bill.pay_date = timezone.now()
                describe = f"账单进行了付款操作"
            else:
                return IResponse(code=400, message="无操作权限")
            bill.save(update_fields=update_fields)

            bill_data = BillSerialziers(instance=bill).data
            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=str(bill.bill_code),
                    model=Bill,
                    describe=describe,
                    raw_object=raw_bill,
                    new_object=bill,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass
            return IResponse(data=bill_data)
        except AssertionError as e:
            logger.error(str(e))
            return IResponse(code=400, message=str(e))
        except Exception as e:
            logger.error(str(e))
            return IResponse(code=500, message=str(e))


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def sub_bill_list(request, bill_code):
    current_user = request.user
    filters = {"company": current_user.company} if request.auth.get("user_type") == "SP" else {}

    try:
        bill = Bill.objects.get(bill_code=bill_code, **filters)
    except Bill.DoesNotExist:
        return IResponse(code=400, message="data not found")

    bill_query = SubBill.objects.filter(bill_id=bill.id)
    re_data, _, _ = custom_django_filter(request, bill_query, filter_set=SubBillFilterSet, order_fields=["-create_date"], iserializer=SubBillSerialziers)
    return IResponse(data=re_data)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def payment_proof_view(request, bill_code):
    """
    查看收款凭证
    """
    current_user = request.user
    company = current_user.company

    try:
        if request.auth.get("user_type") == "OP":
            bill = Bill.objects.get(bill_code=bill_code)
        else:
            assert company, "only operator can not bind company"
            bill = Bill.objects.get(bill_code=bill_code, company=company)
    except Bill.DoesNotExist:
        return IResponse(code=404, message="data not found")

    re_data = {
        "bill_code": bill.bill_code,
        "request_pay_code": bill.request_pay_code,
        "receiver_bank_account_name": bill.receiver_bank_account_name,
        "receiver_bank_card_number": bill.receiver_bank_card_number,
        "receiver_bank_branch_name": bill.receiver_bank_branch_name,
        "pay_bank_account_name": bill.pay_bank_account_name,
        "pay_bank_card_number": bill.pay_bank_card_number,
        "pay_date": bill.pay_date,
        "pay_amount": bill.pay_amount,
        "payment_proof": bill.payment_proof,
        "remark": bill.remark,
    }

    return IResponse(data=re_data)


class CompanyShopView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        filters = {"bill__company_id": request.query_params.get("company_id")}

        bill_query = SubBill.objects.filter(**filters).distinct("shop_id").values("shop_id")
        shop_info = DataShop.objects.filter(shop_id__in=bill_query)

        re_data = []
        for shop in shop_info:
            re_data.append(
                {
                    "shop_id": shop.shop_id,
                    "shop_name": shop.shop_name,
                }
            )

        return IResponse(data=re_data)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def bill_statistics(request):
    """
    # 订单数
    # 商品数
    # 待结算金额
    """
    try:
        current_user = request.user
        company = current_user.company
        re_data = {}
        return re_data

        if request.auth.get("user_type") != "OP":
            assert not company, "only operator can not bind company"

        current_user = request.user
        filters = {"company": current_user.company} if request.auth.get("user_type") == "SP" else {}

        raw_params = request.query_params.copy()
        raw_params_order = deepcopy(raw_params)
        if raw_params_order.get("status__in"):
            raw_params_order.pop("status__in")
        if raw_params_order.get("create_date__range"):
            raw_params_order["order_date__range"] = raw_params_order.pop("create_date__range")[0]

        raw_params_as = deepcopy(raw_params)
        if raw_params_as.get("status__in"):
            raw_params_as.pop("status__in")
        if raw_params_as.get("create_date__range"):
            raw_params_as["as_date__range"] = raw_params_as.pop("create_date__range")[0]

        page_orders, _, obj_qs = custom_filter(raw_params, Bill, force_orders=False, **filters)
        order_qs = Order.objects.filter(bill_id__in=obj_qs.values("id"))
        order_count = order_qs.aggregate(order_count=Count("order_id"))
        product_count = order_qs.values("count").aggregate(
            product_count=Sum("count"),
        )

        # total_amount = obj_qs.values("total_cost_amount", "total_amount").aggregate(
        #     ts_total_cost_amount=Sum("total_cost_amount"),
        #     ts_total_amount=Sum("total_amount"),
        # )

        # 需求变更，待结算金额，使用订单统计金额-售后金额， todo: 合并接口
        # == 订单统计 ==
        subset_fields_order = {"product_name": "raworder"}

        if raw_params_order.get("status") == "FN":
            raw_params_order["status"] = "DL"
        page_orders, _, order_obj_qs = custom_filter(
            raw_params_order,
            Order,
            like_fields=["raworder__product_name"],
            hybrid_fields=["order_id", "raworder__product_name"],
            subset_fields=subset_fields_order,
            force_orders=False,
            **filters,
        )
        price_product = order_obj_qs.values("total_cost_price", "paid_amount", "count").aggregate(
            total_cost_price_sum=Sum("total_cost_price"),
            paid_amount_sum=Sum("paid_amount"),
            product_count=Sum("count"),
        )

        # == 售后单统计 ==
        page_as_list, _, as_obj_qs = custom_filter(
            raw_params_as,
            RawAfterSales,
            like_fields=["product_name"],
            hybrid_fields=["as_id", "product_name"],
            force_orders=False,
            **filters,
        )
        amount_product = as_obj_qs.values("total_cost_amount", "total_amount", "refund_Qty").aggregate(
            total_cost_amount_sum=Sum("total_cost_amount"),
            total_amount_sum=Sum("total_amount"),
            as_product_count=Sum("refund_Qty"),
        )

        total_cost_price_sum = price_product["total_cost_price_sum"] if price_product.get("total_cost_price_sum") else decimal.Decimal(0.00)
        total_cost_amount_sum = amount_product["total_cost_amount_sum"] if amount_product.get("total_cost_amount_sum") else decimal.Decimal(0.00)

        paid_amount_sum = price_product["paid_amount_sum"] if price_product.get("paid_amount_sum") else decimal.Decimal(0.00)
        total_amount_sum = amount_product["total_amount_sum"] if amount_product.get("total_amount_sum") else decimal.Decimal(0.00)

        ts_total_cost_amount = total_cost_price_sum - total_cost_amount_sum
        ts_total_amount = paid_amount_sum - total_amount_sum

        re_data.update(**order_count)
        re_data.update(**product_count)
        re_data["ts_total_cost_amount"] = ts_total_cost_amount
        re_data["ts_total_amount"] = ts_total_amount

        return IResponse(data=re_data)
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc}")
        return IResponse(code=500, message=str(e))
