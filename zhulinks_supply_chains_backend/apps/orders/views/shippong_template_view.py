from urllib import response

from django.db import transaction
from rest_framework import status
from rest_framework.decorators import action

from common.basics.permissions import OPPermissions
from orders.filters import ShippingTemplateFilter
from orders.new_models import ShippingTemplateModel
from orders.serializer import ShippingTemplateSerializer
from utils.api_page_number_pagination import ApiPageNumberPagination
from utils.base_model_view_set import BaseMysqlModelViewSet
from utils.json_response import JsonResponse


class ShippingTemplateViews(BaseMysqlModelViewSet):
    queryset = ShippingTemplateModel.objects.all().order_by("-create_date")
    pagination_class = ApiPageNumberPagination
    filterset_class = ShippingTemplateFilter
    serializer_class = ShippingTemplateSerializer
    permission_classes = [OPPermissions]

    def _generate_copy_name(self, original_name):
        """
        自动生成唯一的副本名称。
        """
        base_name = original_name.split(" 副本")[0]
        existing_templates = ShippingTemplateModel.objects.filter(template_name__startswith=base_name)
        suffix_numbers = [int(template.template_name.replace(base_name + " 副本", "").strip()) for template in existing_templates if "副本" in template.template_name]
        next_suffix = max(suffix_numbers, default=0) + 1
        return f"{base_name} 副本{next_suffix}"

    @action(detail=True, methods=["get"], url_path="copy")
    def copy_template(self, request, pk=None):
        """
        复制运费模板，并将新模板名称添加后缀，置于列表首位。
        """
        try:
            original_template = self.get_object()

            with transaction.atomic():
                new_template_name = self._generate_copy_name(original_template.template_name)
                new_template = ShippingTemplateModel.objects.create(
                    template_name=new_template_name,
                    shipping_address=original_template.shipping_address,
                    shipping_mode=original_template.shipping_mode,
                    shipping_fee=original_template.shipping_fee,
                    conditional_free_shipping=original_template.conditional_free_shipping,
                    restricted_sales_area=original_template.restricted_sales_area,
                )
                new_template.save()
            return JsonResponse(status=status.HTTP_201_CREATED, data={"code": "0", "msg": "复制成功"})
        except Exception as e:
            return JsonResponse(status=status.HTTP_400_BAD_REQUEST, data={"code": "0", "msg": "复制失败"})
