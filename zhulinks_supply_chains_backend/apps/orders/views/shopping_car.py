# -*- coding: utf-8 -*-
import json

from django.db import transaction
from django.db.models import F
from django.db.models.signals import post_save
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request

from common.basic import DBAPIView, CommonAPIView
from common.basics.exceptions import APIViewException
from orders.filtersets import ShoppingCartListFilterSet
from orders.models import ShoppingCart
from orders.serializers import ShoppingCartSerializers
from products.bulk_query import bulk_query_products_promotions
from products.models import StockKeepingUnit
from products.models_v2 import ProductDistributePriceSettings
from utils.http_handle import IResponse, custom_django_filter


def _validate_sku(sku_id, desc_text: str = "购物车") -> StockKeepingUnit:
    try:
        sku = StockKeepingUnit.objects.get(sku_id=sku_id)
    except StockKeepingUnit.DoesNotExist:
        raise APIViewException(err_message="data not found")

    if sku.become_history:
        raise APIViewException(err_message=f"该规格已失效，无法加入{desc_text}")

    if sku.product.is_deleted:
        raise APIViewException(err_message=f"商品已删除，无法加入{desc_text}")

    if sku.product.state != 1:
        raise APIViewException(err_message=f"商品未上架，无法加入{desc_text}")

    return sku


class ShoppingCartView(DBAPIView):

    def get_retail_price(self, sku, number):
        product = sku.product
        price_settings_qs = ProductDistributePriceSettings.objects.filter(product=product, set_type=2)

        product_price_settings_map = {s.product_id: s.ladder_settings for s in price_settings_qs}

        ladder_price_infos = product_price_settings_map.get(product.product_id)

        if not ladder_price_infos:
            return None

        if isinstance(ladder_price_infos, str):
            try:
                ladder_price_infos = json.loads(ladder_price_infos)
            except json.JSONDecodeError:
                return None

        if not isinstance(ladder_price_infos, list):
            return None

        distribute_price = None
        for ladder_price_info in ladder_price_infos:
            purchase_count = ladder_price_info.get("purchase_count", 0)
            if number >= purchase_count:
                distribute_price = ladder_price_info.get("distributor_market_price")

        return distribute_price

    def get(self, request):
        distributor_id = request.user.distributor.distributor_id
        shopping_list = (
            ShoppingCart.objects.annotate(
                retail_price=F("sku__retail_price"),
                product_id=F("sku__product__product_id"),
                product_name=F("sku__product__name"),
                product_state=F("sku__product__state"),
                physical_inventory=F("sku__physical_inventory"),
            )
            .filter(
                user_id=request.user.id,
                distributor_id=distributor_id,
            )
            .order_by("-id")
        )
        promotions_map = {}
        if self.is_distributor_mode:
            shopping_list = shopping_list.annotate(
                retail_price=F("sku__distributor_market_price"),
                product_id=F("sku__product__product_id"),
                product_name=F("sku__product__name"),
                product_state=F("sku__product__state"),
                physical_inventory=F("sku__physical_inventory"),
            ).filter(sku__product__is_in_distributor_market=True)

            product_ids = []
            for shopping in shopping_list:
                product_ids.append(shopping.product_id)
            promotions_map = bulk_query_products_promotions(product_ids, self.current_user)

        res_data, _, _ = custom_django_filter(
            request,
            shopping_list,
            filter_set=ShoppingCartListFilterSet,
            iserializer=ShoppingCartSerializers,
            force_order=False,
        )

        if promotions_map:
            data_list = res_data.get("data", [])
            for data in data_list:
                product_id = int(data.get("product_id"))

                if product_id and promotions_map.get(product_id):

                    promotions = promotions_map.get(product_id)
                    data["promotions"] = promotions

        return IResponse(data=res_data)

    def post(self, request):
        """
        单个sku加入购物车
        :param request:
        :return:
        """
        post_data = request.data
        user_pk = self.current_user.pk
        distributor_id = request.user.distributor.distributor_id

        data_source = 1
        if "data_source" in post_data:
            if post_data["data_source"] not in [1, 2]:
                raise APIViewException(err_message="数据来源错误")
            data_source = post_data["data_source"]

        post_data["user_id"] = user_pk
        desc_text = "购物车" if not self.is_distributor_mode else "采购车"

        if not post_data.get("sku_id"):
            return IResponse(code=400, message="invalid params")

        sku_id = post_data["sku_id"]

        # 校验sku
        sku = _validate_sku(sku_id, desc_text)
        # 分销模式
        if self.is_distributor_mode:
            if not sku.product.is_in_distributor_market:
                raise APIViewException(err_message="Product has been delisted")

        if ShoppingCart.objects.filter(user_id=user_pk, distributor_id=distributor_id).count() >= 120:
            return IResponse(code=400, message=f"{desc_text}放不下啦~ 请清理{desc_text}后再操作")

        try:
            shopping_cart_obj = ShoppingCart.objects.get(
                user_id=user_pk,
                sku_id=post_data["sku_id"],
                distributor_id=distributor_id,
                data_source=data_source,
            )
            if post_data.get("number"):
                if not isinstance(post_data["number"], int):
                    raise APIViewException(err_message="数量必须为数字")

                shopping_cart_obj.number += post_data["number"]
            else:
                shopping_cart_obj.number += 1

            if data_source == 2:
                insert_retail_price = self.get_retail_price(sku, post_data["number"])
            else:
                insert_retail_price = sku.distributor_market_price

            shopping_cart_obj.insert_retail_price = insert_retail_price
            shopping_cart_obj.save()
        except ShoppingCart.DoesNotExist:
            post_data["distributor_id"] = distributor_id
            post_data["data_source"] = data_source
            ShoppingCart.objects.create(**post_data)

        return IResponse()

    def patch(self, request):
        """
        批量加入sku批量
        :param request:
        :return:
        """
        post_data = request.data
        distributor_id = request.user.distributor.distributor_id
        user_pk = self.current_user.id
        desc_text = "购物车" if not self.is_distributor_mode else "采购车"

        if "items" not in post_data:
            raise APIViewException()

        data_source = 1
        if "data_source" in post_data:
            if post_data["data_source"] not in [1, 2]:
                raise APIViewException(err_message="数据来源错误")
            data_source = post_data["data_source"]

        if ShoppingCart.objects.filter(user_id=user_pk, distributor_id=distributor_id).count() >= 120:
            return IResponse(code=400, message=f"{desc_text}放不下啦~ 请清理{desc_text}后再操作")

        items = post_data["items"]
        clean_items = [item for item in items if "sku_id" in item]

        sku_id_list = [_item["sku_id"] for _item in clean_items]

        skus = StockKeepingUnit.objects.filter(sku_id__in=sku_id_list)
        sku_qs_map = {str(sku_obj.sku_id): sku_obj for sku_obj in skus}

        if len(skus) != len(sku_id_list):
            return IResponse(code=400, message="规格信息错误，请刷新后重试")

        for sku in skus:
            if sku.become_history:
                return IResponse(code=400, message=f"该规格已失效，无法加入{desc_text}")

            if sku.product.is_deleted:
                return IResponse(code=400, message=f"商品已删除，无法加入{desc_text}")

            if sku.product.state != 1:
                return IResponse(code=400, message=f"商品未上架，无法加入{desc_text}")

        # 将要新增的数量
        need_create_sku_len = len(sku_id_list)
        if ShoppingCart.objects.filter(user_id=user_pk, distributor_id=distributor_id).count() >= 120 - need_create_sku_len:
            return IResponse(code=400, message=f"{desc_text}放不下啦~ 请清理{desc_text}后再操作")

        StockKeepingUnit.objects.filter(sku_id__in=sku_id_list, become_history=True)

        car_qs = ShoppingCart.objects.filter(
            user_id=user_pk,
            sku_id__in=sku_id_list,
            distributor_id=distributor_id,
            data_source=data_source,
        )
        car_qs_map = {str(car_obj.sku_id): car_obj for car_obj in car_qs}

        # 创建或更新
        need_create_objs = []
        need_update_objs = []

        for item in post_data["items"]:
            sku_id = item.get("sku_id")
            if not sku_id:
                continue

            number = item.get("number")
            if not number:
                number = 1
            else:
                if not isinstance(number, int):
                    raise APIViewException(err_message="数量必须填写数字")

            #
            sku_id_str = str(sku_id)
            if data_source == 2:
                insert_retail_price = self.get_retail_price(sku_qs_map[sku_id_str], item.get("number"))
            else:
                insert_retail_price = sku_qs_map[sku_id_str].distributor_market_price

            if sku_id_str in car_qs_map:
                obj = car_qs_map[sku_id_str]
                obj.number = F("number") + number
                if not obj.insert_retail_price:
                    obj.insert_retail_price = insert_retail_price
                need_update_objs.append(obj)
            else:
                need_create_objs.append(
                    ShoppingCart(
                        user_id=user_pk,
                        sku_id=sku_id,
                        distributor_id=distributor_id,
                        number=number,
                        data_source=data_source,
                        insert_retail_price=insert_retail_price,
                    )
                )

        create_objs = []
        with transaction.atomic():
            create_objs = ShoppingCart.objects.bulk_create(need_create_objs, batch_size=1000)
            ShoppingCart.objects.bulk_update(need_update_objs, fields=["number"], batch_size=1000)

        for c_obj in create_objs:
            post_save.send(sender=ShoppingCart, instance=c_obj, created=False, raw=None)

        for u_obj in need_update_objs:
            post_save.send(sender=ShoppingCart, instance=u_obj, created=False, raw=None)

        return IResponse()

    def delete(self, request: Request):
        """
        删除购物车
        :param request:
        :return:
        """
        post_data = request.data
        if "id" not in post_data:
            raise APIViewException()
        pk_list = post_data["id"]
        ShoppingCart.objects.filter(id__in=pk_list, user_id=request.user.id).delete()
        return IResponse(code=200)

    def put(self, request: Request):
        """
        修改购物车单条数据
        :param request:
        :return:
        """
        post_data = request.data
        if "id" not in post_data:
            raise APIViewException()

        try:
            shopping_cart_obj = ShoppingCart.objects.get(id=post_data["id"], user_id=request.user.id)
            if post_data.get("sku_id"):
                shopping_cart_obj.sku_id = post_data["sku_id"]
            if post_data.get("number"):
                shopping_cart_obj.number = post_data["number"]
            shopping_cart_obj.save()
        except ShoppingCart.DoesNotExist:
            return IResponse(code=400, message="data not found")
        return IResponse()


class ShoppingCartCountView(CommonAPIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        distributor_id = request.user.distributor.distributor_id
        cart_qs = ShoppingCart.objects.filter(user_id=request.user.id, distributor_id=distributor_id)
        if not self.is_self_distributor:
            cart_qs = cart_qs.filter(sku__product__is_in_distributor_market=True)
        count = cart_qs.count()
        re_data = {"count": count}
        return IResponse(data=re_data)
