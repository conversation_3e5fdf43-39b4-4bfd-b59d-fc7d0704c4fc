import django_filters

from orders.models import OrderBasedOnSupplier
from orders.new_models import InvoiceApplication
from users.models import User


class ApplyInvoiceFilter(django_filters.rest_framework.FilterSet):
    invoice_title = django_filters.CharFilter(field_name="invoice_title", lookup_expr="icontains")
    product_id = django_filters.CharFilter(method="filter_product_id")
    invoice_date = django_filters.DateFromToRangeFilter(field_name="invoice_date")
    create_invoice_user = django_filters.CharFilter(method="filter_create_invoice_user")

    class Meta:
        model = InvoiceApplication
        fields = ["product_id", "invoice_title", "status", "invoice_date", "create_invoice_user"]

    def filter_product_id(self, queryset, name, value):
        supplier_order_list = OrderBasedOnSupplier.objects.filter(ex_order_id=value).values_list("ex_order_id", flat=True)
        return queryset.filter(invoice_order_map__in=supplier_order_list)

    def filter_create_invoice_user(self, queryset, name, value):
        user_ids = User.objects.filter(real_name__icontains=value).values_list("user_id", flat=True)
        if not user_ids:
            return queryset.none()
        supplier_order_list = OrderBasedOnSupplier.objects.filter(create_user__in=map(str, user_ids)).values_list("ex_order_id", flat=True)
        if not supplier_order_list:
            return queryset.none()
        return queryset.filter(invoice_order_map__in=supplier_order_list)
