# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-07-26 09:09:53
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-12-12 20:33:07
import datetime
import json

from django.core.paginator import Page
from rest_framework import serializers

# from utils.serializers import CustomizeSerializer
from common.basic import CustomizeSerializer
from common.basics.exceptions import APIViewException
from common.formats import DATETIME_FORMAT
from common.models import SystemConfig
from common.utils import sensitive_fields_save_start_end, convert_2_aware_time, get_current_user_type
from companies.caches import get_distributor_info_by_letters
from companies.models import Company, DataShop, Distributor
from order_server import logger
from products.models import StockKeepingUnit, Product, SubProduct
from products.models_v2.distribute_market import ProductDistributePriceSettings
from users.models import AddressManage, User
from utils.caches import get_user_real_name_by_user_id, get_datashop_name_with_id
from .bulk_query import bulk_query_newest_as_order_id, bulk_query_can_apply_qs
from .models.aftersales import AfterSales, AfterSalesRelatedOrderInfo
from .models.bills import Bill, SubBill
from .models.evaluation import OrderEvaluation
from .models.orders import OrderStatusHistory, OrderBasedOnSupplier, OriginalOrder
from .models.shopping_car import ShoppingCart
from .new_models import InvoiceApplication


class OrderStatusHistorySerializers(CustomizeSerializer):
    class Meta:
        model = OrderStatusHistory
        fields = [
            "status",
            "create_date",
        ]


class AfterSalesSerializers(CustomizeSerializer):
    company_id = serializers.CharField(source="company.company_id", allow_blank=True, required=False)
    company_name = serializers.CharField(source="company.name", allow_blank=True, required=False)
    order_id = serializers.CharField(source="order.order_id", allow_blank=True, required=False)

    class Meta:
        model = AfterSales
        fields = [
            "as_id",
            "parent_as_id",
            "company",
            "company_id",
            "company_name",
            "order_id",
            "order_source_id",
            "order_source",
            "count",
            "total_amount",
            "total_cost_amount",
            "shop_freight",
            # "as_type",
            "as_status",
            # "question_type",
            "as_date",
            "create_date",
            "update_date",
        ]


class BillSerialziers(CustomizeSerializer):
    company_id = serializers.CharField(source="company.company_id", allow_blank=True, required=False)
    company_name = serializers.CharField(source="company.name", allow_blank=True, required=False)
    bill_cycle = serializers.CharField(source="company.bill_cycle")

    class Meta:
        model = Bill
        fields = [
            "bill_code",
            "bill_start",
            "bill_end",
            # "company",
            "company_id",
            "company_name",
            "bill_cycle",
            "total_income",
            "total_expenditure",
            "total_amount",
            "total_cost_income",
            "total_cost_expenditure",
            "total_cost_amount",
            "status",
            "request_pay_code",
            "request_pay_username",
            "request_pay_date",
            # "pay_account",
            # "receiver_account",
            "pay_bank_account_name",
            "pay_bank_card_number",
            "pay_bank_branch_name",
            "receiver_bank_account_name",
            "receiver_bank_card_number",
            "receiver_bank_branch_name",
            "pay_amount",
            "payment_proof",
            "remark",
            "create_date",
            "update_date",
        ]


class SubBillSerialziers(CustomizeSerializer):
    business_type = serializers.CharField(source="get_business_type_display")
    bill_type = serializers.CharField(source="get_bill_type_display")
    shop_name = serializers.SerializerMethodField()
    father_business_number = serializers.SerializerMethodField()

    def get_shop_name(self, obj):
        if obj:
            try:
                return DataShop.objects.get(shop_id=obj.shop_id).name
            except DataShop.DoesNotExist:
                return ""
        return ""

    def get_father_business_number(self, obj):
        if obj.business_number:
            try:
                if "PO" in obj.business_number:
                    return OriginalOrder.objects.get(order_id=obj.business_number).order_based_on_supplier.order_id
            except OriginalOrder.DoesNotExist:
                pass
            return obj.business_number
        return ""

    class Meta:
        model = SubBill
        fields = [
            "business_type",
            "business_number",
            "father_business_number",
            "bill_type",
            "shop_id",
            "shop_name",
            "product_amount",
            "settlement_amount",
            "product_cost_amount",
            "settlement_cost_amount",
            "create_date",
            "update_date",
        ]


class SubBillDownloadSerializers(SubBillSerialziers):
    bill_code = serializers.CharField(source="bill.bill_code")
    status = serializers.SerializerMethodField()
    bill_start_end = serializers.SerializerMethodField()

    def get_bill_start_end(self, obj):
        if obj:
            return f"{obj.bill.bill_start}~{obj.bill.bill_end}"
        return ""

    def get_status(self, obj):
        if obj:
            return obj.bill.get_status_display()
        return ""

    class Meta:
        model = SubBill
        fields = [
            "bill_code",
            "status",
            "bill_start_end",
            "business_type",
            "business_number",
            "bill_type",
            "shop_name",
            "product_amount",
            "settlement_amount",
            "product_cost_amount",
            "settlement_cost_amount",
            "create_date",
            "update_date",
        ]


class DYAfterSalesCreateSer(serializers.ModelSerializer):
    class Meta:
        model = AfterSales
        fields = "__all__"


class AfterSalesOrderListSer(serializers.ModelSerializer):
    # 下单方式
    order_platform = serializers.SerializerMethodField()
    # 子售后单
    items = serializers.SerializerMethodField()

    # 所属订单
    class Meta:
        model = AfterSales
        fields = (
            "as_order_id",
            "ex_as_order_id",
            "num",
            "order_platform",
            "store_id",
            "store_name",
            "apply_time",
            "amount",
            "return_shipping_amount",
            "as_type",
            "ex_order_id",
            "as_status_text",
            "reason_text",
            "items",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        objs = self.instance
        if isinstance(self.instance, Page):
            objs = self.instance.object_list

        # 订单查询
        ex_order_id_list = [obj.ex_order_id for obj in objs]

        self.orders_map = {}
        # 分销商名称

    def get_order_platform(self, obj: AfterSales):
        # 下单方式
        relate_order = self.orders_map.get(obj.ex_order_id)
        if not relate_order:
            return
        return relate_order.order_platform

    def get_items(self, obj: AfterSales):
        request = self.context["request"]

        user_type = get_current_user_type(request)
        current_user = request.user

        infos = obj.aftersalesrelatedorderinfo_set.all()

        re_data = []

        for info in infos:
            if user_type == "DB":
                if info.db_letters != current_user.distributor.letters:
                    continue
                # 去除成本价
                info.cost_price = None

            elif user_type == "SP":
                if info.company != current_user.company:
                    continue
                # 价格使用成本价
                info.price = info.cost_price

            db_info = get_distributor_info_by_letters(info.db_letters)

            tmp_spec = [x["value"] for x in info.sku_spec if x["value"]]

            sku_spec_text = ";".join(tmp_spec) or "默认规格"

            re_data.append(
                {
                    "sku_order_id": info.sku_order_id,
                    "product_name": info.product_name,
                    "product_image": info.product_image,
                    "as_num": info.as_num,
                    "get_num": info.get_num,
                    "price": info.price,
                    "cost_price": info.cost_price,
                    "shop_sku_code": info.shop_sku_code,
                    "sku_spec_text": sku_spec_text,
                    "order_id": info.raw_order_id,
                    "distributor_name": db_info.distributor_name,
                    "company_name": info.company.name if info.company_id else None,
                }
            )

        return re_data


class AfterSalesSubOrderListSer(serializers.ModelSerializer):
    as_order_id = serializers.CharField(source="sub_as_order_id")
    ex_as_order_id = serializers.SerializerMethodField()
    num = serializers.IntegerField(source="as_num")
    # 下单平台
    order_platform = serializers.SerializerMethodField()
    #
    store_id = serializers.CharField(source="data_shop_id")
    store_name = serializers.SerializerMethodField()

    apply_time = serializers.SerializerMethodField()
    final_time = serializers.SerializerMethodField()
    amount = serializers.SerializerMethodField()
    return_shipping_amount = serializers.SerializerMethodField()
    as_type = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    status_desc = serializers.SerializerMethodField()
    as_status_text = serializers.SerializerMethodField()
    reason_text = serializers.SerializerMethodField()
    order_user = serializers.CharField(source="create_user")
    refund_state = serializers.SerializerMethodField()
    items = serializers.SerializerMethodField()
    after_sale_status = serializers.SerializerMethodField()
    sub_order_id = serializers.CharField(source="original_order_id", allow_null=True)
    own_order_id = serializers.SerializerMethodField()
    can_apply = serializers.SerializerMethodField()
    can_receipt_goods = serializers.SerializerMethodField()
    logistics_info = serializers.SerializerMethodField()
    create_user_id = serializers.SerializerMethodField()

    class Meta:
        model = AfterSalesRelatedOrderInfo
        fields = (
            "as_order_id",
            "ex_as_order_id",
            "sub_order_id",
            "own_order_id",
            "num",
            "order_platform",
            "store_id",
            "store_name",
            "apply_time",
            "final_time",
            "amount",
            "return_shipping_amount",
            "as_type",
            "status",
            "status_desc",
            "ex_order_id",
            "as_status_text",
            "reason_text",
            "problem_desc",
            "approve_desc",
            "problem_images",
            "items",
            "as_pay_amount",
            "as_post_amount",
            "as_tax_amount",
            "original_order_id",
            "refund_state",
            "is_pass",
            "order_user",
            "after_sale_status",
            "can_apply",
            "can_receipt_goods",
            "logistics_info",
            "create_user_id",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        request = self.context["request"]
        self.user_type = get_current_user_type(request)

        # 查询订单
        objs = self.instance
        if isinstance(objs, Page):
            objs = self.instance.object_list

        self.supplier_order_map = {}

        # 查询关联订单
        original_order_id_list = [data.original_order_id for data in objs if data.original_order_id]
        original_orders = (
            OriginalOrder.objects.filter(order_id__in=original_order_id_list)
            .prefetch_related("order_based_on_supplier")
            .only(
                "order_id",
                "order_platform",
                "after_sale_status",
                "ex_order_id",
                "order_based_on_supplier__order_id",
            )
        )
        self.original_orders_map = {original_order.order_id: original_order for original_order in original_orders}

        self.can_apply_map = bulk_query_can_apply_qs(original_order_id_list)
        # 关联的售后订单
        as_order_id_list = [info.as_order_id for info in objs if info.as_order_id]
        as_orders = AfterSales.objects.filter(as_order_id__in=as_order_id_list)
        self.as_orders_map = {as_order.as_order_id: as_order for as_order in as_orders}

        # 关联供应商
        company_id_list = [info.company_id for info in objs if info.company_id]
        companies = Company.objects.filter(company_id__in=company_id_list).only("company_id", "name")
        self.company_map = {company.company_id: company.name for company in companies}

        user_pk_list = [as_order.create_user for as_order in as_orders if as_order.create_user]
        users = User.objects.filter(pk__in=user_pk_list, query_all=True).values("id", "user_id")
        self.user_id_map = {str(user["id"]): str(user["user_id"]) for user in users}

        # # 查询关联订单
        # conditions = [(obj.ex_order_id, obj.relate_sku.id) for obj in objs if not obj.original_order_id]
        # query = Q()
        #
        # for field1_val, field2_val in conditions:
        #     query |= Q(ex_order_id=field1_val, sku_id=field2_val)
        #
        # results = OriginalOrder.objects.filter(query).values("ex_order_id", "sku_id", "order_id", "order_based_on_supplier__order_id")
        # order_map = {f"{result['ex_order_id']}-{result['sku_id']}": result["order_id"] for result in results if result['sku_id']}
        # # 触发更新订单信号
        # if order_map:
        #     as_order_relate_update.send(sender=None, order_map=order_map)
        #
        # self.supplier_order_map = {f"{result['ex_order_id']}-{result['sku_id']}": result["order_based_on_supplier__order_id"] for result in results}

    def get_create_user_id(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return self.user_id_map.get(as_order.create_user, "")

    @staticmethod
    def get_store_name(obj: AfterSalesRelatedOrderInfo):
        return get_datashop_name_with_id(str(obj.data_shop_id))

    def get_refund_state(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return as_order.refund_state

    def get_reason_text(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return as_order.reason_text

    def get_as_status_text(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return as_order.as_status_text

    def get_status_desc(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return as_order.get_status_display()

    def get_as_type(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return as_order.as_type

    def get_status(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return as_order.status

    def get_ex_as_order_id(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return as_order.ex_as_order_id

    def get_apply_time(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return as_order.apply_time

    def get_final_time(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return ""
        return as_order.final_time

    def get_logistics_info(self, obj: AfterSalesRelatedOrderInfo):
        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return {}

        if not as_order.return_logistics_code:
            return {}

        return {
            "return_logistics_company_name": as_order.return_logistics_company_name,
            "return_logistics_code": as_order.return_logistics_code,
        }

    def get_order_platform(self, obj: AfterSalesRelatedOrderInfo):
        if not obj.original_order_id:
            return

        original_order = self.original_orders_map.get(obj.original_order_id)
        if not original_order:
            return
        return original_order.order_platform

    def get_can_receipt_goods(self, obj: AfterSalesRelatedOrderInfo):
        if not obj.original_order_id:
            return False

        original_order = self.original_orders_map.get(obj.original_order_id)
        if not original_order:
            return False

        as_order = self.as_orders_map.get(obj.as_order_id)
        if not as_order:
            return False

        if original_order.after_sale_status == 7 and as_order.return_logistics_code:
            return True
        return False

    def get_can_apply(self, obj: AfterSalesRelatedOrderInfo):
        if not obj.original_order_id:
            return False

        original_order = self.original_orders_map.get(obj.original_order_id)
        if not original_order:
            return False
        return not self.can_apply_map.get(obj.original_order_id)

    def get_own_order_id(self, obj: AfterSalesRelatedOrderInfo):
        if not obj.original_order_id:
            return

        original_order = self.original_orders_map.get(obj.original_order_id)
        if not original_order:
            return

        if not original_order.order_based_on_supplier_id:
            return

        return original_order.order_based_on_supplier.order_id

    def get_after_sale_status(self, obj: AfterSalesRelatedOrderInfo):
        if not obj.original_order_id:
            return 0

        original_order = self.original_orders_map.get(obj.original_order_id)
        if not original_order:
            return 0
        return original_order.after_sale_status or 0

    def self_product_id(self, obj: AfterSalesRelatedOrderInfo):
        if self.user_type == "DB":
            return obj.relate_sub_product_id or obj.relate_product_id

        return obj.relate_product_id

    @staticmethod
    def get_amount(obj: AfterSalesRelatedOrderInfo):
        return obj.as_pay_amount

    @staticmethod
    def get_return_shipping_amount(obj: AfterSalesRelatedOrderInfo):
        return obj.as_post_amount

    def get_items(self, obj: AfterSalesRelatedOrderInfo):
        re_data = []
        sku_code = obj.handled_sku_code
        if self.user_type == "DB":
            # 去除成本价
            obj.cost_price = None
            sku_code = obj.shop_sku_code
        elif self.user_type == "SP":
            # 下单金额使用成本价
            obj.order_pay_amount = obj.cost_price

        db_info = get_distributor_info_by_letters(obj.db_letters)

        tmp_spec = [x["value"] for x in obj.sku_spec if x["value"]]

        sku_spec_text = ";".join(tmp_spec) or "默认规格"

        if obj.original_order_id:
            original_order = self.original_orders_map.get(obj.original_order_id)
            if not original_order:
                order_id = ""
            else:
                order_id = original_order.ex_order_id
        else:
            order_id = self.supplier_order_map.get(f"{obj.ex_order_id}-{obj.relate_sku_id}")

        re_data.append(
            {
                "sku_order_id": obj.sku_order_id,
                "product_id": self.self_product_id(obj),
                "product_name": obj.product_name,
                "product_image": obj.product_image,
                "as_num": obj.as_num,
                "get_num": obj.get_num,
                # 下单金额
                "price": obj.price,
                # 成本价
                "cost_price": obj.cost_price,
                "order_pay_amount": obj.order_pay_amount,
                "shop_sku_code": sku_code,
                "sku_spec_text": sku_spec_text,
                "order_id": order_id,
                "distributor_name": db_info.distributor_name,
                "company_name": (self.company_map.get(obj.company_id, "") or "") if obj.company_id else None,
            }
        )

        return re_data


class AfterSalesSubOrderListDownloadSer(serializers.ModelSerializer):
    as_order_id = serializers.CharField(source="sub_as_order_id")
    ex_as_order_id = serializers.CharField(source="as_order.ex_as_order_id")
    num = serializers.IntegerField(source="as_num")
    # 下单平台
    order_platform = serializers.CharField(source="original_order.order_platform", allow_null=True)
    #
    store_id = serializers.CharField(source="data_shop.shop_id")
    store_name = serializers.CharField(source="data_shop.name")

    apply_time = serializers.DateTimeField(source="as_order.apply_time", format=DATETIME_FORMAT, allow_null=True)
    final_time = serializers.DateTimeField(source="as_order.final_time", format=DATETIME_FORMAT, allow_null=True)
    amount = serializers.SerializerMethodField()
    return_shipping_amount = serializers.SerializerMethodField()
    as_type = serializers.CharField(source="as_order.get_as_type_display")
    as_status_text = serializers.CharField(source="as_order.as_status_text")
    reason_text = serializers.CharField(source="as_order.reason_text")
    items = serializers.SerializerMethodField()

    class Meta:
        model = AfterSalesRelatedOrderInfo
        fields = (
            "as_order_id",
            "ex_as_order_id",
            "num",
            "order_platform",
            "store_id",
            "store_name",
            "apply_time",
            "final_time",
            "amount",
            "return_shipping_amount",
            "as_type",
            "ex_order_id",
            "as_status_text",
            "reason_text",
            "items",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        request = self.context["request"]
        self.user_type = get_current_user_type(request)

        # 查询订单
        objs = self.instance
        if isinstance(objs, Page):
            objs = self.instance.object_list
        self.supplier_order_map = {}
        # 查询关联订单
        # conditions = [(obj.ex_order_id, obj.relate_sku.id) for obj in objs if not obj.original_order_id]
        # query = Q()
        #
        # for field1_val, field2_val in conditions:
        #     query |= Q(ex_order_id=field1_val, sku_id=field2_val)
        #
        # results = OriginalOrder.objects.filter(query).values("ex_order_id", "sku_id", "order_id", "order_based_on_supplier__order_id")
        # order_map = {f"{result['ex_order_id']}-{result['sku_id']}": result["order_id"] for result in results if result['sku_id']}
        # # 触发更新订单信号
        # if order_map:
        #     as_order_relate_update.send(sender=None, order_map=order_map)
        #
        # self.supplier_order_map = {f"{result['ex_order_id']}-{result['sku_id']}": result["order_based_on_supplier__order_id"] for result in results}

    def self_product_id(self, obj: AfterSalesRelatedOrderInfo):
        if self.user_type == "DB":
            return obj.relate_sub_product_id

        return obj.relate_product_id

    @staticmethod
    def get_amount(obj: AfterSalesRelatedOrderInfo):
        return obj.as_pay_amount

    @staticmethod
    def get_return_shipping_amount(obj: AfterSalesRelatedOrderInfo):
        return obj.as_post_amount

    def get_items(self, obj: AfterSalesRelatedOrderInfo):

        re_data = []
        sku_code = obj.handled_sku_code
        if self.user_type == "DB":
            # 去除成本价
            obj.cost_price = None
            sku_code = obj.shop_sku_code
        elif self.user_type == "SP":
            # 价格使用成本价
            obj.order_pay_amount = obj.cost_price

        db_info = get_distributor_info_by_letters(obj.db_letters)

        tmp_spec = [x["value"] for x in obj.sku_spec if x["value"]]

        sku_spec_text = ";".join(tmp_spec) or "默认规格"

        if obj.original_order:
            order_id = obj.original_order.order_based_on_supplier.order_id
        else:
            order_id = self.supplier_order_map.get(f"{obj.ex_order_id}-{obj.relate_sku.id}")

        re_data.append(
            {
                "sku_order_id": obj.sku_order_id,
                "product_id": self.self_product_id(obj),
                "product_name": obj.product_name,
                "product_image": obj.product_image,
                "as_num": obj.as_num,
                "get_num": obj.get_num,
                # 下单金额
                "price": obj.price,
                "cost_price": obj.cost_price,
                "shop_sku_code": sku_code,
                "sku_spec_text": sku_spec_text,
                "order_id": order_id,
                "distributor_name": db_info.distributor_name,
                "company_name": obj.company.name if obj.company_id else None,
            }
        )

        return re_data


class AfterSalesSubOrderDetailSer(serializers.ModelSerializer):
    as_order_id = serializers.CharField(source="sub_as_order_id")
    # 下单平台
    order_platform = serializers.CharField(source="original_order.order_platform", allow_null=True)
    as_type = serializers.IntegerField(source="as_order.as_type")
    apply_time = serializers.DateTimeField(source="as_order.apply_time")
    final_time = serializers.DateTimeField(source="as_order.final_time")
    distributor_name = serializers.SerializerMethodField()
    company_id = serializers.IntegerField(source="company.company_id", allow_null=True)
    company_name = serializers.CharField(source="company.name", allow_null=True)
    order_id = serializers.SerializerMethodField()
    receiver_info = serializers.SerializerMethodField()
    logistics_info = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()
    total_num = serializers.SerializerMethodField()
    items = serializers.SerializerMethodField()
    reason_text = serializers.CharField(source="as_order.reason_text")
    status = serializers.IntegerField(source="as_order.status")
    status_desc = serializers.CharField(source="as_order.get_status_display")
    as_order_remark = serializers.CharField(source="as_order.shop_remark")
    order_user = serializers.CharField(source="as_order.create_user")
    got_pkg = serializers.CharField(source="as_order.got_pkg")
    order_time = serializers.CharField(source="original_order.order_time", allow_null=True)
    pay_time = serializers.CharField(source="original_order.pay_time", allow_null=True)
    pay_type = serializers.CharField(source="original_order.pay_type", allow_null=True)
    as_status_text = serializers.CharField(source="as_order.as_status_text", allow_null=True)
    refund_state = serializers.IntegerField(source="as_order.refund_state", allow_null=True)
    total_price = serializers.SerializerMethodField()
    as_logistics_address = serializers.SerializerMethodField()
    approve_user = serializers.SerializerMethodField()
    as_status_placeholder = serializers.SerializerMethodField()
    after_sale_status = serializers.SerializerMethodField()
    sub_order_id = serializers.CharField(source="original_order_id", allow_null=True)
    own_order_id = serializers.SerializerMethodField()
    can_apply = serializers.SerializerMethodField()
    can_receipt_goods = serializers.SerializerMethodField()
    create_user_id = serializers.SerializerMethodField()

    class Meta:
        model = AfterSalesRelatedOrderInfo
        fields = (
            "as_order_id",
            "as_type",
            "distributor_name",
            "company_id",
            "company_name",
            "order_id",
            "sub_order_id",
            "own_order_id",
            "order_platform",
            "apply_time",
            "final_time",
            "receiver_info",
            "logistics_info",
            "items",
            "total_amount",
            "total_num",
            "reason_text",
            "problem_desc",
            "approve_desc",
            "problem_images",
            "approve_date",
            "approve_user",
            "status",
            "status_desc",
            "as_status_text",
            "as_status_placeholder",
            "refund_state",
            "as_order_remark",
            "order_user",
            "is_pass",
            "got_pkg",
            "order_time",
            "pay_time",
            "pay_type",
            "order_pay_amount",
            "order_post_amount",
            "total_price",
            "as_logistics_address",
            "after_sale_status",
            "can_apply",
            "can_receipt_goods",
            "create_user_id",
        )

    def get_create_user_id(self, obj: AfterSalesRelatedOrderInfo):
        user = User.objects.filter(pk=obj.create_user, query_all=True).first()
        if not user:
            return
        return str(user.user_id)

    def get_can_receipt_goods(self, obj: AfterSalesRelatedOrderInfo):
        if not obj.original_order:
            return False

        if obj.original_order.after_sale_status == 7 and obj.as_order.return_logistics_code:
            return True
        return False

    def get_can_apply(self, obj: AfterSalesRelatedOrderInfo):
        order_obj = obj.original_order
        if not order_obj:
            return False
        return not order_obj.aftersalesrelatedorderinfo_set.filter(
            is_pass__in=[0, 1],
            as_order__status=1,
        ).exists()

    def get_after_sale_status(self, obj: AfterSalesRelatedOrderInfo):
        if not obj.original_order:
            return 0
        return obj.original_order.after_sale_status or 0

    def get_as_status_placeholder(self, obj: AfterSalesRelatedOrderInfo):
        # 取消的售后单
        main_as_order = obj.as_order
        is_pass = obj.is_pass

        if main_as_order.status == 2:
            return main_as_order.as_status_text

        if is_pass == 0:
            return "请等待审核..."

        as_type = main_as_order.as_type
        if as_type == 2:
            if main_as_order.final_time:
                return "售后完成"
            # 仅退款
            return main_as_order.get_refund_state_display()
        elif as_type == 0:
            # 没快递单号
            if not main_as_order.return_logistics_code:
                return "商品寄回确认收货后，支付金额原路返回微信账户，请查看！"

            if main_as_order.return_logistics_code and not main_as_order.final_time:
                if obj.confirm_receipt_user:
                    return "商家已确认收货，支付金额原路返回微信账户，请查看！"
                return "商品寄回确认收货后，支付金额原路返回微信账户，请查看！"

            if main_as_order.final_time:
                return "售后完成"
            return f"商家确认收货,{main_as_order.get_refund_state_display()}"
        elif as_type == 3:
            if main_as_order.final_time:
                return "售后完成"
            return "商品寄回确认收货后，重新换货寄出，请留意快递！"

    def get_approve_user(self, obj: AfterSalesRelatedOrderInfo):
        return get_user_real_name_by_user_id(obj.approve_user)

    def get_as_logistics_address(self, obj: AfterSalesRelatedOrderInfo):

        try:
            sys_obj = SystemConfig.objects.get(code=SystemConfig.AFTER_SALES_DEFAULT_ADDRESS)
            return json.loads(sys_obj.value)
        except (SystemConfig.DoesNotExist, Exception):
            return {}

    def get_total_price(self, obj: AfterSalesRelatedOrderInfo):
        if obj.price and obj.buy_num:
            return obj.price * obj.buy_num
        return None

    def get_order_id(self, obj: AfterSalesRelatedOrderInfo):
        if obj.original_order_id:
            return obj.original_order.order_based_on_supplier.order_id

        order = OriginalOrder.objects.filter(ex_order_id=obj.ex_order_id, sku_id=obj.relate_sku.id).only("order_id").last()
        if not order:
            return
        return order.order_based_on_supplier.order_id

    def get_own_order_id(self, obj: AfterSalesRelatedOrderInfo):
        if obj.original_order_id:
            return obj.original_order.order_based_on_supplier.order_id

        order = OriginalOrder.objects.filter(ex_order_id=obj.ex_order_id, sku_id=obj.relate_sku.id).only("order_id").last()
        if not order:
            return
        return order.order_based_on_supplier.order_id

    @staticmethod
    def get_distributor_name(obj: AfterSalesRelatedOrderInfo):
        return get_distributor_info_by_letters(obj.db_letters).distributor_name

    @staticmethod
    def get_receiver_info(obj: AfterSalesRelatedOrderInfo):
        return {
            "receiver_name": obj.as_order.receiver_name,
            "receiver_tel": obj.as_order.receiver_tel,
            "receiver_address": obj.as_order.receiver_address,
        }

    @staticmethod
    def get_logistics_info(obj: AfterSalesRelatedOrderInfo):
        if not obj.as_order.return_logistics_code:
            return {}

        return {
            "return_logistics_company_name": obj.as_order.return_logistics_company_name,
            "return_logistics_code": obj.as_order.return_logistics_code,
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        request = self.context["request"]
        user_type = get_current_user_type(request)
        current_user = request.user

        if user_type == "DB":
            # 去除成本价
            self.instance.cost_price = None

        elif user_type == "SP":
            # 价格使用成本价
            self.instance.order_pay_amount = self.instance.cost_price

    @staticmethod
    def get_total_amount(obj: AfterSalesRelatedOrderInfo):
        return obj.order_pay_amount

    @staticmethod
    def get_total_num(obj: AfterSalesRelatedOrderInfo):
        return obj.as_num

    @staticmethod
    def get_items(obj: AfterSalesRelatedOrderInfo):

        re_data = []

        tmp_spec = [x["value"] for x in obj.sku_spec if x["value"]]

        sku_spec_text = ";".join(tmp_spec) or "默认规格"
        order_id = None
        if obj.original_order_id:
            order_id = obj.original_order.order_id
        else:
            order = OriginalOrder.objects.filter(ex_order_id=obj.ex_order_id, sku_id=obj.relate_sku.id).only("order_id").last()
            if order:
                order_id = order.order_based_on_supplier.order_id

        re_data.append(
            {
                "sku_order_id": obj.sku_order_id,
                "product_id": obj.product_id,
                "product_name": obj.product_name,
                "product_image": obj.product_image,
                "as_num": obj.as_num,
                # 使用下单金额
                "price": obj.price,
                "amount": obj.order_pay_amount,
                "shop_sku_code": obj.shop_sku_code,
                "sku_spec_text": sku_spec_text,
                "order_id": order_id,
            }
        )

        return re_data


class OrderBasedOnSupplierSerializer(serializers.ModelSerializer):
    sub = serializers.SerializerMethodField()
    company_id = serializers.SerializerMethodField()
    company_name = serializers.SerializerMethodField()
    distributor_id = serializers.SerializerMethodField()
    distributor_name = serializers.SerializerMethodField()
    cost_price_amount = serializers.SerializerMethodField()
    order_amount = serializers.SerializerMethodField()
    pay_amount = serializers.SerializerMethodField()
    paid_amount = serializers.SerializerMethodField()
    pay_time = serializers.SerializerMethodField()
    pay_type = serializers.SerializerMethodField()
    pay_type_desc = serializers.SerializerMethodField()
    order_expire_time = serializers.SerializerMethodField()
    post_amount = serializers.SerializerMethodField()
    logistics_company_name = serializers.SerializerMethodField()
    ship_time = serializers.SerializerMethodField()
    tracking_no = serializers.SerializerMethodField()
    order_type = serializers.SerializerMethodField()
    receiver_address_detail = serializers.SerializerMethodField()
    after_sale_status = serializers.SerializerMethodField()
    buyer_words = serializers.SerializerMethodField()
    order_time = serializers.SerializerMethodField()
    create_user = serializers.SerializerMethodField()
    create_user_id = serializers.CharField(source="create_user")
    is_invoice_status = serializers.SerializerMethodField(read_only=True)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.request = self.context["request"]
        self.user_type = get_current_user_type(self.request)

        objs = self.instance
        if isinstance(objs, Page):
            objs = self.instance.object_list
        else:
            objs = [objs]
        version_qs = OriginalOrder.objects.filter(order_based_on_supplier__in=objs).values(
            "order_based_on_supplier_id",
            "sku_version",
            "product_version",
            "pay_time",
            "pay_type",
            "order_expire_time",
            "logistics_company_name",
            "tracking_no",
            "ship_time",
            "order_type",
            "mask_post_addr",
            "buyer_words",
            # "after_sale_status",
        )
        sku_versions = {i["sku_version"] for i in version_qs if i.get("sku_version")}
        product_versions = {i["product_version"] for i in version_qs if i.get("product_version")}
        # # 查询所有相关的历史记录
        sku_history_qs = StockKeepingUnit.history.filter(history_id__in=sku_versions)
        product_history_qs = Product.history.filter(history_id__in=product_versions)

        self.sku_histories = {history.history_id: history for history in sku_history_qs}
        self.product_histories = {history.history_id: history for history in product_history_qs}

        # # 支付时间
        self._original_order_map = {obj["order_based_on_supplier_id"]: obj for obj in version_qs}

        #
        self._original_order_list_map = {}
        for obj in version_qs:
            if obj["order_based_on_supplier_id"] not in self._original_order_list_map:
                self._original_order_list_map[obj["order_based_on_supplier_id"]] = [obj]
            else:
                self._original_order_list_map[obj["order_based_on_supplier_id"]].append(obj)

        #
        newest_order_id_list = [i.order_id for obj in objs for i in obj.originalorder_set.all()]
        # 批量查询最新的售后订单id
        self.newest_as_order_relate_map = bulk_query_newest_as_order_id(newest_order_id_list)

        user_id_list = [_i.create_user for _i in objs if _i.create_user]
        users = User.objects.filter(user_id__in=user_id_list, query_all=True).only("user_id", "real_name")
        self.users_map = {str(u.user_id): u.real_name for u in users}

    class Meta:
        model = OrderBasedOnSupplier
        fields = [
            "order_id",
            "ex_order_id",
            "count",
            "order_amount",
            "pay_amount",
            "paid_amount",
            "cost_price_amount",
            "post_amount",
            "total_promotion_amount",
            "letters",
            "author_id",
            "author_name",
            "order_status",
            "order_type",
            "receiver_name",
            "receiver_phone",
            "receiver_province_name",
            "receiver_province_id",
            "receiver_city_name",
            "receiver_city_id",
            "receiver_town_name",
            "receiver_town_id",
            "receiver_street_name",
            "receiver_street_id",
            "shop_id",
            "shop_name",
            # "ship_time",
            "delivery_method",
            "order_time",
            "post_promotion_amount",
            "company_id",
            "company_name",
            "distributor_id",
            "distributor_name",
            "order_platform",
            "sub",
            "pay_time",
            "pay_type",
            "pay_type_desc",
            "order_expire_time",
            "logistics_company_name",
            "ship_time",
            "tracking_no",
            "receiver_address_detail",
            "after_sale_status",
            "buyer_words",
            "create_user",
            "create_user_id",
            "is_invoice_status",
        ]

    def get_create_user(self, obj):
        return self.users_map.get(obj.create_user)

    @staticmethod
    def get_order_time(obj: OrderBasedOnSupplier):
        return convert_2_aware_time(obj.order_time)

    def get_after_sale_status(self, obj: OrderBasedOnSupplier):
        return (self._original_order_map.get(obj.pk) or {}).get("after_sale_status", 0) or 0

    def get_receiver_address_detail(self, obj: OrderBasedOnSupplier):
        return ((self._original_order_map.get(obj.pk) or {}).get("mask_post_addr", {}) or {}).get("detail", "")

    def get_order_type(self, obj: OrderBasedOnSupplier):
        return (self._original_order_map.get(obj.pk) or {}).get("order_type", 0) or 0

    def get_buyer_words(self, obj: OrderBasedOnSupplier):
        return (self._original_order_map.get(obj.pk) or {}).get("buyer_words")

    def get_logistics_company_name(self, obj: OrderBasedOnSupplier):
        return (self._original_order_map.get(obj.pk) or {}).get("logistics_company_name")

    def get_ship_time(self, obj: OrderBasedOnSupplier):
        return (self._original_order_map.get(obj.pk) or {}).get("ship_time")

    def get_tracking_no(self, obj: OrderBasedOnSupplier):
        return (self._original_order_map.get(obj.pk) or {}).get("tracking_no")

    def get_post_amount(self, obj: OrderBasedOnSupplier):
        return obj.post_amount or 0

    def get_order_expire_time(self, obj: OrderBasedOnSupplier):
        return (self._original_order_map.get(obj.pk) or {}).get("order_expire_time")

    def get_pay_time(self, obj: OrderBasedOnSupplier):
        return (self._original_order_map.get(obj.pk) or {}).get("pay_time")

    def get_pay_type(self, obj: OrderBasedOnSupplier):
        return (self._original_order_map.get(obj.pk) or {}).get("pay_type")

    def get_pay_type_desc(self, obj: OrderBasedOnSupplier):
        _pay_type_map = {
            "1": "微信支付",
        }

        return _pay_type_map.get(str((self._original_order_map.get(obj.pk) or {}).get("pay_type")), "未知")

    def get_sub(self, obj: OrderBasedOnSupplier):
        return OriginalOrderSerializer(
            instance=obj.originalorder_set.all(),
            many=True,
            context={
                "request": self.request,
                "sku_histories": self.sku_histories,
                "product_histories": self.product_histories,
                "newest_as_order_relate_map": self.newest_as_order_relate_map,
            },
        ).data

    def get_company_name(self, obj: OrderBasedOnSupplier):
        return obj.company.name if obj.company_id else None

    def get_company_id(self, obj: OrderBasedOnSupplier):
        return obj.company.company_id if obj.company_id else None

    def get_distributor_name(self, obj: OrderBasedOnSupplier):
        return obj.distributor.name if obj.distributor else None

    def get_distributor_id(self, obj: OrderBasedOnSupplier):
        return obj.distributor.distributor_id if obj.distributor else None

    def get_cost_price_amount(self, obj: OrderBasedOnSupplier):
        if self.user_type != "SP":
            return None
        return obj.cost_price_amount

    def get_order_amount(self, obj: OrderBasedOnSupplier):
        if self.user_type == "SP":
            return None
        return obj.order_amount

    def get_pay_amount(self, obj: OrderBasedOnSupplier):
        if self.user_type == "SP":
            return None
        return obj.pay_amount

    def get_paid_amount(self, obj: OrderBasedOnSupplier):
        if self.user_type == "SP":
            return None
        return obj.paid_amount

    def get_is_invoice_status(self, obj):
        ex_order_id = obj.ex_order_id
        print(f"ex_order_id:{ex_order_id}")
        if not ex_order_id:
            return None
        return True if InvoiceApplication.objects.filter(invoice_order_map=ex_order_id).exists() else False


class OrderBasedOnSupplierDownloadSerializer(OrderBasedOnSupplierSerializer):
    order_status = serializers.CharField(source="get_order_status_display")
    order_platform = serializers.CharField(source="get_order_platform_display")
    ship_time = serializers.SerializerMethodField()

    def get_ship_time(self, obj: OrderBasedOnSupplier):
        ship_time = (self._original_order_map.get(obj.pk) or {}).get("ship_time")
        if isinstance(ship_time, datetime.datetime):
            return (ship_time + datetime.timedelta(hours=8)).strftime("%Y-%m-%d %H:%M:%S")
        return ship_time


class OriginalOrderSerializer(serializers.ModelSerializer):
    product_name = serializers.SerializerMethodField()
    product_id = serializers.SerializerMethodField()
    product_main_images = serializers.SerializerMethodField()
    code = serializers.SerializerMethodField()
    sku_specs = serializers.SerializerMethodField()
    sku_spec_code = serializers.SerializerMethodField()
    # sku_retail_price = serializers.CharField(source="sku.retail_price", allow_null=True)
    cost_price_amount = serializers.SerializerMethodField()
    order_amount = serializers.SerializerMethodField()
    pay_amount = serializers.SerializerMethodField()
    paid_amount = serializers.SerializerMethodField()
    sku_cost_price = serializers.SerializerMethodField()
    sku_id = serializers.IntegerField(source="sku.sku_id", allow_null=True)
    after_sale_status = serializers.SerializerMethodField()
    newest_as_order_id = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        request = self.context["request"]
        self.user_type = get_current_user_type(request)
        self.sku_histories = self.context["sku_histories"]
        self.product_histories = self.context["product_histories"]

        objs = self.instance
        if isinstance(objs, Page):
            objs = objs.object_list

        # 批量查询最新的售后订单id
        self.newest_as_order_relate_map = self.context["newest_as_order_relate_map"]

    class Meta:
        model = OriginalOrder
        fields = [
            "order_id",
            "newest_as_order_id",
            "product_name",
            "product_id",
            "product_main_images",
            "code",
            "sku_id",
            "sku_specs",
            "sku_spec_code",
            # "sku_retail_price",
            "item_num",
            "ship_time",
            "tracking_no",
            "logistics_company",
            "logistics_company_name",
            "sku_cost_price",
            "goods_price",
            "order_amount",
            "refund_status",
            "pay_amount",
            "paid_amount",
            "cost_price_amount",
            "order_status",
            "after_sale_status",
            "after_sale_type",
            "mask_post_addr",
        ]

    def get_newest_as_order_id(self, obj: OriginalOrder):
        return self.newest_as_order_relate_map.get(obj.order_id)

    @staticmethod
    def get_after_sale_status(obj: OriginalOrder):
        return obj.after_sale_status or 0

    def get_product_name(self, obj: OriginalOrder):
        product_history = self.product_histories.get(obj.product_version)
        if product_history:
            return product_history.name
        return None

    def get_product_id(self, obj: OriginalOrder):
        product_history = self.product_histories.get(obj.product_version)
        if product_history:
            return product_history.product_id
        return None

    def get_product_main_images(self, obj: OriginalOrder):
        product_history = self.product_histories.get(obj.product_version)
        if product_history:
            return product_history.main_images
        return []

    def get_code(self, obj: OriginalOrder):
        product_history = self.product_histories.get(obj.product_version)
        if product_history:
            return product_history.code
        return None

    def get_sku_specs(self, obj: OriginalOrder):
        sku_history = self.sku_histories.get(obj.sku_version)
        if sku_history:
            if not sku_history.specs_data:
                return []
            return sku_history.specs_data
        return []

    def get_sku_spec_code(self, obj: OriginalOrder):
        sku_history = self.sku_histories.get(obj.sku_version)
        if sku_history:
            return sku_history.spec_code
        return []

    def get_cost_price_amount(self, obj: OrderBasedOnSupplier):
        if self.user_type != "SP":
            return None
        return obj.cost_price_amount

    def get_order_amount(self, obj: OrderBasedOnSupplier):
        if self.user_type == "SP":
            return None
        return obj.order_amount

    def get_pay_amount(self, obj: OrderBasedOnSupplier):
        if self.user_type == "SP":
            return None
        return obj.pay_amount

    def get_paid_amount(self, obj: OrderBasedOnSupplier):
        if self.user_type == "SP":
            return None
        return obj.paid_amount

    def get_sku_cost_price(self, obj: OrderBasedOnSupplier):
        if self.user_type != "SP":
            return None
        sku_history = self.sku_histories.get(obj.sku_version)
        if sku_history:
            return sku_history.cost_price
        return None


def get_skus_specs_detail(obj: StockKeepingUnit):
    specs_names = StockKeepingUnit.specs_name.through._meta.model.objects.select_related("specskey").filter(stockkeepingunit=obj).order_by("id")

    specs_names_map = {specs_name.specskey_id: specs_name.specskey.name for specs_name in specs_names}

    specs_values_relations = (
        StockKeepingUnit.specs_value.through._meta.model.objects.select_related("specsvalue")
        .filter(
            stockkeepingunit=obj,
            specsvalue__spec_key_id__in=specs_names_map.keys(),
        )
        .order_by("id")
    )
    val_data = []
    for specs_values_relation in specs_values_relations:
        value_id = specs_values_relation.specsvalue_id
        value = specs_values_relation.specsvalue.value
        value_key_id = specs_values_relation.specsvalue.spec_key_id
        val_data.append(
            {
                "name_id": value_key_id,
                "value_id": value_id,
                "value": value,
            }
        )

    re_data = []
    tmp_list = []
    for name_id, name in specs_names_map.items():
        tmp_list.append(
            {
                "name_id": name_id,
                "name": name,
                "values": [
                    {
                        "value_id": val["value_id"],
                        "value": val["value"],
                    }
                    for val in val_data
                    if val["name_id"] == name_id
                ],
            }
        )

    for val in tmp_list:
        _values = val["values"]
        for _val in _values:
            re_data.append(
                {
                    "name_id": val["name_id"],
                    "name": val["name"],
                    "value_id": _val["value_id"],
                    "value": _val["value"],
                }
            )

    return re_data


class ShoppingCartSerializers(serializers.ModelSerializer):
    specs_name = serializers.SerializerMethodField()
    specs_value = serializers.SerializerMethodField()
    retail_price = serializers.SerializerMethodField()
    product_id = serializers.CharField()
    # product_name = serializers.CharField()
    product_name = serializers.SerializerMethodField()
    product_state = serializers.CharField()
    physical_inventory = serializers.CharField()
    image = serializers.SerializerMethodField()
    become_history = serializers.BooleanField(source="sku.become_history")
    data_source_desc = serializers.CharField(source="get_data_source_display")

    # v2.9.7增加涨降价标识
    price_fluctuation = serializers.SerializerMethodField()
    #
    warehouse_inventory = serializers.SerializerMethodField()

    class Meta:
        model = ShoppingCart
        fields = [
            "id",
            # "user_id",
            "number",
            "sku_id",
            "specs_name",
            "specs_value",
            "retail_price",
            "product_id",
            "product_name",
            "product_state",
            "physical_inventory",
            "image",
            "become_history",
            "data_source",
            "data_source_desc",
            "price_fluctuation",
            "warehouse_inventory",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        _instance = self.instance
        if isinstance(_instance, Page):
            _instance = _instance.object_list

        # 商品product_id列表
        product_id_list = {i.product_id for i in _instance}
        price_settings_qs = ProductDistributePriceSettings.objects.filter(product_id__in=product_id_list, set_type=2)
        #
        self.product_price_settings_map = {s.product_id: s.ladder_settings for s in price_settings_qs}

    def get_retail_price(self, obj: ShoppingCart):
        if obj.data_source == 1:
            return getattr(obj, "retail_price")

        ladder_price_infos = self.product_price_settings_map.get(getattr(obj, "product_id", None))
        if not ladder_price_infos:
            return

        distribute_price = None
        for ladder_price_info in list(ladder_price_infos):
            purchase_count = ladder_price_info["purchase_count"]
            if obj.number >= purchase_count:
                distribute_price = ladder_price_info["distributor_market_price"]

        return distribute_price

    @staticmethod
    def get_specs_name(obj):
        if obj:
            return " ".join([related.name for related in obj.sku.specs_name.all()])
        return ""

    @staticmethod
    def get_specs_value(obj):
        if obj:
            return " ".join([related.value for related in obj.sku.specs_value.all()])
        return ""

    @staticmethod
    def get_image(obj):
        """
        获取商品图片
        :param obj: 包含 sku 和 distributor 的对象
        :return: 商品的主图 URL
        """
        try:
            # 获取商品对象
            product = obj.sku.product

            # 如果商品不在分销市场，直接返回商品的主图
            if not product.is_in_distributor_market:
                return product.main_images[0] if product.main_images else ""

            # 获取分销商对象
            owner = Distributor.objects.filter(letters="FX").first()
            # 查询子商品
            sub_product = SubProduct.origin_objects.filter(parent_product=product, owner=owner, parent_product__is_in_distributor_market=True).order_by("-id").first()  # 按 ID 倒序获取最新的子商品

            # 如果子商品存在且包含主图，返回子商品的主图
            if sub_product and sub_product.main_images:
                return sub_product.main_images[0]

            # 否则返回商品的主图
            return product.main_images[0] if product.main_images else ""

        except AttributeError as e:
            # 处理对象属性缺失的情况
            logger.error(f"获取商品图片时发生错误：{str(e)}", exc_info=True)
            return None
        except Exception as e:
            # 处理其他异常
            logger.error(f"获取商品图片时发生未知错误：{str(e)}", exc_info=True)
            return None

    def get_price_fluctuation(self, obj):
        insert_retail_price = obj.insert_retail_price
        if not insert_retail_price:
            return None
        if obj.data_source == 1:
            sku_distributor_market_price = obj.sku.distributor_market_price
        else:
            sku_distributor_market_price = self.get_retail_price(obj)
        if insert_retail_price > sku_distributor_market_price:
            return "降价"
        elif insert_retail_price < sku_distributor_market_price:
            return "涨价"
        return None

    @staticmethod
    def get_warehouse_inventory(obj):
        if obj.sku:
            warehouse_inventory = obj.sku.warehouse_inventory
            return warehouse_inventory if warehouse_inventory else 0
        return 0

    def get_product_name(self, obj):
        # 检查 obj.sku 和 obj.sku.product 是否存在
        if not obj.sku or not hasattr(obj.sku, "product") or not obj.sku.product:
            return "未知商品"

        product = obj.sku.product

        # 如果产品在分销商市场中
        if product.is_in_distributor_market:
            # 检查分销商对象是否存在
            if not obj.distributor:
                return "未知商品"

            # owner = obj.distributor
            owner = Distributor.objects.filter(letters="FX").first()
            # 安全查询 SubProduct
            sub_product = SubProduct.origin_objects.filter(parent_product=product, owner=owner, parent_product__is_in_distributor_market=True).last()

            # 如果没有找到子产品，返回主产品的名称
            if not sub_product or not sub_product.name:
                return product.name
            # 返回子产品的名称
            return sub_product.name

        # 默认返回主产品的名称
        return product.name


class OrderCreateSer(serializers.Serializer):
    receiver_address_id = serializers.IntegerField(required=True, error_messages={"required": "缺少收件人信息,请刷新页面后重试"})
    buyer_words = serializers.CharField(allow_null=True, allow_blank=True, default="")
    products_info = serializers.ListField(required=True, error_messages={"required": "缺少商品信息,请刷新页面后重试"})

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._request = self.context["request"]

    def validate_address(self, receiver_address_id: int):
        try:
            AddressManage.objects.get(pk=receiver_address_id, user_id=self._request.user.pk, is_deleted=False)
        except AddressManage.DoesNotExist:
            raise APIViewException(err_message="收件信息不存在,请刷新页面后重试")

        return receiver_address_id

    @staticmethod
    def validate_products_info(products_info: list):
        sku_id_list = []

        for product_info in products_info:
            if not isinstance(product_info, dict):
                raise APIViewException(err_message="商品信息格式错误,请刷新页面后重试")
            sku_id = product_info.get("sku_id")
            num = product_info.get("num")
            if not sku_id or not num:
                raise APIViewException(err_message="缺少规格信息或规格数量,请刷新页面后重试")

            if not isinstance(num, int):
                raise APIViewException(err_message="规格数量类型错误")

            if sku_id in sku_id_list:
                raise APIViewException(err_message="所选商品包含重复规格数据,请刷新页面后重试")

            sku_id_list.append(sku_id_list)

        skus = StockKeepingUnit.objects.filter(
            sku_id__in=sku_id_list,
            become_history=False,
            product__is_deleted=False,
            product__company__is_self_support=True,
        )
        if not skus:
            raise APIViewException(err_message="规格信息不存在,请刷新页面后重试")

        skus_map = {str(sku.sku_id): sku for sku in skus}
        clean_data = []
        for product_info in products_info:
            sku_id = product_info.get("sku_id")
            num = product_info.get("num")
            if str(sku_id) not in skus_map:
                raise APIViewException(err_message=f"规格:{sku_id}不存在,请刷新页面后重试")
            _sku = product_info[str(sku_id)]
            if _sku.physical_inventory < num:
                raise APIViewException(err_message=f"规格:{sku_id}库存不足")

            clean_data.append({"sku_id": sku_id, "num": num})
        return clean_data


class OrderEvaluationList(serializers.ModelSerializer):
    create_user = serializers.SerializerMethodField()
    avater = serializers.SerializerMethodField()
    product_name = serializers.CharField(source="product.name")
    item_num = serializers.CharField(source="original_order.item_num", allow_blank=True, default="")
    sku_id = serializers.CharField(source="original_order.sku.sku_id", allow_blank=True, default="")
    specs_value = serializers.SerializerMethodField()
    main_images = serializers.SerializerMethodField()
    cost_price = serializers.CharField(source="original_order.sku.cost_price", allow_blank=True, default="")
    pay_amount = serializers.CharField(source="original_order.pay_amount", allow_blank=True, default="")
    order_id = serializers.CharField(source="original_order.order_based_on_supplier.order_id", allow_blank=True, default="")
    sub_order_id = serializers.CharField(source="original_order.sub_order_id", allow_blank=True, default="")
    ex_order_id = serializers.CharField(source="original_order.ex_order_id", allow_blank=True, default="")

    class Meta:
        model = OrderEvaluation
        fields = (
            "evaluation_id",
            "images",
            "video",
            "content",
            "scoring",
            "product_id",
            "product_name",
            "create_user",
            "create_date",
            "avater",
            "item_num",
            "sku_id",
            "specs_value",
            "main_images",
            "cost_price",
            "pay_amount",
            "order_id",
            "sub_order_id",
            "ex_order_id",
        )

    def get_create_user(self, obj):
        try:
            return sensitive_fields_save_start_end(User.objects.get(user_id=obj.create_user).username)
        except User.DoesNotExist:
            return ""

    def get_avater(self, obj):
        try:
            return User.objects.get(user_id=obj.create_user).avater
        except User.DoesNotExist:
            return ""

    def get_specs_value(self, obj):
        try:
            return ",".join(obj.original_order.sku.specs_value.values_list("value", flat=True))
        except Exception:
            return ""

    def get_main_images(self, obj):
        try:
            return obj.original_order.sku.product.main_images[0]
        except Exception:
            return ""


class OrderTodoEvaluationList(serializers.ModelSerializer):
    sub_order_id = serializers.CharField(source="order_id")
    order_id = serializers.CharField(source="order_based_on_supplier.order_id", allow_blank=True, default="")
    product_id = serializers.CharField(source="sku.product.product_id", allow_blank=True, default="")
    product_name = serializers.CharField(source="sku.product.name", allow_blank=True, default="")
    sku_id = serializers.CharField(source="sku.sku_id", allow_blank=True, default="")
    specs_value = serializers.SerializerMethodField()
    main_images = serializers.SerializerMethodField()
    cost_price = serializers.CharField(source="sku.cost_price", allow_blank=True, default="")

    class Meta:
        model = OriginalOrder
        fields = (
            "sub_order_id",
            "order_id",
            "ex_order_id",
            "product_id",
            "product_name",
            "main_images",
            "pay_amount",
            "cost_price",
            "item_num",
            "specs_value",
            "sku_id",
            "create_date",
        )

    def get_specs_value(self, obj):
        try:
            return ",".join(obj.sku.specs_value.values_list("value", flat=True))
        except Exception:
            return ""

    def get_main_images(self, obj):
        try:
            return obj.sku.product.main_images[0]
        except Exception:
            return ""
