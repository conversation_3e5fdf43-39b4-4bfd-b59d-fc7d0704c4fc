# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-08-10 10:50:37
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-12-12 15:44:58

# 2024.02.27 使用新的账单生成方案：
# 一、原方案
# 1.定期创建空账单
# 2.定期账单更新：订单、售后
# 3.定期账单结算

# 二、新方案
# 1.有订单、售后单进入时创建账单
# 2.定期扫描账单周期订单、售后单数据创建账单（查漏）
# 3.定期账单更新：订单、售后
# 4.定期账单结算
import decimal
import json
import math
import time
import traceback
from datetime import datetime, timedelta, date
from hashlib import md5

from django.conf import settings
from django.core.paginator import Paginator
from django.core.serializers.json import DjangoJSONEncoder
from django.db import transaction
from django.db.models import F, Sum, Value, IntegerField, Prefetch, Max, Q, <PERSON><PERSON><PERSON>, OuterRef, Min, Count
from django.db.models.functions import <PERSON>run<PERSON><PERSON><PERSON>, Concat, ExtractWeek, Cast
from django.db.utils import IntegrityError
from django.utils import timezone

from common.basics.paginator import self_cursor_paginator
from common.formats import DATETIME_FORMAT, DATE_FORMAT
from common.models import SystemConfig, get_dy_order_fetcher_config
from common.sources import WECHAT_LOGISTICS_MAP
from common.utils import convert_datatime_val_2_aware_time, convert_timestamp_2_aware_time, convert_2_aware_time
from common.utils import parse_spec_code
from companies.models import Company, Distributor, jst_order_application, dy_order_application, DataShop
from order_server.models import FetchConfigs
from order_server.tasks import process_order_data
from orders import logger
from products.data_shops_logics import execute_order_request
from products.data_shops_logics import get_data_shop_access_token
from products.models import (
    StockKeepingUnit,
    Product,
    ProductSelectionItem,
    ProductSalesCount,
    ProductLabelTypes,
    ProductLabelsRelate,
    ProductLabels,
    ProductLabelsRule,
    ProductDBSalesCount,
    SubProduct,
    HistoryPrice,
    ProductSelectionItemSKU,
    ProductAfterSalesCount,
    TempProduct,
    TempProductDetail,
    SKUDistributorSalesCount,
    SKUSalesCount,
)
from users.models import User
from utils.doudian.api.afterSale_List.AfterSaleListRequest import AfterSaleListRequest
from utils.doudian.api.order_searchList.OrderSearchListRequest import OrderSearchListRequest
from utils.feishu import FeiShuDocx
from utils.http_handle import get_bill_start_and_end
from utils.jst import JSTAPIClient, jst_client
from utils.redis_lock import gen_redis_conn, redis_conn, redis_cache
from utils.wechat_client import mall_client
from utils.wechatpyV3_utils import check_refund_status
from zhulinks_supply_chains_backend.celery import app
from .handlers import ASOrderHandlerFactory, OrderHandlerFactory
from .messages import dispatch_goods_notification_send, refund_notification_send, order_finish_notification_send
from .models.aftersales import AfterSales, AfterSalesRelatedOrderInfo, AfterSalesOrderFailed
from .models.bills import Bill, SubBill
from .models.common import OrderSKUInventoryChangeLog
from .models.orders import OrderBasedOnSupplier, OriginalOrder, WriteOrderFailed, DyOrderSubscribe, DyOrderSubscribeMessage, SyncOrderToJSTFail
from .raw_query import query_db_sales_count

# from django_elasticsearch_dsl.registries import registry

# 订单单次查询的最大量
MAX_ENTRIES = 50000
TASK_QUEUE_KEY = "jst_order_task_list"


def _convert_to_ware_time(native_time):
    tz = timezone.get_current_timezone()
    res = timezone.make_aware(datetime.strptime(native_time, "%Y-%m-%d %H:%M:%S"), tz)
    return res


@app.task(queue="long_running_tasks")
def bill_settlement(this_moment=None):
    """
    4.定期账单结算

    根据账单结算周期进行账单结算
    """
    if this_moment:
        target_bill_end = datetime.strptime(this_moment, "%Y-%m-%d").date()

    else:
        today = timezone.now().date()
        target_bill_end = today - timezone.timedelta(days=1)
    bill_qs = Bill.objects.filter(bill_end=target_bill_end, status="TS")
    if bill_qs:
        bill_qs.update(status="TC", update_date=timezone.now())


@app.task(queue="common_tasks")
def create_bill_when_insert_order_as(order_date: str, company_id: Company.id):
    """
    1.有订单、售后单进入时创建账单
    """
    order_date_time = _convert_to_ware_time(order_date)
    bill_start, bill_end = get_bill_start_and_end(order_date_time)
    company_start_end = f"{company_id}_{bill_start}_{bill_end}"
    # 公司对应的周期账单已经存在
    if Bill.objects.filter(company_start_end=company_start_end).exists():
        return
    _data = {
        "company_id": company_id,
        "bill_start": bill_start,
        "bill_end": bill_end,
        "company_start_end": f"{company_id}_{bill_start}_{bill_end}",
    }
    bill = Bill.objects.create(**_data)
    return f"bill: {bill} created"


@app.task(queue="long_running_tasks")
def bulk_create_bill_by_order_as(bill_start: str, bill_end: str, company_ids: list):
    """
    批量创建账单
    company_ids: list->[Company.id]
    """
    tz = timezone.get_current_timezone()
    bill_start = timezone.make_aware(datetime.strptime(bill_start, "%Y-%m-%d"), tz).date()
    bill_end = timezone.make_aware(datetime.strptime(bill_end, "%Y-%m-%d"), tz).date()
    bills = []
    for company_id in company_ids:
        bill = Bill(
            company_id=company_id,
            bill_start=bill_start,
            bill_end=bill_end,
            company_start_end=f"{company_id}_{bill_start}_{bill_end}",
        )
        bills.append(bill)
    Bill.objects.bulk_create(
        bills,
        update_conflicts=True,
        update_fields=[
            "update_date",
        ],
        unique_fields=[
            "company_start_end",
        ],
    )


@app.task(queue="long_running_tasks")
def create_bill_by_order_as(this_moment=None):
    """
    2.定期扫描账单周期订单、售后单数据创建账单（查漏）
    不再统一为所有供应商创建账单
    """
    if this_moment:
        this_moment = _convert_to_ware_time(this_moment)
    else:
        this_moment = timezone.now()
    bill_start, bill_end = get_bill_start_and_end(this_moment)
    # 获取结算周期内的所有订单-公司
    order_company_qs = (
        OriginalOrder.objects.filter(
            finish_time__range=[
                timezone.make_aware(datetime(bill_start.year, bill_start.month, bill_start.day) - timezone.timedelta(days=7), timezone.get_current_timezone()),
                timezone.make_aware(datetime(bill_end.year, bill_end.month, bill_end.day) - timezone.timedelta(days=7) + timedelta(hours=23, minutes=59, seconds=59), timezone.get_current_timezone()),
            ],
        )
        .values_list("order_based_on_supplier__company_id", flat=True)
        .distinct("order_based_on_supplier__company_id")
        .order_by("order_based_on_supplier__company_id")
    )
    paginator = Paginator(order_company_qs, 1000)  # 分批处理，每批1000条
    for page_num in paginator.page_range:
        comany_ids = [company_id for company_id in paginator.page(page_num).object_list]
        bulk_create_bill_by_order_as.delay(datetime.strftime(bill_start, "%Y-%m-%d"), datetime.strftime(bill_end, "%Y-%m-%d"), comany_ids)

    # 获取结算周期内的所有售后单-公司
    as_company_qs = (
        AfterSalesRelatedOrderInfo.objects.filter(
            as_order__final_time__range=[
                timezone.make_aware(datetime(bill_start.year, bill_start.month, bill_start.day), timezone.get_current_timezone()),
                timezone.make_aware(datetime(bill_end.year, bill_end.month, bill_end.day) + timedelta(hours=23, minutes=59, seconds=59), timezone.get_current_timezone()),
            ],
        )
        .values_list("company_id", flat=True)
        .distinct("company_id")
        .order_by("company_id")
    )
    paginator = Paginator(as_company_qs, 1000)  # 分批处理，每批1000条
    for page_num in paginator.page_range:
        comany_ids = [company_id for company_id in paginator.page(page_num).object_list]
        bulk_create_bill_by_order_as.delay(datetime.strftime(bill_start, "%Y-%m-%d"), datetime.strftime(bill_end, "%Y-%m-%d"), comany_ids)

    # 有新增账单时立马更新一次账单明细
    update_bill(this_moment.strftime("%Y-%m-%d %H:%M:%S"))


@app.task(queue="long_running_tasks")
def create_bill(this_moment=None):
    """
    创建基于某时间点的空结算周期账单
    """
    try:
        if this_moment:
            this_moment = _convert_to_ware_time(this_moment)
        else:
            this_moment = timezone.now()

        bill_start, bill_end = get_bill_start_and_end(this_moment)
        company = Company.objects.all().values("id", "company_id").order_by("-id")
        # 设置批次大小
        page_size = 50
        paginator = Paginator(company, page_size)
        total_page = paginator.num_pages
        for i in range(total_page):
            bills = []
            page_model_objects = paginator.page(i + 1)
            for company in page_model_objects:
                bill = Bill(
                    company_id=company["id"],
                    bill_start=bill_start,
                    bill_end=bill_end,
                    company_start_end=f"{company['company_id']}_{bill_start}_{bill_end}",
                )
                bills.append(bill)
            Bill.objects.bulk_create(
                bills,
                update_conflicts=True,
                update_fields=[
                    "update_date",
                ],
                unique_fields=[
                    "company_start_end",
                ],
            )
        return "Success"
    except Exception as e:
        logger.info(str(e))
        return f"Failed: {str(e)}"


# WaitPay 不接入
ORDER_STATUS_MAP = {
    "Delivering": "RD",
    "Merged": "RD",
    "Question": "RD",
    "Split": "RD",
    "WaitOuterSent": "RD",
    "WaitConfirm": "RD",
    "WaitFConfirm": "RD",
    "Sent": "DL",
    "Cancelled": "CL",
    105: "RD",
    2: "RD",
    101: "RD",
    3: "DL",
    4: "CL",
    5: "FN",
}


def convert_to_ware_time_in_dict(fields, raw_data):
    tz = timezone.get_current_timezone()
    for field in fields:
        if raw_data.get(field):
            raw_data[field] = timezone.make_aware(datetime.strptime(raw_data[field], "%Y-%m-%d %H:%M:%S"), tz)


def update_inventory_and_live(product_id, sku_id, raw_data):
    order_datetime = raw_data["order_date"]
    if isinstance(order_datetime, str):
        order_date = datetime.strptime(order_datetime, DATE_FORMAT)
    elif isinstance(order_datetime, datetime):
        order_date = order_datetime.strftime(DATE_FORMAT)

    # 更新货盘表item的has live
    items = ProductSelectionItem.objects.filter(
        product=product_id,
        selection_plan__distributor__letters=raw_data["letters"],
        selection_plan__live_date_start__lte=order_date,
        selection_plan__live_date_end__gte=order_date,
        is_deleted=False,
    )
    items.update(has_live=True)
    for item in items:
        update_sku = item.product.stockkeepingunit_set.filter(sku_id=sku_id).first()
        if not update_sku:
            continue
        # 不需要修改库存
        # update_sku.physical_inventory = F("physical_inventory") - raw_data["count"]
        update_sku.sales = F("sales") + raw_data["count"]
        # 信号数据
        update_sku.__update_user__ = "system"
        update_sku.save()


# ====== 更新账单 ======


@app.task(queue="long_running_tasks")
def update_bill(this_moment=None, cycle_delta=0, cycle=7):
    """
    3.定期账单更新：订单、售后

    （1）获取本结算周期的所有父账单
    （2）根据父亲账单所属公司及结算周期获取本周期内的所有订单数据，并进行总账单的更新计算、子账单的创建与更新
    （3）根据父亲账单所属公司及结算周期获取本周期内的所有售后单数据，并进行总账单的更新计算、子帐单的创建与更新

    this_moment: 结算周期所在时间，可以设置任意时间所在的结算周期
    cycle_delta: 往前周期数
    cycle: 周期长度
    """
    try:
        if this_moment:
            this_moment = _convert_to_ware_time(this_moment)
        else:
            if cycle_delta:
                this_moment = timezone.now() - timedelta(days=cycle_delta * cycle)
            else:
                this_moment = timezone.now()
        bill_start, bill_end = get_bill_start_and_end(this_moment)
        logger.info(f"start update bill time:{str(bill_start)}, {str(bill_end)}")
        bill_qs = Bill.objects.filter(bill_start=bill_start, bill_end=bill_end).values("bill_code").order_by("-id")
        # 设置批次大小
        page_size = 50
        paginator = Paginator(bill_qs, page_size)
        total_page = paginator.num_pages
        for i in range(total_page):
            page_model_objects = paginator.page(i + 1)
            for bill in page_model_objects:
                update_or_create_bill(bill["bill_code"])
        return "Success Sent Task"
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        return str(e)


@app.task(queue="long_running_tasks")
def update_or_create_bill(bill_code):
    update_or_create_bill_by_order(bill_code)
    update_or_create_bill_by_as(bill_code)


def update_or_create_bill_by_order(bill_code):
    """
    通过订单更新账单
    根据父亲账单所属公司及结算周期获取本周期内的所有订单数据，并进行总账单的更新计算、子账单的创建与更新
    """
    try:
        bill = Bill.objects.filter(bill_code=bill_code).first()
        if not bill:
            logger.warn(f"bill_code: {bill_code} not found")
            return
        order_qs = OriginalOrder.objects.filter(
            order_based_on_supplier__company=bill.company,
            finish_time__range=[
                timezone.make_aware(datetime(bill.bill_start.year, bill.bill_start.month, bill.bill_start.day) - timezone.timedelta(days=7), timezone.get_current_timezone()),
                timezone.make_aware(
                    datetime(bill.bill_end.year, bill.bill_end.month, bill.bill_end.day) - timezone.timedelta(days=7) + timedelta(hours=23, minutes=59, seconds=59), timezone.get_current_timezone()
                ),
            ],
        ).order_by("-id")
        logger.info(f"order_qs----: {len(order_qs)}")
        total_income = 0
        total_cost_income = 0
        subbills = []
        logger.info(f"filter order_qs, {order_qs}")
        for order in order_qs.values("order_id", "paid_amount", "cost_price_amount", "shop_id").iterator():
            if order.get("paid_amount"):
                total_income += order["paid_amount"]
            if order.get("cost_price_amount"):
                total_cost_income += order["cost_price_amount"]

            sub_bill_data_order = {
                "product_cost_amount": order["cost_price_amount"],
                "settlement_cost_amount": order["cost_price_amount"],
                "product_amount": order["paid_amount"],
                "settlement_amount": order["paid_amount"],
            }
            subbill = SubBill(
                bill=bill,
                shop_id=order["shop_id"],
                business_number=order["order_id"],
                business_type="PO",
                bill_type="IN",
                bill_business_number_type=f"{bill.bill_code}_{order['order_id']}_IN",
                **sub_bill_data_order,
            )
            subbills.append(subbill)
        if len(subbills):
            with transaction.atomic():
                SubBill.objects.bulk_create(
                    subbills,
                    update_conflicts=True,
                    update_fields=[
                        "product_cost_amount",
                        "settlement_cost_amount",
                        "product_amount",
                        "settlement_amount",
                    ],
                    unique_fields=[
                        "bill_business_number_type",
                    ],
                )
                # logger.info(f"od_bill_code1: {bill_code}, total_income: {total_income}, total_cost_income: {total_cost_income}")
                # logger.info(f"od_bill_code2: {bill_code}, bill.total_expenditure: {bill.total_expenditure}, bill.total_cost_expenditure: {bill.total_cost_expenditure}")
                total_amount = total_income - bill.total_expenditure if bill.total_expenditure else total_income
                total_cost_amount = total_cost_income - bill.total_cost_expenditure if bill.total_cost_expenditure else total_cost_income

                # logger.info(f"od_bill_code3: {bill_code}, total_amount: {total_amount}, total_cost_amount: {total_cost_amount}")

                bill.total_income = total_income
                bill.total_cost_income = total_cost_income
                bill.total_amount = total_amount
                bill.total_cost_amount = total_cost_amount
                bill.update_date = timezone.now()
                update_fields = [
                    "total_income",
                    "total_cost_income",
                    "total_amount",
                    "total_cost_amount",
                    "update_date",
                ]
                bill.save(update_fields=update_fields)
        return f"Success"

    except Exception as e:
        logger.error(f"update_or_create_bill_by_order Err: {str(e)}")
        return f"Failed: {str(e)}"


def update_or_create_bill_by_as(bill_code):
    """
    通过售后单更新账单
    根据父亲账单所属公司及结算周期获取本周期内的所有售后单数据，并进行总账单的更新计算、子帐单的创建与更新
    """
    try:
        bill = Bill.objects.filter(bill_code=bill_code).first()
        if not bill:
            logger.warn(f"bill_code: {bill_code} not found")
            return
        as_qs = AfterSalesRelatedOrderInfo.objects.filter(
            company=bill.company,
            as_order__final_time__range=[
                timezone.make_aware(datetime(bill.bill_start.year, bill.bill_start.month, bill.bill_start.day), timezone.get_current_timezone()),
                timezone.make_aware(datetime(bill.bill_end.year, bill.bill_end.month, bill.bill_end.day) + timedelta(hours=23, minutes=59, seconds=59), timezone.get_current_timezone()),
            ],
        ).order_by("-id")

        total_expenditure = 0
        total_cost_expenditure = 0
        subbills = []
        for _as in as_qs.values("sub_as_order_id", "price", "cost_price", "data_shop__shop_id").iterator():
            if _as.get("price"):
                logger.info(f"price: {_as['sub_as_order_id']}--{_as['price']}")
                total_expenditure += _as["price"]
            if _as.get("cost_price"):
                logger.info(f"cost_price: {_as['sub_as_order_id']}--{_as['cost_price']}")
                total_cost_expenditure += _as["cost_price"]

            sub_bill_data_as = {
                "product_cost_amount": _as["cost_price"],
                "settlement_cost_amount": _as["cost_price"],
                "product_amount": _as["price"],
                "settlement_amount": _as["price"],
            }
            subbill = SubBill(
                bill=bill,
                shop_id=_as["data_shop__shop_id"],
                business_number=_as["sub_as_order_id"],
                business_type="AS",
                bill_type="EX",
                bill_business_number_type=f"{bill.bill_code}_{_as['sub_as_order_id']}_EX",
                **sub_bill_data_as,
            )
            subbills.append(subbill)
        with transaction.atomic():
            SubBill.objects.bulk_create(
                subbills,
                update_conflicts=True,
                update_fields=[
                    "product_cost_amount",
                    "settlement_cost_amount",
                    "product_amount",
                    "settlement_amount",
                ],
                unique_fields=[
                    "bill_business_number_type",
                ],
            )

            # logger.info(f"as_bill_code1: {bill_code}, total_expenditure: {total_expenditure}, total_cost_expenditure: {total_cost_expenditure}")
            # logger.info(f"as_bill_code2: {bill_code}, bill.total_income: {bill.total_income}, bill.total_cost_income: {bill.total_cost_income}")

            total_amount = bill.total_income - total_expenditure if bill.total_income else decimal.Decimal(0.00) - total_expenditure
            total_cost_amount = bill.total_cost_income - total_cost_expenditure if bill.total_cost_income else decimal.Decimal(0.00) - total_cost_expenditure

            # logger.info(f"as_bill_code3: {bill_code}, total_amount: {total_amount}, total_cost_amount: {total_cost_amount}")

            bill.total_expenditure = total_expenditure
            bill.total_cost_expenditure = total_cost_expenditure
            bill.total_amount = total_amount
            bill.total_cost_amount = total_cost_amount
            bill.update_date = timezone.now()
            update_fields = [
                "total_expenditure",
                "total_cost_expenditure",
                "total_amount",
                "total_cost_amount",
                "update_date",
            ]
            bill.save(update_fields=update_fields)
    except Exception as e:
        logger.error(f"update_or_create_bill_by_order Err: {str(e)}")
        return f"Failed: {str(e)}"


# ====== 插入售后数据 ======

AS_STATUS_MAP = {
    "已确认": "FN",
    "待确认": "WS",
}

QUESTIONS_TYPE_MAP = {
    "尺码拍错/不喜欢/效果差": "BW",  # 商品信息拍错(规格/尺码/颜色等）
    "大小尺寸与商品描述不符": "BW",
    "7天无理由退换货": "NW",  # 不想要了
    "不想买了": "NW",
    "买错了": "NW",
    "支持7天无理由退货(拆封后不支持)": "NW",
    "支持7天无理由退货(激活后不支持)": "NW",
    "支持7天无理由退货(安装后不支持)": "NW",
    "支持7天无理由退货(定制类不支持)": "NW",
    "支持7天无理由退货(使用后不支持)": "NW",
    "商品少件或破损": "PD",  # 商品破损/包装问题
    "丢件或快递破损": "PD",
    "商品质量问题": "PQ",  # 商品价格问题(买贵、降价、质量等）
    "条码或吊牌与商品不符": "PQ",
    "质量问题": "PQ",
    "颜色/款式/图案与描述不符": "PQ",
    "假冒品牌": "PQ",
    "重复购买": "BM",  # 拍多了
    "买错了/买多了": "BM",
    "未按约定时间发货": "LD",  # 未按约定时间发货
    "缺货": "OS",  # 缺货
    "其他": "OT",  # 其他
    "服务承诺或态度": "OT",
    "卖家发错货": "OT",
    "退运费": "OT",
    "差价": "OT",
    "补偿": "OT",
    "客人原因": "OT",
    "客服原因": "OT",
    "仓库原因": "OT",
    "商品与商家宣传不符": "OT",
    "快递/物流一直未送到": "OT",
}

AS_TYPE_MAP = {
    "仅退款": "OR",  # 仅退款
    "普通退货": "RG",  # 退货退款
    "拒收退货": "RG",  # 退货退款
    "补发": "RG",  # 退货退款
    "投诉": "RG",  # 退货退款
    "维修": "RG",  # 退货退款
    "其他": "RG",  # 退货退款
    "退货退款": "RG",  # 退货退款
    "换货": "RP",  # 换货
    "门店换货": "RP",  # 换货
}


# ======================== 统计数据 START ========================
@app.task(queue="common_tasks")
def calculate_product_sales(recursion_depth: int = 0, max_recursion_depth: int = 100, page_size=1000):
    """
    【不再使用，移动到 daily_product_sales_calc_task】
    每小时统计商品的销量
    :return:
    """

    start_time = time.time()
    date_format = "%Y-%m-%d"
    create_rows = update_rows = 0
    chunk_size = 1000

    cache_key = "product_sales_calculate_start_id"
    start_cursor = redis_cache.get(cache_key)
    orders = OriginalOrder.objects.filter(sku__isnull=False, pay_time__isnull=False, order_type=0).only("id", "order_time", "sku", "item_num")
    # 分页处理
    queryset, next_cursor = self_cursor_paginator(orders, start_cursor, page_size=page_size)

    if queryset:
        order_id_list = [q.id for q in queryset]
        #
        sales_count_items = (
            OriginalOrder.objects.filter(id__in=order_id_list)
            .annotate(calc_date=TruncDate("order_time"))
            .annotate(product=F("sku__product"))
            .values("calc_date", "product")
            .annotate(sales_count=Sum("item_num"))
            .values("calc_date", "product", "sales_count")
        )
        product_pk_list = set([x["product"] for x in sales_count_items if x["product"]])
        counts = ProductSalesCount.objects.filter(product_id__in=product_pk_list)
        counts_map = {f"{count.product_id}-{count.calc_date.strftime(date_format)}": count for count in counts}

        need_update_objs = []
        need_create_objs = []

        for data in sales_count_items:
            product_pk = data["product"]
            calc_date = data["calc_date"]
            sales_count = data["sales_count"]

            map_key = f"{product_pk}-{calc_date.strftime(date_format)}"
            if map_key not in counts_map:
                need_create_objs.append(ProductSalesCount(product_id=product_pk, sales=sales_count, calc_date=calc_date))
            else:
                tmp_obj = counts_map[map_key]
                # 初始化
                if not start_cursor:
                    tmp_obj.sales = sales_count
                else:
                    # 增量
                    tmp_obj.sales = tmp_obj.sales + sales_count

                need_update_objs.append(tmp_obj)

        if need_create_objs:
            create_rows += len(ProductSalesCount.objects.bulk_create(need_create_objs, batch_size=chunk_size))

        if need_update_objs:
            update_rows += ProductSalesCount.objects.bulk_update(need_update_objs, fields=["sales"], batch_size=chunk_size)

        redis_cache.set(cache_key, next_cursor, timeout=None)

        if next_cursor:
            if recursion_depth < max_recursion_depth:
                logger.info(f"---- 统计每日销量, 订单ID: {start_cursor}-{next_cursor}, 创建:{create_rows}, 更新:{update_rows}. 耗时:{time.time() - start_time}")
                calculate_product_sales(recursion_depth + 1, max_recursion_depth)
            else:
                calculate_product_sales.delay(max_recursion_depth=max_recursion_depth, page_size=page_size)


@app.task(queue="common_tasks")
def calculate_after_sales_hourly():
    """
    每小时统计商品的售后
    :return: str
    """
    start_time = time.time()
    date_format = "%Y-%m-%d"
    create_rows = update_rows = 0
    chunk_size = 1000

    conn = gen_redis_conn()

    _cache_key = "product_after_sales_calc_start_id"
    start_id = int(conn.getset(_cache_key, 1) or 1)

    max_obj = AfterSalesRelatedOrderInfo.objects.only("id").last()
    max_id = max_obj.id if max_obj and max_obj.id != 1 else 2

    if start_id == max_id:
        return f"SUCCESS,售后订单最大ID相同,不需要统计.当前最大:{max_id}"

    if start_id != 1:
        start_id += 1

    sales_count_items = (
        AfterSalesRelatedOrderInfo.objects.filter(id__range=(start_id, max_id))
        .filter(relate_product__isnull=False)
        .annotate(calc_date=TruncDate("as_order__apply_time"))
        .values("calc_date", "relate_product__id")
        .annotate(refund_count=Sum("as_num"))
    )

    product_pk_list = set([x["relate_product__id"] for x in sales_count_items if x["relate_product__id"]])
    counts = ProductAfterSalesCount.objects.filter(product_id__in=product_pk_list)
    counts_map = {f"{count.product_id}-{count.calc_date.strftime(date_format)}": count for count in counts}

    need_update_objs = []
    need_create_objs = []

    for data in sales_count_items:
        product_pk = data["relate_product__id"]
        calc_date = data["calc_date"]
        refund_count = data["refund_count"]

        map_key = f"{product_pk}-{calc_date.strftime(date_format)}"

        if map_key not in counts_map:
            need_create_objs.append(ProductAfterSalesCount(product_id=product_pk, count=refund_count, calc_date=calc_date))
        else:
            tmp_obj = counts_map[map_key]
            tmp_obj.count = F("count") + refund_count if start_id != 1 else refund_count
            need_update_objs.append(tmp_obj)

    if need_create_objs:
        create_rows += len(ProductAfterSalesCount.objects.bulk_create(need_create_objs, batch_size=chunk_size))

    if need_update_objs:
        update_rows += ProductAfterSalesCount.objects.bulk_update(need_update_objs, fields=["count"], batch_size=chunk_size)

    conn.set(_cache_key, max_id)

    logger.info(f"---- 统计每日售后销量, 创建:{create_rows}, 更新:{update_rows}. 耗时:{time.time() - start_time}")

    return f"SUCCESS, 创建:{create_rows}, 更新:{update_rows}"


@app.task(queue="common_tasks")
def calculate_day_sales_labels():
    """
    每小时计算商品标签
    :return:
    """
    start_time = time.time()
    now_date = datetime.now().date()
    label_type, _ = ProductLabelTypes.objects.get_or_create(name="销售标签")
    label, _ = ProductLabels.objects.update_or_create(
        name="当日热销",
        l_type=label_type,
        is_deleted=False,
        defaults={
            "display": True,
            "is_normal": False,
            "can_edit": False,
        },
    )

    # 将前面的热销置为历史数据
    ProductLabelsRelate.objects.filter(label=label, label_date__lt=now_date, become_history=False).update(become_history=True)

    # 获取规则
    rule, _ = ProductLabelsRule.objects.get_or_create(label=label)
    if not rule.value or not rule.value.isdigit():
        rule.value = 500
        rule.save(update_fields=["value"])
    # 阈值
    threshold = int(rule.value)

    # 不符合条件的设置为become_history
    invalid_product_id_list = ProductSalesCount.objects.select_related("product").filter(calc_date=now_date, sales__lt=threshold).values_list("product__product_id", flat=True)
    invalid_update_rows = ProductLabelsRelate.objects.filter(
        product_id__in=invalid_product_id_list,
        label=label,
        label_date=now_date,
        become_history=False,
    ).update(
        become_history=True,
        remark=Concat("remark", Value(f";{datetime.now().strftime(DATETIME_FORMAT)}不符合阈值:{threshold}")),
    )

    # 获取符合条件的商品销量数据
    sales_count_items = ProductSalesCount.objects.select_related("product").filter(calc_date=now_date, sales__gte=threshold)

    batch_size = 1000
    create_rows = update_rows = 0

    product_id_list = [sales_obj.product.product_id for sales_obj in sales_count_items]

    product_label_relates = ProductLabelsRelate.objects.filter(product_id__in=product_id_list, label=label, label_date=now_date)
    product_label_relates_map = {product_label_relate.product.product_id: product_label_relate for product_label_relate in product_label_relates}

    need_update_objs = []
    need_create_objs = []

    magnitude = 10 ** (len(str(threshold)) - 1)

    for data in sales_count_items:
        if data.product.product_id not in product_label_relates_map:
            need_create_objs.append(
                ProductLabelsRelate(
                    product_id=data.product.product_id,
                    label=label,
                    act_value=data.sales,
                    value=f"{data.sales // magnitude * magnitude}+",
                    label_date=now_date,
                )
            )
        else:
            _tmp_obj = product_label_relates_map[data.product.product_id]
            if _tmp_obj.act_value != str(data.sales) or _tmp_obj.become_history is True:
                _tmp_obj.act_value = data.sales
                _tmp_obj.value = f"{data.sales // magnitude * magnitude}+"
                _tmp_obj.become_history = False
                need_update_objs.append(_tmp_obj)

    if need_create_objs:
        create_rows += len(ProductLabelsRelate.objects.bulk_create(need_create_objs, batch_size=batch_size))

    if need_update_objs:
        update_rows += ProductLabelsRelate.objects.bulk_update(need_update_objs, fields=["act_value", "value", "become_history"], batch_size=batch_size)

    logger.info(f"---- 添加当日标签. 不符合更新:{invalid_update_rows}, 创建:{create_rows}, 更新:{update_rows}. 耗时:{time.time() - start_time}")


@app.task(queue="common_tasks")
def calculate_week_sales_labels():
    """
    统计本周热销
    :return:
    """
    start_time = time.time()
    # 获取当前日期
    current_date = date.today()

    # 获取本周的第一天（星期一）
    first_day_of_week = current_date - timedelta(days=current_date.weekday())

    # 获取本周的最后一天（星期日）
    last_day_of_week = first_day_of_week + timedelta(days=6)

    # 当前当前周
    this_week = current_date.isocalendar().week

    label_type, _ = ProductLabelTypes.objects.get_or_create(name="销售标签")
    label, _ = ProductLabels.objects.update_or_create(
        name="本周热卖",
        l_type=label_type,
        is_deleted=False,
        defaults={
            "display": True,
            "is_normal": False,
            "can_edit": False,
        },
    )

    # 获取规则
    rule, _ = ProductLabelsRule.objects.get_or_create(label=label)
    if not rule.value or not rule.value.isdigit():
        rule.value = 3000
        rule.save(update_fields=["value"])
    # 阈值
    threshold = int(rule.value)

    # 将上周的标签数据置为历史数据
    last_week_update_rows = ProductLabelsRelate.objects.filter(
        label=label,
        label_week__lt=this_week,
        become_history=False,
        label_week__isnull=False,
    ).update(become_history=True)
    # 将本周的标签数据不符合规则的置为历史数据

    # 不符合条件的设置为become_history
    invalid_product_id_list = (
        ProductSalesCount.objects.select_related("product")
        .filter(
            calc_date__range=(first_day_of_week, last_day_of_week),
            sales__lt=threshold,
        )
        .values_list("product__product_id", flat=True)
    )

    invalid_update_rows = ProductLabelsRelate.objects.filter(
        product_id__in=invalid_product_id_list,
        label=label,
        label_week__isnull=False,
        label_week=this_week,
        become_history=False,
    ).update(
        become_history=True,
        remark=Concat("remark", Value(f";{datetime.now().strftime(DATETIME_FORMAT)}不符合阈值:{threshold}")),
    )

    # 获取符合条件的商品销量数据
    week_sales_items = (
        ProductSalesCount.objects.values("product_id")
        .annotate(this_week=ExtractWeek("calc_date"), total_sales=Sum("sales"))
        .filter(this_week=this_week, sales__gte=threshold)
        .values(
            "product__product_id",
            "this_week",
            "total_sales",
        )
    )

    batch_size = 1000
    create_rows = update_rows = 0

    product_id_list = [sales_obj["product__product_id"] for sales_obj in week_sales_items]
    product_label_relates = ProductLabelsRelate.objects.filter(product_id__in=product_id_list, label=label, label_week=this_week)
    product_label_relates_map = {product_label_relate.product_id: product_label_relate for product_label_relate in product_label_relates}

    need_update_objs = []
    need_create_objs = []
    magnitude = 10 ** (len(str(threshold)) - 1)
    for data in week_sales_items:
        data_product_id = data["product__product_id"]

        if data_product_id not in product_label_relates_map:
            need_create_objs.append(
                ProductLabelsRelate(
                    product_id=data_product_id,
                    label=label,
                    act_value=data["total_sales"],
                    value=f"{data['total_sales'] // magnitude * magnitude}+",
                    label_date=current_date,
                    label_week=this_week,
                )
            )
        else:
            _tmp_obj = product_label_relates_map[data_product_id]
            _tmp_obj.act_value = data["total_sales"]
            _tmp_obj.value = f"{data['total_sales'] // magnitude * magnitude}+"
            _tmp_obj.become_history = False
            _tmp_obj.label_date = current_date
            _tmp_obj.remark = None
            need_update_objs.append(_tmp_obj)

    if need_create_objs:
        create_rows += len(ProductLabelsRelate.objects.bulk_create(need_create_objs, batch_size=batch_size))

    if need_update_objs:
        update_rows += ProductLabelsRelate.objects.bulk_update(
            need_update_objs,
            fields=[
                "act_value",
                "value",
                "become_history",
                "label_date",
                "remark",
            ],
            batch_size=batch_size,
        )

    logger.info(f"---- 添加本周标签. 不符合更新:{invalid_update_rows},上周置为历史:{last_week_update_rows}, 创建:{create_rows}, 更新:{update_rows}. 耗时:{time.time() - start_time}")


@app.task(queue="common_tasks")
def calculate_product_db_sales(recursion_depth: int = 0, max_recursion_depth: int = 5, page_size: int = 1000):
    """
    每小时统计商品分销商的销量
    后期优化大于最后一次统计的自增id，再增加销量
    :return:
    """

    start_time = time.time()
    date_format = "%Y-%m-%d"
    create_rows = update_rows = 0
    chunk_size = 1000

    cache_key = "product_db_sales_calculate_start_id"
    start_cursor = redis_cache.get(cache_key)
    orders = OriginalOrder.objects.filter(sku__isnull=False, pay_time__isnull=False, order_type=0, distributor__isnull=False).only("id")
    # 分页处理
    queryset, next_cursor = self_cursor_paginator(orders, start_cursor, page_size=page_size)
    if not queryset:
        return {"message": "no queryset"}

    order_id_list = [q.id for q in queryset]
    #
    sales_count_items = (
        OriginalOrder.objects.filter(id__in=order_id_list)
        .annotate(calc_date=TruncDate("order_time"))
        .annotate(product=F("sku__product"))
        .values("distributor_id", "calc_date", "product")
        .annotate(sales_count=Sum("item_num"), sales_amount=Sum("order_amount"))
        .values("distributor_id", "calc_date", "product", "sales_count", "sales_amount")
    )
    unique_pk_list = set(["{}_{}_{}".format(x["product"], x["distributor_id"], x["calc_date"]) for x in sales_count_items])
    or_Q_condition = Q()
    for unique_pk in unique_pk_list:
        product_id, distributor_pk, calc_date = unique_pk.split("_")
        or_Q_condition.add(Q(product_id=product_id) & Q(distributor_id=distributor_pk) & Q(calc_date=calc_date), Q.OR)

    if not or_Q_condition:
        redis_cache.set(cache_key, next_cursor, timeout=None)
        if next_cursor:
            if recursion_depth < max_recursion_depth:
                logger.info(f"---- 统计分销商每日销量, 订单ID: {start_cursor}-{next_cursor}, 不处理. 耗时:{time.time() - start_time}")
                calculate_product_db_sales(recursion_depth + 1, max_recursion_depth)
            else:
                calculate_product_db_sales.delay(max_recursion_depth=max_recursion_depth, page_size=page_size)
        else:
            logger.info(f"---- 统计分销商每日销量, 订单ID: {start_cursor}-{next_cursor}, 不处理. 耗时:{time.time() - start_time}")
        return

    counts = ProductDBSalesCount.objects.filter(or_Q_condition)
    counts_map = {f"{count.product_id}-{count.calc_date.strftime(date_format)}-{count.distributor_id}": count for count in counts}
    need_update_objs = []
    need_create_objs = []
    for data in sales_count_items:
        distributor_pk = data["distributor_id"]
        product_pk = data["product"]
        calc_date = data["calc_date"]
        sales_count = data["sales_count"]
        sales_amount = data["sales_amount"]

        map_key = f"{product_pk}-{calc_date.strftime(date_format)}-{distributor_pk}"
        if map_key not in counts_map:
            need_create_objs.append(
                ProductDBSalesCount(
                    product_id=product_pk,
                    sales=sales_count,
                    distributor_id=distributor_pk,
                    calc_date=calc_date,
                    sales_amount=sales_amount,
                )
            )
        else:
            tmp_obj = counts_map[map_key]
            # 初始化
            if not start_cursor:
                tmp_obj.sales = sales_count
            else:
                # 增量
                tmp_obj.sales = tmp_obj.sales + sales_count
                tmp_obj.sales_amount = tmp_obj.sales_amount + sales_amount

            need_update_objs.append(tmp_obj)

    if need_create_objs:
        create_rows += len(ProductDBSalesCount.objects.bulk_create(need_create_objs, batch_size=chunk_size))

    if need_update_objs:
        update_rows += ProductDBSalesCount.objects.bulk_update(need_update_objs, fields=["sales", "sales_amount"], batch_size=chunk_size)

    redis_cache.set(cache_key, next_cursor, timeout=None)
    if next_cursor:
        if recursion_depth < max_recursion_depth:
            logger.info(f"---- 统计分销商每日销量, 订单ID: {start_cursor}-{next_cursor}, 创建:{create_rows}, 更新:{update_rows}. 耗时:{time.time() - start_time}")
            calculate_product_db_sales(recursion_depth + 1, max_recursion_depth)
        else:
            calculate_product_db_sales.delay(max_recursion_depth=max_recursion_depth, page_size=page_size)


@app.task(queue="common_tasks")
def calculate_db_sales_labels():
    """
    计算分销商名称
    :return:
    """
    start_time = time.time()
    label_type, _ = ProductLabelTypes.objects.get_or_create(name="销售标签")
    parent_label, _ = ProductLabels.objects.update_or_create(
        name="分销商标签",
        l_type=label_type,
        is_deleted=False,
        defaults={
            "display": False,
            "is_normal": False,
            "can_edit": False,
        },
    )

    # 获取规则
    rule_obj, _ = ProductLabelsRule.objects.get_or_create(label=parent_label)
    if not rule_obj.value or not rule_obj.value.isdigit():
        rule_obj.value = 3000
        rule_obj.save(update_fields=["value"])
    # 阈值
    threshold = int(rule_obj.value)

    base_filter_relates = (
        ProductLabelsRelate.objects.filter(act_value__regex=r"^\d+$", label=parent_label)
        .exclude(distributor_letters="")
        .exclude(distributor_letters__isnull=True)
        .annotate(int_act_value=Cast("act_value", output_field=IntegerField()))
    )

    # 将旧标签数据置为历史数据
    invalid_update_rows = base_filter_relates.update(become_history=True)

    # 查询符合条件的分销商统计数据
    db_sales_qs = query_db_sales_count(threshold)

    batch_size = 1000
    create_rows = update_rows = 0

    #
    product_pk_list = set([obj["product_id"] for obj in db_sales_qs])
    db_pk_list = set([obj["distributor_id"] for obj in db_sales_qs])
    # 批量创建labels
    distributors = Distributor.objects.filter(pk__in=db_pk_list, is_self_support=False).exclude(distributor_mode=2).only("letters", "name", "id")
    db_map = {db.letters: db.name for db in distributors}
    # 分销商主键映射到代码
    db_letters_map = {db.id: db.letters for db in distributors}

    p_labels = ProductLabels.objects.filter(distributor_letters__in=db_map.keys(), l_type=label_type)
    p_labels_map = {p_label.distributor_letters: p_label for p_label in p_labels}

    need_create_db_labels = []
    need_update_db_labels = []
    for db_letters, db_name in db_map.items():
        if db_letters not in p_labels_map:
            need_create_db_labels.append(
                ProductLabels(
                    name=db_name + "热卖",
                    l_type=label_type,
                    display=True,
                    can_edit=False,
                    is_normal=False,
                    distributor_letters=db_letters,
                    parent=parent_label,
                )
            )
        else:
            tmp_p_label = p_labels_map[db_letters]
            tmp_p_label.name = db_name + "热卖"
            tmp_p_label.display = True
            tmp_p_label.can_edit = False
            tmp_p_label.is_normal = False
            tmp_p_label.parent = parent_label
            need_update_db_labels.append(tmp_p_label)

    if need_create_db_labels:
        ProductLabels.objects.bulk_create(need_create_db_labels)
    if need_update_db_labels:
        ProductLabels.objects.bulk_update(need_update_db_labels, fields=["name", "display", "can_edit", "is_normal", "parent"])

    new_p_labels = ProductLabels.objects.filter(distributor_letters__in=db_map.keys(), l_type=label_type)
    new_p_labels_map = {new_p_label.distributor_letters: new_p_label for new_p_label in new_p_labels}

    # 查询已存在的关联
    product_labels_relates = ProductLabelsRelate.objects.filter(label__in=new_p_labels_map.values(), product_id__in=product_pk_list, distributor_letters__in=db_map.keys())
    product_labels_relates_map = {f"{product_labels_relate.product_id}-{product_labels_relate.distributor_letters}": product_labels_relate for product_labels_relate in product_labels_relates}

    need_update_objs = []
    need_create_objs = []
    magnitude = 10 ** (len(str(threshold)) - 1)
    for data in db_sales_qs:
        data_product_id = data["product_id"]
        db_letters = db_letters_map.get(data["distributor_id"])

        if not new_p_labels_map.get(db_letters):
            continue

        act_label = new_p_labels_map[db_letters]

        map_key = f"{data_product_id}-{db_letters}"

        if map_key not in product_labels_relates_map:
            need_create_objs.append(
                ProductLabelsRelate(
                    product_id=data_product_id,
                    label=act_label,
                    act_value=data["sales"],
                    value=f"{data['sales'] // magnitude * magnitude}+",
                    distributor_letters=db_letters,
                )
            )
        else:
            _tmp_obj = product_labels_relates_map[map_key]
            _tmp_obj.label = act_label
            _tmp_obj.act_value = data["sales"]
            _tmp_obj.value = f"{data['sales'] // magnitude * magnitude}+"
            _tmp_obj.become_history = False
            need_update_objs.append(_tmp_obj)

    if need_create_objs:
        create_rows += len(ProductLabelsRelate.objects.bulk_create(need_create_objs, batch_size=batch_size))
    if need_update_objs:
        update_rows += ProductLabelsRelate.objects.bulk_update(need_update_objs, fields=["label", "act_value", "value", "become_history"], batch_size=batch_size)

    logger.info(f"---- 添加分销商标签. 不符合更新:{invalid_update_rows}, 创建:{create_rows}, 更新:{update_rows}. 耗时:{time.time() - start_time}")


@app.task(queue="common_tasks")
def calculate_product_sku_sales(recursion_depth: int = 0, max_recursion_depth: int = 100, page_size=1000, init_sales=False):
    """
    每小时统计商品规格的分销商的销量
    :return:
    """

    if init_sales:
        updated_rows = SKUDistributorSalesCount.objects.update(sales=0, order_count=0, sales_amount=0)
        logger.info(f">>初始化更新: {updated_rows}")

    start_time = time.time()
    date_format = "%Y-%m-%d"
    create_rows = update_rows = 0
    chunk_size = 1000

    cache_key = "sku_sales_calculate_start_id"
    start_cursor = redis_cache.get(cache_key)
    orders = OriginalOrder.objects.filter(
        sku__isnull=False,
        pay_time__isnull=False,
        order_type=0,
        distributor__isnull=False,
    ).only(
        "id",
        "order_time",
        "sku",
        "item_num",
    )
    # 分页处理
    queryset, next_cursor = self_cursor_paginator(orders, start_cursor, page_size=page_size)

    if queryset:
        order_id_list = [q.id for q in queryset]
        # 统计订单数据
        sku_sales_count_items = (
            OriginalOrder.objects.filter(id__in=order_id_list)
            .annotate(calc_date=TruncDate("order_time"))
            .values("calc_date", "sku", "distributor_id")
            .annotate(
                sales_count=Sum("item_num"),
                order_count=Count("ex_order_id", distinct=True),
                sales_amount=Sum("order_amount"),
            )
            .values(
                "calc_date",
                "sku",
                "sales_count",
                "distributor_id",
                "order_count",
                "sales_amount",
            )
        )
        sku_pk_list = set([x["sku"] for x in sku_sales_count_items if x["sku"]])
        counts = SKUDistributorSalesCount.objects.filter(sku_id__in=sku_pk_list)
        counts_map = {f"{count.sku_id}-{count.calc_date.strftime(date_format)}-{count.distributor_id}": count for count in counts}

        need_update_objs = []
        need_create_objs = []

        for data in sku_sales_count_items:
            sku_pk = data["sku"]
            calc_date = data["calc_date"]
            sales_count = data["sales_count"]
            distributor_id = data["distributor_id"]
            order_count = data["order_count"]
            sales_amount = data["sales_amount"]

            map_key = f"{sku_pk}-{calc_date.strftime(date_format)}-{distributor_id}"
            if map_key not in counts_map:
                need_create_objs.append(
                    SKUDistributorSalesCount(
                        sku_id=sku_pk,
                        distributor_id=distributor_id,
                        sales_count=sales_count,
                        calc_date=calc_date,
                        order_count=order_count,
                        sales_amount=sales_amount,
                    )
                )
            else:
                tmp_obj = counts_map[map_key]
                # 增量
                tmp_obj.sales_count = tmp_obj.sales_count + sales_count
                tmp_obj.order_count = tmp_obj.order_count + order_count
                tmp_obj.sales_amount = tmp_obj.sales_amount + sales_amount
                need_update_objs.append(tmp_obj)

        SKUDistributorSalesCount.objects.bulk_create(
            need_create_objs,
            update_conflicts=True,
            unique_fields=["sku_id", "calc_date", "distributor_id"],
            update_fields=["sales_count", "order_count", "sales_amount"],
        )
        if need_create_objs:
            create_rows += len(SKUDistributorSalesCount.objects.bulk_create(need_create_objs, batch_size=chunk_size))

        if need_update_objs:
            update_rows += SKUDistributorSalesCount.objects.bulk_update(
                need_update_objs,
                fields=[
                    "sales_count",
                    "order_count",
                    "sales_amount",
                ],
                batch_size=chunk_size,
            )

        redis_cache.set(cache_key, next_cursor, timeout=None)

        if next_cursor:
            if recursion_depth < max_recursion_depth:
                logger.info(f"---- 统计规格每日销量, 订单ID: {start_cursor}-{next_cursor}, 创建:{create_rows}, 更新:{update_rows}. 耗时:{time.time() - start_time}")
                calculate_product_sku_sales(recursion_depth + 1, max_recursion_depth)
            else:
                calculate_product_sku_sales.delay(max_recursion_depth=max_recursion_depth, page_size=page_size)


@app.task(queue="common_tasks")
def calculate_sku_sales(recursion_depth: int = 0, max_recursion_depth: int = 100, page_size=1000):
    """
    每小时统计sku的销量
    :return:
    """

    start_time = time.time()
    date_format = "%Y-%m-%d"
    create_rows = update_rows = 0
    chunk_size = 1000

    cache_key = "sku_sales_calculate_start_id"
    start_cursor = redis_cache.get(cache_key)
    orders = OriginalOrder.objects.filter(sku__isnull=False, pay_time__isnull=False, order_type=0).only("id", "order_time", "sku", "item_num")
    # 分页处理
    queryset, next_cursor = self_cursor_paginator(orders, start_cursor, page_size=page_size)

    if queryset:
        order_id_list = [q.id for q in queryset]
        #
        sales_count_items = (
            OriginalOrder.objects.filter(id__in=order_id_list)
            .annotate(calc_date=TruncDate("order_time"))
            .annotate(product=F("sku__product"))
            .values("calc_date", "sku", "product")
            .annotate(sales_count=Sum("item_num"))
            .values("calc_date", "sku", "product", "sales_count")
        )
        sku_pk_list = set([x["sku"] for x in sales_count_items if x["sku"]])
        counts = SKUSalesCount.objects.filter(sku_id__in=sku_pk_list)
        counts_map = {f"{count.sku_id}-{count.product_id}-{count.calc_date.strftime(date_format)}": count for count in counts}

        need_update_objs = []
        need_create_objs = []

        for data in sales_count_items:
            sku_pk = data["sku"]
            product_pk = data["product"]
            calc_date = data["calc_date"]
            sales_count = data["sales_count"]

            map_key = f"{sku_pk}-{product_pk}-{calc_date.strftime(date_format)}"
            if map_key not in counts_map:
                need_create_objs.append(SKUSalesCount(sku_id=sku_pk, product_id=product_pk, sales=sales_count, calc_date=calc_date))
            else:
                tmp_obj = counts_map[map_key]
                # 初始化
                if not start_cursor:
                    tmp_obj.sales = sales_count
                else:
                    # 增量
                    tmp_obj.sales = tmp_obj.sales + sales_count

                need_update_objs.append(tmp_obj)
        try:
            if need_create_objs:
                create_rows += len(SKUSalesCount.objects.bulk_create(need_create_objs, batch_size=chunk_size))
        except Exception as e:
            logger.error(f"统计sku每日销量错误，错误内容： {str(e)}, 数据内容：{need_create_objs}， start_cursor：{start_cursor}")
        if need_update_objs:
            update_rows += SKUSalesCount.objects.bulk_update(need_update_objs, fields=["sales"], batch_size=chunk_size)

        redis_cache.set(cache_key, next_cursor, timeout=None)

        if next_cursor:
            if recursion_depth < max_recursion_depth:
                logger.info(f"---- 统计每日sku销量, 订单ID: {start_cursor}-{next_cursor}, 创建:{create_rows}, 更新:{update_rows}. 耗时:{time.time() - start_time}")
                calculate_sku_sales(recursion_depth + 1, max_recursion_depth)
            else:
                calculate_sku_sales.delay(max_recursion_depth=max_recursion_depth, page_size=page_size)


# ======================== 统计数据END ========================


# ======================== 售后订单START ========================
def insert_after_sales_orders(data_list: list, data_shop_id: int | str, platform: str = "DY"):
    """
    批量处理售后订单
    :param data_list:
    :param data_shop_id:
    :param platform:
    :return:
    """
    if not data_list:
        return "SUCCESS: NO DATA FOUND"

    data_handler = ASOrderHandlerFactory.get_handler(platform)
    raw_data_list = data_handler.parse_many_data(data_list)
    # raw_data_list可能存在多条主键相同的数据，按照顺序，只保留最新的一条
    handled_data_list = {raw_data["ex_as_order_id"]: raw_data for raw_data in raw_data_list}.values()

    ignore_count = 0
    failed_objs = []
    related_info_objs = []
    as_objs = []

    # 查询sku_id as order是否存在
    item_sku_order_id_list = [item["sku_order_id"] for handled_data in handled_data_list for item in handled_data.get("order_info") if handled_data.get("ex_order_id")]
    exist_sku_order_id_list = AfterSalesRelatedOrderInfo.objects.filter(sku_order_id__in=item_sku_order_id_list).values_list("ex_order_id", flat=True)

    # 需要更新sku库存的object
    need_update_skus_stock_object = []

    for handled_data in handled_data_list:
        try:
            # 不处理手工单数据
            ex_order_id = handled_data.get("ex_order_id")
            if not ex_order_id:
                ignore_count += 1
                continue

            items = handled_data.pop("order_info")
            # 售后状态
            as_type = handled_data["as_type"]

            handled_data["data_source"] = platform
            handled_data["data_shop_id"] = data_shop_id

            as_obj = AfterSales(**handled_data)
            as_objs.append(as_obj)

            # 处理商编
            tmp_sku_spec_code_list = []
            for item in items:
                item["ex_order_id"] = ex_order_id
                raw_spec_code = item["shop_sku_code"]

                _, spec_code, db_letters = parse_spec_code(raw_spec_code)

                if spec_code:
                    tmp_sku_spec_code_list.append(spec_code)
                item["handled_sku_code"] = spec_code
                item["db_letters"] = db_letters
                item["data_shop_id"] = data_shop_id

            skus = StockKeepingUnit.objects.filter(spec_code__in=tmp_sku_spec_code_list).values(
                "id",
                "sku_id",
                "product__product_id",
                "spec_code",
                "product__company__company_id",
                "cost_price",
            )

            skus_map = {sku["spec_code"]: sku for sku in skus}

            for item in items:
                item["as_order_id"] = as_obj.as_order_id
                sku_order_id = item["sku_order_id"]
                handled_sku_code = item["handled_sku_code"]
                db_letters = item["db_letters"]
                # 售后数量
                as_num = item["as_num"]
                tmp_sku = skus_map.get(handled_sku_code)

                # 退货退款-加库存
                if as_num != 0 and as_type == 0 and tmp_sku and sku_order_id not in exist_sku_order_id_list:
                    need_update_skus_stock_object.append(
                        {
                            "sku_id": tmp_sku["sku_id"],
                            "product_id": tmp_sku["product__product_id"],
                            "order_id": as_obj.as_order_id,
                            "sku_order_id": sku_order_id,
                            "value": as_num,
                        }
                    )

                if tmp_sku:
                    item["relate_product_id"] = tmp_sku["product__product_id"]
                    item["relate_sku_id"] = tmp_sku["sku_id"]
                    item["company_id"] = tmp_sku["product__company__company_id"]
                    item["cost_price"] = tmp_sku["cost_price"]
                    # 订单关联
                    item["original_order"] = OriginalOrder.objects.filter(ex_order_id=ex_order_id, sku_id=tmp_sku["id"]).first()
                    # 增加子商品关联
                    if db_letters:
                        sub_product = SubProduct.objects.only("product_id").filter(parent_product__product_id=tmp_sku["product__product_id"], owner__letters=db_letters).first()
                        if sub_product:
                            item["relate_sub_product"] = sub_product

                related_info_objs.append(AfterSalesRelatedOrderInfo(**item))
        except Exception as e:
            failed_objs.append(
                AfterSalesOrderFailed(
                    as_data=json.loads(
                        json.dumps(
                            handled_data,
                            ensure_ascii=False,
                            default=lambda x: x.isoformat() if isinstance(x, datetime) else None,
                        )
                    ),
                    platform=platform,
                    errors=f"{e}>>{traceback.format_exc()}",
                )
            )
    AfterSales.objects.bulk_create(
        as_objs,
        unique_fields=["ex_as_order_id"],
        update_conflicts=True,
        update_fields=[
            "data_source",
            "ex_order_id",
            "ex_as_order_id",
            "num",
            "order_type",
            "refund_type",
            "final_time",
            "as_type",
            "apply_time",
            "platform_create_time",
            "platform_update_time",
            "exchange_logistics_company_name",
            "exchange_sku_info",
            "got_pkg",
            "is_agree_refuse_sign",
            "part_type",
            "sec_reason_labels",
            "amount",
            "return_shipping_amount",
            "refund_state",
            "state_text",
            "refund_tax_amount",
            "refund_way",
            "shop_remark",
            "return_logistics_code",
            "return_logistics_company_name",
            "return_promotion_amount",
            "risk_decision_code",
            "risk_decision_description",
            "risk_decision_reason",
            "status_deadline",
            "store_id",
            "store_name",
            "receiver_name",
            "receiver_tel",
            "receiver_address",
            "reason_text",
            "logistics_text",
            "as_status_text",
            "as_type_text",
            "return_logistics_text",
            "as_refund_type_text",
            "logistics",
            "tags",
            "data_shop",
        ],
    )

    AfterSalesRelatedOrderInfo.objects.bulk_create(
        related_info_objs,
        unique_fields=["sku_order_id"],
        update_conflicts=True,
        update_fields=[
            "sku_order_id",
            "ex_order_id",
            "outer_oi_id",
            "as_num",
            "as_pay_amount",
            "as_post_amount",
            "as_tax_amount",
            "platform_create_time",
            "given_sku_order_ids",
            "is_gift",
            "is_oversea_order",
            "buy_num",
            "order_pay_amount",
            "order_post_amount",
            "price",
            "product_id",
            "product_name",
            "product_image",
            "promotion_pay_amount",
            "shop_sku_code",
            "sku_spec",
            "tags",
            "tax_amount",
            "handled_sku_code",
            "db_letters",
            "company",
            "relate_product",
            "relate_sku",
            "relate_sub_product",
            "original_order",
            "data_shop",
            "cost_price",
        ],
    )
    filed_rows = len(AfterSalesOrderFailed.objects.bulk_create(failed_objs))

    # 更新sku库存
    change_log_object_list = []
    sku_id_list = [obj["sku_id"] for obj in need_update_skus_stock_object]
    sku_objs = StockKeepingUnit.objects.filter(sku_id__in=sku_id_list)
    sku_map = {sku.sku_id: sku for sku in sku_objs}

    for need_update_sku_obj in need_update_skus_stock_object:
        sku_id = need_update_sku_obj["sku_id"]
        product_id = need_update_sku_obj["product_id"]
        order_id = need_update_sku_obj["order_id"]
        sku_order_id = need_update_sku_obj["sku_order_id"]
        value = need_update_sku_obj["value"]

        if sku_id not in sku_map:
            continue

        _sku = sku_map[sku_id]
        _sku.physical_inventory = F("physical_inventory") + int(value)
        _sku.save(update_fields=["physical_inventory"])
        _sku.refresh_from_db()

        change_log_object_list.append(
            OrderSKUInventoryChangeLog(
                order_id=order_id,
                order_type="as_order",
                sub_order_id=sku_order_id,
                action="+",
                value=int(value),
                after_changed_value=_sku.physical_inventory,
                product_id=product_id,
                sku_id=sku_id,
            )
        )
    logs_count = len(OrderSKUInventoryChangeLog.objects.bulk_create(change_log_object_list))
    logger.info(f"SUCCESS. 错误个数:{filed_rows}, 忽略个数:{ignore_count}, 库存流水新增:{logs_count}")


@app.task(queue="long_running_tasks")
def jst_as_orders_fetcher(shop_id):
    redis_con = None
    lock_key = f"jst_as_task_lock:{shop_id}"
    need_release_lock = True
    try:
        redis_con = gen_redis_conn()
        if not redis_con.setnx(lock_key, 1):
            need_release_lock = False
            return f"SUCCESS, {shop_id}正在执行查询.无需重复执行"

        redis_con.expire(lock_key, 86300)

        jst = JSTAPIClient()
        # 2024年6月1日起ts
        default_start_ts = 3060166029
        try:
            config = SystemConfig.objects.get(code=SystemConfig.JST_AS_START_TS, enable=True)
        except SystemConfig.DoesNotExist:
            # 设置系统设置
            config = SystemConfig(name="聚水潭售后ts(勿动)", desc="聚水潭拉取订单开始ts, 非开发人员勿动", code=SystemConfig.JST_AS_START_TS)

        if not config.extra:
            config.extra = {f"{shop_id}": default_start_ts}
            config.save()
            config.refresh_from_db()
        elif str(shop_id) not in config.extra:
            config_extra = config.extra
            config_extra[shop_id] = default_start_ts
            config.extra = config_extra
            config.save()
            config.refresh_from_db()

        start_ts = int(config.extra[str(shop_id)])

        print(f">>>>{datetime.now()}开始聚水潭{shop_id}售后单任务, start_ts: {start_ts}")
        while 1:
            response = jst.fetch_refund_orders(shop_id, start_ts)
            if not response["datas"]:
                print(f">>>>{datetime.now()}结束{shop_id}任务, 当前ts:{start_ts}")
                return "SUCCESS: 数据为None"

            next_query_ts = max([data["ts"] for data in response["datas"]] or [start_ts])
            if start_ts == next_query_ts:
                print(f">>>>{datetime.now()}结束{shop_id}任务, 当前ts:{start_ts}")
                return "SUCCESS"
            insert_after_sales_orders(response["datas"], shop_id, "JST")
            start_ts = next_query_ts

            with transaction.atomic():
                config = SystemConfig.objects.select_for_update().get(pk=config.pk)
                # 设置最大的查询时间戳
                config_extra = config.extra
                config_extra[shop_id] = start_ts
                config.extra = config_extra
                config.save()
    except Exception as e:
        print(f">>> 获取聚水潭订单错误,{e},{traceback.format_exc()}")
        return "FAIL"
    finally:
        if redis_con and need_release_lock:
            redis_con.delete(lock_key)


@app.task(queue="long_running_tasks")
def dy_as_orders_fetcher(shop_pk, shop_id, begin_start_time: int = None):
    redis_con = None
    lock_key = f"dy_as_task_lock:{shop_id}"
    need_release_lock = True
    try:
        redis_con = gen_redis_conn()
        if not redis_con.setnx(lock_key, 1):
            need_release_lock = False
            return f"SUCCESS, 抖店{shop_id}正在执行查询.无需重复执行"

        redis_con.expire(lock_key, 86400)

        sdk_request = AfterSaleListRequest()
        # 默认是2024-06-01 00:00:00
        default_start_time = 1717171200
        aggregate_ret = AfterSales.objects.filter(data_shop_id=shop_id).values("data_shop_id").aggregate(max_update_time=Max("platform_update_time"))
        if not aggregate_ret:
            start_time = default_start_time
        else:
            max_update_time = aggregate_ret["max_update_time"]
            if not max_update_time:
                start_time = default_start_time
            else:
                start_time = int(aggregate_ret["max_update_time"].timestamp()) - 10

        if begin_start_time:
            start_time = begin_start_time

        print(f">>>>{datetime.now()}开始{shop_id}抖店售后单任务, start_time: {start_time}")

        params = sdk_request.getParams()
        params.update_start_time = start_time
        params.order_by = ["update_time asc"]
        params.size = 100
        params.page = 0

        response = execute_order_request(shop_pk, sdk_request)
        if response.code != 10000:
            return f"FAIL: {response.sub_msg}"

        # 第一页查询
        items = response.data["items"]
        insert_after_sales_orders(items, shop_id, "DY")

        # 计算total_page
        total_nums = response.data["total"]
        if total_nums < 100:
            return f"SUCCESS, total_page:{1}"

        total_pages = math.ceil(total_nums / 100)
        for page in range(1, total_pages):
            # 第一页查询
            params.page = page
            response = execute_order_request(shop_pk, sdk_request)
            items = response.data["items"]
            insert_after_sales_orders(items, shop_id, "DY")

        return f"SUCCESS,总页数:{total_pages}"

    except Exception as e:
        print(f">>> 获取抖店售后订单错误,{e},{traceback.format_exc()}")
        return "FAIL"
    finally:
        if redis_con and need_release_lock:
            redis_con.delete(lock_key)


@app.task(queue="celery")
def jst_shops_as_order_fetcher_starter():
    """
    售后单启动
    :return:
    """
    jst_order_app = jst_order_application()
    # 排除有关联关系的店铺
    relates = jst_order_app.dataapplicationshops_set.filter(enable=True, data_shop__relate_shop__isnull=True)
    for relate in relates:
        jst_as_orders_fetcher.delay(relate.data_shop.shop_id)
        time.sleep(0.5)


@app.task(queue="celery")
def dy_shops_as_order_fetcher_starter(begin_start_time: int = None):
    dy_order_app = dy_order_application()
    relates = dy_order_app.dataapplicationshops_set.filter(enable=True, is_authorized=True)
    for relate in relates:
        dy_as_orders_fetcher.delay(relate.data_shop.id, relate.data_shop.shop_id, begin_start_time)
        time.sleep(0.5)


@app.task(queue="common_tasks")
def update_as_order_relate_task(order_map: dict):
    for k, v in order_map.items():
        ex_order_id, sku_pk = str(k).split("-")
        if sku_pk == "None":
            continue

        AfterSalesRelatedOrderInfo.objects.filter(ex_order_id=ex_order_id, relate_sku__id=sku_pk).update(original_order_id=v)


# ======================== 售后订单END ========================

# ======================== 订单START ========================


def check_and_dispatch_tasks():
    active_tasks = len([k for k, v in redis_conn.hgetall(TASK_QUEUE_KEY).items() if v == b"1"])
    if active_tasks < 2:
        # 获取所有未执行的任务
        tasks = {k.decode("utf-8"): v.decode("utf-8") for k, v in redis_conn.hgetall(TASK_QUEUE_KEY).items() if v == b"0"}
        for task_id in tasks.keys():
            if active_tasks >= 2:
                break
            # 将任务状态设置为1
            redis_conn.hset(TASK_QUEUE_KEY, task_id, "1")
            # 执行任务

            shop_id, now_ts, in_calculate_inventory_time, start_ts, max_active_tasks = str(task_id).split("|")

            JST_order_fetcher.delay(shop_id, int(now_ts), in_calculate_inventory_time, int(start_ts), int(max_active_tasks))
            active_tasks += 1
            time.sleep(0.5)


@app.task(queue="celery")
def JST_shopsorder_fetcher_starter(start_ts: int = 3271146070, max_active_tasks: int = 2):
    """
    聚水潭订单启动
    :return:
    """
    jst_order_app = jst_order_application()
    # 排除有关联关系的店铺
    relates = jst_order_app.dataapplicationshops_set.filter(enable=True, data_shop__relate_shop__isnull=True)
    for relate in relates:
        shop_id = relate.data_shop.shop_id
        now_ts = int(time.time())
        in_calculate_inventory_time = jst_order_app.calculate_inventory_time.strftime(DATETIME_FORMAT) if jst_order_app.calculate_inventory_time else "2029-07-03 04:00:00"
        start_ts = start_ts
        max_active_tasks = max_active_tasks
        #
        task_id = f"{shop_id}|{now_ts}|{in_calculate_inventory_time}|{start_ts}|{max_active_tasks}"

        redis_conn.hset(TASK_QUEUE_KEY, task_id, "0")
    # 检查分发任务
    check_and_dispatch_tasks()
    # JST_order_fetcher.delay(
    #     shop_id,
    #     now_ts,
    #     in_calculate_inventory_time,
    #     start_ts,
    #     max_active_tasks,
    # )
    # time.sleep(0.5)


@app.task(queue="long_running_tasks")
def JST_order_fetcher(shop_id, now_ts, in_calculate_inventory_time=None, start_ts: int = 3271146070, max_active_tasks: int = 2):
    """
    聚水潭订单拉取
    """
    if in_calculate_inventory_time:
        calculate_inventory_time = convert_datatime_val_2_aware_time(in_calculate_inventory_time)
    else:
        calculate_inventory_time = convert_datatime_val_2_aware_time("2025-07-03 04:00:00")
    task_id = f"{shop_id}|{now_ts}|{in_calculate_inventory_time}|{start_ts}|{max_active_tasks}"
    redis_con = None
    lock_key = f"jst_order_task_lock:{shop_id}"
    need_release_lock = True

    try:
        redis_con = gen_redis_conn()
        if not redis_con.setnx(lock_key, 1):
            need_release_lock = False
            return f"SUCCESS, {shop_id}JST订单-正在执行查询.无需重复执行"

        redis_con.expire(lock_key, 86400)

        jst = JSTAPIClient()

        default_start_ts = 3271146070

        if start_ts:
            default_start_ts = start_ts

        try:
            config = SystemConfig.objects.get(code=SystemConfig.JST_ORDER_START_TS, enable=True)
        except SystemConfig.DoesNotExist:
            # 设置系统设置
            config = SystemConfig(name="聚水潭订单ts(勿动)", desc="聚水潭拉取订单开始ts, 非开发人员勿动", code=SystemConfig.JST_ORDER_START_TS)

        if not config.extra:
            config.extra = {f"{shop_id}": default_start_ts}
            config.save()
            config.refresh_from_db()
        elif str(shop_id) not in config.extra:
            config_extra = config.extra
            config_extra[shop_id] = default_start_ts
            config.extra = config_extra
            config.save()
            config.refresh_from_db()

        start_ts = int(config.extra[str(shop_id)])

        print(f">>>>{datetime.now()}开始聚水潭{shop_id}JST订单任务, start_ts: {start_ts}")
        while 1:
            biz = {"shop_id": shop_id, "page_index": 1, "page_size": 100, "start_ts": start_ts}
            url = "/open/orders/single/query"
            try:
                raw_data = jst.request(url, biz)
            except RuntimeError as e:
                return f"FAILED: {str(e)}"

            data_list = raw_data["orders"]
            next_query_ts = max([data["ts"] for data in data_list] or [start_ts])
            if start_ts == next_query_ts:
                print(f">>>>{datetime.now()}结束{shop_id}JST任务, 当前ts:{start_ts}")
                return "SUCCESS"

            insert_original_order("JST", data_list, calculate_inventory_time)
            start_ts = next_query_ts

            with transaction.atomic():
                # 加锁更新数据库
                config = SystemConfig.objects.select_for_update().get(pk=config.pk)
                # 设置最大的查询时间戳
                config_extra = config.extra
                config_extra[shop_id] = start_ts
                config.extra = config_extra
                config.save()
    except Exception as e:
        print(f">>> 获取聚水潭订单错误,{e},{traceback.format_exc()}")
        return "FAIL"
    finally:
        if redis_con and need_release_lock:
            redis_con.delete(lock_key)
            # 从 Redis 中删除任务
            redis_con.hdel(TASK_QUEUE_KEY, task_id)
            # 调用检查并执行下一个任务
            check_and_dispatch_tasks()


def query_existed_original_order(data_source, parsed_data):
    # 批量查询已经存在表中的数据
    if not parsed_data:
        return

    existed_query = Q()
    for data in parsed_data:
        if data_source == "DD":
            existed_query |= Q(
                ex_order_id=data.get("ex_order_id"),
                raw_sku_id=data.get("raw_sku_id"),
            )
        else:
            existed_query |= Q(
                ex_order_id=data.get("ex_order_id"),
                raw_sku_id=data.get("raw_sku_id"),
            )
            # existed_query |= Q(
            #     ex_order_id=data.get("ex_order_id"),
            #     raw_sku_id=data.get("raw_spec_code"),
            # )
    if data_source == "DD":
        existed_qs = OriginalOrder.objects.using("default").filter(existed_query).values("ex_order_id", "raw_sku_id")
        res = [(i.get("ex_order_id"), i.get("raw_sku_id")) for i in existed_qs]
    else:
        # 映射回同一字段
        existed_qs = OriginalOrder.objects.using("default").filter(existed_query).values("ex_order_id", "raw_sku_id")
        res = [(i.get("ex_order_id"), i.get("raw_sku_id")) for i in existed_qs]
        # existed_qs = OriginalOrder.objects.using("default").filter(existed_query).values("ex_order_id", "raw_spec_code")
        # res = [(i.get("ex_order_id"), i.get("raw_spec_code")) for i in existed_qs]
    return res


def get_or_create_latest_history_for_model(model, history_model, ids, related_name):
    """
    获取或创建指定模型的最新历史记录
    :param model: 原始模型类
    :param history_model: 历史记录模型类
    :param ids: 需要获取历史记录的模型实例ID列表
    :param related_name: 历史记录模型中指向原模型的外键字段名
    :return: {实例ID: 最新历史记录实例}
    """
    # 获取最新的历史记录子查询
    subquery = history_model.objects.filter(**{related_name: OuterRef("pk")}).order_by("-history_date")

    # 获取模型实例和其最新的历史记录ID
    latest_histories = model.objects.filter(id__in=ids).annotate(latest_history_id=Subquery(subquery.values("history_id")[:1])).values("id", "latest_history_id")

    # 收集最新历史记录ID
    history_ids = [item["latest_history_id"] for item in latest_histories if item["latest_history_id"]]
    # print(f"history_ids: {history_ids}")

    histories = history_model.objects.filter(history_id__in=history_ids)
    history_dict = {history.instance.id: history for history in histories}

    # 检查并创建缺失的历史记录
    missing_ids = [item["id"] for item in latest_histories if not item["latest_history_id"]]

    if missing_ids:
        instances = model.objects.filter(id__in=missing_ids)
        for instance in instances:
            instance.save()  # 触发保存操作以创建历史记录
            latest_history = instance.history.first()
            history_dict[instance.id] = latest_history

    return history_dict


def get_or_create_latest_histories_for_skus_and_products(sku_ids, product_ids):
    """
    获取或创建SKU和Product的最新历史记录
    :param sku_ids: SKU实例ID列表
    :param product_ids: Product实例ID列表
    :return: (sku_histories, product_histories)
    举例：
    ({139568: <HistoricalStockKeepingUnit: 144554207 as of 2024-06-20 12:48:26.559753+00:00>}, {137390: <HistoricalProduct: 测试ZP(137268357) as of 2024-06-20 12:48:26.699372+00:00>})

    """
    sku_histories = get_or_create_latest_history_for_model(StockKeepingUnit, StockKeepingUnit.history.model, sku_ids, "id")
    product_histories = get_or_create_latest_history_for_model(Product, Product.history.model, product_ids, "id")
    return sku_histories, product_histories


def bulk_create_or_get_latest_history(sku_dict_qs):
    """
    批量创建或获取最新版本
    """
    sku_ids = []
    product_ids = []
    for sku in sku_dict_qs:
        sku_ids.append(sku.id)
        product_ids.append(sku.product.id)

    return get_or_create_latest_histories_for_skus_and_products(sku_ids, product_ids)


def insert_original_order(data_source, raw_data, calculate_inventory_time):
    """
    插入原始订单
    Args:
        raw_data (_type_): _description_
        data_source (_type_): _description_
        calculate_inventory_time: 开始核算库存时间

    Returns:
        _type_: _description_
    """
    try:
        if not raw_data:
            return "no data insert."
        start = time.time()
        # 订单状态流转顺序
        ORDER_STATUS_TF = {
            "RD": 0,
            "DL": 1,
            "FN": 2,
            "CL": 3,
        }
        order_handler = OrderHandlerFactory(data_source, raw_data)
        parsed_data = order_handler.process()
        # sku
        sku__spec_code_list = [data.get("sku__spec_code") for data in parsed_data if data.get("sku__spec_code")]
        company_query = Prefetch("product__company", queryset=Company.objects.all())

        sku_dict_qs = StockKeepingUnit.objects.using("default").filter(spec_code__in=sku__spec_code_list).prefetch_related("product", company_query)
        sku_histories, product_histories = bulk_create_or_get_latest_history(sku_dict_qs)
        sku_dict = {sku.spec_code: sku for sku in sku_dict_qs if sku.spec_code}

        # distributor
        distributor_qs = Distributor.objects.all().values("live_author__author_id", "letters", "distributor_id", "name", "id")
        dist_author_dict = {i["live_author__author_id"]: i for i in distributor_qs}
        dist_letters_dict = {i["letters"]: i for i in distributor_qs}

        # 记录并去重
        order_based_objs = {}
        original_order_objs = {}
        item_num_dict = {}

        history_price_data_dict = {}

        order_based_fields = [field.name for field in OrderBasedOnSupplier._meta.fields]
        original_fields = [field.name for field in OriginalOrder._meta.fields]
        existed_order = query_existed_original_order(data_source, parsed_data)
        # 临时商品创建
        temp_product_list = []

        for data in parsed_data:
            ex_order_id = data.get("ex_order_id")
            raw_spec_code = data.get("raw_spec_code")
            sku__spec_code = data.get("sku__spec_code")
            # 获取分销商
            distributor = dist_author_dict.get(data.get("author_id", ""), {}).get("id") or dist_letters_dict.get(data.get("letters", ""), {}).get("id")
            # 获取分销商id
            distributor_id = dist_author_dict.get(data.get("author_id", ""), {}).get("distributor_id") or dist_letters_dict.get(data.get("letters", ""), {}).get("distributor_id")

            if not ex_order_id:
                # 过滤没有外部订单id的订单
                continue

            # 获取sku
            sku = sku_dict.get(sku__spec_code)
            if not sku:
                # logger.info(f">>> sku not found: ex_order_id: {ex_order_id}; raw_spec_code: {raw_spec_code}, parsed_spec_code:{sku__spec_code}")
                if not data.get("raw_product_id") or not data.get("raw_sku_id"):
                    # 过滤没有抖店商品id和sku_id的数据
                    continue
                # 没有sku信息创建临时商品
                _category_list = [data.get(_field_name) for _field_name in ["first_cid", "second_cid", "third_cid", "fourth_cid"] if data.get(_field_name)]
                temp_product_info = {
                    "data_source": data_source,
                    "product_id": data["raw_product_id"],
                    "category": _category_list,
                    "name": data.get("raw_product_name"),
                    "pic": data.get("raw_product_pic"),
                    "sku_id": data["raw_sku_id"],
                    "raw_spec_code": raw_spec_code,
                    "parsed_spec_code": sku__spec_code,
                    "sku_specs": data.get("raw_sku_specs"),
                    "price": data["goods_price"],
                    "shop_id": data["shop_id"],
                    "shop_name": data["shop_name"],
                    "author_id": data["author_id"],
                    "author_name": data["author_name"],
                    "distributor_id": distributor_id,
                    "order_date": data["order_time"].strftime(DATE_FORMAT),
                    "ex_order_id": data["ex_order_id"],
                }
                temp_product_list.append(temp_product_info)

            # ==== 创建 order_based ====
            order_based = None
            if sku:
                company = sku.product.company.id
                order_based_data = {i: data.get(i) for i in order_based_fields if data.get(i) is not None}
                if distributor:
                    order_based_data["distributor_id"] = distributor
                order_based_data["company_id"] = company
                # 合并重复数据
                based_key = (order_based_data.get("ex_order_id"), order_based_data.get("company_id"))
                order_based = order_based_objs.get(based_key)
                if not order_based:
                    order_based = OrderBasedOnSupplier(**order_based_data)
                    order_based_objs[based_key] = order_based
                else:
                    # 取消遍历
                    order_status_v = order_based_data.get("order_status")
                    if not order_status_v:
                        continue

                    if ORDER_STATUS_TF.get(order_status_v, 0) < ORDER_STATUS_TF.get(order_based.order_status, 0):
                        continue

                    setattr(order_based, "order_status", order_status_v)

            # ==== 创建 original order ====
            original_data = {i: data.get(i) for i in original_fields if data.get(i) is not None}
            if distributor:
                original_data["distributor_id"] = distributor
            original_data["order_amount"] = data.get("sub_order_amount")
            original_data["data_source"] = data_source
            original_data["order_based_on_supplier"] = order_based
            original_data["sku_id"] = sku.id if sku else None
            original_data["sku_version"] = sku_histories.get(sku.id).history_id if sku and sku_histories.get(sku.id) else None
            original_data["product_version"] = product_histories.get(sku.product.id).history_id if sku and product_histories.get(sku.product.id) else None

            sku_cost_price = sku_histories.get(sku.id).cost_price if sku and sku_histories.get(sku.id) else None
            item_num = original_data.get("item_num")
            if not item_num:
                logger.info(f"[{ex_order_id}]-[{raw_spec_code}] item_num error: {item_num}")
            if sku_cost_price:
                original_data["cost_price_amount"] = sku_cost_price * (item_num or 0)
            # 合并重复数据
            key = (str(original_data.get("ex_order_id", "")), str(original_data.get("raw_sku_id", "")))

            original = original_order_objs.get(key)
            if not original:
                original = OriginalOrder(**original_data)
                original_order_objs[key] = original
            else:
                # 取消遍历
                order_status_v = original_data.get("order_status")
                if not order_status_v:
                    continue

                if ORDER_STATUS_TF.get(order_status_v, 0) < ORDER_STATUS_TF.get(original.order_status, 0):
                    continue

                setattr(original, "order_status", order_status_v)

            # 对于新增的数据，计算销量和库存
            if key not in existed_order:
                if sku and data.get("item_num"):
                    _original = original_order_objs.get(key)
                    logger.warning(f"{_original.order_time}, {calculate_inventory_time}")
                    if _original.order_time < calculate_inventory_time:
                        # print(f"old order order_time: {_original.order_time}, calculate_inventory_time: {calculate_inventory_time}")
                        continue
                    new_key = key + (sku.sku_id,)
                    item_num_dict[new_key] = data["item_num"]

            # 创建历史价
            author_id = dist_author_dict.get(data.get("author_id", ""), {}).get("live_author__author_id") or dist_letters_dict.get(data.get("letters", ""), {}).get("live_author__author_id")
            if sku:
                sku_id = sku.id
                create_date = original_data.get("order_time").strftime(DATETIME_FORMAT)
                history_price_data = {
                    "product_id": sku.product.id,
                    "sku_id": sku_id,
                    "history_price": original_data.get("goods_price"),
                    "author_id": author_id,
                    "create_date": create_date,
                }

                # 有支付时间的插入离时间
                if original_data.get("pay_time"):
                    history_price_data_dict[str((sku_id, create_date, author_id))] = history_price_data

        order_base_instance_ls, original_order_instance_ls = [], []
        inventory_change_logs = []
        if original_order_objs:
            try:
                with transaction.atomic():
                    if order_based_objs:
                        order_base_instance_ls = OrderBasedOnSupplier.objects.bulk_create(
                            order_based_objs.values(),
                            batch_size=100,
                            update_conflicts=True,
                            update_fields=[f for f in order_based_fields if f not in ("id", "order_id", "ex_order_id")],
                            unique_fields=["ex_order_id", "company_id"],
                        )
                    original_order_instance_ls = OriginalOrder.objects.bulk_create(
                        original_order_objs.values(),
                        batch_size=100,
                        update_conflicts=True,
                        update_fields=[f for f in original_fields if f not in ("id", "order_id", "ex_order_id")],
                        unique_fields=["ex_order_id", "raw_sku_id"],
                    )
                    # 同步库存和销量
                    if item_num_dict:
                        item_num_sku = {}
                        for k, v in item_num_dict.items():
                            sku_sku_id = k[2]
                            if sku_sku_id in item_num_sku:
                                item_num_sku[sku_sku_id]["sales"] += v
                                item_num_sku[sku_sku_id]["keys"].append(k)
                            else:
                                item_num_sku[sku_sku_id] = {"sales": v, "keys": [k]}

                        sku_objs = StockKeepingUnit.objects.filter(sku_id__in=item_num_sku.keys()).select_for_update()
                        for sku in sku_objs:
                            sales = item_num_sku[sku.sku_id]["sales"]
                            if not sales:
                                continue
                            sku.physical_inventory = (sku.physical_inventory or 0) - sales
                            sku.sales = (sku.sales or 0) + sales
                            inventory_change_logs.append(
                                {
                                    "sku_id": sku.sku_id,
                                    "value": sales,
                                    "action": "-",
                                    "order_type": "order",
                                    "after_changed_value": sku.physical_inventory,
                                    "data_source": data_source,
                                    "record_keys": item_num_sku[sku.sku_id]["keys"],
                                }
                            )
                        StockKeepingUnit.objects.bulk_update(sku_objs, ["physical_inventory", "sales"])
            except ValueError as e:
                logger.info(f"{str(e)}--{traceback.format_exc()}")
                try:
                    WriteOrderFailed.objects.create(data_source=data_source, task_type="order", data=raw_data, calculate_inventory_time=calculate_inventory_time, errors=str(e))
                except Exception as e:
                    logger.info(f"{str(e)}--{traceback.format_exc()}")
                return f"insert_original_order err: {str(e)}"
            except IntegrityError as e:
                logger.info(f"{str(e)}--{traceback.format_exc()}")
                try:
                    WriteOrderFailed.objects.create(data_source=data_source, task_type="order", data=raw_data, calculate_inventory_time=calculate_inventory_time, errors=str(e))
                except Exception as e:
                    logger.info(f"{str(e)}--{traceback.format_exc()}")
                return f"insert_original_order err: {str(e)}"

        # 计算实际销量和标记已播
        if original_order_instance_ls:
            original_orders = [(i.order_time, i.distributor.distributor_id, i.sku.sku_id, i.goods_price) for i in original_order_instance_ls if i.distributor and i.sku]
            calculate_actual_sales_and_mark_has_live.delay(data_source, original_orders, calculate_inventory_time)

        # 基于供应商的订单数据计算
        if order_base_instance_ls:
            order_base_ids = [(i.ex_order_id, i.company_id) for i in order_base_instance_ls]
            calculate_order_based_on_supplier.delay(data_source, order_base_ids, calculate_inventory_time)

        # 批量创建历史价
        if history_price_data_dict:
            bulk_create_history_prices.delay(data_source, history_price_data_dict, calculate_inventory_time)

        if temp_product_list:
            bulk_create_temp_products.delay(temp_product_list)

        if inventory_change_logs:
            try:
                objs = [OrderSKUInventoryChangeLog(**d) for d in inventory_change_logs]
                OrderSKUInventoryChangeLog.objects.bulk_create(objs)
            except Exception as e:
                logger.error(f"OrderSKUInventoryChangeLog err: {str(e)}--{traceback.format_exc()}")
        logger.info(f">>>>insert order waist time: {time.time() - start}")
        return "insert_original_order success"
    except Exception as e:
        print(f"{str(e)}--{traceback.format_exc()}")
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        try:
            WriteOrderFailed.objects.create(data_source=data_source, task_type="order", data=raw_data, calculate_inventory_time=calculate_inventory_time, errors=str(e))
        except Exception as e:
            logger.info(f"{str(e)}--{traceback.format_exc()}")

        return f"Failed: insert_original_order err: {str(e)}"


@app.task(queue="common_tasks")
def calculate_actual_sales_and_mark_has_live(data_source, original_orders, calculate_inventory_time):
    """
    计算实际销量并标记已播
    """
    try:
        query = Q()
        goods_price_map = {}
        for order_time, distributor_id, sku_id, goods_price in original_orders:
            # 订单时间属于凌晨4点前的算前一天的
            if isinstance(order_time, str):
                dt = datetime.fromisoformat(order_time)
                order_time = convert_datatime_val_2_aware_time(dt)
            _order_date = order_time.date()
            hour = order_time.hour
            if hour < 4:
                _order_date = _order_date - timedelta(days=1)
            query |= Q(
                # selection_plan__state__in=[1, 2],
                selection_plan__live_date_start__lte=_order_date,
                selection_plan__live_date_end__gte=_order_date,
                selection_plan__distributor_id=distributor_id,
                sku=sku_id,
            )
            goods_price_map[sku_id] = goods_price

        if not goods_price_map:
            return

        plan_sku_dict = {}
        item_sku_qs = ProductSelectionItemSKU.objects.using("default").filter(query).select_related("selection_plan", "item", "sku")
        for itme_sku in item_sku_qs:
            live_date_start = timezone.make_aware(
                datetime.combine(
                    itme_sku.selection_plan.live_date_start,
                    datetime.min.time(),
                ),
                timezone.get_current_timezone(),
            )
            live_date_end = timezone.make_aware(
                datetime.combine(itme_sku.selection_plan.live_date_end, datetime.min.time()) + timedelta(hours=28, minutes=0, seconds=0),
                timezone.get_current_timezone(),
            )
            distributor_id = itme_sku.selection_plan.distributor.id
            if not plan_sku_dict.get((live_date_start, live_date_end, distributor_id)):
                plan_sku_dict[(live_date_start, live_date_end, distributor_id)] = [itme_sku]
            else:
                plan_sku_dict[(live_date_start, live_date_end, distributor_id)].append(itme_sku)

        for k, v in plan_sku_dict.items():
            live_date_start, live_date_end, distributor_id = k
            sku_ids = [item_sku.sku.id for item_sku in v]

            # 支付后的才算销量，有pay_time. 不使用is not null跑索引
            orders = OriginalOrder.objects.using("default").filter(
                order_time__range=(live_date_start, live_date_end),
                distributor_id=distributor_id,
                sku_id__in=sku_ids,
                pay_time__gte=live_date_start,
                from_mall=False,
                order_type=0,
            )
            # 根据SKU分组,计算总销量,并获取最新售价
            # [{'sku__sku_id': 163841541, 'total_sales': 1, 'latest_price': Decimitemal('10.00')}]
            sku_sales = orders.values("sku_id").annotate(
                total_sales=Sum("item_num"),
                order_count=Count("ex_order_id", distinct=True),
                sales_amount=Sum("order_amount"),
            )
            sku_sales_dict = {i["sku_id"]: i for i in sku_sales}

            bulk_update_item_sku = []
            bulk_update_items = []
            for item_sku in v:
                sku__sku_id = item_sku.sku.sku_id
                sku_pk = item_sku.sku.id
                _sku_sales = sku_sales_dict.get(sku_pk)
                if not _sku_sales:
                    continue
                actual_price = goods_price_map.get(sku__sku_id)
                actual_sales = _sku_sales.get("total_sales")
                order_count = _sku_sales.get("order_count")
                sales_amount = _sku_sales.get("sales_amount")

                # 更新统计信息
                item_sku.actual_price = actual_price
                item_sku.actual_sales = actual_sales
                item_sku.order_count = order_count
                item_sku.sales_amount = sales_amount
                bulk_update_item_sku.append(item_sku)

                if not item_sku.item.has_live:
                    item_sku.item.has_live = True
                    bulk_update_items.append(item_sku.item)
            if bulk_update_item_sku:
                ProductSelectionItemSKU.objects.bulk_update(bulk_update_item_sku, ["actual_price", "actual_sales", "order_count", "sales_amount"])
            if bulk_update_items:
                ProductSelectionItem.objects.bulk_update(bulk_update_items, ["has_live"])
        print("calculate_actual_sales_and_mark_has_live completed.")
    except Exception as e:
        logger.error(f"{str(e)}--{traceback.format_exc()}")
        try:
            WriteOrderFailed.objects.create(
                data_source=data_source, task_type="actual_sales", data=json.dumps(original_orders, cls=DjangoJSONEncoder), calculate_inventory_time=calculate_inventory_time, errors=str(e)
            )
        except Exception as e1:
            logger.info(f"{str(e1)}--{traceback.format_exc()}")
        return f"Failed: {str(e)}"


@app.task(queue="common_tasks")
def bulk_create_history_prices(data_source: str, data_dict: dict, calculate_inventory_time):
    """

    批量创建历史价
    data_dict[(sku_id, create_date, author_id)] = history_price_data

    history_price_data = {
        "product_id": sku.product.id,
        "sku_id": sku_id,
        "history_price": original_data.get("goods_price"),
        "author_id": author_id,
        "create_date": create_date,
    }

    history_price_data_dict[str((sku_id, create_date, author_id))] = history_price_data

    data_source: DD抖店, JST聚水潭
    """

    # 过期时间
    def get_seconds_until_next_day_one_am():
        now = datetime.now()
        # 计算第二天凌晨1点的时间
        next_day_one_am = (now + timedelta(days=1)).replace(hour=1, minute=0, second=0, microsecond=0)
        # 计算从现在到第二天凌晨1点的秒数
        seconds_until = (next_day_one_am - now).seconds
        return seconds_until

    def batch_insert_orders_history_price(_data_source, orders_history_dict: list[dict]):
        # 生成set
        # 根据sku_id,日期date,价格,主播id作为唯一键
        redis_set_key = "order_history_price_set"
        redis_keys = [
            f"order:{history_data['sku_id']}:{datetime.strptime(history_data['create_date'], DATETIME_FORMAT).strftime(DATE_FORMAT)}:{history_data['history_price']}:{history_data['author_id']}"
            for history_data in orders_history_dict
        ]
        # 批量查询 Bitmap
        pipeline = redis_conn.pipeline()
        for key in redis_keys:
            pipeline.sismember(redis_set_key, key)
        results = pipeline.execute()
        # 过滤需要插入数据库的订单
        history_objects_to_insert = [orders_history_dict[i] for i in range(len(orders_history_dict)) if results[i] == 0]
        bitmap_indices_to_set = [redis_keys[i] for i in range(len(redis_keys)) if results[i] == 0]

        # 批量插入数据库
        if history_objects_to_insert:
            history_price_bulk_objects = [
                HistoryPrice(
                    product_id=obj["product_id"],
                    sku_id=obj["sku_id"],
                    history_price=obj["history_price"],
                    author_id=obj["author_id"],
                    create_date=convert_datatime_val_2_aware_time(obj["create_date"]),
                )
                for obj in history_objects_to_insert
            ]

            HistoryPrice.objects.bulk_create(
                history_price_bulk_objects,
                batch_size=100,
                update_conflicts=True,
                update_fields=["history_price"],
                unique_fields=["sku_id", "create_date", "author_id"],
            )

        # 批量设置 Bitmap
        if bitmap_indices_to_set:
            seconds_until_next_day_one_am = get_seconds_until_next_day_one_am()
            pipeline = redis_conn.pipeline()
            for key in bitmap_indices_to_set:
                pipeline.sadd(redis_set_key, key)
            pipeline.expire(redis_set_key, seconds_until_next_day_one_am)
            pipeline.execute()
            print(f"Added {len(bitmap_indices_to_set)} keys to Redis Set with expiration")

    cleaned_data_dict = {}
    for data in data_dict.values():
        sku_id = data["sku_id"]
        price = data["history_price"]
        author_id = data["author_id"]
        date_str = datetime.strptime(data["create_date"], DATETIME_FORMAT).strftime(DATE_FORMAT)
        cleaned_data_dict[f"{sku_id}{price}{author_id}{date_str}"] = data
    # 转换数据
    batch_insert_orders_history_price(data_source, list(cleaned_data_dict.values()))


@app.task(queue="common_tasks")
def bulk_create_temp_products(product_info_list: list[dict]):
    # {
    #     "product_id": data["raw_product_id"],
    #     "category": _category_list,
    #     "name": data.get("raw_product_name"),
    #     "pic": data.get("raw_product_pic"),
    #     "sku_id": data["raw_sku_id"],
    #     "raw_spec_code": raw_spec_code,
    #     "parsed_spec_code": sku__spec_code,
    #     "sku_specs": data["raw_sku_specs"],
    #     "price": data["goods_price"],
    #     "shop_id": data["shop_id"],
    #     "shop_name": data["shop_name"],
    #     "author_id": data["author_id"],
    #     "author_name": data["author_name"],
    #     "distributor_id": distributor_id,
    #     "order_date": data["order_time"].strftime(DATE_FORMAT),
    #     "ex_order_id": data["ex_order_id"],
    # }

    # 组合ex_order_id
    clean_dict = {}
    for info in product_info_list:
        # 过滤没有id的数据
        if not info["product_id"] or not info["sku_id"]:
            continue

        uni_id = md5("{}{}{}".format(info["product_id"], info["sku_id"], info["order_date"]).encode()).hexdigest()

        ex_order_id = info.pop("ex_order_id")

        info["uni_id"] = uni_id
        if uni_id not in clean_dict:
            clean_dict[uni_id] = {"temp_product_obj": TempProduct(**info), "ex_order_id_list": [ex_order_id]}
        else:
            clean_dict[uni_id]["temp_product_obj"] = TempProduct(**info)
            clean_dict[uni_id]["ex_order_id_list"].append(ex_order_id)

    temp_product_bulk_create_objs = []
    temp_product_detail_bulk_create_objs = []

    for k, v in clean_dict.items():
        temp_product_obj = v["temp_product_obj"]
        ex_order_id_list = v["ex_order_id_list"]

        temp_product_bulk_create_objs.append(temp_product_obj)

        for _ex_order_id in ex_order_id_list:
            temp_product_detail_bulk_create_objs.append(TempProductDetail(relate_product_id=temp_product_obj.uni_id, ex_order_id=_ex_order_id))

    with transaction.atomic():
        created_product_rows = len(
            TempProduct.objects.bulk_create(
                temp_product_bulk_create_objs,
                batch_size=1000,
                update_conflicts=True,
                unique_fields=("uni_id",),
                update_fields=(
                    "category",
                    "name",
                    "pic",
                    "raw_spec_code",
                    "parsed_spec_code",
                    "price",
                    "shop_id",
                    "shop_name",
                    "author_id",
                    "author_name",
                    "distributor_id",
                ),
            )
        )

        created_product_detail_rows = len(TempProductDetail.objects.bulk_create(temp_product_detail_bulk_create_objs, batch_size=1000, ignore_conflicts=True))

    print(f">>创建临时商品, {created_product_rows}条, 订单关联: {created_product_detail_rows}条")


@app.task(queue="common_tasks")
def calculate_order_based_on_supplier(data_source, order_list, calculate_inventory_time):
    """
    计算基于供应商的订单数据：
    1.金额计算
    2.状态计算
    """
    # 订单状态流转顺序
    ORDER_STATUS = {
        "RD": 0,
        "DL": 1,
        "FN": 2,
        "CL": 3,
    }
    try:
        if not order_list:
            return
        query = Q()
        for ex_order_id, company_id in order_list:
            query |= Q(ex_order_id=ex_order_id, company_id=company_id)

        order_based_list = []
        update_fields = ["order_status", "cost_price_amount", "paid_amount", "pay_amount", "count"]

        original_order_prefetch = Prefetch("originalorder_set", OriginalOrder.objects.only("cost_price_amount", "order_status", "paid_amount", "pay_amount"))
        order_based_qs = OrderBasedOnSupplier.objects.using("default").prefetch_related(original_order_prefetch).filter(query)

        for order_based in order_based_qs:
            original_qs = order_based.originalorder_set.all()

            cost_price_amount = 0
            paid_amount = 0
            pay_amount = 0
            status_list = []
            count = 0
            for original in original_qs:
                if original.cost_price_amount is not None:
                    cost_price_amount += original.cost_price_amount

                if original.paid_amount is not None:
                    paid_amount += original.paid_amount

                if original.pay_amount is not None:
                    pay_amount += original.pay_amount
                status_list.append(original.order_status)
                count += 1

            if not status_list:
                continue

            max_status = max(status_list, key=lambda status: ORDER_STATUS.get(status, 0))
            order_based.order_status = max_status
            order_based.cost_price_amount = cost_price_amount
            order_based.paid_amount = paid_amount
            order_based.pay_amount = pay_amount
            order_based.count = count

            if order_based.total_promotion_amount is None:
                order_based.total_promotion_amount = order_based.order_amount - order_based.paid_amount
                update_fields.append("total_promotion_amount")

            order_based_list.append(order_based)
            # 更新历史
            # order_history, _ = OrderStatusHistory.objects.using("default").get_or_create(order_id=order_based.order_id, status=max_status)
        OrderBasedOnSupplier.objects.bulk_update(order_based_list, fields=update_fields)
    except Exception as e:
        logger.error(f"calculate_order Err: {str(e)}--{traceback.format_exc()}")
        try:
            WriteOrderFailed.objects.create(
                data_source=data_source,
                task_type="on_supplier",
                data=order_list,
                calculate_inventory_time=calculate_inventory_time,
                errors=str(e),
            )
        except Exception as e1:
            logger.info(f"{str(e1)}--{traceback.format_exc()}")
        return f"Failed: {str(e)}"


@app.task(queue="long_running_tasks")
def retry_insert_failed_order():
    """
    重试插入失败的订单
    """
    wof_qs = WriteOrderFailed.objects.all().order_by("-create_date")[:100]
    for wof in wof_qs:
        try:
            data_source = wof.data_source
            task_type = wof.task_type
            data = wof.data
            calculate_inventory_time = wof.calculate_inventory_time

            if calculate_inventory_time:
                calculate_inventory_time = convert_datatime_val_2_aware_time(calculate_inventory_time)
            else:
                calculate_inventory_time = convert_datatime_val_2_aware_time("2025-07-05 00:00:00")
            if task_type == "order":
                insert_original_order(data_source, data, calculate_inventory_time)
            elif task_type == "actual_sales":
                decoded_data = json.loads(data)
                calculate_actual_sales_and_mark_has_live.delay(data_source, decoded_data, calculate_inventory_time)
            elif task_type == "on_supplier":
                calculate_order_based_on_supplier.delay(data_source, data, calculate_inventory_time)
            elif task_type == "history_prices":
                decoded_data = json.loads(data)
                bulk_create_history_prices.delay(data_source, decoded_data, calculate_inventory_time)
            else:
                print(f"wrong task_type: {task_type}")
                continue
            wof.delete()
        except Exception as e:
            wof.retry_times += 1
            wof.save(update_fields=["retry_times"])
            print(f"retry_insert_failed_order err: {str(e)}--{traceback.format_exc()}")
            continue
    return "retry completed"


@app.task(queue="long_running_tasks")
def dy_order_fetcher_with_order_create_time(
    shop_pk,
    shop_id,
    create_time_start: int,
    create_time_end: int,
    calculate_inventory_time_str: None | str = None,
):
    """
    抖店订单拉取
    :param create_time_end: 订单创建开始时间  【时间戳】
    :param create_time_start: 订单创建结束时间 【时间戳】
    :param shop_pk: 店铺主键
    :param shop_id: 店铺shop_id
    :param calculate_inventory_time_str: datetime类型的str
    :return:
    """
    # 计算库存扣减时间
    calculate_inventory_time = convert_datatime_val_2_aware_time(calculate_inventory_time_str or "2025-07-16 00:00:00")

    lock_key = f"dy_order_task_lock:{shop_id}_{create_time_start}_{create_time_end}"
    need_release_lock = True
    redis_con = None
    try:
        redis_con = gen_redis_conn()
        if not redis_con.setnx(lock_key, 1):
            need_release_lock = False
            return f"SUCCESS, 抖店{shop_id}-订单-正在执行查询.无需重复执行"

        redis_con.expire(lock_key, 86400)
        # 默认是2024-06-01 00:00:00
        sdk_request = OrderSearchListRequest()
        print(f">>>>{datetime.now().strftime(DATETIME_FORMAT)}开始{shop_id}抖店订单任务, 订单创建开始时间:{create_time_start},结束时间:{create_time_end}")
        params = sdk_request.getParams()
        # 提前30s
        params.create_time_start = create_time_start - 30
        params.create_time_end = create_time_end
        params.order_by = "create_time"
        params.order_asc = True
        params.size = 100
        params.page = 0

        response = execute_order_request(shop_pk, sdk_request)
        if response.code != 10000:
            return f"FAIL: {response.sub_msg}"

        # 第一页查询
        shop_order_list = response.data["shop_order_list"]
        if not shop_order_list:
            return "SUCCESS, 没有订单."

        # 保存原始订单表
        process_order_data(shop_order_list, shop_id)

        insert_original_order("DD", shop_order_list, calculate_inventory_time)

        # 计算total_page
        total_nums = response.data["total"]
        if total_nums < 100:
            return f"SUCCESS, 总页数:{1}"
        # 计算总页数
        total_pages = math.ceil(total_nums / 100)

        chain_task = False
        # 只爬取500页数据, 从0开始
        if total_pages > 499:
            chain_task = True
            total_pages = 500
        for page in range(1, total_pages):
            # 第二页查询
            params.page = page
            response = execute_order_request(shop_pk, sdk_request)
            if response.code != 10000:
                return f"FAIL: {response.sub_msg}"

            response_data = response.data

            if response_data:
                shop_order_list = response_data["shop_order_list"]
                # 插入元数据
                process_order_data(shop_order_list, shop_id)

                insert_original_order("DD", shop_order_list, calculate_inventory_time)

                if chain_task and page == 499:
                    # 得出当前最大的创建时间
                    max_create_time = max([d["create_time"] for d in shop_order_list])
                    dy_order_fetcher_with_order_create_time.delay(shop_pk, shop_id, max_create_time, create_time_end, calculate_inventory_time_str)

            print(f"完成{shop_id}第{page}页")
        return f"SUCCESS,总页数:{total_pages}"
    except Exception as e:
        print(f">>> 获取抖店订单错误,{e},{traceback.format_exc()}")
        return f"FAIL,{e}"
    finally:
        if redis_con and need_release_lock:
            redis_con.delete(lock_key)


def generate_time_intervals(start_timestamp: int, end_timestamp: int, interval_minutes=60 * 24):
    if end_timestamp - start_timestamp > 86400:
        intervals = [start_timestamp, start_timestamp]

    intervals = []
    current_time = datetime.fromtimestamp(start_timestamp)

    now_timestamp = int(time.time())
    end_time = datetime.fromtimestamp(end_timestamp)

    # 如果end_time比现在大，取现在时间
    if end_timestamp > now_timestamp:
        end_time = datetime.fromtimestamp(now_timestamp)

    while current_time < end_time:
        next_time = current_time + timedelta(minutes=interval_minutes)
        if next_time > end_time:
            intervals.append((int(current_time.timestamp()), int(end_time.timestamp())))
            break
        intervals.append((int(current_time.timestamp()), int(next_time.timestamp())))
        current_time = next_time

    return intervals


@app.task(queue="celery")
def dy_order_scanner(
    in_start_timestamp: int | None = None,
    in_end_timestamp: int | None = None,
    in_shop_id: int | None = None,
    interval_minutes: int = 60 * 24,
):
    """
    :param in_start_timestamp:
    :param in_end_timestamp:
    :param in_shop_id:
    :param interval_minutes: 拆分任务时间，默认720分钟，太快容易导致oom
    :return:
    """
    dy_order_app = dy_order_application()
    relates = dy_order_app.dataapplicationshops_set.filter(enable=True, is_authorized=True)
    config = get_dy_order_fetcher_config()

    for relate in relates:
        shop_pk = relate.data_shop.id
        shop_id = relate.data_shop.shop_id

        try:
            get_data_shop_access_token("order", shop_pk)
        except Exception as e:
            logger.warning(f"{shop_id},{in_start_timestamp}-{in_end_timestamp}获取token失败:{e}")
            continue

        if in_shop_id and str(in_shop_id) != str(shop_id):
            continue

        # 人工补单
        if in_start_timestamp and in_end_timestamp:
            # 大于一天的只启动一个任务
            if in_end_timestamp - in_start_timestamp > 86400:
                intervals = [in_start_timestamp, in_end_timestamp]
            else:
                intervals = generate_time_intervals(in_start_timestamp, in_end_timestamp, interval_minutes)

            print(f">>>补单{shop_id}启动{len(intervals)}个任务,{in_start_timestamp}-{in_end_timestamp}")
            for interval_time in intervals:
                min_create_time, max_create_timestamp = interval_time

                if min_create_time == max_create_timestamp:
                    continue

                min_create_timestamp, max_create_time_timestamp = int(min_create_time), int(max_create_timestamp)
                # 查询订阅表的创建时间
                dy_order_fetcher_with_order_create_time.delay(shop_pk, shop_id, min_create_timestamp, max_create_time_timestamp, dy_order_app.calculate_inventory_time)

            continue

        # 查询最大的id
        subscribe_ret = DyOrderSubscribe.objects.filter(shop_id=shop_id).only("id").last()
        if not subscribe_ret:
            continue

        max_id = subscribe_ret.id

        # 人工先初始化拉取订单
        # 然后根据订阅不断拉取数据
        if in_start_timestamp:
            in_start_time = convert_timestamp_2_aware_time(in_start_timestamp)
            first_id_object = DyOrderSubscribe.objects.filter(shop_id=shop_id, platform_create_time__gte=in_start_time).only("id").first()
            if not first_id_object:
                logger.info(f">>>>{shop_id} {in_start_time}没有最新订阅,不启动任务")
                continue
            start_id = first_id_object.id
        else:
            if str(shop_id) not in config.extra:
                start_time = datetime.combine(datetime.today().date(), datetime.min.time())
                aware_start_time = convert_datatime_val_2_aware_time(start_time)
                first_id_object = DyOrderSubscribe.objects.filter(shop_id=shop_id, platform_create_time__gte=aware_start_time).only("id").first()
                if not first_id_object:
                    logger.info(f">>>>{shop_id} {start_time}没有最新订阅,不启动任务2")
                    continue
                else:
                    start_id = first_id_object.id
            else:
                start_id = int(config.extra[str(shop_id)])
                if start_id == max_id:
                    logger.info(f">>>>{shop_id}没有最新订阅,不启动任务")
                    continue

        create_time_ret = DyOrderSubscribe.objects.filter(id__range=[start_id, max_id], shop_id=shop_id).aggregate(
            min_create_time=Min("platform_create_time"),
            max_create_time=Max("platform_create_time"),
        )

        min_create_time, max_create_time = create_time_ret["min_create_time"], create_time_ret["max_create_time"]
        min_create_timestamp, max_create_time_timestamp = int(min_create_time.timestamp()), int(max_create_time.timestamp())
        # 每20分钟拆分任务
        intervals = generate_time_intervals(min_create_timestamp, max_create_time_timestamp, interval_minutes)
        logger.info(f">>>{shop_id}启动{len(intervals)}个任务,{min_create_time}-{max_create_time}")
        for interval_time in intervals:
            min_create_timestamp, max_create_timestamp = interval_time
            # 查询订阅表的创建时间
            dy_order_fetcher_with_order_create_time.delay(shop_pk, shop_id, min_create_timestamp, max_create_timestamp, dy_order_app.calculate_inventory_time)

        extra = config.extra or {}
        extra.update({str(shop_id): max_id})
        config.extra = extra
        config.save()

        # 先同步config
        FetchConfigs.objects.update_or_create(cnf_source="dy", cnf_type="order", shop_id=shop_id, defaults={"start_id": max_id})


# ======================== 订单END ========================


# ======================== 珠凌臻品START ========================
@app.task(queue="celery")
def sync_new_order_to_jst(ex_order_id: str):
    """

    todo: 上传订单到聚水潭
    https://openweb.jushuitan.com/dev-doc?docType=4&docId=18
    :return:
    """
    shop_id = settings.JST_SELF_OPERATED_SHOP_ID
    label_name = "珠凌臻品"
    try:
        original_orders = OriginalOrder.objects.filter(ex_order_id=ex_order_id)

        order_date = None
        shop_status = "WAIT_SELLER_SEND_GOODS"
        buyer_id = ""
        receiver_state = ""
        receiver_city = ""
        receiver_district = ""
        receiver_address = ""
        receiver_name = ""
        receiver_phone = ""
        buyer_words = ""
        modified_time = ""

        pay_amount = 0
        so_id = ""
        pay_date = ""
        seller_account = ""

        # 自研商城系统订单状态：
        # 等待买家付款 = WAIT_BUYER_PAY，
        # 等待卖家发货 = WAIT_SELLER_SEND_GOODS（传此状态时实际支付金额即pay节点支付金额 = 应付金额ERP才会显示已付款待审核）,
        # 等待买家确认收货 = WAIT_BUYER_CONFIRM_GOODS,
        # 交易成功 = TRADE_FINISHED,
        # 付款后交易关闭 = TRADE_CLOSED,
        # 付款前交易关闭 = TRADE_CLOSED_BY_TAOBAO

        items = []
        for original_order in original_orders:
            if original_order.order_status == "WP":
                return {"success": False, "res": "未支付订单不需要上传"}

            sp_order = original_order.order_based_on_supplier

            # 店铺id
            if sp_order.distributor.distributor_mode == 2:
                shop_id = settings.DISTRIBUTOR_MODE_SHOP_ID
                label_name = "珠凌甄选"

            order_date = convert_2_aware_time(original_order.order_time)
            buyer_id = original_order.create_user
            receiver_state = original_order.receiver_province_name or ""
            receiver_city = original_order.receiver_city_name or ""
            receiver_district = original_order.receiver_town_name or ""
            receiver_address = (original_order.mask_post_addr or {}).get("detail", "")
            receiver_name = original_order.encrypt_post_receiver or ""
            receiver_phone = original_order.encrypt_post_tel or ""
            buyer_words = original_order.buyer_words or ""
            modified_time = original_order.update_date.strftime(DATETIME_FORMAT) if original_order.update_date else ""
            # 珠凌供应商订单号
            so_id = sp_order.ex_order_id

            pay_amount += original_order.paid_amount

            pay_date = convert_2_aware_time(original_order.pay_time)

            seller_account = original_order.create_user

            #
            items.append(
                {
                    # 珠凌商品 J12313123V
                    "sku_id": original_order.raw_spec_code,
                    # 珠凌商品 J12313123V
                    "shop_sku_id": original_order.raw_spec_code,
                    "amount": float(original_order.order_amount),
                    "base_price": float(original_order.cost_price_amount / original_order.item_num),
                    "qty": original_order.item_num,
                    "name": original_order.raw_product_name,
                    "outer_oi_id": original_order.order_id,
                }
            )

        post_data = [
            {
                "shop_id": int(shop_id),
                "so_id": so_id,
                "order_date": order_date,
                "shop_status": shop_status,
                "shop_buyer_id": buyer_id,
                "receiver_state": receiver_state,
                "receiver_city": receiver_city,
                "receiver_district": receiver_district,
                "receiver_address": receiver_address,
                "receiver_name": receiver_name,
                "receiver_phone": receiver_phone,
                "pay_amount": float(pay_amount),
                "freight": 0,
                # 卖家备注
                # "remark": "KCL",
                "buyer_message": buyer_words,
                "shop_modified": modified_time,
                # 快递单号
                "l_id": "",
                # 快递名称
                "logistics_company": "",
                "labels": label_name,
                "lc_id": "OTHER",
                "items": items,
                "pay": {
                    "outer_pay_id": ex_order_id,
                    "pay_date": pay_date,
                    "payment": "微信",
                    "seller_account": seller_account,
                    "buyer_account": seller_account,
                    "amount": float(pay_amount),
                },
            }
        ]
        response = jst_client.upload_order(post_data)
        return {"success": True, "res": response}
    except Exception as e:
        SyncOrderToJSTFail.objects.update_or_create(shop_id=shop_id, ex_order_id=ex_order_id, defaults=dict(error=str(traceback.format_exc())))
        print(traceback.format_exc())
        logger.warning(f">>>上传订单失败:{e}")
        return {"success": False, "error": str(e)}


@app.task(queue="common_tasks")
def zl_order_expired_scanner():
    try:
        # 定时扫描未支付,
        current_time = datetime.now()
        with transaction.atomic():
            original_orders = OriginalOrder.objects.filter(
                order_platform="ZL",
                order_status="WP",
                order_expire_time__lte=current_time,
            )
            update_original_order_rows = 0

            for original_order in original_orders:
                sku = original_order.sku
                if sku:
                    sku.physical_inventory = F("physical_inventory") + original_order.item_num
                    sku.save()
                original_order.order_status = "CC"
                original_order.cancel_reason = "超过支付有效时间"
                original_order.save()
                update_original_order_rows += 1

            update_sp_order_rows = OrderBasedOnSupplier.objects.filter(
                order_status="WP",
                order_platform="ZL",
                originalorder__order_expire_time__lte=current_time,
            ).update(order_status="CC")

        return {"update_original_order_rows": update_original_order_rows, "update_sp_order_rows": update_sp_order_rows}
    except Exception as e:
        logger.warning(f">>>未支付更新失败:{e}")
        return {"error": str(e)}


@app.task(queue="common_tasks")
def zl_order_fail_upload_scanner():
    records = SyncOrderToJSTFail.objects.all()[:100]
    for record in records:
        resp = sync_new_order_to_jst(ex_order_id=record.ex_order_id)
        if resp["success"]:
            record.delete()


@app.task(queue="common_tasks")
def zl_order_logistics_scanner():
    # 日常扫描聚水潭订单物流信息
    so_id_qs = (
        OriginalOrder.objects.filter(
            order_platform="ZL",
            order_status="RD",
        )
        .values_list("ex_order_id", flat=True)
        .distinct()
    )
    paginator = Paginator(so_id_qs, 50)

    try:
        for page_num in paginator.page_range:
            so_id_list = list(paginator.page(page_num).object_list)
            if not so_id_list:
                return {"message": "no need update"}
            # 更新订单信息
            resp = jst_client.query_order_logistics_info_by_so_id_list(so_id_list)
            orders = resp["orders"]
            for order in orders:
                so_id = order["so_id"]
                if not so_id:
                    continue

                ship_time = order["send_date"]
                lc_id = order["lc_id"]
                logistics_company = order["logistics_company"]
                l_id = order["l_id"]

                with transaction.atomic():
                    # 更新原始订单物流信息
                    OriginalOrder.objects.filter(ex_order_id=so_id).update(
                        ship_time=ship_time,
                        tracking_no=l_id,
                        logistics_company=lc_id,
                        logistics_company_name=logistics_company,
                        order_status="DL",
                    )

                    # 更新供应商订单的物流信息
                    OrderBasedOnSupplier.objects.filter(ex_order_id=so_id).update(
                        ship_time=ship_time,
                        order_status="DL",
                    )
                # 同步到微信支付
                parse_lc_id = str(lc_id).split(".")[0]
                if parse_lc_id in WECHAT_LOGISTICS_MAP:
                    # item_ex_order_id_list = set([item["outer_oi_id"] for item in order["items"]])
                    # for _ex_order_in in item_ex_order_id_list:
                    update_wechat_logistics.delay(so_id, l_id, parse_lc_id)
                order = OriginalOrder.objects.filter(ex_order_id=so_id)
                if order:
                    try:
                        order = order[0]
                        # 发送发货成功消息
                        dispatch_goods_notification_send(order)
                    except Exception as e:
                        logger.error(f"发送发货完成消息失败， 失败原因：{str(e)}")
        return {"success": True}
    except Exception as e:
        logger.warning(f">>>更新物流信息异常:{e}")
        return {"error": str(e)}


@app.task(queue="common_tasks")
def update_wechat_logistics(ex_order_id: str, tracking_no, logistics_id):
    origin_orders_qs_dict = OriginalOrder.objects.filter(ex_order_id=ex_order_id).values("doudian_open_id", "raw_spec_code", "item_num", "raw_product_name")
    if not origin_orders_qs_dict:
        return {"success": False, "msg": "订单查询失败", "ex_order_id": ex_order_id}
    open_id = ""
    for qs_dict in origin_orders_qs_dict:
        open_id = qs_dict["doudian_open_id"]
    if not open_id:
        return {"success": False, "msg": "订单缺少open_id", "ex_order_id": ex_order_id}
    try:
        item_desc = ";".join([f"{origin_order_info['raw_product_name']} * {origin_order_info['item_num']}" for origin_order_info in origin_orders_qs_dict])
        if len(item_desc) > 120:
            item_desc = ";".join([f"{origin_order_info['raw_spec_code']} * {origin_order_info['item_num']}" for origin_order_info in origin_orders_qs_dict])

        total_count = 0
        for info in origin_orders_qs_dict:
            if info["item_num"]:
                total_count += info["item_num"]

        if len(item_desc) > 120:
            item_desc = f"珠凌臻品商品购买:{total_count}件"

        # 更新
        resp = mall_client.shipping.upload_shipping_info(ex_order_id, tracking_no, logistics_id, item_desc, open_id)
        return {"success": True, "ex_order_id": ex_order_id, "resp": json.dumps(resp.json(), ensure_ascii=False)}
    except Exception as e:
        return {"success": False, "msg": str(e)}


@app.task(queue="common_tasks")
def zl_order_finish_scanner():
    try:
        # 定时扫描已发货 未确认收货7天的订单
        seven_days_before = datetime.now() - timedelta(days=7)

        original_orders = OriginalOrder.objects.filter(
            order_platform="ZL",
            order_status="DL",
            ship_time__lte=seven_days_before,
        )
        update_original_order_rows = 0
        update_sp_order_rows = 0
        with transaction.atomic():
            for original_order in original_orders:
                # 有售后单都算完成
                # if original_order.aftersalesrelatedorderinfo_set.filter(is_pass__in=[0, 1]):
                #     # 有售后中的订单不确认收货
                #     continue

                # 更新已完成状态
                original_order.order_status = "FN"
                original_order.finish_time = timezone.now()
                original_order.save()

                update_original_order_rows += 1
                # 更新供应商订单状态
                sp_order = original_order.order_based_on_supplier
                sp_order.order_status = "FN"
                sp_order.save()
                update_sp_order_rows += 1
                try:
                    order_finish_notification_send(original_order)
                except Exception as e:
                    logger.error(f"发送订单完成消息失败， 失败原因：{str(e)}")
        return {"update_original_order_rows": update_original_order_rows, "update_sp_order_rows": update_sp_order_rows}
    except Exception as e:
        logger.warning(f">>>未确认售后7天更新失败:{e}")
        return {"error": str(e)}


@app.task(queue="common_tasks")
def zl_order_close_scanner():
    try:
        # 定时扫描已完成，没售后的7天的订单
        seven_days_before = datetime.now() - timedelta(days=7)

        original_orders = OriginalOrder.objects.filter(
            order_platform="ZL",
            order_status="FN",
            finish_time__lte=seven_days_before,
        )
        update_original_order_rows = 0
        update_sp_order_rows = 0
        with transaction.atomic():
            for original_order in original_orders:
                if original_order.aftersalesrelatedorderinfo_set.filter(is_pass__in=[0, 1]):
                    # 有售后中的订单不确认收货
                    continue

                # 更新关闭状态
                original_order.order_status = "CL"
                original_order.finish_time = timezone.now()
                original_order.save()

                update_original_order_rows += 1
                # 更新供应商订单状态
                sp_order = original_order.order_based_on_supplier
                sp_order.order_status = "CL"
                sp_order.save()
                update_sp_order_rows += 1

        return {"update_original_order_rows": update_original_order_rows, "update_sp_order_rows": update_sp_order_rows}
    except Exception as e:
        logger.warning(f">>>完成后，7天更新完成状态失败:{e}")
        return {"error": str(e)}


@app.task(queue="common_tasks")
def zl_as_order_pay_status_scanner():
    """
    查询退款状态
    :return:
    """
    sub_as_orders = AfterSalesRelatedOrderInfo.objects.filter(as_order__data_source="ZL", as_order__refund_state=2)
    paginator = Paginator(sub_as_orders, 50)
    success_count = 0
    try:
        for page_num in paginator.page_range:
            orders = paginator.page(page_num).object_list
            for sub_as_order in orders:
                as_order = sub_as_order.as_order

                refund_no = as_order.ex_as_order_id
                if not refund_no:
                    continue
                resp = check_refund_status(refund_no)
                if resp.status_code != 200:
                    logger.warning("查询退款结果失败")
                    continue

                response_json = resp.json()
                status = response_json["status"]
                original_order = sub_as_order.original_order
                if status == "CLOSE":
                    logger.warning(f">>>微信关闭退款:{response_json}")
                    as_order.refund_state = 4
                    as_order.state_text = "退款关闭"
                    as_order.as_status_text = "同意申请,退款关闭"
                    as_order.platform_update_time = timezone.now()
                    as_order.refund_info = response_json
                    as_order.save()
                    # 售后成功, 退款成功3
                    original_order.after_sale_status = 28
                    original_order.refund_status = 4
                    original_order.order_status = "CL"
                    original_order.save()
                elif status == "SUCCESS":
                    # 退款完成
                    as_order.refund_state = 3
                    as_order.state_text = "退款成功"
                    as_order.as_status_text = "同意退款，退款成功"
                    as_order.platform_update_time = timezone.now()
                    as_order.final_time = timezone.now()
                    as_order.refund_info = response_json

                    # 售后成功, 退款成功3
                    original_order.after_sale_status = 12
                    original_order.refund_status = 3
                    original_order.order_status = "CL"
                    original_order.save()

                    as_order.save()
                    # 发送退款成功消息
                    try:
                        user_obj = User.objects.get(id=sub_as_order.create_user)
                        refund_notification_send(sub_as_order, user_obj)
                    except Exception as e:
                        logger.error(f"发送售后退款消息错误{str(e)}")
                elif status == "ABNORMAL":
                    # 退款完成
                    as_order.refund_state = 4
                    as_order.state_text = "退款失败"
                    as_order.as_status_text = "同意申请,退款异常"
                    as_order.platform_update_time = timezone.now()
                    as_order.refund_info = response_json
                    as_order.save()
                    # 售后中, 退款失败
                    original_order.after_sale_status = 28
                    original_order.refund_status = 4
                    original_order.order_status = "CL"
                    original_order.save()
                success_count += 1

        return {"success": True, "success_count": success_count}
    except Exception as e:
        logger.warning(f">>>更新物流信息异常:{e}")
        return {"error": str(e)}


# ======================== 珠凌臻品END ========================


@app.task(queue="common_tasks")
def cleanup_dy_order_create_subscribe_message(days: int = 15):
    """
    清理订单创建订阅信息,只保留15天的数据
    :return:
    """
    clean_day = datetime.now().date() - timedelta(days=days)

    deleted, rows = DyOrderSubscribe.objects.filter(create_date__lte=clean_day).delete()
    return json.dumps({"day": f"{clean_day}", "deleted": deleted, "rows": rows})


@app.task(queue="common_tasks")
def cleanup_dy_order_subscribe_message(days: int = 15):
    clean_day = datetime.now().date() - timedelta(days=days)

    deleted, rows = DyOrderSubscribeMessage.objects.filter(create_date__lte=clean_day).delete()
    return json.dumps({"day": f"{clean_day}", "deleted": deleted, "rows": rows})


@app.task(queue="common_tasks")
def insert_history_by_ex_order_id(ex_order_id: str):
    try:
        orders = OriginalOrder.objects.filter(ex_order_id=ex_order_id)[:1]
        if not orders:
            return f"{ex_order_id}没有订单"

        order = orders[0]
        if not order.sku:
            return f"{ex_order_id}没有关联SKU信息"
        # distributor
        distributor_qs = Distributor.objects.all().values("live_author__author_id", "letters", "distributor_id", "name", "id")
        dist_author_dict = {i["live_author__author_id"]: i for i in distributor_qs}
        dist_letters_dict = {i["letters"]: i for i in distributor_qs}
        author_id = dist_author_dict.get(order.author_id or "", {}).get("live_author__author_id") or dist_letters_dict.get(order.letters or "", {}).get("live_author__author_id")
        sku = order.sku
        create_date = order.order_time.strftime(DATETIME_FORMAT)
        sku_id = sku.pk
        history_price_data = {
            "product_id": sku.product.id,
            "sku_id": sku_id,
            "history_price": order.goods_price,
            "author_id": author_id,
            "create_date": create_date,
        }
        history_price_data_dict = {}
        history_price_data_dict[str((sku_id, create_date, author_id))] = history_price_data
        if history_price_data_dict:
            bulk_create_history_prices.delay(order.data_source, history_price_data_dict, None)
    except Exception as e:
        logger.warning(f"单个插入历史价失败:{e}")


def fetch_old_orders(data_shop_pk: int, date_str, days=3):
    """
    快速补单任务调度
    :param data_shop_pk:
    :param date_str:
    :param days:
    :return:
    """
    try:
        data_shop = DataShop.objects.get(pk=data_shop_pk)
    except DataShop.DoesNotExist:
        return f"{data_shop_pk} cannot fetch. Days: {days}"

    start_date = datetime.strptime(date_str, DATE_FORMAT)
    end_date = start_date + timedelta(days=days)

    start_time = int(start_date.timestamp())
    end_time = int(end_date.timestamp())
    shop_id = data_shop.shop_id
    dy_order_fetcher_with_order_create_time.delay(data_shop_pk, shop_id, start_time, end_time, "2025-07-01 00:00:00")


@app.task(queue="common_tasks")
def cached_order_total_count():
    max_id_cache_key = "order_count:max_id"
    order_count_cache_key_prefix = OrderBasedOnSupplier.paginator_order_count_key_prefix

    with transaction.atomic():
        # 获取当前最大 ID
        last_obj = OrderBasedOnSupplier.objects.last()
        if not last_obj:
            return "no order"
        max_id = last_obj.pk

        # 获取缓存中的最大 ID
        cached_max_id = redis_conn.get(max_id_cache_key)

        # 缓存键
        total_count_key = order_count_cache_key_prefix + ":all"
        db_count_key_prefix = order_count_cache_key_prefix + ":db:"
        sp_count_key_prefix = order_count_cache_key_prefix + ":sp:"

        if cached_max_id is None:
            # 如果缓存为空，初始化缓存
            db_rets = OrderBasedOnSupplier.objects.values("distributor_id").annotate(count=Count("*")).values("distributor_id", "count")
            sp_rets = OrderBasedOnSupplier.objects.values("company_id").annotate(count=Count("*")).values("company_id", "count")

            total_count = 0

            with redis_conn.pipeline() as pipe:
                for db_ret in db_rets:
                    db_id = str(db_ret["distributor_id"])
                    db_count = db_ret["count"]
                    total_count += db_count or 0
                    pipe.set(db_count_key_prefix + db_id, db_count, None)

                for sp_ret in sp_rets:
                    sp_id = str(sp_ret["company_id"])
                    sp_count = sp_ret["count"]
                    pipe.set(sp_count_key_prefix + sp_id, sp_count, None)

                pipe.set(total_count_key, total_count, None)
                pipe.set(max_id_cache_key, max_id, None)
                pipe.execute()
            return "complete 1"

        if max_id == cached_max_id:
            # 如果最大 ID 没有变化，返回缓存结果
            return "no order"

        # 计算新增的订单数
        db_rets = OrderBasedOnSupplier.objects.filter(id__gt=cached_max_id, id__lte=max_id).values("distributor_id").annotate(count=Count("*")).values("distributor_id", "count")
        sp_rets = OrderBasedOnSupplier.objects.filter(id__gt=cached_max_id, id__lte=max_id).values("company_id").annotate(count=Count("*")).values("company_id", "count")

        total_count = int(redis_conn.get(total_count_key)) or 0

        with redis_conn.pipeline() as pipe:
            for db_ret in db_rets:
                db_id = str(db_ret["distributor_id"])
                db_count = db_ret["count"]
                total_count += db_count or 0

                db_cache_key = db_count_key_prefix + db_id
                pipe.incrby(db_cache_key, db_count)

            for sp_ret in sp_rets:
                sp_id = str(sp_ret["company_id"])
                sp_count = sp_ret["count"]

                sp_cache_key = sp_count_key_prefix + sp_id
                pipe.incrby(sp_cache_key, sp_count)

            pipe.set(total_count_key, total_count, None)
            pipe.set(max_id_cache_key, max_id, None)
            pipe.execute()

        return "complete 2"


@app.task(queue="common_tasks")
def send_as_order_notify(
    sub_as_order_id,
    img_url,
    code,
    product_id,
    product_name,
    spec_code,
    reason_text,
    problem_desc,
    num,
    amount,
    create_date,
):
    try:
        # 测试环境有配置
        request_url = getattr(settings, "AS_ORDER_NOTIFY_ROBOT_URL", "https://open.feishu.cn/open-apis/bot/v2/hook/3ac0a76e-06fe-448a-96e0-f7c3b5405db8")
        feishu_client = FeiShuDocx()
        resp = feishu_client.send_afters_sales_order(
            sub_as_order_id,
            img_url,
            code,
            product_id,
            product_name,
            spec_code,
            reason_text,
            problem_desc,
            num,
            amount,
            create_date,
            request_url,
        )
        logger.warning(f"发送售后订单提醒响应:{resp.json()}")
    except Exception as e:
        logger.warning(f"发送售后订单错误, {e}")
