import random
import time
from datetime import datetime

from django.db import IntegrityError
from django.utils.timezone import now
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from orders.new_models import InvoiceApplication


class ApplyInvoiceSerializer(serializers.ModelSerializer):
    """
    发票申请序列化器
    """

    class Meta:
        model = InvoiceApplication
        fields = [
            "id",
            "application_id",
            "invoice_order_map",
            "title_type",
            "invoice_title",
            "invoice_content",
            "tax_number",
            "recipient_phone",
            "recipient_email",
            "registration_address",
            "registration_phone",
            "bank_name",
            "bank_account",
            "invoice_date",
            "invoice_amount",
            "invoice_type",
            "status",
        ]
        read_only_fields = ("application_id",)

    def validate_recipient_phone(self, value):
        if not (value and value.isdigit() and len(value) == 11):
            raise ValidationError("请输入正确的11位手机号码。")
        return value

    def validate(self, data):
        title_type = data.get("title_type")
        invoice_title = data.get("invoice_title")
        tax_number = data.get("tax_number")
        # todo 对于已经开票的订单要验证
        if title_type == "personal":
            if not invoice_title:
                raise ValidationError({"invoice_title": "个人抬头不能为空，请填写您的姓名。"})

            self._remove_company_fields(data)

        elif title_type == "company":
            if not invoice_title:
                raise ValidationError({"invoice_title": "企业抬头不能为空。"})
            if not tax_number:
                raise ValidationError({"tax_number": "企业抬头的纳税人识别号不能为空。"})

        return data

    def create(self, validated_data):
        if validated_data.get("status") == "ISSUED":
            validated_data["invoice_date"] = now()
        # 动态生成申请编号
        if not validated_data.get("application_id", ""):
            application_id = self.generate_application_id()
            validated_data["application_id"] = application_id
        return super().create(validated_data)

    @staticmethod
    def generate_application_id():
        """
        生成申请编号规则：
        FP + 当前年后两位 + 月日 + 4位随机数 + 时间戳后4位
        """
        current_date = now().strftime("%y%m%d")  # 当前日期：格式为 年月日
        random_number = random.randint(1000, 9999)  # 4位随机数
        timestamp_suffix = str(int(time.time()))[-4:]  # 时间戳后4位
        return f"FP{current_date}{random_number}{timestamp_suffix}"

    def _remove_company_fields(self, data):
        """
        移除不需要的企业相关字段
        """
        company_fields = [
            "tax_number",
            "registration_address",
            "registration_phone",
            "bank_name",
            "bank_account",
        ]
        for field in company_fields:
            data.pop(field, None)
