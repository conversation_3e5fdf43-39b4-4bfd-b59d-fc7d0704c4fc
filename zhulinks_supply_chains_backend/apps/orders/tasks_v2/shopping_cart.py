# -*- coding: utf-8 -*-
from common.models import get_feishu_table_config_info
from common.utils import convert_2_aware_time
from companies.models import Distributor
from orders import logger
from orders.models import ShoppingCart, ShoppingCartRecordMap
from products.models import SubProduct
from products.models_v2.inventory import DistributorWarehouseInventory
from users.models import User
from utils.feishu import FeiShuDocx
from zhulinks_supply_chains_backend.celery import app


@app.task(queue="common_tasks")
def create_or_update_feishu_shopping_car_record_task(shopping_car_pk):
    # 采购的通知
    try:
        shopping_car = ShoppingCart.objects.get(pk=shopping_car_pk, distributor__distributor_mode=2)
        user = User.objects.get(pk=shopping_car.user_id)
    except ShoppingCart.DoesNotExist:
        return f"Not found pk {shopping_car_pk}"
    except User.DoesNotExist:
        return f"Not found with shopping car id {shopping_car_pk}"

    table_id, table_token = get_feishu_table_config_info("shopping_car")
    if not table_id or not table_token:
        table_token = "WkWHbnuxwaYG7nsJms3cr1oXnfK"
        table_id = "tbljyhajt6vNdvYE"

    sku = shopping_car.sku
    product = sku.product
    db = shopping_car.distributor

    default_db, _ = Distributor.objects.get_distribute_object()
    try:
        sub_product = SubProduct.objects.get(parent_product=product, owner=default_db, is_deleted=False)
    except SubProduct.DoesNotExist:
        return "Not found sub product"

    sub_products_map = dict(SubProduct.objects.filter(parent_product=product).exclude(owner=default_db).values_list("owner_id", "code"))
    inventories = DistributorWarehouseInventory.objects.filter(sku=sku).exclude(distributor=default_db)

    link_inventory_list = []
    for inventory in inventories:
        if inventory.distributor_id in sub_products_map:
            _code = sub_products_map[inventory.distributor_id]
            wi_value = inventory.warehouse_inventory

            link_inventory_list.append(f"{_code}: {wi_value}")

    try:
        record_id = shopping_car.shoppingcartrecordmap.record_id
    except ShoppingCartRecordMap.DoesNotExist:
        record_id = None
    feishu_client = FeiShuDocx()
    spec_value = "默认规格"
    if sku.specs:
        spec_value = ";".join([x["value"] for x in sku.specs])

    if not record_id:
        resp = feishu_client.upload_image(product.main_images[0], table_token)
        if not resp:
            img_val = ""
        else:
            img_val = [{"file_token": resp["file_token"]}]

        post_data = {
            "规格ID": f"{sku.sku_id}",
            "商编": f"{sku.spec_code}FX",
            "规格值": spec_value,
            "商品ID": f"{product.product_id}",
            "商品图片": img_val,
            "类型": shopping_car.get_data_source_display(),
            "采购数量": shopping_car.number,
            "最后更新时间": convert_2_aware_time(shopping_car.update_date),
            "手机号": f"{user.mobile}",
            "真实姓名": f"{user.real_name}({db.name})",
            "预购用户ID": f"{user.user_id}",
            "仓库总库存": sku.warehouse_inventory,
            "珠凌分销货号": f"{sub_product.code}",
            "关联其他主播货号": "、".join(sub_products_map.values()) or "",
            "不同主播货号仓库库存": "\n".join(link_inventory_list),
        }
        resp, success = feishu_client.insert_record_to_table(table_token, table_id, post_data)
        if success:
            # 创建关联关系
            record_map = ShoppingCartRecordMap.objects.create(shopping_car=shopping_car, record_id=resp["data"]["record"]["record_id"])
            logger.info(f"Created: {resp}, map_id: {record_map.pk}")
        else:
            logger.info(f"Failed Created: {resp}")
    else:
        post_data = {
            "类型": shopping_car.get_data_source_display(),
            "采购数量": shopping_car.number,
            "最后更新时间": convert_2_aware_time(shopping_car.update_date),
            "手机号": f"{user.mobile}",
            "真实姓名": f"{user.real_name}({db.name})",
            "预购用户ID": f"{user.user_id}",
            "仓库总库存": sku.warehouse_inventory,
            "珠凌分销货号": f"{sub_product.code}",
            "关联其他主播货号": "、".join(sub_products_map.values()) or "",
            "不同主播货号仓库库存": "\n".join(link_inventory_list),
        }
        resp = feishu_client.update_table_record(table_token, table_id, record_id, post_data)
        logger.info(f"updated: {resp}")


@app.task(queue="common_tasks")
def delete_shopping_car_record_task(record_id):
    table_id, table_token = get_feishu_table_config_info("shopping_car")
    if not table_id or not table_token:
        table_token = "WkWHbnuxwaYG7nsJms3cr1oXnfK"
        table_id = "tbljyhajt6vNdvYE"
    feishu_client = FeiShuDocx()
    print(feishu_client.delete_table_record(table_token, table_id, record_id))
