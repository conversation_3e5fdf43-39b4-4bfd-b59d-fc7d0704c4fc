# -*- coding: utf-8 -*-
from django.db import models

from companies.models import Distributor
from products.models import StockKeepingUnit


class ShoppingCart(models.Model):
    # 用户的主键, id值
    user_id = models.BigIntegerField(
        verbose_name="用户id",
        blank=False,
        null=False,
        default="",
    )
    sku = models.ForeignKey(
        StockKeepingUnit,
        on_delete=models.SET_NULL,
        to_field="sku_id",
        blank=True,
        null=True,
        default=None,
        verbose_name="商品sku",
    )
    number = models.IntegerField(
        verbose_name="数量",
        blank=False,
        null=False,
        default=1,
    )
    distributor = models.ForeignKey(
        Distributor,
        on_delete=models.CASCADE,
        to_field="distributor_id",
        blank=True,
        null=True,
        default=None,
        verbose_name="所属分销商",
    )
    data_source = models.PositiveSmallIntegerField(
        "数据来源",
        choices=(
            (1, "一件代发"),
            (2, "批发"),
        ),
        default=1,
    )
    # V2.9.7增加一个加入购物车时候sku.retail_price,用于判断购物车中的商品价格是否发生变化
    insert_retail_price = models.DecimalField("加入购物车时售价", max_digits=10, decimal_places=2, blank=True, null=True)

    create_date = models.DateTimeField("创建时间", auto_now_add=True)
    update_date = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        db_table = "shopping_cart"
        verbose_name = "购物车"
        verbose_name_plural = verbose_name


class ShoppingCartRecordMap(models.Model):
    shopping_car = models.OneToOneField(ShoppingCart, on_delete=models.CASCADE, db_constraint=False, verbose_name="关联购物车记录")
    record_id = models.CharField("飞书记录ID", max_length=128)

    class Meta:
        verbose_name = "购物车多维表格映射"
        verbose_name_plural = verbose_name
