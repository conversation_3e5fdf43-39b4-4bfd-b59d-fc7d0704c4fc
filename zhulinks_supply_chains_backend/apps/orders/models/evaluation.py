# -*- coding: utf-8 -*-
from django.contrib.postgres.fields import ArrayField
from django.db import models

from orders.models.orders import OriginalOrder
from products.models import Product
from utils.common import get_random


class OrderEvaluation(models.Model):
    """
    订单评价
    """

    evaluation_id = models.BigIntegerField("评价id", unique=True, blank=False, null=False, default=get_random)
    images = ArrayField(models.CharField("评价图片", max_length=300, default=None), size=10, blank=False, null=False, verbose_name="评价图片列表")
    video = ArrayField(models.CharField("评价视频", max_length=300, blank=True, null=True), size=10, blank=False, null=False, verbose_name="评价视频列表")
    content = models.TextField("分享内容", blank=True, null=True)
    scoring = models.IntegerField("评分2.4.6.8.10分", blank=True, null=True)
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        to_field="product_id",
        db_constraint=False,
        verbose_name="所属商品",
    )
    original_order = models.ForeignKey(OriginalOrder, on_delete=models.CASCADE, to_field="order_id", db_constraint=False, null=True, blank=True)
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        verbose_name = "商品评价"
        verbose_name_plural = verbose_name
        ordering = ("-create_date", "id")

    def __str__(self):
        return f"{self.evaluation_id}({self.content})"
