# -*- coding: utf-8 -*-
from dataclasses import dataclass
from typing import Any

from django.apps import apps
from django.core.cache import cache

Distributor = apps.get_model("companies", "Distributor")


@dataclass
class DistributorInfo:
    distributor_id: int
    distributor_name: str


def get_distributor_info_by_letters(letters: str) -> DistributorInfo:
    """
    根据代码获取分销商信息
    :param letters: 分销商代码
    :return:

    """
    if not letters:
        return DistributorInfo(distributor_id=0, distributor_name="")

    cache_key = f"distributor_info_{letters}"
    info = cache.get(cache_key)
    if info:
        return info

    try:
        distributor = Distributor.objects.get(letters=letters)
    except Distributor.DoesNotExist:
        return DistributorInfo(distributor_id=0, distributor_name="")
    info = DistributorInfo(distributor_id=distributor.id, distributor_name=distributor.name)
    cache.set(cache_key, info, timeout=60 * 60)
    return info


@dataclass
class NewDistributorInfo:
    distributor_pk: int
    distributor_id: int
    distributor_name: str


def get_distributor_info_by_letters_v2(letters: str) -> NewDistributorInfo | Any:
    """
    根据代码获取分销商信息
    :param letters: 分销商代码
    :return:

    """
    if not letters:
        return NewDistributorInfo(distributor_pk=0, distributor_id=0, distributor_name="")

    cache_key = f"distributor_info_v2:{letters}"
    info = cache.get(cache_key)
    if info:
        return info

    try:
        distributor = Distributor.objects.get(letters=letters)
    except Distributor.DoesNotExist:
        return NewDistributorInfo(distributor_pk=0, distributor_id=0, distributor_name="")
    info = NewDistributorInfo(distributor_pk=distributor.pk, distributor_id=distributor.distributor_id, distributor_name=distributor.name)
    cache.set(cache_key, info, timeout=60 * 60)
    return info


@dataclass
class LiveAuthorDistributorInfo:
    distributor_pk: int
    live_author_id: str


def get_distributor_info_by_live_author_id(live_author_id: str) -> LiveAuthorDistributorInfo:
    if not live_author_id:
        return LiveAuthorDistributorInfo(distributor_pk=0, live_author_id="")

    cache_key = f"distributor_info_author:{live_author_id}"
    info = cache.get(cache_key)
    if info:
        return info

    newest_distributor = (
        Distributor.objects.values(
            "id",
            "live_author__real_author_id",
        )
        .filter(live_author__real_author_id=live_author_id)
        .order_by("id")
        .last()
    )
    if not newest_distributor:
        return LiveAuthorDistributorInfo(distributor_pk=0, live_author_id=live_author_id)

    info = LiveAuthorDistributorInfo(
        distributor_pk=newest_distributor["id"],
        live_author_id=newest_distributor["live_author__real_author_id"],
    )
    cache.set(cache_key, info, timeout=60 * 60)
    return info


@dataclass
class LiveAuthorRoomDistributorInfo:
    distributor_pk: int
    room_id: str


def get_distributor_info_by_room_id(room_id: str) -> LiveAuthorRoomDistributorInfo:
    if not room_id:
        return LiveAuthorRoomDistributorInfo(distributor_pk=0, room_id="")

    cache_key = f"distributor_info_author:{room_id}"
    info = cache.get(cache_key)
    if info:
        return info

    newest_distributor = (
        Distributor.objects.values(
            "id",
            "live_author__real_author_id",
        )
        .filter(live_author__live_room_ids__contains=room_id)
        .order_by("id")
        .last()
    )

    if not newest_distributor:
        return LiveAuthorDistributorInfo(distributor_pk=0, live_author_id=live_author_id)

    info = LiveAuthorDistributorInfo(
        distributor_pk=newest_distributor["id"],
        live_author_id=newest_distributor["live_author__real_author_id"],
    )
    cache.set(cache_key, info, timeout=60 * 60)
    return info
