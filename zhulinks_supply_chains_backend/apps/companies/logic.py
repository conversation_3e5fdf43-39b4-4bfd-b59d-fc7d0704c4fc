# -*- coding: utf-8 -*-
from companies.models import Distributor, Company
from utils.redis_lock import redis_conn
from companies.tasks import sync_supplier_to_JST


def get_db_name_with_letters(letters: str) -> str:
    """
    根据分销商代码获取名称
    :param letters:
    :return:
    """
    cache_set_key = "db_name_set"

    data = redis_conn.hget(cache_set_key, letters)

    if data:
        return data.decode()

    try:
        distributor = Distributor.objects.get(letters=letters)
        redis_conn.hset(cache_set_key, letters, distributor.name)
        return distributor.name
    except Distributor.DoesNotExist:
        return ""


def get_combine_product_default_company(need_sync_to_jst: bool = False) -> Company:
    company, created = Company.objects.update_or_create(
        name="系统组合商品",
        short_name="珠凌组合商品默认供应商",
        defaults={
            "state": 1,
            "create_user": "system",
        },
    )

    if created or need_sync_to_jst:
        sync_supplier_to_JST.delay(company.company_id)

    return company
