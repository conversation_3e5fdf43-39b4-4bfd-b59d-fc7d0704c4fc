# -*- coding: utf-8 -*-
import copy

from common.basic import DBAPIView
from common.basics.views import set_log_params, OperateLogAPIViewMixin
from rest_framework.request import Request

from companies import logger
from companies.models import Distributor
from companies.serializers import DistributorSettingSer
from utils.http_handle import IResponse


class DBProductInfoSyncSetupView(DBAPIView):
    """
    分销商设置信息同步(废弃,使用settings view)
    """

    def get(self, request: Request):
        re_data = {"product_info_sync": self.current_user.distributor.product_info_sync}
        return IResponse(data=re_data)

    def patch(self, request: Request):
        if getattr(self.current_user, "can_edit_product_info_sync", False) is not True:
            return IResponse(code=403, message="无权修改该配置项")

        product_info_sync = request.data.get("product_info_sync", None)
        if product_info_sync not in [True, False]:
            return IResponse(code=400, message="invalid params")

        distributor = self.current_user.distributor
        distributor.product_info_sync = product_info_sync
        distributor.update_user = self.current_user.user_id
        distributor.save()
        return IResponse()


class DBProductInfoSettingsView(DBAPIView):
    fronted_page = "我的商品"
    resource_name = "商品设置"
    need_format_resource_name = False

    def get(self, request: Request):
        distributor = self.current_user.distributor

        ser_data = DistributorSettingSer(instance=distributor, many=False).data
        ser_data.update(
            {
                "can_edit_product_info_sync": self.current_user.can_edit_product_info_sync,
            }
        )
        return IResponse(data=ser_data)

    def patch(self, request: Request):
        distributor = self.current_user.distributor

        # 旧的同步开关
        old_product_info_sync = distributor.product_info_sync
        new_product_info_sync = request.data.get("product_info_sync", None)
        if new_product_info_sync is not None and old_product_info_sync != new_product_info_sync and getattr(self.current_user, "can_edit_product_info_sync", False) is not True:
            return IResponse(code=403, message="无权修改该配置项")

        if new_product_info_sync is not None and new_product_info_sync not in [True, False]:
            return IResponse(code=400, message="invalid params")

        raw_distributor = copy.deepcopy(distributor)

        update_fields = ["dy_product_prefix", "dy_product_suffix", "after_sales_image", "rings_image"]
        for field in update_fields:
            if field in request.data:
                setattr(distributor, field, request.data.get(field))

        distributor = self.current_user.distributor

        if new_product_info_sync is not None:
            distributor.product_info_sync = new_product_info_sync

        distributor.update_user = self.current_user.user_id
        distributor.save()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=str(distributor.distributor_id),
                model=Distributor,
                describe=f"修改了商品设置信息, 分销商:{distributor}",
                raw_object=raw_distributor,
                new_object=distributor,
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse()
