# -*- coding: utf-8 -*-


from django.urls import path

from companies.views.common_views import (
    SupplierInfoView,
    SupplierInfoDetailView,
    BrandView,
    ShopView,
    BankView,
    get_supplier_enum,
    CompanyView,
    DistributorEnumView,
    distributor_letters_check_view,
)
from companies.views.db_company_views import DBProductInfoSyncSetupView, DBProductInfoSettingsView
from companies.views.op_company_views import (
    OPCompanyDetailView,
    OPCompanyReviewView,
    OPCompanyView,
    OPDistributorView,
    OPDistributorDetailView,
    OPDistributorReviewView,
    OPDistributorDataSourceEnumView,
    OPCompanySetSelfSupportView,
    OPDistributorSetSelfSupportView,
    SupplierVisibleDistributorsView,
    OPCompanyGradeView,
    OPDataShopsView,
    OPDataShopsUnbindView,
)
from companies.views.sp_company_views import SPCompanyInfoView

urlpatterns = [
    path("company", CompanyView.as_view(), name="company"),
    # 供应商列表（弃用）
    path("supplier", SupplierInfoView.as_view(), name="supplier"),
    # 弃用
    path("supplier/<int:supplier_id>", SupplierInfoDetailView.as_view(), name="supplier.datail"),
    # 供应商枚举、商品列表下拉筛选
    path("supplier_enum", get_supplier_enum, name="supplier_enum"),
    # 品牌枚举
    path("brand", BrandView.as_view(), name="brand"),
    #
    path("shop", ShopView.as_view(), name="shop"),
    path("bank", BankView.as_view(), name="bank"),
    # 分销商下拉列表
    path("distributor_enum", DistributorEnumView.as_view(), name="distributor_enum"),
    # PC端 供应商商家管理
    path("op_company", OPCompanyView.as_view(), name="op_company"),
    # 供应商设置是否自营
    path("op_company/set_self_support/<int:company_id>", OPCompanySetSelfSupportView.as_view(), name="op_company.set_self_support"),
    # 供应商详情
    path("op_company/<int:company_id>", OPCompanyDetailView.as_view(), name="op_company"),
    # 审核供应商
    path("op_company/review/<int:company_id>", OPCompanyReviewView.as_view(), name="op_company"),
    # PC端 分销商商家管理
    path("op_distributor", OPDistributorView.as_view(), name="op_distributor"),
    # 分销商设置是否自营
    path("op_distributor/set_self_support/<int:distributor_id>", OPDistributorSetSelfSupportView.as_view(), name="op_distributor.set_self_support"),
    # 分销商信息同步设置(废弃)
    path("product_info_sync", DBProductInfoSyncSetupView.as_view(), name="db_product_info_sync_set_up"),
    # 分销商信息设置
    path("db_product_settings", DBProductInfoSettingsView.as_view(), name="db_product_info_settings"),
    # 分销商来源枚举
    path("op_distributor/data_source_enum", OPDistributorDataSourceEnumView.as_view(), name="op_distributor.datasource_enum"),
    # 分销商详情
    path("op_distributor/<int:distributor_id>", OPDistributorDetailView.as_view(), name="op_distributor_detail"),
    # 分销商审核
    path("op_distributor/review/<int:distributor_id>", OPDistributorReviewView.as_view(), name="op_distributor_detail.review"),
    # 分销商代码查询
    path("op_distributor/letters_check/<str:letters>", distributor_letters_check_view, name="op_distributor.letters_check"),
    # 聚水潭数据同步
    # path("jst_supplier_sync", JSTCompanySyncView.as_view(), name="jst_supplier_sync"),
    # 分销商信息
    path("company_info", SPCompanyInfoView.as_view(), name="sp_company_info"),
    # 供应商设置指定分销商可见
    path("op_company/<int:company_id>/visible_distributors", SupplierVisibleDistributorsView.as_view(), name="supplier.db_visible"),
    # 供应商评分
    path("op_company/grade", OPCompanyGradeView.as_view(), name="company.grade"),
    # 店铺管理
    path("shops", OPDataShopsView.as_view(), name="company.shops"),
    # 店铺解绑
    path("shops/unbind", OPDataShopsUnbindView.as_view(), name="company.shops_unbind"),
]
