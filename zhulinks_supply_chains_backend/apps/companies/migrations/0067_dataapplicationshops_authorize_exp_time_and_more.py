# Generated by Django 5.0.8 on 2024-11-06 09:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0066_company_grade"),
    ]

    operations = [
        migrations.AddField(
            model_name="dataapplicationshops",
            name="authorize_exp_time",
            field=models.DateTimeField(
                blank=True, default=None, null=True, verbose_name="授权过期时间"
            ),
        ),
        migrations.AddField(
            model_name="dataapplicationshops",
            name="authorize_time",
            field=models.DateTimeField(
                blank=True, default=None, null=True, verbose_name="授权时间"
            ),
        ),
        migrations.AddField(
            model_name="dataapplicationshops",
            name="code_generate_time",
            field=models.DateTimeField(
                blank=True, default=None, null=True, verbose_name="最新生成code的时间"
            ),
        ),
        migrations.AddField(
            model_name="dataapplicationshops",
            name="err_msg",
            field=models.TextField(blank=True, null=True, verbose_name="授权失败信息"),
        ),
        migrations.AddField(
            model_name="dataapplicationshops",
            name="purchase_info",
            field=models.JSONField(
                blank=True, default=dict, null=True, verbose_name="订购信息"
            ),
        ),
        migrations.AddField(
            model_name="dataapplicationshops",
            name="status",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, "已授权"),
                    (2, "授权失败"),
                    (3, "授权过期"),
                    (4, "取消授权"),
                ],
                default=1,
                verbose_name="授权状态",
            ),
        ),
        migrations.AddField(
            model_name="datashop",
            name="data_source",
            field=models.CharField(
                choices=[("dy", "抖音"), ("xhs", "小红书"), ("ks", "快手")],
                default="dy",
                max_length=12,
                verbose_name="店铺来源",
            ),
        ),
    ]
