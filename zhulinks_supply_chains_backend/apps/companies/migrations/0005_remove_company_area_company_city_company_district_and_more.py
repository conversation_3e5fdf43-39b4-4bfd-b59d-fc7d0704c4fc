# Generated by Django 4.2.1 on 2023-05-29 16:54

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("companies", "0004_company_company_id_alter_company_state"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="company",
            name="area",
        ),
        migrations.AddField(
            model_name="company",
            name="city",
            field=models.CharField(
                blank=True, max_length=56, null=True, verbose_name="市"
            ),
        ),
        migrations.AddField(
            model_name="company",
            name="district",
            field=models.CharField(
                blank=True, max_length=56, null=True, verbose_name="区/县"
            ),
        ),
        migrations.AddField(
            model_name="company",
            name="province",
            field=models.CharField(
                blank=True, max_length=56, null=True, verbose_name="省"
            ),
        ),
    ]
