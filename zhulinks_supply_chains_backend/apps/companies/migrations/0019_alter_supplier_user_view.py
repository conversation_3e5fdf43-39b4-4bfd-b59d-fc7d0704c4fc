# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-07-14 11:50:13
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-12-11 18:34:43
# Generated by Django 4.2.1 on 2023-06-28 14:04

import django.contrib.postgres.fields
from django.db import migrations, models
import utils.common


class Migration(migrations.Migration):
    dependencies = [
        ("companies", "0018_alter_supplierinfo_options"),
    ]

    operations = [
        migrations.RunSQL(
            sql=[
                (
                    """CREATE OR REPLACE VIEW supplier_user_view
                    AS
                    SELECT 
                    distinct(a.company_id), 
                    a.name, 
                    a.credit_code, 
                    a.short_name, 
                    a.state, 
                    a.state_reason, 
                    a.province, 
                    a.city, 
                    a.district, 
                    a.address, 
                    a.main_products, 
                    a.qualification_images, 
                    a.bank_account_name, 
                    a.bank_card_number, 
                    a.bank_branch_name, 
                    a.create_date, 
                    a.update_date, 
                    b.id as user_raw_id, 
                    b.user_id, 
                    b.nickname, 
                    b.username, 
                    b.mobile,
                    row_number() OVER () as id 
                    FROM companies_company a 
                    LEFT JOIN users_user b 
                    ON 
                    a.id = b.company_id 
                    and a.role='SP' 
                    and b.is_active=true 
                    and b.is_deleted=false 
                    and b.user_type='SP'
                    and b.is_founder=true
                    ORDER BY b.id;"""
                )
            ],
            # reverse_sql=[("drop view supplier_user_view;")],
        )
    ]
