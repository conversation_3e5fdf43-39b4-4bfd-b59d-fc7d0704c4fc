# Generated by Django 4.2.7 on 2023-12-12 06:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("common", "0009_operationlog_real_name"),
        ("companies", "0030_alter_supplier_user_view_add_id"),
    ]

    operations = [
        migrations.AddField(
            model_name="distributor",
            name="live_author",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="common.liveauthor",
                verbose_name="绑定的主播",
            ),
        ),
        migrations.AddConstraint(
            model_name="distributor",
            constraint=models.UniqueConstraint(
                fields=("distributor_id", "live_author"),
                name="distributor_live_author_uni",
            ),
        ),
    ]
