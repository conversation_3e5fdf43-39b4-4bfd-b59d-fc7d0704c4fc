# Generated by Django 4.2.11 on 2024-05-16 05:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0040_alter_distributor_distributor_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="DataApplication",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "platform",
                    models.CharField(
                        choices=[("dy", "抖音"), ("jst", "聚水潭")],
                        max_length=8,
                        verbose_name="应用平台",
                    ),
                ),
                ("name", models.CharField(max_length=32, verbose_name="应用名称")),
                (
                    "desc",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="应用描述"
                    ),
                ),
                (
                    "app_key",
                    models.Char<PERSON>ield(
                        blank=True, max_length=255, null=True, verbose_name="应用ID"
                    ),
                ),
                (
                    "app_secret",
                    models.Char<PERSON>ield(
                        blank=True, max_length=255, null=True, verbose_name="应用秘钥"
                    ),
                ),
                ("enable", models.<PERSON>oleanField(default=True, verbose_name="是否启用")),
                (
                    "remark",
                    models.TextField(
                        blank=True, default="", null=True, verbose_name="备注信息"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True, default="", null=True, verbose_name="创建者"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "数据应用",
                "verbose_name_plural": "数据应用",
            },
        ),
        migrations.CreateModel(
            name="DataShop",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=32, verbose_name="店铺名称")),
                (
                    "shop_id",
                    models.CharField(max_length=32, verbose_name="平台店铺shop_id"),
                ),
                (
                    "auth_code",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="授权code"
                    ),
                ),
                (
                    "co_id",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="公司编号"
                    ),
                ),
                (
                    "shop_site",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="店铺站点"
                    ),
                ),
                (
                    "shop_url",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="店铺网址"
                    ),
                ),
                (
                    "created",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="平台店铺创建时间",
                    ),
                ),
                (
                    "nick",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="主账号"
                    ),
                ),
                (
                    "short_name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="店铺简称"
                    ),
                ),
                ("enable", models.BooleanField(default=True, verbose_name="是否启用")),
                (
                    "remark",
                    models.TextField(blank=True, null=True, verbose_name="备注信息"),
                ),
                (
                    "application",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.dataapplication",
                        verbose_name="所属应用",
                    ),
                ),
            ],
            options={
                "verbose_name": "数据应用店铺",
                "verbose_name_plural": "数据应用店铺",
            },
        ),
    ]
