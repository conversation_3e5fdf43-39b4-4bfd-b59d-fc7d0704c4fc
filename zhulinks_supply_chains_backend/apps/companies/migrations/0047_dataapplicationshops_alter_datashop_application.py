# Generated by Django 5.0.6 on 2024-05-30 01:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("companies", "0046_datashop_authorized_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="DataApplicationShops",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "auth_code",
                    models.CharField(blank=True, max_length=255, null=True, verbose_name="授权code"),
                ),
                (
                    "is_authorized",
                    models.BooleanField(default=False, verbose_name="是否已授权"),
                ),
                (
                    "authorized_type",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "不需要授权"),
                            (1, "通过Code授权"),
                            (2, "通过ShopID授权"),
                            (3, "通过ShopID和Code授权"),
                        ],
                        default=0,
                        verbose_name="授权类型",
                    ),
                ),
                (
                    "remark",
                    models.TextField(blank=True, null=True, verbose_name="备注信息"),
                ),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.dataapplication",
                        verbose_name="所属应用",
                    ),
                ),
                (
                    "data_shop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.datashop",
                        verbose_name="商店",
                    ),
                ),
            ],
            options={
                "verbose_name": "应用店铺设置",
                "verbose_name_plural": "应用店铺设置",
            },
        ),
        migrations.RemoveField(
            model_name="datashop",
            name="application",
        ),
        migrations.AddField(
            model_name="datashop",
            name="application",
            field=models.ManyToManyField(
                through="companies.DataApplicationShops",
                to="companies.dataapplication",
                verbose_name="所属应用",
            ),
        ),
    ]
