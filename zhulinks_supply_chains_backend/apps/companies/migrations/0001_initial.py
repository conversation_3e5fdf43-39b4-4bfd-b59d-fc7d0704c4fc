# Generated by Django 4.2.1 on 2023-05-29 07:48

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Area",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=20, verbose_name="名称")),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="subs",
                        to="companies.area",
                        verbose_name="上级行政区划",
                    ),
                ),
            ],
            options={
                "verbose_name": "行政区划",
                "verbose_name_plural": "行政区划",
                "db_table": "tb_areas",
            },
        ),
        migrations.CreateModel(
            name="Company",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=56, verbose_name="公司名称")),
                (
                    "credit_code",
                    models.CharField(max_length=18, verbose_name="统一社会信用代码"),
                ),
                (
                    "short_name",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="公司简称"
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[("SP", "供应商"), ("OP", "运营商")],
                        default="SP",
                        max_length=2,
                        verbose_name="公司角色",
                    ),
                ),
                (
                    "state",
                    models.SmallIntegerField(
                        choices=[(0, "待审核"), (1, "审核通过"), (2, "审核不通过")],
                        default=0,
                        max_length=1,
                        verbose_name="公司状态",
                    ),
                ),
                (
                    "state_reason",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="状态描述"
                    ),
                ),
                ("address", models.CharField(max_length=128, verbose_name="公司详细地址")),
                (
                    "main_products",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="主营品类"
                    ),
                ),
                (
                    "qualification_images",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            blank=True, max_length=300, verbose_name="资质图片链接"
                        ),
                        size=6,
                    ),
                ),
                (
                    "band_account_name",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="银行账户名"
                    ),
                ),
                (
                    "bank_card_number",
                    models.CharField(
                        blank=True, max_length=19, null=True, verbose_name="银行账号"
                    ),
                ),
                (
                    "bank_branch_name",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="开户银行"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "area",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="companies.area",
                    ),
                ),
            ],
            options={
                "verbose_name": "公司列表",
                "verbose_name_plural": "公司列表",
            },
        ),
    ]
