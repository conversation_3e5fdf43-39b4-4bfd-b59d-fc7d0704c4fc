# Generated by Django 4.2.1 on 2023-06-28 14:04

import django.contrib.postgres.fields
from django.db import migrations, models
import utils.common


class Migration(migrations.Migration):
    dependencies = [
        ("companies", "0014_alter_company_company_id"),
    ]

    operations = [
        migrations.AlterField(
            model_name="company",
            name="company_id",
            field=models.BigIntegerField(
                default=utils.common.get_random, unique=True, verbose_name="公司id"
            ),
        ),
        migrations.AlterField(
            model_name="company",
            name="qualification_images",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    blank=True, max_length=300, null=True, verbose_name="资质图片链接"
                ),
                blank=True,
                null=True,
                size=6,
                verbose_name="资质图片列表",
            ),
        ),
    ]
