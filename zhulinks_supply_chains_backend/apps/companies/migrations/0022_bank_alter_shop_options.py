# Generated by Django 4.2.1 on 2023-08-09 07:14

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("companies", "0021_shop"),
    ]

    operations = [
        migrations.CreateModel(
            name="Bank",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "bank_account_name",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="银行账户名"
                    ),
                ),
                (
                    "bank_card_number",
                    models.CharField(
                        blank=True,
                        max_length=19,
                        null=True,
                        unique=True,
                        verbose_name="银行账号",
                    ),
                ),
                (
                    "bank_branch_name",
                    models.CharField(
                        blank=True, max_length=56, null=True, verbose_name="开户银行"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "银行",
                "verbose_name_plural": "银行",
            },
        ),
        migrations.AlterModelOptions(
            name="shop",
            options={"verbose_name": "店铺", "verbose_name_plural": "店铺"},
        ),
    ]
