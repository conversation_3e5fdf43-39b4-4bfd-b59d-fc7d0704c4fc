# -*- coding: utf-8 -*-
import django_filters

from common.basics.filtersets import IContainCharField
from companies.models import Company, Distributor, DataApplicationShops
from users import logger
from users.models import User
from common.basic import BaseDateFilterSet, DateRangeFilter
from common.basics.exceptions import ParseException
import json


class CompanyFilter(django_filters.FilterSet):
    name = django_filters.CharFilter(lookup_expr="icontains")
    code = django_filters.CharFilter(lookup_expr="iexact")
    mobile = django_filters.CharFilter(method="mobile_filter")
    username = django_filters.CharFilter(method="username_filter")
    create_date = django_filters.CharFilter(method="create_date_filter")

    class Meta:
        model = Company
        fields = ("name", "mobile", "username", "state", "create_date")

    @staticmethod
    def username_filter(queryset, name, value):
        company_id_list = User.objects.filter(
            username__icontains=value,
            is_founder=True,
            user_types__code="SP",
            is_deleted=False,
        ).values_list("company_id", flat=True)
        return queryset.filter(id__in=company_id_list)

    @staticmethod
    def mobile_filter(queryset, name, value):
        company_id_list = User.objects.filter(
            mobile__icontains=value,
            is_founder=True,
            user_types__code="SP",
            is_deleted=False,
        ).values_list("company_id", flat=True)
        return queryset.filter(id__in=company_id_list)

    @staticmethod
    def create_date_filter(queryset, name, value):
        try:
            return queryset.filter(create_date__range=json.loads(value))
        except Exception as e:
            logger.warning(f"日期解析错误: {e}, {value}")
            raise ParseException(err_message="日期格式错误")


class DistributorFilter(BaseDateFilterSet):
    name = django_filters.CharFilter(lookup_expr="icontains")
    letters = django_filters.CharFilter(lookup_expr="icontains")
    mobile = django_filters.CharFilter(method="mobile_filter")

    class Meta:
        model = Distributor
        fields = (
            "name",
            "mobile",
            "letters",
            "state",
            "distributor_mode",
            "create_date",
            "update_date",
        )

    @staticmethod
    def mobile_filter(queryset, name, value):
        distributor_id_list = list(
            User.objects.filter(
                mobile__icontains=value,
                is_distributor_manager=True,
                user_types__code="DB",
                is_deleted=False,
            ).values_list("distributor_id", flat=True)
        )
        if not distributor_id_list:
            return queryset.none()

        return queryset.filter(id__in=distributor_id_list)


class DataApplicationShopsFilter(BaseDateFilterSet):
    shop_name = IContainCharField(field_name="data_shop__name")
    shop_id = IContainCharField(field_name="data_shop__shop_id")
    app_id = django_filters.CharFilter(field_name="application__app_key")
    authorize_exp_time = DateRangeFilter()

    class Meta:
        model = DataApplicationShops
        fields = (
            "shop_name",
            "shop_id",
            "app_id",
            "status",
            "authorize_exp_time",
        )
