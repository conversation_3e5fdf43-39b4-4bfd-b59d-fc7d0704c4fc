# -*- coding: utf-8 -*-
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Count, Q, Sum

from order_server.models import (
    OrderItems,
    OrderItemsSubProductSnapshotRelate,
    OrderItemsSubProductSnapshot,
    OrderSupplierRelate,
    Order,
)
from zhulinks_supply_chains_backend.celery import app


@app.task(queue="common_tasks")
def replace_product_orders_tasks(oi_raw_sku_id: str, replace_data: dict, snap_info: dict):
    """
    替换商品 - 全部商品替换任务
    :param oi_raw_sku_id:
    :param replace_data:
    :param snap_info:
    :return:
    """
    items = OrderItems.objects.filter(raw_sku_id=oi_raw_sku_id).only("order_id", "relate_order_id", "ex_sub_order_id")

    paginator = Paginator(items, 1000)

    uid = snap_info.pop("u_id")
    snapshot, _ = OrderItemsSubProductSnapshot.objects.get_or_create(u_id=uid, defaults=snap_info)

    updated_counts = 0
    for page_num in paginator.page_range:
        page_object_list = paginator.page(page_num).object_list
        oi_order_id_list = [i.order_id for i in page_object_list]
        oi_ex_sub_order_id_list = [i.ex_sub_order_id for i in page_object_list]

        order_id_list = [i.relate_order_id for i in page_object_list]

        with transaction.atomic(using="orders"):
            # update items
            updated_counts += OrderItems.objects.filter(order_id__in=oi_order_id_list).update(**replace_data)
            # delete origin relates
            OrderItemsSubProductSnapshotRelate.objects.filter(ex_sub_order_id__in=oi_ex_sub_order_id_list).delete()
            # create snapshot relates
            # create relate
            OrderItemsSubProductSnapshotRelate.objects.bulk_create(
                [
                    OrderItemsSubProductSnapshotRelate(
                        relate_order_id=j.relate_order_id,
                        ex_sub_order_id=j.ex_sub_order_id,
                        sub_snapshot_id=uid,
                    )
                    for j in page_object_list
                ]
            )

            # add supplier relate
            OrderSupplierRelate.objects.bulk_create(
                [OrderSupplierRelate(order_id=order_id, company_id=replace_data["company_id"]) for order_id in order_id_list],
                ignore_conflicts=True,
            )
            # calculate sum cost price
            order_rets = (
                Order.objects.filter(order_id__in=order_id_list)
                .values("order_id")
                .annotate(
                    invalid_count=Count("orderitems", filter=Q(orderitems__product_id__gt=0)),
                    order_items_count=Count("orderitems"),
                    cost_price_amount=Sum("orderitems__cost_price_amount", filter=~Q(orderitems__product_id=0)),
                )
                .values("order_id", "invalid_count", "order_items_count", "cost_price_amount")
            )

            order_rets_map = {
                i["order_id"]: {
                    "invalid_count": i["invalid_count"],
                    "order_items_count": i["order_items_count"],
                    "cost_price_amount": i["cost_price_amount"],
                }
                for i in order_rets
            }

            need_update_orders_objs = []
            need_update_orders = Order.objects.filter(order_id__in=order_id_list).only("id", "order_id")

            for need_update_order in need_update_orders:
                if need_update_order.order_id in order_rets_map:
                    _ret = order_rets_map[need_update_order.order_id]
                    need_update_order.cost_price_amount = _ret["cost_price_amount"]

                    invalid_count = _ret["invalid_count"]
                    oi_count = _ret["order_items_count"]

                    product_match_state = 2
                    if invalid_count == 0:
                        product_match_state = 1
                    elif invalid_count == oi_count:
                        product_match_state = 3

                    need_update_order.product_match_state = product_match_state
                    need_update_orders_objs.append(need_update_order)

            if need_update_orders_objs:
                Order.objects.bulk_update(need_update_orders_objs, fields=["cost_price_amount", "product_match_state"])

    return f"Completed replace {oi_raw_sku_id}, updated {updated_counts}"
