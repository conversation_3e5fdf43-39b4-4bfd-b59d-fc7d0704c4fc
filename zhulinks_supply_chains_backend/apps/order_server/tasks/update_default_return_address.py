from django.db.models import Q

from order_server import logger
from order_server.models import DeliveryReturnAddress, AfterSales
from order_server.serializers import DeliveryReturnAddressSerializer
from zhulinks_supply_chains_backend.celery import app


@app.task(queue="common_tasks")
def update_after_sales_order_default_return_address(pk):
    logger.info(f"开始更新默认退货地址")
    # 获取默认退货地址

    # return_address_obj = DeliveryReturnAddress.objects.filter(is_default_return=True).first()
    return_address_obj = DeliveryReturnAddress.objects.filter(id=pk).first()
    if not return_address_obj:
        # 如果没有默认退货地址，则直接退出
        logger.warning("没有默认退货地址")
        return

    # 使用序列化器获取退货地址数据
    return_address_ser = DeliveryReturnAddressSerializer(instance=return_address_obj)
    return_address_data = return_address_ser.data

    # 提取退货地址相关信息
    after_sales_address = f"{return_address_data.get('address_info_display', '')} {return_address_data.get('detailed_address', '')}".strip()
    after_sales_name = return_address_data.get("contact_name", "")
    after_sales_tel = return_address_data.get("phone_number", "")

    # 如果地址信息不完整，记录日志并退出
    if not (after_sales_address and after_sales_name and after_sales_tel):
        logger.warning("默认退货地址信息不完整，无法更新售后订单。")
        return

    # 筛选需要更新的售后订单
    all_after_sales_obj = AfterSales.objects.filter(Q(as_status=6, refund_state=1) | Q(as_status=7, refund_state=1))

    # 批量更新售后订单的退货地址
    updated_count = all_after_sales_obj.update(
        return_address={
            "after_sales_address": after_sales_address,
            "after_sales_name": after_sales_name,
            "after_sales_tel": after_sales_tel,
        }
    )

    # 记录更新的订单数
    logger.info(f"成功更新了 {updated_count} 个售后订单的退货地址。")
