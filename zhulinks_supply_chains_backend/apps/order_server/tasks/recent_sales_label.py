from datetime import timedelta

from django.db import transaction
from django.db.models import OuterRef, Subquery
from django.utils import timezone

from products.models import ProductLabels, ProductLabelsRelate, ProductSalesCount
from zhulinks_supply_chains_backend.celery import app

labels_name_list = ["近7天卖过", "近1个月卖过", "近3个月卖过", "近6个月卖过"]

# 每批次处理的数据量
BATCH_SIZE = 1000


@app.task(queue="common_tasks")
def calculate_recent_sales_labels():
    # 获取当前日期
    today = timezone.now().date()

    # 计算180天前的日期
    sixty_days_ago = today - timedelta(days=180)

    # 查询符合条件的标签，并缓存到字典中
    product_labels_all = ProductLabels.objects.filter(name__in=labels_name_list)
    label_name_to_obj = {label.name: label for label in product_labels_all}

    # 查询商品每日销量统计每个商品最后一次出现的数据
    # 子查询：获取每个商品最后一次出现的 calc_date
    latest_sales_subquery = ProductSalesCount.objects.filter(calc_date__gte=sixty_days_ago, product=OuterRef("product")).order_by("-calc_date").values("calc_date")[:1]

    # 主查询：根据子查询结果获取每个商品最后一次出现的记录
    latest_sales = ProductSalesCount.objects.filter(calc_date=Subquery(latest_sales_subquery))

    # 分批次处理
    for offset in range(0, latest_sales.count(), BATCH_SIZE):
        # 获取当前批次的数据
        batch_sales = latest_sales[offset : offset + BATCH_SIZE]

        # 获取当前批次的所有相关 ProductLabelsRelate 对象
        product_ids = [sales.product.product_id for sales in batch_sales]
        existing_relates = ProductLabelsRelate.objects.filter(product_id__in=product_ids, label__in=product_labels_all)
        existing_relates_dict = {(relate.product_id, relate.label_id): relate for relate in existing_relates}
        # 用于批量更新的数据
        to_update = []
        to_create = []

        # 处理当前批次的每个商品
        for sales in batch_sales:
            product = sales.product
            last_sale_date = sales.calc_date

            # 计算日期差
            delta = today - last_sale_date

            # 判断标签
            if delta <= timedelta(days=7):
                label_name = "近7天卖过"
            elif delta <= timedelta(days=30):
                label_name = "近1个月卖过"
            elif delta <= timedelta(days=90):
                label_name = "近3个月卖过"
            elif delta <= timedelta(days=180):
                label_name = "近6个月卖过"
            else:
                continue  # 如果超过6个月，跳过

            # 获取对应的标签对象
            label = label_name_to_obj.get(label_name)
            if not label:
                continue  # 如果标签不存在，跳过

            # 检查是否已存在关联记录
            key = (product.product_id, label.id)
            existing_relate = existing_relates_dict.get(key)
            if existing_relate:
                # 如果存在记录
                if existing_relate.become_history:
                    # 如果是历史数据，则更新为非历史数据
                    existing_relate.become_history = False
                    to_update.append(existing_relate)
            else:
                # 如果不存在记录，创建新记录
                to_create.append(ProductLabelsRelate(product=product, label=label, become_history=False, label_date=today))

        # 使用事务确保数据一致性
        with transaction.atomic():

            # 批量更新
            if to_update:
                ProductLabelsRelate.objects.bulk_update(to_update, fields=["become_history"])
            # 批量创建
            if to_create:
                ProductLabelsRelate.objects.bulk_create(to_create)
