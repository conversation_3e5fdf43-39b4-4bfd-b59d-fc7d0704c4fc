# -*- coding: utf-8 -*-
from django.db import models


class FetchConfigs(models.Model):
    cnf_source = models.CharField(
        "配置来源",
        choices=(
            ("dy", "抖音"),
            ("xhs", "小红书"),
            ("ks", "快手"),
        ),
        default="dy",
    )
    cnf_type = models.CharField(
        "配置类型",
        choices=(
            ("order", "订单"),
            ("as_order", "售后"),
        ),
        default="order",
    )

    shop_id = models.CharField("店铺ID", max_length=32)
    start_id = models.BigIntegerField("开始ID", default=1)

    class Meta:
        db_table = "orders_fetchconfigs"
        verbose_name = "拉取数据配置"
        verbose_name_plural = verbose_name


class ConfigDict(models.Model):
    code = models.CharField("配置标识", unique=True, default=64)
    value = models.CharField("配置内容", null=True, blank=True, default="")
    extra = models.JSONField("配置额外信息", null=True, blank=True, default=dict)
    remark = models.TextField("备注信息")

    class Meta:
        db_table = "orders_configdict"
        verbose_name = "配置设置"
        verbose_name_plural = verbose_name
        abstract = True
