from django.core.validators import MinLengthValidator, MaxLengthValidator, RegexValidator
from django.db import models


class DeliveryReturnAddress(models.Model):
    contact_name = models.CharField(max_length=10, validators=[MinLengthValidator(1), MaxLengthValidator(10)], verbose_name="联系人", help_text="请输入联系人（不超过10个字符）")
    phone_number = models.CharField(
        max_length=11,
        validators=[MinLengthValidator(11), MaxLengthValidator(11), RegexValidator(regex=r"^1[3-9]\d{9}$", message="请输入有效的手机号")],
        verbose_name="手机号",
        help_text="请输入手机号（11位数字）",
    )

    address_info = models.JSONField(verbose_name="地址信息", help_text="请选择省、市、区、街道/镇")
    detailed_address = models.Char<PERSON>ield(max_length=255, verbose_name="详细地址", help_text="请输入详细地址")

    is_default_shipping = models.BooleanField(default=False, verbose_name="发货默认地址")
    is_default_return = models.BooleanField(default=False, verbose_name="退货默认地址")

    create_date = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_date = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "发货地址"
        verbose_name_plural = "发货地址列表"
        ordering = ["-create_date"]

    def __str__(self):
        return f"{self.contact_name} - {self.phone_number} - {self.detailed_address}"

    def save(self, *args, **kwargs):
        # 确保只有一个发货默认地址
        if self.is_default_shipping:
            DeliveryReturnAddress.objects.filter(is_default_shipping=True).update(is_default_shipping=False)
        # 确保只有一个退货默认地址
        if self.is_default_return:
            DeliveryReturnAddress.objects.filter(is_default_return=True).update(is_default_return=False)
        super().save(*args, **kwargs)
