from django.db import models
from order_server.models import OrderItems


class LogisticsInformationModel(models.Model):
    SHIPPING_METHOD_CHOICES = [(1, "自行发货"), (2, "电子面单发货")]
    order_items = models.ForeignKey(
        OrderItems,
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name="order_items_logistics_information",
        verbose_name="所属订单",
    )
    shipping_method = models.PositiveSmallIntegerField(choices=SHIPPING_METHOD_CHOICES, default=1, verbose_name="发货方式")
    original_logistics = models.ForeignKey("self", null=True, blank=True, on_delete=models.SET_NULL, related_name="redelivery_records", verbose_name="原始发货记录")
    shipping_address = models.ForeignKey("DeliveryReturnAddress", null=True, blank=True, on_delete=models.SET_NULL, verbose_name="发货地址")
    shipping_quantity = models.IntegerField(blank=True, null=True, verbose_name="发货数量")
    is_redelivery = models.BooleanField(default=False, verbose_name="是否为重新发货")
    is_valid = models.BooleanField(default=True, verbose_name="是否有效")
    is_deleted = models.BooleanField(verbose_name="是否删除", default=False)

    logistics_information_data = models.JSONField(blank=True, null=True, verbose_name="物流轨迹")
    status = models.IntegerField(blank=True, null=True, verbose_name="物流状态")
    state = models.IntegerField(blank=True, null=True, verbose_name="快递状态")
    nu = models.CharField(blank=True, null=True, max_length=255, verbose_name="运单号")
    com = models.CharField(blank=True, null=True, max_length=255, verbose_name="快递公司编码")
    company_name = models.CharField(blank=True, null=True, max_length=255, verbose_name="快递公司名称")
    condition = models.CharField(blank=True, null=True, max_length=255, verbose_name="快递单明细状态标记")
    routeInfo = models.JSONField(blank=True, null=True, verbose_name="本数据元对应的目的地城市信息")
    delivery_time = models.DateTimeField("发货时间", blank=True, null=True)
    isLoop = models.BooleanField(default=False, verbose_name="isLoop")

    create_date = models.DateTimeField(auto_now_add=True, blank=True, null=True, verbose_name="创建时间")
    update_date = models.DateTimeField(auto_now=True, blank=True, null=True, verbose_name="更新时间")

    class Meta:
        db_table = "orders_logistics_information"
        verbose_name = "商品发货物流信息"
        verbose_name_plural = verbose_name

    def __str__(self):
        return f"订单 {self.order_items} 物流信息"


# YT1911987535600
# YT1909196817637
