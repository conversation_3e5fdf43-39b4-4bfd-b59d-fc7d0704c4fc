# -*- coding: utf-8 -*-
from django.db import models

from order_server.models import OrderItems
from utils.common import get_random


class OrderEvaluation(models.Model):
    """
    订单评价
    """

    evaluation_id = models.BigIntegerField("评价id", unique=True, blank=False, null=False, default=get_random)
    images = models.JSONField("评价图片列表", null=True, blank=True, default=list)
    video = models.JSONField("评价视频列表", null=True, blank=True, default=list)
    content = models.TextField("分享内容", blank=True, null=True)
    scoring = models.IntegerField("评分2.4.6.8.10分", blank=True, null=True)
    product_id = models.BigIntegerField("商品product_id", db_index=True)
    order_item = models.ForeignKey(OrderItems, on_delete=models.CASCADE, to_field="order_id", db_constraint=False, verbose_name="关联商品订单")
    is_deleted = models.Bo<PERSON>anField(verbose_name="是否删除", default=False)
    create_user = models.CharField(verbose_name="创建者", default="", null=True, blank=True)
    create_date = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        db_table = "orders_evaluation"
        verbose_name = "商品评价"
        verbose_name_plural = verbose_name
        ordering = (
            "-create_date",
            "id",
        )

    def __str__(self):
        return f"{self.evaluation_id}({self.content})"
