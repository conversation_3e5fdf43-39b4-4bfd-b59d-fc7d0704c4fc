# -*- coding: utf-8 -*-
from django.db import models


class ReplaceProductLog(models.Model):
    REPLACE_TYPE_CHOICES = (
        (1, "全订单替换"),
        (2, "当前订单替换"),
    )

    item_order_id = models.Char<PERSON>ield("子订单order_id", max_length=255)
    sku_id = models.CharField("规格ID", max_length=32)
    replace_type = models.PositiveSmallIntegerField("替换类型", choices=REPLACE_TYPE_CHOICES, default=2)
    create_user = models.<PERSON>r<PERSON>ield("替换人", max_length=64)
    create_time = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        db_table = "orders_replace_product_log"
        verbose_name = "替换商品记录"
        verbose_name_plural = verbose_name
