import django_filters

from order_server.models import DeliveryReturnAddress


class DeliveryReturnAddressFilter(django_filters.rest_framework.FilterSet):
    contact_name = django_filters.CharFilter(field_name="contact_name", lookup_expr="icontains")
    phone_number = django_filters.CharFilter(field_name="phone_number", lookup_expr="icontains")

    class Meta:
        model = DeliveryReturnAddress
        fields = ["contact_name", "phone_number", "is_default_shipping", "is_default_return"]
