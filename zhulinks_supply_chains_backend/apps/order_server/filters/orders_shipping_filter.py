import django_filters

from order_server.models import DeliveryReturnAddress, LogisticsInformationModel


class OrdersShippingFilter(django_filters.rest_framework.FilterSet):
    nu = django_filters.CharFilter(field_name="nu", lookup_expr="icontains")
    com = django_filters.CharFilter(field_name="com", lookup_expr="icontains")
    company_name = django_filters.CharFilter(field_name="company_name", lookup_expr="icontains")

    class Meta:
        model = LogisticsInformationModel
        fields = ["nu", "com", "company_name"]
