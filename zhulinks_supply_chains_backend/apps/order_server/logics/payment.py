# -*- coding: utf-8 -*-
import decimal
import json
import time

import httpx
from django.conf import settings
from django.core.cache import cache
from django.db import transaction
from django.utils import timezone

from order_server import logger
from order_server.logics.coupon import increase_coupon_data
from order_server.logics.notifications import send_success_order_pay_message, send_refund_success_message
from order_server.models import Order, AfterSales
from order_server.tasks import post_order_to_jst
from order_server.logics.aftersales import ZLAfterSalesRefundRespHandler
from order_server.tasks.feishu_notifications import send_order_create_notification_and_insert_table
from orders.messages import success_notification_send, refund_notification_send
from orders.models import OrderMap, OriginalOrder, wechat_trade_type_map, OrderBasedOnSupplier, AfterSalesRelatedOrderInfo
from orders.tasks import sync_new_order_to_jst
from users.models import User
from utils.wechatpyV3_utils import random_str, get_sign_v3, build_headers, validate_wechat_sign


class WeChatPayService:
    def __init__(
        self,
        trade_type: str,
        order_id: str,
        ex_order_id: str,
        app_id: str,
        description: str,
        total_fee: int,
        goods_details: dict,
        order_time: str,
        order_expire_time: str,
        open_id: str = None,
    ):
        self.trade_type = trade_type
        self.order_id = order_id
        self.ex_order_id = ex_order_id
        self.app_id = app_id
        self.description = description
        self.total_fee = total_fee
        self.goods_details = goods_details
        self.order_time = order_time
        self.order_expire_time = order_expire_time
        self.open_id = open_id

    def prepare_data(self) -> dict:
        """Prepare request data based on trade type."""
        data = {
            "mchid": settings.WECHATPAY_MCH_ID,
            "out_trade_no": self.ex_order_id,
            "appid": self.app_id,
            "description": self.description,
            "notify_url": settings.WECHATPAY_V3_NOTIFY_URL,
            "amount": {"total": self.total_fee, "currency": "CNY"},
            "detail": self.goods_details,
        }
        if self.trade_type == "JSAPI":
            data["payer"] = {"openid": self.open_id}
        return data

    def get_url_and_endpoint(self) -> tuple[str, str]:
        """Retrieve the URL and endpoint based on trade type."""
        url_mapping = {
            "JSAPI": (settings.WECHATPAY_JSAPI_URL, "/v3/pay/transactions/jsapi"),
            "NATIVE": (settings.WECHATPAY_NATIVE_URL, "/v3/pay/transactions/native"),
        }
        return url_mapping.get(self.trade_type)

    @staticmethod
    def send_request(url: str, endpoint: str, data: dict) -> tuple[httpx.Response, str, str]:
        """Send request to WeChat Pay API."""
        nonce_str = random_str(32)
        timestamp = str(int(time.time()))
        data_json_str = json.dumps(data)
        sign_str = f"POST\n{endpoint}\n{timestamp}\n{nonce_str}\n{data_json_str}\n"
        sign = get_sign_v3(sign_str)
        headers = build_headers(nonce_str, timestamp, sign)

        response = httpx.post(url, data=data_json_str, headers=headers)
        return response, nonce_str, timestamp

    @staticmethod
    def validate_response_signature(headers: dict, pay_response: dict) -> bool:
        """Validate WeChat Pay response signature."""
        serial = headers.get("wechatpay-serial")
        if serial != settings.WECHATPAY_VALIDATE_SIGN_SERIAL_NO:
            logger.warning(f"Invalid serial number: {serial}")
            return False

        return validate_wechat_sign(
            headers.get("wechatpay-timestamp"),
            headers.get("wechatpay-nonce"),
            pay_response,
            headers.get("wechatpay-signature"),
        )

    def process_response(self, response: httpx.Response, nonce_str: str, timestamp: str) -> dict | None:
        """Process API response based on trade type."""
        if response.status_code != 200:
            logger.warning(f"Request failed: {response.json()}")
            return

        resp_headers = response.headers
        pay_response = response.json()

        if not self.validate_response_signature(resp_headers, pay_response):
            logger.warning(f"Invalid signature: {pay_response}")
            return

        if self.trade_type == "JSAPI":
            return self.process_jsapi_response(pay_response, nonce_str, timestamp)
        elif self.trade_type == "NATIVE":
            return self.process_native_response(pay_response)
        return

    def process_jsapi_response(self, pay_response: dict, nonce_str: str, timestamp: str) -> dict | None:
        """Process JSAPI response."""
        prepay_id = pay_response.get("prepay_id")
        if not prepay_id:
            return

        sign_str = f"{self.app_id}\n{timestamp}\n{nonce_str}\nprepay_id={prepay_id}\n"
        pay_sign = get_sign_v3(sign_str)

        re_data = {
            "out_trade_no": self.ex_order_id,
            "pay_type": 1,
            "pay_type_desc": "微信支付",
            "total_amount": self.total_fee / 100,
            "prepay_id": prepay_id,
            "timestamp": timestamp,
            "nonce_str": nonce_str,
            "pay_sign": pay_sign,
            "order_time": self.order_time,
            "order_expire_time": self.order_expire_time,
            "order_id": self.order_id,
        }
        # 缓存1.5小时
        cache.set(f"wechat_pay_code:{self.ex_order_id}_{self.open_id}", re_data, 60 * 90)
        return re_data

    def process_native_response(self, pay_response: dict) -> dict | None:
        """Process NATIVE response."""
        code_url = pay_response.get("code_url")
        if not code_url:
            return

        re_data = {
            "out_trade_no": self.ex_order_id,
            "code_url": code_url,
            "pay_type": 1,
            "pay_type_desc": "微信支付",
            "total_amount": self.total_fee / 100,
            "order_time": self.order_time,
            "order_expire_time": self.order_expire_time,
            "order_id": self.order_id,
        }
        # 缓存1.5小时
        cache.set(f"wechat_pay_code:{self.ex_order_id}", re_data, 60 * 90)
        return re_data

    def process(self) -> dict | None:
        """Main entry point for processing the payment request."""
        if self.trade_type == "JSAPI":
            val = cache.get(f"wechat_pay_code:{self.ex_order_id}_{self.open_id}")
        else:
            val = cache.get(f"wechat_pay_code:{self.ex_order_id}")
        if val:
            return val

        url, endpoint = self.get_url_and_endpoint()
        data = self.prepare_data()
        response, nonce_str, timestamp = self.send_request(url, endpoint, data)
        return self.process_response(response, nonce_str, timestamp)


class WeChatCallBackService:
    def __init__(self, payment):
        self.payment = payment
        self.pay_amount = decimal.Decimal(payment["amount"]["total"] / 100)
        self.out_trade_no = payment["out_trade_no"]
        self.success_time = payment["success_time"]

    def _handle_old_distribute_order(self):
        """
        处理分销订单的支付回调
        :return:
        """
        # cache.set(f"wechatpay-{out_trade_no}", Order.StatusTypes.PAYMENT_SUCCESS.value, 60 * 60)
        #
        ex_order_id_list = OrderMap.objects.filter(virtual_order_id=self.out_trade_no).values_list("ex_order_id", flat=True)
        order = OriginalOrder.objects.filter(ex_order_id__in=ex_order_id_list).only("order_status").first()
        if not order:
            raise True

        if order.order_status != "WP":
            return True

        with transaction.atomic():
            OriginalOrder.objects.filter(ex_order_id__in=ex_order_id_list, order_status="WP").select_for_update().update(
                pay_time=self.success_time,
                order_status="RD",
                channel_payment_no=self.payment.get("transaction_id"),
                trade_type=wechat_trade_type_map.get(self.payment.get("trade_type"), 0) or 0,
            )
            OrderBasedOnSupplier.objects.filter(ex_order_id__in=ex_order_id_list).select_for_update().update(order_status="RD")

        # 同步到聚水潭
        for ex_order_id in ex_order_id_list:
            sync_new_order_to_jst.delay(ex_order_id)

        # 发送成功支付信息
        success_notification_send(order, self.pay_amount, self.out_trade_no, self.out_trade_no)
        return True

    def _handle_normal_order(self):
        # 先查询新订单流程
        new_order = Order.objects.filter(ex_order_id=self.out_trade_no).first()
        if new_order:
            order_status = "RD"
            order_status_desc = "待发货"
            main_status = 2
            main_status_desc = "待发货"

            new_order = Order.objects.get(ex_order_id=self.out_trade_no)
            if new_order.order_status == "WP":
                with transaction.atomic():
                    new_order.pay_time = self.success_time
                    new_order.order_status = order_status
                    new_order.order_status_desc = order_status_desc
                    new_order.main_status = main_status
                    new_order.main_status_desc = main_status_desc
                    new_order.channel_payment_no = self.payment.get("transaction_id")
                    new_order.save()
                    # 更新商品订单信息
                    new_order.orderitems_set.update(
                        pay_time=self.success_time,
                        order_status=order_status,
                        order_status_desc=order_status_desc,
                        main_status=main_status,
                        main_status_desc=main_status_desc,
                        channel_payment_no=self.payment.get("transaction_id"),
                        update_date=timezone.now(),
                    )

                    # 统计优惠券数据
                    increase_coupon_data(new_order)

                    # 同步订单到聚水潭
                    post_order_to_jst.delay(self.out_trade_no)
                    # 发送用户通知
                    send_success_order_pay_message(new_order)
                    # 发送飞书通知，插入表格
                    send_order_create_notification_and_insert_table.delay(new_order.order_id)

            return True

        # 走旧逻辑
        order = OriginalOrder.objects.filter(ex_order_id=self.out_trade_no).first()
        if not order:
            return True

        # 检查订单状态再确定后续动作，否则会出现订单执行两次的情况
        if order.order_status == "WP":
            with transaction.atomic():
                OriginalOrder.objects.filter(ex_order_id=self.out_trade_no, order_status="WP").select_for_update().update(
                    pay_time=self.success_time,
                    order_status="RD",
                    channel_payment_no=self.payment.get("transaction_id"),
                    trade_type=wechat_trade_type_map.get(self.payment.get("trade_type"), 0) or 0,
                )
                OrderBasedOnSupplier.objects.filter(ex_order_id=self.out_trade_no).select_for_update().update(
                    pay_amount=self.pay_amount,
                    order_status="RD",
                )
            # 同步到聚水潭
            sync_new_order_to_jst.delay(self.out_trade_no)
            # 发送成功支付信息
            success_notification_send(order, self.pay_amount, self.out_trade_no)
        return True

    def transaction_success(self):
        """
        支付成功的回调
        :return:
        """
        if str(self.out_trade_no).startswith("C"):
            return self._handle_old_distribute_order()
        return self._handle_normal_order()

    def refund_success(self):
        """
        退款成功
        :return:
        """
        new_as_order = AfterSales.objects.filter(as_order_id=self.out_trade_no).first()
        if new_as_order:
            new_as_order.refund_info = self.payment

            # 处理付款
            handler = ZLAfterSalesRefundRespHandler("SUCCESS")
            as_item = new_as_order.aftersalesitems_set.first()
            order_item = as_item.order_item
            handler.dispatch(new_as_order, order_item, new_as_order.as_type)

            as_order_item = new_as_order.aftersalesitems_set.first()
            order_item = as_order_item.order_item
            # 发送退款成功通知
            send_refund_success_message(
                new_as_order.as_order_id,
                as_order_item.sub_as_order_id,
                order_item.raw_product_id,
                new_as_order.create_user,
                new_as_order.distributor_id,
                new_as_order.amount,
            )
            return True

        sub_as_order = AfterSalesRelatedOrderInfo.objects.filter(as_order__ex_as_order_id=self.out_trade_no).first()
        now = timezone.now()
        if sub_as_order and sub_as_order.as_order_id and sub_as_order.as_order.refund_state != 3:
            with transaction.atomic():
                # 退款完成
                as_order = sub_as_order.as_order
                original_order = sub_as_order.original_order
                as_order.refund_state = 3
                as_order.state_text = "退款成功"
                as_order.as_status_text = "同意退款，退款成功"
                as_order.platform_update_time = now
                as_order.final_time = now
                as_order.save()
                # 售后成功, 退款成功3
                original_order.after_sale_status = 12
                original_order.refund_status = 3
                original_order.order_status = "CL"
                original_order.save()
        try:
            # 发送退款成功消息
            user_obj = User.objects.get(id=sub_as_order.create_user)
            refund_notification_send(sub_as_order, user_obj)
        except Exception as e:
            logger.error(f"发送售后退款消息错误{str(e)}")
        return True

    def refund_close(self):
        """
        退款关闭
        :return:
        """
        new_as_order = AfterSales.objects.filter(as_order_id=self.out_trade_no).first()
        if new_as_order:
            new_as_order.refund_info = self.payment

            # 处理付款
            handler = ZLAfterSalesRefundRespHandler("CLOSE")
            as_item = new_as_order.aftersalesitems_set.first()
            order_item = as_item.order_item
            handler.dispatch(new_as_order, order_item, new_as_order.as_type)
            return True

        sub_as_order = AfterSalesRelatedOrderInfo.objects.filter(as_order__ex_as_order_id=self.out_refund_no).first()

        now = timezone.now()
        if sub_as_order and sub_as_order.as_order_id and sub_as_order.as_order.refund_state != 3:
            with transaction.atomic():
                as_order = sub_as_order.as_order
                original_order = sub_as_order.original_order

                as_order.refund_state = 4
                as_order.state_text = "退款关闭"
                as_order.as_status_text = "同意申请,退款关闭"
                as_order.platform_update_time = now
                refund_info = as_order.refund_info
                if refund_info:
                    refund_info["callback_info"] = self.payment
                as_order.refund_info = refund_info
                as_order.save()
                # 售后成功, 退款成功3
                original_order.after_sale_status = 28
                original_order.refund_status = 4
                original_order.order_status = "CL"
                original_order.save()

        return True

    def refund_abnormal(self):
        """
        异常
        :return:
        """
        new_as_order = AfterSales.objects.filter(as_order_id=self.out_trade_no).first()
        if new_as_order:
            new_as_order.refund_info = self.payment

            # 处理付款
            handler = ZLAfterSalesRefundRespHandler("ABNORMAL")
            as_item = new_as_order.aftersalesitems_set.first()
            order_item = as_item.order_item
            handler.dispatch(new_as_order, order_item, new_as_order.as_type)
            return True

        sub_as_order = AfterSalesRelatedOrderInfo.objects.filter(as_order__ex_as_order_id=self.out_trade_no).first()
        now = timezone.now()
        if sub_as_order and sub_as_order.as_order_id and sub_as_order.as_order.refund_state != 3:
            with transaction.atomic():
                as_order = sub_as_order.as_order
                original_order = sub_as_order.original_order
                # 退款完成
                as_order.refund_state = 4
                as_order.state_text = "退款失败"
                as_order.as_status_text = "同意申请,退款异常"
                as_order.platform_update_time = now
                refund_info = as_order.refund_info
                if refund_info:
                    refund_info["callback_info"] = self.payment
                as_order.save()
                # 售后中, 退款失败
                original_order.after_sale_status = 12
                original_order.refund_status = 4
                original_order.order_status = "CL"
                original_order.save()
        return True

    def run(self, event_type):
        func_map = {
            "TRANSACTION.SUCCESS": self.transaction_success,
            "REFUND.SUCCESS": self.refund_success,
            "REFUND.CLOSED": self.refund_close,
            "REFUND.ABNORMAL": self.refund_abnormal,
        }

        if event_type not in func_map:
            logger.warning(f">>>微信回调: {event_type}")
            logger.warning(f">>>微信回调: {self.payment}")
            return

        return func_map[event_type]()
