# Generated by Django 5.0.8 on 2024-11-14 03:36

import django.db.models.deletion
import order_server.models.orders
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order_server", "0003_dyordersubscribemessage_s_ids_text"),
    ]

    operations = [
        migrations.AddField(
            model_name="dyordersubscribemessage",
            name="aftersale_id",
            field=models.CharField(
                blank=True,
                db_index=True,
                default=None,
                max_length=32,
                null=True,
                verbose_name="售后订单ID",
            ),
        ),
        migrations.CreateModel(
            name="Order",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "order_id",
                    models.CharField(
                        default=order_server.models.orders.generate_order_id,
                        editable=False,
                        max_length=64,
                        unique=True,
                        verbose_name="订单ID",
                    ),
                ),
                (
                    "ex_order_id",
                    models.Char<PERSON><PERSON>(
                        db_index=True,
                        default=0,
                        max_length=64,
                        verbose_name="外部订单号",
                    ),
                ),
                (
                    "order_status",
                    models.Char<PERSON>ield(
                        choices=[
                            ("WP", "待付款"),
                            ("RD", "待发货"),
                            ("DL", "待收货"),
                            ("FN", "已完成"),
                            ("CL", "已关闭"),
                        ],
                        db_index=True,
                        default="WP",
                        max_length=6,
                        verbose_name="状态",
                    ),
                ),
                (
                    "order_status_desc",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=16,
                        null=True,
                        verbose_name="订单状态",
                    ),
                ),
                (
                    "main_status",
                    models.PositiveSmallIntegerField(
                        default=0, verbose_name="主订单状态"
                    ),
                ),
                (
                    "main_status_desc",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=64,
                        null=True,
                        verbose_name="主订单状态描述",
                    ),
                ),
                (
                    "order_type",
                    models.IntegerField(
                        db_index=True, default=0, verbose_name="订单类型"
                    ),
                ),
                (
                    "order_platform",
                    models.CharField(
                        choices=[
                            ("DY", "抖音"),
                            ("KS", "快手"),
                            ("ZL", "珠凌"),
                            ("XHS", "小红书"),
                        ],
                        db_index=True,
                        default="DY",
                        max_length=6,
                        verbose_name="下单方式",
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        choices=[
                            ("JST", "聚水潭"),
                            ("DD", "抖店"),
                            ("XHS", "小红书"),
                            ("ZL", "珠凌"),
                            ("KS", "快手"),
                        ],
                        db_index=True,
                        default="DD",
                        max_length=3,
                        verbose_name="数据来源",
                    ),
                ),
                (
                    "biz",
                    models.PositiveSmallIntegerField(
                        default=2, verbose_name="业务来源"
                    ),
                ),
                (
                    "order_time",
                    models.DateTimeField(db_index=True, verbose_name="下单时间"),
                ),
                (
                    "pay_time",
                    models.DateTimeField(
                        blank=True,
                        db_index=True,
                        default=None,
                        null=True,
                        verbose_name="支付时间",
                    ),
                ),
                (
                    "finish_time",
                    models.DateTimeField(
                        blank=True,
                        db_index=True,
                        null=True,
                        verbose_name="订单完成时间",
                    ),
                ),
                (
                    "order_expire_time",
                    models.DateTimeField(
                        blank=True,
                        db_index=True,
                        null=True,
                        verbose_name="订单过期时间",
                    ),
                ),
                (
                    "appointment_ship_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="预约发货时间"
                    ),
                ),
                ("pay_type", models.IntegerField(default=1, verbose_name="支付类型")),
                (
                    "channel_payment_no",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="支付渠道的流水号",
                    ),
                ),
                (
                    "b_type",
                    models.IntegerField(
                        db_index=True, default=2, verbose_name="下单端"
                    ),
                ),
                (
                    "trade_type",
                    models.IntegerField(blank=True, null=True, verbose_name="交易类型"),
                ),
                (
                    "order_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="订单金额",
                    ),
                ),
                (
                    "pay_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="支付金额",
                    ),
                ),
                (
                    "paid_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="实付金额",
                    ),
                ),
                (
                    "cost_price_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="按成本算总金额",
                    ),
                ),
                (
                    "post_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="快递金额",
                    ),
                ),
                (
                    "post_origin_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="运费原价（单位：分）",
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="税费",
                    ),
                ),
                (
                    "shop_id",
                    models.CharField(
                        blank=True,
                        default="0",
                        max_length=64,
                        null=True,
                        verbose_name="店铺ID",
                    ),
                ),
                (
                    "distributor_id",
                    models.IntegerField(
                        db_index=True, default=0, verbose_name="分销商ID(主键)"
                    ),
                ),
                (
                    "cancel_reason",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="取消原因"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=32,
                        null=True,
                        verbose_name="创建用户",
                    ),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="更新用户"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="order_server.order",
                        to_field="order_id",
                        verbose_name="父订单",
                    ),
                ),
            ],
            options={
                "verbose_name": "订单表",
                "verbose_name_plural": "订单表",
                "db_table": "orders_order",
                "unique_together": {("ex_order_id", "order_type")},
            },
        ),
        migrations.CreateModel(
            name="OrderBuyerInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "buyer_words",
                    models.TextField(blank=True, null=True, verbose_name="买家留言"),
                ),
                (
                    "user_coordinate",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        max_length=255,
                        null=True,
                        verbose_name="买家收货地址经纬度信息",
                    ),
                ),
                (
                    "user_tag_ui",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        max_length=255,
                        null=True,
                        verbose_name="用户特征标签",
                    ),
                ),
                (
                    "open_id",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="用户ID",
                    ),
                ),
                (
                    "order",
                    models.OneToOneField(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="order_server.order",
                        to_field="order_id",
                        verbose_name="所属订单",
                    ),
                ),
            ],
            options={
                "verbose_name": "订单买家信息",
                "verbose_name_plural": "订单买家信息",
                "db_table": "orders_buyerinfo",
            },
        ),
        migrations.CreateModel(
            name="OrderOtherInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "serial_number_list",
                    models.JSONField(
                        blank=True,
                        default=list,
                        null=True,
                        verbose_name="商品序列号（IMEI码或SN码）",
                    ),
                ),
                (
                    "app_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="具体某个小程序的ID",
                    ),
                ),
                (
                    "shop_order_tag_ui",
                    models.JSONField(
                        blank=True,
                        default=list,
                        max_length=255,
                        null=True,
                        verbose_name="店铺单标签",
                    ),
                ),
                (
                    "address_tag_ui",
                    models.JSONField(
                        blank=True,
                        default=list,
                        max_length=255,
                        null=True,
                        verbose_name="地址标签列",
                    ),
                ),
                (
                    "order_tag",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        null=True,
                        verbose_name="订单的C端标签",
                    ),
                ),
                (
                    "aweme_id",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="达人抖音号"
                    ),
                ),
                (
                    "user_icon",
                    models.CharField(
                        blank=True,
                        max_length=300,
                        null=True,
                        verbose_name="达人抖音头像",
                    ),
                ),
                (
                    "user_nick_name",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="达人抖音昵称",
                    ),
                ),
                (
                    "order",
                    models.OneToOneField(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="order_server.order",
                        to_field="order_id",
                        verbose_name="所属订单",
                    ),
                ),
            ],
            options={
                "verbose_name": "订单额外信息",
                "verbose_name_plural": "订单额外信息",
                "db_table": "orders_otherinfo",
            },
        ),
        migrations.CreateModel(
            name="OrderPromotionInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "total_promotion_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="总优惠金额",
                    ),
                ),
                (
                    "post_promotion_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="运费优惠金额",
                    ),
                ),
                (
                    "author_cost_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="作者（达人）承担金额",
                    ),
                ),
                (
                    "only_platform_cost_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="仅平台承担金额",
                    ),
                ),
                (
                    "platform_cost_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="平台承担金额",
                    ),
                ),
                (
                    "post_insurance_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="运费险金额",
                    ),
                ),
                (
                    "promotion_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="订单优惠总金额",
                    ),
                ),
                (
                    "promotion_pay_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="支付优惠金额",
                    ),
                ),
                (
                    "promotion_platform_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="平台优惠金额",
                    ),
                ),
                (
                    "promotion_redpack_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="红包优惠金额",
                    ),
                ),
                (
                    "promotion_redpack_platform_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="平台红包优惠金额",
                    ),
                ),
                (
                    "promotion_redpack_talent_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="达人红包优惠金额",
                    ),
                ),
                (
                    "promotion_shop_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="店铺优惠金额",
                    ),
                ),
                (
                    "promotion_talent_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="达人优惠金额",
                    ),
                ),
                (
                    "order",
                    models.OneToOneField(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="order_server.order",
                        to_field="order_id",
                        verbose_name="所属订单",
                    ),
                ),
            ],
            options={
                "verbose_name": "主订单优惠信息",
                "verbose_name_plural": "主订单优惠信息",
                "db_table": "orders_promotioninfo",
            },
        ),
        migrations.CreateModel(
            name="OrderReceiverInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "receiver_name",
                    models.CharField(
                        blank=True, max_length=28, null=True, verbose_name="收件人姓名"
                    ),
                ),
                (
                    "receiver_phone",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="收件人手机"
                    ),
                ),
                (
                    "receiver_province_name",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="收件人省份名称",
                    ),
                ),
                (
                    "receiver_province_id",
                    models.CharField(
                        blank=True,
                        max_length=30,
                        null=True,
                        verbose_name="收件人省份ID",
                    ),
                ),
                (
                    "receiver_city_name",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="收件人城市名",
                    ),
                ),
                (
                    "receiver_city_id",
                    models.CharField(
                        blank=True,
                        max_length=30,
                        null=True,
                        verbose_name="收件人城市ID",
                    ),
                ),
                (
                    "receiver_town_name",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="收件人城镇名称",
                    ),
                ),
                (
                    "receiver_town_id",
                    models.CharField(
                        blank=True,
                        max_length=30,
                        null=True,
                        verbose_name="收件人城镇ID",
                    ),
                ),
                (
                    "receiver_street_name",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="收件人街道名称",
                    ),
                ),
                (
                    "receiver_street_id",
                    models.CharField(
                        blank=True,
                        max_length=30,
                        null=True,
                        verbose_name="收件人街道 ID",
                    ),
                ),
                (
                    "encrypt_detail",
                    models.TextField(
                        blank=True, null=True, verbose_name="加密的详细地址"
                    ),
                ),
                (
                    "detail",
                    models.TextField(blank=True, null=True, verbose_name="详细地址"),
                ),
                (
                    "order",
                    models.OneToOneField(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="order_server.order",
                        to_field="order_id",
                        verbose_name="所属订单",
                    ),
                ),
            ],
            options={
                "verbose_name": "主订单收信息",
                "verbose_name_plural": "主订单收信息",
                "db_table": "orders_receiverinfo",
            },
        ),
        migrations.CreateModel(
            name="OrderSellerInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "seller_remark_stars",
                    models.CharField(
                        blank=True, max_length=5, null=True, verbose_name="卖家订单标记"
                    ),
                ),
                (
                    "seller_words",
                    models.TextField(blank=True, null=True, verbose_name="商家备注"),
                ),
                (
                    "order",
                    models.OneToOneField(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="order_server.order",
                        to_field="order_id",
                        verbose_name="所属订单",
                    ),
                ),
            ],
            options={
                "verbose_name": "订单卖价信息表",
                "verbose_name_plural": "订单卖价信息表",
                "db_table": "orders_sellerinfo",
            },
        ),
        migrations.CreateModel(
            name="OrderItems",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "order_id",
                    models.CharField(
                        default=order_server.models.orders.generate_order_items_order_id,
                        editable=False,
                        max_length=64,
                        unique=True,
                        verbose_name="子订单ID",
                    ),
                ),
                (
                    "parent_ex_order_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=100,
                        null=True,
                        verbose_name="外部父订单号",
                    ),
                ),
                (
                    "ex_sub_order_id",
                    models.CharField(
                        db_index=True,
                        default=0,
                        max_length=64,
                        verbose_name="外部子订单号",
                    ),
                ),
                (
                    "raw_spec_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=255,
                        null=True,
                        verbose_name="原始商品编码",
                    ),
                ),
                (
                    "spec_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=255,
                        null=True,
                        verbose_name="解析后商品编码",
                    ),
                ),
                (
                    "letters",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default="0",
                        max_length=12,
                        null=True,
                        verbose_name="分销商代码",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default="",
                        max_length=20,
                        null=True,
                        verbose_name="货号",
                    ),
                ),
                (
                    "distributor_id",
                    models.IntegerField(
                        db_index=True, default=0, verbose_name="分销商ID(主键)"
                    ),
                ),
                (
                    "company_id",
                    models.IntegerField(
                        db_index=True, default=0, verbose_name="供应商ID(主键)"
                    ),
                ),
                (
                    "sku_id",
                    models.IntegerField(
                        blank=True,
                        db_index=True,
                        default=0,
                        null=True,
                        verbose_name="SKU主键",
                    ),
                ),
                (
                    "sku_version_id",
                    models.BigIntegerField(
                        blank=True,
                        db_index=True,
                        default=0,
                        null=True,
                        verbose_name="SKU快照版本ID",
                    ),
                ),
                (
                    "product_id",
                    models.IntegerField(
                        blank=True,
                        db_index=True,
                        default=0,
                        null=True,
                        verbose_name="商品主键",
                    ),
                ),
                (
                    "product_version_id",
                    models.BigIntegerField(
                        blank=True,
                        db_index=True,
                        default=0,
                        null=True,
                        verbose_name="商品快照版本ID",
                    ),
                ),
                (
                    "sub_product_id",
                    models.IntegerField(
                        blank=True,
                        db_index=True,
                        default=0,
                        null=True,
                        verbose_name="副本商品主键",
                    ),
                ),
                (
                    "sub_product_version_id",
                    models.BigIntegerField(
                        blank=True,
                        db_index=True,
                        default=0,
                        null=True,
                        verbose_name="副本商品快照版本ID",
                    ),
                ),
                (
                    "author_id",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="直播主播id（达人）",
                    ),
                ),
                (
                    "author_name",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="直播主播名称",
                    ),
                ),
                (
                    "goods_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="商品价格",
                    ),
                ),
                (
                    "goods_type",
                    models.SmallIntegerField(
                        blank=True,
                        choices=[(0, "实体"), (1, "虚拟")],
                        default=0,
                        null=True,
                        verbose_name="商品类型",
                    ),
                ),
                (
                    "item_num",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="订单商品数量"
                    ),
                ),
                (
                    "product_count",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="发货商品数量"
                    ),
                ),
                (
                    "order_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="子订单金额",
                    ),
                ),
                (
                    "pay_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="支付金额",
                    ),
                ),
                (
                    "paid_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="实付款",
                    ),
                ),
                (
                    "cost_price_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="按成本算总金额",
                    ),
                ),
                (
                    "post_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="快递费",
                    ),
                ),
                (
                    "exp_ship_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="预计发货时间"
                    ),
                ),
                (
                    "finish_time",
                    models.DateTimeField(
                        blank=True,
                        db_index=True,
                        null=True,
                        verbose_name="子订单完成时间",
                    ),
                ),
                (
                    "ship_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="发货时间"
                    ),
                ),
                (
                    "confirm_receipt_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="用户确认收货时间"
                    ),
                ),
                (
                    "order_status",
                    models.CharField(
                        choices=[
                            ("WP", "待付款"),
                            ("RD", "待发货"),
                            ("DL", "待收货"),
                            ("FN", "已完成"),
                            ("CL", "已关闭"),
                        ],
                        db_index=True,
                        default="WP",
                        max_length=2,
                        verbose_name="状态",
                    ),
                ),
                (
                    "order_status_desc",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=16,
                        null=True,
                        verbose_name="订单状态",
                    ),
                ),
                (
                    "raw_order_status",
                    models.CharField(
                        blank=True,
                        default="",
                        null=True,
                        verbose_name="第三方平台订单状态",
                    ),
                ),
                (
                    "from_mall",
                    models.BooleanField(
                        blank=True,
                        default=False,
                        null=True,
                        verbose_name="是否为商城数据",
                    ),
                ),
                (
                    "receiver_info",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="收件人信息"
                    ),
                ),
                (
                    "encrypt_post_receiver",
                    models.TextField(
                        blank=True, max_length=300, null=True, verbose_name="收件人姓名"
                    ),
                ),
                (
                    "encrypt_post_tel",
                    models.TextField(
                        blank=True, max_length=300, null=True, verbose_name="收件人电话"
                    ),
                ),
                (
                    "ad_env_type",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="广告来源"
                    ),
                ),
                (
                    "appointment_ship_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="预约发货时间"
                    ),
                ),
                (
                    "c_biz",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="C端流量来源"
                    ),
                ),
                (
                    "sub_b_type",
                    models.IntegerField(blank=True, null=True, verbose_name="下单场景"),
                ),
                (
                    "cancel_reason",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="取消原因"
                    ),
                ),
                (
                    "channel_payment_no",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="支付渠道的流水号",
                    ),
                ),
                (
                    "other_info",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="订单额外字段"
                    ),
                ),
                (
                    "category_id_info",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="类目信息"
                    ),
                ),
                (
                    "has_tax",
                    models.BooleanField(default=False, verbose_name="是否包税"),
                ),
                (
                    "is_comment",
                    models.SmallIntegerField(
                        blank=True,
                        choices=[(0, "未评价"), (1, "已评价"), (2, "表示追评")],
                        null=True,
                        verbose_name="是否评价",
                    ),
                ),
                (
                    "logistics_receipt_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="物流收货时间"
                    ),
                ),
                (
                    "main_status",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="主流程状态"
                    ),
                ),
                (
                    "main_status_desc",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="主流程状态描述",
                    ),
                ),
                (
                    "mask_post_receiver",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="收件人姓名（脱敏后）",
                    ),
                ),
                (
                    "mask_post_tel",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="收件人电话（脱敏后）",
                    ),
                ),
                (
                    "modify_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="改价金额变化量",
                    ),
                ),
                (
                    "modify_post_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="改价运费金额变化量",
                    ),
                ),
                (
                    "only_platform_cost_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="仅平台承担金额（单位：分）",
                    ),
                ),
                (
                    "order_expire_time",
                    models.DateTimeField(
                        blank=True,
                        db_index=True,
                        null=True,
                        verbose_name="订单过期时间",
                    ),
                ),
                (
                    "order_level",
                    models.IntegerField(default=0, verbose_name="订单层级"),
                ),
                (
                    "origin_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="商品现价",
                    ),
                ),
                (
                    "packing_charge_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="打包费，单位：分",
                    ),
                ),
                (
                    "platform_cost_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="平台承担金额（单位：分）",
                    ),
                ),
                (
                    "post_insurance_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="运费险金额（单位：分）",
                    ),
                ),
                (
                    "pre_sale_type",
                    models.SmallIntegerField(
                        blank=True, null=True, verbose_name="预售类型"
                    ),
                ),
                (
                    "promotion_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="订单优惠总金额（单位：分）",
                    ),
                ),
                (
                    "promotion_pay_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="支付优惠金额（单位：分）",
                    ),
                ),
                (
                    "promotion_platform_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="平台优惠金额（单位：分）",
                    ),
                ),
                (
                    "promotion_redpack_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="红包优惠金额（单位：分）",
                    ),
                ),
                (
                    "promotion_redpack_platform_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="平台红包优惠金额（单位：分）",
                    ),
                ),
                (
                    "promotion_redpack_talent_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="达人红包优惠金额（单位：分）",
                    ),
                ),
                (
                    "promotion_shop_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="店铺优惠金额（单位：分）",
                    ),
                ),
                (
                    "promotion_talent_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="达人优惠金额（单位：分）",
                    ),
                ),
                (
                    "quality_inspection_status",
                    models.IntegerField(default=0, verbose_name="质检状态"),
                ),
                (
                    "receive_type",
                    models.IntegerField(blank=True, null=True, verbose_name="收货方式"),
                ),
                (
                    "reduce_stock_type",
                    models.SmallIntegerField(default=1, verbose_name="库存扣减方式"),
                ),
                (
                    "shop_cost_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="商家承担金额（单位：分）",
                    ),
                ),
                (
                    "sku_order_tag_ui",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="商品单标签"
                    ),
                ),
                (
                    "spec",
                    models.JSONField(
                        blank=True, max_length=255, null=True, verbose_name="规格信息"
                    ),
                ),
                (
                    "sum_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="总额",
                    ),
                ),
                (
                    "supplier_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="sku外部供应商编码",
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="税费",
                    ),
                ),
                (
                    "theme_type",
                    models.CharField(blank=True, null=True, verbose_name="下单来源"),
                ),
                (
                    "trade_type",
                    models.IntegerField(blank=True, null=True, verbose_name="交易类型"),
                ),
                (
                    "voucher_deduction_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        null=True,
                        verbose_name="提货券商品抵扣金额；单位：分",
                    ),
                ),
                (
                    "given_product_activity_info",
                    models.JSONField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="赠品活动信息",
                    ),
                ),
                (
                    "after_sale_status",
                    models.IntegerField(
                        blank=True,
                        db_index=True,
                        default=0,
                        null=True,
                        verbose_name="售后状态",
                    ),
                ),
                (
                    "after_sale_type",
                    models.SmallIntegerField(
                        blank=True,
                        db_index=True,
                        default=-1,
                        null=True,
                        verbose_name="售后类型",
                    ),
                ),
                (
                    "refund_status",
                    models.SmallIntegerField(
                        blank=True,
                        db_index=True,
                        default=0,
                        null=True,
                        verbose_name="退款状态",
                    ),
                ),
                (
                    "inventory_list",
                    models.JSONField(
                        blank=True, default=list, null=True, verbose_name="仓库信息"
                    ),
                ),
                (
                    "logistics_info",
                    models.JSONField(
                        blank=True, default=dict, null=True, verbose_name="物流信息"
                    ),
                ),
                (
                    "raw_sku_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="第三方sku_id",
                    ),
                ),
                (
                    "raw_product_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default="",
                        max_length=255,
                        null=True,
                        verbose_name="第三方商品ID",
                    ),
                ),
                (
                    "raw_product_name",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=255,
                        null=True,
                        verbose_name="第三方商品名称",
                    ),
                ),
                (
                    "raw_product_pic",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=300,
                        null=True,
                        verbose_name="第三方商品图片",
                    ),
                ),
                (
                    "raw_sku_specs",
                    models.JSONField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="第三方统规格信息",
                    ),
                ),
                (
                    "platform_update_time",
                    models.DateTimeField(
                        blank=True,
                        db_index=True,
                        null=True,
                        verbose_name="订单更新时间",
                    ),
                ),
                (
                    "is_gift",
                    models.BooleanField(default=False, verbose_name="是否为赠品"),
                ),
                (
                    "create_user",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=32,
                        null=True,
                        verbose_name="创建用户",
                    ),
                ),
                (
                    "update_user",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="更新用户"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        null=True,
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(
                        auto_now=True, db_index=True, null=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "relate_order",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="order_server.order",
                        to_field="order_id",
                        verbose_name="所属订单",
                    ),
                ),
            ],
            options={
                "verbose_name": "子订单信息表",
                "verbose_name_plural": "子订单信息表",
                "db_table": "orders_orderitems",
                "unique_together": {("relate_order_id", "ex_sub_order_id")},
            },
        ),
    ]
