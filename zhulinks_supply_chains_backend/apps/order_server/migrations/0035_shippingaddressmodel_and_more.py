# Generated by Django 5.1.2 on 2025-01-09 10:04

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order_server", "0034_logisticsinformationmodel_shipping_quantity"),
    ]

    operations = [
        migrations.CreateModel(
            name="ShippingAddressModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "contact_name",
                    models.Char<PERSON>ield(
                        help_text="请输入联系人", max_length=10, verbose_name="联系人"
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        help_text="请输入手机号",
                        max_length=11,
                        validators=[
                            django.core.validators.RegexValidator(
                                "^1[3-9]\\d{9}$", "手机号格式不正确"
                            )
                        ],
                        verbose_name="手机号",
                    ),
                ),
                (
                    "province",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="请输入省",
                        max_length=50,
                        null=True,
                        verbose_name="省",
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        blank=True,
                        help_text="请输入市",
                        max_length=50,
                        null=True,
                        verbose_name="市",
                    ),
                ),
                (
                    "district",
                    models.CharField(
                        blank=True,
                        help_text="请输入区",
                        max_length=50,
                        null=True,
                        verbose_name="区",
                    ),
                ),
                (
                    "street",
                    models.CharField(
                        blank=True,
                        help_text="请输入街道/镇",
                        max_length=50,
                        null=True,
                        verbose_name="街道/镇",
                    ),
                ),
                (
                    "detail_address",
                    models.CharField(
                        help_text="请输入详细地址",
                        max_length=255,
                        verbose_name="详细地址",
                    ),
                ),
                (
                    "is_default_shipping",
                    models.BooleanField(
                        default=False,
                        help_text="是否为默认发货地址",
                        verbose_name="发货默认",
                    ),
                ),
                (
                    "is_default_return",
                    models.BooleanField(
                        default=False,
                        help_text="是否为默认退货地址",
                        verbose_name="退货默认",
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "地址信息",
                "verbose_name_plural": "地址信息",
                "ordering": ["-create_date"],
            },
        ),
        migrations.AddField(
            model_name="logisticsinformationmodel",
            name="is_redelivery",
            field=models.BooleanField(default=False, verbose_name="是否为重新发货"),
        ),
        migrations.AddField(
            model_name="logisticsinformationmodel",
            name="is_valid",
            field=models.BooleanField(default=True, verbose_name="是否有效"),
        ),
        migrations.AddField(
            model_name="logisticsinformationmodel",
            name="logistics_type",
            field=models.CharField(
                choices=[("SHIPPING", "发货"), ("RETURN", "退货")],
                default="SHIPPING",
                max_length=10,
                verbose_name="物流类型",
            ),
        ),
        migrations.AddField(
            model_name="logisticsinformationmodel",
            name="original_logistics",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="redelivery_records",
                to="order_server.logisticsinformationmodel",
                verbose_name="原始发货记录",
            ),
        ),
        migrations.AlterField(
            model_name="logisticsinformationmodel",
            name="logistics_information_data",
            field=models.JSONField(blank=True, null=True, verbose_name="物流轨迹"),
        ),
        migrations.AlterField(
            model_name="logisticsinformationmodel",
            name="order_items",
            field=models.ForeignKey(
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="order_items_logistics_information",
                to="order_server.orderitems",
                verbose_name="所属订单",
            ),
        ),
        migrations.AddField(
            model_name="logisticsinformationmodel",
            name="shipping_address",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="order_server.shippingaddressmodel",
                verbose_name="发货地址",
            ),
        ),
    ]
