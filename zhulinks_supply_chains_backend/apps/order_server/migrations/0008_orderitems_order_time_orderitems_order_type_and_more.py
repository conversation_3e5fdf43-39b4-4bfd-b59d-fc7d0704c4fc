# Generated by Django 5.0.8 on 2024-11-27 06:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "order_server",
            "0007_alter_orderitemssubproductsnapshotrelate_unique_together",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="orderitems",
            name="order_time",
            field=models.DateTimeField(
                db_index=True, default=None, verbose_name="下单时间"
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="orderitems",
            name="order_type",
            field=models.IntegerField(
                db_index=True, default=0, verbose_name="订单类型"
            ),
        ),
        migrations.AddField(
            model_name="orderitems",
            name="pay_time",
            field=models.DateTimeField(
                blank=True,
                db_index=True,
                default=None,
                null=True,
                verbose_name="支付时间",
            ),
        ),
        migrations.AddIndex(
            model_name="orderitems",
            index=models.Index(
                fields=[
                    "order_time",
                    "distributor_id",
                    "sku_id",
                    "pay_time",
                    "from_mall",
                    "order_type",
                ],
                name="orderitems_calc_plan_sales_idx",
            ),
        ),
    ]
