# Generated by Django 5.0.8 on 2025-01-16 02:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order_server", "0042_create_order_user_tag_index"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderTagTextEnum",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.CharField(max_length=64, verbose_name="标签文本")),
                (
                    "tag_type",
                    models.CharField(
                        choices=[("user", "用户特征标签"), ("product", "商品特征标签")],
                        default="user",
                        max_length=12,
                        verbose_name="标签类型",
                    ),
                ),
                (
                    "order",
                    models.PositiveSmallIntegerField(
                        default=0, help_text="从小到大排序", verbose_name="优先级"
                    ),
                ),
            ],
            options={
                "verbose_name": "订单标签文本枚举",
                "verbose_name_plural": "订单标签文本枚举",
                "db_table": "orders_order_tag_text",
                "ordering": ("order",),
                "unique_together": {("order", "tag_type")},
            },
        ),
    ]
