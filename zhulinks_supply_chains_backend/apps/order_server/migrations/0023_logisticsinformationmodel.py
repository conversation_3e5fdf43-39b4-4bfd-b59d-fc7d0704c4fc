# Generated by Django 5.1.2 on 2024-12-26 08:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order_server", "0022_missingorderlist"),
    ]

    operations = [
        migrations.CreateModel(
            name="LogisticsInformationModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "logistics_information_data",
                    models.JSONField(verbose_name="物流信息"),
                ),
                ("status", models.CharField(verbose_name="物流状态")),
                ("nu", models.CharField(verbose_name="运单号")),
                ("com", models.Char<PERSON>ield(verbose_name="快递公司编码")),
                ("condition", models.Char<PERSON>ield(verbose_name="快递单明细状态标记")),
                (
                    "routeInfo",
                    models.JSONField(verbose_name="本数据元对应的目的地城市信息"),
                ),
                ("isLoop", models.BooleanField(default=False, verbose_name="isLoop")),
                (
                    "create_date",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_date",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "order_items",
                    models.OneToOneField(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="order_items_logistics_information",
                        to="order_server.orderitems",
                        verbose_name="所属订单",
                    ),
                ),
            ],
            options={
                "verbose_name": "商品物流信息",
                "verbose_name_plural": "商品物流信息",
                "db_table": "logistics_information",
            },
        ),
    ]
