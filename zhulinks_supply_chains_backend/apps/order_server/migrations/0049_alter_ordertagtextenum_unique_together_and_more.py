# Generated by Django 5.0.8 on 2025-01-22 02:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order_server", "0048_alter_ordertagtextenum_unique_together"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="ordertagtextenum",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="ordertagtextenum",
            name="data_source",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "直播订单"), (2, "商城订单"), (3, "分销订单")],
                default=1,
                verbose_name="数据来源",
            ),
        ),
        migrations.AlterField(
            model_name="ordertagtextenum",
            name="tag_type",
            field=models.CharField(
                choices=[
                    ("user", "用户特征标签"),
                    ("product", "商品特征标签"),
                    ("coupon", "优惠券标签"),
                    ("ktt", "快团团特征标签"),
                ],
                default="user",
                max_length=12,
                verbose_name="标签类型",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="ordertagtextenum",
            unique_together={("text", "tag_type", "data_source")},
        ),
    ]
