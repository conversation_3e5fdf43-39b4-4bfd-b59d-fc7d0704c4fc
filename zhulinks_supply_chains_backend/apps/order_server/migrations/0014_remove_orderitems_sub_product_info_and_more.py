# Generated by Django 5.0.8 on 2024-12-02 02:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order_server", "0013_alter_ordersupplierrelate_order"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="orderitems",
            name="sub_product_info",
        ),
        migrations.AddField(
            model_name="orderotherinfo",
            name="jst_oid",
            field=models.CharField(
                blank=True, max_length=32, null=True, verbose_name="聚水潭订单id"
            ),
        ),
        migrations.AlterField(
            model_name="order",
            name="distributor_id",
            field=models.IntegerField(
                db_index=True, default=0, verbose_name="分销商ID"
            ),
        ),
        migrations.AlterField(
            model_name="orderitems",
            name="sub_product_id",
            field=models.IntegerField(
                blank=True,
                db_index=True,
                default=0,
                null=True,
                verbose_name="副本商品ID",
            ),
        ),
    ]
