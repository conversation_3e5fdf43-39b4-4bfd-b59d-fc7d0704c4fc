# Generated by Django 5.0.8 on 2024-11-21 11:48

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order_server", "0004_dyordersubscribemessage_aftersale_id_order_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="ordermetadata",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="ordermetadata",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_index=True,
                default=django.utils.timezone.now,
                verbose_name="更新时间",
            ),
            preserve_default=False,
        ),
    ]
