# Generated by Django 5.0.8 on 2025-01-16 02:18

from django.db import migrations


class Migration(migrations.Migration):
    from order_server.models import OrderBuyerInfo
    table_name = OrderBuyerInfo._meta.db_table

    index_name = "idx_user_tag_ui_json_gin"

    dependencies = [
        ("order_server", "0041_create_order_items_index"),
    ]

    operations = [
        migrations.RunSQL(
            f"""
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 
                    FROM pg_indexes 
                    WHERE tablename = '{table_name}' AND indexname = '{index_name}'
                ) THEN
                    CREATE INDEX {index_name} 
                    ON {table_name} 
                    USING gin (user_tag_ui);
                END IF;
            END $$;
            """,
            reverse_sql=f"""
                DROP INDEX IF EXISTS {index_name};
                """
        ),
    ]
