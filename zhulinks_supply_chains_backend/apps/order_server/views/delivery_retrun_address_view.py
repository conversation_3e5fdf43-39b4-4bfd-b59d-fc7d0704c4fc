from common.basics.permissions import OPPermissions
from order_server.filters import DeliveryReturnAddressFilter
from order_server.models import DeliveryReturnAddress
from order_server.serializers import DeliveryReturnAddressSerializer
from utils.api_page_number_pagination import ApiPageNumberPagination
from utils.base_model_view_set import BaseMysqlModelViewSet


class DeliveryReturnAddressViews(BaseMysqlModelViewSet):
    queryset = DeliveryReturnAddress.objects.all().order_by("-create_date")
    pagination_class = ApiPageNumberPagination
    filterset_class = DeliveryReturnAddressFilter
    serializer_class = DeliveryReturnAddressSerializer
    # permission_classes = [OPPermissions]
