from django.db import transaction
from rest_framework import status
from rest_framework.decorators import action
from django.db.models import Sum
from common.basics.permissions import OPPermissions
from order_server import logger
from order_server.filters import OrdersShippingFilter
from order_server.models import LogisticsInformationModel, DeliveryReturnAddress, OrderItems, Order
from order_server.serializers import OrdersShippingSerializer
from utils.api_page_number_pagination import ApiPageNumberPagination
from utils.base_model_view_set import BaseMysqlModelViewSet
from utils.http_handle import IResponse
from utils.kuaidi100_api.client import KuaiDi100


class OrdersShippingView(BaseMysqlModelViewSet):
    queryset = LogisticsInformationModel.objects.filter(is_deleted=False).order_by("-create_date")
    pagination_class = ApiPageNumberPagination
    filterset_class = OrdersShippingFilter
    serializer_class = OrdersShippingSerializer
    permission_classes = [OPPermissions]

    def create(self, request, *args, **kwargs):
        """
        创建物流信息
        shipping_method: 1（自行发货），2（电子面单发货）
        """
        try:
            # 获取请求参数
            shipping_method = request.data.get("shipping_method")
            delivery_address = request.data.get("delivery_address")
            tracking_no = request.data.get("tracking_no")
            shipping_info = request.data.get("shipping_info")
            tracking_code = request.data.get("tracking_code")

            # 检查必要参数是否为空
            required_params = [shipping_method, tracking_no, shipping_info, tracking_code]
            if not all(required_params):
                return IResponse(data=None, message="参数不能为空", code=status.HTTP_400_BAD_REQUEST)

            # 获取发货地址信息
            delivery_return_address = DeliveryReturnAddress.objects.filter(id=delivery_address).first()
            if not delivery_return_address:
                return IResponse(data=None, message="发货地址不存在", code=status.HTTP_400_BAD_REQUEST)

            # 通过快递100 API识别物流公司
            kuaidi_client = KuaiDi100()
            logistics_info = kuaidi_client.logistics_number_identification(tracking_no)
            if not logistics_info:
                return IResponse(data=None, message="物流单号识别失败", code=status.HTTP_400_BAD_REQUEST)
            company_name = logistics_info[0].get("name")
            company_code = logistics_info[0].get("comCode")

            if company_code != tracking_code:
                return IResponse(data=None, message="快递公司选择有误，请重新选择", code=status.HTTP_400_BAD_REQUEST)
            # product_count = 0
            # 在事务中批量创建物流信息
            with transaction.atomic():
                for data in shipping_info:
                    order_id = data.get("order_id")
                    shipping_quantity = data.get("shipping_quantity")

                    # 获取订单项和相关数据
                    order_item_obj, existing_logistics, order_item_count = self._get_order_item_and_logistics(order_id)

                    if not order_item_obj:
                        raise Exception(f"订单不存在: {order_id}")

                    # 检查发货数量是否超过订单总量
                    total_shipped_quantity = sum(existing_logistics.values_list("shipping_quantity", flat=True))
                    is_exceeding_quantity = shipping_quantity + total_shipped_quantity > order_item_count

                    if order_item_obj.order_status in ["WP", "FN", "CL"]:
                        return IResponse(data=None, message=f"该 {order_id} 订单状态无法重新发货", code=500)

                    # 更新物流信息
                    self._update_or_create_logistics(is_exceeding_quantity, order_item_obj, existing_logistics, delivery_return_address, tracking_no, company_name, shipping_quantity)
                    # product_count += shipping_quantity

            # 获取 order_id
            order_id = shipping_info[0].get("order_id")

            # 获取 OrderItems 对象及其关联的 relate_order
            order_items_obj = OrderItems.objects.filter(order_id=order_id).first()

            relate_order = order_items_obj.relate_order

            # 使用聚合函数计算 item_num 的总和
            item_num_sum = OrderItems.objects.filter(relate_order=relate_order).aggregate(total_item_num=Sum("item_num"))["total_item_num"] or 0
            product_count = (
                LogisticsInformationModel.objects.filter(order_items__relate_order=relate_order, is_valid=True).aggregate(total_shipping_quantity=Sum("shipping_quantity"))["total_shipping_quantity"]
                or 0
            )
            if item_num_sum == product_count:
                relate_order.main_status = 3
                relate_order.main_status_desc = "全部发货"
            else:
                relate_order.main_status = 101
                relate_order.main_status_desc = "部分发货"

            relate_order.order_status = "DL"
            relate_order.order_status_desc = "待收货"
            relate_order.save()

            return IResponse(data=None, message="success", code=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"创建物流信息失败: {str(e)}")
            return IResponse(data=None, message="服务器内部错误", code=status.HTTP_500_INTERNAL_SERVER_ERROR, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_order_item_and_logistics(self, order_id):
        """获取订单项及相关物流信息"""
        order_item_obj = OrderItems.objects.filter(order_id=order_id).first()
        existing_logistics = LogisticsInformationModel.objects.filter(order_items=order_item_obj, is_valid=True)
        order_item_count = order_item_obj.item_num if order_item_obj else 0
        return order_item_obj, existing_logistics, order_item_count

    def _update_or_create_logistics(self, is_exceeding_quantity, order_item_obj, existing_logistics, delivery_return_address, tracking_no, company_name, shipping_quantity):
        """更新或创建物流信息"""
        if is_exceeding_quantity:
            # 超过总量时，标记旧记录为无效并创建新的物流信息
            original_logistics = existing_logistics.first() if len(existing_logistics) == 1 else None
            existing_logistics.update(is_valid=False)
            self._create_logistics(order_item_obj, delivery_return_address, tracking_no, company_name, shipping_quantity, is_redelivery=True, original_logistics=original_logistics)
        else:
            # 未超过总量，直接创建新的物流信息
            self._create_logistics(order_item_obj, delivery_return_address, tracking_no, company_name, shipping_quantity, is_redelivery=False)

    def _create_logistics(self, order_item_obj, delivery_return_address, tracking_no, company_name, shipping_quantity, is_redelivery, original_logistics=None):
        """创建物流信息"""
        LogisticsInformationModel.objects.create(
            order_items=order_item_obj,
            shipping_address=delivery_return_address,
            nu=tracking_no,
            company_name=company_name,
            shipping_quantity=shipping_quantity,
            is_redelivery=is_redelivery,
            original_logistics=original_logistics,
            is_valid=True,
        )
        order_item_obj.order_status = "DL"
        order_item_obj.order_status_desc = "待收货"
        order_item_obj.product_count = shipping_quantity
        order_item_obj.save()

    @action(methods=["GET"], detail=False)
    def query_re_shipment(self, request):
        order_id = request.GET.get("order_id")
        if not order_id:
            return IResponse(data=None, message="参数不能为空", code=status.HTTP_400_BAD_REQUEST)

        # 获取物流数据
        logistics_data = LogisticsInformationModel.objects.filter(order_items__relate_order_id=order_id, is_valid=True)
        if not logistics_data:
            return IResponse(data=None, message="该订单暂无有效物流信息", code=400)

        logistics_dict = {}

        for data in logistics_data:
            tracking_no = data.nu
            order_info = {data.order_items.order_id: data.order_items.raw_product_pic}

            if tracking_no in logistics_dict:
                # 如果物流单号已存在，合并order_info
                logistics_dict[tracking_no]["order_info"].append(order_info)
                logistics_dict[tracking_no]["orders"].append(data.order_items.order_id)
                logistics_dict[tracking_no]["imgs"].append(data.order_items.raw_product_pic)
            else:
                # 如果物流单号不存在，创建新的条目
                logistics_dict[tracking_no] = {
                    "shipping_method": data.shipping_method,
                    "delivery_address": data.shipping_address.id if data.shipping_address else None,
                    "tracking_no": tracking_no,
                    "tracking_code": data.com,
                    "tracking_name": data.company_name,
                    "shipping_quantity": data.shipping_quantity,
                    "order_info": [order_info],
                    "orders": [data.order_items.order_id],
                    "imgs": [data.order_items.raw_product_pic],
                }

        # 将字典转换为列表
        data_list = list(logistics_dict.values())

        return IResponse(data=data_list, code=200)

    @action(methods=["POST"], detail=False)
    def re_shipment(self, request):
        data_list = request.data
        print(f"data_list:{data_list}")
        kuaidi_client = KuaiDi100()
        for data in data_list:
            old_tracking_no = data.get("old_tracking_no")
            new_tracking_no = data.get("new_tracking_no")
            tracking_code = data.get("tracking_code")
            order_ids = data.get("order_ids")

            if not all([old_tracking_no, new_tracking_no, order_ids, tracking_code]):
                return IResponse(data=None, message="请携带完整参数", code=400)

            # 通过快递100 API识别物流公司
            logistics_info = kuaidi_client.logistics_number_identification(new_tracking_no)
            if not logistics_info:
                return IResponse(data=None, message="物流单号识别失败", code=status.HTTP_400_BAD_REQUEST)
            company_name = logistics_info[0].get("name")
            company_code = logistics_info[0].get("comCode")
            print(f"company_code:{company_code}")
            if company_code != tracking_code:
                return IResponse(data=None, message="快递公司选择有误，请重新选择", code=status.HTTP_400_BAD_REQUEST)

            try:
                self._process_reshipment(old_tracking_no, new_tracking_no, company_code, company_name, order_ids)
            except Exception as e:
                return IResponse(data=None, message=f"重新发货失败: {str(e)}",
                                 code=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return IResponse(data=None, message="重新发货成功", code=200)

    def _process_reshipment(self, old_tracking_no, new_tracking_no, company_code, company_name, order_ids):
        """处理重新发货逻辑"""
        old_logistics_list = []
        new_logistics_list = []

        # 查询旧物流信息并标记为无效
        for order_id in order_ids:
            try:
                old_data_all = LogisticsInformationModel.objects.filter(order_items__order_id=order_id, nu=old_tracking_no, is_valid=True)
                old_data = old_data_all.last()
                for update_data in old_data_all:
                    update_data.is_valid = False
                    old_logistics_list.append(update_data)

                # 创建新的物流信息
                new_logistics = LogisticsInformationModel(
                    order_items=old_data.order_items,
                    shipping_address=old_data.shipping_address,
                    nu=new_tracking_no,
                    com=company_code,
                    company_name=company_name,
                    shipping_quantity=old_data.shipping_quantity,
                    is_redelivery=True,
                    original_logistics=old_data,
                    is_valid=True,
                )
                new_logistics_list.append(new_logistics)
            except LogisticsInformationModel.DoesNotExist:
                raise Exception(f"订单 {order_id} 的旧物流信息未找到")

        # 批量更新旧物流信息
        LogisticsInformationModel.objects.bulk_update(old_logistics_list, ["is_valid"])

        # 批量创建新物流信息
        LogisticsInformationModel.objects.bulk_create(new_logistics_list)

    # def update(self, request, *args, **kwargs):
    #     """
    #     更新物流信息
    #     """
    #     try:
    #         instance = self.get_object()  # 获取当前需要更新的物流信息实例
    #         tracking_no = request.data.get("tracking_no")
    #         shipping_quantity = request.data.get("shipping_quantity")
    #
    #         # 检查必要参数是否为空
    #         if not all([tracking_no, shipping_quantity]):
    #             return IResponse(data=None, message="参数不能为空", code=status.HTTP_400_BAD_REQUEST, status=status.HTTP_400_BAD_REQUEST)
    #
    #         # 通过快递100 API 识别物流公司
    #         kuaidi_client = KuaiDi100()
    #         logistics_info = kuaidi_client.logistics_number_identification(tracking_no)
    #         if not logistics_info:
    #             return IResponse(data=None, message="物流单号识别失败", code=status.HTTP_400_BAD_REQUEST, status=status.HTTP_400_BAD_REQUEST)
    #
    #         company_name = logistics_info[0].get("name")
    #
    #         # 在事务中更新物流信息
    #         with transaction.atomic():
    #             # 检查发货数量是否超过订单总量
    #             order_item_obj = instance.order_items
    #             existing_logistics = LogisticsInformationModel.objects.filter(order_items=order_item_obj, is_valid=True).exclude(id=instance.id)  # 排除当前实例
    #             total_shipped_quantity = sum(existing_logistics.values_list("shipping_quantity", flat=True))
    #
    #             if shipping_quantity + total_shipped_quantity > order_item_obj.item_num:
    #                 return IResponse(data=None, message="发货数量超过订单总量", code=status.HTTP_400_BAD_REQUEST, status=status.HTTP_400_BAD_REQUEST)
    #
    #             # 更新当前物流信息
    #             instance.nu = tracking_no
    #             instance.company_name = company_name
    #             instance.shipping_quantity = shipping_quantity
    #
    #             instance.save()
    #
    #         return IResponse(data=None, message="物流信息更新成功", code=status.HTTP_200_OK, status=status.HTTP_200_OK)
    #
    #     except Exception as e:
    #         logger.error(f"更新物流信息失败: {str(e)}")
    #         return IResponse(data=None, message="服务器内部错误", code=status.HTTP_500_INTERNAL_SERVER_ERROR, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    #
    # def destroy(self, request, *args, **kwargs):
    #     """
    #     软删除物流信息（将 is_deleted 字段设置为 True）
    #     """
    #     try:
    #         instance = self.get_object()  # 获取当前需要删除的物流信息实例
    #
    #         # 在事务中执行软删除
    #         with transaction.atomic():
    #             instance.is_deleted = True
    #             instance.save()
    #
    #         return IResponse(data=None, message="物流信息删除成功", code=status.HTTP_200_OK, status=status.HTTP_200_OK)
    #
    #     except Exception as e:
    #         logger.error(f"删除物流信息失败: {str(e)}")
    #         return IResponse(data=None, message="服务器内部错误", code=status.HTTP_500_INTERNAL_SERVER_ERROR, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# SF3107246916834
# YT1909196817637


# PO24121251610509777
# PO24121289302832324
