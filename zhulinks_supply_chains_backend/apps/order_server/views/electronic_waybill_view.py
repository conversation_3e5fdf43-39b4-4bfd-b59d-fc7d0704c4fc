from rest_framework.views import APIView
from rest_framework import serializers
from utils.http_handle import IResponse
from utils.kuaidi100_api.client import KuaiDi100


# 定义序列化器进行参数验证
class ElectronicWaybillSerializer(serializers.Serializer):
    print_type = serializers.CharField(required=True, max_length=10)
    partner_id = serializers.CharField(required=True, max_length=50)
    kuaidi_com = serializers.CharField(required=True, max_length=50)
    cargo = serializers.CharField(required=True, max_length=100)
    temp_id = serializers.CharField(required=True, max_length=50)
    rec_man_name = serializers.CharField(required=True, max_length=100)
    rec_man_mobile = serializers.CharField(required=True, max_length=15)
    rec_man_addr = serializers.CharField(required=True, max_length=255)
    send_man_name = serializers.CharField(required=True, max_length=100)
    send_man_mobile = serializers.Char<PERSON>ield(required=True, max_length=15)
    send_man_addr = serializers.Char<PERSON>ield(required=True, max_length=255)
    ocr_include = serializers.BooleanField(required=False, default=False)


class ElectronicWaybillView(APIView):
    def post(self, request):
        # 使用序列化器验证请求数据
        serializer = ElectronicWaybillSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # 提取验证后的数据
        validated_data = serializer.validated_data

        # 调用快递接口
        kuaidi_client = KuaiDi100()
        try:
            data = kuaidi_client.electronic_manifest_submission(**validated_data)
        except Exception as e:
            return IResponse(errorMessage=f"Error occurred: {str(e)}")

        return IResponse(data=data)
