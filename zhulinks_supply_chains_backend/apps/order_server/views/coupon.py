# -*- coding: utf-8 -*-
from django.db import transaction
from rest_framework.request import Request

from common.basic import OPAPIView, MallDBAPIView
from common.basics.exceptions import DataNotFoundException, APIViewException
from order_server.filtersets import CouponListFilterSet, CouponUserListFilterSet, UserMyCouponListFilterSet
from order_server.models import Coupon, CouponProduct, CouponUser
from order_server.serializers.coupon import (
    CouponCreateSerializer,
    CouponListSerializer,
    CouponDetailSerializer,
    CouponUpdateSer,
    CouponUserListSer,
    CouponUsageSerializer,
    UserMyCouponListSer,
)
from products.views.distributor_market_views.operator_views import OPDistributorMarketProductView
from utils.http_handle import FieldsError, IResponse, custom_django_filter, EmptyListResponse


def _get_coupon(code: str) -> Coupon:
    try:
        coupon = Coupon.objects.get(code=code)
        return coupon
    except Coupon.DoesNotExist:
        raise DataNotFoundException


class CouponView(OPAPIView):
    def get(self, request: Request):
        coupons = Coupon.objects.all().select_related("coupondata").order_by("-update_date")
        re_data, _, _ = custom_django_filter(
            request,
            coupons,
            CouponListFilterSet,
            CouponListSerializer,
            force_order=False,
        )
        return IResponse(data=re_data)

    def post(self, request: Request):
        post_data = request.data
        post_data["create_user"] = self.current_user.user_id
        coupon_create_ser = CouponCreateSerializer(data=request.data)
        if not coupon_create_ser.is_valid():
            raise FieldsError(error_value=coupon_create_ser.errors)

        coupon_create_ser.save()
        return IResponse()


class UserMyCouponView(MallDBAPIView):
    def get(self, request: Request):
        user_coupons = (
            CouponUser.objects.prefetch_related("coupon")
            .filter(
                user_id=self.current_user.user_id,
                distributor_id=self.current_distributor.distributor_id,
            )
            .order_by("-valid_days")
        )
        re_data, _, _ = custom_django_filter(
            request,
            user_coupons,
            UserMyCouponListFilterSet,
            UserMyCouponListSer,
            force_order=False,
        )
        return IResponse(data=re_data)


class CouponDetailView(OPAPIView):
    def get(self, request: Request, code: str):
        coupon = _get_coupon(code)
        ser = CouponDetailSerializer(instance=coupon, many=False)
        return IResponse(data=ser.data)

    def put(self, request: Request, code: str):
        coupon = _get_coupon(code)
        ser = CouponUpdateSer(instance=coupon, data=request.data)
        if not ser.is_valid():
            raise FieldsError(ser.errors)
        ser.save()
        return IResponse()


class CouponProductScopeDetailView(OPAPIView):
    def get(self, request: Request, code: str):
        """
        优惠券指定商品列表
        :param request:
        :param code:
        :return:
        """
        p_id_list = list(CouponProduct.objects.filter(coupon_id=code).values_list("product_id", flat=True))
        if not p_id_list:
            return IResponse(data=EmptyListResponse)

        request.query_params.update({"is_in_distributor_market": "1"})
        response = OPDistributorMarketProductView.get(self, request, internal_pid_list=p_id_list)
        return response


class CouponUserScopeDetailView(OPAPIView):
    def get(self, request: Request, code: str):
        """
        优惠券指定商品列表
        :param request:
        :param code:
        :return:
        """
        coupon = _get_coupon(code)
        coupon_users = coupon.couponuser_set.all()

        re_data, _, _ = custom_django_filter(
            request,
            coupon_users,
            CouponUserListFilterSet,
            CouponUserListSer,
        )
        return IResponse(data=re_data)


class CouponInvalidateView(OPAPIView):
    def post(self, request: Request, code: str):
        coupon = _get_coupon(code)
        if coupon.display_dynamic_status == 3:
            raise APIViewException(err_message="Coupon has expired")

        if coupon.display_dynamic_status == 4:
            raise APIViewException(err_message="Coupon has deprecated")
        with transaction.atomic():
            coupon.status = 4
            coupon.update_user = self.current_user.user_id
            coupon.save(update_fields=["status", "update_user", "update_date"])

            # 用户的优惠券也过期
            coupon.couponuser_set.exclude(dynamic_status=2).update(status=4)

        return IResponse()


class CouponUsageView(OPAPIView):
    def get(self, request: Request, code: str):
        coupon = _get_coupon(code)
        ser = CouponUsageSerializer(instance=coupon, many=False)
        return IResponse(data=ser.data)
