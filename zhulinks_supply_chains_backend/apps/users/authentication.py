# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-06-12 15:42:17
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-11-23 18:30:50
import time

from django.apps import apps
from rest_framework.exceptions import NotAuthenticated
from rest_framework.request import Request
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import AuthenticationFailed, InvalidToken

from common.basics.exceptions import APIViewException


class MyForbiddenToken(InvalidToken):
    status_code = 403
    default_code = "user_not_valid"


class MyJWTAuthentication(JWTAuthentication):

    # 供应商没补充详细信息可通行接口
    supplier_with_no_company_white_api_list = [
        "/v1/common/img_ocr",
        "/v1/users/set_profile",
        "/v1/users/user/myprofile",
        "/v1/common/areas",
        "/v1/common/oss_credential",
    ]

    # 未经过供应商审核的的可通行接口
    supplier_with_unaudited_company_white_api_list = [
        "/v1/users/company_detail",
        "/v1/users/user/myprofile",
        "/v1/common/areas",
    ]

    def jwt_authenticate(self, request):
        pass

    def authenticate(self, request: Request):
        ret = getattr(request, "user_jwt", None)
        try:
            # 判断 token是否在redis黑名单中
            if ret:
                jti = ret[1].get("jti")
                if jti and jwt_in_redis_blacklist(jti):
                    raise AuthenticationFailed
                # 切换当前分销商
                user, info = ret

                db_id = info.get("db_id")
                user_type = info.get("user_type")
                if db_id:
                    Distributor = apps.get_model("companies", "Distributor")
                    UserDistributorRelationShip = apps.get_model("users", "UserDistributorRelationShip")

                    if not user.distributor or (user.distributor and user.distributor.distributor_id != db_id):
                        try:
                            distributor = Distributor.objects.get(distributor_id=db_id)
                            setattr(user, "distributor", distributor)
                            # 查询relation
                            relation = UserDistributorRelationShip.objects.filter(user=user, distributor=distributor).first()
                            if not relation:
                                raise AuthenticationFailed

                            # 特殊的权限控制
                            setattr(user, "is_distributor_manager", relation.is_distributor_manager)
                            setattr(user, "can_edit_code", relation.can_edit_code)
                            setattr(user, "can_view_zhuji_rank", relation.can_view_zhuji_rank)
                            setattr(user, "can_edit_product_info_sync", relation.can_edit_product_info_sync)
                            setattr(user, "can_view_plan_profit", relation.can_view_plan_profit)
                            setattr(user, "can_see_refund_rate", relation.can_see_refund_rate)
                            setattr(user, "can_watch_plan", relation.can_watch_plan)
                        except Distributor.DoesNotExist:
                            pass
                else:
                    # 校验分销商
                    # 如果原来有分销商，登录后再去除分销商，fbv类型的接口会报错, 有些没有校验request.user.distributor
                    if user_type == "DB":
                        if not user.distributor:
                            raise AuthenticationFailed
                    elif user_type == "SP":
                        # 如果用户没有供应商信息，302跳转到补充资料的地址
                        if not user.company_id and request._request.path not in self.supplier_with_no_company_white_api_list:
                            raise APIViewException(err_message="请先补充完整公司信息", code=1001)

                        #
                        if user.company_id:
                            if user.company.state in [0, 2] and request._request.path not in self.supplier_with_unaudited_company_white_api_list:
                                raise APIViewException(err_message="", code=1002)

            return ret
        except AuthenticationFailed as e:
            print(e)
            if "该用户已禁用" in e.args:
                raise MyForbiddenToken
        #
        # # 校验登录用户是否在logged user表
        # user = ret[0]
        # if not user.logged_in_user.filter(user_type=user.user_type).exists():
        #     raise DRFNotAuthenticationFailed
        # return ret


BLACK_TOKEN_PREFIX = "blank_t:"


def add_jwt_to_blacklist(request):
    from utils.redis_lock import redis_cache

    """添加jwt token到黑名单"""
    jwt_auth = request._auth
    if not jwt_auth:
        raise ValueError("this request is not valid.")

    jti = jwt_auth.get("jti")
    exp = jwt_auth.get("exp")

    now_ts = int(time.time())
    redis_cache.set(BLACK_TOKEN_PREFIX + jti, jwt_auth, exp - now_ts + 5)
    return True


def jwt_in_redis_blacklist(jti):
    """判断jwt jti是否在redis黑名单"""
    from utils.redis_lock import redis_cache

    return bool(redis_cache.get(BLACK_TOKEN_PREFIX + jti))
