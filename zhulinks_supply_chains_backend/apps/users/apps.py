# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-05-26 11:26:56
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-06-13 05:14:40
from django.apps import AppConfig


class UsersConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "users"
    verbose_name = "用户管理"

    def ready(self):
        from users import signals

        signals.signal_load_func()

        from common.basic import generic_m2m_change_handler
        from users.models import User
        from users.models_v2 import Roles

        generic_m2m_change_handler(User, "groups")
        generic_m2m_change_handler(User, "user_types")
        generic_m2m_change_handler(User, "depts")
        generic_m2m_change_handler(User, "roles")

        # 注册信号
        generic_m2m_change_handler(Roles, "permissions")

        # 加载model冲突重写
        # from zhulinks_supply_chains_backend.monkey_patch import rewrite_models_query_with_retry
        # rewrite_models_query_with_retry()
