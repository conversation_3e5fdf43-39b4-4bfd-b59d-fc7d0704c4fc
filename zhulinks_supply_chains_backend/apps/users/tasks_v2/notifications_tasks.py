from datetime import timedelta

from common.models import get_user_notification_max_keep_days
from django.core.paginator import Paginator
from django.db import connection
from django.utils import timezone
from users import logger
from users.models import Notification, NotificationReadStatus, bulk_update_notification_read_counts

from zhulinks_supply_chains_backend.celery import app


@app.task(queue="long_running_tasks")
def read_all_notifications(user_pk, user_id, user_type, company_id: int = None, batch_size=200):
    filters = {}
    if user_type == "SP":
        filters["company_id"] = company_id
        filters["notify_type__in"] = ["plan", "review", "stock", "price"]
    elif user_type == "OP":
        filters["notify_type__in"] = ["stock_warning", "review", "price"]
    elif user_type == "DB":
        filters["notify_type__in"] = ["stock_warning", "price"]
    elif user_type == "SELF_DB":
        filters["notify_type__in"] = ["sales", "order"]
        filters["visible_range__user__contains"] = [user_id]
    else:
        raise ValueError("user type invalid")

    now = timezone.now()
    total_created = 0
    total_updated = 0

    # 查询所有未读通知，使用 Paginator 进行分页
    # 排除7天前的通知，因为它们会被自动标记为已读

    days = get_user_notification_max_keep_days()

    seven_days_ago = timezone.now() - timedelta(days=days)
    queryset = Notification.objects.filter(**filters, create_date__gte=seven_days_ago).only("id")
    paginator = Paginator(queryset, batch_size)  # 创建分页器，每页 batch_size 条记录

    for page_num in range(1, paginator.num_pages + 1):
        page = paginator.page(page_num)
        page_ids = list(page.object_list.values_list("id", flat=True))

        # 批量更新已存在但未读的记录
        updated_count = NotificationReadStatus.objects.filter(
            notification_id__in=page_ids,
            user_id=user_pk,
            has_read=False,
        ).update(has_read=True, read_time=now)
        total_updated += updated_count

        # 找出需要创建的新记录
        existing_ids = set(
            NotificationReadStatus.objects.filter(
                notification_id__in=page_ids,
                user_id=user_pk,
            ).values_list("notification_id", flat=True)
        )
        new_statuses = [
            NotificationReadStatus(
                user_id=user_pk,
                notification_id=notification_id,
                has_read=True,
                read_time=now,
            )
            for notification_id in page_ids
            if notification_id not in existing_ids
        ]

        # 批量插入新记录
        if new_statuses:
            NotificationReadStatus.objects.bulk_create(new_statuses, batch_size=batch_size, ignore_conflicts=True)
            total_created += len(new_statuses)

        # 更新阅读计数
        bulk_update_notification_read_counts(page_ids)

    print(f"rows_updated: {total_updated}")
    print(f"rows_created: {total_created}")

    return f"Created: {total_created}, Updated: {total_updated}"


@app.task(queue="long_running_tasks")
def cleanup_old_notification_read_status_task():
    """
    清理7天前的通知关联记录

    由于超过7天的通知会自动视为已读，因此可以安全地删除这些旧记录
    这个任务应该定期运行（例如每天凌晨）以减少数据库负担

    注意：该实现优化了内存使用，可以处理大量数据（百万级别）而不会导致OOM
    """
    try:
        # 计算7天前的时间点
        days = get_user_notification_max_keep_days()
        seven_days_ago = timezone.now() - timedelta(days=days)
        total_deleted = 0

        # 直接使用原生SQL删除，避免将所有ID加载到内存
        with connection.cursor() as cursor:
            # 获取表名
            table_name = NotificationReadStatus._meta.db_table
            notification_table = Notification._meta.db_table

            # 先检查有多少条记录需要删除（仅计数，不返回实际数据）
            cursor.execute(
                f"""
                SELECT COUNT(*) FROM {table_name}
                WHERE notification_id IN (
                    SELECT id FROM {notification_table}
                    WHERE create_date < %s
                )
            """,
                [seven_days_ago],
            )

            count_result = cursor.fetchone()
            estimated_count = count_result[0] if count_result else 0

            # 如果数量很大，分批删除以避免锁表太久
            if estimated_count > 100000:
                logger.info(f"需要删除的记录数量较大（{estimated_count}），将分批处理")

                # 按照通知ID范围分批删除
                # 先获取最小和最大的通知ID
                cursor.execute(
                    f"""
                    SELECT MIN(id), MAX(id) FROM {notification_table}
                    WHERE create_date < %s
                """,
                    [seven_days_ago],
                )

                id_range = cursor.fetchone()
                if id_range and id_range[0] and id_range[1]:
                    min_id, max_id = id_range
                    batch_size = 50000  # 每批处理的ID范围

                    for batch_start in range(min_id, max_id + 1, batch_size):
                        batch_end = min(batch_start + batch_size - 1, max_id)

                        # 删除当前批次的数据
                        cursor.execute(
                            f"""
                            DELETE FROM {table_name}
                            WHERE notification_id IN (
                                SELECT id FROM {notification_table}
                                WHERE id BETWEEN %s AND %s
                                AND create_date < %s
                            )
                        """,
                            [batch_start, batch_end, seven_days_ago],
                        )

                        batch_deleted = cursor.rowcount
                        total_deleted += batch_deleted

                        logger.info(f"已删除ID范围 {batch_start}-{batch_end} 的 {batch_deleted} 条记录")

                        # 给数据库一点时间来释放资源
                        import time

                        time.sleep(0.5)
            else:
                # 对于较少的记录，直接删除
                cursor.execute(
                    f"""
                    DELETE FROM {table_name}
                    WHERE notification_id IN (
                        SELECT id FROM {notification_table}
                        WHERE create_date < %s
                    )
                """,
                    [seven_days_ago],
                )

                total_deleted = cursor.rowcount

        logger.info(f"清理了 {total_deleted} 条7天前的通知关联记录")
        return f"Deleted {total_deleted} old notification read status records"
    except Exception as e:
        logger.error(f"清理旧通知关联记录时出错: {str(e)}")
        return f"Error: {str(e)}"
