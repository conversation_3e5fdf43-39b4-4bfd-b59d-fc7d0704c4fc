# -*- coding: utf-8 -*-
from django.core.cache import cache
from django.db.models import Manager, QuerySet
from django_cte import CTEManager, With


class RolesManager(Manager):
    def get_corresponding_roles(self, user, user_type_code):
        filter_conditions = {
            "type_id": user_type_code,
            "is_deleted": False,
        }

        if user_type_code == "DB":
            filter_conditions["distributor"] = user.distributor
        elif user_type_code == "SP":
            filter_conditions["supplier"] = user.company

        return self.filter(**filter_conditions)


class PermissionsManager(CTEManager):
    def get_corresponding_permissions(self, user_type_code) -> QuerySet:
        """
        todo: 获取当前用户可使用的权限
        :param user_type_code:
        :return:
        """
        filter_conditions = {
            "user_types__code": user_type_code,
            "state": 1,
            "is_deleted": False,
        }
        return self.filter(**filter_conditions)

    def get_permissions_with_complete_parents(self, permissions: QuerySet | list):
        """
        获取权限和完整的父级关系
        :param permissions:
        :return:
        """

        self.get_queryset()
        if isinstance(permissions, QuerySet):
            permission_id_list = [p.pk for p in permissions]
        else:
            permission_id_list = permissions

        def make_parents_cte(cte):
            return self.model.objects.filter(id__in=permission_id_list, state=1).union(
                cte.join(self.model.objects.filter(state=1), id=cte.col.parent_id),
            )

        parent_cte = With.recursive(make_parents_cte)
        qs = parent_cte.queryset().with_cte(parent_cte)
        return qs

    def get_system_data_permissions(self, client_code, user_type_code, init: bool = False):
        """
        获取系统的数据权限
        :param user_type_code: OP/SP/DB
        :param client_code: PC/MP
        :param init:
        :return:
        """
        cache_key = f"rbac:data_perms:system_{client_code}_{user_type_code}"

        if not init:
            val = cache.get(cache_key)
            if val:
                return val

        qs = self.get_queryset().filter(
            type=4,
            codename__startswith="backend",
            user_types__code=user_type_code,
            client__code=client_code,
            state=1,
            is_deleted=False,
        )
        # 不经常变
        cache.set(cache_key, qs, None)

        return qs
