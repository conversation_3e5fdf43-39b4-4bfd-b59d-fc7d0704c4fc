# -*- coding: utf-8 -*-
from common.basic import CustomizeSerializer
from common.basics.exceptions import (
    APIViewException,
)
from rest_framework import serializers

from users.models import ClientType
from users.models_v2 import Permissions, UserType


class PermissionListSer(CustomizeSerializer):
    class Meta:
        model = Permissions
        fields = (
            "id",
            "name",
            "type",
            "state",
            "parent_id",
            "codename",
            "icon",
            "order",
            "create_date",
            "update_date",
        )


class PermissionCreateSerializer(CustomizeSerializer):
    user_types = serializers.ListField(
        child=serializers.CharField(), required=True, write_only=True
    )
    client = serializers.CharField(required=True, write_only=True)

    class Meta:
        model = Permissions
        fields = (
            "name",
            "description",
            "type",
            "parent",
            "codename",
            "icon",
            "user_types",
            "client",
            "state",
            "condition",
            "order",
        )

    def validate_user_types(self, value):
        user_types = UserType.objects.filter(code__in=value)
        if len(user_types) != len(value):
            raise serializers.ValidationError("部分用户类型不存在")
        return user_types

    def validate_client(self, value):
        try:
            return ClientType.objects.get(code=value)
        except ClientType.DoesNotExist:
            raise serializers.ValidationError("平台端类型不存在")

    def validate_parent(self, value):
        if value:
            if self.initial_data.get("type", 0) <= value.type:
                raise APIViewException(
                    err_message="父级权限类型必须大于当前权限类型"
                )
            if value.client.code != self.initial_data.get("client"):
                raise APIViewException(
                    err_message="父级权限和当前权限不在同一个平台端"
                )

            if (
                value.user_types.filter(
                    code__in=self.initial_data.get("user_types")
                ).exists()
                is False
            ):
                raise APIViewException(
                    err_message="父级权限和当前权限不在同一个用户类型"
                )
        return value

    def create(self, validated_data):
        user_types = validated_data.pop("user_types")
        current_user = self.context["request"].user

        # 设置创建者信息
        validated_data["create_user"] = current_user.user_id

        # 创建权限
        permission = Permissions.objects.create(**validated_data)

        # 添加用户类型关联
        permission.user_types.set(user_types)

        return permission


class PermissionUpdateSerializer(CustomizeSerializer):
    user_types = serializers.ListField(
        child=serializers.CharField(), required=False
    )
    client = serializers.CharField(required=False)

    class Meta:
        model = Permissions
        fields = (
            "name",
            "description",
            "type",
            "parent",
            "codename",
            "icon",
            "user_types",
            "client",
            "state",
            "condition",
            "order",
        )

    def validate_user_types(self, value):
        user_types = UserType.objects.filter(code__in=value)
        if len(user_types) != len(value):
            raise serializers.ValidationError("部分用户类型不存在")
        return user_types

    def validate_client(self, value):
        try:
            return ClientType.objects.get(code=value)
        except ClientType.DoesNotExist:
            raise serializers.ValidationError("平台端类型不存在")

    def validate_parent(self, value):
        if value:
            if self.initial_data.get("type", 0) <= value.type:
                raise serializers.ValidationError(
                    "父级权限类型必须大于当前权限类型"
                )
            # 检查是否形成循环依赖
            if self.instance and self.instance.id == value.id:
                raise serializers.ValidationError("不能将自己设为父级权限")
        return value

    def update(self, instance, validated_data):
        user_types = validated_data.pop("user_types", None)
        current_user = self.context["request"].user

        # 设置更新者信息
        validated_data["update_user"] = current_user.user_id

        # 更新权限
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新用户类型关联
        if user_types is not None:
            instance.user_types.set(user_types)

        return instance
