# -*- coding: utf-8 -*-
from django.db import models


class UserDistributorDBView(models.Model):
    user_id = models.IntegerField()
    real_name = models.CharField(max_length=255)
    mobile = models.CharField(max_length=20)
    distributor_id = models.CharField(max_length=255)
    distributor_name = models.CharField(max_length=255)
    distributor_letters = models.CharField(max_length=255)
    distributor_create_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = "user_distributor_view"  # 视图的名称
