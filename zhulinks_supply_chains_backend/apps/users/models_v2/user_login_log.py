from django.db import models


class UserLoginLog(models.Model):
    CLIENT_CHOICES = (
        ("PC", "网页"),
        ("WM", "微信小程序"),
        ("WB", "微信小程序"),
        ("MP", "微信小程序"),
    )

    USER_TYPE_CHOICES = (
        ("SP", "供应商"),
        ("OP", "运营商"),
        ("DB", "分销商"),
    )
    user_id = models.BigIntegerField(blank=False, null=False, verbose_name="用户id")
    login_time = models.DateTimeField(auto_now_add=True, verbose_name="登录时间")

    client = models.CharField(max_length=11, null=False, choices=CLIENT_CHOICES, blank=True, default="", verbose_name="登录app：PC, WM, WB")
    user_type = models.CharField(max_length=11, null=False, choices=USER_TYPE_CHOICES, blank=True, default="", verbose_name="登陆端：SP, OP, DB")
    gpu_model = models.CharField(max_length=1000, null=False, blank=True, default="", verbose_name="GPU型号")
    store = models.CharField(max_length=50, null=False, blank=True, default="", verbose_name="存储")
    app_version = models.CharField(max_length=1000, null=False, blank=True, default="", verbose_name="设备版本")
    md5_flag = models.CharField(max_length=64, blank=True, default="", verbose_name="唯一索引")

    distributor_type = models.CharField(max_length=50, null=True, blank=True, verbose_name="分销商类型")
    distributor_id = models.CharField(max_length=128, null=True, blank=True, verbose_name="分销商数据id")
    company = models.ForeignKey(
        "companies.Company",
        on_delete=models.CASCADE,
        db_constraint=False,
        null=True,
        blank=True,
        verbose_name="关联供应商",
    )

    real_name = models.CharField(null=True, blank=True, max_length=100, verbose_name="真实姓名")  #
    username = models.CharField(max_length=150, verbose_name="用户名")
    phone_number = models.CharField(max_length=20, verbose_name="手机号")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    region = models.CharField(max_length=100, verbose_name="地区")
    role_id = models.JSONField(verbose_name="角色id")
    role_name = models.JSONField(verbose_name="角色名称")
    equipment_system = models.CharField(max_length=50, verbose_name="设备系统")
    employee_status = models.CharField(max_length=50, verbose_name="员工状态")
    login_status = models.CharField(max_length=100, verbose_name="登录情况")
    create_date = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_date = models.DateTimeField(verbose_name="更新时间", auto_now=True)

    class Meta:
        verbose_name = "用户登录日志"
        verbose_name_plural = "用户登录日志"
        ordering = ["-login_time"]

    def __str__(self):
        return f"{self.username} - {self.login_time}"
