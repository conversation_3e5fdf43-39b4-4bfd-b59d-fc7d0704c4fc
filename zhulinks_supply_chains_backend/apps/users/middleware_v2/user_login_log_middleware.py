from django.utils.timezone import now
from django.utils.deprecation import MiddlewareMixin

from users.models_v2 import UserLoginLog


class UserLoginLogMiddleware(MiddlewareMixin):
    def process_response(self, request, response):
        print(1)
        print(f"is_auth:{request.user.is_authenticated}")
        # 仅在用户已通过认证时记录登录日志
        if request.user.is_authenticated:
            print(f"记录登录日志: {request.path}")
            # 检查是否是登录请求，避免重复记录
            if "/login/" in request.path:
                user = request.user
                UserLoginLog.objects.create(
                    login_time=now(),
                    client=self.get_client(request),
                    app=self.get_app(request),
                    real_name=getattr(user, "real_name", "未知"),
                    username=user.username,
                    phone_number=getattr(user, "phone_number", ""),
                    ip_address=self.get_client_ip(request),
                    region=self.get_region(request),
                    role=getattr(user, "role", "未知"),
                    system=self.get_system_info(request),
                    employee_status="正常",
                    login_status="正常",
                )

    def get_client(self, request):
        # 根据请求信息判断客户端，例如 "运营商"
        return "运营商"

    def get_app(self, request):
        # 根据请求头或路径判断登录APP
        user_agent = request.META.get("HTTP_USER_AGENT", "")
        if "MicroMessenger" in user_agent:
            return "微信小程序"
        elif "Android" in user_agent:
            return "Android App"
        elif "iPhone" in user_agent:
            return "iOS App"
        return "未知"

    def get_client_ip(self, request):
        # 获取用户的 IP 地址
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            return x_forwarded_for.split(",")[0]
        return request.META.get("REMOTE_ADDR", "")

    def get_region(self, request):
        # 假设使用第三方服务获取 IP 所属地区
        ip = self.get_client_ip(request)
        return f"地区信息({ip})"  # 示例，实际可使用 IP 地理位置查询服务

    def get_system_info(self, request):
        # 从 User-Agent 提取系统信息
        user_agent = request.META.get("HTTP_USER_AGENT", "")
        if "Android" in user_agent:
            return "Android 系统"
        elif "iPhone" in user_agent:
            return "iOS 系统"
        return "未知系统"
