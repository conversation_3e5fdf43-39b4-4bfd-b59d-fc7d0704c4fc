from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request

from common.basic import DBAPIView
from utils.http_handle import IResponse


class QuerySubscribeUserView(DBAPIView):

    def get(self, request: Request):
        print(f"user_id:{request.user.user_id}")
        user_profile = request.user.userminiprogramsubscribe_set.select_related("user").first()

        if not user_profile:
            return IResponse(code=400, msg="用户未授权")

        is_subscribe_status = user_profile.is_subscribe
        return IResponse(data={"is_subscribe_status": is_subscribe_status})