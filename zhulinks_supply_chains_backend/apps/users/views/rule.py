import copy
import traceback

from django.apps import apps
from django.contrib.auth.models import Permission
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from common.basic import CommonAPIView
from common.basics.views import set_log_params, OperateLogAPIViewMixin
from users import logger
from utils.http_handle import (
    IResponse,
    build_rule_tree,
)
from ..models import Rule
from ..serializers import RuleSerializer


class DataPermissonListView(CommonAPIView):
    """
    获取所有数据权限
    """

    resource_id_field = "id"

    # 未实现数据权限增加
    resource_name = "数据权限"
    fronted_page = "数据权限"

    def get(self, request):
        # 分销商模式先返回空列表
        if self.is_distributor_mode:
            return IResponse(data=[])


        # 不展示给前端的的app
        exclude_app = [
            "sessions",
            "messages",
            "staticfiles",
            "rest_framework",
            "drf_yasg",
            "rest_framework_simplejwt",
            "token_blacklist",
            "corsheaders",
            "django_celery_beat",
            "groupextend",
            "loggedinuser",
            "userextraauth",
            "mini_program",
        ]
        # 数据行为映射
        verb_mapping = {
            "add": "新增",
            "view": "查看",
            "delete": "删除",
            "change": "更改",
        }

        # 匹配动词
        def make_replace_verb_function(model_name, verb_mapping):
            def replace_verb(match):
                verb = match.group(1)  # 获取匹配到的动词
                replacement_verb = verb_mapping.get(verb, verb)  # 从映射中获取对应的替换词汇，如果没有则返回动词本身
                return f"{replacement_verb}{model_name}"

            return replace_verb

        try:
            permissions_list = []
            for app_config in apps.get_app_configs():  # 遍历所有已注册的应用配置
                if app_config.label in exclude_app:
                    continue
                app_data = {}
                # app_data["app_name"] = app_config.label
                # app_data["app_verbose_name"] = app_config.verbose_name
                # app_data["models"] = []
                app_data["id"] = app_config.label
                app_data["name"] = app_config.verbose_name
                app_data["subs"] = []

                # 获取应用中的所有模型
                models = app_config.get_models()
                for model in models:
                    model_data = {}
                    model_data["id"] = model._meta.model_name
                    model_verbose_name = model._meta.verbose_name.title()
                    model_data["name"] = model_verbose_name
                    permissions_qs = Permission.objects.filter(content_type__app_label=app_config.label, content_type__model=model._meta.model_name)
                    permissions = list(permissions_qs.values("id", "codename", "name"))
                    custom_permission = []
                    for i in permissions:
                        if i["codename"].split("_")[1] == model._meta.model_name:
                            # 自带的model不暴露, 改为只检查自定义的字段权限
                            pass
                            # replace_verb_with_params = make_replace_verb_function(model_verbose_name, verb_mapping)
                            # pattern = r"\bCan\b\s+(\w+).*"
                            # new_string = re.sub(pattern, replace_verb_with_params, i["name"])
                            # i["name"] = new_string
                        else:
                            custom_permission.append(i)
                    model_data["subs"] = custom_permission
                    # 有数据才加入model_data
                    if custom_permission:
                        app_data["subs"].append(model_data)
                # 有数据才加入app_data
                if app_data["subs"]:
                    permissions_list.append(app_data)
            return IResponse(data=permissions_list)
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class RuleList(CommonAPIView):
    permission_classes = [IsAuthenticated]
    resource_id_field = "id"
    resource_name = "权限"
    fronted_page = "权限管理"

    def get(self, request):
        client = request.META.get("HTTP_X_CLIENT", "PC")
        assert client in ["PC", "MP"], "client not valid"

        if self.is_distributor_mode:
            rules = Rule.objects.filter(user_types__code__in=["DB_M2"], state=1)
        else:
            rules = Rule.objects.filter(user_types__code__in=[self.current_user_type], state=1)
        tree_json = build_rule_tree(rules)
        return IResponse(data=tree_json)

    def post(self, request):
        try:
            serializer = RuleSerializer(data=request.data)
            if serializer.is_valid():
                create_object = serializer.save()

                # 日志记录
                try:
                    set_log_params(
                        request,
                        resource_id=create_object.pk,
                        model=Rule,
                        describe=f"新增了权限:{create_object.name}",
                        is_success_input=True,
                    )
                except Exception as e:
                    logger.warning(f"set_log_params catch error: {e}")
                    pass

                return IResponse(data=serializer.data)
            return IResponse(code=400, message=str(serializer.errors))
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(code=500, message=str(e))


class RuleDetail(OperateLogAPIViewMixin, APIView):
    permission_classes = [IsAuthenticated]
    resource_id_field = "id"
    resource_name = "权限"
    fronted_page = "权限管理"

    def get(self, request, id):
        try:
            rule = Rule.objects.get(pk=id)
        except Rule.DoesNotExist:
            return IResponse(data="data not found")
        serializer = RuleSerializer(rule)
        return IResponse(data=serializer.data)

    def put(self, request, id):
        try:
            rule = Rule.objects.get(pk=id)
        except Rule.DoesNotExist:
            return IResponse(data="data not found")
        # 日志使用对比对象
        raw_object = copy.deepcopy(rule)
        serializer = RuleSerializer(rule, data=request.data)
        if serializer.is_valid():
            update_object = serializer.save()

            # 日志记录
            try:
                set_log_params(
                    request,
                    resource_id=id,
                    model=Rule,
                    describe=f"修改了权限:{rule.name}",
                    raw_object=raw_object,
                    new_object=update_object,
                    is_success_input=True,
                )
            except Exception as e:
                logger.warning(f"set_log_params catch error: {e}")
                pass

            return IResponse(data=serializer.data)
        return IResponse(code=400, message=str(serializer.errors))

    def delete(self, request, id):
        try:
            rule = Rule.objects.get(pk=id)
        except Rule.DoesNotExist:
            return IResponse(data="data not found")
        rule.delete()

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=id,
                model=Rule,
                describe=f"删除了权限:{rule.name}",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")
            pass

        return IResponse(code=200)
