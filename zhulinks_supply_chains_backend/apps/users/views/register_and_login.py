# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-05-26 11:26:56
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-12-22 10:30:17
import secrets
import traceback
from datetime import datetime

from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.models import Permission
from django.db import transaction
from django.db.models import Q
from django.db.utils import IntegrityError
from django.utils import timezone
from rest_framework import exceptions
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework_simplejwt.exceptions import TokenError
from rest_framework_simplejwt.views import TokenRefreshView

from common.basic import CommonAPIView
from common.basics.views import set_log_params, operate_log_decorator
from common.basics.exceptions import APIViewException
from common.tasks import gen_login_device
from companies.models import Company
from companies.serializers import CompanySerializers
from companies.tasks import sync_supplier_to_JST
from products.models import ProductSelectionPlan
from users import logger
from utils.http_handle import (
    IResponse,
    verification_code_is_valid,
    FieldsError,
)
from utils.redis_lock import gen_redis_cache
from utils.redis_lock import release_lock
from utils.yasg import user_swagger
from ..authentication import add_jwt_to_blacklist
from ..models import (
    User,
    LoggedInUser,
    UserType,
    UserDistributorRelationShip,
)
from ..serializers import (
    UserSerializer,
    CompanyAndUserUpdateSerializer,
    MyTokenRefreshSerializer,
)
from ..utils import generate_basic_jwt_token


class MobileorUsernameModelBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        UserModel = get_user_model()
        try:
            # user = UserModel.objects.get(
            #     (Q(username__iexact=username) | Q(mobile__iexact=username)),
            #     user_type="OP",
            # )
            user = UserModel.objects.get(
                Q(username__iexact=username) | Q(mobile__iexact=username),
            )

        except UserModel.DoesNotExist:
            return None
        else:
            if user.check_password(password):
                return user
        return None

    def _get_group_permissions(self, user_obj):
        perm_id_list = []
        user_groups = user_obj.groups.all()
        if user_groups:
            for _group in user_groups:
                _ids = _group.permissions.values_list("id", flat=True)
                perm_id_list.extend(_ids)
        perms = Permission.objects.filter(id__in=perm_id_list)
        return perms


class SupplierRegisterView(APIView):
    """
    供应商入驻与入驻信息更新
    """

    permission_classes = [AllowAny]

    def post(self, request):
        try:
            if not verification_code_is_valid(request, send_type="SP-RG"):
                return IResponse(message="verificode is incorrect", code=400, status=status.HTTP_200_OK)

            raw_data = request.data
            mobile = request.data.get("mobile")
            assert mobile, "mobile required"
            existed_user = User.objects.filter(mobile=mobile).first()
            if existed_user and existed_user.user_types.filter(code="SP").first():
                return IResponse(code=400, message="mobile of the user_type existed")

            if existed_user:
                if existed_user.user_types.filter(code="SP").first():
                    return IResponse(code=400, message="mobile of the user_type existed")
                else:
                    # 已经存在其他平台类型，不可在此修改以下字段
                    if raw_data.get("real_name"):
                        raw_data.pop("real_name")
                    if raw_data.get("username"):
                        raw_data.pop("username")
                    if raw_data.get("password"):
                        raw_data.pop("password")
            try:
                with transaction.atomic():
                    company = self.create_company(raw_data)
                    user = self.create_user(raw_data, company)
            except ValueError as e:
                logger.error(str(e))
                return IResponse(code=400, message=str(e))
            except IntegrityError as e:
                logger.error(str(e))
                return IResponse(code=500, message=str(e))

            user_data = UserSerializer(instance=user)
            user_data = dict(user_data.data)
            user_data.pop("password")
            user_data.pop("id")
            user_data.pop("company")

            company_data = CompanySerializers(instance=company)
            data = dict(company_data.data)
            data = {f"company_{k}": v for k, v in data.items() if k != "company_id"}
            data["company_id"] = company.company_id
            data["company_state"] = company.state
            data["company_state_reason"] = company.state_reason
            data.update(user_data)

            # 同步数据至聚水潭
            sync_supplier_to_JST.delay(company.company_id)

            return IResponse(data=data, status=status.HTTP_200_OK)

        except IntegrityError as e:
            logger.error(str(e))
            return IResponse(message=str(e), code=500)
        except Exception as e:
            logger.error(f"{str(e)}--{traceback.format_exc()}")
            return IResponse(message=str(e), code=400, status=status.HTTP_200_OK)

    @staticmethod
    def create_company(raw_data):
        company_data = {k[8:]: v for k, v in raw_data.items() if k.startswith("company_")}
        company_data["role"] = "SP"
        company = CompanySerializers(data=company_data)
        if company.is_valid():
            instance = company.save(role="SP")
            return instance
        raise FieldsError(company.errors)

    @staticmethod
    def create_user(raw_data, company):
        user_data = {k: v for k, v in raw_data.items() if not k.startswith("company_")}
        user_data["company"] = company.id
        user_type = UserType.objects.get(code="SP")
        mobile = user_data.get("mobile")
        try:
            instance = User.objects.get(mobile=mobile)
        except User.DoesNotExist:
            # 如果模型实例不存在，创建一个新的实例
            instance = None
        if not instance or not instance.user_types.filter(code="SP").first():
            user_data["is_founder"] = True
        # 用户类型
        user_data["user_type"] = "SP"
        user_data["is_founder"] = True
        user = UserSerializer(instance, data=user_data, partial=True)
        if user.is_valid():
            instance = user.save()
            if not instance.user_types.filter(code="SP").first():
                instance.user_types.add(user_type)
                # instance.save()
            return instance

        raise FieldsError(user.errors)

    @user_swagger(request_body=CompanyAndUserUpdateSerializer, operation_summary="供应商注册-更新")
    # @transaction.atomic
    def patch(self, request):
        """
        供应商入驻-信息更新, 只更新供应商信息
        """
        try:
            user_id = request.data.get("user_id")
            company_id = request.data.get("company_id")
            assert user_id is not None and company_id is not None, "user and company required"

            user_qs = User.objects.filter(user_id=user_id)
            company_qs = Company.objects.filter(company_id=company_id)
            if not user_qs.first() or not company_qs.first():
                return IResponse(code=400, message="invalid user or company")

            if user_qs.first().company.company_id != company_qs.first().company_id:
                logger.error(f"user{user_id} not match company {company_id}")
                return IResponse(code=400, message="user not match company")

            user = user_qs.first()
            company_data = self.update_company(request)
            company_qs.update(**company_data)

            data = {f"company_{k}": v for k, v in company_data.items() if k != "company_id"}
            data["company_id"] = company_id
            data["username"] = user.username
            data["mobile"] = user.mobile

            # 同步数据至聚水潭
            sync_supplier_to_JST.delay(company_id)

            return IResponse(data=data)
        except AssertionError as e:
            return IResponse(message=str(e), code=400)
        except Exception as e:
            logger.error(e)
            return IResponse(message=str(e), code=500)

    @staticmethod
    def update_company(request):
        company_data = {k[8:]: v for k, v in request.data.items() if k.startswith("company_")}
        company_id = company_data.pop("id")
        if company_data.get("role"):
            company_data.pop("role")
        company_data["state_reason"] = None
        company_data["state"] = 0
        company_data["company_id"] = company_id
        company_data["update_date"] = datetime.strftime(timezone.now(), "%Y-%m-%dT%H:%M:%S.%f%z")
        return company_data

    @staticmethod
    def update_user(request):
        user_data = {k: v for k, v in request.data.items() if not k.startswith("company_")}
        user_data["update_date"] = datetime.strftime(timezone.now(), "%Y-%m-%dT%H:%M:%S.%f%z")
        if user_data.get("verification_code"):
            user_data.pop("verification_code")
        return user_data


@api_view(["POST"])
@permission_classes([AllowAny])
def account_login(request, user_type):
    """
    用户-账号登录
    user_type:
      - supplier: 供应商
      - operator：运营商
      - distributor: 分销商
    """
    try:
        assert user_type in ("supplier", "operator", "distributor"), "invalid url"
        user_type_map = {
            "supplier": "SP",
            "operator": "OP",
            "distributor": "DB",
        }
        is_self = request.GET.get("is_self", "0")
        user_type = user_type_map.get(user_type)
        client = request.META.get("HTTP_X_CLIENT", "PC")
        assert client in ["PC", "MP"], "client not valid"

        username = request.data.get("username")
        password = request.data.get("password")
        assert username is not None and password is not None, "invalid username or password"
        logger.info(f"account login begin: {username}")
        try:
            user = User.objects.get(
                (Q(username__exact=username) | Q(mobile__exact=username)),
                user_types__code=user_type,
            )
        except User.DoesNotExist:
            return IResponse(
                message="User does not exist or password is incorrect",
                code=400,
                status=status.HTTP_200_OK,
            )

        logger.info(f"account login: {username}, will check_password")
        if not user.check_password(password):
            return IResponse(
                message="User does not exist or password is incorrect",
                code=400,
                status=status.HTTP_200_OK,
            )
        if user_type == "SP":
            logger.info(f"account login: {username}, will login_and_check_company_state")
            re_response = login_and_check_company_state(user, client, user_type)
        else:
            re_response = get_token(user, client, user_type, is_self=is_self)
        # 动态给分销商用户登录设置分销商
        gen_login_device(request, user, user_type)
        return re_response
    except APIViewException as e:
        return IResponse(code=e.err_code, message=str(e.err_message))
    except AssertionError as e:
        return IResponse(message=str(e), code=400, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"{e}, username: {username}")
        return IResponse(message=str(e), code=500, status=status.HTTP_200_OK)


@api_view(["POST"])
@permission_classes([AllowAny])
def mobile_login(request, user_type):
    """
    手机号登录
    user_type:
      - supplier: 供应商
      - operator：运营商
    """
    try:
        user_type_map = {
            "supplier": "SP",
            "operator": "OP",
            "distributor": "DB",
        }
        client = request.META.get("HTTP_X_CLIENT", "PC")
        if user_type not in user_type_map:
            return IResponse(code=400, message="invalid url")
        if client not in ["PC", "MP"]:
            return IResponse(code=400, message="client not valid")
        mobile = request.data.get("mobile")
        is_self = request.GET.get("is_self", "0")
        if not mobile:
            return IResponse(code=400, message="invalid mobile")
        inner_user_type = user_type_map.get(user_type)

        # 先校验登录验证码
        if not verification_code_is_valid(request, send_type=f"{inner_user_type}-LG"):
            return IResponse(
                message="verificode is incorrect",
                code=400,
                status=status.HTTP_200_OK,
            )
        # 普通用户手机号登录校验
        if is_self == "0":
            try:
                user = User.objects.get(mobile=mobile, user_types__code=inner_user_type)
            except User.DoesNotExist:
                # return IResponse(message="User does not exist, please register", code=400, status=status.HTTP_200_OK)
                return IResponse(message="号码非内部邀请注册账号，无权查看", code=400, status=status.HTTP_200_OK)
        else:
            # 自营分销商，自动创建角色
            user = User.objects.get_or_create_self_distributor_user(mobile)

        if inner_user_type == "SP":
            return login_and_check_company_state(user, client, inner_user_type)
        else:
            return get_token(user, client, inner_user_type, is_self=is_self)
    except APIViewException as e:
        return IResponse(code=400, message=str(e))
    except AssertionError as e:
        return IResponse(message=str(e), code=400, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"{e}, mobile: {mobile}")
        return IResponse(message=str(e), code=500, status=status.HTTP_200_OK)


def _get_token(user, client, user_type, **kwargs):
    # 冻结用户
    if not user.is_active:
        raise APIViewException(err_message="用户已被冻结,请联系管理员")
    extra = {}
    # 是否为自营分销商登录
    is_self = kwargs.pop("is_self", "0")
    if user_type == "DB":
        # 正常分销商登录
        if is_self == "0":
            if not user.distributor:
                # 非自营的优先
                relation = UserDistributorRelationShip.objects.filter(user=user, distributor__is_self_support=False).first()
                if relation:
                    extra["distributor_id"] = relation.distributor_id

                    # 设置分销商
                    user.distributor = relation.distributor

                else:
                    relation = UserDistributorRelationShip.objects.filter(user=user).first()
                    if relation:
                        extra["distributor_id"] = relation.distributor_id

                        user.distributor = relation.distributor

            elif user.distributor.is_self_support:
                # 非自营的优先
                relation = UserDistributorRelationShip.objects.filter(user=user, distributor__is_self_support=False).first()
                if relation:
                    extra["distributor_id"] = relation.distributor_id
                    user.distributor = relation.distributor
                else:
                    relation = UserDistributorRelationShip.objects.filter(user=user).first()
                    if relation:
                        extra["distributor_id"] = relation.distributor_id
                        user.distributor = relation.distributor
        else:
            # 臻品端登录
            if user.distributor:
                if user.distributor.is_self_support:
                    extra["distributor_id"] = user.distributor.distributor_id
                else:
                    relation = UserDistributorRelationShip.objects.filter(user=user, distributor__is_self_support=True).first()
                    if not relation:
                        raise APIViewException(err_message="用户暂无珠凌臻品商城权限，请联系管理员")
                    extra["distributor_id"] = relation.distributor_id
                    user.distributor = relation.distributor
            else:
                relation = UserDistributorRelationShip.objects.filter(user=user, distributor__is_self_support=True).first()
                if not relation:
                    raise APIViewException(err_message="用户暂无珠凌臻品商城权限，请联系管理员")
                extra["distributor_id"] = relation.distributor_id
                user.distributor = relation.distributor
    elif user_type == "SP":
        if not user.company:
            raise APIViewException(code=400, err_message="user with no company")

        if user.company.is_deleted:
            raise APIViewException(code=400, err_message="公司已被禁用,请联系管理员")

    if kwargs:
        extra.update(**kwargs)

    data = generate_basic_jwt_token(user, user_type, client, extra)

    return data


def get_token(user, client, user_type, **kwargs):
    """
    验证无误后，获取token
    """
    try:
        data = _get_token(user, client, user_type, **kwargs)
        return IResponse(data=data, status=status.HTTP_200_OK)
    except exceptions.AuthenticationFailed as e:
        return IResponse(code=403, message=str(e))


def login_and_check_company_state(user, client, user_type):
    """
    根据用户的公司状态，返回不同响应：
    1.公司状态为审核通过（1），返回token
    2.公司状态为待审核（0）或审核不通过（2），返回用户和公司信息
    """
    logger.info(f"account login: {user.username}, login_and_check_company_state")

    # 如果没有供应商，需要重定向到补充信息页面
    data = generate_basic_jwt_token(user, user_type="SP", client=client)
    if not user.company_id:
        return IResponse(code=1001, data=data)

    try:
        company = Company.objects.get(pk=user.company_id)
    except Company.DoesNotExist:
        return IResponse(message="invalid user, not bound company", code=400, status=status.HTTP_200_OK)

    if company.state != 1:
        return IResponse(code=1002, data=data)

    # try:
    #     company = Company.objects.get(pk=user.company.id)
    # except Company.DoesNotExist:
    #     return IResponse(message="invalid user, not bound company", code=400, status=status.HTTP_200_OK)

    # if company.state == 1:
    #     # todo: cache.set(f"logged_in_user_refresh:{user.id}", str(refresh))
    #     logger.info(f"account login: {user.username}, RefreshToken begin")
    #     data = _get_token(user, client, user_type)
    #     data["company_state"] = company.state
    #     logger.info(f"account login: {user.username}, RefreshToken end")
    # else:
    #     user_data = UserSerializer(instance=user)
    #     user_data = dict(user_data.data)
    #     user_data.pop("password")
    #
    #     company_data = CompanySerializers(instance=company)
    #     data = dict(company_data.data)
    #     data = {f"company_{k}": v for k, v in data.items() if k != "company_id"}
    #     data["company_id"] = company.company_id
    #     data["company_state"] = company.state
    #     data["company_state_reason"] = company.state_reason
    #     data.update(user_data)
    return IResponse(data=data, status=status.HTTP_200_OK)


class MyTokenRefreshView(TokenRefreshView):
    """
    自定义刷新token
    """

    serializer_class = MyTokenRefreshSerializer

    @user_swagger(operation_summary="刷新token")
    def post(self, request, *args, **kwargs):
        client = request.META.get("HTTP_X_CLIENT", "PC")
        assert client in ["PC", "MP"], "client not valid"

        serializer = self.get_serializer(client, data=request.data, context={"client": client})

        try:
            serializer.is_valid(raise_exception=True)
        except (TokenError, ValidationError) as e:
            return IResponse(code=401, message="Token is invalid or expired")

        data = serializer.validated_data
        # data.pop("refresh")
        return IResponse(data=data, status=status.HTTP_200_OK)


class LogoutView(CommonAPIView):

    @user_swagger(operation_summary="供应商-账号登出")
    def post(self, request):
        try:

            client = request.META.get("HTTP_X_CLIENT", "PC")
            assert client in ["PC", "MP"], "client not valid"
            current_user_id = self.current_user.user_id

            # 删除记录，在refresh view中判断是否token过期
            LoggedInUser.objects.filter(
                user=self.current_user,
                user_type=self.current_user_type,
                client=client,
            ).delete()

            # 清除redis中修改的状态
            # 优化:不查询数据库,使用redis.keys("lock:*")遍历数据库key并删除
            plans = ProductSelectionPlan.objects.filter(state__in=[1, 2]).only("plan_id")
            for plan in plans:
                key = plan.plan_id

                plan_state_release_res = release_lock(key, current_user_id)

                map_state_key = f"plan_map:{plan.plan_id}"
                plan_map_state_release_res = release_lock(map_state_key, current_user_id)
                # logger.info(f">>user:{current_user_id} logout. Release {key}:{plan_state_release_res},map:{plan_map_state_release_res}")
            # 添加到redis黑名单
            add_jwt_to_blacklist(request)
            return IResponse()
        except Exception as e:
            logger.error(e)
            return IResponse(code=400)


@operate_log_decorator("修改密码", "修改密码")
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_password_view(request, user_type):
    try:
        assert user_type in ("supplier", "operator", "distributor"), "invalid url"
        user_type_map = {
            "supplier": "SP",
            "operator": "OP",
            "distributor": "DB",
        }
        user_type = user_type_map.get(user_type)
        mobile = request.data.get("mobile")
        assert mobile is not None, "invalid mobile"

        if not verification_code_is_valid(request, send_type=f"{user_type}-UP"):
            return IResponse(
                message="verificode is incorrect",
                code=400,
            )
        User.objects.get(mobile=mobile, user_types__code__in=[user_type])
        token = generate_secure_token()
        token_value = f"{mobile}:{user_type}"
        key = f"reset_token:{token}"

        cache = gen_redis_cache()
        cache.set(key, token_value, 300)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=mobile,
                model=None,
                describe=f"修改密码{user_type}:{mobile}",
                operate_content="修改密码",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")

        return IResponse(data={"token": token})

    except User.DoesNotExist:
        return IResponse(code=400, message="User does not exist")


@operate_log_decorator("用户修改手机号", "用户修改手机号")
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_phone_view(request, user_type):
    try:
        assert user_type in ("supplier", "operator", "distributor"), "invalid url"
        user_type_map = {
            "supplier": "SP",
            "operator": "OP",
            "distributor": "DB",
        }
        user_type = user_type_map.get(user_type)
        mobile = request.data.get("mobile")
        assert mobile is not None, "invalid mobile"
        token = request.data.get("token")
        assert token, "请输入正确的手机验证码"
        key = f"reset_token:{token}"
        cache = gen_redis_cache()
        token_value = cache.get(key)
        if not token_value:
            if request.headers.get("X-Client", "") != "MP":
                return IResponse(code=400, message="手机验证码已过期，请重新获取")
            else:
                return IResponse(code=400, message="旧手机验证码已过期，需返回上一级重新获取")

        _mobile = token_value.split(":")[0]
        _user_type = token_value.split(":")[1]

        if user_type != _user_type:
            return IResponse(code=400, message="user_type not match")

        if _mobile == mobile:
            return IResponse(code=400, message="新旧手机号一致，请更换新手机号")

        if not verification_code_is_valid(request, send_type=f"{user_type}-UP"):
            return IResponse(
                message="新手机验证码错误",
                code=400,
            )

        user = User.objects.get(mobile=_mobile, user_types__code__in=[user_type])
        user.mobile = mobile
        user.save()
        # 删除缓存
        cache.delete(key)
        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=mobile,
                model=None,
                describe=f"修改手机号{user_type}:{mobile}",
                operate_content="修改手机号",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")

        return IResponse()

    except User.DoesNotExist:
        return IResponse(code=400, message="User does not exist")


@operate_log_decorator("忘记密码", "忘记密码")
@api_view(["POST"])
@permission_classes([AllowAny])
def forgot_password_view(request, user_type):
    try:
        assert user_type in ("supplier", "operator", "distributor"), "invalid url"
        user_type_map = {
            "supplier": "SP",
            "operator": "OP",
            "distributor": "DB",
        }
        user_type = user_type_map.get(user_type)
        mobile = request.data.get("mobile")
        assert mobile is not None, "invalid mobile"

        if not verification_code_is_valid(request, send_type=f"{user_type}-FG"):
            return IResponse(
                message="verificode is incorrect",
                code=400,
            )
        User.objects.get(mobile=mobile, user_types__code__in=[user_type])
        token = generate_secure_token()
        token_value = f"{mobile}:{user_type}"
        key = f"reset_token:{token}"

        cache = gen_redis_cache()
        cache.set(key, token_value, 300)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=mobile,
                model=None,
                describe=f"忘记密码{user_type}:{mobile}",
                operate_content="忘记密码",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")

        return IResponse(data={"token": token})

    except User.DoesNotExist:
        return IResponse(code=400, message="User does not exist")


@operate_log_decorator("重置密码", "重置密码")
@api_view(["POST"])
@permission_classes([AllowAny])
def reset_password_view(request, user_type):
    try:
        assert user_type in ("supplier", "operator", "distributor"), "invalid url"
        user_type_map = {
            "supplier": "SP",
            "operator": "OP",
            "distributor": "DB",
        }
        user_type = user_type_map.get(user_type)

        raw_data = request.data
        token = raw_data.get("token")
        password = raw_data.get("password")
        assert token and password, "token and password required"

        key = f"reset_token:{token}"
        cache = gen_redis_cache()
        token_value = cache.get(key)
        if not token_value:
            return IResponse(code=400, message="Token is invalid or expired")

        _mobile = token_value.split(":")[0]
        _user_type = token_value.split(":")[1]

        if user_type != _user_type:
            return IResponse(code=400, message="user_type not match")

        user = User.objects.get(mobile=_mobile, user_types__code__in=[user_type])
        if user.check_password(password):
            return IResponse(code=400, message="新旧密码不能一致")
        user.set_password(password)
        user.save()
        # 删除缓存
        cache.delete(key)

        # 日志记录
        try:
            set_log_params(
                request,
                resource_id=_mobile,
                model=None,
                describe=f"重置密码{user_type}:{_mobile}",
                operate_content="重置密码",
                is_success_input=True,
            )
        except Exception as e:
            logger.warning(f"set_log_params catch error: {e}")

        return IResponse()
    except User.DoesNotExist:
        return IResponse(code=400, message="User does not exist")


def generate_secure_token():
    return secrets.token_hex(16)
