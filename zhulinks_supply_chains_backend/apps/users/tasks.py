import json

from django.core.cache import cache
from django.db import transaction
from django.db.models import Q, Count, Sum
from django.db.utils import IntegrityError
from django.utils import timezone

from common.models import get_feishu_table_config_info
from order_server.models import OrderItems, Order, AfterSales, OrderOtherInfo
from users import logger
from users.models import SystemAnnouncement, Notification, NotificationReadStatus, User, Group, LoggedInUser, UserDistributorRelationShip
from users.tasks_v2.user_login_log_tasks import user_login_log_task
from utils.feishu import FeiShuDocx
from utils.redis_lock import redis_conn
from zhulinks_supply_chains_backend.celery import app


@app.task(queue="common_tasks")
def publish_if_effective():
    # 按生效时间发布公告
    sa_qs = SystemAnnouncement.objects.filter(status="PD", effective_time__lte=timezone.now())
    if sa_qs:
        sa_qs.update(status="PB")


@app.task(queue="common_tasks")
def ceate_notifications(data, user_type_list):
    """
    创建消息通知
    """
    try:
        company_id = data.get("company_id")
        # assert company_id, "company_id required"

        query = Q(is_deleted=False)
        if data.get("user_ids", []):
            query = query & Q(user_id__in=data["user_ids"])
        if "SP" in user_type_list:
            sp_query = Q(user_types__code="SP", company__company_id=company_id)
            other_query = Q(user_types__code__in=[ut for ut in user_type_list if ut != "SP"])
            query = query & (sp_query | other_query)
        elif "SELF_DB" in user_type_list:
            self_db_query = Q(user_types__code="DB")
            # other_query = Q(user_types__code__in=[ut for ut in user_type_list if ut != "SELF_DB"])
            query = query & self_db_query
        else:
            query = query & Q(user_types__code__in=user_type_list)
        users = User.objects.filter(query).distinct()
        with transaction.atomic():
            data.pop("user_ids", None)
            notification = Notification.objects.create(**data)
            user_notifications = [NotificationReadStatus(user=user, notification=notification) for user in users]
            if user_notifications:
                NotificationReadStatus.objects.bulk_create(user_notifications)
    except ValueError as e:
        logger.error(f"ceate_notifications err: {str(e)}-{data}")
        return f"Failed: {str(e)}"
    except IntegrityError as e:
        logger.error(f"ceate_notifications err: {str(e)}-{data}")
        return f"Failed: {str(e)}"
    except Exception as e:
        logger.error(f"ceate_notifications err: {str(e)}-{data}")
        return f"Failed: {str(e)}"
    return "Success"


@app.task(queue="common_tasks")
def feichu_dimission_feactivate_account(data):
    """
    飞书离职禁用账户
    """
    try:
        mobile = data["event"]["object"]["mobile"]
        name = data["event"]["object"]["name"]
        open_id = data["event"]["object"]["open_id"]
        if "+86" in mobile:
            mobile = mobile.replace("+86", "")
        if " " in mobile:
            mobile = mobile.replace(" ", "")
        try:
            user = User.objects.get(mobile=mobile)
            user.state = 2
            user.save()
            cli = FeiShuDocx()
            cli.batch_send_user_dimission_message(f"{name}已离职，并已冻结珠凌账户, 手机号为：{mobile}")
            logger.info(f"订阅飞书离职，处理成功，员工：{name}，mobile：{mobile}")
        except User.DoesNotExist:
            cli = FeiShuDocx()
            cli.batch_send_user_dimission_message(f"{name}已离职，无珠凌账户, 手机号为：{mobile}")
            logger.warning(f"员工：{name} 在系统中不存在，手机号为：{mobile}，飞书open_id：{open_id}")
    except ValueError as e:
        logger.error(f"feichu_dimission_feactivate_account err: {str(e)}-{data}")


@app.task(queue="common_tasks")
def cache_show_cost_price_user_permission():
    """
    在列表内的用户创建的品
    别人看不到推广价和供应商
    运营商也不能改他的价
    :return:
    """
    from users.models_v2 import Roles

    roles = Roles.objects.filter(
        type="OP",
        permissions__codename="backend:cost_price_supplier_not_display",
        permissions__state=1,
        is_deleted=False,
    ).values("id", "permissions__client__code")

    diff_roles = {}
    for role in roles:
        client_type = role["permissions__client__code"]
        if client_type not in diff_roles:
            diff_roles[client_type] = [role["id"]]
        else:
            diff_roles[client_type].append(role["id"])

    permission_prefix = "permission:show_cost_price_user_permission"
    if not diff_roles:
        cache.delete(f"{permission_prefix}:PC")
        cache.delete(f"{permission_prefix}:MP")
    else:
        for client_type, role_ids in diff_roles.items():
            user_id_list = list(User.objects.filter(roles__in=role_ids).values_list("user_id", flat=True))
            cache.set(f"{permission_prefix}:{client_type}", user_id_list, 60 * 60 * 24)

    try:
        group_objs = (
            Group.objects.prefetch_related("permissions")
            .filter(
                permissions__codename__in=[
                    "show_cost_price_StockKeepingUnit",
                    "show_max_cost_price_Product",
                    "show_min_cost_price_Product",
                ],
                is_deleted=False,
                company__isnull=True,
                distributor__isnull=True,
            )
            .values_list("id", flat=True)
        )
        if not group_objs:
            redis_conn.delete("show_cost_price_user_permission")
            return "deleted"

        group_ids = set(group_objs)
        user_ids = list(User.objects.filter(groups__id__in=group_ids).values_list("user_id", flat=True))
        redis_conn.set("show_cost_price_user_permission", json.dumps(user_ids), 60 * 60 * 24)
    except ValueError as e:
        logger.error(f"cache_show_cost_price_user_permission err: {str(e)}")
        return "Failed"

    return "Flushed"


@app.task(queue="common_tasks")
def flush_op_cannot_modify_cost_price_permission_cache():
    """
    刷新运营商无法修改推广价缓存(待定使用)
    :return:
    """
    try:
        group_objs = Group.objects.prefetch_related("permissions").filter(
            permissions__codename__in=[
                "show_cost_price_StockKeepingUnit",
                "show_max_cost_price_Product",
                "show_min_cost_price_Product",
            ]
        )

        filter_conditions = {
            "groups__in": group_objs,
            "groups__is_deleted": False,
            "company__is_null": True,
            "distributor__is_null": True,
        }
        user_ids = list(User.objects.filter(filter_conditions).values_list("user_id", flat=True))
        # 保留一天
        redis_conn.set("cannot_modify_cost_price_user_list", json.dumps(user_ids), 60 * 60 * 24)
    except ValueError as e:
        logger.error(f"cannot_modify_cost_price_user_list err: {str(e)}")


@app.task(queue="common_tasks")
def add_user_login_log(device_info):
    try:
        user_login_log_task(device_info)
    except Exception as e:
        logger.error(f"add_user_login_log err: {device_info['user_id']}登陆日志报错：{str(e)}")


@app.task(queue="common_tasks")
def sync_distributor_login_info_task():
    table_id, table_token = get_feishu_table_config_info("db_login_info")

    if not table_id or not table_token:
        table_id = "tbl9mzC69DJB8Qz1"
        table_token = "OWXYb4y9rahhmEs7dPzcTI0Ynrt"

    feishu_client = FeiShuDocx()

    post_data_list = []

    def get_user_login_info(user):
        last_login_record = LoggedInUser.objects.filter(user_id=user.pk, user_type="DB").order_by("-last_login_time").first()

        last_login_time = None
        client = ""
        if last_login_record:
            last_login_time = int(last_login_record.last_login_time.timestamp() * 1000)
            client = last_login_record.get_client_display()

        return last_login_time, client

    def get_user_order_aggregates(user) -> (dict, float):
        items = (
            OrderItems.objects.filter(
                order_type__in=[99, 100],
                create_user=user.user_id,
            )
            .filter(
                Q(Q(ship_time__isnull=True) & Q(order_status="RD")) | Q(ship_time__isnull=False),
            )
            .values("id", "relate_order_id", "parent_ex_order_id")
        )

        if not items:
            return {"order_count": 0, "product_count": 0}, 0

        oi_ids = []
        relate_order_ids = set()
        relate_ex_order_ids = set()
        for i in items:
            oi_ids.append(i["id"])
            relate_order_ids.add(i["relate_order_id"])
            relate_ex_order_ids.add(i["parent_ex_order_id"])

        _aggregate_data = OrderItems.objects.filter(id__in=oi_ids).aggregate(
            order_count=Count("relate_order", distinct=True),
            product_count=Sum("item_num"),
        )

        total_amount = Order.objects.filter(order_id__in=relate_order_ids).aggregate(total_amount=Sum("paid_amount"))["total_amount"]

        refund_amount = AfterSales.objects.filter(refund_state=3, ex_order_id__in=relate_ex_order_ids).aggregate(total_as_pay_amount=Sum("as_pay_amount"))["total_as_pay_amount"]

        return _aggregate_data, total_amount - (refund_amount or 0)

    def get_ktt_fx_aggregates(user) -> dict:
        items = (
            OrderItems.objects.filter(
                order_type__in=[99, 100],
                create_user=user.user_id,
            )
            .filter(
                Q(Q(ship_time__isnull=True) & Q(order_status="RD")) | Q(ship_time__isnull=False),
            )
            .values("id", "relate_order_id", "parent_ex_order_id")
        )
        if not items:
            return {}

        oi_ids = []
        relate_order_ids = set()
        relate_ex_order_ids = set()
        for i in items:
            oi_ids.append(i["id"])
            relate_order_ids.add(i["relate_order_id"])
            relate_ex_order_ids.add(i["parent_ex_order_id"])

        ktt_leaders = {}
        infors = OrderOtherInfo.objects.filter(order_id__in=relate_order_ids)
        for info in infors:
            leader_name = info.order_tag.get("ktt_leader_name") or "未知团长"

            if leader_name not in ktt_leaders:
                ktt_leaders[leader_name] = [info.order_id]
            else:
                ktt_leaders[leader_name].append(info.order_id)
        re_data = {}
        # 快团团
        for leader_name, order_id_list in ktt_leaders.items():
            _aggregate_data = OrderItems.objects.filter(id__in=oi_ids, relate_order_id__in=order_id_list).aggregate(
                order_count=Count("relate_order", distinct=True),
                product_count=Sum("item_num"),
            )
            total_amount = Order.objects.filter(order_id__in=order_id_list).aggregate(total_amount=Sum("paid_amount"))["total_amount"]

            refund_amount = AfterSales.objects.filter(refund_state=3, ex_order_id__order_id__in=order_id_list).aggregate(total_as_pay_amount=Sum("as_pay_amount"))[
                "total_as_pay_amount"
            ]

            re_data[leader_name] = {
                "aggregate_data": _aggregate_data,
                "amount_total": total_amount - (refund_amount or 0),
            }

        return re_data

    def insert_feishu_table(user, distributor):
        last_login_time, client = get_user_login_info(user)
        # 快团团分销
        if distributor.distributor_id == 148659610:
            re_data = get_ktt_fx_aggregates(user)
            for leader_name, value in re_data.items():
                aggregate_data = value["aggregate_data"]
                total_amount = value["amount_total"]
                post_data = {
                    "手机号": int(user.mobile),
                    "分销商名称": leader_name,
                    "注册时间": int(distributor.create_date.timestamp() * 1000) if distributor.create_date else None,
                    "来源": "快团团分销",
                    "最后登录时间": last_login_time,
                    "登录端": client,
                    "订单数量": aggregate_data["order_count"] or 0,
                    "商品数量": aggregate_data["product_count"] or 0,
                    "总金额": round(float(total_amount), 2) if total_amount else 0,
                }
                post_data_list.append(post_data)
        else:
            aggregate_data, total_amount = get_user_order_aggregates(user)
            post_data = {
                "手机号": int(user.mobile),
                "分销商名称": distributor.name,
                "注册时间": int(distributor.create_date.timestamp() * 1000) if distributor.create_date else None,
                "来源": "珠凌分销",
                "最后登录时间": last_login_time,
                "登录端": client,
                "订单数量": aggregate_data["order_count"] or 0,
                "商品数量": aggregate_data["product_count"] or 0,
                "总金额": round(float(total_amount), 2) if total_amount else 0,
            }
            post_data_list.append(post_data)

    # Process direct distributor users
    users = User.objects.filter(distributor__distributor_mode=2, query_all=True)

    for user in users:
        insert_feishu_table(user, user.distributor)

    # Process relationship users
    relationships = UserDistributorRelationShip.objects.filter(distributor__distributor_mode=2, user__user_types__code="DB")
    for relationship in relationships:
        insert_feishu_table(relationship.user, relationship.distributor)

    insert_resp = feishu_client.batch_insert_table_records(table_token, table_id, post_data_list)
    print(insert_resp)
