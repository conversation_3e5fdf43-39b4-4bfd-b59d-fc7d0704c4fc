# Generated by Django 4.2.1 on 2024-01-11 06:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0037_group_unique_group_name_distributor"),
    ]

    operations = [
        migrations.CreateModel(
            name="Rule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="名称")),
                ("is_menu", models.BooleanField(default=False, verbose_name="是否为菜单")),
                ("front_path", models.CharField(max_length=300, verbose_name="前端路径")),
                (
                    "icon",
                    models.CharField(
                        blank=True, max_length=300, null=True, verbose_name="菜单图标"
                    ),
                ),
                (
                    "condition",
                    models.Char<PERSON>ield(
                        blank=True, max_length=300, null=True, verbose_name="后端规则"
                    ),
                ),
                ("method", models.Char<PERSON>ield(max_length=10, verbose_name="请求方法")),
                (
                    "state",
                    models.SmallIntegerField(
                        choices=[("0", "禁用"), ("1", "启用")], default=1, verbose_name="状态"
                    ),
                ),
                ("order", models.IntegerField(default=0, verbose_name="排序")),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subs",
                        to="users.rule",
                        verbose_name="父级菜单",
                    ),
                ),
            ],
            options={
                "verbose_name": "菜单规则",
                "verbose_name_plural": "菜单规则",
            },
        ),
        migrations.AddField(
            model_name="group",
            name="rules",
            field=models.ManyToManyField(
                blank=True,
                help_text="The rules this group belongs to. A group will get all rules",
                related_name="group_set",
                related_query_name="group",
                to="users.rule",
                verbose_name="rules",
            ),
        ),
    ]
