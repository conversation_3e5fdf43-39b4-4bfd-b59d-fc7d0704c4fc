# Generated by Django 5.0.8 on 2024-12-02 08:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0077_alter_company_main_products"),
        ("users", "0101_alter_userloginlog_phone_number_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="userapp",
            name="company",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="companies.company",
                to_field="company_id",
                verbose_name="供应商ID",
            ),
        ),
        migrations.AddField(
            model_name="userapp",
            name="disable",
            field=models.BooleanField(default=False, verbose_name="是否禁用"),
        ),
        migrations.AddField(
            model_name="userapp",
            name="distributor",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="companies.distributor",
                to_field="distributor_id",
                verbose_name="分销商ID",
            ),
        ),
        migrations.AddField(
            model_name="userapp",
            name="user_type",
            field=models.CharField(
                choices=[("OP", "运营商"), ("DB", "分销商"), ("SP", "供应商")],
                default="OP",
                verbose_name="用户类型",
            ),
        ),
    ]
