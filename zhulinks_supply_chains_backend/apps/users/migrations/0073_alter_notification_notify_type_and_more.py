# Generated by Django 5.0.8 on 2024-08-29 08:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0072_notification_route_info_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="notification",
            name="notify_type",
            field=models.CharField(
                choices=[
                    ("plan", "货盘动态"),
                    ("review", "审核动态"),
                    ("price", "价格动态"),
                    ("stock", "库存动态"),
                    ("stock_not_update", "库存未更新"),
                    ("stock_warning", "库存预警"),
                    ("order", "订单"),
                    ("sales", "售后"),
                ],
                max_length=20,
                verbose_name="消息类型",
            ),
        ),
        migrations.AlterField(
            model_name="notification",
            name="operation_type",
            field=models.CharField(
                choices=[
                    ("added_plan", "加入货盘"),
                    ("removed_plan", "移除货盘"),
                    ("review_approved", "通过审核"),
                    ("review_rejected", "未通过审核"),
                    ("price_changed", "价格变动"),
                    ("stock_approved", "库存审核通过"),
                    ("stock_rejected", "库存审核未通过"),
                    ("stock_warning", "库存预警"),
                    ("stock_not_update", "库存未更新"),
                    ("pay_success", "支付成功"),
                    ("order_delivery", "发货成功"),
                    ("order_finish", "订单完成"),
                    ("sales_return_success", "退款成功"),
                    ("sales_approve_agree", "售后申请同意"),
                    ("sales_approve_reject", "售后申请拒绝"),
                ],
                max_length=20,
                verbose_name="操作类型",
            ),
        ),
    ]
