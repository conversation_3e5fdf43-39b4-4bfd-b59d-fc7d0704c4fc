# Generated by Django 5.1.2 on 2024-11-19 02:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0090_userinfos_new_on_zhuji_product"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserLoginLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "login_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="登录时间"),
                ),
                ("client", models.CharField(max_length=50, verbose_name="登录端")),
                (
                    "distributor_type",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="分销商类型"
                    ),
                ),
                ("app", models.CharField(max_length=50, verbose_name="登录APP")),
                (
                    "real_name",
                    models.Char<PERSON>ield(max_length=100, verbose_name="真实姓名"),
                ),
                ("username", models.Char<PERSON>ield(max_length=100, verbose_name="用户名")),
                (
                    "phone_number",
                    models.Char<PERSON>ield(max_length=15, verbose_name="手机号"),
                ),
                ("ip_address", models.GenericIPAddressField(verbose_name="IP地址")),
                ("region", models.CharField(max_length=100, verbose_name="地区")),
                ("role", models.CharField(max_length=50, verbose_name="角色")),
                ("system", models.CharField(max_length=50, verbose_name="系统")),
                (
                    "employee_status",
                    models.CharField(max_length=50, verbose_name="员工状态"),
                ),
                (
                    "login_status",
                    models.CharField(max_length=100, verbose_name="登录情况"),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "用户登录日志",
                "verbose_name_plural": "用户登录日志",
                "ordering": ["-login_time"],
            },
        ),
    ]
