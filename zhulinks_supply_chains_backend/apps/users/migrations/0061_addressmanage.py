# Generated by Django 5.0.7 on 2024-07-12 07:59

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0060_notification_trend"),
    ]

    operations = [
        migrations.CreateModel(
            name="AddressManage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "user_id",
                    models.BigIntegerField(
                        default="", unique=True, verbose_name="用户id"
                    ),
                ),
                (
                    "recipient",
                    models.CharField(
                        blank=True, default="", max_length=20, verbose_name="收件人"
                    ),
                ),
                (
                    "mobile",
                    models.CharField(
                        blank=True, default="", max_length=11, verbose_name="电话号码"
                    ),
                ),
                (
                    "area",
                    models.CharField(
                        blank=True, default="", max_length=255, verbose_name="地区"
                    ),
                ),
                (
                    "address",
                    models.Char<PERSON>ield(
                        blank=True, default="", max_length=255, verbose_name="详细地址"
                    ),
                ),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="是否默认地址"),
                ),
                ("is_deleted", models.BooleanField(default=False, verbose_name="是否删除")),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "地址管理",
                "verbose_name_plural": "地址管理",
                "db_table": "address_manage",
            },
        ),
    ]
