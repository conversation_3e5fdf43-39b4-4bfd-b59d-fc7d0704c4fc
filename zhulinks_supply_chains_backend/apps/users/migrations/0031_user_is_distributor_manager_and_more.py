# Generated by Django 4.2.7 on 2023-12-08 09:32

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0030_user_distributor"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="is_distributor_manager",
            field=models.BooleanField(
                blank=True, default=False, verbose_name="是否为分销商数据管理者"
            ),
        ),
        migrations.AddConstraint(
            model_name="user",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("is_deleted__exact", False),
                    ("is_distributor_manager__exact", True),
                ),
                fields=("distributor", "is_distributor_manager"),
                name="unique_distributor_manager",
            ),
        ),
    ]
