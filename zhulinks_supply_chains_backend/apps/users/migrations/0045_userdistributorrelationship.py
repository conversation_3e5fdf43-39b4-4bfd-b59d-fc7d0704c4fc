# Generated by Django 4.2.7 on 2024-03-13 07:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("companies", "0036_distributor_letters"),
        ("users", "0044_remove_user_unique_mobile_user_type_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserDistributorRelationShip",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "distributor",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.distributor",
                        to_field="distributor_id",
                        verbose_name="绑定的分销商",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        to_field="user_id",
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户关联分销商",
                "verbose_name_plural": "用户关联分销商",
            },
        ),
    ]
