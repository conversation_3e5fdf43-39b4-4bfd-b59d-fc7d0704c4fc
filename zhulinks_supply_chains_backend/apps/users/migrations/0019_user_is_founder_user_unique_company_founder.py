# Generated by Django 4.2.1 on 2023-07-13 10:39

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0018_remove_user_unique_username_not_deleted_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="is_founder",
            field=models.BooleanField(default=False, verbose_name="是否为公司数据创建者"),
        ),
        migrations.AddConstraint(
            model_name="user",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("is_deleted__exact", False), ("is_founder__exact", True)
                ),
                fields=("company", "is_founder"),
                name="unique_company_founder",
            ),
        ),
    ]
