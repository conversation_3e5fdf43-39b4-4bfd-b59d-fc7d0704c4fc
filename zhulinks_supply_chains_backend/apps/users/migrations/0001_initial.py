# Generated by Django 4.2.1 on 2023-05-29 07:48

import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("companies", "0001_initial"),
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="GroupExtend",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "describe",
                    models.CharField(
                        blank=True, max_length=256, null=True, verbose_name="群组描述"
                    ),
                ),
                ("user_count", models.IntegerField(default=0, verbose_name="员工数量")),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        verbose_name="所属公司",
                    ),
                ),
                (
                    "group",
                    models.OneToOneField(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="auth.group",
                    ),
                ),
            ],
            options={
                "verbose_name": "群组扩展",
                "verbose_name_plural": "群组扩展",
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "nickname",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="昵称"
                    ),
                ),
                ("mobile", models.CharField(max_length=20, verbose_name="手机号")),
                (
                    "user_type",
                    models.CharField(
                        choices=[("SP", "供应商"), ("OP", "运营商")],
                        default="SP",
                        max_length=2,
                        verbose_name="用户类型",
                    ),
                ),
                (
                    "state",
                    models.SmallIntegerField(
                        choices=[(0, "已删除"), (1, "正常"), (2, "停用")],
                        default=1,
                        verbose_name="用户状态",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=200, null=True, verbose_name="邮箱"
                    ),
                ),
                (
                    "avater",
                    models.CharField(
                        blank=True, max_length=300, null=True, verbose_name="头像图片网址"
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="语言"
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="城市"
                    ),
                ),
                (
                    "province",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="省份"
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="国家"
                    ),
                ),
                (
                    "country_code",
                    models.CharField(
                        blank=True,
                        default="86",
                        max_length=8,
                        null=True,
                        verbose_name="国家区号",
                    ),
                ),
                (
                    "two_letter_region_code",
                    models.CharField(
                        blank=True, max_length=2, null=True, verbose_name="区域二字代码"
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_date",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                        verbose_name="所属公司",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户",
                "verbose_name_plural": "用户",
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
