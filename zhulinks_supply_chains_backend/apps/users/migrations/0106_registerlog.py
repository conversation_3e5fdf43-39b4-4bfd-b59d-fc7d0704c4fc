# Generated by Django 5.0.8 on 2024-12-11 05:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0105_userminiprogramsubscribe"),
    ]

    operations = [
        migrations.CreateModel(
            name="RegisterLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("nickname", models.CharField(max_length=25, verbose_name="昵称")),
                (
                    "username",
                    models.Char<PERSON>ield(
                        blank=True,
                        default="",
                        max_length=25,
                        null=True,
                        verbose_name="用户名",
                    ),
                ),
                ("mobile", models.CharField(max_length=11, verbose_name="手机号码")),
                (
                    "inviter",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=25,
                        null=True,
                        verbose_name="邀请人",
                    ),
                ),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "is_register",
                    models.<PERSON><PERSON>an<PERSON>ield(default=False, verbose_name="是否已注册"),
                ),
            ],
            options={
                "verbose_name": "注册申请记录",
                "verbose_name_plural": "注册申请记录",
            },
        ),
    ]
