# Generated by Django 5.0.6 on 2024-07-22 12:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0065_addressmanage_city_alter_addressmanage_street"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserExtraProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        choices=[("WM", "微信小程序"), ("WB", "微信web端")],
                        default="WM",
                        max_length=16,
                        verbose_name="数据来源",
                    ),
                ),
                (
                    "platform_uni_id",
                    models.CharField(
                        db_index=True,
                        max_length=255,
                        unique=True,
                        verbose_name="平台唯一ID",
                    ),
                ),
                (
                    "nickname",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=100,
                        null=True,
                        verbose_name="昵称",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        default="",
                        max_length=200,
                        null=True,
                        verbose_name="邮箱",
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=100,
                        null=True,
                        verbose_name="语言",
                    ),
                ),
                (
                    "country_code",
                    models.CharField(
                        default="86", max_length=8, null=True, verbose_name="国家区号"
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=100,
                        null=True,
                        verbose_name="国家",
                    ),
                ),
                (
                    "province",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=100,
                        null=True,
                        verbose_name="省份",
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=100,
                        null=True,
                        verbose_name="城市",
                    ),
                ),
                (
                    "avatar",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=300,
                        null=True,
                        verbose_name="头像图片",
                    ),
                ),
                (
                    "session_key",
                    models.CharField(
                        blank=True,
                        default="",
                        max_length=300,
                        null=True,
                        verbose_name="会话KEY",
                    ),
                ),
                ("enable", models.BooleanField(default=True, verbose_name="是否启用")),
                (
                    "user",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        to_field="user_id",
                        verbose_name="关联用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "第三方用户信息",
                "verbose_name_plural": "第三方用户信息",
            },
        ),
    ]
