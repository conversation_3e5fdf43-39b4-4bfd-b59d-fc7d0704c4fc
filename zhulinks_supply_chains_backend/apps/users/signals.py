# -*- coding: utf-8 -*-
from django.core.cache import cache
from django.db import transaction
from django.db.models.signals import pre_save, post_save, m2m_changed, post_delete
from django.dispatch import receiver

from users.models import User, update_cache_real_name_with_user_id, ClientType
from users.models_v2 import Permissions, Roles, BackendRoutes
from users.tasks_v2.rbac_tasks import flush_system_data_perms_cache_task


def signal_load_func():
    pass


@receiver(pre_save, sender=User)
def user_pre_save_handler(sender, instance: User, *args, **kwargs):
    instance._origin_real_name = None

    if instance.pk:
        user = User.objects.get(pk=instance.pk, query_all=True)
        instance._origin_real_name = user.real_name


@receiver(post_save, sender=User)
def user_post_save_handler(sender, instance, created, **kwargs):
    if not created and getattr(instance, "_origin_real_name") != instance.real_name:
        update_cache_real_name_with_user_id(instance.user_id)


@receiver(pre_save, sender=Permissions)
def save_old_permission_data(sender, instance: Permissions, **kwargs):
    if instance.pk:
        old_permissions = Permissions.objects.get(pk=instance.pk)
        instance.__origin_codename__ = old_permissions.codename


@receiver(post_save, sender=Permissions)
@receiver(post_delete, sender=Permissions)
def system_perms_changed_handler(sender, instance: Permissions, *args, **kwargs):
    """
    刷新系统的数据权限
    :param sender:
    :param instance:
    :param created:
    :param kwargs:
    :return:
    """
    if kwargs.get("created", False):
        if instance.codename.startswith("backend:"):
            transaction.on_commit(lambda: flush_system_data_perms_cache_task.delay())
    else:
        if getattr(instance, "__origin_codename__", "").startswith("backend:"):
            transaction.on_commit(lambda: flush_system_data_perms_cache_task.delay())


@receiver(post_save, sender=Roles)
@receiver(post_delete, sender=Roles)
def roles_perms_changed_handler(sender, instance: Roles, *args, **kwargs):
    if not kwargs.get("created", False):
        client_types = ClientType.objects.values_list("code", flat=True)
        users = User.objects.filter(roles=instance).only("user_id")
        cache_keys = [f"rbac:data_perms:user_{u.user_id}_{client_code}_{instance.type_id}" for u in users for client_code in client_types]
        cache.delete_many(cache_keys)


@receiver(m2m_changed, sender=Roles.permissions.through)
def handle_roles_permissions_changed(sender, instance, action, pk_set, **kwargs):
    """
    角色跟权限关联的更新
    :param sender:
    :param instance:
    :param action:
    :param pk_set:
    :param kwargs:
    :return:
    """
    if action in ("post_add", "post_remove", "post_clear"):
        client_types = ClientType.objects.values_list("code", flat=True)
        if isinstance(instance, Roles):
            users = User.objects.filter(roles=instance).only("user_id")
            cache_keys = [f"rbac:data_perms:user_{u.user_id}_{client_code}_{instance.type_id}" for u in users for client_code in client_types]
            cache.delete_many(cache_keys)
        elif isinstance(instance, Permissions):
            roles = instance.roles_set.all()
            users = User.objects.filter(roles__in=roles).only("user_id")
            cache_keys = [f"rbac:data_perms:user_{u.user_id}_{client_code}_{role.type_id}" for u in users for client_code in client_types for role in roles]
            cache.delete_many(cache_keys)


@receiver(m2m_changed, sender=User.roles.through)
def handle_user_roles_changed(sender, instance, action, pk_set, **kwargs):
    """
    角色跟权限关联的更新
    :param sender:
    :param instance:
    :param action:
    :param pk_set:
    :param kwargs:
    :return:
    """
    if action in ("post_add", "post_remove", "post_clear"):
        client_types = ClientType.objects.values_list("code", flat=True)
        cache_keys = []
        if isinstance(instance, Roles):
            user_add_pk_list = getattr(instance, "__User_roles_add__", [])
            user_remove_pk_list = getattr(instance, "__User_roles_remove__", [])
            user_pk_list = user_add_pk_list + user_remove_pk_list
            user_id_list = User.objects.filter(pk__in=user_pk_list).values_list("user_id", flat=True)
            cache_keys = [f"rbac:data_perms:user_{user_id}_{client_code}_{instance.type_id}" for client_code in client_types for user_id in user_id_list]
        else:
            roles = Roles.objects.filter(pk__in=pk_set)
            for role in roles:
                cache_keys = [f"rbac:data_perms:user_{instance.user_id}_{client_code}_{role.type_id}" for client_code in client_types]
        if cache_keys:
            cache.delete_many(cache_keys)


@receiver(post_save, sender=BackendRoutes)
@receiver(post_delete, sender=BackendRoutes)
def delete_backend_routes_cache(sender, instance, **kwargs):
    transaction.on_commit(lambda: cache.delete("rbac:limit_backend_routes"))
