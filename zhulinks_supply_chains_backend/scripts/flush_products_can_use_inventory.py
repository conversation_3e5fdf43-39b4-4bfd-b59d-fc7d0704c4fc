# -*- coding: utf-8 -*-
import time

from django.db.models import F


def flush_data():
    """
    刷新现在item的所有sku信息
    :return:
    """
    from products.models import Product, StockKeepingUnit

    start = time.time()
    print(f"-------- 脚本开始 --------")
    product_rows = Product.objects.update(can_use_inventory=F("physical_inventory") - F("plan_use_inventory"))
    sku_rows = StockKeepingUnit.objects.update(can_use_inventory=F("physical_inventory") - F("plan_use_inventory"))
    print(f"-------- 执行脚本完成,更新商品:{product_rows},更新SKU:{sku_rows}, 耗时: {time.time() - start} --------")


if __name__ == "__main__":
    import os

    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "zhulinks_supply_chains_backend.settings")
    import django

    django.setup()

    flush_data()
