# -*- coding: utf-8 -*-


def flush_data():
    from companies.models import Distributor

    letter_map = {
        "加加珠宝": "MJ",
        "抖店商城": "DD",
        "学好姐": "XH",
        "董先生": "D",
        "明道": "M",
        "达播部": "DB",
        "云上叙甄选珠宝": "ZX",
        "贵妃臻品": "GF",
        "曹掌柜珠宝": "G",
        "小宝珠宝": "T",
        "云上珠宝": "V",
    }
    distributors = Distributor.objects.filter(name__in=letter_map.keys())

    need_update_objects = []
    for distributor in distributors:
        if letter_map.get(distributor.name):
            distributor.letters = letter_map.get(distributor.name)
            need_update_objects.append(distributor)

    updated_rows = Distributor.objects.bulk_update(need_update_objects, fields=["letters"])
    print(f">>>>更新成功.数量:{updated_rows}")
