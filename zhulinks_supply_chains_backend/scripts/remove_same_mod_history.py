# -*- coding: utf-8 -*-


from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Count
from django.db.models import DateTimeField
from django.db.models.functions import Trunc

from products.models import ProductSelectionPlanRecord, ProductSelectionPlanRecordType


def flush_data(record_type: str = ProductSelectionPlanRecordType.ADD_PROD):
    """
    删除一分钟内重复的数据
    :param record_type:
    :return:
    """
    prs = (
        (
            ProductSelectionPlanRecord.objects.filter(
                record_type=record_type,
            )
            .annotate(
                min_date=Trunc(
                    "create_date",
                    "minute",
                    output_field=DateTimeField(),
                ),
            )
            .values(
                "min_date",
                "product_id",
                "selection_plan_id",
            )
            .annotate(
                total_count=Count("id"),
                id_list=ArrayAgg("id"),
            )
        )
        .values("min_date", "id_list")
        .filter(total_count__gte=2)
    )
    for pr in prs:
        id_list = pr.get("id_list")
        if len(id_list) >= 2:
            remove_ids = id_list[1:]
            ProductSelectionPlanRecord.objects.filter(id__in=remove_ids).delete()


if __name__ == "__main__":
    flush_data()
