# -*- coding: utf-8 -*-
from products.models import SelectionPlanLabelMark


def flush_basic_selection_plan_marks():

    name_list = [
        "重点品",
        "引流品",
        "平播品",
        "爆品",
        "新品",
        "利润品",
        "福利品",
    ]

    bulk_objs = []
    for idx, name in enumerate(name_list):
        bulk_objs.append(SelectionPlanLabelMark(name=name, order=idx))

    SelectionPlanLabelMark.objects.bulk_create(bulk_objs)

    print(f">>>> 初始化货盘基础标记完成:{len(bulk_objs)}个")


def flush_data():
    flush_basic_selection_plan_marks()
