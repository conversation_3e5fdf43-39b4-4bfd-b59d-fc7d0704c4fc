# -*- coding: utf-8 -*-


def flush_erp_sales_order_details():
    from erp_orders.models import ERPSalesOrderDetails

    details = ERPSalesOrderDetails.objects.prefetch_related(
        "sku_inventory",
        "sku_inventory__sku",
    ).filter(sku_inventory__isnull=False)

    for detail in details:
        inv = detail.sku_inventory

        if not inv:
            continue

        sku = inv.sku

        detail.sku = sku
        detail.tag_type = sku.tag_type

        detail.label_price = sku.label_price
        detail.market_price = sku.market_price
        detail.purchase_price = sku.purchase_price

        detail.other_price1 = sku.other_price1
        detail.other_price2 = sku.other_price2
        detail.other_price3 = sku.other_price3
        detail.other_price4 = sku.other_price4
        detail.other_price5 = sku.other_price5
        detail.other_price6 = sku.other_price6

        detail.save()

        sales_order = detail.sales_order
        sales_order.calculate_price_and_quantity_data()
        sales_order.save()

    print(">>>刷新销售单成功")


def flush_erp_order_details():
    from erp_orders.models import ERPOrderDetails

    details = ERPOrderDetails.objects.prefetch_related("sku_inventory").filter(sku_inventory__isnull=False)
    need_update_order_details = []
    for detail in details:
        inv = detail.sku_inventory
        sku = inv.sku

        detail.sku = sku
        detail.tag_type = sku.tag_type
        detail.cost_price = sku.cost_price
        detail.label_price = sku.label_price
        detail.market_price = sku.market_price
        detail.purchase_price = sku.purchase_price
        detail.other_price1 = sku.other_price1
        detail.other_price2 = sku.other_price2
        detail.other_price3 = sku.other_price3
        detail.other_price4 = sku.other_price4
        detail.other_price5 = sku.other_price5

        need_update_order_details.append(detail)

    ERPOrderDetails.objects.bulk_update(
        need_update_order_details,
        fields=[
            "sku",
            "tag_type",
            "cost_price",
            "label_price",
            "market_price",
            "purchase_price",
            "other_price1",
            "other_price2",
            "other_price3",
            "other_price4",
            "other_price5",
        ],
    )

    print(">>>刷新销售单成功")
