# -*- coding: utf-8 -*-
# @Author: <PERSON><PERSON>
# @Date:   2023-11-28 15:33:15
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-12-04 18:18:39
from products.models import Product


def exec_update():
    products = Product.objects.filter(is_deleted=False)
    for product in products:
        tags_list = []
        if product.tags:
            for i in product.tags:
                if i:
                    tags_list.append(i)
        if tags_list and not product.remark:
            tags_str = ",".join(tags_list)
            product.remark = tags_str
    Product.objects.bulk_update(products, ["remark"], batch_size=1000)
    print("update completed.")
