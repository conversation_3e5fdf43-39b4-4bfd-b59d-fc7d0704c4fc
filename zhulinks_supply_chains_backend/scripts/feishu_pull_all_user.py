import random
from datetime import datetime

from django.contrib.auth.hashers import make_password
from django.utils import timezone

from companies.models import Distributor
from users.models import User, UserType, UserDistributorRelationShip
from utils.feishu import FeiShuDocx


def get_random_letters(count=3):
    return ''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=count))


def create_update_user(user_info, distributor_id):
    for user in user_info:
        phone = user["system_fields"]["mobile"]
        random_ = get_random_letters()
        username = f"{random_}{phone[-4:]}"
        while User.objects.filter(username=username).exists():
            random_ = get_random_letters()
            username = f"{random_}{phone[-4:]}"
        real_name = user["system_fields"]["name"]
        mobile = phone.split(" ")[-1]
        country_code = phone.split(" ")[0].replace("+", "")
        if len(country_code) != 2:
            country_code = "86"

        existed_user = User.objects.filter(mobile=mobile).first()
        if existed_user:
            if existed_user.create_date < timezone.make_aware(datetime(2024, 8, 23), timezone.get_current_timezone()):
                if existed_user.distributor_id != distributor_id:
                    distributor_obj = Distributor.objects.get(id=distributor_id)
                    if not UserDistributorRelationShip.objects.filter(distributor=distributor_obj, user=existed_user).exists():
                        UserDistributorRelationShip.objects.create(distributor=distributor_obj, user=existed_user)
                        db_user_type = UserType.objects.filter(code="DB").first()
                        if existed_user.user_type != "DB" and not existed_user.user_types.filter(code="DB").first():
                            existed_user.user_types.add(db_user_type)
        else:
            existed_user = User.objects.create(
                username=username,
                real_name=real_name,
                mobile=mobile,
                country_code=country_code,
                distributor_id=distributor_id,
                user_type="DB",
            )
            existed_user.password = make_password("123456")
            add_user_type = UserType.objects.filter(code="DB").first()
            existed_user.user_types.add(add_user_type)
        existed_user.save()
    return len(user_info)


def feishu_pull_all_user(distributor_id):
    feishu_client = FeiShuDocx()
    res = feishu_client.get_all_user()
    res = res.json()["data"]
    user_info = res["items"]
    num = create_update_user(user_info, distributor_id)
    while res["has_more"]:
        num += 1
        res = feishu_client.get_all_user(res["page_token"])
        res = res.json()["data"]
        user_info = res["items"]
        num += create_update_user(user_info, distributor_id)

    # a["data"]["items"][1]["system_fields"]["mobile"]
    # a["data"]["items"][1]["system_fields"]["name"]
    # a["data"]["items"][1]["system_fields"]["employee_no"]
    # a["data"]["items"][1]["user_id"]
    print(f"首批飞书用户导入完成，数量：{num}")
    return num
