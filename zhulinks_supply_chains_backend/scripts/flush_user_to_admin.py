from users.models import User, UserType


def exec_update():
    user_qs = User.objects.all()
    db_type = UserType.objects.filter(code="DB").first()
    op_type = UserType.objects.filter(code="OP").first()
    sp_type = UserType.objects.filter(code="SP").first()
    for user in user_qs:
        if user.user_type == "OP":
            user.is_superuser = True
            user.user_types.add(op_type)
            user.save()
        elif user.user_type == "DB":
            user.is_distributor_manager = True
            user.user_types.add(db_type)
            user.save()
        elif user.user_type == "SP":
            user.is_founder = True
            user.user_types.add(sp_type)
            user.save()
    print("completed")
