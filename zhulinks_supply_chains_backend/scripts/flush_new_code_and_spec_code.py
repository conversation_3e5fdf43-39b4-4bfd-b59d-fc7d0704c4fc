from products.models import (
    Product,
    StockKeepingUnit,
    ProductRecord,
    SkuRecord,
)
from companies.models import Company
from django.core.paginator import Paginator
from utils.common import get_random_number_str
from django.db import transaction
from django.db.utils import IntegrityError

from utils.http_handle import IResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def test_flush_view(request):
    flush_new_code_data()
    return IResponse()


def generate_code():
    unique = False
    while not unique:
        code = get_random_number_str(6)
        unique = not Product.objects.filter(code=code).exists()
    return code


def generate_spec_code(company_code):
    unique = False
    while not unique:
        spec_code = f"J{company_code}" + get_random_number_str(6)
        unique = not StockKeepingUnit.objects.filter(spec_code=spec_code).exists()
    return spec_code


def get_all_company_map():
    _objects = Company.objects.all()
    return {com.id: com.code for com in _objects}


def flush_new_code_data():
    products_qs = Product.objects.filter(is_deleted=False, has_new_code=False).order_by("-id")
    company_map = get_all_company_map()

    paginator = Paginator(products_qs, 1000)  # 分批处理，每批1000条
    for page_num in paginator.page_range:
        print(f"=== page_num {page_num} ===")
        for product_qs in paginator.page(page_num):
            try:
                with transaction.atomic():
                    product_id = product_qs.product_id
                    print(f"start_handle: {product_id}")
                    old_code = product_qs.code
                    new_code = generate_code()
                    product_qs.code = new_code
                    company_code = company_map.get(product_qs.company_id)

                    # 记录表
                    product_record, _ = ProductRecord.objects.get_or_create(
                        product_id=product_qs.product_id,
                        old_code=old_code,
                        new_code=new_code,
                    )
                    skus = product_qs.stockkeepingunit_set.filter(become_history=False)
                    for sku in skus:
                        old_spec_code = sku.spec_code
                        new_spec_code = generate_spec_code(company_code)
                        sku.spec_code = new_spec_code
                        sku.save()
                        # 记录表
                        SkuRecord.objects.get_or_create(
                            product_record_id=product_record.product_id,
                            sku_id=sku.sku_id,
                            old_spec_code=old_spec_code,
                            new_spec_code=new_spec_code,
                        )
                    product_qs.has_new_code = True
                    product_qs.save()
                    print(f"end_handle: {product_id}")
            except ValueError as e:
                print(f"ValueError: {e}")
            except IntegrityError as e:
                print(f"IntegrityError: {e}")

    print("completed")
