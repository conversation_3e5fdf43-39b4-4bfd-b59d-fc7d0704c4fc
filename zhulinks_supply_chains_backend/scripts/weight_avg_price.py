from erp_purchase.models import InboundOrder, InboundOrderDetail, ErpWeightAvgPriceSnapshot, InventoryChangeDetails

"""
    新价格 = new_cost_price
    新采购量  = quantity
    加权平均价 = average_price
    当前总库存 = in_warehouse_quantity

    新加权平均价 = (新价格 * 新采购量 + 旧库存 * 旧加权平均价) / (旧库存 + 新采购量)
    X = new_cost_price * quantity + average_price * Y
    Y = quantity + in_warehouse_quantity

    新加权平均价average_price = X / Y
    """

def outbound2snapshot():
    items = [
        {"cost_price": 10, "quantity": 10, "out_quantity": 0, "avg_price": 10},
        {"cost_price": 20, "quantity": 10, "out_quantity": 1, "avg_price": 0},
        {"cost_price": 30, "quantity": 10, "out_quantity": 0, "avg_price": 0},
        {"cost_price": 40, "quantity": 10, "out_quantity": 0, "avg_price": 0},
        {"cost_price": 50, "quantity": 10, "out_quantity": 0, "avg_price": 0},
    ]

    last_avg_price = None

    for index, item in enumerate(items):
        item.setdefault("in_quantity", 0)

        # 旧库存
        if index == 0:
            last_in_quantity = 0
        else:
            last_in_quantity = items[index - 1]["in_quantity"]

        item["in_quantity"] = last_in_quantity + item["quantity"] - item["out_quantity"]

        if last_avg_price is None:
            last_avg_price = item["avg_price"]
            item["total_cost_price"] = last_avg_price * last_in_quantity
            print(last_avg_price)
        else:
            # 当前库存总成本: 新采购总价 + 旧加权平均价 * 旧库存
            X = item["cost_price"] * item["quantity"] + last_avg_price * last_in_quantity
            item["total_cost_price"] = last_avg_price * last_in_quantity

            Y = last_in_quantity + item["quantity"]

            # 新加权平均价
            last_avg_price = X / Y
            item["avg_price"] = last_avg_price

            print(item["avg_price"])

        print(item)

    # 批量计算加权平均价
    CHANGE_TYPE_11 = 11
    CHANGE_TYPE_12 = 12
    CHANGE_TYPE_16 = 16
    change_type_mp = {
        1: CHANGE_TYPE_11,
        2: CHANGE_TYPE_12,
        3: CHANGE_TYPE_16,
    }

    in_warehouse_quantity_mp = {}

    for inbound_order in InboundOrder.objects.filter(is_deleted=False):
        details = InboundOrderDetail.objects.filter(inbound_order=inbound_order)
        for detail in details:
            in_warehouse_quantity_mp.setdefault(detail.sku_id, 0)
            in_warehouse_quantity_mp[detail.sku_id] += detail.quantity

            ErpWeightAvgPriceSnapshot.add_snapshot(
                change_type_mp[inbound_order.order_type],
                detail.id,
                **dict(
                    company_id=inbound_order.company_id,
                    sku_id=detail.sku_id,
                    cost_price=detail.cost_price,
                    quantity=detail.quantity,
                    from_order_id=inbound_order.id,
                    in_warehouse_quantity=in_warehouse_quantity_mp[detail.sku_id]
                )
            )
            print(in_warehouse_quantity_mp[detail.sku_id])


def execl2snaphost():
    # execl读取
    import pandas as pd

    # 读取 Excel 文件（替换为你的文件路径）
    file_path = "动态加权平均价计算.xlsx"
    df = pd.read_excel(file_path)

    # 将每一行转换为字典，生成列表
    data_list = df.to_dict(orient='records')

    # 输出结果（可选）
    print(data_list)
    weight_avg_price = None
    for detail in data_list[:-1]:
        if "批次" not in detail["批次"]:
            continue
        instance, weight_avg_price = ErpWeightAvgPriceSnapshot.add_snapshot(
            InventoryChangeDetails.CHANGE_TYPE_11,
            detail["批次"].replace("批次", ""),
            **dict(
                company_id=*********,
                sku_id=909,
                cost_price=detail["单价"],
                quantity=detail["数量"],
                in_warehouse_quantity=detail["累计库存"]
            )
        )
        print(detail["批次"], weight_avg_price)

def cancel_approve():
    # 反审批次6
    ErpWeightAvgPriceSnapshot.remove_snapshot(
        InventoryChangeDetails.CHANGE_TYPE_11,
        "批次16".replace("批次", ""),
    )

def approve():
    # 审核指定批次
    instance, weight_avg_price = ErpWeightAvgPriceSnapshot.add_snapshot(
        InventoryChangeDetails.CHANGE_TYPE_11,
        "批次16".replace("批次", ""),
        **dict(
            company_id=*********,
            sku_id=909,
            cost_price=15,
            quantity=900,
            in_warehouse_quantity=1900
        )
    )
    pass

def changedetail2snapshot():
    # 将指定类型流水明细转换为加权平均价快照

    changedetails = InventoryChangeDetails.objects.filter(change_type__in=[
            InventoryChangeDetails.CHANGE_TYPE_11,
            InventoryChangeDetails.CHANGE_TYPE_12,
            InventoryChangeDetails.CHANGE_TYPE_14,
            InventoryChangeDetails.CHANGE_TYPE_15,
            InventoryChangeDetails.CHANGE_TYPE_16,
        ])

    for change in changedetails:
        ErpWeightAvgPriceSnapshot.add_snapshot(
            change.change_type,
            change.from_detail_id,
            **dict(
                company_id=change.company_id,
                sku_id=change.sku.id,
                cost_price=change.cost_price,
                quantity=change.quantity,
                from_detail_id=change.id,
                in_warehouse_quantity=change.in_warehouse_quantity
            )
        )

if __name__ == '__main__':
    # execl2snaphost()
    cancel_approve()