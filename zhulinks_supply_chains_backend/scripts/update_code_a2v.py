import random
from django.db.models import Q
from products.models import Product, ProductCodeA2VRecord
from django.db import transaction
from django.db.utils import IntegrityError
from django.db.models import Count
from django.db.models.functions import Substr


def get_new_code(old_code):
    new_code = "V" + old_code[1:]
    if not Product.objects.filter(code=new_code).exists():
        return new_code
    unique = False
    times = 0
    while not unique:
        if times > 10:
            raise RuntimeError(f"get_new_code more than 10 times: {old_code}")
        new_code = "V" + random.choice("0123456789") + old_code[1:]
        unique = not Product.objects.filter(code=new_code).exists()
        times += 1
    return new_code


def update_A2V():
    a_code = Q(code__startswith="A") | Q(code__startswith="a")
    # 未被删除
    need_handle = Q(is_deleted=False)
    a_code_qs = Product.objects.filter(need_handle, a_code)
    for a_code in a_code_qs:
        try:
            with transaction.atomic():
                product_id = a_code.product_id
                print(f"start {product_id}")
                old_code = a_code.code
                new_code = get_new_code(old_code)
                a_code.code = new_code
                a_code.save()
                # 记录
                a2v_record, _ = ProductCodeA2VRecord.objects.get_or_create(
                    product_id=a_code.product_id,
                    old_code=old_code,
                    new_code=new_code,
                )
                print(f"end {product_id}")
        except ValueError as e:
            print(f"ValueError: {e}")
        except IntegrityError as e:
            print(f"IntegrityError: {e}")

    print("completed")


def update_v2V():
    v_code = Q(code__startswith="v")
    # 未被删除
    need_handle = Q(is_deleted=False)
    v_code_qs = Product.objects.filter(need_handle, v_code)
    for v_code in v_code_qs:
        try:
            with transaction.atomic():
                product_id = v_code.product_id
                print(f"start {product_id}")
                old_code = v_code.code
                new_code = "V" + old_code[1:]
                v_code.code = new_code
                v_code.save()
                # 记录
                a2v_record, _ = ProductCodeA2VRecord.objects.get_or_create(
                    product_id=v_code.product_id,
                    old_code=old_code,
                    new_code=new_code,
                )
                print(f"end {product_id}")
        except ValueError as e:
            print(f"ValueError: {e}")
        except IntegrityError as e:
            print(f"IntegrityError: {e}")

    print("completed")
