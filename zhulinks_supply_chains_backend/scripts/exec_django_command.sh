#!/bin/bash
# @Author: <PERSON><PERSON>
# @Date:   2023-12-01 13:17:30
# @Last Modified by:   <PERSON><PERSON>
# @Last Modified time: 2023-12-04 16:52:42
#!/bin/bash

# 远程项目路径
remote_project_path="/home/<USER>/main/"

cd $remote_project_path
su ecs-user -c "source .venv/bin/activate; python zhulinks_supply_chains_backend/manage.py shell -c '
from erp_purchase.models import (
        ERPOutboundOrder,
        ERPOutboundOrderDetail
    )

from erp_purchase.models import (
    InboundOrder,
    InboundOrderDetail
)

ERPOutboundOrderDetail.objects.filter(id__in=[42,41,49]).update(cost_price=0)
InboundOrder.objects.filter(inbound_code__in=[\"RK25052400020\",\"RK25052400004\", \"RK25052400019\"]).update(total_cost_price=0)

'"
echo "shell end"


# 原生sql
from django.db import connection, connections
with connection.cursor() as cursor:
    cursor.execute()

