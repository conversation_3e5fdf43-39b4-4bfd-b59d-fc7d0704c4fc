from django.db.models import Q
from products.models import Product
from django.db.models import Count
from django.db.models.functions import Substr


def count_unique_code_first_characters():
    first_chars = (
        Product.objects.annotate(first_char=Substr("code", 1, 1))
        .values("first_char")
        .annotate(count=Count("first_char"))
        .filter(first_char__isnull=False, is_deleted=False)
        .order_by("first_char")
    )
    return list(first_chars)


def statistics():
    v_code = Q(code__startswith="V") | Q(code__startswith="v")
    g_code = Q(code__startswith="G") | Q(code__startswith="g")
    t_code = Q(code__startswith="T") | Q(code__startswith="t")
    # 未被删除
    need_handle = Q(is_deleted=False)
    # 未被关联
    n_linked = Q(origin_product__isnull=True)

    # 所有商品
    all_qs = Product.objects.filter(need_handle)
    print(f"所有商品：{all_qs.count()}")
    # 所有未被关联上
    all_nk_qs = all_qs.filter(n_linked)
    print(f"所有未关联商品（包含主商品）：{all_nk_qs.count()}")

    # 未关联上
    v_qs = Product.objects.filter(v_code, need_handle)
    print(f"以V开头商品：{v_qs.count()}")
    v_nk_qs = v_qs.filter(n_linked)
    print(f"以V开头未关联上主商品：{v_nk_qs.count()}")
    # 未关联上但是被关联
    n_v_qs = v_nk_qs.filter(Q(productlinkdistributor__isnull=False) & Q(productlinkdistributor__is_deleted=False))
    print(f"以V开头未关联上主商品但是被关联：{n_v_qs.count()}")

    # 未关联上
    g_qs = Product.objects.filter(g_code, need_handle, n_linked)
    print(f"以G开头未关联上主商品：{g_qs.count()}")
    # 未关联上但是被关联
    n_g_qs = g_qs.filter(Q(productlinkdistributor__isnull=False) & Q(productlinkdistributor__is_deleted=False))
    print(f"以G开头未关联上主商品但是被关联：{n_g_qs.count()}")

    # 未关联上
    t_qs = Product.objects.filter(t_code, need_handle, n_linked)
    print(f"以T开头未关联上主商品：{t_qs.count()}")
    # 未关联上但是被关联
    n_t_qs = t_qs.filter(Q(productlinkdistributor__isnull=False) & Q(productlinkdistributor__is_deleted=False))
    print(f"以T开头未关联上主商品但是被关联：{n_t_qs.count()}")

    print("===字符统计===")
    unique_code_first_characters = count_unique_code_first_characters()
    for item in unique_code_first_characters:
        print("首字符: {}, 数量: {}".format(item.get("first_char"), item.get("count")))
