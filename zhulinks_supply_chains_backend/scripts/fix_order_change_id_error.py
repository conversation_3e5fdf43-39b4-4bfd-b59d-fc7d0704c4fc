import os
import logging
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zhulinks_supply_chains_backend.settings')
django.setup()
from collections import defaultdict
from erp_orders.models.erp_orders_models import ERPOrder, ERPOrderDetails
from erp_purchase.models import InventoryChangeDetails

"""
销售单，进出流水明细的from_order_id错误记录了order.id
修改为对应的detail.id
1、时间区间：小于2025-06-04 14:11
2、遍历所有chang_type在[12, 14, 21, 23]的数据changes，用from_order_id=erp_order_detail.pk内查找数据detail
    - 找到：判断change.sku=detail.sku是否一致
        步骤一：是则跳过; 
        步骤二：否则查询erp_order_detail.erp_order=change.from_order_id + erp_order_detail.sku=change.sku，得到detail, 更新change.from_order_id=detail.id
    - 没找到：重复骤二
"""

def run():
    def get_order_detail(**kwargs):
        return ERPOrderDetails.objects.filter(**kwargs).first()

    n = 0
    m = 0
    for change in InventoryChangeDetails.objects.filter(change_type__in=[12, 14, 21, 23], create_date__lte="2025-06-04 22:11"):
        detail = get_order_detail(pk=change.from_order_id)
        if detail:
            if change.sku == detail.sku:
                continue
            else:
                detail = get_order_detail(erp_order_id=change.from_order_id, sku_id=change.sku_id)
                if detail:
                    n += 1
                    print(f"change.from_order_id:{change.from_order_id} ==> change.from_order_id = {detail.id}")
                    change.from_order_id = detail.id
                    # change.save()
        else:
            detail = get_order_detail(erp_order_id=change.from_order_id, sku_id=change.sku_id)
            if detail:
                m += 1
                print(f"change.from_order_id:{change.from_order_id} ==> change.from_order_id = {detail.id}")
                change.from_order_id = detail.id
                # change.save()

    print(f"n={n}, m={m}")

if __name__ == "__main__":
    run()